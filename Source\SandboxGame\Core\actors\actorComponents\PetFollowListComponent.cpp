#include "PetFollowListComponent.h"
#include "ClientMob.h"
#include "ICloudProxy.h"

#define MAX_TAMEDMON_FOLLOWS_LENGTH 5
IMPLEMENT_COMPONENTCLASS(PetFollowListComponent)

PetFollowListComponent::PetFollowListComponent()
{
	m_TamedMobFollowObjidList.reserve(MAX_TAMEDMON_FOLLOWS_LENGTH);
}

void PetFollowListComponent::load(const flatbuffers::Vector<int64_t> * tamedFollows)
{
	m_TamedMobFollowObjidList.reserve(MAX_TAMEDMON_FOLLOWS_LENGTH);
	if (tamedFollows) {
		for (int i = 0; i < (int)tamedFollows->size(); i++)
		{	
			int64_t tamedFollow = tamedFollows->Get(i);
			if (tamedFollow)
			{
				m_TamedMobFollowObjidList.push_back(tamedFollows->Get(i));
				if (m_TamedMobFollowObjidList.size() > MAX_TAMEDMON_FOLLOWS_LENGTH)
				{
					SLOG(INFO) << "PetFollowListComponent::load: too many tamed mob follows uin=" << (GetOwnerActor() ? GetOwnerActor()->getObjId(): 0);
					return;
				}
			}
		}
	}
}
bool PetFollowListComponent::addMobToTamedFollows(ClientMob* mob)
{
	if (mob == NULL) return false;
	if (m_TamedMobFollowObjidList.size() > MAX_TAMEDMON_FOLLOWS_LENGTH)  return false;
	m_TamedMobFollowObjidList.push_back(mob->getObjId());
	return true;	
}
void PetFollowListComponent::removeMobFromTamedFollows(ClientMob* mob)
{
	std::vector<int64_t>::iterator it = m_TamedMobFollowObjidList.begin();
	while (it != m_TamedMobFollowObjidList.end())
	{
		if (*it == mob->getObjId())
		{
			m_TamedMobFollowObjidList.erase(it);
			break;
		}
		it++;
	}	
}
bool PetFollowListComponent::isInTamedFollows(ClientMob * mob)
{
	std::vector<int64_t>::iterator it = m_TamedMobFollowObjidList.begin();
	while (it != m_TamedMobFollowObjidList.end())
	{
		if (*it == mob->getObjId())
		{
			return true;
		}
		it++;
	}
	return false;	
}