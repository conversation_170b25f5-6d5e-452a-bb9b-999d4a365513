#ifndef __ACTOR_MOVING_LIGHT_H__
#define __ACTOR_MOVING_LIGHT_H__

#include "ActorComponent_Base.h"
#include "OgreWCoord.h"

#include "SandboxListener.h"

class ClientActor;
class Chunk;

class ActorMovingLight : public ActorComponentBase
{ 
	DECLARE_COMPONENTCLASS(ActorMovingLight)
public:
	ActorMovingLight();
	virtual ~ActorMovingLight();
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	void SetEnable(bool value);

private:
	//void onEnterWorld();
	void OnLeaveWorld(bool keepInChunk);
	void RemoveLightingPoint();

protected:

	//virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner) override;

	//virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner) override;

	WCoord m_BlockPos;
	bool m_Enable;

	//MNSandbox::ListenerFunction<World*>*  m_ListenerEnterWorld = nullptr;
	//MNSandbox::ListenerFunction<bool>*    m_ListenerLeaveWorld = nullptr;
};

#endif