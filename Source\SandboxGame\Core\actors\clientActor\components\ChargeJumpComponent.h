#ifndef __CHARGE_JUMP_COMPONENT_H__
#define __CHARGE_JUMP_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "ActorTypes.h"
#include "ClientActorDef.h"
#include "SandboxGame.h"
#include "GameCamera.h"

class ClientActor;
class EXPORT_SANDBOXGAME ChargeJumpComponent;
class ChargeJumpComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(ChargeJumpComponent)
	ChargeJumpComponent();
	~ChargeJumpComponent();

	/* 进入owner */
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	/* 离开owner */
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	
	void startCharge();
	void endCharge();
	void bounce(); //弹跳
	void resetBounceState();
	void tickCharge();
	void rebounce(float realMovY, float fallDist);
	bool canRebounce();
	bool isChargeJumpEnabled();
	void updateChargeCameraZoom(GameCamera* camera, bool bChargeRelease);
	bool isChargeCameraZoom();
	float getChargeProgress();
	void updateChargeState(bool b);
	float getCurCharge();
	float getMaxChargeSpeed();
	void updatePlayerChargeJump(bool jump);
private:
	float m_nCurChargeJumpSpeed;
	float m_CurCharge;
	float m_minRebounceSpeed; // 最小回弹速度
	bool m_curCameZoom;
}; //tolua_exports
#endif // __CHARGE_JUMP_COMPONENT_H__