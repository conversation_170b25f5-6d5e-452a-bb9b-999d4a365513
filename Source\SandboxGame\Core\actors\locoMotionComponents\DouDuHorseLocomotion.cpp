
#include "DouDuHorseLocomotion.h"
#include "world.h"
#include "ActorDouDuMount.h"
#include "RiddenComponent.h"
#include "SoundComponent.h"
#include "EffectComponent.h"
#include "defdata.h"
static Vector3f s_MotionDecay(0.9f, 0.98f, 0.9f);
static float s_InWaterForce = 1.0f;
IMPLEMENT_COMPONENTCLASS(DouDuHorseLocomotion)
DouDuHorseLocomotion::DouDuHorseLocomotion() //: HorseLocomotion(owner)
{
	speedMultiplier = 20.0f;
	m_MaxSpeed = 0;
	m_fSpeedUpTime = 0;
	m_fSpeedUpVal = 0;
	m_bWaitStopEffect = false;
	m_bRun = false;
}

DouDuHorseLocomotion::~DouDuHorseLocomotion()
{

}

void DouDuHorseLocomotion::tick()
{
	HorseLocomotion::tick();

	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		m_Motion *= s_MotionDecay;
	}
	else
	{
		if (!getOwnerActor())
			return;
		auto RidComp = getOwnerActor()->getRiddenComponent();
		ClientActor* riddenactor = NULL;
		if (RidComp)
		{
			riddenactor = RidComp->getRiddenByActor();
		}
		if (!riddenactor)
		{
			return;
		}

		WCoord oldpos = m_Position;
		float motionlen = Rainbow::Sqrt(m_Motion.x * m_Motion.x + m_Motion.z * m_Motion.z);

		if ((m_MaxSpeed >= -Rainbow::EPSILON) && (m_MaxSpeed <= Rainbow::EPSILON))
		{
			ActorDouDuMount* horse = static_cast<ActorDouDuMount*>(getOwnerActor());
			if (horse)
			{
				m_MaxSpeed = (float)(horse->getRiddenLandSpeed() / 10);  //每tick的速度
				speedMultiplier = m_MaxSpeed;
			}
		}

		bool hasPower = false;
		float MAX_MOTION_LEN = m_MaxSpeed > 0 ? m_MaxSpeed : 50.0f;

		m_Motion.y = calGravityMotionY(m_Motion.y);
		ActorLiving* riddenby = NULL;
		if (RidComp)
		{
			riddenby = dynamic_cast<ActorLiving*>(RidComp->getRiddenByActor());
		}
		if (riddenby)
		{
			LivingLocoMotion* locomove = static_cast<LivingLocoMotion*>(riddenby->getLocoMotion());
			if (locomove)
			{
				ActorDouDuMount* horse = static_cast<ActorDouDuMount*>(getOwnerActor());
				if (horse && !horse->isFloatageing())
				{
					if (horse->getHorseDef()->ID == DOUDU_MOUNT_ID_LEVEL_0 ||
						horse->getHorseDef()->ID == DOUDU_MOUNT_ID_LEVEL_1 ||
						horse->getHorseDef()->ID == MUMU_MOUNT_ID_LEVEL_0 ||
						horse->getHorseDef()->ID == MUMU_MOUNT_ID_LEVEL_1 ||
						horse->getHorseDef()->ID == NINECOLORDEER_MOUNT_ID_LEVEL_0 ||	// 20210804：低级九色鹿坐骑移动速度异常 codeby： keguanqiang
						horse->getHorseDef()->ID == NINECOLORDEER_MOUNT_ID_LEVEL_1)
					{ //动物坐骑可以横着走

						if (!((locomove->m_MoveStrafing >= -Rainbow::EPSILON) && (locomove->m_MoveStrafing <= Rainbow::EPSILON)) &&
							!((locomove->m_MoveForward >= -Rainbow::EPSILON) && (locomove->m_MoveForward <= Rainbow::EPSILON)))
						{
							Vector3f dir;
							float yaw = locomove->m_RotateYaw;
							MAX_MOTION_LEN = m_MaxSpeed / 2;
							if (locomove->m_MoveForward > 0 && locomove->m_MoveStrafing > 0) {
								yaw += 45.0f;
							}
							else if (locomove->m_MoveForward > 0 && locomove->m_MoveStrafing < 0) {
								yaw += 315.0f;
							}
							else if (locomove->m_MoveForward < 0 && locomove->m_MoveStrafing < 0) {
								yaw += 225.0f;
							}
							else if (locomove->m_MoveForward < 0 && locomove->m_MoveStrafing > 0) {
								yaw += 135.0f;
							}
							PitchYaw2Direction(dir, yaw, 0);
							m_Motion += dir * (m_MaxSpeed * 0.05f);
							hasPower = false;
						}
						if (!((locomove->m_MoveStrafing >= -Rainbow::EPSILON) && (locomove->m_MoveStrafing <= Rainbow::EPSILON)) ||
							!((locomove->m_MoveForward >= -Rainbow::EPSILON) && (locomove->m_MoveForward <= Rainbow::EPSILON)))
						{
							Vector3f dir;
							float yaw = locomove->m_RotateYaw;
							MAX_MOTION_LEN = m_MaxSpeed / 2;
							float speed = m_MaxSpeed * 0.9f;
							if (locomove->m_MoveStrafing < 0)
							{
								yaw += 270.0f;
							}
							else if (locomove->m_MoveStrafing > 0) {
								yaw += 90.0f;
							}

							if (locomove->m_MoveForward < 0)  //后退
							{
								yaw += 180.0f;
							}
							else if (locomove->m_MoveForward > 0) {
								speed = m_MaxSpeed;
							}
							PitchYaw2Direction(dir, yaw, 0);

							m_Motion += dir * (speed * 0.05f);
							hasPower = false;
						}
						if (((locomove->m_MoveStrafing >= -Rainbow::EPSILON) && (locomove->m_MoveStrafing <= Rainbow::EPSILON)) &&
							((locomove->m_MoveForward >= -Rainbow::EPSILON) && (locomove->m_MoveForward <= Rainbow::EPSILON)))
						{
							m_Motion = Vector3f(0, m_Motion.y, 0);
						}
					}
					else
					{
						if (!((locomove->m_MoveForward >= -Rainbow::EPSILON) && (locomove->m_MoveForward <= Rainbow::EPSILON)))
						{
							Vector3f dir;
							float yaw = locomove->m_RotateYaw;
							static bool isBraking = false;
							if (locomove->m_MoveForward < 0)  //后退
							{
								yaw += 180.0f;
								MAX_MOTION_LEN = m_MaxSpeed / 2;
							}

							PitchYaw2Direction(dir, yaw, 0);
							Vector3f tempMotion = m_Motion;
							tempMotion.y = 0;
							if (tempMotion.Length() > 10.0f && DotProduct(tempMotion, dir) < 0 && !isBraking)
							{
								char soundName[64] = { 0 };
								sprintf(soundName, "ent.%d.brake", getOwnerActor()->getDefID());
								auto sound = riddenby->getSoundComponent();
								if (sound)
								{
									sound->playSound(soundName, 1.0f, 1.0f);
								}
								isBraking = true;
							}
							else
								isBraking = false;

							m_Motion += dir * (speedMultiplier * 0.05f);

							hasPower = true;
						}
					}
				}
			}
		}

		float newlen = Rainbow::Sqrt(m_Motion.x * m_Motion.x + m_Motion.z * m_Motion.z);

		if (newlen > MAX_MOTION_LEN)
		{
			float t = MAX_MOTION_LEN / newlen;
			m_Motion.x *= t;
			m_Motion.z *= t;
			newlen = MAX_MOTION_LEN;
		}

		if (newlen > motionlen && speedMultiplier < MAX_MOTION_LEN)
		{
			speedMultiplier += (MAX_MOTION_LEN - speedMultiplier) / MAX_MOTION_LEN;

			if (speedMultiplier > MAX_MOTION_LEN)
			{
				speedMultiplier = MAX_MOTION_LEN;
			}
		}
		else
		{
			speedMultiplier -= (speedMultiplier - MAX_MOTION_LEN / 5.0f) / MAX_MOTION_LEN;

			if (speedMultiplier < MAX_MOTION_LEN / 5.0f)
			{
				speedMultiplier = MAX_MOTION_LEN / 5.0f;
			}
		}

		bool canTurn = false;
		Vector3f speedUpMotion(0, 0, 0);
		Vector3f turnMotion(0, 0, 0);
		if (m_fSpeedUpTime > 0 && riddenby)
		{
			LivingLocoMotion* locomove = static_cast<LivingLocoMotion*>(riddenby->getLocoMotion());
			if (locomove)
			{
				Vector3f dir;
				PitchYaw2Direction(dir, m_RotateYaw, 0);
				speedUpMotion = dir * m_fSpeedUpVal;

				PitchYaw2Direction(dir, locomove->m_RotateYaw, 0);
				turnMotion = dir * m_fSpeedUpVal;
				canTurn = true;
			}
		}
		doMoveStep(m_Motion + speedUpMotion);
		m_Motion *= s_MotionDecay;
		m_RotationPitch = 0.0f;

		float targetyaw = m_RotateYaw;
		float targetPitch = m_RotationPitch;
		Vector3f dpos = m_Motion;
		dpos.y = 0;
		float curSpeedSqr = dpos.LengthSqr();
		if (!hasPower && canTurn) //松开了按键但使用加速技能的时候，给与转弯
		{
			dpos += turnMotion;
			dpos.y = 0;
			curSpeedSqr = dpos.LengthSqr();
			hasPower = true;
		}

		if (curSpeedSqr > 10.0f && hasPower)
		{
			if (riddenby)
			{
				LivingLocoMotion* locomove = static_cast<LivingLocoMotion*>(riddenby->getLocoMotion());
				if (locomove)
				{
					Vector3f dir;
					PitchYaw2Direction(dir, locomove->m_RotateYaw, 0);

					if (DotProduct(dpos, dir) < 0)  //后退不用转向
					{
						dpos.x = 0 - dpos.x;
						dpos.z = 0 - dpos.z;
					}
				}
			}

			Direction2PitchYaw(&targetyaw, NULL, dpos);
		}

		if (riddenby)
		{
			LivingLocoMotion* locomove = static_cast<LivingLocoMotion*>(riddenby->getLocoMotion());
			ActorDouDuMount* horse = static_cast<ActorDouDuMount*>(getOwnerActor());
			if (horse && (horse->getHorseDef()->ID == DOUDU_MOUNT_ID_LEVEL_0 ||
				horse->getHorseDef()->ID == DOUDU_MOUNT_ID_LEVEL_1 ||
				horse->getHorseDef()->ID == MUMU_MOUNT_ID_LEVEL_0 ||
				horse->getHorseDef()->ID == MUMU_MOUNT_ID_LEVEL_1 ||
				horse->getHorseDef()->ID == NINECOLORDEER_MOUNT_ID_LEVEL_0 ||		// 20210804：低级九色鹿坐骑移动速度异常 codeby： keguanqiang
				horse->getHorseDef()->ID == NINECOLORDEER_MOUNT_ID_LEVEL_1)) {
				if (m_RotateYaw != locomove->m_RotateYaw && locomove->m_MoveForward > 0)
				{
					m_Motion = Vector3f(0, m_Motion.y, 0);
					Vector3f dir;
					PitchYaw2Direction(dir, m_RotateYaw, 0);
					m_Motion += dir * speedMultiplier;
				}
				m_RotateYaw = m_PrevRotateYaw = locomove->m_RotateYaw;
				//m_RotationPitch = m_PrevRotatePitch = locomove->m_RotationPitch * 0.5f;
			}
			else
			{
				if (!((locomove->m_MoveForward >= -Rainbow::EPSILON) && (locomove->m_MoveForward <= Rainbow::EPSILON)))
				{
					m_RotateYaw += Rainbow::Clamp(WrapAngleTo180(targetyaw - m_RotateYaw), -20.0f, 20.0f);
				}
			}
		}
		if (m_fSpeedUpTime > 0)
		{
			m_fSpeedUpTime -= 0.05f;
		}
		else if (m_bWaitStopEffect)
		{
			m_bRun = false;
			ActorDouDuMount* horse = static_cast<ActorDouDuMount*>(getOwnerActor());
			m_bWaitStopEffect = false;
			auto effectComponent = getOwnerActor()->getEffectComponent();
			if (effectComponent)
			{
				if (horse && horse->hasHorseSkill(HORSE_SKILL_DOUDURUSH))
				{
					effectComponent->stopBodyEffect("horse_3487_3");
				}
				else if (horse && horse->hasHorseSkill(HORSE_SKILL_FORTUNEMOO))
				{
					effectComponent->stopBodyEffect("horse_3489_3");
				}
				else if (horse && horse->hasHorseSkill(HORSE_SKILL_YE_WU))
				{
					effectComponent->stopBodyEffect("horse_4501");
				}
				else
				{
					effectComponent->stopBodyEffect("horsechange_3453_1");
				}
			}
		}
	}
}