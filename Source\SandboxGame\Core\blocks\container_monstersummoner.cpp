#include "ClientItem.h"
#include "container_monstersummoner.h"
#include "world.h"
#include "LuaInterfaceProxy.h"
#include "Text3D/Text3D.h"
#include "DefManagerProxy.h"
#include "BlockScene.h"
#include "WorldManager.h"
#include "LuaInterfaceProxy.h"
#include "chunk.h"
using namespace MINIW;
using namespace Rainbow;
//85618		难度：
//85619		点击召唤丧尸大军
//85620		第一波丧尸来临：
//85621		第二波丧尸来临：
//85622		BOSS来临：
//85623		激活倒计时：
//85624		分钟
//85625		秒
//85626		等会再来试试吧 
static std::vector < std::vector<int> > stringGroup {
	{85618,85623,85619},
	{85620},{85621},{85622},
};
static int fontsize = 50;
static int rectw = 500;
static int recth = 230;
static float realw = 150.0f;
static float realh = 75.0f;
static WorldPos stringPos1 = WorldPos::fromVector3(0.f, 310.0f, 0.0f);
static WorldPos stringPos2 = WorldPos::fromVector3(0.f, 290.0f, 0.0f);
static float offsetX = 10.0f;
static int firstRoundNum = 1;
static int secondRoundNum = 1;
static int bossRoundNum = 1;
#define CD_TYPE_READY 0
#define CD_TYPE_FIRST_ROUND 1
#define CD_TYPE_SECOND_ROUND 2
#define CD_TYPE_AFTER_BOSS 3

ContainerMonsterSummoner::ContainerMonsterSummoner() : WorldContainer(WCoord(0, 0, 0), 0)
{
	m_TextObj = NULL;
#ifndef IWORLD_SERVER_BUILD
	m_TextObj = Rainbow::Text3D::Create(fontsize, rectw, recth, realw, realh, true, true);
#endif
	m_active = false;
	isFirstTimeReport = false;
	m_typeCD = CD_TYPE_READY;
	m_CD = 0;
	m_levelType = 1;
	m_ObjId = 0;
	m_doneTime = 0;
	m_level = 0;
	m_NeedTick = true;
}
ContainerMonsterSummoner::ContainerMonsterSummoner(const WCoord& blockpos) : WorldContainer(blockpos, 0)
{
	m_TextObj = NULL;
#ifndef IWORLD_SERVER_BUILD
	m_TextObj = Rainbow::Text3D::Create(fontsize, rectw, recth, realw, realh, true, true);
	
#endif
	m_active = false;
	isFirstTimeReport = false;
	m_typeCD = CD_TYPE_READY;
	m_CD = 0;
	m_ObjId = 0;
	m_levelType = 1; 
	m_doneTime = 0;
	m_level = 0;
	m_NeedTick = true;
}
ContainerMonsterSummoner::~ContainerMonsterSummoner()
{
	if (m_TextObj) {
		Rainbow::GameObject::Destroy(m_TextObj->GetGameObject());
	}
}
void ContainerMonsterSummoner::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	if (!m_TextObj)
	{
		m_TextObj = Rainbow::Text3D::Create(fontsize, rectw, recth, realw, realh, true, true);
	}
	if (pworld && pworld->onClient() && m_TextObj)
	{
		m_TextObj->AttachToScene(pworld->getScene());
		m_TextObj->setAutoWrap(true);
		m_TextObj->setTextColor(255, 255, 255);
		m_TextObj->setVisible(true);
		m_TextObj->SetVisibleDistance(16 * BLOCK_SIZE);
		WCoord centerPos = BlockBottomCenter(m_BlockPos);
		WorldPos showPos = WorldPos::fromVector3(Vector3f(centerPos.x, centerPos.y, centerPos.z));
		m_TextObj->SetPosition(showPos + stringPos1);
	}

	registerUpdateTick();
}
void ContainerMonsterSummoner::leaveWorld()
{
	if (m_World->onClient() && m_TextObj)
	{
		if (m_TextObj)
			m_TextObj->DetachFromScene();

	}
	//离开则强制结束
	
	if (m_typeCD > 0)
	{
		ObserverEvent obevent;
		jsonxx::Object customObj;
		customObj << "PosX" << m_BlockPos.x;
		customObj << "PosY" << m_BlockPos.y;
		customObj << "PosZ" << m_BlockPos.z;
		customObj << "forceEnd" << true;
		obevent.SetData_CustomStr(customObj.json_nospace());
		GetObserverEventManager().OnTriggerEvent("Summoner.CheckSummoner", &obevent);
	}
	

	WorldContainer::leaveWorld();
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerMonsterSummoner::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);
	auto actor = FBSave::CreateContainerMonsterSummoner(builder, basedata, m_active, m_level, m_typeCD, m_CD, m_doneTime, m_levelType);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerMonsterSummoner, actor.Union());
}

bool ContainerMonsterSummoner::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerMonsterSummoner*>(srcdata);
	loadContainerCommon(src->basedata());
	m_active = src->active();
	m_level = src->level();
	m_typeCD = src->typeCD();
	m_CD = src->CD();
	m_doneTime = src->doneTime();
	m_levelType = src->levelType();
	return true;
}

int ContainerMonsterSummoner::getObjType() const
{
	return OBJ_TYPE_MONSTERSUMMONER;
}

void ContainerMonsterSummoner::updateTick()
{
	if (m_World && m_World->onClient() && m_TextObj)
	{
		updateText();
	}
	if (m_CD > 0)
	{
		m_CD--;
	}
	if (m_World && m_World->GetWorldMgr() && m_doneTime > 0 && m_World->GetWorldMgr()->getWorldTime() >= m_doneTime && m_typeCD==0)
	{
		m_CD = 0;
		m_doneTime = 0;
	}
	if (m_CD == 0)
	{
		switchStage();
	}
	if (m_CD % 20 == 0 && m_typeCD > CD_TYPE_READY)
	{
		int blockDir = m_BlockData & 3;
		ObserverEvent obevent;
		jsonxx::Object customObj;
		customObj << "m_CD" << m_CD;
		customObj << "m_typeCD" << m_typeCD;
		customObj << "PosX" << m_BlockPos.x;
		customObj << "PosY" << m_BlockPos.y;
		customObj << "PosZ" << m_BlockPos.z;
		customObj << "uin" << m_ObjId;
		customObj << "monsterLevelType" << m_levelType;
		customObj << "blockDir" << blockDir;
		customObj << "monsterLevel" << m_level;
		obevent.SetData_CustomStr(customObj.json_nospace());
		GetObserverEventManager().OnTriggerEvent("Summoner.CheckSummoner", &obevent);
	}
	if (m_CD % 200 == 0 && m_CD>0)
	{
		if (m_World)
		{
			Chunk* pchunk = m_World->getChunk(m_BlockPos);
			if (pchunk)
			{
				pchunk->m_Dirty = true;
			}
			m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
		}
	}
}

void ContainerMonsterSummoner::updateText()
{
	char textTmp[256];
	std::string levelStr = "?";
	if (m_level > 0)
	{
		levelStr = std::to_string(m_level);
	}
	if (m_typeCD == CD_TYPE_FIRST_ROUND)
	{
		int secTime = m_CD / 20;
		sprintf(textTmp, "%s%s\n%s\n%d%s", GetDefManagerProxy()->getStringDef(stringGroup[0][0]), levelStr.c_str(), 
			GetDefManagerProxy()->getStringDef(stringGroup[2][0]), 
			secTime, GetDefManagerProxy()->getStringDef(85625));
		m_TextObj->setText(textTmp);
		m_TextObj->setOffsetX(offsetX);
	}
	else if (m_typeCD == CD_TYPE_SECOND_ROUND)
	{
		int secTime = m_CD / 20;
		sprintf(textTmp, "%s%s\n%s\n%d%s", GetDefManagerProxy()->getStringDef(stringGroup[0][0]), levelStr.c_str(),
			GetDefManagerProxy()->getStringDef(stringGroup[3][0]),
			secTime, GetDefManagerProxy()->getStringDef(85625));
		m_TextObj->setText(textTmp);
		m_TextObj->setOffsetX(offsetX);
	}
	else if (m_typeCD == CD_TYPE_AFTER_BOSS || (m_typeCD == CD_TYPE_READY && m_CD > 0) )
	{
		int minTime = m_CD / (20 * 60);
		sprintf(textTmp, "%s%s\n%s\n%d%s", GetDefManagerProxy()->getStringDef(stringGroup[0][0]), levelStr.c_str(),
			GetDefManagerProxy()->getStringDef(stringGroup[0][1]), minTime, GetDefManagerProxy()->getStringDef(85624));
		m_TextObj->setText(textTmp);
		m_TextObj->setOffsetX(offsetX);
	}
	else
	{
		sprintf(textTmp, "%s%s\n%s", GetDefManagerProxy()->getStringDef(stringGroup[0][0]), levelStr.c_str(),
			GetDefManagerProxy()->getStringDef(stringGroup[0][2]));
		m_TextObj->setText(textTmp);
		m_TextObj->setOffsetX(offsetX);
	}
}

void ContainerMonsterSummoner::switchStage()
{
	WCoord centerPos = BlockBottomCenter(m_BlockPos);
	WorldPos showPos = WorldPos::fromVector3(Vector3f(centerPos.x, centerPos.y, centerPos.z));
	if (m_typeCD == CD_TYPE_FIRST_ROUND)
	{
		m_monsterCount = 0;
		m_typeCD = CD_TYPE_SECOND_ROUND;
		m_CD = GetLuaInterfaceProxy().get_lua_const()->SummonerSecondRoundCD;;
		m_TextObj->SetPosition(showPos + stringPos2);
	}
	else if (m_typeCD == CD_TYPE_SECOND_ROUND)
	{
		m_monsterCount = 0;
		m_typeCD = CD_TYPE_AFTER_BOSS;
		m_CD = GetLuaInterfaceProxy().get_lua_const()->SummonerMainCD;;
		m_TextObj->SetPosition(showPos + stringPos1);
	}
	else if (m_typeCD == CD_TYPE_AFTER_BOSS)
	{
		m_monsterCount = 0;
		m_typeCD = CD_TYPE_READY;
		m_CD = 0;
		m_TextObj->SetPosition(showPos + stringPos1);
	}
}

void ContainerMonsterSummoner::activeSummoner(long long Uin)
{
	 if (m_CD > 0) //还在CD中
	{
		MINIW::ScriptVM::game()->callFunction("ShowGameTipsStrID", "i", 85626);
		return;
	}
	m_ObjId = Uin;
	isFirstTimeReport = true;
	m_active = true;
	m_typeCD = CD_TYPE_FIRST_ROUND;
	m_CD = GetLuaInterfaceProxy().get_lua_const()->SummonerFirstRoundCD;
	WCoord centerPos = BlockBottomCenter(m_BlockPos);
	WorldPos showPos = WorldPos::fromVector3(Vector3f(centerPos.x, centerPos.y, centerPos.z));
	m_TextObj->SetPosition(showPos + stringPos2);
	m_monsterCount = 0;
	if (m_World)
	{
		Chunk* pchunk = m_World->getChunk(m_BlockPos);
		if (pchunk)
		{
			pchunk->m_Dirty = true;
		}
		m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
	}
}