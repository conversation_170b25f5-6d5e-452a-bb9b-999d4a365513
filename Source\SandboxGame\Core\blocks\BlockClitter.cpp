﻿/*
*	file: BlockClitter
*	func: 仿 BlockGrayHerbs
*/
#include "BlockClitter.h"
#include "special_blockid.h"
#include "world.h"
#include "WorldProxy.h"
#include "ActorManagerInterface.h"
#include "BlockGeom.h"
#include "LuaInterfaceProxy.h"
#include "WorldManager.h"
#include "WeatherManager.h"
#include "ShareRenderMaterial.h"
#include "BlockMaterialMgr.h"
#include "BlockSnow.h"
#include "SectionMesh.h"
#include "worldData/coreMisc.h"

IMPLEMENT_BLOCKMATERIAL(BlockClitter)
IMPLEMENT_BLOCKMATERIAL(BlockMineralPile)

static const int s_GrowOnBlocks[] = {
	BLOCK_GRASS,
	BLOCK_DIRT,
	BLOCK_STONE,
	BLOCK_SAND,
	BLOCK_PLANTSPACE_DRYDIRT,
	BLOCK_SOLIDSAND,
	BLOCK_DIRT_FREEZE
};

BlockClitter::BlockClitter() : m_SnowMtl(NULL)
{

}
BlockClitter::~BlockClitter()
{
	ENG_RELEASE(m_SnowMtl);
}

void BlockClitter::init(int resid)
{
	ModelBlockMaterial::init(resid);
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BlockFaceToPlane;
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BranchNotLink;
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::IsRoundBlock;
	m_SnowMtl = g_BlockMtlMgr.createRenderMaterial("snow", m_Def, GETTEX_WITHDEFAULT, getDrawType(), getMipmapMethod());
	SetToggle(BlockToggle_RandomTick, true);
}

bool BlockClitter::canStayOnPos(WorldProxy *pworld, const WCoord &blockpos)
{
	if(GetBlockDef()->Height > 1)
	{
		if(pworld->getBlockID(DownCoord(blockpos)) == getBlockResID()) return true;
		if(pworld->getBlockID(TopCoord(blockpos)) != getBlockResID()) return false;
	}
	return canThisPlantGrowOnThisBlockID(pworld->getBlockID(DownCoord(blockpos)));
}

void BlockClitter::setCrabData(World* pworld, const WCoord& blockpos)
{
	auto data = pworld->getBlockData(blockpos);
	pworld->setBlockData(blockpos.x, blockpos.y, blockpos.z, data | 8);
}

void BlockClitter::blockTick(World* pworld, const WCoord& blockpos)
{
	int blockId = pworld->getBlockID(blockpos);
	if (blockId == BLOCK_CLITTER)
	{
		int blockdata = pworld->getBlockData(blockpos);
		if (pworld && pworld->getWeatherMgr())
		{
			int weather = pworld->getWeatherMgr()->getWeather(blockpos);
			if (((4 & blockdata) != 4) && (weather == GROUP_BLIZZARD_WEATHER || weather == GROUP_SNOW_WEATHER))
			{
				pworld->setBlockAll(blockpos, BLOCK_CLITTER, blockdata | 4);//覆雪
			}
		}
	}
}

void BlockClitter::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	if ((blockdata >> 3) == 1)
	{
		pworld->getActorMgr()->iSpawnMob(WCoord(blockpos.x * BLOCK_SIZE, blockpos.y * BLOCK_SIZE, blockpos.z * BLOCK_SIZE), 3621, false, false);
	}
	else
	{
		auto type = pworld->getBiomeType(blockpos.x, blockpos.z);
		if ((type == BIOME_BEACH || type == BIOME_OCEAN) && GenRandomInt(GetLuaInterfaceProxy().get_lua_const()->crab_create_random_num) == 1)
		{
			pworld->getActorMgr()->iSpawnMob(WCoord(blockpos.x * BLOCK_SIZE, blockpos.y * BLOCK_SIZE, blockpos.z * BLOCK_SIZE), 3621, false, false);
		}
	}
}

void BlockClitter::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;
	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();
	bool isSnowing = false;
	if ((blockdata & 4) == 4)
	{
		isSnowing = true;
	}
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::white };
	psection->getBlockVertexLight(blockpos, verts_light);

	int idbuf[32];
	int dirbuf[32];
	int ngeom = getBlockGeomID(idbuf, dirbuf, psection, blockpos, data.m_World);
	BlockGeomMeshInfo meshinfo;
	BlockGeomMeshInfo meshinfo_snow;
	RenderBlockMaterial* pmtl = getDefaultMtl(); //getGeomMtl(psection, blockpos);
	SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);

	ChunkRandGen chunkrand;
	WCoord wpos = psection->getOrigin() + blockpos;
	WCoordHashCoder coder;
	chunkrand.setSeed(coder(wpos));
	float tx, ty, tz;
	tx = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
	tz = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
	ty = 0;
	float max_scale = 0.8f;
	float min_scale = 0.5f;

	float angle = (chunkrand.getFloat()) * 360;
	float scale = min_scale + (chunkrand.getFloat()) * (max_scale - min_scale);
	geom->getFaceScaleVerts(meshinfo, idbuf[0], scale, scale, 0, angle > 7 ? angle : 7, 0, NULL, tx, ty, tz, 0);

	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl->getUVTile());


	if (isSnowing == true)
	{
		RenderBlockMaterial* pmtl_snow = m_SnowMtl; //getGeomMtl(psection, blockpos);
		SectionSubMesh* psubmesh_snow = poutmesh->getSubMesh(pmtl_snow);
		if (!psubmesh_snow) return;
		idbuf[1] = 4;
		int dir = dirbuf[1] & 0xffff;
		int mirrortype = (dirbuf[1] >> 16) & 3;
		geom->getFaceVerts(meshinfo_snow, idbuf[1], 1.0f, 0, dir, mirrortype);
		psubmesh_snow->addGeomBlockLight(meshinfo_snow, &blockpos, verts_light, NULL, pmtl_snow->getUVTile());
	}

}

bool BlockClitter::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	if(!BlockMaterial::canPutOntoPos(pworld, blockpos)) return false;
	if(!canThisPlantGrowOnThisBlockID(pworld->getBlockID(DownCoord(blockpos)))) return false;

	for(int i=1; i< GetBlockDef()->Height; i++)
	{
		if(!BlockMaterial::canPutOntoPos(pworld, blockpos+WCoord(0,i,0))) return false;
	}

	return true;
}

void BlockClitter::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (player && pworld)
	{
		int data = GenRandomInt(0, 3);
		int blockdata = pworld->getBlockData(blockpos);
		pworld->setBlockData(blockpos, blockdata | data, 3);
	}
}

int BlockClitter::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	int data = sectionData->getBlock(blockpos).getData();
	bool isSnowing = false;
	idbuf[0] = (data&3);
	int returnNegom = 1;
	if ((data & 4) == 4)
	{
		isSnowing = true;
	}
	if (isSnowing == true)
	{
		idbuf[1] = 4;
		returnNegom = 2;
		dirbuf[1] = DIR_NEG_X;
	}
	dirbuf[0] = (abs(blockpos.x+ blockpos.z))%4;
	return returnNegom;
}

bool BlockClitter::canThisPlantGrowOnThisBlockID(int blockid)
{
	int count = sizeof(s_GrowOnBlocks) / sizeof(s_GrowOnBlocks[0]);

	for (int i = 0; i < count; i++)
	{
		if (blockid == s_GrowOnBlocks[i])
			return true;
	}
	return false;
}


BlockMineralPile::BlockMineralPile() : BlockClitter()
{

}

BlockMineralPile::~BlockMineralPile()
{

}

int BlockMineralPile::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	return ModelBlockMaterial::getBlockGeomID(idbuf, dirbuf, sectionData, blockpos, world);
	//int data = sectionData->getBlock(blockpos).getData();
	//idbuf[0] = 0;
	//dirbuf[0] = data % 4;
	//return 1;
}

void BlockMineralPile::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	ModelBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
}



