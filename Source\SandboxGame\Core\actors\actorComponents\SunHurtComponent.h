#ifndef __SUN_HURT_COMPONENT_H__
#define __SUN_HURT_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "SandboxGame.h"

class ClientActor;

class EXPORT_SANDBOXGAME SunHurtComponent;
class SunHurtComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(SunHurtComponent)
public:
	//tolua_begin
	SunHurtComponent(bool canPlaySunHurtEffet = false);
	virtual void OnTick() override;
	//tolua_end
	/**
    * 设置是否播放日照伤害特效
    */
    void SetCanPlayEffect(bool enable) { m_canPlaySunHurtEffet = enable; }

protected:
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner) override;
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner) override;
private:

	void updateSunHurt();

	NS_SANDBOX::SandboxResult SANDBOXAPI OnSetSunHurt(NS_SANDBOX::SandboxContext context);
	NS_SANDBOX::SandboxResult SANDBOXAPI OnGetSunHurt(NS_SANDBOX::SandboxContext context);

	bool m_canPlaySunHurtEffet;
	bool m_SunHurt;//是否受日照伤害 	
	int m_SunFleeTick;//能承受多少次太阳伤害 
}; //tolua_exports

#endif