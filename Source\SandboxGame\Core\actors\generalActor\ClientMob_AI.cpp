#include "ActorBody.h"
#include "world.h"
#include "ActorVillager.h"
#include "AIFishAttack.h"
#include "AITargetHurtee.h"
#include "AISwimming.h"
#include "AIFishBeg.h"
#include "AISit.h"
#include "AIFollowOwner.h"
#include "AIWander.h"
#include "AITargetOwnerHurtee.h"
#include "AITargetOwnerHurter.h"
#include "AITargetNonTamed.h"
#include "AIBeg.h"
#include "AIWatchClosest.h"
#include "AILookIdle.h"
#include "AIRestrictSun.h"
#include "AIFleeSun.h"
#include "AITargetNearest.h"
#include "AIBreakDoor.h"
#include "AIMoveTowardsRestriction.h"
#include "AIPanic.h"
#include "AITempt.h"
#include "AILeapAtTarget.h"
#include "AIMate.h"
#include "AIFollowParent.h"
#include "AIBoom.h"
#include "AIArrowAttack.h"
#include "AIEatGrass.h"
#include "AIRandomSwim.h"
#include "AIFearPlayer.h"
#include "AIFollowDirection.h"
#include "AITargetSpecificEntity.h"
#include "AITransfiguration.h"
#include "AIClosestDance.h"
#include "AISleep.h"
#include "AILoggerHeads.h"
#include "AILayEggs.h"
#include "AILayEggInNest.h"
#include "AIHatch.h"
#include "AIEatFeedBlock.h"
#include "AIToppleOver.h"
#include "AISitbyItem.h"
#include "AIMilking.h"
#include "AIEatLeaf.h"
#include "AIEatFlower.h"
#include "AIRideHorse.h"
#include "AIKickAway.h"
#include "AIMakeTrouble.h"
#include "AIGetSpecialAttackattr.h"
#include "AILoveBlock.h"
#include "AITargetFollowingPlayer.h"
#include "AIHoldMonster.h"
#include "AICeilingAtk.h"
#include "AIGoCeiling.h"
#include "AIRandomFly.h"
#include "AIFlyAttack.h"
#include "AIFlyBeg.h"
#include "AIFlyLoveBlock.h"
#include "AIPlayerPanic.h"
#include "AISeparatePanic.h"
#include "AICombine.h"
#include "AIWizardFly.h"
#include "AIWizardAttack.h"
#include "AIWizardProjectileAttack.h"
#include "AIBumpAttack.h"
#include "AIDigBlock.h"
#include "AIPickupItem.h"
#include "AIStoreItem.h"
#include "AITakeItem.h"
#include "AICraftItem.h"
#include "AINpcSleep.h"
#include "AIHunger.h"
#include "AIEatFood.h"
#include "AIEatThenMutate.h"
#include "AIMutateFly.h"
#include "AIPatrolOnBlock.h"
#include "AIMutateTarget.h"
#include "AIPlant.h"
#include "AITargetScream.h"
#include "AIPanicBuff.h"
#include "AIPickupItemEx.h"
#include "BehaviorTreeManager.h"

#include "ClientActorFuncWrapper.h"
#include "ActorDesertBusinessman.h"
#include "EffectComponent.h"
#include "Optick/optick.h"
#include "AIAtk.h"
#include "UGCSerialize.h"

using namespace MNSandbox;
using namespace MINIW;

#define _UPDATE_BOUND_BY_SCALE_

void ClientMob::ParseAI(jsonxx::Array aiJson)
{
	OPTICK_EVENT();
	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setAvoidWater(false);
		functionWrapper->setCanPassClosedWoodenDoors(false);
		functionWrapper->setCanSwimming(false);
	}
	jsonxx::Object tmpAi;
	for (size_t i = 0; i < aiJson.size(); i++)
	{
		tmpAi = aiJson.get<jsonxx::Object>(i);
		if (!tmpAi.has<jsonxx::String>("name"))
			continue;

		std::string aiName = tmpAi.get<jsonxx::String>("name");
		if (aiName.compare("can_fly") == 0)
		{
			if (functionWrapper)
			{
				functionWrapper->setCanFly(true);
			}
			continue;
		}
		else if (aiName.compare("avoid_water") == 0)
		{
			if (functionWrapper)
			{
				functionWrapper->setAvoidWater(true);
			}
			continue;
		}
		else if (aiName.compare("can_pass_closed_wooden_doors") == 0)
		{
			if (functionWrapper)
			{
				functionWrapper->setCanPassClosedWoodenDoors(true);
			}
			continue;
		}
		else if (aiName.compare("sun_hurt") == 0)
		{
			setSunHurt(true);
			continue;
		}
		else if (aiName.compare("path_hide") == 0)
		{
			setPathHide((int)tmpAi.get<jsonxx::Number>("type"));
			continue;
		}
		else if (aiName.compare("immune_fire") == 0)
		{
			setImmuneToFire((int)tmpAi.get<jsonxx::Number>("type"));
			continue;
		}
		else if (aiName.compare("invulnerable_prob") == 0)
		{
			setAiInvulnerableProb((int)tmpAi.get<jsonxx::Number>("prob"));
			continue;
		}
		else if (aiName.compare("can_ride_by_player") == 0)
		{
			setCanRideByPlayer(false);
			continue;
		}
		else if (aiName.compare("growing_age") == 0)
		{
			setGrowingAge((int)tmpAi.get<jsonxx::Number>("grow_tick"));
			continue;
		}
		else if (aiName.compare("oxygen_need") == 0)
		{
			setOxygenNeed(false);
			continue;
		}

		if (!tmpAi.has<jsonxx::Number>("priority"))
			continue;

		if (aiName.compare("swimming") == 0)
		{
			// 			bool value = (bool)tmpAi.get<jsonxx::Boolean>("name");
			if (functionWrapper)
			{
				functionWrapper->setCanSwimming(true);
			}
			addAiTask<AISwimming>((int)tmpAi.get<jsonxx::Number>("priority"));
		}
		else if (aiName.compare("sit") == 0)
		{
			addAiTask<AISit>((int)tmpAi.get<jsonxx::Number>("priority"));
		}
		else if (aiName.compare("restrict_sun") == 0)
		{
			addAiTask<AIRestrictSun>((int)tmpAi.get<jsonxx::Number>("priority"));
		}
		else if (aiName.compare("flee_sun") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AIFleeSun>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("beg") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("id") || !tmpAi.has<jsonxx::Number>("dist"))
				continue;
			addAiTask<AIBeg>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("id"), (int)tmpAi.get<jsonxx::Number>("dist"));
		}
		else if (aiName.compare("fish_beg") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("need_favor_food") || !tmpAi.has<jsonxx::Number>("range") || !tmpAi.has<jsonxx::Boolean>("fear_motion"))
				continue;

			addAiTask<AIFishBeg>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("need_favor_food"), (int)tmpAi.get<jsonxx::Number>("range"), tmpAi.get<jsonxx::Boolean>("fear_motion"));
		}
		else if (aiName.compare("fear_player") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("dist") || !tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AIFearPlayer>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("dist"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("boom_atk") == 0)
		{
			addAiTask<AIBoom>((int)tmpAi.get<jsonxx::Number>("priority"));
		}
		else if (aiName.compare("panic") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed"))
				continue;

			float maxHp = tmpAi.has<jsonxx::Number>("maxhp") ? (float)tmpAi.get<jsonxx::Number>("maxhp") : 0;
			addAiTask<AIPanic>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"), maxHp);
		}
		else if (aiName.compare("mate") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed"))
				continue;

			int itemID = tmpAi.has<jsonxx::Number>("itemid") ? (int)tmpAi.get<jsonxx::Number>("itemid") : 0;
			addAiTask<AIMate>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"), 1, 1, itemID);
		}
		else if (aiName.compare("tempt") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("id") || !tmpAi.has<jsonxx::Boolean>("scared"))
				continue;

			addAiTaskTempt((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"), (int)tmpAi.get<jsonxx::Number>("id"), tmpAi.get<jsonxx::Boolean>("scared"));
		}
		else if (aiName.compare("eat_grass") == 0)
		{
			addAiTask<AIEatGrass>((int)tmpAi.get<jsonxx::Number>("priority"));
		}
		else if (aiName.compare("wander") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed"))
				continue;

			int prop = tmpAi.has<jsonxx::Number>("prop") ? (int)tmpAi.get<jsonxx::Number>("prop") : 120;
			addAiTask<AIWander>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"), prop);
		}
		else if (aiName.compare("watch_closest") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("dist"))
				continue;

			int prop = tmpAi.has<jsonxx::Number>("prop") ? (int)tmpAi.get<jsonxx::Number>("prop") : 50;
			addAiTask<AIWatchClosest>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("dist"), prop);
		}
		else if (aiName.compare("look_idle") == 0)
		{
			int prop = tmpAi.has<jsonxx::Number>("prop") ? (int)tmpAi.get<jsonxx::Number>("prop") : 50;
			addAiTask<AILookIdle>((int)tmpAi.get<jsonxx::Number>("priority"), prop);
		}
		else if (aiName.compare("rand_swim") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("interval"))
				continue;

			addAiTask<AIRandomSwim>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("interval"));
		}
		else if (aiName.compare("break_door") == 0)
		{
			addAiTask<AIBreakDoor>((int)tmpAi.get<jsonxx::Number>("priority"));
		}
		else if (aiName.compare("attack") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("attack_type") || !tmpAi.has<jsonxx::Boolean>("will_trace") || !tmpAi.has<jsonxx::Number>("move_speed"))
				continue;

			addAiTask<AIAtk>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("attack_type"), tmpAi.get<jsonxx::Boolean>("will_trace"), (float)tmpAi.get<jsonxx::Number>("move_speed"));
		}
		else if (aiName.compare("fish_attack") == 0)
		{
			if (!tmpAi.has<jsonxx::Boolean>("will_trace") || !tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AIFishAttack>((int)tmpAi.get<jsonxx::Number>("priority"), tmpAi.get<jsonxx::Boolean>("will_trace"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("arrow_attack") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("min_atktime") || !tmpAi.has<jsonxx::Number>("max_atktime") || !tmpAi.has<jsonxx::Number>("atkrange"))
				continue;

			addAiTask<AIArrowAttack>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"), \
				(int)tmpAi.get<jsonxx::Number>("min_atktime"), (int)tmpAi.get<jsonxx::Number>("max_atktime"), (int)tmpAi.get<jsonxx::Number>("atkrange"));
		}
		else if (aiName.compare("projectile_attack") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("min_atktime") || !tmpAi.has<jsonxx::Number>("max_atktime") || !tmpAi.has<jsonxx::Number>("atkrange") || \
				!tmpAi.has<jsonxx::Number>("projectileid") || !tmpAi.has<jsonxx::Number>("power") || !tmpAi.has<jsonxx::Number>("buffid") || !tmpAi.has<jsonxx::Number>("count"))
				continue;

			int prob = tmpAi.has<jsonxx::Number>("prob") ? (int)tmpAi.get<jsonxx::Number>("prob") : 100; // 冒险版本之前插件没有Prob  addAiTaskProjectileAttack参数默认100
			addAiTask<AIProjectileAttack>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"), \
				(int)tmpAi.get<jsonxx::Number>("min_atktime"), (int)tmpAi.get<jsonxx::Number>("max_atktime"), (int)tmpAi.get<jsonxx::Number>("atkrange"), (int)tmpAi.get<jsonxx::Number>("projectileid"), \
				(float)tmpAi.get<jsonxx::Number>("power"), (int)tmpAi.get<jsonxx::Number>("buffid"), (int)tmpAi.get<jsonxx::Number>("count"), prob);
		}
		else if (aiName.compare("leap_at_target") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("motion_y") || !tmpAi.has<jsonxx::Number>("min_range") || !tmpAi.has<jsonxx::Number>("max_range"))
				continue;

			addAiTask<AILeapAtTarget>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("motion_y"), (int)tmpAi.get<jsonxx::Number>("min_range"), (int)tmpAi.get<jsonxx::Number>("max_range"));
		}
		else if (aiName.compare("follow_owner") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("max_dist") || !tmpAi.has<jsonxx::Number>("min_dist"))
				continue;

			addAiTask<AIFollowOwner>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"), (int)tmpAi.get<jsonxx::Number>("max_dist"), (int)tmpAi.get<jsonxx::Number>("min_dist"), 0);
		}
		else if (aiName.compare("target_hurtee") == 0)
		{
			if (!tmpAi.has<jsonxx::Boolean>("help"))
				continue;

			addAiTaskTarget<AITargetHurtee>((int)tmpAi.get<jsonxx::Number>("priority"), tmpAi.get<jsonxx::Boolean>("help"), true);
		}
		else if (aiName.compare("target_onwner_hurtee") == 0)
		{
			addAiTaskTarget<AITargetOwnerHurtee>((int)tmpAi.get<jsonxx::Number>("priority"));
		}
		else if (aiName.compare("target_onwner_hurter") == 0)
		{
			addAiTaskTarget<AITargetOwnerHurter>((int)tmpAi.get<jsonxx::Number>("priority"));
		}
		else if (aiName.compare("target_nearest") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("chance") || !tmpAi.has<jsonxx::Boolean>("check_sight") || !tmpAi.has<jsonxx::Number>("brightness"))
				continue;

			float minHp = tmpAi.has<jsonxx::Number>("minhp") ? (float)tmpAi.get<jsonxx::Number>("minhp") : 0;
			addAiTaskTarget<AITargetNearest>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("chance"), tmpAi.get<jsonxx::Boolean>("check_sight"), \
				(float)tmpAi.get<jsonxx::Number>("brightness"), minHp, 0);
		}
		else if (aiName.compare("target_nontamed") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("atkeeid") || !tmpAi.has<jsonxx::Number>("chance"))
				continue;

			addAiTaskTarget<AITargetNonTamed>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("atkeeid"), (int)tmpAi.get<jsonxx::Number>("chance"));
		}
		else if (aiName.compare("follow_direction") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AIFollowDirection>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("follow_parent") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AIFollowParent>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("move_towards_restriction") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AIMoveTowardsRestriction>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("ceiling_atk") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("attach_timer"))
				continue;

			addAiTask<AICeilingAtk>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("attach_timer"));
		}
		else if (aiName.compare("get_special_attack_attr") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AIGetSpecialAttackattr>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("block_id"), (int)tmpAi.get<jsonxx::Number>("prob"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("go_ceiling") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob"))
				continue;

			addAiTask<AIGoCeiling>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"));
		}
		else if (aiName.compare("hold_monster") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("monster_id") || !tmpAi.has<jsonxx::Number>("prob"))
				continue;

			addAiTask<AIHoldMonster>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("monster_id"), (int)tmpAi.get<jsonxx::Number>("prob"));
		}
		else if (aiName.compare("logger_heads") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("item_id"))
				continue;

			addAiTask<AILoggerHeads>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"), (int)tmpAi.get<jsonxx::Number>("item_id"));
		}
		else if (aiName.compare("love_block") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AILoveBlock>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("block_id"), (int)tmpAi.get<jsonxx::Number>("prob"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("sit_by_item") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AISitbyItem>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("target_following_player") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Boolean>("check_sight") || !tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AITargetFollowingPlayer>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), tmpAi.get<jsonxx::Boolean>("check_sight"), (int)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("closest_dance") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("dist") || !tmpAi.has<jsonxx::Number>("number") || !tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("cd_time") || \
				!tmpAi.has<jsonxx::Number>("width") || !tmpAi.has<jsonxx::Number>("dance_time"))
				continue;

			addAiTaskTarget<AIClosestDance>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("dist"), (int)tmpAi.get<jsonxx::Number>("number"), (float)tmpAi.get<jsonxx::Number>("speed"), \
				(int)tmpAi.get<jsonxx::Number>("cd_time"), (int)tmpAi.get<jsonxx::Number>("width"), (int)tmpAi.get<jsonxx::Number>("dance_time"), 20);
		}
		else if (aiName.compare("eat_feed_block") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("feed_block") || !tmpAi.has<jsonxx::Number>("max_ticks"))
				continue;

			addAiTask<AIEatFeedBlock>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("feed_block"), (int)tmpAi.get<jsonxx::Number>("max_ticks"));
		}
		else if (aiName.compare("eat_flower") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("prob_adult"))
				continue;

			addAiTask<AIEatFlower>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("prob_adult"));
		}
		else if (aiName.compare("eat_leaf") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("prob_adult"))
				continue;

			addAiTask<AIEatLeaf>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("prob_adult"));
		}
		else if (aiName.compare("hatch") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("hatch_blockid") || !tmpAi.has<jsonxx::Number>("max_ticks"))
				continue;
			//bayid是新增的,做个兼容
			//之前只有鸡会
			int babyid = 3813;
			if (tmpAi.has<jsonxx::Number>("baby_id"))
			{
				babyid = (int)tmpAi.get<jsonxx::Number>("baby_id");
			}
			addAiTask<AIHatch>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("hatch_blockid"), (int)tmpAi.get<jsonxx::Number>("max_ticks"), babyid);
		}
		else if (aiName.compare("kick_away") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("monster_id") || !tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("force"))
				continue;

			addAiTask<AIKickAway>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("monster_id"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("force"));
		}
		else if (aiName.compare("lay_egg_in_nest") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("egg_id") || !tmpAi.has<jsonxx::Number>("nestgen"))
				continue;
			//egg_blockid是新增的,做个兼容
			//之前只有鸡会
			int egg_blockid = 1185;
			if (tmpAi.has<jsonxx::Number>("egg_blockid"))
			{
				egg_blockid = (int)tmpAi.get<jsonxx::Number>("egg_blockid");
			}
			addAiTask<AILayEggInNest>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("block_id"), (int)tmpAi.get<jsonxx::Number>("egg_id"), (int)tmpAi.get<jsonxx::Number>("nestgen"), egg_blockid);
		}
		else if (aiName.compare("lay_eggs") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("lay_prob") || !tmpAi.has<jsonxx::String>("lay_sound") || !tmpAi.has<jsonxx::Number>("item1"))
				continue;

			int prop1 = tmpAi.has<jsonxx::Number>("prob1") ? (int)tmpAi.get<jsonxx::Number>("prob1") : 100;
			int item2 = tmpAi.has<jsonxx::Number>("item2") ? (int)tmpAi.get<jsonxx::Number>("item2") : 0;
			int prop2 = tmpAi.has<jsonxx::Number>("prop2") ? (int)tmpAi.get<jsonxx::Number>("prop2") : 0;
			int item3 = tmpAi.has<jsonxx::Number>("item3") ? (int)tmpAi.get<jsonxx::Number>("item3") : 0;
			int prop3 = tmpAi.has<jsonxx::Number>("prop3") ? (int)tmpAi.get<jsonxx::Number>("prop3") : 0;

			addAiTaskLayEggs((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("lay_prob"), tmpAi.get<jsonxx::String>("lay_sound").c_str(), \
				(int)tmpAi.get<jsonxx::Number>("item1"), prop1, item2, prop2, item3, prop3);
		}
		else if (aiName.compare("make_trouble") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("search_block_dist") || !tmpAi.has<jsonxx::Boolean>("is_hit"))
				continue;

			addAiTask<AIMakeTrouble>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("block_id"), (int)tmpAi.get<jsonxx::Number>("prob"), \
				(float)tmpAi.get<jsonxx::Number>("speed"), (float)tmpAi.get<jsonxx::Number>("search_block_dist"), tmpAi.get<jsonxx::Boolean>("is_hit"));
		}
		else if (aiName.compare("milking") == 0)
		{
			addAiTask<AIMilking>((int)tmpAi.get<jsonxx::Number>("priority"));
		}
		else if (aiName.compare("ride") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("monster_id") || !tmpAi.has<jsonxx::Number>("dist"))
				continue;

			addAiTask<AIRideHorse>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("monster_id"), (int)tmpAi.get<jsonxx::Number>("dist"));
		}
		else if (aiName.compare("sleep") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("period") || !tmpAi.has<jsonxx::Number>("sleep_ticks"))
				continue;

			int productID = tmpAi.has<jsonxx::Number>("product_id") ? (int)tmpAi.get<jsonxx::Number>("product_id") : 0;
			int minNum = tmpAi.has<jsonxx::Number>("min_num") ? (int)tmpAi.get<jsonxx::Number>("min_num") : 0;
			int maxNum = tmpAi.has<jsonxx::Number>("max_num") ? (int)tmpAi.get<jsonxx::Number>("max_num") : 0;
			addAiTask<AISleep>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("period"), (int)tmpAi.get<jsonxx::Number>("sleep_ticks"), productID, minNum, maxNum);
		}
		else if (aiName.compare("target_specific_entity") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("atkeeid") || !tmpAi.has<jsonxx::Number>("chance"))
				continue;

			addAiTaskTarget<AITargetSpecificEntity>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("atkeeid"), (int)tmpAi.get<jsonxx::Number>("chance"));
		}
		else if (aiName.compare("transfiguration") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("target_id") || !tmpAi.has<jsonxx::Number>("wait_tick"))
				continue;

			addAiTask<AITransfiguration>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("target_id"), (int)tmpAi.get<jsonxx::Number>("wait_tick"));
		}
		else if (aiName.compare("topple_over") == 0)
		{
			addAiTask<AIToppleOver>((int)tmpAi.get<jsonxx::Number>("priority"));
		}
		else if (aiName.compare("gather") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("range") || !tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("speed") || \
				!tmpAi.has<jsonxx::Number>("distance") || !tmpAi.has<jsonxx::Number>("hungry_check") || !tmpAi.has<jsonxx::Number>("hungry_expend"))
				continue;

			addAiTask<AIDigBlock>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("range"), (int)tmpAi.get<jsonxx::Number>("block_id"), (float)tmpAi.get<jsonxx::Number>("speed"),
				(int)tmpAi.get<jsonxx::Number>("distance"), (int)tmpAi.get<jsonxx::Number>("hungry_check"), (float)tmpAi.get<jsonxx::Number>("hungry_expend"));
		}
		else if (aiName.compare("pick") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("item_id") || !tmpAi.has<jsonxx::Number>("quantity") || !tmpAi.has<jsonxx::Number>("range") || \
				!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("distance") || !tmpAi.has<jsonxx::Number>("hungry_check") || !tmpAi.has<jsonxx::Number>("hungry_expend"))
				continue;

			addAiTask<AIPickupItem>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("item_id"), (int)tmpAi.get<jsonxx::Number>("quantity"), (int)tmpAi.get<jsonxx::Number>("range"),
				(float)tmpAi.get<jsonxx::Number>("speed"), (int)tmpAi.get<jsonxx::Number>("distance"), (int)tmpAi.get<jsonxx::Number>("hungry_check"), (float)tmpAi.get<jsonxx::Number>("hungry_expend"));
		}
		else if (aiName.compare("store_item") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("item_id") || !tmpAi.has<jsonxx::Number>("quantity") || !tmpAi.has<jsonxx::Number>("range") || \
				!tmpAi.has<jsonxx::Number>("container_id") || !tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("distance") || !tmpAi.has<jsonxx::Number>("hungry_check") || \
				!tmpAi.has<jsonxx::Number>("hungry_expend"))
				continue;

			addAiTask<AIStoreItem>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("item_id"), (int)tmpAi.get<jsonxx::Number>("quantity"), (int)tmpAi.get<jsonxx::Number>("range"),
				1, (int)tmpAi.get<jsonxx::Number>("container_id"), (float)tmpAi.get<jsonxx::Number>("speed"), (int)tmpAi.get<jsonxx::Number>("distance"), (int)tmpAi.get<jsonxx::Number>("hungry_check"), (float)tmpAi.get<jsonxx::Number>("hungry_expend"));
		}
		else if (aiName.compare("get_item") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("item_id") || !tmpAi.has<jsonxx::Number>("quantity") || !tmpAi.has<jsonxx::Number>("range") || \
				!tmpAi.has<jsonxx::Number>("container_type") || !tmpAi.has<jsonxx::Number>("container_id") || !tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("distance") || \
				!tmpAi.has<jsonxx::Number>("hungry_check") || !tmpAi.has<jsonxx::Number>("hungry_expend"))
				continue;

			addAiTask<AITakeItem>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("item_id"), (int)tmpAi.get<jsonxx::Number>("quantity"), (int)tmpAi.get<jsonxx::Number>("range"),
				(int)tmpAi.get<jsonxx::Number>("container_type"), (int)tmpAi.get<jsonxx::Number>("container_id"), (float)tmpAi.get<jsonxx::Number>("speed"), (int)tmpAi.get<jsonxx::Number>("distance"), (int)tmpAi.get<jsonxx::Number>("hungry_check"), (float)tmpAi.get<jsonxx::Number>("hungry_expend"));
		}
		else if (aiName.compare("compose_item") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("craft_id") || !tmpAi.has<jsonxx::Number>("craft_time") || !tmpAi.has<jsonxx::Number>("hungry_check") || \
				!tmpAi.has<jsonxx::Number>("hungry_expend"))
				continue;

			addAiTask<AICraftItem>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("craft_id"), (float)(tmpAi.get<jsonxx::Number>("craft_time") * 20), (int)tmpAi.get<jsonxx::Number>("hungry_check"), (float)tmpAi.get<jsonxx::Number>("hungry_expend"));
		}
		else if (aiName.compare("like_sleep") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("what_time") || !tmpAi.has<jsonxx::Number>("how_long") || !tmpAi.has<jsonxx::Number>("sleep_blockid") || \
				!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("range"))
				continue;

			addAiTask<AINpcSleep>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("what_time"), (float)tmpAi.get<jsonxx::Number>("how_long"), (int)tmpAi.get<jsonxx::Number>("sleep_blockid"),
				(float)tmpAi.get<jsonxx::Number>("speed"), (int)tmpAi.get<jsonxx::Number>("range"));
		}
		else if (aiName.compare("hungry") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("hungry_check") || !tmpAi.has<jsonxx::Number>("how_long") || !tmpAi.has<jsonxx::Boolean>("onlyone_food") || \
				!tmpAi.has<jsonxx::Number>("food_id"))
				continue;

			addAiTask<AIHunger>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("hungry_check"), (float)tmpAi.get<jsonxx::Number>("how_long"), tmpAi.get<jsonxx::Boolean>("onlyone_food") ? 1 : 0,
				(int)tmpAi.get<jsonxx::Number>("food_id"));
		}
		else if (aiName.compare("eat_food") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("hungry_check") || !tmpAi.has<jsonxx::Boolean>("onlyone_food") || !tmpAi.has<jsonxx::Number>("food_id"))
				continue;

			int howLong = tmpAi.has<jsonxx::Number>("how_long") ? (int)tmpAi.get<jsonxx::Number>("how_long") * 20 : 60;
			addAiTask<AIEatFood>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), howLong, (int)tmpAi.get<jsonxx::Number>("hungry_check"), tmpAi.get<jsonxx::Boolean>("onlyone_food") ? 1 : 0, (int)tmpAi.get<jsonxx::Number>("food_id"));
		}
		else if (aiName.compare("plant") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("range") || !tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("item_id") || \
				!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("distance") || !tmpAi.has<jsonxx::Number>("hungry_check") || !tmpAi.has<jsonxx::Number>("hungry_expend"))
				continue;

			addAiTask<AIPlant>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("range"), (int)tmpAi.get<jsonxx::Number>("block_id"), (int)tmpAi.get<jsonxx::Number>("item_id"), (float)tmpAi.get<jsonxx::Number>("speed"),
				(int)tmpAi.get<jsonxx::Number>("distance"), (int)tmpAi.get<jsonxx::Number>("hungry_check"), (float)tmpAi.get<jsonxx::Number>("hungry_expend"));
		}
		else if (aiName.compare("fly_attack") == 0)
		{
			if (!tmpAi.has<jsonxx::Boolean>("trace") || !tmpAi.has<jsonxx::Number>("speed"))
				continue;

			int buffId = tmpAi.has<jsonxx::Number>("buff_id") ? (int)tmpAi.get<jsonxx::Number>("buff_id") : 0;
			int buffLevel = tmpAi.has<jsonxx::Number>("buff_level") ? (int)tmpAi.get<jsonxx::Number>("buff_level") : 1;
			addAiTask<AIFlyAttack>((int)tmpAi.get<jsonxx::Number>("priority"), tmpAi.get<jsonxx::Boolean>("trace"), (float)tmpAi.get<jsonxx::Number>("speed"), buffId, buffLevel);
		}
		else if (aiName.compare("rand_fly") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("interval") || !tmpAi.has<jsonxx::Number>("max_height"))
				continue;

			int maxRange = tmpAi.has<jsonxx::Number>("max_range") ? (int)tmpAi.get<jsonxx::Number>("max_range") : 0;
			addAiTask<AIRandomFly>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("interval"), (int)tmpAi.get<jsonxx::Number>("max_height"), maxRange);
		}
		else if (aiName.compare("fly_love_block") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("love_tick"))
				continue;

			addAiTask<AIFlyLoveBlock>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("block_id"), (int)tmpAi.get<jsonxx::Number>("prob"), (float)tmpAi.get<jsonxx::Number>("speed"), (int)tmpAi.get<jsonxx::Number>("love_tick"));
		}
		else if (aiName.compare("fly_beg") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("food_id") || !tmpAi.has<jsonxx::Number>("dist") || !tmpAi.has<jsonxx::Boolean>("fear_motion"))
				continue;

			addAiTask<AIFlyBeg>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("food_id"), (int)tmpAi.get<jsonxx::Number>("dist"), tmpAi.get<jsonxx::Boolean>("fear_motion"));
		}
		else if (aiName.compare("wizard_projectile_attack") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("min_atk_time") || !tmpAi.has<jsonxx::Number>("max_atk_time") || !tmpAi.has<jsonxx::Number>("atk_range") || \
				!tmpAi.has<jsonxx::Number>("projectile_id") || !tmpAi.has<jsonxx::Number>("power") || !tmpAi.has<jsonxx::Number>("buff_id") || !tmpAi.has<jsonxx::Number>("count"))
				continue;

			addAiTask<AIWizardProjectileAttack>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"), (int)tmpAi.get<jsonxx::Number>("min_atk_time"), (int)tmpAi.get<jsonxx::Number>("max_atk_time"),
				(int)tmpAi.get<jsonxx::Number>("atk_range"), (int)tmpAi.get<jsonxx::Number>("projectile_id"), (float)tmpAi.get<jsonxx::Number>("power"), (int)tmpAi.get<jsonxx::Number>("buff_id"), (int)tmpAi.get<jsonxx::Number>("count"));
		}
		else if (aiName.compare("wizard_attack") == 0)
		{
			float speed = tmpAi.has<jsonxx::Number>("speed") ? (float)tmpAi.get<jsonxx::Number>("speed") : 0.2f;
			int damage = tmpAi.has<jsonxx::Number>("damage") ? (int)tmpAi.get<jsonxx::Number>("damage") : 5;
			int atkDist = tmpAi.has<jsonxx::Number>("atk_dist") ? (int)tmpAi.get<jsonxx::Number>("atk_dist") : 0;
			int confineTick = tmpAi.has<jsonxx::Number>("confine_tick") ? (int)tmpAi.get<jsonxx::Number>("confine_tick") : 40;
			int continueTick = tmpAi.has<jsonxx::Number>("continue_tick") ? (int)tmpAi.get<jsonxx::Number>("continue_tick") : 160;
			int noConfineTick = tmpAi.has<jsonxx::Number>("no_confine_tick") ? (int)tmpAi.get<jsonxx::Number>("no_confine_tick") : 100;
			int buffId = tmpAi.has<jsonxx::Number>("buff_id") ? (int)tmpAi.get<jsonxx::Number>("buff_id") : 46;
			int buffLevel = tmpAi.has<jsonxx::Number>("buff_level") ? (int)tmpAi.get<jsonxx::Number>("buff_level") : 1;
			int prob = tmpAi.has<jsonxx::Number>("prob") ? (int)tmpAi.get<jsonxx::Number>("prob") : 10;

			addAiTask<AIWizardAttack>((int)tmpAi.get<jsonxx::Number>("priority"), speed, damage, atkDist,
				confineTick, continueTick, noConfineTick, buffId,
				buffLevel, prob);
		}
		else if (aiName.compare("wizard_fly") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("interval") || !tmpAi.has<jsonxx::Number>("max_height"))
				continue;

			int minHeight = tmpAi.has<jsonxx::Number>("min_height") ? (int)tmpAi.get<jsonxx::Number>("min_height") : 0;
			int maxRange = tmpAi.has<jsonxx::Number>("max_range") ? (int)tmpAi.get<jsonxx::Number>("max_range") : 0;
			addAiTask<AIWizardFly>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("interval"), (int)tmpAi.get<jsonxx::Number>("max_height"), minHeight, maxRange);
		}
		else if (aiName.compare("player_panic") == 0)
		{
			float speed = tmpAi.has<jsonxx::Number>("speed") ? (float)tmpAi.get<jsonxx::Number>("speed") : 1.4f;
			int range = tmpAi.has<jsonxx::Number>("range") ? (int)tmpAi.get<jsonxx::Number>("range") : 8 * BLOCK_SIZE;
			int panicTick = tmpAi.has<jsonxx::Number>("panic_tick") ? (int)tmpAi.get<jsonxx::Number>("panic_tick") : 40;
			int intervalTick = tmpAi.has<jsonxx::Number>("interval_tick") ? (int)tmpAi.get<jsonxx::Number>("interval_tick") : 40;

			addAiTask<AIPlayerPanic>((int)tmpAi.get<jsonxx::Number>("priority"), speed, range, panicTick, intervalTick);
		}
		else if (aiName.compare("combine") == 0)
		{
			float speed = tmpAi.has<jsonxx::Number>("speed") ? (float)tmpAi.get<jsonxx::Number>("speed") : 1.4f;
			int dist = tmpAi.has<jsonxx::Number>("dist") ? (int)tmpAi.get<jsonxx::Number>("dist") : 8 * BLOCK_SIZE;
			int intervalTick = tmpAi.has<jsonxx::Number>("interval_tick") ? (int)tmpAi.get<jsonxx::Number>("interval_tick") : 200;
			int fitTick = tmpAi.has<jsonxx::Number>("fit_tick") ? (int)tmpAi.get<jsonxx::Number>("fit_tick") : 200;
			float minHpFit = tmpAi.has<jsonxx::Number>("min_hp_fit") ? (float)tmpAi.get<jsonxx::Number>("min_hp_fit") : 0.5f;
			float fitCircleAngle = tmpAi.has<jsonxx::Number>("fit_circle_angle") ? (float)tmpAi.get<jsonxx::Number>("fit_circle_angle") : 22.0f;

			addAiTask<AICombine>((int)tmpAi.get<jsonxx::Number>("priority"), speed, dist, intervalTick,
				fitTick, minHpFit, fitCircleAngle);
		}
		else if (aiName.compare("separate_panic") == 0)
		{
			float speed = tmpAi.has<jsonxx::Number>("speed") ? (float)tmpAi.get<jsonxx::Number>("speed") : 1.4f;
			int panicTick = tmpAi.has<jsonxx::Number>("panic_tick") ? (int)tmpAi.get<jsonxx::Number>("panic_tick") : 160;

			addAiTask<AISeparatePanic>((int)tmpAi.get<jsonxx::Number>("priority"), speed, panicTick);
		}
		else if (aiName.compare("bump_attack") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("dist") || !tmpAi.has<jsonxx::Number>("dizzy_tick") || !tmpAi.has<jsonxx::Number>("pre_tick") || !tmpAi.has<jsonxx::Number>("prob") || \
				!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("up") || !tmpAi.has<jsonxx::Number>("back"))
				continue;

			int mode = tmpAi.has<jsonxx::Number>("mode") ? (int)tmpAi.get<jsonxx::Number>("mode") : 0;
			addAiTask<AIBumpAttack>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("dist"), (int)tmpAi.get<jsonxx::Number>("dizzy_tick"), (int)tmpAi.get<jsonxx::Number>("pre_tick"),
				(int)tmpAi.get<jsonxx::Number>("prob"), (float)tmpAi.get<jsonxx::Number>("speed"), (float)tmpAi.get<jsonxx::Number>("up"), (float)tmpAi.get<jsonxx::Number>("back"), mode);
		}
		else if (aiName.compare("mutate_fly") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AIMutateFly>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("block_id"), (int)tmpAi.get<jsonxx::Number>("prob"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("patrol_on_block") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("speed"))
				continue;

			addAiTask<AIPatrolOnBlock>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("block_id"), (int)tmpAi.get<jsonxx::Number>("prob"), (float)tmpAi.get<jsonxx::Number>("speed"));
		}
		else if (aiName.compare("target_mutate") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("buff_id") || !tmpAi.has<jsonxx::Boolean>("check_sight") || \
				!tmpAi.has<jsonxx::Number>("length"))
				continue;

			addAiTaskTarget<AIMutateTarget>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("block_id"), (int)tmpAi.get<jsonxx::Number>("prob"), (int)tmpAi.get<jsonxx::Number>("buff_id"),
				tmpAi.get<jsonxx::Boolean>("check_sight"), (int)tmpAi.get<jsonxx::Number>("length"));
		}
		else if (aiName.compare("target_scream") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("sound_tick"))
				continue;

			addAiTask<AITargetScream>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("sound_tick"));
		}
		else if (aiName.compare("target_panic_buff") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("buff_id"))
				continue;
			addAiTask<AIPanicBuff>((int)tmpAi.get<jsonxx::Number>("priority"), (float)tmpAi.get<jsonxx::Number>("speed"), (int)tmpAi.get<jsonxx::Number>("buff_id"));
		}
		else if (aiName.compare("eat_then_mutate") == 0)
		{
			if (!tmpAi.has<jsonxx::Number>("block_id") || !tmpAi.has<jsonxx::Number>("prob") || !tmpAi.has<jsonxx::Number>("speed") || !tmpAi.has<jsonxx::Number>("eat_tick") || \
				!tmpAi.has<jsonxx::Number>("die_tick") || !tmpAi.has<jsonxx::Number>("buff_id") || !tmpAi.has<jsonxx::Number>("buff_tick"))
				continue;

			addAiTask<AIEatThenMutate>((int)tmpAi.get<jsonxx::Number>("priority"), (int)tmpAi.get<jsonxx::Number>("block_id"), (int)tmpAi.get<jsonxx::Number>("prob"), (float)tmpAi.get<jsonxx::Number>("speed"),
				(int)tmpAi.get<jsonxx::Number>("eat_tick"), (int)tmpAi.get<jsonxx::Number>("die_tick"), (int)tmpAi.get<jsonxx::Number>("buff_id"), (int)tmpAi.get<jsonxx::Number>("buff_tick"));
		}
	}
}


void ClientMob::ParseAIByMonsterModConfig(const MonsterDef* def, const jsonxx::Object& jsonObj)
{
	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setAvoidWater(false);
		functionWrapper->setCanPassClosedWoodenDoors(false);
		functionWrapper->setCanSwimming(false);
		functionWrapper->setCanFly(false);
	}
	const jsonxx::Object& propObj = jsonObj.get<jsonxx::Object>("properties");
	
	bool value = false;
	if (UnSerializeValue<bool, jsonxx::Boolean>("goCeiling", propObj, value) && value)
		addAiTask<AIGoCeiling>(1, 1);
	
	if (UnSerializeValue<bool, jsonxx::Boolean>("fleeSun", propObj, value) && value)
		addAiTask<AIFleeSun>(3, 1);

	if (UnSerializeValue<bool, jsonxx::Boolean>("restrictSun", propObj, value) && value)
		addAiTask<AIRestrictSun>(2);
	
	if (UnSerializeValue<bool, jsonxx::Boolean>("swimming", propObj, value) && value)
	{
		if (functionWrapper)
		{
			functionWrapper->setCanSwimming(value);
		}
		addAiTask<AISwimming>(1);
	}
	UnSerializeValue<bool, jsonxx::Boolean>("avoidWater", propObj, value);
	if (functionWrapper)
	{
		functionWrapper->setAvoidWater(value);
	}
	UnSerializeValue<bool, jsonxx::Boolean>("canFly", propObj, value);
	if (functionWrapper)
	{
		functionWrapper->setCanFly(value);
	}
	UnSerializeValue<bool, jsonxx::Boolean>("canPassClosedWoodenDoors", propObj, value);
	
	if (functionWrapper)
	{
		functionWrapper->setCanPassClosedWoodenDoors(value);
	}
}

void ClientMob::ParseAIByFightModConfig(const MonsterDef* def, const jsonxx::Object& jsonObj)
{
	const jsonxx::Object& propObj = jsonObj.get<jsonxx::Object>("properties");

	bool value = false;
	bool bAttack = false;
	int nAttackType = 0;
	int nActiveAtk = 0;

	if (UnSerializeValue<bool, jsonxx::Boolean>("ceilingAtk", propObj, value) && value) 	//跳到目标头上
		addAiTask<AICeilingAtk>(0, 200);

	if (UnSerializeValue<bool, jsonxx::Boolean>("leapAtTarget", propObj, value) && value)//跳跃攻击
		addAiTask<AILeapAtTarget>(2, 40.0f, 100, 600);

	if (UnSerializeValue<bool, jsonxx::Boolean>("invulnerableProb", propObj, value) && value) //概率闪避攻击
		addAiTask<AIRestrictSun>(2);

	if (UnSerializeValue<bool, jsonxx::Boolean>("immuneFire", propObj, value) && value) //免疫火焰伤害
		setImmuneToFire(2);

	if (UnSerializeValue<bool, jsonxx::Boolean>("pathHide", propObj, value) && value)	//隐秘寻路
		setPathHide(1);

	if (UnSerializeValue<bool, jsonxx::Boolean>("sunHurt", propObj, value) && value)	 	//阳光照射受伤
		setSunHurt(true);
	
	UnSerializeValue<bool, jsonxx::Boolean>("isAttack", propObj, bAttack);	 	//是否攻击
	UnSerializeValue<int, jsonxx::Number>("attackType", propObj, nAttackType);	 	//攻击类型
	UnSerializeValue<int, jsonxx::Number>("activeAtk", propObj, nActiveAtk);	 	//是否攻击
	if (bAttack)
	{
		addAiTask<AIAtk>(0, nActiveAtk, false, 1.0f);
		addAiTaskTarget<AITargetNearest>(2, 0, true, 0, 0, 0);
	}	
}


void ClientMob::addAiTaskTempt(int iPriority, float speed, int iSeedID, bool bScared)
{
	if (NULL == m_AITask)
	{
		m_AITask = ENG_NEW(AITask)(this);
	}
	m_aiTempt = ENG_NEW(AITempt)(this, speed, iSeedID, bScared);
	m_AITask->addTask(iPriority, m_aiTempt);


	if (IsUseAILua())
	{
		m_aiTempt->m_bPriority = iPriority;
		GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCurTaskAdd", "u[AIBase]", m_aiTempt);
	}
}

void ClientMob::addAIPickupItemExItem(int itemID)
{
	if (NULL == m_AITask)
	{
		return;
	}
	AIPickupItemEx* p = static_cast<AIPickupItemEx*>(m_AITask->getTaskAI<AIPickupItemEx>());
	if (p)
	{
		p->addItemID(itemID);
	}
}

void ClientMob::addAiTaskLayEggs(int iPriority, int layprob, const char* laysound, int item1, int prob1, int item2, int prob2, int item3, int prob3)
{
	if (NULL == m_AITask)
	{
		m_AITask = ENG_NEW(AITask)(this);
	}

	int items[4];
	int probs[4];
	int nitem = 0;

	items[nitem] = item1;
	probs[nitem] = prob1;
	nitem++;

	if (item2 > 0)
	{
		items[nitem] = item2;
		probs[nitem] = prob2;
		nitem++;
	}

	if (item3 > 0)
	{
		items[nitem] = item3;
		probs[nitem] = prob3;
		nitem++;
	}

	AIBase* NewAI = ENG_NEW(AILayEggs)(this, layprob, nitem, items, probs, laysound);
	m_AITask->addTask(iPriority, NewAI);

	if (IsUseAILua())
	{
		NewAI->m_bPriority = iPriority;
		GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCurTaskAdd", "u[AIBase]", NewAI);
	}
}

void ClientMob::removeAiTaskTempt(AIBase* aiTempt)
{
	if (m_AITask != NULL && m_aiTempt != NULL)
	{
		m_AITask->removeTask(aiTempt);
		m_aiTempt = NULL;
	}
}

void ClientMob::AITick()
{
	OPTICK_EVENT();
	//SANDBOXPROFILING_FUNC("ClientMob::AITick");

	if (needUpdateAI())
	{
		if (getNavigator() == NULL)
		{
			CreateComponent<NavigationPath>("NavigationPath");
		}
		if (!m_isUseAILua)
		{
			if (m_AITask) m_AITask->onUpdateTasks();
			if (m_AITaskTarget) m_AITaskTarget->onUpdateTasks();
		}

		aiTick();
	}
}


// 重置行为树
void ClientMob::ResetBTree()
{
	if (m_btree)
	{
		clearAnimFlatBit();
		if (isSleeping())
		{
			ActorVillager* ager = dynamic_cast<ActorVillager*>(this);
			//if (ager)
			//{
			//	if (ager->getDefID() == 3200 || ager->getDefID() == 3201 || ager->getDefID() == 3202) {
			//		int rountPlayer;
			//		ager->m_bb->GetData_Number("bb_roundPlayer", rountPlayer);
			//		if (rountPlayer == 100) {
			//			ager->setSleeping(false);
			//			setAIJumping(false);
			//			setCanSwimming(false);
			//			m_btree->Reset();
			//			return;
			//		}
			//		else {
			//			wakeUp();
			//		}
			//	}
			//	else {
			//		wakeUp();
			//	}
			//}
			//else {
			//	wakeUp();
			//}
			if (ager)
			{
				if (ager->getDefID() == 3200 || ager->getDefID() == 3201 || ager->getDefID() == 3202) {
					int rountPlayer;
					ager->m_bb->GetData_Number("bb_roundPlayer", rountPlayer);
					if (rountPlayer == 100) {
						ager->setSleeping(false);
						SetAIJumping(false);
						auto functionWrapper = getFuncWrapper();
						if (functionWrapper)
						{
							functionWrapper->setCanSwimming(false);
						}
						m_btree->Reset();
						return;
					}
				}
			}
			wakeUp();
		}
		else if (getSitting())
		{
			setSitting(false);
			if (getObjType() == OBJ_TYPE_DESERTBUSINESSMAN)
			{
				ActorDesertBusInessMan* man = dynamic_cast<ActorDesertBusInessMan*>(this);
				if (man != NULL)
				{
					man->standUp();
					if (getBody())
						getBody()->setHeadExchangeDisplayIcon(0, 0);
				}
			}
			else
			{
				ActorVillager* ager = dynamic_cast<ActorVillager*>(this);
				if (ager)
				{
					ager->standUp();
				}
			}
		}
		if (getObjType() == OBJ_TYPE_DESERTBUSINESSMANGUARD)
		{
			ActorDesertBusInessManGuard* guard = dynamic_cast<ActorDesertBusInessManGuard*>(this);
			if (guard != NULL)
			{
				auto effectComponent = guard->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->stopBodyEffect(BODYFX_AI_SLEEP);
				}
			}
		}
		SetAIJumping(false);
		auto functionWrapper = getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setCanSwimming(false);
		}
		m_btree->Reset();
	}
}

bool ClientMob::OpenBTreeForTest()
{
	if (m_btree)
	{
		MINIW::ScriptVM::game()->callFunction("OpenBTreeForTest", "i", (int)m_btree->GetInstanceID());
		return true;
	}
	return false;
}
