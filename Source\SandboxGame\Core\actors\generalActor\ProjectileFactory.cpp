#include "ProjectileFactory.h"
#include "ProjectileLocoMotion.h"
#include "ClientActorProjectile.h"
#include "ClientActorNewArrow.h"
#include "ClientActorEgg.h"
#include "ClientActorSnowBall.h"
#include "PlayerControl.h"
#include "GameCamera.h"
#include "GunUseComponent.h"
#include "ClientActorHook.h"
#include "ClientActorLaser.h"
#include "ClientActorAttract.h"
#include "ClientActorImpulse.h"
#include "ActorHorse.h"
#include "RiddenComponent.h"
#include "PlayerAttrib.h"
#include "ClientActorGlowSticks.h"
#include "ClientActorThornBall.h"
#include "ClientActorAirBall.h"
#include "ClientActorPirateChest.h"
#ifdef IWORLD_SERVER_BUILD
#include "ICloudProxy.h"
#endif
#include "world.h"
#include "LuaInterfaceProxy.h"
#include "ClientActorProjectile.h"
#include "AIFunctionMgr.h"
#include "world_types.h"
#include "GunGridDataComponent.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

static ClientActorProjectile* CreateProjectileFromItem(int itemId, ClientActor *actor = nullptr)
{
	ClientActorProjectile *projectile = NULL;

	PROJECTILE_TYPE type = ProjectileFactory::getTypeFromItemId(itemId);
	switch (type)
	{
	case PROJECTILE_ARROW:
		projectile = SANDBOX_NEW(ClientActorNewArrow);
		break;
	case PROJECTILE_EGG:
		projectile = SANDBOX_NEW(ClientActorEgg);
		break;
	case PROJECTILE_SNOWBALL:
		projectile = SANDBOX_NEW(ClientActorSnowBall);
		break;
	case PROJECTILE_HOOK:
		projectile = SANDBOX_NEW(ClientActorHook);
		break;
	case PROJECTILE_LASER:
		projectile = SANDBOX_NEW(ClientActorLaser);
		break;
	case PROJECTILE_ATTRACT:
		projectile = SANDBOX_NEW(ClientActorAttract);
		break;
	case PROJECTILE_IMPULSE:
		projectile = SANDBOX_NEW(ClientActorImpulse);
		break;
	case PROJECTILE_AIRBALL:
		projectile = SANDBOX_NEW(ClientActorAirBall);
		break;
	case PROJECTILE_GLOWSTICK:
		projectile = SANDBOX_NEW(ClientActorGlowSticks);
		break;
	case PROJECTILE_PIRATECHEST:
		projectile = SANDBOX_NEW(ClientActorPirateChest);
		break;
	case PROJECTILE_THORNBALL:
		projectile = SANDBOX_NEW(ClientActorThornBall);
		break;
	//case PROJECTILE_VEHICLE:
	//	projectile = SANDBOX_NEW(ActorVehicle);
	//	break;	
	default:
		projectile = SANDBOX_NEW(ClientActorProjectile);
	}

	projectile->init(itemId, actor);
	return projectile;
}

ClientActorProjectile *ProjectileFactory::throwItemByActorSpecifyTransform(World *pworld, ClientActor *shootactor, float yaw, float pitch, WCoord pos, float strength, int itemId, bool setfire, bool canpickup, int fireLv, unsigned char attachedEffect, bool damageInsByShooter, bool speedInsByStrength)
{
	ActorLocoMotion *shootloc = shootactor->getLocoMotion();

	const ProjectileDef *projectileDef = GetDefManagerProxy()->getProjectileDef(itemId);
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return NULL;
	int shakeTick = 0;
	if (itemId == ITEM_ARROW)
		shakeTick = 7;
	ClientActorProjectile *projectile = CreateProjectileFromItem(itemId);

	Rainbow::Vector3f dir = Yaw2FowardDir(yaw);
	pos.x += int(dir.x * 16);
	pos.z += int(dir.z * 16);
	pos.y -= 10;
	
	projectile->setShootingActor(shootactor); // 开发者需要在spawnActor之前知道shooter
	actorMgr->spawnActor(projectile, pos, yaw, pitch);
	projectile->m_StartPos = projectile->getPosition();

	ActorLiving *living = dynamic_cast<ActorLiving *>(shootactor);
	if (living)
	{
		LivingAttrib *attrib = living->getLivingAttrib();

		// 新伤害计算系统 code-by:liya
		if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
		{
			if (attrib)
				projectile->m_bowDamageIns = attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_BOW_ATTACK, ATTACK_RANGE, ATTACK_TARGET_ALL);
				
			projectile->UpdateAttackPoints();
				
			// 伤害受施法者属性加成
			if (damageInsByShooter && g_pPlayerCtrl && projectileDef->ID != g_pPlayerCtrl->getCurToolID())
			{
				projectile->UpdateAttackPointsByAttri(attrib);
			}
		}
		else
		{
			projectile->m_BuffAttackAdd = attrib->getModAttrib(MODATTR_ATTACK_RANGE);

			float bowenchant = attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_BOW_ATTACK, ATTACK_RANGE, ATTACK_TARGET_ALL);

			if (g_pPlayerCtrl && projectileDef->ID != g_pPlayerCtrl->getCurToolID())
			{
				projectile->m_AttackPoints = (attrib->getAttackPoint(ATTACK_RANGE, 1) * (1.0f + bowenchant) + projectileDef->AttackValue) * strength * (1.0f + projectile->m_BuffAttackAdd);
			}
			else
			{
				projectile->m_AttackPoints = (projectileDef->AttackValue) * strength * (1.0f + projectile->m_BuffAttackAdd);
			}
		}

		//projectile->m_AttackPoints = attrib->getAttackPoint(ATTACK_RANGE) * (1.0f + strength*7.0f) * (1.0f + bowenchant);
		projectile->m_KnockbackStrength += attrib->getKnockback(ATTACK_RANGE, ATTACK_TARGET_ALL);

		if (attachedEffect == 0)
		{
			if (attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_EXPLODE, ATTACK_RANGE, ATTACK_TARGET_ALL) > 0)
			{
				projectile->m_AttachedEffect |= 2;
			}
		}
	}

	PitchYaw2Direction(dir, yaw, pitch);
	if(speedInsByStrength)
		static_cast<ProjectileLocoMotion *>(projectile->getLocoMotion())->setThrowableHeading(dir, strength*300.0f, 1.0f);
	//projectile->GetModel()->playMotion("1026");

	if (attachedEffect == 0)
	{
		if (setfire)
		{
			projectile->m_AttachedEffect |= 1;
			projectile->m_FireLv = fireLv;
			projectile->playMotion("1028", true, 0);
		}
		if (!canpickup)
		{
			projectile->m_AttachedEffect |= 4;
		}
	}
	else
	{
		projectile->m_AttachedEffect = attachedEffect;
	}

	projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);

	return projectile;
}

ClientActorProjectile * ProjectileFactory::throwItemByMob(World *pworld, ClientActor *shootactor, float strength, int itemId, int buffId,int randomYaw, int randomPitch, int dropType)
{
	ActorLocoMotion *shootloc = shootactor->getLocoMotion();
	float yaw = shootloc->m_RotateYaw + GenRandomInt(-randomYaw, randomYaw);
	float pitch = shootloc->m_RotationPitch + GenRandomInt(-randomPitch, randomPitch);
	float speedadd = 0.0f;

	const ProjectileDef *projectileDef = GetDefManagerProxy()->getProjectileDef(itemId, true);
	if (!projectileDef) return NULL;

	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return NULL;

	ClientActorProjectile *projectile = CreateProjectileFromItem(projectileDef->ID);
	projectile->setBuff(buffId);
	if (dropType > 0)
	{
		projectile->setDropable(false);
	}

	Rainbow::Vector3f dir;
	WCoord pos;

	PitchYaw2Direction(dir, yaw, pitch);

	ClientMob* mob = dynamic_cast<ClientMob*>(shootactor);
	if (mob && mob->getDefID() == 3236)
		pos = mob->getLocoMotion() ? mob->getLocoMotion()->getPosition() + WCoord(0, mob->getLocoMotion()->m_BoundHeight * 50 / 100, 0) : WCoord(0,-1,0);
	else
		pos = WCoord(shootactor->getEyePosition());

	Quaternionf bodyRotation = AngleEulerToQuaternionf(Vector3f(pitch, yaw, 0));

	//Rainbow::Vector3f right  = MINIW::Normalize(g_pPlayerCtrl->m_CameraModel->m_HandModel->getRotation() * Rainbow::Vector3f::xAxis);
	//Rainbow::Vector3f up  = MINIW::Normalize(g_pPlayerCtrl->m_CameraModel->m_HandModel->getRotation()* Rainbow::Vector3f::yAxis);
	//Rainbow::Vector3f forward  = MINIW::Normalize(g_pPlayerCtrl->m_CameraModel->m_HandModel->getRotation()* Rainbow::Vector3f::zAxis);
	
	projectile->setShootingActor(shootactor); // 开发者需要在spawnActor之前知道shooter
	actorMgr->spawnActor(projectile, pos, yaw, pitch);
	projectile->m_StartPos = projectile->getPosition();

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(shootactor);

	ActorLiving *living = dynamic_cast<ActorLiving *>(shootactor);

	if (living)
	{
		// 新伤害计算系统 code-by:liya
		if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
		{
			getProjectileAtkDataNewByMob(shootactor, projectile, projectileDef, strength, itemId);
		}
		else
		{
			getProjectileAtkDataByMob(shootactor, projectile, projectileDef, strength, itemId);
		}

		LivingAttrib* attrib = living->getLivingAttrib();
		projectile->m_KnockbackStrength += attrib->getKnockback(ATTACK_RANGE, ATTACK_TARGET_ALL);

		if (attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_EXPLODE, ATTACK_RANGE, ATTACK_TARGET_ALL) > 0)
		{
			projectile->m_AttachedEffect |= 2;
		}
	}
	int itemid = player != NULL ? player->getCurToolID() : (mob != NULL ? mob->getEquipItem(EQUIP_WEAPON) : 0);
	if (itemid)
	{
		//int itemid = player->getCurToolID();
		int usetarget = 0;
		if (itemid > 0 && GetDefManagerProxy()->getItemDef(itemid))// 如果itemid =0会奔溃
		{
			usetarget = GetDefManagerProxy()->getItemDef(itemid)->UseTarget;
		}
		switch (usetarget)
		{
		case ITEM_USE_GUN:
		{
			const GunDef *gundef = GetDefManagerProxy()->getGunDef(itemid);
			if (gundef)
			{
				speedadd += gundef->SpeedAdd;
			}
			break;
		}
		case ITEM_USE_BOW:
		{
			const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(itemid);
			if (tooldef)
			{
				speedadd += tooldef->SpeedAdd;
			}
			break;
		}
		}		
	}

	PitchYaw2Direction(dir, yaw, pitch);
	static_cast<ProjectileLocoMotion *>(projectile->getLocoMotion())->setThrowableHeading(dir, strength*projectileDef->InitSpeed*(1+speedadd), 1.0f);

	//Trigger when throw!
	if (projectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}
	// TriggerCondition==5 不触发
	else if (projectileDef->TriggerCondition == 5)
	{
		projectile->m_ImpactTimeMark = -1;
	}

	if (projectileDef->ID == ITEM_ARROW)
	{
		if (strength > 0.9f)
		{
			projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);
		}
	}
	else
	{
		if (mob && mob->getAifire())
		{
			projectile->playMotion("1028", true, 1);
		}
		else
			projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);
	}

	return projectile;
}
ClientActorProjectile* ProjectileFactory::throwItemByTarger(World* pworld, ClientActor* shootactor, ClientActor* targeractor, float strength, int itemId, int buffId, int randomYaw, int randomPitch, int dropType)
{
	if (!shootactor)
		return nullptr;

	ActorLocoMotion* shootloc = shootactor->getLocoMotion();
	if (!shootactor)
		return nullptr;
	float pitch = 0;
	float yaw = 0;
	bool needsetVal = true;
	if (targeractor)
	{
		ActorLocoMotion* targerloc = targeractor->getLocoMotion();
		if (targerloc)
		{
			CollideAABB shootboundbox;
			shootboundbox.dim = WCoord(shootloc->m_BoundSize, shootloc->m_BoundHeight, shootloc->m_BoundSize);
			shootboundbox.pos = shootloc->getPosition() - WCoord(shootboundbox.dim.x / 2, shootloc->m_yOffset, shootboundbox.dim.z / 2);
			WCoord	pos = WCoord(shootboundbox.centerX(), shootboundbox.centerY(), shootboundbox.centerZ());

			WCoord shootcenter = pos;


			CollideAABB targerboundbox;
			targerboundbox.dim = WCoord(targerloc->m_BoundSize, targerloc->m_BoundHeight, targerloc->m_BoundSize);
			targerboundbox.pos = targerloc->getPosition() - WCoord(targerboundbox.dim.x / 2, targerloc->m_yOffset, targerboundbox.dim.z / 2);
			WCoord targercenter;
			targercenter = WCoord(targerboundbox.centerX(), targerboundbox.centerY() + GenRandomInt(0, targerboundbox.dim.y / 5), targerboundbox.centerZ());
			//向量
			Vector3f vpos = (shootcenter - targercenter).toVector3();

			float  r = sqrtf(vpos.x * vpos.x + vpos.y * vpos.y + vpos.z * vpos.z);
			vpos.x = vpos.x / r;
			vpos.y = vpos.y / r;
			vpos.z = vpos.z / r;
			pitch = std::asin(vpos.y);
			pitch = pitch * kRad2Deg;
			yaw = std::atan2(vpos.x, vpos.z) * kRad2Deg;
			yaw = yaw + GenRandomInt(-randomYaw, randomYaw);
			pitch = pitch + GenRandomInt(-randomPitch, randomPitch);
			needsetVal = false;
		}
		
	}
	if (needsetVal)
	{
		yaw = shootloc->m_RotateYaw + GenRandomInt(-randomYaw, randomYaw);
		pitch = shootloc->m_RotationPitch + GenRandomInt(-randomPitch, randomPitch);
	}
	float speedadd = 0.0f;
	const ProjectileDef* projectileDef = GetDefManagerProxy()->getProjectileDef(itemId, true);
	if (!projectileDef) return NULL;

	ClientActorProjectile* projectile = CreateProjectileFromItem(projectileDef->ID);
	projectile->setBuff(buffId);
	if (dropType > 0)
	{
		projectile->setDropable(false);
	}

	Rainbow::Vector3f dir;
	WCoord pos;

	PitchYaw2Direction(dir, yaw, pitch);

	ClientMob* mob = dynamic_cast<ClientMob*>(shootactor);
	if (mob && mob->getDefID() == 3236)
		pos = mob->getLocoMotion() ? mob->getLocoMotion()->getPosition() + WCoord(0, mob->getLocoMotion()->m_BoundHeight * 50 / 100, 0) : WCoord(0, -1, 0);
	else
		pos = WCoord(shootactor->getEyePosition());

	Quaternionf bodyRotation = AngleEulerToQuaternionf(Vector3f(pitch, yaw, 0));

	//Rainbow::Vector3f right  = MINIW::Normalize(g_pPlayerCtrl->m_CameraModel->m_HandModel->getRotation() * Rainbow::Vector3f::xAxis);
	//Rainbow::Vector3f up  = MINIW::Normalize(g_pPlayerCtrl->m_CameraModel->m_HandModel->getRotation()* Rainbow::Vector3f::yAxis);
	//Rainbow::Vector3f forward  = MINIW::Normalize(g_pPlayerCtrl->m_CameraModel->m_HandModel->getRotation()* Rainbow::Vector3f::zAxis);

	projectile->setShootingActor(shootactor); // 开发者需要在spawnActor之前知道shooter
	pworld->getActorMgr()->ToCastMgr()->spawnActor(projectile, pos, yaw, pitch);
	projectile->m_StartPos = projectile->getPosition();

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(shootactor);

	ActorLiving* living = dynamic_cast<ActorLiving*>(shootactor);

	if (living)
	{
		// 新伤害计算系统 code-by:liya
		if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
		{
			getProjectileAtkDataNewByMob(shootactor, projectile, projectileDef, strength, itemId);
		}
		else
		{
			getProjectileAtkDataByMob(shootactor, projectile, projectileDef, strength, itemId);
		}

		LivingAttrib* attrib = living->getLivingAttrib();
		projectile->m_KnockbackStrength += attrib->getKnockback(ATTACK_RANGE, ATTACK_TARGET_ALL);

		if (attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_EXPLODE, ATTACK_RANGE, ATTACK_TARGET_ALL) > 0)
		{
			projectile->m_AttachedEffect |= 2;
		}
	}
	int itemid = player != NULL ? player->getCurToolID() : (mob != NULL ? mob->getEquipItem(EQUIP_WEAPON) : 0);
	if (itemid)
	{
		//int itemid = player->getCurToolID();
		int usetarget = 0;
		if (itemid > 0 && GetDefManagerProxy()->getItemDef(itemid))// 如果itemid =0会奔溃
		{
			usetarget = GetDefManagerProxy()->getItemDef(itemid)->UseTarget;
		}
		switch (usetarget)
		{
		case ITEM_USE_GUN:
		{
			const GunDef* gundef = GetDefManagerProxy()->getGunDef(itemid);
			if (gundef)
			{
				speedadd += gundef->SpeedAdd;
			}
			break;
		}
		case ITEM_USE_BOW:
		{
			const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
			if (tooldef)
			{
				speedadd += tooldef->SpeedAdd;
			}
			break;
		}
		}
	}

	PitchYaw2Direction(dir, yaw, pitch);
	static_cast<ProjectileLocoMotion*>(projectile->getLocoMotion())->setThrowableHeading(dir, strength * projectileDef->InitSpeed * (1 + speedadd), 1.0f);

	//Trigger when throw!
	if (projectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}
	// TriggerCondition==5 不触发
	else if (projectileDef->TriggerCondition == 5)
	{
		projectile->m_ImpactTimeMark = -1;
	}

	if (projectileDef->ID == ITEM_ARROW)
	{
		if (strength > 0.9f)
		{
			projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);
		}
	}
	else
	{
		if (mob && mob->getAifire())
		{
			projectile->playMotion("1028", true, 1);
		}
		else
			projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);
	}

	return projectile;

}



ClientActorProjectile *ProjectileFactory::throwItemByActor(World *pworld, ClientActor *shootactor, float strength, int itemId, bool setfire, bool canpickup, int fireLv, WCoord offset)
{
	LOG_INFO("ProjectileFactory::throwItemByActor[itemId = %d]", itemId);
	// 生成投掷物的时候，播放投掷物技能的对应特效及音效
	ActorLocoMotion *shootloc = shootactor->getLocoMotion();
	float yaw;
	float pitch;
	float speedadd = 0.0f;

	const ProjectileDef *projectileDef = GetDefManagerProxy()->getProjectileDef(itemId, true);
	if (!projectileDef) return NULL;

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(shootactor);
	PlayerControl* player_control = dynamic_cast<PlayerControl*>(shootactor);
	
	int shakeTick = 0;
	if (projectileDef->ID == ITEM_ARROW)
		shakeTick = 7;
	ClientActorProjectile *projectile = CreateProjectileFromItem(projectileDef->ID, shootactor);
	
	if (player && projectile && (projectileDef->ID == ITEM_GLOW_STICK_USED) || (projectileDef->ID == ITEM_GLOW_STICK))
	{
		int duration = player->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON)->getDuration();
		//耐久传递，背包格子->投掷物
		ClientActorGlowSticks* glowstickProjectile = dynamic_cast<ClientActorGlowSticks*>(projectile);
		glowstickProjectile->setDuration(duration);
	}
	WCoord pos = WCoord(shootactor->getEyePosition());
	pos += offset;
	// 彩蛋枪子弹
	if(itemId == 12249)
	{
		pos = WCoord(shootactor->getChestPosition());
	}

	int usetarget = 0;
	ItemDef* itemdef = NULL;
	if (player)
	{
		itemdef = GetDefManagerProxy()->getItemDef(player->getCurToolID());
		if (itemdef)
		{
			usetarget = itemdef->UseTarget;
		}
	}

	auto RidComp = shootactor->getRiddenComponent();
	ClientActor *ride = NULL;
	if (RidComp)
	{
		ride = RidComp->getRidingActor();
	}
	if (ride)
	{
		ActorHorse *horse = dynamic_cast<ActorHorse *>(ride);
		if (horse)
		{
			if (usetarget == ITEM_USE_GUN)
				horse->changeGunBulletSpawnPos(pos);

			// 20210917: 远程攻击打断隐身 codeby： keguanqiang
			if (horse->isInvisible())
				horse->breakInvisible();
		}
	}

	yaw = shootloc->m_RotateYaw;
	pitch = shootloc->m_RotationPitch;

	// 玩家射击/投射
	if (player)
	{
		// 客机
		if ((player_control != g_pPlayerCtrl) || (player_control == NULL))
		{
			// 如果是客机，从GunLogic获取客机同步过来的精确位置和朝向
			Rainbow::Vector3f tmp;
			if (player->getGunLogical()) player->getGunLogical()->getDir(yaw, pitch, tmp);
			pos = tmp;
		}
		// 主机
		else if(g_pPlayerCtrl)
		{
			// 枪械外的射击武器
			if (usetarget != ITEM_USE_GUN)
			{
				pos = g_pPlayerCtrl->getCamera()->getEyePos();
				Rainbow::Vector3f camlookdir = g_pPlayerCtrl->getCamera()->getLookDir();
				auto PlayerRidComp = g_pPlayerCtrl->getRiddenComponent();
				ClientActor *riding = NULL;
				if (PlayerRidComp)
				{
					riding = PlayerRidComp->getRidingActor();
				}
				Rainbow::Quaternionf quat;
				if (riding)
				{
					auto ridingComp = riding->getRiddenComponent();
					if (ridingComp && ridingComp->getRiddenBindRot(g_pPlayerCtrl, quat))
					{
						//quat.rotate(camlookdir, camlookdir);
						camlookdir = RotateVectorByQuat(quat, camlookdir);
					}
				}

				CameraControlMode viewmode = g_pPlayerCtrl->getCamera()->getMode();
				if (viewmode == CAMERA_TPS_BACK || viewmode == CAMERA_TPS_BACK_2 || viewmode == CAMERA_TPS_BACK_SHOULDER)
				{
					Vector2f deltaXZ(
						(float)pos.x - g_pPlayerCtrl->getPosition().x, 
						(float)pos.z - g_pPlayerCtrl->getPosition().z);
					if (fabs(camlookdir.y) > fabs(camlookdir.x) && fabs(camlookdir.y) > fabs(camlookdir.z))
					{
						Rainbow::Vector3f sDir = g_pPlayerCtrl->getLookDir();
						WCoord ssDir = WCoord(sDir.x * BLOCK_SIZE, BLOCK_SIZE, sDir.z * BLOCK_SIZE);
						pos = shootactor->getPosition() + ssDir;
					}
					else
					{
						pos += camlookdir * deltaXZ.Length() * 1.1f;
					}
				}
				else if (viewmode == CAMERA_FPS)
				{
					pos += BLOCK_SIZE * camlookdir;
				}
				else if (viewmode == CAMERA_TPS_OVERLOOK)
				{
					pos = shootactor->getPosition() + WCoord(0, BLOCK_SIZE / 2, 0);
				}
				else if (viewmode == CAMERA_CUSTOM_VIEW)
				{
					pos = WCoord(shootactor->getEyePosition());
				}
			}

			// 自定义视角，以屏幕中心的位置发射（因为主机是不知客机的当前视角设置的，所以暂时以主机玩家的视角作为参考判断）
			// 背视角2，都是以屏幕中心发射。若要特殊处理在此加特例判断
			if ((g_pPlayerCtrl->getCamera()->getMode() == CAMERA_CUSTOM_VIEW
				&& g_pPlayerCtrl->getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_SCREEN_CENTER) ||
				g_pPlayerCtrl->getCamera()->getMode() == CAMERA_TPS_BACK_2 ||
				g_pPlayerCtrl->getCamera()->getMode() == CAMERA_TPS_BACK_SHOULDER)
			{
				// 主机直接使用摄像机位置发射
				//pos = player_control->m_pCamera->getPosition();
				pitch = player_control->m_pCamera->m_RotatePitch;
				yaw = player_control->m_pCamera->m_RotateYaw + 180.0f;
			}
		}
		
		if (pworld->IsUGCEditMode() && !g_pPlayerCtrl->isSightMode())
		{
			//新编辑场景的鼠标模式, 朝鼠标位置射击
			MINIW::WorldRay ray;
			ray.m_Origin = Rainbow::WorldPos(0, 0, 0);
			ray.m_Range = 0;

			g_pPlayerCtrl->m_pCamera->getViewRayByScreenPt(&ray, g_pPlayerCtrl->m_CurMouseX, g_pPlayerCtrl->m_CurMouseY);
			ray.m_Range = 1;

			auto tmpOrigin = g_pPlayerCtrl->getEyePosition();
			ray.m_Origin = tmpOrigin.toWorldPos();
			Direction2PitchYaw(&yaw, &pitch, ray.m_Dir);
		}

		// 枪械射击的随机偏移
		if (usetarget == ITEM_USE_GUN && !(player->getGunLogical()->isFirstShoot()))
		{
			float ratio = 0.15f;
			Vector2f randomVec2 = RandomUnitVector2 ()*ratio* player->getGunLogical()->getGunSpread() * GenRandomFloat() ;
			yaw += randomVec2.x;
			pitch += randomVec2.y;
		}

		player->attackOnTrigger();
	}

	//LOG_INFO("dodo2222 [yaw = %f, pitch = %f]", shootloc->m_RotateYaw, shootloc->m_RotationPitch);

	Quaternionf bodyRotation = AngleEulerToQuaternionf(Vector3f(pitch, yaw, 0));

	//Rainbow::Vector3f right  = MINIW::Normalize(g_pPlayerCtrl->m_CameraModel->m_HandModel->getRotation() * Rainbow::Vector3f::xAxis);
	//Rainbow::Vector3f up  = MINIW::Normalize(g_pPlayerCtrl->m_CameraModel->m_HandModel->getRotation()* Rainbow::Vector3f::yAxis);
	//Rainbow::Vector3f forward  = MINIW::Normalize(g_pPlayerCtrl->m_CameraModel->m_HandModel->getRotation()* Rainbow::Vector3f::zAxis);
	
	projectile->setShootingActor(shootactor); // 开发者需要在spawnActor之前知道shooter
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return NULL;
	actorMgr->spawnActor(projectile, pos, yaw, pitch);
	projectile->m_StartPos = projectile->getPosition();  
	Rainbow::Vector3f dir;
	PitchYaw2Direction(dir, yaw, pitch);

	ActorLiving *living = dynamic_cast<ActorLiving *>(shootactor);
	if (living)
	{
		// 新伤害计算系统 code-by:liya
		if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
		{
			getProjectileAtkDataNewByActor(shootactor, projectile, projectileDef, strength);
		}
		else
		{
			getProjectileAtkDataByActor(shootactor, projectile, projectileDef, strength);
		}
		LivingAttrib* attrib = living->getLivingAttrib();
		projectile->m_KnockbackStrength += attrib->getKnockback(ATTACK_RANGE, ATTACK_TARGET_ALL);

		// 暂时去掉玩家枪械的击退效果
		if (player && usetarget == ITEM_USE_GUN)
		{
			projectile->m_KnockbackStrength = 0.0f;
		}

		if (attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_EXPLODE, ATTACK_RANGE, ATTACK_TARGET_ALL) > 0)
		{
			projectile->m_AttachedEffect |= 2;
		}

		auto livingRidComp = living->getRiddenComponent();
		ClientActor *riding = NULL;
		if (livingRidComp)
		{
			riding = livingRidComp->getRidingActor();
		}
		Rainbow::Quaternionf quat;
		if (riding)
		{
			auto ridingComp = riding->getRiddenComponent();
			if (ridingComp && ridingComp->getRiddenBindRot(living, quat))
			{
				//quat.rotate(dir, dir);
				dir = RotateVectorByQuat(quat, dir);
			}
		}
	}

	if (player && itemdef)
	{
		int itemid = player->getCurToolID();
		int usetarget = itemdef->UseTarget;
		switch (usetarget)
		{
		case ITEM_USE_GUN:
		{
			const GunDef *gundef = GetDefManagerProxy()->getGunDef(itemid);
			if (gundef)
			{
				speedadd += gundef->SpeedAdd;
			}
			break;
		}
		case ITEM_USE_BOW:
		{
			const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(itemid);
			if (tooldef)
			{
				speedadd += tooldef->SpeedAdd;
			}
			break;
		}
		}
	}

	float deviation = 0.0f;
	if (player){
		deviation = player->getCurToolID() == 15004 ? 0.0f : 1.0f; 
		
		#ifdef IWORLD_SERVER_BUILD
		Rainbow::GetICloudProxyPtr()->OnPlayerShoot(player, pos, yaw, pitch, projectile->getObjId());
		#endif
	}
	static_cast<ProjectileLocoMotion *>(projectile->getLocoMotion())->setThrowableHeading(dir, strength*projectileDef->InitSpeed*(1+speedadd), deviation);
	//projectile->GetModel()->playMotion("1026");

	//LOG_INFO("dodo3333 [yaw = %f, pitch = %f, (%d, %d, %d)]", projectile->getLocoMotion()->m_RotateYaw, projectile->getLocoMotion()->m_RotationPitch, projectile->getLocoMotion()->getPosition().x, projectile->getLocoMotion()->getPosition().y, projectile->getLocoMotion()->getPosition().z);
	

	if (setfire)
	{
		projectile->m_AttachedEffect |= 1;
		projectile->m_FireLv = fireLv;
		projectile->playMotion("1028", true, 1);
	}

	if(!canpickup)
	{
		projectile->m_AttachedEffect |= 4;
	}

	//Trigger when throw!
	if (projectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}
	// TriggerCondition==5 不触发
	else if (projectileDef->TriggerCondition == 5)
	{
		projectile->m_ImpactTimeMark = -1;
	}
	
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit(
		"WeaponSkin_System_UseCustomTailEffect",
		SandboxContext(nullptr)
		.SetData_Number("uin", player ? player->getUin() : 0)
		.SetData_Number("itemid", itemId)
		.SetData_Number("strength", strength)
		.SetData_Userdata("ClientActorProjectile", "projectile", projectile));

	if (!result.IsExecSuccessed())
	{
		if (itemId == ITEM_ARROW)
		{
			if (strength > 0.9f)
			{
				projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);
			}
		}
		else
		{
			projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);
		}
	}

	return projectile;

}
ClientActorProjectile* ProjectileFactory::throwItemByGun(World* pworld, ClientActor* shootactor, ClientActor* targeractor, float strength, int itemId, bool setfire, bool canpickup, int attackType, int fireLv, WCoord offset)
{
	// 生成投掷物的时候，播放投掷物技能的对应特效及音效
	const ProjectileDef* projectileDef = GetDefManagerProxy()->getProjectileDef(itemId, true);
	if (!projectileDef) return NULL;

	int shakeTick = 0;
	if (projectileDef->ID == ITEM_ARROW)
		shakeTick = 7;
	ClientActorProjectile* projectile = CreateProjectileFromItem(projectileDef->ID, shootactor);
	ActorLocoMotion* shootloc = shootactor->getLocoMotion();
	CollideAABB shootboundbox;
	shootboundbox.dim = WCoord(shootloc->m_BoundSize, shootloc->m_BoundHeight, shootloc->m_BoundSize);
	shootboundbox.pos = shootloc->getPosition() - WCoord(shootboundbox.dim.x / 2, shootloc->m_yOffset, shootboundbox.dim.z / 2);
	WCoord	pos = WCoord(shootboundbox.centerX(), shootboundbox.centerY(), shootboundbox.centerZ());
	int usetarget = 0;
	

	auto RidComp = shootactor->getRiddenComponent();
	ClientActor* ride = NULL;
	if (RidComp)
	{
		ride = RidComp->getRidingActor();
	}
	
	WCoord shootcenter = pos;
	ActorLocoMotion* targerloc = targeractor->getLocoMotion();

	CollideAABB targerboundbox;
	targerboundbox.dim = WCoord(targerloc->m_BoundSize, targerloc->m_BoundHeight, targerloc->m_BoundSize);
	targerboundbox.pos = targerloc->getPosition() - WCoord(targerboundbox.dim.x / 2, targerloc->m_yOffset, targerboundbox.dim.z / 2);
	WCoord targercenter;
	switch (attackType)
	{
	case NOATTACK:
	{
		int result = GenRandomInt(1, 2);
		int x, z;
		if (result == 1)
		{
			x = targerboundbox.minX() - GenRandomInt(10,30 );
			z = targerboundbox.minZ() - GenRandomInt(10, 30);
		}
		else
		{
			x = targerboundbox.maxX() + GenRandomInt(10, 30);
			z = targerboundbox.maxY() + GenRandomInt(10, 30);
		}
		targercenter = WCoord(x, targerboundbox.centerY() + GenRandomInt(0, targerboundbox.dim.y / 2), z);
	}
		break;
	case ATTACK:
	{
		targercenter = WCoord(targerboundbox.centerX(), targerboundbox.centerY()+ GenRandomInt(0, targerboundbox.dim.y/2), targerboundbox.centerZ());
	}
		break;
	case ATTACKHEAD:
	{
		int heigt = static_cast<int>(targerboundbox.minY() + GenRandomInt(targerboundbox.dim.y * (2.0f / 3.0f), targerboundbox.dim.y ));
		targercenter = WCoord(targerboundbox.centerX(), heigt, targerboundbox.centerZ());
	}
		break;
	default:
		break;
	}
	//向量
	Vector3f vpos = (shootcenter - targercenter).toVector3();
	float yaw;
	float  r = sqrtf(vpos.x * vpos.x + vpos.y * vpos.y + vpos.z * vpos.z);
	vpos.x = vpos.x / r;
	vpos.y = vpos.y / r;
	vpos.z = vpos.z / r;
	float tpitch = std::asin(vpos.y);
	tpitch = tpitch * kRad2Deg;
	yaw = std::atan2(vpos.x,vpos.z) * kRad2Deg;	
	float pitch;
	float speedadd = 0.0f;

	if (ride)
	{
		ActorHorse* horse = dynamic_cast<ActorHorse*>(ride);
		if (horse)
		{
			if (usetarget == ITEM_USE_GUN)
				horse->changeGunBulletSpawnPos(pos);

			// 20210917: 远程攻击打断隐身 codeby： keguanqiang
			if (horse->isInvisible())
				horse->breakInvisible();
		}
	}
	
	pitch = tpitch;
	Quaternionf bodyRotation = AngleEulerToQuaternionf(Vector3f(pitch, yaw, 0));
	projectile->setShootingActor(shootactor); // 开发者需要在spawnActor之前知道shooter
	pworld->getActorMgr()->ToCastMgr()->spawnActor(projectile, pos, yaw, pitch);
	projectile->m_StartPos = projectile->getPosition();
	Rainbow::Vector3f dir;
	PitchYaw2Direction(dir, yaw, pitch);

	ActorLiving* living = dynamic_cast<ActorLiving*>(shootactor);
	if (living)
	{
			if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
			{
				getProjectileAtkDataNewByMob(shootactor, projectile, projectileDef, strength, itemId);
			}
			else
			{
				getProjectileAtkDataByMob(shootactor, projectile, projectileDef, strength, itemId);
			}
		LivingAttrib* attrib = living->getLivingAttrib();
		projectile->m_KnockbackStrength += attrib->getKnockback(ATTACK_RANGE, ATTACK_TARGET_ALL);
		if (attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_EXPLODE, ATTACK_RANGE, ATTACK_TARGET_ALL) > 0)
		{
			projectile->m_AttachedEffect |= 2;
		}

		auto livingRidComp = living->getRiddenComponent();
		ClientActor* riding = NULL;
		if (livingRidComp)
		{
			riding = livingRidComp->getRidingActor();
		}
		Rainbow::Quaternionf quat;
		if (riding)
		{
			auto ridingComp = riding->getRiddenComponent();
			if (ridingComp && ridingComp->getRiddenBindRot(living, quat))
			{
				dir = RotateVectorByQuat(quat, dir);
			}
		}
	}
	
	static_cast<ProjectileLocoMotion*>(projectile->getLocoMotion())->setThrowableHeading(dir, strength * projectileDef->InitSpeed * (1 + speedadd), 1.0f);
	if (setfire)
	{
		projectile->m_AttachedEffect |= 1;
		projectile->m_FireLv = fireLv;
		projectile->playMotion("1028", true, 1);
	}

	if (!canpickup)
	{
		projectile->m_AttachedEffect |= 4;
	}

	//Trigger when throw!
	if (projectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}
	// TriggerCondition==5 不触发
	else if (projectileDef->TriggerCondition == 5)
	{
		projectile->m_ImpactTimeMark = -1;
	}

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit(
		"WeaponSkin_System_UseCustomTailEffect",
		SandboxContext(nullptr)
		.SetData_Number("uin", 0)
		.SetData_Number("itemid", itemId)
		.SetData_Number("strength", strength)
		.SetData_Userdata("ClientActorProjectile", "projectile", projectile));

	if (!result.IsExecSuccessed())
	{
		if (itemId == ITEM_ARROW)
		{
			if (strength > 0.9f)
			{
				projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);
			}
		}
		else
		{
			projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);
		}
	}

	return projectile;
}


PROJECTILE_TYPE ProjectileFactory:: getTypeFromItemId(int itemId)
{
	if(itemId == ITEM_EGG || itemId == ITEM_EGG2)
		return PROJECTILE_EGG;
	if(itemId == ITEM_ARROW || itemId == ITEM_OCEANARROW)
		return PROJECTILE_ARROW;
	if (itemId == ITEM_SNOWBALL)
		return PROJECTILE_SNOWBALL;
	if (itemId == ITEM_HOOK)
		return PROJECTILE_HOOK;
	if (itemId == ITEM_LASER || itemId == ITEM_LASER_BOSS_RED || itemId == ITEM_LASER_BOSS_BLUE || itemId == ITEM_LASER_BOSS_WHITE)
		return PROJECTILE_LASER;
	if (itemId == ITEM_ATTRACT)
		return PROJECTILE_ATTRACT;
	if (itemId == ITEM_IMPULSE)
		return PROJECTILE_IMPULSE;
	if (itemId == ITEM_AIR_BALL)
		return PROJECTILE_AIRBALL;
	if (itemId == ITEM_THORNBALL)
		return PROJECTILE_THORNBALL;
	if (itemId == ITEM_GLOW_STICK || itemId == ITEM_GLOW_STICK_USED)
		return PROJECTILE_GLOWSTICK;
	if (itemId == ITEM_PIRATE_CHEST)
		return PROJECTILE_PIRATECHEST;
	//if (itemId == 15529)
	//	return PROJECTILE_VEHICLE;

	return PROJECTILE_NORMAL;
}

ClientActorProjectile* ProjectileFactory::createProjectile(int itemid, ClientActor *shootactor)
{
	return CreateProjectileFromItem(itemid, shootactor);
}

void ProjectileFactory::getProjectileAtkDataByActor(ClientActor* shootactor, ClientActorProjectile* projectile, const ProjectileDef* projectileDef, float strength, float attackValue)
{
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(shootactor);
	ClientMob* mob = dynamic_cast<ClientMob*>(shootactor);

	//如果外部传入就用外部的
	if (attackValue == 0)
		attackValue = projectileDef->AttackValue;
	ActorLiving* living = dynamic_cast<ActorLiving*>(shootactor);
	if (living)
	{
		LivingAttrib* attrib = living->getLivingAttrib();
		if (attrib)
		{
			projectile->m_BuffAttackAdd = attrib->getModAttrib(MODATTR_ATTACK_RANGE);
			float bowenchant = attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_BOW_ATTACK, ATTACK_RANGE, ATTACK_TARGET_ALL);

			//对于弓箭类的投射物
			if (player && projectileDef->ID != player->getCurToolID())
			{
				//弓箭类射击加成
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_CalAtkAdd",
					SandboxContext(nullptr)
					.SetData_Number("uin", player->getUin())
					.SetData_Number("itemid", player->getCurToolID())
				);
				float fAtkAdd = 0.f;
				if (result.IsExecSuccessed())
				{
					fAtkAdd = (float)result.GetData_Number("fAtkAdd");
				}
				projectile->m_AttackPoints = (attrib->getAttackPoint(ATTACK_RANGE, 1) * (1.0f + bowenchant) + attackValue) * strength * (1.0f + projectile->m_BuffAttackAdd) + fAtkAdd;
			}
			//对于雪球类的投射物
			else
			{
				if (mob) projectile->m_AttackPoints = (attackValue + mob->m_Def->Attacks[0]) * strength * (1.0f + projectile->m_BuffAttackAdd);
				else
				{
					// 矛受到玩家自身属性加成 code_by:liya designer:huzhe
					auto toolDef = GetDefManagerProxy()->getToolDef(projectileDef->ID);
					if (toolDef && toolDef->SubType == 4)
					{
						projectile->m_AttackPoints = (attackValue + attrib->getAttackPoint(ATTACK_RANGE, 1)) * strength * (1.0f + projectile->m_BuffAttackAdd);
					}
					else
						projectile->m_AttackPoints = (attackValue) * strength * (1.0f + projectile->m_BuffAttackAdd);
				}
			}
		}
	}

	//projectile->m_AttackPoints += attrib->getEnchantAttackPoint(ATTACK_RANGE, ATTACK_TARGET_ALL);
}

void ProjectileFactory::getProjectileAtkDataNewByActor(ClientActor* shootactor, ClientActorProjectile* projectile, const ProjectileDef* projectileDef, float strength, float attackValue)
{
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(shootactor);
	ClientMob* mob = dynamic_cast<ClientMob*>(shootactor);

	projectile->m_strength = strength;
	//如果外部传入就用外部的
	if (attackValue == 0)
		attackValue = projectileDef->AttackValue;
	ActorLiving * living = dynamic_cast<ActorLiving*>(shootactor);
	if (living)
	{
		LivingAttrib* attrib = living->getLivingAttrib();
		//aktRangePoints = attrib->getAttackPoint(ATTACK_RANGE);

		if(attrib)
			projectile->m_bowDamageIns = attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_BOW_ATTACK, ATTACK_RANGE, ATTACK_TARGET_ALL);

		projectile->UpdateAttackPoints();

		//对于弓箭类的投射物
		if (player && projectileDef->ID != player->getCurToolID())
		{
			//弓箭类射击加成（暂未处理）
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_CalAtkAdd",
				SandboxContext(nullptr)
				.SetData_Number("uin", player->getUin())
				.SetData_Number("itemid", player->getCurToolID())
			);
			float fAtkAdd = 0.f;
			if (result.IsExecSuccessed())
			{
				fAtkAdd = (float)result.GetData_Number("fAtkAdd");
			}
			projectile->m_bowDamageIns += fAtkAdd;

			projectile->UpdateAttackPointsByAttri(attrib);
		}
		else
		{
			// 矛受到玩家自身属性加成 code_by:liya designer:huzhe
			auto toolDef = GetDefManagerProxy()->getToolDef(projectileDef->ID);
			if (toolDef && toolDef->SubType == 4)
			{
				projectile->UpdateAttackPointsByAttri(attrib);
			}
		}
	}
}

void ProjectileFactory::getProjectileAtkDataByMob(ClientActor* shootactor, ClientActorProjectile* projectile, const ProjectileDef* projectileDef, float strength, int itemId)
{
	// todo，清理掉玩家相关逻辑
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(shootactor);
	ClientMob* mob = dynamic_cast<ClientMob*>(shootactor);

	ActorLiving* living = dynamic_cast<ActorLiving*>(shootactor);

	if (living)
	{
		LivingAttrib* attrib = living->getLivingAttrib();
		if (attrib)
		{
			projectile->m_BuffAttackAdd = attrib->getModAttrib(MODATTR_ATTACK_RANGE);
			float bowenchant = attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_BOW_ATTACK, ATTACK_RANGE, ATTACK_TARGET_ALL);

			projectile->m_AttackPoints = (projectileDef->AttackValue) * strength * (1.0f + projectile->m_BuffAttackAdd);

			if (player == NULL || (player && projectileDef->ID != player->getCurToolID()))
			{
				projectile->m_AttackPoints += (attrib->getAttackPoint(ATTACK_RANGE, 1) * (1.0f + bowenchant)) * strength * (1.0f + projectile->m_BuffAttackAdd);
			}
			else
			{
				if (mob)
				{
					projectile->m_AttackPoints += (mob->m_Def->Attacks[1]) * strength * (1.0f + projectile->m_BuffAttackAdd);
				}
			}
		}
	}
	//projectile->m_AttackPoints = attrib->getAttackPoint(ATTACK_RANGE) * (1.0f + strength*7.0f) * (1.0f + bowenchant);
}

void ProjectileFactory::getProjectileAtkDataNewByMob(ClientActor* shootactor, ClientActorProjectile* projectile, const ProjectileDef* projectileDef, float strength, int itemId)
{
	// todo，清理掉玩家相关逻辑
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(shootactor);
	ClientMob* mob = dynamic_cast<ClientMob*>(shootactor);

	projectile->m_strength = strength;

	ActorLiving* living = dynamic_cast<ActorLiving*>(shootactor);
	if (living)
	{
		LivingAttrib* attrib = living->getLivingAttrib();

		projectile->UpdateAttackPoints();

		if (player == NULL || (player && projectileDef->ID != player->getCurToolID()))
		{
			if (attrib)
			{
				projectile->UpdateAttackPointsByAttri(attrib);

				projectile->m_bowDamageIns = attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_BOW_ATTACK, ATTACK_RANGE, ATTACK_TARGET_ALL);
			
				if (player == nullptr)
				{
					// 怪物手持新枪
					BackPackGrid* pweapon = shootactor->getEquipGrid(EQUIP_WEAPON);
					if (pweapon)
					{
						GunGridDataComponent* guncomp = dynamic_cast<GunGridDataComponent*>(pweapon->getGunDataComponent());
						if (guncomp)
						{
							CustomGunDef* gundef = guncomp->GetGunDef();
							if (gundef && gundef->ID > 0)
							{
								//获取新枪属性
								float baseDamageBonus = gundef->baseDamageBonus;
								baseDamageBonus += attrib->getActorAttValueWithStatus(BUFFATTRT_FIREARM_DAMAGE_PERCENT, 1);
								baseDamageBonus = std::fmaxf(0, 1 + baseDamageBonus);
								float baseDamage = gundef->baseDamage;
								baseDamage += attrib->getActorAttValueWithStatus(BUFFATTRT_FIREARM_DAMAGE, baseDamage);
								float atkPoints = baseDamage * baseDamageBonus;

								int index = gundef->damageType;
								if (projectileDef->AttackType == 0)
								{
									projectile->m_atkType |= (1 << index);
									projectile->m_AttackPointsNew[index] += atkPoints;
								}
								else
								{
									if (index < MAX_PHYSICS_ATTACK)
									{
										projectile->m_ExplodePoints[0] += atkPoints;
									}
									else
									{
										projectile->m_ExplodePoints[index - 3] += atkPoints;
									}
								}
							}
						}
					}
				}
			}
		}
		else
		{
			if (mob)
			{
				projectile->UpdateAttackPointsByAttri(attrib);
			}
		}
	}
}


ClientActorProjectile* ProjectileFactory::throwItemByActorByPlayerRay(World* pworld, ClientActor* shootactor, MINIW::WorldRay ray, float strength, int itemId, float attackValue)
{
	const ProjectileDef* projectileDef = GetDefManagerProxy()->getProjectileDef(itemId, true);
	if (!projectileDef) return NULL;

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(shootactor);
	ClientActorProjectile* projectile = CreateProjectileFromItem(projectileDef->ID, shootactor);

	Vector3f origin = ray.m_Origin.toVector3();
	WCoord pos = origin;


	auto RidComp = shootactor->getRiddenComponent();
	if (RidComp)
	{
		ClientActor* ride = RidComp->getRidingActor();
		if (ride)
		{
			ActorHorse* horse = dynamic_cast<ActorHorse*>(ride);
			if (horse)
			{
				horse->changeGunBulletSpawnPos(pos);

				// 20210917: 远程攻击打断隐身 codeby： keguanqiang
				if (horse->isInvisible())
					horse->breakInvisible();
			}
		}
	}
	
	float yaw, pitch = 0.f;
	Direction2PitchYaw(&yaw, &pitch, ray.m_Dir);
	projectile->setShootingActor(shootactor); // 开发者需要在spawnActor之前知道shooter
	pworld->getActorMgr()->ToCastMgr()->spawnActor(projectile, pos, yaw, pitch);
	projectile->m_StartPos = projectile->getPosition();
	Rainbow::Vector3f dir;
	PitchYaw2Direction(dir, yaw, pitch);

	ActorLiving* living = dynamic_cast<ActorLiving*>(shootactor);
	if (living)
	{
		// 新伤害计算系统 code-by:liya
		if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
		{
			getProjectileAtkDataNewByActor(shootactor, projectile, projectileDef, strength, attackValue);
		}
		else
		{
			getProjectileAtkDataByActor(shootactor, projectile, projectileDef, strength, attackValue);
		}
		LivingAttrib* attrib = living->getLivingAttrib();
		projectile->m_KnockbackStrength += attrib->getKnockback(ATTACK_RANGE, ATTACK_TARGET_ALL);

		// 暂时去掉玩家枪械的击退效果
		projectile->m_KnockbackStrength = 0.0f;

		if (attrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_EXPLODE, ATTACK_RANGE, ATTACK_TARGET_ALL) > 0)
		{
			projectile->m_AttachedEffect |= 2;
		}

		auto livingRidComp = living->getRiddenComponent();
		ClientActor* riding = NULL;
		if (livingRidComp)
		{
			riding = livingRidComp->getRidingActor();
		}
		Rainbow::Quaternionf quat;
		if (riding)
		{
			auto ridingComp = riding->getRiddenComponent();
			if (ridingComp && ridingComp->getRiddenBindRot(living, quat))
			{
				//quat.rotate(dir, dir);
				dir = RotateVectorByQuat(quat, dir);
			}
		}
	}

	if (player) 
	{
		player->attackOnTrigger();
#ifdef IWORLD_SERVER_BUILD
		Rainbow::GetICloudProxyPtr()->OnPlayerShoot(player, pos, yaw, pitch, projectile->getObjId());
#endif
	}

	float speedadd = 0.f;
	static_cast<ProjectileLocoMotion*>(projectile->getLocoMotion())->setThrowableHeading(dir, strength * projectileDef->InitSpeed * (1 + speedadd), 1.0f);

	//Trigger when throw!
	if (projectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}
	// TriggerCondition==5 不触发
	else if (projectileDef->TriggerCondition == 5)
	{
		projectile->m_ImpactTimeMark = -1;
	}

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit(
		"WeaponSkin_System_UseCustomTailEffect",
		SandboxContext(nullptr)
		.SetData_Number("uin", player ? player->getUin() : 0)
		.SetData_Number("itemid", itemId)
		.SetData_Number("strength", strength)
		.SetData_Userdata("ClientActorProjectile", "projectile", projectile));

	if (!result.IsExecSuccessed())
	{
		if (itemId == ITEM_ARROW)
		{
			if (strength > 0.9f)
			{
				projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);
			}
		}
		else
		{
			projectile->playMotion(projectileDef->TailEffect.c_str(), true, 0, 2);
		}
	}

	return projectile;

}
