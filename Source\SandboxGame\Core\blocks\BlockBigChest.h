﻿
#ifndef __BLOCK_BIGCHEST_H__
#define __BLOCK_BIGCHEST_H__

#include "BlockChest.h"

enum //大储物箱放置方位
{
	PUT_PLACE_NONE = 0,
	PUT_PLACE_UP = 1,
	PUT_PLACE_DOWN,
	PUT_PLACE_RIGHT,
	PUT_PLACE_LEFT,
};

// 横竖大储物箱
class BigChestMaterial : public ChestMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BigChestMaterial)
public:
	//tolua_begin
	BigChestMaterial();
	virtual ~BigChestMaterial();

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos) override;
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint = Rainbow::Vector3f(0, 0, 0));
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid) override;

	bool isMajor(World* pworld, const WCoord& blockpos);	//大型储物箱有两个格子，两个container，取其中一个为主体
	//tolua_end
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
	WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos);	//获取核心方块位置，横箱先右后左，竖箱先上后下
	WCoord getExtendBlockPos(World* pworld, const WCoord& blockpos);	//获取扩展方块位置，横箱为左边，竖箱为下边

	virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage) override;
	virtual int getBlockHP(World* pworld, const WCoord& blockpos) override;
	virtual bool getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf = false) override;

	virtual WorldContainer* repairContainer(const WCoord& blockpos, const int& data) override;
private:
	virtual void initGeomName() override;
	int canPutOntoUpDown(WorldProxy *pworld, BaseSection *psection, const WCoord &blockpos);
	int canPutOntoUpDown(WorldProxy *pworld, BaseSection *psection, const WCoord &blockpos, WCoord& newbpos);
	int canPutOntoLeftRight(WorldProxy *pworld, BaseSection *psection, const WCoord &blockpos);
	int canPutOntoLeftRight(WorldProxy *pworld, BaseSection *psection, const WCoord &blockpos, WCoord& newbpos);
}; //tolua_exports

#endif