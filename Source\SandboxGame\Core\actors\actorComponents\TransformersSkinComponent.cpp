#include "TransformersSkinComponent.h"
#include "ActorBody.h"
#include "PlayerControl.h"
#include "MpActorManager.h"
#include "GameCamera.h"
#include "ActorSkinNpc.h"
#include "BlockMaterialMgr.h"
#include "GameNetManager.h"
#include "ClientInfoProxy.h"
#include "EffectManager.h"
#include "WorldManager.h"
#include "ActorLocoMotion.h"

using namespace Rainbow;

IMPLEMENT_COMPONENTCLASS(TransformersSkinComponent)

TransformersSkinComponent::TransformersSkinComponent()
:m_bInTransform(false)
,m_TransformReason(0)
,m_MainSkinPlayerID(0)
,m_iCheckDistanceTick(0)
{
}

TransformersSkinComponent::~TransformersSkinComponent()
{

}

void TransformersSkinComponent::onPlayerTransformSkinModel(int reason, long long mainplayerid, long long objid)
{
	if (reason == 1)
	{
		m_bInTransform = true;
	}
	if (reason == 2 || reason == 0)
	{
		m_bInTransform = false;
		if (objid == g_pPlayerCtrl->getObjId())
		{
			if (m_MainSkinPlayerID == 0)
			{
				InfoTips(30358);
			}
			else
			{
				InfoTips(30355);
			}
		}
	}
	if (reason == 3)
	{
		m_bInTransform = false;
		if (objid == g_pPlayerCtrl->getObjId())
			InfoTips(30354);
	}
	if (reason == 4)
	{
		m_bInTransform = false;
		if (objid == g_pPlayerCtrl->getObjId())
			InfoTips(30358);
	}
	m_MainSkinPlayerID = mainplayerid;	
}

void TransformersSkinComponent::restoreSkinByReason(int transformReason)
{
	if (InTransform())
	{
		m_TransformReason = transformReason;
		restoreSkin();
	}
}

void TransformersSkinComponent::eraseSkinSubPlayer(long long objid)
{
	auto iter = std::find(m_vecSkinSubPlayers.begin(), m_vecSkinSubPlayers.end(), objid);
	if (iter != m_vecSkinSubPlayers.end())
		m_vecSkinSubPlayers.erase(iter);	
}

void TransformersSkinComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void TransformersSkinComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void TransformersSkinComponent::OnTick()
{
	//检查与子装扮的距离
	if (m_bInTransform)
	{
		if (!GetOwner()) return;
		ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
		if (!m_owner) return;
		World* pworld = m_owner->getWorld();
		if (!pworld) return;
		ClientActorMgr* actorMgr = pworld->getActorMgr() ? pworld->getActorMgr()->ToCastMgr() : nullptr;
		if (!actorMgr) return;
		m_iCheckDistanceTick++;
		if (20 <= m_iCheckDistanceTick)
		{
			m_iCheckDistanceTick = 0;
			auto iter = m_vecSkinSubActors.begin();
			while (iter != m_vecSkinSubActors.end())
			{
				ClientActor *actor = actorMgr->findActorByWID(*iter);
				if (actor)
				{
					WCoord actorPos = actor->getPosition();
					float dis = actorPos.distanceTo(m_owner->getPosition());
					if (dis >= BLOCK_SIZE * 40)
					{
						actor->setNeedClear();
						iter = m_vecSkinSubActors.erase(iter);
					}
					else
					{
						++iter;
					}
				}
				else
				{
					iter = m_vecSkinSubActors.erase(iter);
				}

			}
		}
	}

	
}

void TransformersSkinComponent::onPlayerRevive()
{
	if (m_vecSkinSubActors.size() == 0 && m_vecSkinSubPlayers.size() == 0 && m_bInTransform)
	{
		restoreSkin();
	}
}
void TransformersSkinComponent::OnUpdate(float elapse)
{
	if (!GetOwner()) return;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	ActorBody* body = m_owner->getBody();
	if (m_TransformReason > 0 && body && !body->hasAnimPlaying(SEQ_RE_SHAPE_SHIFT))
	{
		restoreSkin();
		m_TransformReason = 0;
	}
}

void TransformersSkinComponent::restoreSkin()
{
	if (!GetOwner()) return;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	ActorBody* body = m_owner->getBody();
	World* pWorld   = m_owner->getWorld();
	auto objId      = m_owner->getObjId();

	if (!pWorld) return;
	ClientActorMgr* actorMgr = pWorld->getActorMgr() ? pWorld->getActorMgr()->ToCastMgr() : nullptr;
	if (!actorMgr) return;
	if (body && m_bInTransform && pWorld)
	{
		int playerindex = ComposePlayerIndex(
			body->getModelID(),
			body->getGeniusLv(), m_owner->m_originSkinId);
		m_owner->setCustomJson(m_owner->m_strOriginCustomJson);
		m_owner->ClientPlayer::changePlayerModel(playerindex, body->getMutateMob(), m_owner->m_strOriginCustomJson.c_str());
		body = m_owner->getBody();
		if (m_owner->hasUIControl())
		{
			if ((m_TransformReason == 0 || m_TransformReason == 4) && objId == g_pPlayerCtrl->getObjId())
			{
				if (m_MainSkinPlayerID != 0)
				{
					InfoTips(30354);
				}
				else
				{
					InfoTips(30358);
				}
			}
			if ((m_TransformReason == 3) && objId == g_pPlayerCtrl->getObjId())
			{
				InfoTips(30354);
			}
			if ((m_TransformReason == 2)
				&& objId == g_pPlayerCtrl->getObjId())
			{
				InfoTips(30355);
			}
		}

		m_owner->playAnim(SEQ_STAND, true);
		if (!pWorld->isRemoteMode())
		{
			PB_PlayerTransformSkinHC changeModelHC;
			changeModelHC.set_uin(m_owner->getUin());
			changeModelHC.set_playerindex(playerindex);
			changeModelHC.set_customskins(m_owner->m_strCustomjson);
			changeModelHC.set_reason(m_TransformReason);
			changeModelHC.set_mainplayerid(0);
			pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYERTRANSFORMSKIN_HC, changeModelHC, m_owner, true);
		}

		for (size_t i = 0; i < m_vecSkinSubPlayers.size(); i++)
		{
			if (m_vecSkinSubPlayers[i] != objId && m_vecSkinSubPlayers[i] != 0)
			{
				ClientPlayer *actor = dynamic_cast<ClientPlayer*> (GetWorldManagerPtr()->findActorByWID(m_vecSkinSubPlayers[i]));
				if (actor)
				{
					actor->restoreSkin();
				}
			}
		}
		m_vecSkinSubPlayers.clear();
		for (size_t i = 0; i < m_vecSkinSubActors.size(); i++)
		{
			ClientActor *actor = dynamic_cast<ClientActor*>(GetWorldManagerPtr()->findActorByWID(m_vecSkinSubActors[i]));
			if (actor)
			{
				pWorld->getEffectMgr()->playParticleEffectAsync("particles/acchorse.ent", actor->getPosition(), 40);
				actor->setNeedClear();
			}
		}
		m_vecSkinSubActors.clear();
		m_bInTransform = false;
		m_owner->changeViewMode(GetClientInfoProxy()->getGameData("view") - 1, false, true);
		m_TransformReason = 0;
		ClientPlayer* mainPlayer = actorMgr->findPlayerByUin(m_MainSkinPlayerID);
		if (mainPlayer /*&& mainPlayer->m_pTransformersSkinComp*/)
		{
			//mainPlayer->m_pTransformersSkinComp->eraseSkinSubPlayer(objId);
			//todo_check Logo
			mainPlayer->eraseSkinSubPlayer(objId);
		}
		m_MainSkinPlayerID = 0;
	}
}
// 还原变形
// reason = 1 副装扮主动还原
// reason = 2 子装扮主动还原
// reason = 3 子装扮被动还原
void TransformersSkinComponent::resetDeformation(int reason)
{
	m_TransformReason = reason;
	if (m_vecSkinSubActors.size() == 0 && m_vecSkinSubPlayers.size() == 0)
	{
		restoreSkin();
		return;
	}
	if (!GetOwner()) return ;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	m_owner->playAnim(SEQ_RE_SHAPE_SHIFT, true);
	for (size_t i = 0; i < m_vecSkinSubActors.size(); i++)
	{
		ClientActor* actor = dynamic_cast<ClientActor*>(GetWorldManagerPtr()->findActorByWID(m_vecSkinSubActors[i]));
		if (actor)
		{
			actor->setNeedClear();
			//actor->playAnim(SEQ_RE_SHAPE_SHIFT);
		}
	}
	for (size_t i = 0; i < m_vecSkinSubPlayers.size(); i++)
	{
		ClientPlayer *player = dynamic_cast<ClientPlayer*>(GetWorldManagerPtr()->findActorByWID(m_vecSkinSubPlayers[i]));
		if (player)
		{
			/*player->m_pTransformersSkinComp->m_TransformReason = 3;
			player->m_pTransformersSkinComp->restoreSkin();*/
			//todo_check Logo
			player->transformSkinReason(3);
			player->restoreSkin();
			//player->resetDeformation(3);
		}
	}
	m_owner->changeViewMode(CameraControlMode::CAMERA_TPS_BACK, false);
}

void TransformersSkinComponent::DeformationSkin(RoleSkinDef * skinDef)
{
	if (!GetOwner()) return;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	ActorBody* body = m_owner->getBody();
	World* pWorld = m_owner->getWorld();
	if (!pWorld) return;
	ClientActorMgr* actorMgr = pWorld->getActorMgr() ? pWorld->getActorMgr()->ToCastMgr() : nullptr;
	if (!actorMgr) return;
	int uin = m_owner->getUin();
	auto objId = m_owner->getObjId();

	if (!skinDef || !pWorld) {
		return;
	}
	if (!pWorld->isRemoteMode())
	{
		PB_PlayerTransformSkinHC changeModelHC;
		changeModelHC.set_uin(uin);
		changeModelHC.set_playerindex(ComposePlayerIndex(
			body->getModelID(),
			body->getGeniusLv(), skinDef->ChangeContact[0]));
		changeModelHC.set_customskins("");
		changeModelHC.set_reason(1);
		changeModelHC.set_mainplayerid(0);
		pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYERTRANSFORMSKIN_HC, changeModelHC, m_owner, true);
	}
	m_owner->changePlayerModel(ComposePlayerIndex(body->getModelID(), body->getGeniusLv(), skinDef->ChangeContact[0]));
	body = m_owner->getBody();
	m_bInTransform = true;
	m_owner->playAnim(SEQ_STAND, true);
	m_owner->changeViewMode(GetClientInfoProxy()->getGameData("view") - 1, false, true);


	//生成多个子装扮(其中有一个ID是副装扮，这里从1开始)
	//LOG_INFO("%d, %d, %d", m_Body->getPosition().x, m_Body->getPosition().y, m_Body->getPosition().z);
	m_vecSkinSubActors.clear();
	int result;
	findPlaceSplitDisguise(result, true);
	for (short i = 1; i < MAX_SKIN_EFFECT; ++i)
	{
		if (skinDef->ChangeContact[i] > 0 && i <= m_SplitPos.size())
		{
			ActorSkinNpc* an = SANDBOX_NEW(ActorSkinNpc);
			an->init(skinDef->ChangeContact[i]);
			//保证NPC站在格子中央，所以+0.5
			WCoord pos = WCoord((m_SplitPos[i - 1].x + 0.5f) * BLOCK_SIZE, m_SplitPos[i - 1].y * BLOCK_SIZE, (m_SplitPos[i - 1].z + 0.5f) * BLOCK_SIZE);
			Rainbow::WorldPos p = pos.toWorldPos();
			an->setPosition(pos.x, pos.y, pos.z);
			an->m_ChangeModel = skinDef->ChangeContact[i];
			an->m_MainPlayer = objId;
			pos = an->getPosition();
			pWorld->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
			actorMgr->spawnActor(an);
			long long objId = an->getObjId();
			m_vecSkinSubActors.push_back(objId);
			pWorld->getEffectMgr()->playParticleEffectAsync("particles/acchorse.ent", pos, 40);
			//播放生成动画
			//actor->playAnim();
		}
	}
}


/**
*准备装扮分裂
*@param id:装扮id
*/
bool TransformersSkinComponent::trySplitDisguise(short id)
{
	if (!GetOwner()) return false;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	if (m_owner->isDead()) //死亡直接返回
		return false;

	//判断是否可以分裂
	int splitResult = 0; //变形结果，0:可分裂，1:子装扮分裂位置过高或过低，2:子装扮分裂位置被占用，3:在太空或者水中，4:其他
	RoleSkinDef *def = GetDefManagerProxy()->getRoleSkinDef(id);
	if (!def)
	{
		return false;
	}

	//判断当前地形是否可以分裂
	WCoord pos = m_owner->getPosition();
	pos = CoordDivBlock(pos - WCoord(0, BLOCK_SIZE, 0));
	Block block = m_owner->getWorld()->getBlock(pos);
	//if (block.isEmpty())
	//{
	//	splitResult = 4;
	//}
	if (block.getResID() == 0)
	{
		splitResult = 1;//在空中(非太空)也不能变形;
	}
	else if (m_owner->getLocoMotion()->isInSpaceAirBlock() || m_owner->isInWater())
	{
		splitResult = 3;
	}
	else
	{
		int splitNum = 0;//--def ? def->ChangeContact : 0;
		for (int i = 0; i < MAX_SKIN_EFFECT; ++i)
		{
			if (def->ChangeContact[i] > 0)
			{
				splitNum++;
			}
		}
		if (splitNum > 1)
		{
			splitResult = 0;
		}
		else
		{
			splitResult = 4;
		}
		findPlaceSplitDisguise(splitResult);
	}

	if (splitResult != 0)
	{
		if (splitResult == 4)
		{
			//暂无提示
			return false;
		}
		else if (splitResult < 4)
		{
			InfoTips(30350 + splitResult);
			return false;
		}
	}

	return true;
}

void TransformersSkinComponent::InfoTips(int id, int num)
{
	char dest[128];
	const char* info = GetDefManagerProxy()->getStringDef(id);
	const char* replacestr = strstr(info, "@num");
	if (replacestr)
	{
		size_t n = replacestr - info;
		memcpy(dest, info, n);
		snprintf(&dest[n], sizeof(dest) - n, "%d%s", num, replacestr + 4);
		dest[sizeof(dest) - 1] = 0;
	}
	else 
	{
		//MyStringCpy(dest, sizeof(dest), info);
		strncpy(dest, info, sizeof(dest));
		dest[sizeof(dest) - 1] = 0;
	} 

	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_String("info", dest);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	    MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_INFO_TIPS", sandboxContext);
}

void TransformersSkinComponent::findPlaceSplitDisguise(int &splitResult, bool ignoreOccupied /* = false */)
{
	if (!GetOwner()) return ;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	World* pWorld = m_owner->getWorld();
	if (!pWorld)
	{
		return;
	}
	WCoord pos = m_owner->getPosition();
	pos = CoordDivBlock(pos - WCoord(0, BLOCK_SIZE, 0));
	//最多四个子装扮位置
	int posArrX[4] = { -2, 2, -2, 2 };
	int posArrZ[4] = { 2, 2, -2, -2 };
	m_SplitPos.clear();
	for (int i = 0; i < 4; ++i)
	{
		WCoord findPos = pos + WCoord(posArrX[i], 0, posArrZ[i]);
		Block findBlock = pWorld->getBlock(findPos);
		bool isFindEmpty = false;
		if (findBlock.isEmpty())
		{
			splitResult = 1;
			break;
		}
		BlockMaterial* findMaterial = g_BlockMtlMgr.getMaterial(findBlock.getResID());
		if (findMaterial && findMaterial->isSolid())
		{
			//向上寻找，看看是不是有最上面的方块
			for (int j = 0; j < 5; ++j)
			{
				findPos += WCoord(0, 1, 0);
				findBlock = pWorld->getBlock(findPos);
				//if (findBlock.isEmpty())
				//{
				//	continue;
				//}
				findMaterial = g_BlockMtlMgr.getMaterial(findBlock.getResID());
				//LOG_INFO("Block res id is: %d", m_pWorld->getBlockID(findPos));
				if (findMaterial && (findBlock.isAir() || (!findMaterial->isLiquid() && !findMaterial->m_Def->PhyCollide)))
				{
					//检查当前方块是否被其他NPC，动物之类的占用
					std::vector<IClientActor*> ca;
					CollideAABB box;
					findMaterial->getCollisionBoundingBox(box, pWorld, findPos);
					pWorld->getActorsInBox(ca, box);
					if (ca.size() == 0 || ignoreOccupied)
					{
						isFindEmpty = true;
						m_SplitPos.push_back(findPos);
					}
					else
					{
						splitResult = 2;
					}
					break;
				}
			}
		}
		else if (findBlock.isAir() || (findMaterial && !findMaterial->isLiquid() && !findMaterial->m_Def->PhyCollide))
		{
			//是空气的话往下寻找
			for (int j = 0; j < 5; ++j)
			{
				findPos -= WCoord(0, 1, 0);
				findBlock = pWorld->getBlock(findPos);
				//if (findBlock.isEmpty())
				//{
				//	continue;
				//}
				findMaterial = g_BlockMtlMgr.getMaterial(findBlock.getResID());
				if (findMaterial && findMaterial->isSolid() && findMaterial->m_Def->PhyCollide)
				{
					findPos += WCoord(0, 1, 0);
					findBlock = pWorld->getBlock(findPos);
					//if (findBlock.isEmpty())
					//{
					//	continue;
					//}
					findMaterial = g_BlockMtlMgr.getMaterial(findBlock.getResID());
					if (findMaterial)
					{
						//检查当前方块是否被其他NPC，动物之类的占用
						std::vector<IClientActor*> ca;
						CollideAABB box;
						findMaterial->getCollisionBoundingBox(box, pWorld, findPos);
						pWorld->getActorsInBox(ca, box);
						if (ca.size() == 0 || ignoreOccupied)
						{
							isFindEmpty = true;
							m_SplitPos.push_back(findPos);
						}
						else
						{
							splitResult = 2;
						}
						break;
					}
				}
				else if (findMaterial && !findMaterial->isSolid() && !findBlock.isAir())
				{
					//找到非固体和非空气直接中止;
					splitResult = 1;
					break;
				}
			}
		}
		if (!isFindEmpty)
		{
			if (splitResult == 0)
			{
				splitResult = 1;
			}
			break;
		}
	}
}

bool TransformersSkinComponent::onInteractByActorSkinNpc(long long mainPlayer, int changeModel)
{
	if (!GetOwner()) return false;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	ActorBody* body = m_owner->getBody();
	World* pWorld = m_owner->getWorld();
	int uin = m_owner->getUin();
	long long objId = m_owner->getObjId();

	//副装扮玩家点NPC无效
	if (objId != mainPlayer && !m_bInTransform)
	{
		m_owner->ClientPlayer::changePlayerModel(ComposePlayerIndex(
			body->getModelID(),
			body->getGeniusLv(), changeModel));
		body = m_owner->getBody();

		m_bInTransform = true;
		ClientActor * mainActor = dynamic_cast<ClientActor*>(GetWorldManagerPtr()->findActorByWID(mainPlayer));
		ClientPlayer *pMainPlayer = dynamic_cast<ClientPlayer*> (mainActor);
		if (pMainPlayer)
		{
			//pMainPlayer->m_pTransformersSkinComp->m_vecSkinSubPlayers.push_back(objId);
			//todo_check Logo
			pMainPlayer->insertSkinSubPlayer(objId);
			m_MainSkinPlayerID = pMainPlayer->getObjId();
		}
		/*LOG_INFO("ActorSkinNpc %d, %d", pPlayer->getObjId(), pMainPlayer->getObjId())*/

		if (!pWorld->isRemoteMode())
		{
			PB_PlayerTransformSkinHC changeModelHC;
			changeModelHC.set_uin(uin);
			changeModelHC.set_playerindex(ComposePlayerIndex(
				body->getModelID(),
				body->getGeniusLv(), changeModel));
			changeModelHC.set_customskins("");
			changeModelHC.set_reason(1);
			if (pMainPlayer)
				changeModelHC.set_mainplayerid(pMainPlayer->getObjId());
			else
			{
				changeModelHC.set_mainplayerid(0);
			}
			//m_pWorld->getMpActorMgr()->sendMsgToNearPlayers(PB_PLAYER_CHANGEMODEL_HC, changeModelHC, pos, broadcastRange, true, UNRELIABLE_SEQUENCED);
			pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYERTRANSFORMSKIN_HC, changeModelHC, m_owner, true);
		}
		return true;
	}
	return false;
}


void TransformersSkinComponent::resetActorBody()
{
	TransformersSkinComponent::resetDeformation(2);
}
IMPLEMENT_COMPONENTCLASS(MPTransformersSkinComponent)

MPTransformersSkinComponent::MPTransformersSkinComponent():TransformersSkinComponent()
{

}

void MPTransformersSkinComponent::resetActorBody()
{
	if (!GetOwner()) return;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	TransformersSkinComponent::resetActorBody();
	if (m_owner->getWorld()->isRemoteMode())
	{
		PB_PlayerResetDeformationCH resetMsg;
		resetMsg.set_actorid(m_owner->getObjId());
		GetGameNetManagerPtr()->sendToHost(PB_RESETDEFORMATION_CH, resetMsg);
	}
}

void MPTransformersSkinComponent::restoreSkin()
{
	if (!GetOwner()) return;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	if (m_owner->getWorld()->isRemoteMode())
	{
		PB_PlayerRestoreTransformSkinCH resetMsg;
		GetGameNetManagerPtr()->sendToHost(PB_RESTORE_DEFORMATION_CH, resetMsg);
	}
	else
	{
		TransformersSkinComponent::restoreSkin();
	}
}