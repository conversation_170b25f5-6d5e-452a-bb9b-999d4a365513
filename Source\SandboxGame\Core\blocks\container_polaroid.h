#ifndef __CONTAINER_POLAROID_H__
#define __CONTAINER_POLAROID_H__

#include "container_world.h"
#include "SandboxCallback.h"
#include "SandboxGame.h"

/*
* 边界类型 
//0表示单个
//1表示左包 2表示右包 3表示上包 4表示下包
//3表示左上角，4表示上中间，5表示右上角
//6表示左中间，7表示中间  ，8表示右中间
//9表示左下角，10表示下中间，11表示右下角
*/
enum FrameEdgeType {
	NONE_EDGE = -1,
	//用于核心方块，分表代表了不同方向
	SINGLE_POS_X = 0,
	SINGLE_NEG_X,
	SINGLE_POS_Z,
	SINGLE_NEG_Z,
	TOP_CLOSURE,
	//用于核心方块，分表代表了不同方向
	BOTTOM_CLOSURE_POS_X,
	BOTTOM_CLOSURE_NEG_X,
	BOTTOM_CLOSURE_POS_Z,
	BOTTOM_CLOSURE_NEG_Z,
	//用于核心方块，分表代表了不同方向
	LEFT_CLOSURE_POS_X,
	LEFT_CLOSURE_NEG_X,
	LEFT_CLOSURE_POS_Z,
	LEFT_CLOSURE_NEG_Z,
	RIGHT_CLOSURE,
	LEFT_TOP,
	MIDDLE_TOP,
	RIGHT_TOP,
	LEFT_MIDDLE,
	MIDDLE_MIDDLE,
	RIGHT_MIDDLE,
	LEFT_BOTTOM_POS_X,
	LEFT_BOTTOM_NEG_X,
	LEFT_BOTTOM_POS_Z,
	LEFT_BOTTOM_NEG_Z,
	MIDDLE_BOTTOM_POS_X,
	MIDDLE_BOTTOM_NEG_X,
	MIDDLE_BOTTOM_POS_Z,
	MIDDLE_BOTTOM_NEG_Z,
	RIGHT_BOTTOM,
};

struct PolaroidPartFrameInfo {
	PolaroidPartFrameInfo() : blockPos(0,0,0), edgeType(FrameEdgeType::NONE_EDGE)
	{
	}
	PolaroidPartFrameInfo(WCoord pos, FrameEdgeType type) : blockPos(pos), edgeType(type)
	{
	}

	WCoord blockPos;
	FrameEdgeType edgeType;
};

class EXPORT_SANDBOXGAME ContainerPolaroidFrame;
class ContainerPolaroidFrame : public WorldContainer //tolua_exports
{//tolua_exports
public:
	//tolua_begin
	ContainerPolaroidFrame();
	ContainerPolaroidFrame(const WCoord& blockpos);
	virtual ~ContainerPolaroidFrame();

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata) override;

	virtual bool doOpenContainer() override;

	virtual int getObjType() const override;
	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerPolaroidFrame;
	}
	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld() override;

	//id转换
	flatbuffers::Offset<FBSave::ChunkContainer>  ConvertModResourceId(const void* srcdata, SAVE_BUFFER_BUILDER& builder);

	std::string getPhotoURL()
	{
		return m_PhotoURL;
	}
	int getPhotoStretchMode()
	{
		return m_PhotoStretchMode;
	}
	std::string getFrameURL()
	{
		return m_FrameStyle;
	}
	void getFrameSize(unsigned int& width, unsigned int& height)
	{
		width = m_FrameWidth;
		height = m_FrameHeight;
	}
	//监听相片的状态改变
	void listenPhotoStatusChange();
	//改变相框的大小（这里不能改变的放置方向，放置方向跟着核心方块走）
	bool changeFrameSize(int width, int height);
	//除了核心方块外销毁相框的其余部分
	void destroyFrameParts();
	//获取相框指定部分的信息
	PolaroidPartFrameInfo* getPartFrameInfo(const WCoord& blockPos);
	//判断方块位置书否属于相框
	bool isBlockBelongToMe(const WCoord& blockPos);
	//获取相框指定部分的类型
	FrameEdgeType getBlockFrameType(const WCoord& blockPos);
	//更新相框的样式
	void updateFrameStyle(std::string frameStyle);
	//更新图片的适配方式
	void updatePhotoFitMode(int fitModel);
	void updatePhotoURL(std::string photoUrl);
	//URL变了，更新
	void updatePhoto3DTexture();
	//删除相片的显示对象
	void destroyPhoto3DTexture();
	//tolua_end
	void updateFrameChange(std::string jsonstr, bool isHC);//isHC 是否主机到客机
	//同步给其他玩家相框改变了
	void syncToOtherPlayerFrameChange(DirectionType blockDir = DirectionType::DIR_NOT_INIT, bool isDestoryTex = false);
	//重新创建拍立得相框
	void recreatePolaroidFrame();
	//旋转拍立得相框
	bool rotatePolaroidFrame(DirectionType direc);
	void setRecreate(bool isRecreate) { m_IsRecreate = isRecreate; };
private:
	//是否足够的空间放置相框
	bool isEnoughSpacePlaceFrame(int width, int height, int blockDir);
	//创建特殊的相框，有一边为1的
	void createSpecialFrame(World* pWorld, int blockId, int blockData, int blockDir);
	//创建普通的相框，两边都部位1的
	void createNormalFrame(World* pWorld, int blockId, int blockData, int blockDir);
	void resetPolaroidParam();

	//所有相框的信息
	std::vector<PolaroidPartFrameInfo> m_FramePartsInfo;
	//相片地址
	std::string m_PhotoURL;
	//相片的适配模式
	int m_PhotoStretchMode;
	//相框地址
	std::string m_FrameStyle;
	unsigned int m_FrameWidth;
	unsigned int m_FrameHeight;

	//这个纹理Id不用持久化
	int m_Dev3DTextureID;
	//用于蓝图
	bool m_IsRecreate;
	//审核状态改变回调
	MNSandbox::Callback m_PicStateChangeCB;
};//tolua_exports
#endif