
#include "BombLocoMotion.h"
#include "coreMisc.h"
#include "ActorBomb.h"
#include "world.h"
IMPLEMENT_COMPONENTCLASS(BombLocoMotion)
BombLocoMotion::BombLocoMotion()
{
	m_BoundHeight = 98;
	m_BoundSize = 98;
	m_yOffset = m_BoundHeight/2;
}

void BombLocoMotion::tick()
{
	ActorBomb *actor = static_cast<ActorBomb *>(getOwnerActor());
	if(actor->getBombType() != BOMB_DEFAULT) return;

	ActorLocoMotion::tick();

	m_Motion.y -= m_pWorld->getGravity(GRAVITY_ITEM);
	doMoveStep(m_Motion);
	m_Motion *= 0.98f;

	if(m_OnGround)
	{
		m_Motion.x *= 0.7f;
		m_Motion.z *= 0.7f;
		m_Motion.y *= -0.5f;
	}
}

static int Yaw2DirectionType(float yaw)
{
	yaw = WrapAngleTo180(yaw);
	if(yaw < -135) return DIR_NEG_Z;
	else if(yaw < -45) return DIR_NEG_X;
	else if(yaw < 45) return DIR_POS_Z;
	else if(yaw < 135) return DIR_POS_X;
	else return DIR_NEG_Z;
}
