#ifndef __RADIATION_COMPONENT_H__
#define __RADIATION_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "ClientActor.h"
#include "math/CCGeometry.h"

namespace Rainbow {
    class MaterialInstance;
}
namespace fairygui
{
    class GLoader;
}

class EffectParticle;

enum class RCBuffType : int {
    Safe = 0,            // 安全
    LowRadiation,        // 低度辐射
    MediumRadiation,     // 中度辐射
    HighRadiation,       // 高度辐射
    LethalRadiation,     // 致命辐射
};

struct RadiationImpactDuration
{
    int  BuffId;           // Buff ID
    int  TickNum;          // 计时器
    int  JumpAddTickNum;   // 跳跃增加计时
    int  JumpIntervalTickNum; // 跳跃间隔
    int  PermanentBuffTickNum; // 永久Buff计时
    int  LimitTime;        // 时间限制
    float TickMark;        // 计时标记
    int  JumpTickMark;     // 跳跃计时标记
    int  SoundTick;        // 声音计时
    RadiationImpactDuration() : BuffId(0), TickNum(0), JumpAddTickNum(0), JumpIntervalTickNum(0), 
                              PermanentBuffTickNum(0), LimitTime(0), TickMark(0), JumpTickMark(0), SoundTick(0) {}
};

struct RadiationEffect
{
    int BuffId;
    EffectParticle* Particle;  // 可以使用EffectID来获取Effect
    int EffectID;
    std::string Path;
    float Scale;
    bool IsChangeScale;
    RadiationEffect() : BuffId(0), Particle(nullptr), EffectID(0), Path(""), Scale(0), IsChangeScale(false) {}
    RadiationEffect(int buffId, EffectParticle* particle, std::string path, float scale, bool isChangeScale);
    void setParticle(EffectParticle* particle);
};

struct RadiationScreenDecal
{
    int BuffId;
    int ImgId;
    bool IsAlpha;
    RadiationScreenDecal(int buffId, int imgId, bool isAlpha)
        : BuffId(buffId), ImgId(imgId), IsAlpha(isAlpha) {}
};

class World;

class RadiationComponent : public ActorComponentBase 
{
public:
    DECLARE_COMPONENTCLASS(RadiationComponent)

    RadiationComponent();

    virtual void OnTick() override;
    virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
    virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

    void Update();
   
    void RenderUI();

    void GetRealRadiation();
    World* GetWorld();

    RCBuffType GetBuffType(float rad);

    void UpdateScreenDecals();
    void SetScreenDecals(int buffId, bool isScreenDecals, int imgId = 0, bool isAlpha = false);

    void UpdateModelTexture();
    void UpdateWeaponTexture();
    void ChangeModelTexture(int buffId, bool isChange, bool isTransition = false, float minValue = 0, float maxValue = 0);

    void UpdateRadiationEffects();
    void AddRadiationEffect(int buffId, int particleId, float modelScale, bool isChangeScale);
    void RemoveRadiationEffect(int buffId);
    void ClearAllRadiationEffects();
    
    void UpdateChangeRadiation();
    void ChangeRadiation(float value, int interval, bool isSafeRadiation = false);

    // 辐射影响持续时间
    void UpdateRadiationImpact();
    void AddRadiationImpact(RadiationImpactDuration radiationImpact);
    void RemoveRadiationImpact(int buffId);
    
    // 震动相关功能
    void PlayerShake();
    bool PlayerCameraCanMove();

    // 暂停当前帧
    void UpdatePauseCurrentFrame();
    void PauseCurrentFrame(bool isPause);

    // 获取纹理Alpha
    void GetTextureAlpha();

    ~RadiationComponent();
protected:
    void OnInit();
    
private:
    bool       m_IsInit;
    bool       m_IsAffectedRadiation;
    float      m_RadiationValue;
    RCBuffType m_CurrentBuff;
    std::map<RCBuffType, int> m_BuffTextureAlpha;

    Rainbow::SharePtr<Rainbow::Texture2D>  m_Texture;
    Rainbow::SharePtr<Rainbow::MaterialInstance> m_TextureMaterial;
    int  m_TextureAlpha;
    bool m_IsTextureAlpha;
    bool m_IsChangeScreenDecal;

    std::vector<RadiationScreenDecal> m_ScreenDecals;
    std::vector<RadiationEffect> m_RadiationEffects;
    std::vector<RadiationImpactDuration> m_RadiationImpacts;
    std::vector<int> m_ModelTextures;
    
    bool m_IsChangeModelTexture;
    float m_CMTMaxValue;
    float m_CMTValue;

    bool m_IsSafeRadiation;
    float m_ChangeRadiationValue;
    int m_ChangeRadiationTicks;
    int m_ChangeRadiationTickMark;
  
    bool m_IsPlayerShake;
    bool m_IsPlayerCameraCanMove;

    World* m_pWorld;

    bool m_ChangeIsPauseCurrentFrame;
    bool m_PauseCurrentFrame;

    fairygui::GLoader* m_gloader;
    cocos2d::Rect m_Rect;

    // 累积辐射值 - 用于模拟长期低剂量辐射效应
    float m_AccumulatedRadiation;
    int m_AccumulationTick;
    
    // 辐射防护值 - 来自防护装备
    float m_RadiationProtection;
};

#endif 