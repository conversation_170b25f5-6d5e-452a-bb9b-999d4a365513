﻿#pragma once
#include "OgreHashTable.h"
#include "world_types.h"
#include "world.h"
#include "blocks/container_workshop.h"
#include "SandboxGame.h"
#include "Graphics/Texture2D.h"
#include "CoreCommonDef.h"
#include "PlayManagerInterface.h"
class VehicleBlock;
struct IconDesc;

namespace game {
	namespace hc {
		class PB_WorkshopItemInfoHC;
	}
}
//tolua_begin
struct PhysicPartTypeStruct
{
	int PartType;
	int SubType;
};
//tolua_end

//tolua_begin
struct AutoLineStruct
{
	int ControlType;
	int ControlSubType;
	int WorkType;
	int WorkSubType;
	char CanEdit;
};
//tolua_end
class ClientActor;
class ClientPlayer;
class EXPORT_SANDBOXGAME VehicleMgr;
class VehicleMgr //tolua_exports
	 : public VehicleMgrInterface
{ //tolua_exports
public:
	//tolua_begin
	VehicleMgr(PluginManager* p);
	virtual ~VehicleMgr(); 

	virtual bool Awake();

	virtual bool Init();

	virtual bool Execute(float dtime);

	virtual bool Shut();

	void CreateModuleEvent();
	void unModuleEvent();

	// 连通检查函数（递归方式）
	bool doCheckNeighborCore(ClientActor* actor, const WCoord &blockpos, Rainbow::HashTable<WCoord, bool, WCoordHashCoder>& check_core,const WCoord& startPos,const WCoord& endPos);
	// 检查是否出了工作台的范围
	bool isOutBox(const WCoord &blockpos,const WCoord& startPos,const WCoord& endPos);

	/*
		清理机械工作台中的块
		pworld:世界指针
		startPos：起始位置
		dim：维度
	*/ 
	bool clearWorkshopBlock(World *pworld,WCoord& startPos,WCoord&dim, ContainerWorkshop* pWorkshop);
	// 利用userdatastr内的信息 生成item对应的model
	virtual Rainbow::Model* getItemModel(std::string userdatastr, ITEM_MESH_TYPE meshtype) override;
	//tolua_end

	void saveOriginalFB(ClientPlayer* player, VehicleBlock* vehicleblock[MAX_DIM_X][MAX_DIM_Y][MAX_DIM_Z], int itemID, std::string filename, std::string modelname, std::string modeldesc, int modeltype, int dirtype);

	//tolua_begin
	void converBlockPosByPlaceDir(WCoord &pos, int dirtype, WCoord &dim, int modeltype);
	int covertPosToBit(int x, int y, int z) { return (x + (y << 8) + (z << 16)); };
	int getRealPosByBit(int bit, int pos);
	int getFreeId(int type);
	//tolua_end

	// 掉落模型 return:itemid
	int dropItem(ClientPlayer* player, VehicleBlock* vehicleblock[MAX_DIM_X][MAX_DIM_Y][MAX_DIM_Z], int origItem, std::string filename, std::string modelname, std::string modeldesc, int dir, WCoord position, std::vector<VehicleBlockLine>& blocklines);
	
	//tolua_begin
	// 掉落模型新函数
	int dropItem(ClientPlayer* player, ActorVehicleAssemble* assemble,int origItem,std::string filename, std::string modelname, std::string modeldesc);

	WCoord convertWcoordByRotate(int rotatetype, WCoord pos,bool isOldVer=false);
	// 将dir方向转换成连续型的rotateType：ROTATE_0、ROTATE_90、ROTATE_180、ROTATE_270
	int changeDirToSquence(int dir);
	bool parseUserdatastrForNameDesc(std::string userdatastr, std::string& name, std::string& desc);
	//tolua_end
	/*
		解析userdatastr
		userdatastr：要解析的json字符串
		blocks：存储数据的vector
		return true：解析成功 false：解析失败

		p2：添加flatbuffer格式的savedata

		p3：注意用这个函数之前先把参数vContainer进行delete操作，防止内存泄漏
	*/
	//返回值0为解析失败，1为旧存档，2为新存档
	int parseUserdatastr(std::string userdatastr, std::vector<BlockData> &blocks, std::vector<int>& vBlockhp, std::vector<int>& vBlockfuel, std::vector<int>& vBlockheat, int& dir, std::map<WCoord,char*>& vContainer, ActorVehicleAssemble* pVehicle = NULL, bool isCopy = false);
	/*
		根据container生成一个block
		pworld:世界指针
		start：起始位置
		index：索引值 创建到了第几个block
		container:container_workshop
		rotatetype：旋转类型

		return 创建块完毕或者下一个索引
	*/
	int buildVehicleOneBlock(World *pworld, WCoord start, WCoord dim, int index, ContainerWorkshop *container, int rotatetype, std::vector<BlockData> &blocks, std::map<WCoord,char*>& containerData);

	//一次性创建全部的方块
	bool buildVehicleAllBlock(World *pworld, WCoord start, WCoord dim, ContainerWorkshop *container, int rotatetype, std::vector<BlockData> &blocks, std::map<WCoord, char*>& containerData);

	//tolua_begin
	// 参数含义同上
	// 载入中断，剩余block成为掉落物
	void interruptVehicleBuild(World *pworld, WCoord start, WCoord dim, int index, ContainerWorkshop *container, int rotatetype, std::vector<BlockData> &blocks);
	// 播放建造音效
	void playBuildSound(World *pworld, int blockid, WCoord pos);

	//for ContainerWorkshop manage
	void addWorkshop(ContainerWorkshop* pWorkshop) { if(pWorkshop)m_vWorkshopList.push_back(pWorkshop); }
	void removeWorkshop(ContainerWorkshop* pWorkshop);
	const std::vector<ContainerWorkshop*>& getWorkshopList() { return m_vWorkshopList; }
	int checkIfCanPlaceBlockInWorkShop(ClientPlayer* player, int blockid, const WCoord& blockPos, bool bNeedCal = true);//return -100 means can be placed, other can not
	void addPlayerInWorkshop(ContainerWorkshop* pWorkshop) { if (pWorkshop)m_vPlayerInWorkshopList.push_back(pWorkshop); }
	void removePlayerInWorkshop(ContainerWorkshop* pWorkshop);
	
	// 生成保存itemid的文件
	bool saveVehicleItemidFile();
	void loadVehicleItemidFile(long long owid, int specialType = NORMAL_WORLD);
	void leaveWorld();
	void clearItemDef();
	// 控制删除一下对应的itemid
	void removeOneItem(int id);
	// 添加一个itemid  needSync表示是否需要同步给客机
	void addOneItem(int id, bool needSync=true);
	// 发消息 对刚进入地图的科技进行同步所有数据
	void syncPlayerAllVehicleItemid(int uin);
	// 发消息 同步一条新的itemid给所有客机
	void syncPlayerOneVehicleItemid(int itemid);
	// 收消息 同步来自主机的所有itemid
	void syncPlayerAllVehicleItemidByHost(std::vector<int> itemid);
	// 收消息 同步来自主机的一条itemid
	void syncPlayerOneVehicleItemidByHost(int itemid);

	// 获取item对应的icon
	Rainbow::SharePtr<Rainbow::Texture2D>   getModelIcon(int itemid,std::string geomname, std::string userdatastr, int modeltype, int &u, int &v, int &width, int &height, int &r, int &g, int &b);

	// 预览物理机械
	bool preVehicleBlock(World *pworld, WCoord start, WCoord dim, std::vector<PreBlocksData> &preblocks, int rotateType, std::string userdatastr, MachineInfo& stMachineInfo);

	int getMachineAttribute(int iType, bool bNeedCal);
	void createVehicleWithItem(ClientPlayer* player, int x, int y, int z, int dir, int iShortcutIdx);
	int checkIfSpecialBlockCanPlace(ClientPlayer* player, int blockid, int x, int y, int z, bool bNeedCal);//return -100 means can be placed, other can not

	// 关于机械工作台的一些 iteminfo信息同步
	void setWorkShopItemInfoByHost(game::hc::PB_WorkshopItemInfoHC& workshopItemInfoHC);
	void sendBuildInfoToClient(bool isbuild, int mapid, WCoord& pos);

	void VehicleCentroidDisplay(bool isDisplay);

	void loadPhysicsPartsConnectInfo();

	/*
		计算一次机械车间的连通（刚进房间时，客机会有方块同步不完全的情况，这里是通过lua延时2s计算）
	*/
	void caculateConnectByLua();

	// 通过预览机械direction和当前工作台direction计算出旋转角度
	int caculateRotateType(int curDirection, int preDirection);

	int getBlockPhysicsPortType(int blockid);

	// 渲染正在确定目标点的线
	void renderVehicleUnendLink();
	// 确定是结束点还是起始点
	bool makesureVehicleEndLink();

	/*
		确定是结束点还是起始点(机械车间内)
	*/
	bool makesureWorkshopEndLink();
	/*
		确定是结束点还是起始点(机械actor)
	*/
	bool makesureVehicleActorEndLink();

	//bool blockCollideWithRay(BlockMaterial *blockmtl, World *pworld, WCoord &blockpos, Rainbow::Vector3f &origin, Rainbow::Vector3f &dir);
	void clearVehicleEndLink(long long objid=0);

	/*
		清理工作台连线
	*/
	void clearWorkshopEndLink();
	/*
		查看是否匹配 可编辑 的连线 
	*/
	bool isLineMatch(int from, int to, WCoord position);
	/*
		查看是否匹配 可编辑 的节点 
	*/
	bool isNodeMatch(int node);

	/*
		找到 准心对准物理机械对应的block
	*/
	void findTheVehicleBlock();

	/*
		针对工作车间进行操作
	*/
	void goInOutVehicleWorkshop(bool show, WCoord workshopPos);
	/*
		连线相关
		获取这条线的连接顺序 -1：倒序  0：不存在  1：正序
	*/
	int findBlockLinesOrder(int startBlock, int endBlock);

	/*
		获取表格数据（默认连接）
	*/
	std::vector<AutoLineStruct> getPhysicsDefaultConnect()
	{
		return m_PhysicsDefaultConnect;
	}

	/*
		获取是否可编辑（默认连接）
		return: 8代表不存在这条连接，3代表手动连接， 0代表不可编辑（默认连接） 1代表可编辑（默认连接）
	*/
	char findDefaultConnect(int startBlock, int endBlock, bool& order);

	/*
		进行方块连通更新
	*/
	void resetWorkshopConnectCoreBlock(std::vector<WCoord> &positions);

	/*
		获取扯出来的起始点
	*/
	WCoord getStartBlockNode()
	{
		return m_StartBlockNode;
	}

	/*
		获取扯出来拉扯出来的机械id
	*/
	long long getVehicleAssembleWID()
	{
		return m_VehicleAssembleWID;
	}

	//tolua_end

	size_t getActorVehiclesAndCollidePosInBox(World* world, std::map<WCoord, ActorVehicleAssemble*>& actors, const CollideAABB& box, int extendrange = -1, long long excludeId = -1);

	size_t getActorVehiclesInBox(World* world, std::vector<ClientActor*>& actors, const CollideAABB& box, int extendrange = -1, long long excludeId = -1, bool allpass = false);

	/*
		事件Protocol_Message_Dispatcher 对应的回调函数
	*/
	MNSandbox::SandboxResult DispatcherMessage_Protocol_Message(MNSandbox::SandboxContext context);
public:
	//tolua_begin
	std::map<std::string, IconDesc*> m_IconDescs;

	std::vector<ContainerWorkshop*> m_vWorkshopList;
	std::vector<ContainerWorkshop*> m_vPlayerInWorkshopList;

	// 用来缓存每进一张地图生成的物理机械对应的itemid
	// 退出地图需要保存到对应地图item.fb文件中
	// 同时清空m_ItemId
	std::vector<int> m_VehicleItemId;
	long long m_Owid;
	int m_nSpecialType;

	/*
		缓存需要渲染机械车间的blockpos
		如果进入的车间变了，那就对应清理其他缓存，重新计算新的缓存数据
		反之就在缓存基础上进行各种增删和渲染连线、圆圈操作
	*/
	WCoord m_InWorkshopPos;

	// int：类型（控制端、执行端、双端）  vector：对应所有的block相对坐标
	std::map<int, std::vector<WCoord>> m_BlockNodes;
	
	// 是否显示各节点
	bool m_BlockNodesShow;

	// 控制端、双端、执行端类型 这个是根据 parttype和subtype来做连线用的，与PhysicsPartsConnect.csv有关
	// 与PhysicsPartType.csv无关
	std::map<std::string, std::vector<PhysicPartTypeStruct>> m_PhysicPartType;

	bool  m_PhysicesConnectLoaded;
	std::vector<int>				m_PhysicsConnectNodes;		// PhysicsPartsConnect.csv表对应的所有可编辑的节点 属性
	std::map<int, std::vector<int>>	m_PhysicsConnectLines;		// PhysicsPartsConnect.csv表对应的所有可编辑的连线 属性

	/*
		默认可连接的 连接信息
	*/
	std::vector<AutoLineStruct>		m_PhysicsDefaultConnect;

	/*
		正在操作连线的物理机械
	*/
	long long m_VehicleAssembleWID;

	// 拉线操作的起始点坐标
	WCoord m_StartBlockNode;
	
	// 准心对准的物理机械
	long long m_FindBlockVehicleWID;
	WCoord m_FindBlockPosition;
	std::map<std::string, MNSandbox::Callback> m_callbackList;
	//tolua_end
}; //tolua_exports
