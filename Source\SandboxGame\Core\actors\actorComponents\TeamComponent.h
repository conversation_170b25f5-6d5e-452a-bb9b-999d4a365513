#ifndef __TEAM_COMPONENT_H__
#define __TEAM_COMPONENT_H__

#include "ActorComponent_Base.h"

class ActorLiving;
class ClientMob;
class ClientPlayer;

class TeamComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(TeamComponent)

	//tolua_begin
	TeamComponent();
	int getTeam(){return m_TeamID;};
	virtual void setTeam(int id ,bool ResetAttr);
	virtual bool isSameTeam(ActorLiving *target); //同一边,  不应该攻击
	virtual void addGameScoreByRule(int ruleid, int num = 1); //ruleid使用的是 GAMEMAKER_RULE，这里为了解耦改成int
	//tolua_end
protected:

	int m_TeamID; //0: 不是任何一边,  >=1: 相同的数字表示相同边
}; //tolua_exports

class MobTeamComponent: public TeamComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(MobTeamComponent)

	//tolua_begin
	MobTeamComponent();
	//tolua_end
protected:
	virtual bool isSameTeam(ActorLiving *target) override; //同一边,  不应该攻击
	virtual void addGameScoreByRule(int ruleid, int num = 1) override; //ruleid使用的是 GAMEMAKER_RULE，这里为了解耦改成int	
private:
	ClientMob* m_ownerMob;
}; //tolua_exports

class PlayerTeamComponent : public TeamComponent //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(PlayerTeamComponent)
public:
	//tolua_begin
	PlayerTeamComponent();
	//tolua_end
	void ResetPlayerAttr(); //设置队伍后是否重置属性
protected:
	void onChangeTeam(int oldteamid);

	virtual void setTeam(int id, bool ResetAttr) override;
	virtual void addGameScoreByRule(int ruleid, int num = 1) override; //ruleid使用的是 GAMEMAKER_RULE，这里为了解耦改成int
private:
	ClientPlayer* m_ownerPlayer;
}; //tolua_exports

#endif