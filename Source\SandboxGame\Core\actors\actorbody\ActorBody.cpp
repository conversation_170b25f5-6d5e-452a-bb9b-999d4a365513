﻿#include "ActorBody.h"
#include "genCustomModel.h"

#include "Mesh/LegacySkinMeshRenderer.h"

#include "BlockScene.h"
#include "WorldRender.h"
#include "ClientItem.h"
#include "SandBoxManager.h"

#include "Text3D/ProgressBar3D.h"
#include "Text3D/MoveByText.h"
#include "Text3D/Voice3D.h"
#include "IClientGameManagerInterface.h"
#include "ModelItemMesh.h"
#include "GameMode.h"
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
#include "ModelView.h"
#endif
#include "OgreUtils.h"
#include "BlockMesh.h"
#include "special_blockid.h"
#include "PlayerControl.h"
#include "GameCamera.h"

#include "RecordPkgManager.h"
#include "GameNetManager.h"
#include "CustomModelMgr.h"
#include "Download/LegacyDownloadManager.h"
#include "FullyCustomModelMgr.h"
#include "minisystem/base/Plugin.h"
#include "VehicleMgr.h"
#include "ActorVillager.h"
#include "ImportCustomModelMgr.h"

#include "Pkgs/PkgUtils.h"
#include "PlayerAttrib.h"
#include "Text3D/MusicClubChatBubble3D.h"
#include "Texture/LegacyTextureUtils.h"

#include "ClientFlyMob.h"
#include "ActorBindVehicle.h"
#include "CarryComponent.h"
#include "RiddenComponent.h"
#include "ClientActorFuncWrapper.h"
#include "EffectComponent.h"
#include "ActorSandworm.h"
#include "Text3D/ImageBoard3D.h"
#include "Common/StatisticHelper.h"
#include "AssetPipeline/Prefab/SharedObjectManager.h"
#include "backpack.h"
#include "ActorGeniusMgr.h"
#include "ActorHorse.h"
#include "LegacyOgreSkeletonData.h"
#include "BlockMaterialMgr.h"
#include "ActorVehicleAssemble.h"
#include "CustomModel.h"
#include "CustomModelWaitSyncListMgr.h"

#include "ClientFlyComponent.h"
#include "SceneEditorMeshGen.h"
#include "UgcAssetMgr.h"
#include "Optick/optick.h"
#include "Entity/ModelRenderer.h"
#include "Entity/ModelAnimationPlayer.h"
#include "Core/nodes/chatBubble/SandboxChatBubbleManager.h"
#include "PlayerLocoMotion.h"

#include "SandboxGameDef.h"
#if HUD_TEST
#include "HUDUI/Render/HUDLevitationFontRender.h"
#include "HUDUI/Render/HUDTitleRender.h"
#endif
#if (defined(DEBUG) || defined(PROFILE_MODE)) && GIZMO_DRAW_ENGABLE
#include "Gizmo/DebugUtility.h"
#endif
#include "UGCEntity.h"
#include "ActorBodySpecialComp.h"
#include "ActorBodySafeHandle.h"
#include "UGCModelLoader.h"
#include <camera/CameraManager.h>

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

#pragma region StaticVariables&ToolFunctions
namespace MNSandbox
{
	static int gs_ActorBodyId = 0;

	//为什么缺省对象使用两个不同的模型？
	static Rainbow::FixedString DefaultActorBody = "entity/100039/body";
	static Rainbow::FixedString DefaultActorAsset = "entity/100026/body";

	//0: 男性, 1: 女性, 2: 小男孩, 3: 小女孩
	static int s_PlayerSex[] =
	{
		0,  //野蛮人男
		0,  //野蛮人小孩
		0,  //考古老人
		0,  //军人男
		0,  //研究男
		2,  //熊孩子
		1,  //旗袍女
		1,  //野蛮人女
		1,  //军人女
		3,  //考古小孩
	};

	int GetPlayerSex(int modelId)
	{
		return s_PlayerSex[modelId - 1];
	}

	static float GotoRotation(float from, float to, float range)
	{
		float da = WrapAngleTo180(to - from);
		if (da < -range) da = -range;
		else if (da > range) da = range;

		return to - da;
	}
}

//从from到to，变化的最大范围为range
//从30到60，限制10，那么返回的 30+10=40
//从30到-30，限制10，返回 30-10=20
//应当用LimitAngle函数
float UpdateRotation(float from, float to, float range)
{
	float angle = WrapAngleTo180(to - from);
	if (angle > range) angle = range;
	else if (angle < -range) angle = -range;

	return from + angle;
}
#pragma endregion


STATIST_HELPER_DECLEAR(EXPORT_SANDBOXGAME, ActorBody)
STATIST_HELPER_IMPL(EXPORT_SANDBOXGAME, ActorBody)

ActorBody::ActorBody(ClientActor* owner)
	: m_MoveDir(0, 0, -1.0f), m_LookAt(0, 0, -1.0f), m_LookTargetYaw(0), m_LookTargetPitch(0), m_DeltaLookYaw(0),
	m_DeltaLookPitch(0),
	m_OwnerActor(owner),
	m_World(NULL),
	m_Entity(nullptr), m_PlayerIndex(0), m_PlayerFrameId(0), m_MutateMob(0),
	m_SkinEffectCount(0),
	m_SkinFlyingEffect(false), m_isLookAt(false), m_NeedUpdateAnim(true), m_NeedUpdateSkinEffect(true),
	/*m_NeedUpdateBallEffect(false),*/ m_NowPlaySeqID(-1),
	m_bIsCheckSeqList(false), m_bIsWinking(false), m_unWinkTick(0), m_HeadBoneID(0), m_HeadBoneScale(1.0f),
	m_FaceMesh(nullptr),
	/*m_pInitTex(NULL),*/ m_ModelScale(1.0f), m_RealScale(1.0f), m_hasAvatar(false), m_hasShearedColor(false),
	m_Sheared(false),
	m_bIsMonsterSkin(false),
	m_ShowSaddle(0), m_ShowNecklace(0), m_ShowRake(0), m_faceId(0),
	m_IsAttachModelView(false),
	m_TargetYaw(0), m_OriginRotation(0), m_LerpRotationStartMarker(0),
	m_nAct(-1), m_bSideAct(false), //20210908 codeby：chenwei 新增副动作标记
	m_nActTrigger(-1),
	m_CheckCustomModelBindTick(0),
	m_iActSeqId(-1),
	m_nAnimSeq(0), //20210926 codeby：chenwei 新增动作序列号初始化
	m_shapeAnimEntity(NULL),
	m_bNeedRecoverShapeHeight(false),
	m_fBeforeShapeHeight(16.0f),
	m_ForceCull(-1),
	m_fHeadLerpSpeed(15.0f),
	m_fBodyLerpSpeed(6.0f),
	m_bInterpRotation(true),
	m_bDebugDrawLerpDirection(false),
	m_HeadEffectObjHeight(0),
	m_ownerActorTriggerProjecttile(false),
	m_EquipComponent(nullptr),
	m_AvatarComponent(nullptr),
	m_UIComponent(nullptr),
	m_ThornBallComponent(nullptr),
	m_suSeqMustPlayDesc(nullptr),
	m_AttachmentHandlerComponent(nullptr)
{
	m_Id = ++gs_ActorBodyId;
	STATIST_HELPER_ADD(ActorBody);
	ActorBodySafeHandle* gsActorBodySafeHandle = Rainbow::GetActorBodySafeHandle();
	gsActorBodySafeHandle->Regist(this);
	//LogStringMsg("[actorbody] +++++++++++ counter:%d,actorid:%d", STATIST_HELPER_GET_COUNTER(ActorBody), m_Id);

	m_RotationYawHead = 0;
	m_RenderYawOffset = 0;
	m_CurAnim[0] = -1;
	m_CurAnim[1] = -1;
	m_CurSeqID[0] = -1;
	m_CurSeqID[1] = -1;
	m_CurWeaponSeqID = -1;

	m_YawOffsetHelper = 0;
	m_YawOffsetHelpTicks = 0;

	m_HurtTicks = 0;
	m_HeadRotRange = 75.0f;

	m_IsInUI = false;
	m_ctrlRotate = true;
	m_LerpRotation = false;
	m_ExchangeTex = nullptr;
	m_NeedExchangeTexture = false;
	m_NeedBackgroundloadModel = false;
	m_LerpRotationTick = 20;
	m_ModelIsLoading = false;

	m_LastSkinEffectPos = WCoord(0, -9999, 0);

	m_SkinEffect3Playing = false;
	m_SkinEffect4Playing = false;
	m_FaceTexs = NULL;
	m_OwnerPlayer = NULL;
	m_BodyType = 0;
	m_BodyScale = 1.0;

	m_NeedUpdateLookUp = true;
	m_NeedUpdateRenderYawOffset = true;

	m_LockRenderYawOffset = false;
	m_LockRenderYawLookTarget = { 0,0,0 };

	m_pNowBodyTex = nullptr;
	m_pSourceTex = nullptr;
	m_pOcclusionTex = nullptr;
	m_pTransitionTex = nullptr;
	m_ARTexPath = "";

	m_BodyColor = 0;
	m_NeedAlpha = false;

	m_iAvtBodyID = 0;


	m_vMeshToShow.clear();

	m_bIsShapeShift = false;
	m_bIsShow = true;
	m_bAnimSwitchCall = false;

	m_bkeeplook = false;
	m_bIsNeedLoadCusAnim = false;
	m_iCusAnimCount = 0;
	m_CurMotionId = 0;

	//TODO:unrealskin
	//m_nUSCurSkinType = 0;
	//m_pUSSkinMotion = SANDBOX_NEW UnrealSkinMotion(this);

	m_bIsNeedRemoveCusMotion = false;
	m_iImportModelType = 0;
	m_NameObjHeight = 16;
	m_bupdateSkin = false;
	m_bShowUp = false;
	m_bInStarStationCabin = false;
	m_bIsEdit = false;
	m_AttachScene = NULL;
	m_CurToolID = 0;
	m_NowPlayAnimLayer = -1;
	m_ablePlayerOtherAnim = true;

	if (m_OwnerActor)
	{
		if (m_OwnerActor->IsTriggerProjectile())
		{
			m_ownerActorTriggerProjecttile = true;
		}
	}

	m_takeoffAble = true;
}

ActorBody::~ActorBody()
{
	ActorBodySafeHandle* gsActorBodySafeHandle = GetUnsafeActorBodyHandle();// GetActorBodySafeHandle();
	if (gsActorBodySafeHandle)
	{
		gsActorBodySafeHandle->Unregist(this);
	}
	if (m_OwnerActor)
	{
		SceneEditorMeshGen::GetInstancePtr()->DestoryPreviewMesh(m_OwnerActor->getObjId());
	}
	if (m_OldModelData.m_ModelAsset.IsValid()) {
		m_OldModelData.m_ModelAsset->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitCustomskinsModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_AnimAsset.IsValid()) {
		m_OldModelData.m_AnimAsset->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitCustomskinsModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_AnimData.IsValid()) {
		m_OldModelData.m_AnimData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitCustomskinsModelResLoadForPlayer, this);
	}

#ifndef IWORLD_SERVER_BUILD
	if (m_iAvtBodyID != 0 && MINIW::ScriptVM::IsVaild())
	{
		MINIW::ScriptVM::game()->callFunction("AvtCallTable", "ii", 3, m_iAvtBodyID);
	}
#endif
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	//if(m_UIModelView) LOG_INFO("ActorBody destory :%p,%p,%d",this,m_UIModelView,m_ModelViewIndex);
	detachUIModelView(NULL, -1);
	//g_UIActorBodyMgr->clearActorbody(this);
	m_UIModelView = NULL;
#endif
	Rainbow::Model* model = nullptr;
	if (m_Entity) {
		model = m_Entity->GetMainModel();
	}

	CleanLoadModelData();
	if (m_bIsNeedRemoveCusMotion && model != nullptr && model->IsKindOf<Rainbow::ModelLegacy>())
	{
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
		if (legacymodel != nullptr && legacymodel->GetModelData())
		{
			auto modeldata = legacymodel->GetModelData();

			if (modeldata)
			{
				long long objid = 0;
				if (m_OwnerActor)
				{
					objid = m_OwnerActor->getObjId();
				}
				modeldata->CheckRemoveAnims(objid);
			}
		}
	}
	
	ReleaseMainEntity();
	// m_WeaponModel's GameObject destroy in  GameScene::~GameScene()  

	//贴图的共享指针不需要额外释放
	//OGRE_RELEASE(m_ExchangeTex);
	//OGRE_RELEASE(m_pInitTex);
	//OGRE_RELEASE(m_pNowBodyTex);
	//OGRE_RELEASE(m_pSourceTex);
	//OGRE_RELEASE(m_pOcclusionTex);
	//OGRE_RELEASE(m_pTransitionTex);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_shapeAnimEntity);
	//DESTORY_GAMEOBJECT_BY_COMPOENT(m_NeedItemIcon);

	SANDBOX_DELETE(m_UIComponent);

	if (m_FaceTexs)
	{
		/*for(int i=0; i<EXPRESSION_COUNT; i++)
		{
			OGRE_RELEASE(m_FaceTexs[i]);
		}*/
		OGRE_DELETE_ARRAY(m_FaceTexs);
	}
	//if(m_hModelRes)
	//{
	//	//!!!需要释政
	//	m_LoadModelData = nullptr;
	//}


	//m_mAvtBasetex.clear();
	//m_mAvtMasktex.clear();

	m_OwnerActor = nullptr;
	m_OwnerPlayer = nullptr;

	m_vCustomAnimMap.clear();
	m_vMeshToShow.clear();
	m_oriMatMainTexs.clear();

	//ENG_DELETE(m_pUSSkinMotion);
	if (MINIW::ScriptVM::IsVaild())
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("UnrealSkin_delete", SandboxContext(NULL).
			SetData_Usertype("actor", m_OwnerActor));
	}

	m_OwnerActor = NULL;
	//TODO
	ReleaseUIEquipmentPartDef();
	m_vNeedLoadCusMotionID.clear();
	if (MINIW::ScriptVM::IsVaild())
	{
		tolua_clear_tousertype(MINIW::ScriptVM::game()->getLuaState(), this, "ActorBody");
	}

	STATIST_HELPER_SUB(ActorBody);
	//LogStringMsg("[actorbody] ------------- counter:%d,actorid:%d", STATIST_HELPER_GET_COUNTER(ActorBody), m_Id);

	for (int i = 0; i < m_pEffectID.size(); i++)
	{
		ENG_FREE_LABEL(m_pEffectID[i], kMemGame);
	}
	m_pEffectID.clear();
	SANDBOX_DELETE(m_AvatarComponent);
	SANDBOX_DELETE(m_ThornBallComponent);
	SANDBOX_DELETE(m_suSeqMustPlayDesc);
	SANDBOX_DELETE(m_AttachmentHandlerComponent);
}

void ActorBody::ReleaseMainEntity()
{
	m_bIsShow = true;
	if (m_AvatarComponent)
	{
		m_AvatarComponent->CleanCacheAvatarParts();
		m_AvatarComponent->CleanCacheAvatarModelParts();
	}
	//if (m_AttachmentHandlerComponent)
	//{
	//	m_AttachmentHandlerComponent->CleanAttachments();
	//}
	SANDBOX_DELETE(m_EquipComponent);

	releaseCustomEquip();
	if (m_AvatarComponent)
	{
		m_AvatarComponent->releaseAvatarSkin();
	}

	removeThornBallMesh(true);
#if DEBUG_MODE
	m_IsEntityReleaseing = true;
#endif
	if (m_Entity)
	{
		m_Entity->GetGameObject()->RemoveEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_Entity);
	}
	m_vLoadedAnimSeqs.clear();
#if DEBUG_MODE
	m_IsEntityReleaseing = false;
#endif
}

void ActorBody::OnComponentRemoved(const Rainbow::EventContent* evt)
{
	if (!GetActorBodySafeHandle()->IsVaild(this))
		return;

	if (evt->eventType == Evt_ComponentRemoved)
	{
		Rainbow::Component* comp = static_cast<Rainbow::Component*>(evt->userData);

		if (comp == nullptr)
		{
			return;
		}

		if (comp == m_Entity)
		{
			m_Entity = nullptr;
		}
	}
}


//TODO:GM命令切换皮肤
void ActorBody::GMChangeSkin(int nType, int nSkinId, const char* file)
{
	char path[256] = { 0 };
	Rainbow::Model* pModel = NULL;

	//m_nUSCurSkinType = nType;
	core::string modelPath;
	if (nType < 3)
	{
		//角色和皮肤
		if (nType == 1)
		{
			sprintf(path, "entity/player/player%.2d/body.omod", nSkinId);
			modelPath = path;

			sprintf(path, "entity/player/player%.2d", nSkinId);
			m_ResourcePath = path;
		}
		else
		{
			const RoleSkinDef* skindef = GetDefManagerProxy()->getRoleSkinDef(nSkinId);
			if (skindef)
			{
				sprintf(path, "entity/%d/body.omod", skindef->Model);
				modelPath = path;

				sprintf(path, "entity/%d", skindef->Model);
				m_ResourcePath = path;
			}
		}

		if (!modelPath.empty())
		{
			pModel = LoadModel(modelPath.c_str(), NULL);
		}
	}
	else
	{
		//avator
		modelPath = "entity/player/player12/boy1/boy1.prefab";
		pModel = LoadModel(modelPath.c_str(), NULL);
	}

	if (pModel)
	{
		ReleaseMainEntity();

		m_Entity = Entity::Create();
		m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);

		LogStringMsg("GMChangeSkin Entity::Create goUID:%d", m_Entity->GetGameObject()->GetUID());
		if (m_Entity)
		{
			playMotion("acchorse", false, 0);
			if (nType == 1)
				setPlayerIndex(ComposePlayerIndex(nSkinId));
			else
				setPlayerIndex(ComposePlayerIndex(1, 0, nSkinId));

			loadModel(pModel);
			setCurAnim(0, 0);
			UpdateVisiableDistance();
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
			if (player != NULL)
			{
				m_OwnerPlayer = player;
			}
		}

		if (nType >= 3)
		{
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("UnrealSkin_onChangeSkin", SandboxContext(NULL).
				SetData_Usertype("actor", m_OwnerActor).
				SetData_Number("nType", nType));

			playAnimBySeqId(100212);
		}
	}
}

ActorBody::ModelAssetData ActorBody::createRoleModel(int model, bool async)
{
	OPTICK_EVENT();
	ActorBody::ModelAssetData modelData;
	char path[256];

	if (model <= 0)
		model = 1;
	sprintf(path, "entity/player/player%.2d", model);
	m_ResourcePath = path;
	sprintf(path, "entity/player/player%.2d/body.omod", model);

	int playersex = GetPlayerSex(model);
	const char* animpath = "";
	m_IsPlayerModel = true;

	modelData = LoadModelData(path, animpath, async);

	// 兜底
	if (!modelData.m_ModelAsset)
	{
		GetUpDownloadManager().RequestAsyLoad(GetFileManagerWeb().GeneralRealName(path), FileManagerWeb::DP_BLOCK);
		modelData = LoadModelData(DefaultActorBody.c_str(), nullptr, false);
	}

	return modelData;
}

bool ActorBody::initPlayer(int playerindex, int mutate_mob, const char* customskins, int replacemodle, bool async, bool loadHigh)
{
	OPTICK_EVENT();
	if (playerindex < 0)
		playerindex = 1;
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif

	m_IsPlayerModel = true;    
	ReleaseMainEntity();
	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
	//ResetGameObjParent();

	m_NeedBackgroundloadModel = false;
	//m_hModelRes	= NULL;

	m_ModelIsLoading = false;
	m_bIsMonsterSkin = false;
	setPlayerIndex(playerindex);
	m_MutateMob = mutate_mob;
	if (customskins)
		m_CustomSkins = customskins;
	else
		m_CustomSkins.clear();

	m_FaceMesh = nullptr;
#ifndef IWORLD_SERVER_BUILD	
	std::string modelPath;
	m_ResourcePath = "entity/player/player12/boy1";
	modelPath = "entity/player/player12/boy1/boy1.prefab";
	/*
	player12黄皮肤   对应索引 0
	player1 白  对应索引 1
	player2 黑 对应索引 2
	*/
	// 统一处理customskins到player编号的映射
	int playerNum = playerindex; // 默认player12
	if (playerNum == 0)
	{
		playerNum = 12;
	}
	//if (customskins)
	//{
	//	std::string skinStr = customskins;
	//	if (skinStr == "0")
	//	{
	//		playerNum = 12; // girl1 当前版本没有女性，使用player12
	//	}
	//	else if (skinStr == "1")
	//	{
	//		playerNum = 1; // player1
	//	}
	//	else if (skinStr == "2")
	//	{
	//		playerNum = 2; // player2
	//	}
	//}

	// 统一设置资源路径
	char playerPath[256];
	snprintf(playerPath, sizeof(playerPath), "entity/player/player%02d/boy1", playerNum);
	m_ResourcePath = playerPath;

	char modelFullPath[256];
	snprintf(modelFullPath, sizeof(modelFullPath), "%s/boy1.prefab", playerPath);
	modelPath = modelFullPath;
	const char* animpath = NULL/*"entity/player/body02.oanim"*/;
	//缺省模型用同步加载
	m_OldModelData = LoadModelData(modelPath.c_str(), animpath, false);

	if (m_OldModelData.m_ModelAsset.IsValid())
	{
		if (IsOldModelDataLoaded())
		{
			this->OnInitCustomskinsModelResLoadForPlayer(nullptr);
		}
		else
		{
			RegistCustomSkinLoadEvent();
		}

		m_BodyType = 3;
		if (customskins && customskins[0] == 'a')
			m_BodyType = 1;

		return true;
	}
#endif
	UpdateVisiableDistance();
	setCurAnim(0, 0);

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	if (player != NULL)
	{
		m_OwnerPlayer = player;
		if (m_OwnerPlayer->isHost() && m_OwnerPlayer->IsXrayEffectEnable() && getEntity())
		{
			getEntity()->SetXrayEffectEnable(true);
		}
	}
	checkSpectatorBody();
	changeActorActionBySkin(0);
	
	return true;
}

bool ActorBody::IsOldModelDataLoaded()
{
	if (m_OldModelData.m_ModelAsset.IsValid() && m_OldModelData.m_ModelAsset->IsLoaded()
		&& (!m_OldModelData.m_AnimAsset.IsValid() || m_OldModelData.m_AnimAsset.IsValid() && m_OldModelData.m_AnimAsset->IsLoaded())
		&& (!m_OldModelData.m_AnimData.IsValid() || m_OldModelData.m_AnimData.IsValid() && m_OldModelData.m_AnimData->IsLoaded())
		)
	{
		return true;
	}

	return false;
}

void ActorBody::RegistCustomSkinLoadEvent()
{
	if (m_OldModelData.m_ModelAsset.IsValid())
	{
		m_OldModelData.m_ModelAsset->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitCustomskinsModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_AnimAsset.IsValid())
	{
		m_OldModelData.m_AnimAsset->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitCustomskinsModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_AnimData.IsValid())
	{
		m_OldModelData.m_AnimData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitCustomskinsModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_ModelAsset.IsValid())
	{
		m_OldModelData.m_ModelAsset->AddEvent(kResLoadedFinished, &ActorBody::OnInitCustomskinsModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_AnimAsset.IsValid())
	{
		m_OldModelData.m_AnimAsset->AddEvent(kResLoadedFinished, &ActorBody::OnInitCustomskinsModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_AnimData.IsValid())
	{
		m_OldModelData.m_AnimData->AddEvent(kResLoadedFinished, &ActorBody::OnInitCustomskinsModelResLoadForPlayer, this);
	}
}

void ActorBody::RegistModelLoadEvent()
{
	if (m_OldModelData.m_ModelAsset.IsValid())
	{
		m_OldModelData.m_ModelAsset->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitOldRoleModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_AnimAsset.IsValid())
	{
		m_OldModelData.m_AnimAsset->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitOldRoleModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_AnimData.IsValid())
	{
		m_OldModelData.m_AnimData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitOldRoleModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_ModelAsset.IsValid())
	{
		m_OldModelData.m_ModelAsset->AddEvent(kResLoadedFinished, &ActorBody::OnInitOldRoleModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_AnimAsset.IsValid())
	{
		m_OldModelData.m_AnimAsset->AddEvent(kResLoadedFinished, &ActorBody::OnInitOldRoleModelResLoadForPlayer, this);
	}

	if (m_OldModelData.m_AnimData.IsValid())
	{
		m_OldModelData.m_AnimData->AddEvent(kResLoadedFinished, &ActorBody::OnInitOldRoleModelResLoadForPlayer, this);
	}
}


Rainbow::Model* ActorBody::LoadModel(const char* path, const char* animfile)
{
	OPTICK_EVENT();
	OPTICK_TAG("path", path);
	OPTICK_TAG("animfile", animfile);
#ifndef IWORLD_SERVER_BUILD		
	/*if (GetBlockMaterialMgrPtr())
	{
		SharePtr<ModelData> pdata = GetBlockMaterialMgrPtr()->loadModelData(path, animfile);
		if (!pdata) return NULL;
		Rainbow::Model* pmodel = Rainbow::Model::CreateInstanceLegacy(pdata);
		return pmodel;
	}*/

	//oamin动画暂时不转换
	SharePtr<Asset> asset = Model::LoadModelAssetByPathRule(path);
	if (asset)
	{
		if (!asset->IsLoaded())
		{
			WarningString("ActorBody::LoadModel() asset is not loaded!!!");
#ifndef VERSION_MINICODE
			return nullptr;
#endif
		}

		if (asset->IsKindOf<Prefab>())
		{
			ModelNew* model = static_cast<ModelNew*>(Model::CreateInstanceFromPrefab(asset.CastTo<Prefab>()));
			//判断一下animfile的有效性，避免对assetloader传入null
			if (model && animfile)
			{
				SharePtr<RefObject> animAsset = Model::LoadModelAnimAssetByPathRule(animfile);
				if (animAsset)
				{
					if (animAsset->IsKindOf<Prefab>())
					{
						SharePtr<SkinAnimContainer> panim = GetShareObjectManager().LoadShareObject((animAsset.CastTo<Prefab>())->GetPrefabGUID()).CastTo<SkinAnimContainer>();
						model->AddAnimationClips(panim);
					}
					else if (animAsset->IsKindOf<AnimationData>())
					{
						SharePtr<AnimationData> panim = animAsset.CastTo<AnimationData>();
						if (panim->IsVaild())
						{
							std::vector<ModelAnimData> anims;
							bool flag = true;
							for (size_t i = 0; i < anims.size(); i++)
							{
								if (anims[i].anim == panim) {
									flag = false;
									break;
								}
							}
							if (flag)
							{
								ModelAnimData animdata;
								animdata.nbonetrack = panim->m_BoneTracks.size();
								animdata.boneids = new short[animdata.nbonetrack];
								animdata.anim = panim;
								animdata.isNeedRemove = false;
								anims.push_back(animdata);

								SharePtr<SkinAnimContainer> conatainer = MakeSharePtr<SkinAnimContainer>();
								conatainer->AddSkeletonAnimationClips(anims);

								for (size_t i = 0; i < anims.size(); i++)
								{
									delete[] anims[i].boneids;
									anims[i].boneids = nullptr;
								}

								model->AddAnimationClips(conatainer);
							}
						}
						else
						{
							assert(false);
						}
					}
				}
			}
			return model;
		}
		else if (asset->IsKindOf<Rainbow::ModelData>())
		{
			SharePtr<Rainbow::ModelData> modelData = asset.CastTo<Rainbow::ModelData>();
			if (animfile != nullptr)
			{
				SharePtr<AnimationData> panim = GetAssetManager().LoadAsset<AnimationData>(animfile);
				if (panim) {
					LOG_INFO("ANIMFILE %s load success", animfile);
					modelData->AddAnimation(panim);
				}
			}

			Model* model = Model::CreateInstanceLegacy(modelData);
			return model;
		}
	}
#endif	
	return nullptr;
}

const char* ActorBody::GetActorCustomMatPath(ACTOR_CUSTOM_MAT_TYPE matType)
{
	switch (matType)
	{
	case ACTOR_CUSTOM_MAT_TYPE::ACMT_OPAQUE:
		return "Materials/MiniGame/MaterialTemplate/Character_NPR/Character_NPR_Opaque.templatemat";
	case ACTOR_CUSTOM_MAT_TYPE::ACMT_TRANSCLUCENT:
		return "Materials/MiniGame/MaterialTemplate/Character_NPR/Character_NPR_Tranclucent.templatemat";
	case ACTOR_CUSTOM_MAT_TYPE::ACMT_MASKED:
		return "Materials/MiniGame/MaterialTemplate/Character_NPR/Character_NPR_Masked.templatemat";
	default:
		break;
	}

	return nullptr;
}


ActorBody::ModelAssetData ActorBody::LoadModelData(const char* path, const char* animfile, bool async)
{
	ModelAssetData loadModelData;
#ifndef IWORLD_SERVER_BUILD		
	//oamin动画暂时不转换
	SharePtr<Asset> asset = Model::LoadModelAssetByPathRule(path, async);
	loadModelData.m_ModelAsset = asset;

	if (asset)
	{
		if (asset->IsKindOf<Prefab>())
		{
			SharePtr<RefObject> animAsset = Model::LoadModelAnimAssetByPathRule(animfile, async);
			loadModelData.m_AnimAsset = animAsset.CastTo<Asset>();
			return loadModelData;
		}
		else if (asset->IsKindOf<ModelData>())
		{
			SharePtr<AnimationData> panim;
			if (async) {
				panim = GetAssetManager().LoadAssetAsync<AnimationData>(animfile);
			}
			else {
				panim = GetAssetManager().LoadAsset<AnimationData>(animfile);
			}
			loadModelData.m_AnimData = panim;
			return loadModelData;
		}
	}
#endif
	return loadModelData;
}

void ActorBody::InitFaceTexts(Rainbow::Model* model, core::string resourcePath)
{
	if (!model) {
		return;
	}
	m_FaceTexs = new Rainbow::SharePtr<Rainbow::Texture2D>[EXPRESSION_COUNT];

	char path[256];
	char pathprefix[256];
	sprintf(pathprefix, "%s", resourcePath.c_str());


	sprintf(path, "%s/face_100108.png", pathprefix);
	m_FaceTexs[0] = GetAssetManager().LoadAssetAsync<Texture2D>(path);
	sprintf(path, "%s/face_100130.png", pathprefix);
	m_FaceTexs[1] = GetAssetManager().LoadAssetAsync<Texture2D>(path);
	sprintf(path, "%s/face.png", pathprefix); // 新皮肤不再分出_100100.png，EXPRESSION_STAND表情使用face.png，仅表情EXPRESSION，不影响SEQ
	m_FaceTexs[2] = GetAssetManager().LoadAssetAsync<Texture2D>(path);
	sprintf(path, "%s/face_100107.png", pathprefix);
	m_FaceTexs[3] = GetAssetManager().LoadAssetAsync<Texture2D>(path);
	sprintf(path, "%s/face_100155.png", pathprefix);
	m_FaceTexs[4] = GetAssetManager().LoadAssetAsync<Texture2D>(path);
	sprintf(path, "%s/face_100159.png", pathprefix);
	m_FaceTexs[5] = GetAssetManager().LoadAssetAsync<Texture2D>(path);
	sprintf(path, "%s/face_100158.png", pathprefix);
	m_FaceTexs[6] = GetAssetManager().LoadAssetAsync<Texture2D>(path);
	sprintf(path, "%s/face_100100.png", pathprefix); // 新皮肤又再分出_100100.png，用于实现模型眨眼的效果
	m_FaceTexs[7] = GetAssetManager().LoadAssetAsync<Texture2D>(path);
}

void ActorBody::loadModel(Rainbow::Model* model)
{
	if (!model) {
		return;
	}

#ifndef IWORLD_SERVER_BUILD	
	if (m_Entity) {
		m_Entity->Load(model);
		m_HeadBoneID = model->GetBoneId("Head");
	}

	m_FaceMesh = getModel()->GetModelMeshRenderer("face");
	if (m_FaceMesh) {

		InitFaceTexts(model, m_ResourcePath);
		if (m_FaceMesh->GetSubMeshMaterial(0))
		{
			MaterialInstance* mi = m_FaceMesh->GetSubMeshMaterial(0);
			m_pInitTex = NativeToSharePtr<Texture2D>(static_cast<Texture2D*>(mi->GetTexture(ShaderParamNames::g_DiffuseTex)));
		}
	}

	if (m_bIsShapeShift) {
		show(false, true);
	}
#endif		
}


bool ActorBody::initMonster(const char* modelpath, float modelscale, bool has_avatar, const char* effect, const char* replacetex, bool initModel)
{
	OPTICK_EVENT();
	OPTICK_TAG("async", !initModel);
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif

	Assert(modelpath != nullptr);
	ReleaseMainEntity();
	bool ret = true;
	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
	m_FaceMesh = nullptr;
	m_PlayerIndex = -1;

	m_ModelScale = modelscale;
	m_RealScale = modelscale;
	m_hasAvatar = has_avatar;
	m_ReplaceTex = replacetex;
	m_Effect = effect;
#ifndef IWORLD_SERVER_BUILD	
	Rainbow::SharePtr<Rainbow::Asset> asset;
	asset = Model::LoadModelAssetByPathRule(modelpath, !initModel);

	//LogStringMsg("initMonster Entity[%s]::Create goUID:%d", !initModel ? "async" : "sync", m_Entity->GetGameObject()->GetUID());
	if (!asset) {
		asset = LoadDefaultAsset();
		ret = false;
	}

	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
#endif

#ifndef IWORLD_SERVER_BUILD
	if (modelpath) {
		m_ResourcePath = modelpath;
	}
	//资源的加载路径
	m_ResourcePath = m_ResourcePath.substr(0, m_ResourcePath.find_last_of('/'));
	if (m_BodyType == 3) m_IsPlayerModel = true;

	CleanLoadModelData();

	m_LoadModelData = asset;
	if (!m_LoadModelData) return false;
	m_ModelIsLoading = true;
	AssetLoadHelper<ActorBody>::HandleAssetByState(this, m_LoadModelData.Get(), &ActorBody::OnInitModelResLoadForNormal);
	// 	if (m_LoadModelData->IsLoaded()) {
	// 		this->OnInitModelResLoadForNormal(nullptr);
	// 	}
	// 	else {
	// 		m_LoadModelData->AddEvent<ActorBody>(kResLoadedFinished, &ActorBody::OnInitModelResLoadForNormal, this);
	// 	}
#endif		
	return ret;
}

void ActorBody::initCustomActor(ACTOR_MODEL_TYPE type, std::string modelmark/* ="" */, float modelscale/* =1.0f */, int iType /* = -1 */)
{
	OPTICK_EVENT();
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif

	ReleaseMainEntity();
	CleanLoadModelData();
	m_FaceMesh = nullptr;
	m_PlayerIndex = -1;
	m_HeadBoneID = -1;
	//m_LoadModelData = nullptr;
	m_ModelScale = modelscale;
	m_RealScale = modelscale;


#ifndef IWORLD_SERVER_BUILD
	CustomActorModelData* data = NULL;
	if (modelmark != "")
	{
		if (!CustomModelMgr::GetInstancePtr())
		{

			return;
		}

		if (iType == PREVIEW_MODEL_CLASS)
		{
			data = CustomModelMgr::GetInstancePtr()->findCustomActorModelData(PREVIEW_MODEL_CLASS, modelmark);
		}
		else
		{
			data = CustomModelMgr::GetInstancePtr()->findCustomActorModelData(MAP_MODEL_CLASS, modelmark);
			if (!data)
				data = CustomModelMgr::GetInstancePtr()->findCustomActorModelData(RES_MODEL_CLASS, modelmark);
		}

		if (data)
		{
			type = data->type;
		}
	}
	Rainbow::Model* model = nullptr;
	if (type == HUMAN_MODEL)
		model = LoadModel("entity/custommodel/body1.omod");
	else if (type == QUADRUPED_MODEL)
		model = LoadModel("entity/custommodel/body2.omod");
	else if (type == SINGLE_BONE_MODEL)
		model = LoadModel("entity/custommodel/body3.omod");
	else
		model = LoadModel("entity/custommodel/body1.omod");
	//LogStringMsg("initCustomActor Entity::Create goUID:%d", m_Entity->GetGameObject()->GetUID());
#endif		
	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
#ifndef IWORLD_SERVER_BUILD		
	m_Entity->Load(model);
	UpdateVisiableDistance();
	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
	if (!modelmark.empty() && data)
	{
		CustomModelWaitSyncListMgr::TCustomAvatarModelDataMap* pWaitSyncList = GetCustomModelWaitSyncListMgrPtr()->createEntityWaitMap(m_Entity, false);
		if (model)
			model->SetName(modelmark.c_str());
		if (!data->skindisplay && m_Entity->GetMainModel())
			m_Entity->GetMainModel()->ShowSkins(false);
		auto iter = data->models.begin();
		for (; iter != data->models.end(); iter++)
		{
			std::string boneName = iter->first;

			if (iter->second.size() > 0)
			{
				std::string subModelName = boneName;
				transform(subModelName.begin(), subModelName.end(), subModelName.begin(), ::tolower);

				if (m_Entity->GetMainModel())
					m_Entity->GetMainModel()->ShowSkin(subModelName.c_str(), false);
			}

			std::vector<CustomAvatarModelData> avatarModelDatas;
			avatarModelDatas.clear();
			for (size_t i = 0; i < iter->second.size(); i++)
			{
				Rainbow::Model* pSubModel = nullptr;
				if (CustomModelMgr::GetInstancePtr()->isAvatarCMItemModelInCache(iter->second[i].modelfilename, PROJECTILE_ACTOR_MESH))
				{
					pSubModel = CustomModelMgr::GetInstancePtr()->getAvatarModel(iter->second[i].modelfilename, PROJECTILE_ACTOR_MESH);
				}
				else
				{
					// 异步加载
					CustomModelMgr::GetInstancePtr()->loadAvatarCMItemModelToCache(iter->second[i].modelfilename, PROJECTILE_ACTOR_MESH);
				}

				if (!pSubModel)
				{
					avatarModelDatas.push_back(iter->second[i]);
					continue;
				}

				pSubModel->SetPosition(Rainbow::Vector3f(iter->second[i].offset_x, iter->second[i].offset_y, iter->second[i].offset_z));
				pSubModel->SetRotation(iter->second[i].newrotatemode, iter->second[i].yaw, iter->second[i].pitch, iter->second[i].roll);
				pSubModel->SetScale(iter->second[i].scale);
				m_Entity->BindCunstomObject(boneName.c_str(), pSubModel);

			}
			if (avatarModelDatas.size() > 0 && pWaitSyncList)
			{
				(*pWaitSyncList)[boneName] = avatarModelDatas;
			}
		}
		//if (data->models.size())
		//	m_Entity->packBindCunstomObjs();
		if (data->models.size() && (!pWaitSyncList || pWaitSyncList->size() == 0))
		{
			GenCustomModelManager::GetInstance().bindCusntomObjs(m_Entity);
		}
	}
	if (model)
	{
		m_HeadBoneID = model->GetBoneId("Head");
	}
#endif
	setCurAnim(0, 0);
#ifndef IWORLD_SERVER_BUILD
	if (m_Entity) {
		m_Entity->SetScale(Vector3f(modelscale, modelscale, modelscale));
	}
#endif		
}

void ActorBody::initFullyCustomActor(int type, FullyCustomModel* custommodel/* =NULL */, std::string skey/* ="" */, bool isedit/* =false */, float modelscale /* = 1.0f */)
{
	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD	
	bool packBind = custommodel ? false : true;

	if (!custommodel && !skey.empty())
	{
		if (!FullyCustomModelMgr::GetInstancePtr())
			return;

		if (type == -1)
		{
			custommodel = FullyCustomModelMgr::GetInstancePtr()->findFullyCustomModel(MAP_MODEL_CLASS, skey, true);
			if (!custommodel)
				custommodel = FullyCustomModelMgr::GetInstancePtr()->findFullyCustomModel(RES_MODEL_CLASS, skey, true);
		}
		else
			custommodel = FullyCustomModelMgr::GetInstancePtr()->findFullyCustomModel(type, skey, true);

		if (!custommodel && g_pPlayerCtrl && g_pPlayerCtrl->getWorld() && g_pPlayerCtrl->getWorld()->isRemoteMode())
		{	//请求下载相对应的自定义模型文件
			FullyCustomModelMgr::GetInstancePtr()->addDownload(skey);
		}
	}

#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif
#endif
	ReleaseMainEntity();
	CleanLoadModelData();
	m_FaceMesh = nullptr;
	m_PlayerIndex = -1;
	m_HeadBoneID = -1;
	//m_LoadModelData = nullptr;
	m_ModelScale = modelscale;
	m_RealScale = modelscale;

	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
	//LogStringMsg("initFullyCustomActor Entity::Create goUID:%d", m_Entity->GetGameObject()->GetUID());
#ifndef IWORLD_SERVER_BUILD
	Rainbow::Model* model = nullptr;
	if (!custommodel)  //找不到自定义模型时用默认模型
	{
		model = LoadModel("entity/custommodel/body1");
		//m_Model = GenCustomModelManager::getSingleton().genCustomModelByPath("./res/entity/player/player12/avt_jump2013.fbx");
		//m_Model->resetTexture("./res/entity/player/player12/face.png");
		m_Entity->Load(model);
	}
	else
	{
		SharePtr<ModelData> modeldata = MakeSharePtr<ModelData>();
		//把所有的动作都加进去
		modeldata->addCustomSequenceID(seqType2ID(SEQ_STAND), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_WALK), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_LAYDOWN), isedit ? ANIE_MODE_EDIT : ANIM_MODE_ONCE_STOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_SITCHAIR), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_SWIM), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_ATTACK), isedit ? ANIE_MODE_EDIT : ANIM_MODE_ONCE);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_DIE), isedit ? ANIE_MODE_EDIT : ANIM_MODE_ONCE_STOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_BEHIT), isedit ? ANIE_MODE_EDIT : ANIM_MODE_ONCE);

		modeldata->addCustomSequenceID(seqType2ID(SEQ_SAYHELLO), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_THINK), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_CAY), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_ANGRY), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_GET_UP), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_MOBCONCEAL), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_THANKS), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_SHOWTIME), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_MOBHOWL), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(seqType2ID(SEQ_POSE), isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);
		modeldata->addCustomSequenceID(0, isedit ? ANIE_MODE_EDIT : ANIM_MODE_LOOP);

		Rainbow::Model* model = Rainbow::Model::CreateInstanceLegacy(modeldata, true);
		model->SetCustomPrepareFinish(false);

		m_Entity->Load(model);
		//char name[256];
		//sprintf(name, "%d_%s", type, skey.c_str());
		//m_Model->setName(name);
		custommodel->setModelData(m_Entity, modeldata, NULL, m_World ? true : false, packBind);

		model->SetCustomPrepareFinish(true);
		model->CalculateBoundFromSkinMesh();
		m_Entity->UpdateBindFather();
	}
#endif		
	UpdateVisiableDistance();
	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
	if (!skey.empty())
		setCurAnim(0, 0);

	if (m_Entity) {
		m_Entity->SetScale(Vector3f(modelscale, modelscale, modelscale));
	}

}

void ActorBody::initExampleFCMActor(std::string skey)
{
	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD		
	char path[256] = { 0 };
	sprintf(path, "entity/custommodel/fcmexample/%s.fcm", skey.c_str());

	FullyCustomModel* custommodel = ENG_NEW_LABEL(FullyCustomModel, kMemTempAlloc)();

	//custommodel->setEditing(true); //just for debug!

	custommodel->load(path, skey, -999);

#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif

	ReleaseMainEntity();

	m_FaceMesh = nullptr;
	m_PlayerIndex = -1;
	m_HeadBoneID = -1;
	//m_LoadModelData = nullptr;

	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
	LogStringMsg("initExampleFCMActor Entity::Create goUID:%d", m_Entity->GetGameObject()->GetUID());
	SharePtr<ModelData> modeldata = MakeSharePtr<ModelData>();
	//把所有的动作都加进去
	modeldata->addCustomSequenceID(seqType2ID(SEQ_STAND), ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(seqType2ID(SEQ_WALK), ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(seqType2ID(SEQ_LAYDOWN), ANIM_MODE_ONCE_STOP);
	modeldata->addCustomSequenceID(seqType2ID(SEQ_SITCHAIR), ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(seqType2ID(SEQ_SWIM), ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(seqType2ID(SEQ_ATTACK), ANIM_MODE_ONCE);
	modeldata->addCustomSequenceID(seqType2ID(SEQ_DIE), ANIM_MODE_ONCE_STOP);
	modeldata->addCustomSequenceID(seqType2ID(SEQ_BEHIT), ANIM_MODE_ONCE);
	modeldata->addCustomSequenceID(0, ANIM_MODE_LOOP);
	Rainbow::Model* model = Rainbow::Model::CreateInstanceLegacy(modeldata, true);
	model->SetCustomPrepareFinish(false);

	m_Entity->Load(model);
	UpdateVisiableDistance();
	custommodel->setModelData(m_Entity, modeldata, NULL, m_World ? true : false, false);

	model->SetCustomPrepareFinish(true);
	m_Entity->UpdateBindFather();

	ENG_DELETE_LABEL(custommodel, kMemTempAlloc);
	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
#endif		
}


void ActorBody::initEditingPakcingCMActor()
{
	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD		
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY

#else
	detachUIModelView(NULL, 0);
#endif

	ReleaseMainEntity();

	m_FaceMesh = nullptr;
	m_PlayerIndex = -1;
	m_HeadBoneID = -1;
	//m_LoadModelData = nullptr;

	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
	LogStringMsg("initEditingPakcingCMActor Entity::Create goUID:%d", m_Entity->GetGameObject()->GetUID());

	SharePtr< ModelData> modeldata = MakeSharePtr<ModelData>();
	modeldata->addCustomSequenceID(0, ANIM_MODE_LOOP);

	Rainbow::Model* model = Rainbow::Model::CreateInstanceLegacy(modeldata, true);

	m_Entity->Load(model);
	UpdateVisiableDistance();
	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
#endif		
}

void ActorBody::initTimeLinePlayer(Rainbow::GameObject* model)
{
	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD
	Vector3f modelposition = model->GetTransform()->GetWorldPosition();
	model->GetTransform()->SetParent(NULL);
	Model* realmodel = Model::CreateInstanceFromGameObject(model);
	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);

	if (realmodel != nullptr && m_Entity != nullptr)
	{
		if (model && model->GetTransform() && model->GetTransform()->GetScene())
		{
			model->GetTransform()->GetScene()->AddGameObject(m_Entity->GetGameObject());
		}

		m_Entity->Load(realmodel);
		m_HeadBoneID = realmodel->GetBoneId("Head");
	}

	if (m_Entity)	m_Entity->SetScale(Vector3f(m_RealScale, m_RealScale, m_RealScale));
	if (m_Entity)	m_Entity->SetPosition(modelposition);
#endif
}




void ActorBody::initCustomModel(std::string modelmark, float modelscale/* =1.0f */, int iType /* = -1 */)
{
	OPTICK_EVENT();
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif

	ReleaseMainEntity();

	m_FaceMesh = nullptr;
	m_PlayerIndex = -1;
	m_HeadBoneID = -1;
	CleanLoadModelData();
	m_ModelScale = modelscale;
	m_RealScale = modelscale;
	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
#ifndef IWORLD_SERVER_BUILD
	LogStringMsg("initCustomModel Entity::Create goUID:%d", m_Entity->GetGameObject()->GetUID());
	CustomModel* modeldata = NULL;
	if (iType == PREVIEW_MODEL_CLASS)
	{
		modeldata = CustomModelMgr::GetInstancePtr()->getCustomModel(PREVIEW_MODEL_CLASS, modelmark);
	}
	else
	{
		modeldata = CustomModelMgr::GetInstancePtr()->getCustomModel(RES_MODEL_CLASS, modelmark);
		if (!modeldata)
		{
			modeldata = CustomModelMgr::GetInstancePtr()->getCustomModel(MAP_MODEL_CLASS, modelmark);
		}
	}

	Rainbow::Model* model = nullptr;
	if (modeldata)
	{
		model = modeldata->getItemModel(EDIT_BLOCK_MESH);
	}
	else
	{
		model = LoadModel("entity/custommodel/body1.omod"); // ?????????????????????
	}

	if (m_Entity && model)
	{
		m_Entity->Load(model);
		UpdateVisiableDistance();
	}
	if (m_Entity) {
		m_Entity->SetScale(Rainbow::Vector3f(modelscale, modelscale, modelscale));
	}
	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
#endif		
}

bool ActorBody::initOffcialModel(std::string modelmark, float modelscale /* = 1.0f */, int iType /* = -1 */)
{
	OPTICK_EVENT();
	//这个函数的实现估计还有问题，看是什么地方用的，到时候再修复 by chensongbo
	if (!ImportCustomModelMgr::GetInstancePtr())
		return false;
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif

	ReleaseMainEntity();

	m_FaceMesh = nullptr;
	m_HeadBoneID = -1;
	CleanLoadModelData();
	m_ModelScale = modelscale;
	m_RealScale = modelscale;

	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
#ifndef IWORLD_SERVER_BUILD
	LogStringMsg("initOffcialModel Entity::Create goUID:%d", m_Entity->GetGameObject()->GetUID());
	m_BodyType = 99;

	m_iImportModelType = iType;


	if (m_Entity) {
		m_Entity->SetScale(Vector3f(modelscale, modelscale, modelscale));
	}
#endif

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	if (player != NULL)
	{
		m_OwnerPlayer = player;
	}
#ifndef IWORLD_SERVER_BUILD
	if (m_BodyType == 99)  //导入模型
	{
		Rainbow::Model* model = NULL;
		if (ImportCustomModelMgr::GetInstancePtr())
			model = ImportCustomModelMgr::GetInstancePtr()->getImportModel(modelmark.c_str(), nullptr, m_iImportModelType);

		m_BodyType = 0;
		m_iImportModelType = 0;

		if (!model)
			return false;

		model->SetInstanceAmbient(ColourValue(0.2f, 0.2f, 0.2f));
		m_Entity->Load(model);
		UpdateVisiableDistance();
		if (m_AttachScene) {
			m_AttachScene->AddGameObject(m_Entity->GetGameObject());
		}
	}
#endif		
	return true;
}

void ActorBody::initModelWithModelComp(Rainbow::Entity* entity, float modelscale)
{

#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif

	ReleaseMainEntity();
	m_FaceMesh = nullptr;
	m_PlayerIndex = -1;
	m_HeadBoneID = -1;
	CleanLoadModelData();
	m_ModelScale = modelscale;
	m_RealScale = modelscale;
#ifndef IWORLD_SERVER_BUILD
	m_Entity = entity;

	if (m_Entity) {
		UpdateVisiableDistance();
		m_Entity->SetScale(Rainbow::Vector3f(modelscale, modelscale, modelscale));
	}
	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
#endif	
}

void ActorBody::initUgcModel(std::string modelmark, UgcAssetType type, bool isTemp, float modelscale)
{
	OPTICK_EVENT();
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif

	ReleaseMainEntity();
	m_FaceMesh = nullptr;
	m_HeadBoneID = -1;
	CleanLoadModelData();
	m_ModelIsLoading = false;
	m_ModelScale = modelscale;
	m_RealScale = modelscale;

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	if (player != NULL)
	{
		m_OwnerPlayer = player;
	}

#ifndef IWORLD_SERVER_BUILD

	//LogStringMsg("initUgcModel Entity::Create goUID:%d", m_Entity->GetGameObject()->GetUID());
	m_BodyType = 99;
	if (isTemp)
	{
		m_Entity = Entity::Create();
		m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
		UgcAssetMgr::GetInstance().LoadTempModel(Rainbow::PPtr<Rainbow::Entity>(m_Entity), modelmark, type);
	}
	else
	{
		UGCEntity* ugcEntity = UGCEntity::Create();
		m_Entity = ugcEntity;
		m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
		m_ModelIsLoading = true;
		ugcEntity->LoadModelAsync(modelmark, type, [this](bool success, UGCModelLoader* modelLoader)->void
			{
				if (success && m_Entity && modelLoader)
				{
					Rainbow::Model* model = modelLoader->GetModel();
					if (model)
					{
						m_Entity->Load(model);
						model->SetInstanceAmbient(ColourValue(0.2f, 0.2f, 0.2f));
						if (m_OwnerActor)
						{
							if (m_World && m_World->isRemoteMode())
							{
								m_OwnerActor->ResetActorCollider();
							}
							else
							{
								// 主机这里没有调用 ResetActorCollider了， 因为会重置物理盒子，把用户设置的一些盒子大小信息重置掉
								// lua 中 model 组件会发异步加载完成的通知给物理组件，由物理组件判断要不要重置物理盒子
								m_OwnerActor->ResetHitBox();
							}
						}
						m_ModelIsLoading = false;
					}
				}
			});
	}
	m_Entity->SetScale(Vector3f(modelscale, modelscale, modelscale));
	UpdateVisiableDistance();
	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
#endif
}

void ActorBody::initBlockModel(int blockid, float modelscale, bool isInitBlockItemData)
{
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif
	CleanLoadModelData();
	ReleaseMainEntity();
	m_FaceMesh = NULL;
#ifndef IWORLD_SERVER_BUILD		
	BaseItemMesh* blockitem = ClientItem::createItemModel(blockid, ITEM_MODELDISP_CAMERA);
	Vector3f scl(modelscale, modelscale, modelscale);

	if (blockitem)
		blockitem->SetScale(scl);

	if (isInitBlockItemData && blockitem && blockitem->IsKindOf<BlockMesh>())
	{
		BlockMesh* blockMesh = static_cast<BlockMesh*>(blockitem);
		blockMesh->SetItemInstanceData(Rainbow::Vector4f(1.0f, 0.0f, 0.0f, 0.0f));
		blockMesh->SetCanBeEffectedByFog(true);
	}
	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
	LogStringMsg("initBlockModel Entity::Create goUID:%d", m_Entity->GetGameObject()->GetUID());

	if (blockitem)
	{
		m_Entity->BindObject(101, blockitem);
		blockitem->GetTransform()->SetLocalPosition(Vector3f(0, BLOCK_SIZE / 2, 0));
	}

	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
	UpdateVisiableDistance();
#endif
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	if (player != NULL)
	{
		m_OwnerPlayer = player;
	}
}

void ActorBody::initBaseModel(int iType, float modelscale)
{
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif

	ReleaseMainEntity();
	m_FaceMesh = nullptr;
	m_PlayerIndex = -1;
	m_HeadBoneID = -1;
	CleanLoadModelData();
	m_ModelScale = modelscale;
	m_RealScale = modelscale;
#ifndef IWORLD_SERVER_BUILD
	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);
	//LogStringMsg("SetModel Entity::Create goUID:%d", m_Entity->GetGameObject()->GetUID());

	Rainbow::Model* model = SceneEditorMeshGen::GetInstancePtr()->CreateModelByType(iType);
	if (m_Entity && model)
	{
		m_Entity->Load(model);
		UpdateVisiableDistance();
	}
	if (m_Entity) {
		m_Entity->SetScale(Rainbow::Vector3f(modelscale, modelscale, modelscale));
	}
	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
#endif	
}

void ActorBody::initWildmanMob()
{
	OPTICK_EVENT();
	if (m_Entity == nullptr) {
		return;
	}
	if (m_OwnerActor && m_Entity->GetMainModel())
	{
		if (m_hairSkinName != "")
			setHairColor(m_hairSkinName.c_str(), m_hairColor, m_hairId);

		if (m_faceId > 0)
			setFaceModel(m_faceId);
	}
}

bool ActorBody::initItem(const char* zipPath, const char* omodPath, const char* resPath)
{
	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD		
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
	detachUIModelView(NULL, 0);
#endif

	Rainbow::Model* pMode = nullptr;
	SharePtr<Texture2D> tex = nullptr;

	Rainbow::SharePtr<ModelData> modelData = PkgUtils::ZipFileResLoad<ModelData>(zipPath, omodPath);
	if (modelData)
	{
		pMode = Rainbow::Model::CreateInstanceLegacy(modelData);
		tex = PkgUtils::ZipFileResLoad<Texture2D>(zipPath, resPath);
	}

	if (!pMode || !tex) return false;

	ReleaseMainEntity();
	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);

	if (pMode)
	{
		m_Entity->Load(pMode);
		UpdateVisiableDistance();
		m_HeadBoneID = pMode->GetBoneId("Head");

		pMode->SetTexture(ShaderParamNames::g_DiffuseTex, tex);
	}

	setCurAnim(0, 0);
#endif
	return true;
}

bool ActorBody::initAvatar(int avatarmodel, int index)
{
	OPTICK_EVENT();
	core::string modelPath = "entity/player/avatar/body.prefab";
	Rainbow::Model* pModel = LoadModel(modelPath.c_str(), NULL);

	if (!pModel) return false;

	ReleaseMainEntity();
	m_Entity = Entity::Create();
	m_Entity->GetGameObject()->AddEvent(Evt_ComponentRemoved, &ActorBody::OnComponentRemoved, this);

	if (pModel)
	{
		m_Entity->Load(pModel);
		UpdateVisiableDistance();
		m_HeadBoneID = pModel->GetBoneId("Head");
		// 换装展示模型
		addAvatarPartModel(avatarmodel, index, true);
	}

	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}

	return true;
}

void ActorBody::DoInitModelResLoadForPlayer(Rainbow::Model* model)
{
	if (!model)return;
	if (!m_Entity)return;
#ifndef IWORLD_SERVER_BUILD
	int mutate_mob = m_MutateMob;
	int skinid = (m_PlayerIndex > 0) ? getSkinID() : 0;
	//对于皮肤和野兽状态
	if (mutate_mob > 0 || skinid > 0)
	{
		const RoleSkinDef* skindef = GetDefManagerProxy()->getRoleSkinDef(skinid);
		if (skindef == NULL) setPlayerIndex(ComposePlayerIndex(getModelID(), getGeniusLv(), 0));

		if (mutate_mob > 0)
		{

		}
		else if (skindef)
		{
			if (skindef->TextureID > 0)
			{
				char path[256];
				sprintf(path, "entity/%d/male%d.png", skindef->Model, skindef->TextureID);
				SharePtr<Texture2D> tex = GetAssetManager().LoadAssetAsync<Texture2D>(path);
				if (tex)
				{
					model->SetTexture(ShaderParamNames::g_DiffuseTex, tex);
				}
			}
			//皮肤也可以缓存部件进行加载了
			if (m_AvatarComponent)
			{
				m_AvatarComponent->UpdateCacheAvatarParts();
			}
			/*if (skindef->ChangeType == 1|| skindef->ChangeType == 3)
			{
				m_Model->ShowSkin("body3", false);
			}*/
		}
	}
	m_vCustomAnimMap.clear();

	if (model && m_Entity)
	{
		if (m_Entity->GetMainModel() && !m_vLoadedAnimSeqs.empty())
		{
			//如果将旧模型干掉，之前缓存的动态加载的动作也得清掉
			m_vLoadedAnimSeqs.clear();
		}
		m_Entity->Load(model);
		m_HeadBoneID = model->GetBoneId("Head");

		// 初始化抬头点头的变量
		IModelAnimationPlayer* animPlayer = model->GetModelAnimationPlayer();
		if (animPlayer)
		{
			m_NeedUpdateLookUp = !m_IsInUI && m_NeedUpdateLookUp && animPlayer->CanRotateHead();
		}
	}
	if (m_CurAnim[0] <= 0)
	{
		setCurAnim(0, 0);
	}
	else
	{
		int anim = m_CurAnim[0];
		setCurAnim(0, 0);
		setCurAnim(anim, 0);
	}

	if (model != nullptr)
	{
		m_FaceMesh = model->GetModelMeshRenderer(MESH_FACE_NAME);
		if (m_FaceMesh != nullptr)
		{
			InitFaceTexts(model, m_ResourcePath);

			if (m_FaceMesh->GetSubMeshMaterial(0))
			{
				MaterialInstance* mi = m_FaceMesh->GetSubMeshMaterial(0);
				if (mi)
				{
					m_pInitTex = NativeToSharePtr<Texture2D>(static_cast<Texture2D*>(mi->GetTexture(ShaderParamNames::g_DiffuseTex)));
					LogStringMsg("Resounce Path:%s,InitTex:%s", m_ResourcePath.c_str(), m_pInitTex ? m_pInitTex->GetResPath().c_str() : "");
				}
			}
		}

		if (IsUIActorBody())
		{
			IModelAnimationPlayer* animPlayer = model->GetModelAnimationPlayer();
			if (animPlayer)
			{
				animPlayer->SetAnimatorSetCullingMode(0);
			}
		}
	}

	if (m_MutateMob == 0 && m_hasShearedColor)
	{
		applyBodyColor(m_BodyColor, m_Sheared);
		m_hasShearedColor = false;
	}

	if (m_Entity)	m_Entity->SetScale(Vector3f(m_RealScale, m_RealScale, m_RealScale));

	if (m_OwnerPlayer && !m_IsInUI)
		setEquipItem(EQUIP_WEAPON, m_OwnerPlayer->getCurToolID());


	if (m_bupdateSkin)
	{
		checkMeshToShow();
		showSaddle(m_ShowSaddle);
		showNecklace(m_ShowNecklace);
		replaceStoreAndFaceTex();
		showRake(m_ShowRake);
		setNeedUpdateSkin(false);
	}
	if (m_OwnerPlayer && m_OwnerPlayer->isSittingInStarStationCabin())
		setIsInStarStationCabin(true, false);

	if (getIsNeedLoadCusAnim())
	{
		loadCusAnim();
	}
	UpdateVisiableDistance();

	if (model && m_bIsShapeShift)
		show(false, true);

#ifdef VERSION_MINICODE
	if (m_bNeedClearEquipMiniCode)
	{
		clearEquipItems(model);
	}
	m_bNeedClearEquipMiniCode = false;
#endif
	checkSpectatorBody();
	InitUIActorbodyModel();
#endif
}

#ifdef VERSION_MINICODE
bool ActorBody::LoadPlayModelMiniCode(const char* modelPath, const char* animpath)
{
	Rainbow::Model* pModel = nullptr;

	if (m_LoadModelData)
	{
		//删除事件，有则删除，无也没关系
		m_LoadModelData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitModelResLoadForPlayer, this);
		m_LoadModelData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitModelResLoadForNormal, this);
	}


	m_LoadModelData = Model::LoadModelAssetByPathRule(modelPath, true); //GetAssetManager().LoadAssetAsync<Rainbow::Prefab>(modelPath.c_str());
	// animpath 都是打在本地的。
	m_LoadAnimationDataMiniCode = GetAssetManager().LoadAsset<AnimationData>(animpath);
	if (m_LoadModelData)
	{
		if (m_LoadModelData->IsLoaded())
		{
			this->OnInitModelResLoadForPlayer(nullptr);
		}
		else
		{
			m_LoadModelData->AddEvent(kResLoadedFinished, &ActorBody::OnInitModelResLoadForPlayer, this);
			pModel = LoadDefaultModel();

			if (pModel == NULL)
				return false;
			else
				loadModel(pModel);
		}
	}

	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
	clearEquipItems(pModel);
	UpdateVisiableDistance();
	setCurAnim(0, 0);

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	if (player != NULL)
	{
		m_OwnerPlayer = player;
	}
	checkSpectatorBody();
	return true;
}
#endif

void ActorBody::OnInitModelResLoadForPlayer(const Rainbow::EventContent* data)
{
	if (m_LoadModelData)
	{
		m_LoadModelData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitModelResLoadForPlayer, this);
		m_LoadModelData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitModelResLoadForNormal, this);
	}

	if (!GetActorBodySafeHandle()->IsVaild(this) || !m_ModelIsLoading) return;

	SharePtr<Rainbow::Asset> pres;
	if (m_LoadModelData)
	{
		pres = m_LoadModelData;
		m_LoadModelData = nullptr;
#ifdef VERSION_MINICODE
		if (m_LoadAnimationDataMiniCode)
		{
			if (pres->IsKindOf<Rainbow::ModelData>())
			{
				SharePtr<ModelData> modelDataRes = pres.CastTo<Rainbow::ModelData>();
				modelDataRes->AddAnimation(m_LoadAnimationDataMiniCode);
			}
			m_LoadAnimationDataMiniCode = nullptr;
		}
#endif
	}
	else
	{
		pres = LoadDefaultAsset();
	}

	if (!pres) return;


	Rainbow::Model* model = Rainbow::Model::CreateInstanceFromAsset(pres);

	if (model != nullptr)
	{
		DoInitModelResLoadForPlayer(model);
	}

	m_bIsRebuildModel = true;
	m_ModelIsLoading = false;
}

Rainbow::Model* ActorBody::createRoleModelInstance(ActorBody::ModelAssetData& modelData)
{
	auto asset = modelData.m_ModelAsset;
	auto animAsset = modelData.m_AnimAsset;
	auto panim = modelData.m_AnimData;
	Model* pModel = nullptr;

	if (asset->IsKindOf<Prefab>())
	{
		ModelNew* model = static_cast<ModelNew*>(Model::CreateInstanceFromPrefab(asset.CastTo<Prefab>(), false, true));
		if (model)
		{
			if (animAsset)
			{
				if (animAsset->IsKindOf<Prefab>())
				{
					SharePtr<SkinAnimContainer> panim = GetShareObjectManager().LoadShareObject((animAsset.CastTo<Prefab>())->GetPrefabGUID()).CastTo<SkinAnimContainer>();
					model->AddAnimationClips(panim);
				}
				else if (animAsset->IsKindOf<AnimationData>())
				{
					SharePtr<AnimationData> panim = animAsset.CastTo<AnimationData>();
					if (panim->IsVaild())
					{
						std::vector<ModelAnimData> anims;
						bool flag = true;
						for (size_t i = 0; i < anims.size(); i++)
						{
							if (anims[i].anim == panim) {
								flag = false;
								break;
							}
						}
						if (flag)
						{
							ModelAnimData animdata;
							animdata.nbonetrack = panim->m_BoneTracks.size();
							animdata.boneids = new short[animdata.nbonetrack];
							animdata.anim = panim;
							animdata.isNeedRemove = false;
							anims.push_back(animdata);

							SharePtr<SkinAnimContainer> conatainer = MakeSharePtr<SkinAnimContainer>();
							conatainer->AddSkeletonAnimationClips(anims);
							model->AddAnimationClips(conatainer);
						}
					}
					else
					{
						assert(false);
					}
				}
			}

			pModel = model;
		}
	}
	else if (asset->IsKindOf<ModelData>())
	{
		SharePtr<ModelData> modelData = asset.CastTo<ModelData>();
		if (panim)
		{
			modelData->AddAnimation(panim);
		}

		pModel = Model::CreateInstanceLegacy(modelData);
	}

	return pModel;
}

void ActorBody::OnRoleModelCreated(Model* pModel)
{
	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}
	clearEquipItems(pModel);

	UpdateVisiableDistance();
	setCurAnim(0, 0);

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	if (player != NULL)
	{
		m_OwnerPlayer = player;
	}
	checkSpectatorBody();

	m_OldModelData.m_ModelAsset = nullptr;
	m_OldModelData.m_AnimAsset = nullptr;
	m_OldModelData.m_AnimData = nullptr;
}

void ActorBody::OnInitOldRoleModelResLoadForPlayer(const Rainbow::EventContent* data)
{
	if (!m_OldModelData.m_ModelAsset.IsValid()) {
		return;
	}

	if (!m_OldModelData.m_ModelAsset->IsLoaded()) {
		return;
	}

	if (m_OldModelData.m_AnimAsset.IsValid() && !m_OldModelData.m_AnimAsset->IsLoaded()) {
		return;
	}
	if (m_OldModelData.m_AnimData.IsValid() && !m_OldModelData.m_AnimData->IsLoaded()) {
		return;
	}

	{
		Model* pModel = createRoleModelInstance(m_OldModelData);

		if (pModel)
		{
			loadModel(pModel);
		}

		OnRoleModelCreated(pModel);
	}
}


void ActorBody::OnInitCustomskinsModelResLoadForPlayer(const Rainbow::EventContent* data)
{
	if (!m_OldModelData.m_ModelAsset.IsValid()) {
		return;
	}

	if (!m_OldModelData.m_ModelAsset->IsLoaded())
		return;

	if (m_OldModelData.m_AnimAsset.IsValid() && !m_OldModelData.m_AnimAsset->IsLoaded())
		return;

	if (m_OldModelData.m_AnimData.IsValid() && !m_OldModelData.m_AnimData->IsLoaded())
		return;

	{
		Model* pModel = createRoleModelInstance(m_OldModelData);

		if (pModel)
		{
			loadModel(pModel);
		}

		addDefaultAvatar();
		OnRoleModelCreated(pModel);
	}
}


void ActorBody::InitUIActorbodyModel()
{
	if (m_Entity)
	{
		ActorBodySpecialComp* specialComp = m_Entity->GetComponent<ActorBodySpecialComp>();
		if (specialComp)
		{
			specialComp->InitModel();
		}
	}
}
void ActorBody::DoInitModelForNormal(Rainbow::Model* model)
{
	if (!model)return;
	if (!m_Entity)return;

	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD	
	model->ShowSkin("part1", false);
	model->ShowSkin("part2", false);

	//m_Model->ShowSkin("zhuye" , false);
	//m_Model->ShowSkin("body01" , false);

	model->SetInstanceAmbient(ColourValue(0.2f, 0.2f, 0.2f));
	if (m_hasAvatar)
	{
		clearEquipItems(model);
		if (m_OwnerActor)
		{
			LivingAttrib* attr = dynamic_cast<LivingAttrib*>(m_OwnerActor->getAttrib());
			if (attr) attr->applyEquips(this);
		}
	}

	if (m_Entity) {
		m_Entity->Load(model);

		// set local custom bounds from m_LocoMotion
		this->UpdateLocalCustomBounds();
		UpdateVisiableDistance();
	}

	checkMeshToShow();//?????????m_Entity??model ??????????m_Entity->Load(model)???

	replaceStoreAndFaceTex();
	showSaddle(m_ShowSaddle);
	showNecklace(m_ShowNecklace);
	showRake(m_ShowRake);
	setCurAnim(0, 0);
	if (m_CurAnim[0] >= 0) m_CurSeqID[0] = playAnim(m_CurAnim[0]);
	if (m_CurAnim[1] >= 0) m_CurSeqID[1] = playAnim(m_CurAnim[1]);

	if (model != nullptr)
	{
		if (IsUIActorBody())
		{
			IModelAnimationPlayer* animPlayer = model->GetModelAnimationPlayer();
			if (animPlayer)
			{
				animPlayer->SetAnimatorSetCullingMode(0);
			}
		}
	}

	if (m_Effect.IsVaild() && m_Entity != nullptr)
	{
		const char* peffect = (const char*)m_Effect;
		if (peffect[0])
			m_Entity->PlayMotion(peffect, true, 0);
	}

	m_HeadBoneID = model->GetBoneId("Head");

	// 初始化抬头点头的变量
	if (model != nullptr)
	{
		IModelAnimationPlayer* animPlayer = model->GetModelAnimationPlayer();
		if (animPlayer)
		{
			m_NeedUpdateLookUp = !m_IsInUI && m_NeedUpdateLookUp && animPlayer->CanRotateHead();
		}
	}

	if (m_hasShearedColor)
	{
		applyBodyColor(m_BodyColor, m_Sheared);
		m_hasShearedColor = false;
	}

	if (m_Entity) {
		m_Entity->SetScale(Vector3f(m_RealScale, m_RealScale, m_RealScale));
	}

	initWildmanMob();

	if (m_OwnerPlayer && m_OwnerPlayer->isSittingInStarStationCabin())
		setIsInStarStationCabin(true, false);

	if (getIsNeedLoadCusAnim())
	{
		loadCusAnim();
	}
	if (model && m_bIsShapeShift)
		show(false, true);

	if (m_AvatarComponent)
	{
		m_AvatarComponent->UpdateCacheAvatarParts();
	}
	InitUIActorbodyModel();

	if (m_OwnerActor)
	{
		m_OwnerActor->UpdateXrayEffectEnable();
	}
#endif
}

void ActorBody::OnInitModelResLoadForNormal(const Rainbow::EventContent* data)
{
	OPTICK_EVENT();
	if (m_LoadModelData) {
		m_LoadModelData->RemoveEvent<ActorBody>(kResLoadedFinished, &ActorBody::OnInitModelResLoadForNormal, this);
		m_LoadModelData->RemoveEvent<ActorBody>(kResLoadedFinished, &ActorBody::OnInitModelResLoadForPlayer, this);
	}

	if (!GetActorBodySafeHandle()->IsVaild(this) || !m_ModelIsLoading) return;

	SharePtr<Rainbow::Asset> pres;
	if (m_LoadModelData)
	{
		pres = m_LoadModelData;
		m_LoadModelData = nullptr;
#ifdef VERSION_MINICODE
		if (m_LoadAnimationDataMiniCode)
		{
			if (pres && pres->IsKindOf<Rainbow::ModelData>())
			{
				SharePtr<ModelData> modelDataRes = pres.CastTo<Rainbow::ModelData>();
				modelDataRes->AddAnimation(m_LoadAnimationDataMiniCode);
			}
			m_LoadAnimationDataMiniCode = nullptr;
		}
#endif
	}
	else
	{
		pres = LoadDefaultAsset();
	}

	if (!pres) return;

	Rainbow::Model* model = Rainbow::Model::CreateInstanceFromAsset(pres);
	if (model != nullptr)
	{
		DoInitModelForNormal(model);
	}

	//怪物这里把装备模型重新刷一遍
	if (model && m_OwnerActor)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(m_OwnerActor);
		if (mob != NULL)
		{
			MobAttrib* attrib = dynamic_cast<MobAttrib*>(mob->getAttrib());
			if (attrib)
			{
				attrib->applyEquips(this, MAX_EQUIP_SLOTS);
			}
		}
	}

	m_ModelIsLoading = false;
}

Rainbow::Model* ActorBody::LoadDefaultModel() {
	auto model = LoadModel(DefaultActorBody.c_str());
	if (model != nullptr) {
		auto data = model->GetModelData();
		if (data) {
			//给默认模型设置一个特殊的缓存时间
			data->SetExpiresTime(60.0f);
		}
	}
	return model;
}
Rainbow::SharePtr<Rainbow::Asset> ActorBody::LoadDefaultAsset() {
	return Model::LoadModelAssetByPathRule(DefaultActorAsset);
}

void ActorBody::SyncModelData()
{
	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD		
	const FixedString modelPath = m_LoadModelData->GetResPath();
	//删除注册事件
	if (m_LoadModelData) {

		m_LoadModelData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitModelResLoadForPlayer, this);
		m_LoadModelData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitModelResLoadForNormal, this);

		//同步加载模型
		if (m_LoadModelData->IsAsync())
		{
			m_LoadModelData = Model::LoadModelAssetByPathRule(modelPath.c_str());
		}
	}

	if (!m_LoadModelData) return; //?????????
	if (m_BodyType == 99)  //???????
	{
		Rainbow::Model* model = ImportCustomModelMgr::GetInstancePtr()->getImportModel(modelPath.c_str(), nullptr, m_iImportModelType);
		m_BodyType = 0;
		m_iImportModelType = 0;
		if (!model)
			return;

		model->SetInstanceAmbient(ColourValue(0.2f, 0.2f, 0.2f));
		m_Entity->Load(model);
		checkSpectatorBody();
		return;
	}

	if (m_PlayerIndex != -1)
	{
		OnInitModelResLoadForPlayer(nullptr);
	}
	else
	{
		OnInitModelResLoadForNormal(nullptr);
	}
#endif
}


#pragma region update&tick

void ActorBody::updateLookAt(float dt)
{
    OPTICK_EVENT();
	if (m_OwnerActor == NULL) return;
	ActorLocoMotion* loc = m_OwnerActor->getLocoMotion();

	if (m_isLookAt)
	{
		if (Rainbow::FltEqual(loc->m_RotationPitch, m_LookTargetPitch) && Rainbow::FltEqual(m_RotationYawHead, m_LookTargetYaw) && !m_bkeeplook)
		{
			m_isLookAt = false;
			m_RotationYawHead = m_RenderYawOffset = m_LookTargetYaw;
		}
		else
		{
			loc->m_RotationPitch = UpdateRotation(loc->m_RotationPitch, m_LookTargetPitch, m_DeltaLookPitch);
			m_RotationYawHead = UpdateRotation(m_RotationYawHead, m_LookTargetYaw, m_DeltaLookYaw);
			if (m_OwnerPlayer && m_OwnerPlayer->isNewMoveSyncSwitchOn())
				m_OwnerPlayer->setMoveControlPitch(loc->m_RotationPitch);
		}
	}
	if (!m_isLookAt)
	{
		m_RotationYawHead = UpdateRotation(m_RotationYawHead, m_RenderYawOffset, 10.0f);
	}


	//NavigationPath* navipath = m_OwnerActor->getNavigator();
	//if (navipath != NULL && !navipath->noPath()) //如果走路, 限制在身体方向的正负75度
	{
		//限制头部旋转不超过相机角度m_HeadRotRange
		float da = WrapAngleTo180(m_RotationYawHead - m_RenderYawOffset);

		if (da < -m_HeadRotRange) m_RotationYawHead = m_RenderYawOffset - m_HeadRotRange;
		else if (da > m_HeadRotRange) m_RotationYawHead = m_RenderYawOffset + m_HeadRotRange;
	}

	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model)
	{
		Quaternionf quat = AngleEulerToQuaternionf(Vector3f(-loc->m_RotationPitch, m_RotationYawHead - m_RenderYawOffset, 0));
		/*Quaternionf quat = AngleEulerToQuaternionf(Vector3f(0,m_RotationYawHead - m_RenderYawOffset, 0));*/
		if (m_OwnerActor)
		{
			auto RidComp = m_OwnerActor->getRiddenComponent();
			if (RidComp && RidComp->isRiding())
			{
				ClientActor* riding = RidComp->getRidingActor();
				if (riding && riding->getDefID() && (riding->getDefID() == BAMBOO_DRAGONFLY1 || riding->getDefID() == BAMBOO_DRAGONFLY2))
				{
					quat.x = 0; //骑乘竹蜻蜓，头只可以左右看
					quat.z = 0;
				}
			}
			if (m_OwnerActor && (m_OwnerActor->getDefID() == 3625 || m_OwnerActor->getDefID() == 3624 || m_OwnerActor->getDefID() == 3623))//海马坐骑限制往上抬头
			{
				quat.x = 0;
				quat.z = 0;
			}
		}
		//float pitch = -loc->m_RotationPitch + 15;
		//pitch = Clamp(pitch, -75.0f, 75.0f);
		//quat.setEulerAngle(m_RotationYawHead - m_RenderYawOffset, pitch, 0);
		{
			bool bInterp = m_bInterpRotation && m_qInterpYawHeadRotation != Quaternionf::identity;
			if (bInterp)
			{
				m_qInterpYawHeadRotation = Lerpto(m_qInterpYawHeadRotation, quat, dt, m_fHeadLerpSpeed);
			}
			else
			{
				m_qInterpYawHeadRotation = quat;
			}

			model->SetBoneRotate(m_HeadBoneID, &m_qInterpYawHeadRotation, m_HeadBoneScale);
		}
	}

	if (m_isLookAt && m_OwnerActor && m_OwnerActor->isSleeping())
	{
		ActorLocoMotion* loc = m_OwnerActor->getLocoMotion();
		if (loc)
		{
			m_RotationYawHead = m_RenderYawOffset = loc->m_RotateYaw;
			m_isLookAt = false;
		}
	}
}

//m_RenderYawOffset用于身体旋转
void ActorBody::updateRenderYawOffset(float dt)
{
	if (m_OwnerActor == NULL) return;
	ActorLocoMotion* loc = m_OwnerActor->getLocoMotion();

	WCoord dpos = loc->m_Position - loc->m_TickPosition.m_LastTickPos;
	//使用灶台的时候，人物固定朝向 code-by: liya
	if (dpos.isZero() && m_OwnerActor == g_pPlayerCtrl && m_CurAnim[0] != SEQ_LAYDOWN && !g_pPlayerCtrl->isUseHearth())
	{
		float range = m_HeadRotRange;
		if (Rainbow::Abs(m_RotationYawHead - m_YawOffsetHelper) > 15.0f)
		{
			m_YawOffsetHelpTicks = 0;
			m_YawOffsetHelper = m_RotationYawHead;
		}
		else
		{
			++m_YawOffsetHelpTicks;

			if (m_YawOffsetHelpTicks > 10)
			{
				range = Rainbow::Max(1.0f - float(m_YawOffsetHelpTicks - 10) / 10.0f, 0.0f) * m_HeadRotRange;
			}
		}

		m_RenderYawOffset = GotoRotation(m_RenderYawOffset, m_RotationYawHead, range);
	}
	else if (m_LerpRotation)
	{
		float ratio = (float)m_LerpRotationStartMarker / m_LerpRotationTick;

		float currentYaw = ratio * (m_TargetYaw - m_OriginRotation) + m_OriginRotation;
		auto loc = m_OwnerActor->getLocoMotion();
		if (loc)
		{
			loc->m_RotateYaw = currentYaw;
		}
		m_RotationYawHead = currentYaw;
		m_RenderYawOffset = currentYaw;
		//m_RotationYawHead = GotoRotation(m_RotationYawHead, m_RenderYawOffset, m_HeadRotRange);
		m_YawOffsetHelpTicks = 0;
		m_LerpRotationStartMarker++;

		if (m_LerpRotationStartMarker > m_LerpRotationTick)
		{
			m_LerpRotationStartMarker = 0;
			m_LerpRotation = false;
		}
	}
	else
	{
		// 自己和非玩家生物保持原有效果
		if ((m_OwnerPlayer && m_OwnerPlayer->hasUIControl()) || !m_OwnerPlayer)
		{
			m_RenderYawOffset = loc->m_RotateYaw;
			m_RotationYawHead = GotoRotation(m_RotationYawHead, m_RenderYawOffset, m_HeadRotRange);
			if (GetRecordPkgManager().isRecordPlaying())
				m_RotationYawHead = m_RenderYawOffset;
			m_YawOffsetHelper = m_RotationYawHead;
			m_YawOffsetHelpTicks = 0;
		}
		// 其他玩家增加头先转动身体跟着转动的效果
		else
		{
			m_RotationYawHead = loc->m_RotateYaw;
			float range = m_HeadRotRange;
			if (Rainbow::Abs(m_RotationYawHead - m_YawOffsetHelper) > 15.0f)
			{
				m_YawOffsetHelpTicks = 0;
				m_YawOffsetHelper = m_RotationYawHead;
			}
			else
			{
				++m_YawOffsetHelpTicks;

				if (m_YawOffsetHelpTicks > 10)
				{
					range = Rainbow::Max(1.0f - float(m_YawOffsetHelpTicks - 10) / 10.0f, 0.0f) * m_HeadRotRange;
				}
			}

			m_RenderYawOffset = GotoRotation(m_RenderYawOffset, m_RotationYawHead, range);
		}
	}

	if (m_LockRenderYawOffset)
	{
		Rainbow::Vector3f dir = (m_LockRenderYawLookTarget - loc->m_Position).toVector3();
		float yaw = 0.0f, pitch = 0.0f;
		Direction2PitchYaw(&yaw, &pitch, dir);
		m_RenderYawOffset = yaw;
	}
}

void ActorBody::tick()
{

	if (m_OwnerActor == NULL)
	{
#ifdef VERSION_MINICODE
		if (m_NeedUpdateAnim)
		{
			int animBody = 0;
			int animUpBody = 0;
			if (m_nAct > 0)
			{
				//20210929 codeby:chenwei 根据接口参数修改，修改逻辑
				if (updateAction(m_nAct))
					animBody = SEQ_PLAY_ACT;
				else {
					m_nAct = -1;
					m_bSideAct = false;
					clearAction(); //2021-10-20 codeby:wangyu 清除互动动作
				}
			}

			setCurAnim(animBody, 0);
			setCurAnim(animUpBody, 1);
		}
#endif
		return;
	}

	ActorLocoMotion* loc = NULL;
	int objType = m_OwnerActor->getObjType();

	if (objType == OBJ_TYPE_GIANT
		|| objType == OBJ_TYPE_DRAGON
		|| objType == OBJ_TYPE_FLYMONSTER
		|| objType == OBJ_TYPE_AQUATICMONSTER
		|| objType == OBJ_TYPE_SKIN_NPC
		|| objType == OBJ_TYPE_VACANTBOSS
		|| objType == OBJ_TYPE_TRIXENIE
		|| m_OwnerActor->canUseActorLocoMotion())
	{
		loc = m_OwnerActor->getLocoMotion();
	}
	else if (objType != OBJ_TYPE_GAMEOBJECT)
	{
		LivingLocoMotion* living = dynamic_cast<LivingLocoMotion*>(m_OwnerActor->getLocoMotion());
		if (living)
		{
			if (living->m_MoveStrafing > 0)
			{
				m_RenderYawOffset = living->m_RotateYaw + 45.0f;
			}
			else if (living->m_MoveStrafing < 0)
			{
				m_RenderYawOffset = living->m_RotateYaw - 45.0f;
			}
			loc = living;
		}
	}

	if (loc == NULL)
	{
		return;
	}

	if (m_HurtTicks > 0)
	{
		if (--m_HurtTicks == 0)
		{
			auto effectComponent = m_OwnerActor->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect(BODYFX_HURT);
			}
		}
	}

	if (m_Entity)
	{
#ifndef DEDICATED_SERVER
		Vector4f lightparam(0, 0, 0, 0);

		WCoord center = loc->getPosition();
		center.y += loc->m_BoundHeight / 2;
		if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(center));
		if (m_PlayerIndex >= 0)
		{
			lightparam.x += 0.2f;
			lightparam.y += 0.2f;
		}
		m_Entity->SetInstanceData(lightparam,false);



		if (m_EquipComponent && m_EquipComponent->m_WeaponModel && m_EquipComponent->m_WeaponModel->IsKindOf<ModelItemMesh>())
		{
			ModelItemMesh* itemmodel = static_cast<ModelItemMesh*>(m_EquipComponent->m_WeaponModel);
			if (itemmodel && itemmodel->getEntity() && itemmodel->getEntity()->GetMainModel())
			{
				itemmodel->getEntity()->SetInstanceData(m_IsInUI ? Vector4f::one : lightparam);
				itemmodel->getEntity()->SetInstanceAmbient(ColourValue::ZERO);
				//itemmodel->getEntity()->GetMainModel()->SetInstanceData(lightparam);
			}
		}

		if (m_EquipComponent && m_EquipComponent->m_WeaponModel && m_EquipComponent->m_WeaponModel->IsKindOf<BlockMesh>())
		{

			BlockMesh* blockmesh = static_cast<BlockMesh*>(m_EquipComponent->m_WeaponModel);
			blockmesh->SetItemInstanceData(m_IsInUI ? Vector4f::one : lightparam);
			//blockmesh->SetUseBlockVertexLight(true);
		}

		if (m_EquipComponent && m_EquipComponent->m_WeaponModel_left && m_EquipComponent->m_WeaponModel_left->IsKindOf<ModelItemMesh>())
		{
			ModelItemMesh* itemmodel = static_cast<ModelItemMesh*>(m_EquipComponent->m_WeaponModel_left);
			if (itemmodel && itemmodel->getEntity() && itemmodel->getEntity()->GetMainModel())
			{
				itemmodel->getEntity()->SetInstanceData(m_IsInUI ? Vector4f::one : lightparam);
				itemmodel->getEntity()->SetInstanceAmbient(ColourValue::ZERO);
				//itemmodel->getEntity()->GetMainModel()->SetInstanceData(lightparam);
			}
		}
		if (m_EquipComponent && m_EquipComponent->m_WeaponModel_left && m_EquipComponent->m_WeaponModel_left->IsKindOf<BlockMesh>())
		{

			BlockMesh* blockmesh = static_cast<BlockMesh*>(m_EquipComponent->m_WeaponModel_left);
			blockmesh->SetItemInstanceData(m_IsInUI ? Vector4f::one : lightparam);
			//blockmesh->SetUseBlockVertexLight(true);
		}

		if (m_EquipComponent)
		{
			for (int slot = 0; slot < MAX_EQUIP_SLOTS; slot++)
			{
				for (int i = 0; i < MAX_EQUIPMENTPART_COUNT; i++)
				{
					if (m_EquipComponent && m_EquipComponent->m_CustomEquipPartModel[slot][i])
					{
						m_EquipComponent->m_CustomEquipPartModel[slot][i]->SetInstanceData(m_IsInUI ? Vector4f::one : lightparam);
					}
				}
			}
		}
#endif


		// 20210924：眩晕buff模型旋转  codeby： keguanqiang
		LivingAttrib* livingAttr = dynamic_cast<LivingAttrib*>(m_OwnerActor->getAttrib());
		if (loc && livingAttr && livingAttr->hasBuff(SWIMMY_BUFF))
		{
			float yaw = loc->m_RotateYaw + 5;
			if (m_Entity && m_Entity->GetMainModel())
			{
				m_Entity->GetMainModel()->SetRotation(yaw, 0, 0);
			}

			loc->m_RotateYaw = yaw;
		}
	}
#if 1
	if (m_OwnerActor->isDead() || m_OwnerActor->isOffLine())
	{
		updatePlayAnim(m_OwnerActor->getRunWalkFactor());
	}
	else if (m_NeedUpdateAnim)
	{
		if (!m_bIsCheckSeqList)
		{
			updatePlayAnim(m_OwnerActor->getRunWalkFactor());
		}
		else if (m_Entity && m_Entity->GetMainModel())
		{
			checkSeqMustPlayAnim();
		}
	}
#else
	if ((m_NeedUpdateAnim || m_OwnerActor->isDead()))
	{
		if (!m_bIsCheckSeqList || m_OwnerActor->isDead())
		{
			updatePlayAnim(m_OwnerActor->getRunWalkFactor());
		}
		else if (m_Entity && m_Entity->GetMainModel())
		{
			checkSeqMustPlayAnim();
		}
	}
#endif
	if (m_NeedUpdateSkinEffect)
	{
		updateSkinEffect();
	}

	//if (m_NeedUpdateBallEffect)
	//{
	//	updateBallEffect();
	//}


	if (m_bIsNeedLoadCusAnim && m_vNeedLoadCusMotionID.size() > 0)
	{
		loadCusAnim();
	}
}

void ActorBody::updateForModelView(float dtime, Rainbow::Vector3f& pos, ClientActor* riding, bool setbindrot, Quaternionf& quat)
{
    OPTICK_EVENT();
	float renderyawoffset = m_RenderYawOffset;
	if (m_BodyType == 3) renderyawoffset += 0;
	if (!m_IsAttachModelView)
	{
		Quaternionf selfquat;
		if (m_ctrlRotate && m_OwnerActor && !m_OwnerActor->getReverse())
		{
			if (m_bShowUp) //道具模型偏移50
			{
				pos.y += 50;
			}

			if (pos.y != MAX_FLOAT) setPosition(pos);
			selfquat = AngleEulerToQuaternionf(Vector3f(0, renderyawoffset, 0));

		}
		//倒立
		else if (m_OwnerActor && m_OwnerActor->getReverse())
		{
			selfquat = AngleEulerToQuaternionf(Vector3f(0, renderyawoffset, 180));
			setPosition(WorldPos::fromVector3(pos + Vector3f(0.0f, (float)m_OwnerActor->getLocoMotion()->m_BoundHeight, 0.0f)));
		}
		else
		{
			if (m_OwnerActor && m_OwnerActor->IsObject())  //同步新actor的loc的旋转和位置给模型
			{
				// 客机，实体父节点如果不被运动器驱动运动时才需要平滑
				bool bNeedSmooth = m_World && m_World->isRemoteMode() && (m_OwnerActor->GetParentWID() == 0) && (!m_OwnerActor->IsControlByScript());
				if (bNeedSmooth)
				{
					pos = m_OwnerActor->getLocoMotion()->m_UpdatePos.toVector3();
					selfquat = m_OwnerActor->getLocoMotion()->m_UpdateRot;
				}
				else
				{
					//selfquat = Lerpto(m_qInterpRotation, m_OwnerActor->getLocoMotion()->m_RotateQuat, dtime, m_fBodyLerpSpeed);
					//pos = m_OwnerActor->getLocoMotion()->getPosition().toVector3();

					if (g_WorldMgr && g_WorldMgr->getNewActorMoveLerpSwtich() && m_OwnerActor->IsIgonreUpdateFrequencyCtrl()) //没有刷新频率限制的，才走平滑处理
					{
						pos = m_OwnerActor->getLocoMotion()->getFramePosition().toVector3();
						selfquat = Lerpto(m_qInterpRotation, m_OwnerActor->getLocoMotion()->m_RotateQuat, dtime, m_fBodyLerpSpeed);
					}
					else
					{
						auto pTransform = m_OwnerActor->GetTransform();
						if (pTransform)
						{
							pos = pTransform->GetWorldPosition();
						}
						else
						{
							pos = m_OwnerActor->getLocoMotion()->getPosition().toVector3();
						}

						selfquat = m_OwnerActor->getLocoMotion()->m_RotateQuat;
					}
				}

				if (pos.y != MAX_FLOAT) setPosition(pos);
			}
			else
			{
				selfquat = Quaternionf::identity;
				if (pos.y != MAX_FLOAT) setPosition(WorldPos::fromVector3(pos));
			}

			//if (pos.y != MAX_FLOAT) setPosition(WorldPos::fromVector3(pos));

		}

		if (riding && riding->getObjType() == OBJ_TYPE_ROCKET)
		{
			selfquat = AngleEulerToQuaternionf(Vector3f(0, 0, 0));
		}

		if (riding && riding->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE)
		{
			selfquat = AngleEulerToQuaternionf(Vector3f(0, 0, 0));
		}

		if (riding && riding->getObjType() == OBJ_TYPE_VEHICLE)
		{
			ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(riding);
			if (vehicle)
			{
				int dir = vehicle->getRiddenBindSeatDir(m_OwnerActor);
				float angle = 0.0f;
				if (dir == DIR_NEG_X)
				{
					angle = 90.0f;
				}
				else if (dir == DIR_POS_X)
				{
					angle = -90.0f;
				}
				else if (dir == DIR_NEG_Z)
				{
					angle = 0.0f;
				}
				else if (dir == DIR_POS_Z)
				{
					angle = 180.0f;
				}

				selfquat = AngleEulerToQuaternionf(Vector3f(0, angle, 0));
			}
		}
		if (setbindrot)
		{
			// ?????????????????????????
			if (m_OwnerPlayer && (m_OwnerPlayer->isSleeping() || m_OwnerPlayer->isRestInBed()))
				selfquat = quat;
			else
				selfquat = quat * selfquat;
		}
		//乘坐传送仓--玩家身体设置不随相机转动而转动，固定方向
		if (m_OwnerPlayer && m_OwnerPlayer->isSittingInStarStationCabin())
		{
			int blockdata = m_OwnerPlayer->getWorld()->getBlockData(CoordDivBlock(m_OwnerPlayer->getPosition()));
			int placeDir = blockdata & 3;
			float playerYaw = 180.0f;
			if (placeDir == DIR_NEG_X)
			{
				playerYaw = 90.0f;
			}
			else if (placeDir == DIR_POS_X)
			{
				playerYaw = -90.0f;
			}
			else if (placeDir == DIR_NEG_Z)
			{
				playerYaw = 0.0f;
			}
			selfquat = AngleEulerToQuaternionf(Vector3f(0, playerYaw, 0));
		}
		bool bInterp = m_bInterpRotation && m_qInterpRotation != Quaternionf::identity;
		if (bInterp)
		{
			m_qInterpRotation = Lerpto(m_qInterpRotation, selfquat, dtime, m_fBodyLerpSpeed);
		}
		else
		{
			m_qInterpRotation = selfquat;
		}

		bool isStopBodyMotion = false;
		if (getOwnerActor())
		{
			LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getOwnerActor()->getAttrib());
			if (pLivingAttrib && pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
			{
				isStopBodyMotion = true;
			}
		}

		if (!isStopBodyMotion && (!m_OwnerActor || !m_OwnerActor->isStopBodyRotation()))
		{
			/*if (!m_bIsEdit)
				m_Entity->SetRotation(m_qInterpRotation);
			if (m_shapeAnimEntity)
				m_shapeAnimEntity->SetRotation(m_qInterpRotation);*/
			if (!m_bIsEdit)
				setRotation(m_qInterpRotation);
		}

		//UpdateOwnerActorTransform(pos, m_qInterpRotation);

#if (defined(DEBUG) || defined(PROFILE_MODE)) && GIZMO_DRAW_ENGABLE
		if (m_bDebugDrawLerpDirection && m_OwnerActor != NULL)
		{
			ActorLocoMotion* loc = m_OwnerActor->getLocoMotion();
			if (loc)
			{
				auto line = RotateVectorByQuat(m_qInterpRotation, -Vector3f::zAxis * 100.0f);
				auto p = loc->getPosition().toVector3() + Vector3f(0.0f, 200.0f, 0.0f);
				DebugDrawLine(p, p + line, Rainbow::ColorRGBAf::blue);

				auto q = AxisAngleToQuaternionf(Vector3f::yAxis, Deg2Rad(loc->m_RotateYaw));
				line = RotateVectorByQuat(q, -Vector3f::zAxis * 100.0f);

				p += Vector3f(0.0f, 10.0f, 0.0f);
				DebugDrawLine(p, p + line, Rainbow::ColorRGBAf::red);
			}
		}
#endif
	}
}
#if HUD_TEST
float totalFontTime = 0;
#endif

void ActorBody::updateForPlayer(unsigned int dtick)
{
    OPTICK_EVENT();
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model != nullptr)
	{
		if (m_FaceMesh && model->IsKindOf<ModelLegacy>() && model->GetModelAnimationPlayer() &&
			model->GetModelAnimationPlayer()->HasAnimPlaying(seqType2ID(SEQ_STAND)) &&
			m_bIsWinking)
		{
			m_unWinkTick += dtick;
			if (m_unWinkTick > 100)
			{
				if (m_FaceMesh != nullptr && m_FaceTexs && m_FaceTexs[EXPRESSION_STAND])
				{
					m_FaceMesh->SetTexture("g_DiffuseTex", m_FaceTexs[EXPRESSION_STAND]);
					m_unWinkTick = 0;

				}
				m_bIsWinking = false;
			}
		}

		AnimPlayMsg animmsg;
		while (model->GetModelAnimationPlayer() && model->GetModelAnimationPlayer()->PopPlayMsg(animmsg))
		{
			// 动作ID拓展到任意动作。如果是自定义的动作ID，则不检测其他 增加：进阶动作使用基础动作做判断条件
			if (animmsg.seqid == m_iActSeqId || m_vCustomAnimMap[m_iActSeqId] == animmsg.seqid)
			{
#ifdef CHINA_SEQ_USED
				if (animmsg.msgtype == APMSG_SEQ_END && (m_iActSeqId == seqType2ID(SEQ_TRANSFER) || m_iActSeqId == seqType2ID(SEQ_MOBCONCEAL)))
#else
				if (animmsg.msgtype == APMSG_SEQ_END && (animmsg.seqid == seqType2ID(SEQ_MOBCONCEAL)))
#endif
				{
					if (m_bAnimSwitchCall)
					{
						m_bAnimSwitchCall = false;
						MINIW::ScriptVM::game()->callFunction("ActorAnimSwitch", "i", m_iActSeqId);
					}
				}
				if (!m_Entity)
				{
					break;
				}
				continue;
			}

			if (animmsg.msgtype == APMSG_SEQ_END)
			{
				//20210926 codeby:wangyu 新增互动装扮动画id
				if (animmsg.seqid == seqType2ID(SEQ_SHOWTIME) || animmsg.seqid == seqType2ID(SEQ_IDLEACTION) || animmsg.seqid == seqType2ID(SEQ_IDLEACTIONINVITE) ||
					animmsg.seqid == seqType2ID(SEQ_AVATAT_SUMMON))
				{
					setCurAnim(SEQ_STAND, 0);

					if (m_bAnimSwitchCall)
					{
						MINIW::ScriptVM::game()->callFunction("ActorAnimSwitch", "i", animmsg.seqid);
						MINIW::ScriptVM::game()->callFunction("ShopActorAnimComplete", "i", animmsg.seqid);
						MINIW::ScriptVM::game()->callFunction("PlayerCenterActorAnimComplete", "i", animmsg.seqid);
						SandboxEventDispatcherManager::GetGlobalInstance().Emit("ActorAnimComplete", SandboxContext(nullptr)
							.SetData_Number("seqid", animmsg.seqid)
						);

						if (!m_Entity)
						{
							break;
						}
					}

					if (SeqType2ID(SEQ_IDLEACTION, isPlayer()) == animmsg.seqid && m_PlayerIndex > 0)
					{
						const RoleSkinDef* skindef = GetDefManagerProxy()->getRoleSkinDef(getSkinID());
						// 装扮换装功能，默认不播放脚印特效
						if (skindef && skindef->Effect && (skindef->PreSkin == "" || (skindef->PreSkin != "" && skindef->EffectType != 1)))
						{
							if (!checkWholeBodyEffect())
							{
								if (m_Entity)
									m_Entity->PlayMotion(skindef->Effect, false, 0);
							}
						}
					}

					//只有在商城界面的时候去换脸
					if (m_IsInUI && m_FaceMesh != nullptr && m_FaceTexs != nullptr && m_FaceTexs[EXPRESSION_STAND].Get() != nullptr)
						m_FaceMesh->SetTexture("g_DiffuseTex", m_FaceTexs[EXPRESSION_STAND]);
				}

				//ge GetGameEventQue().postActorBodyAnimEnd(animmsg.seqid);
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("seqid", animmsg.seqid);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_ACTORBODY_ANIM_END", sandboxContext);
				}
			}

			if (m_FaceMesh && model->IsKindOf<ModelLegacy>() && animmsg.msgtype == APMSG_SEQ_END && animmsg.seqid == seqType2ID(SEQ_STAND) && !m_bIsWinking)
			{
				m_unWinkTick++;
				if (m_unWinkTick >= 5)
				{
					if (m_FaceMesh != nullptr && m_FaceTexs != nullptr && m_FaceTexs[EXPRESSION_WINK]) //眨眼
					{
						m_FaceMesh->SetTexture("g_DiffuseTex", m_FaceTexs[EXPRESSION_WINK]);
						m_bIsWinking = true;
						m_unWinkTick = 0;
					}
				}
			}
			//LOG_INFO("animmsg.seqid %d", animmsg.seqid);
			//新骨骼动画的关键帧事件
			if (animmsg.msgtype == APMSG_SEQ_KEYFRAME && m_OwnerActor)
			{
				OnAnimationEvent(m_OwnerActor->getObjId(), animmsg.seqid, animmsg.name.c_str());
			}
		}

		if (model->IsKindOf<ModelLegacy>())
		{
			//旧动画的关键帧事件
			ModelLegacy* pLegacyModel = static_cast<ModelLegacy*>(model);
			if (pLegacyModel != NULL) //ModelNew没实现GetAnimPlayer会报assert
			{
				AnimationPlayer* pAnimPlayer = pLegacyModel->GetAnimPlayer();
				if (pAnimPlayer)
				{
					auto& vFrameEvents = pAnimPlayer->m_FrameEvents;
					for (unsigned int i = 0; i < vFrameEvents.size(); i++)
					{
						Rainbow::LegacyAnimationFrameEventData& pEvent = vFrameEvents[i];
						OnAnimationEvent(pEvent.objId, pEvent.id, pEvent.event);
					}
					pAnimPlayer->m_FrameEvents.clear();
				}
			}
		}

		if (m_CurMotionId != 0 && m_Entity && m_Entity->GetMainModel()) {
			playAnimByMotionId(m_CurMotionId, m_CurMotionIdIsLoop);
		}
	}
}


void ActorBody::update(float dtime)
{
    OPTICK_EVENT();
	m_IsUpdating = true;
	updateImpl(dtime);
	m_IsUpdating = false;
}

void ActorBody::updateImpl(float dtime)
{
	if (m_IsInUI && m_IsAttachModelView == false)
	{
		//优化，当avatar在UI页面内，但是又从modelview移除的时候，不需要更新
		return;
	}
	//PROFINY_NAMED_SCOPE("actorbody::update")

	unsigned int dtick = TimeToTick(dtime);
	//checkModelResLoad();
	checkReplaceTexture();

	if (m_ThornBallComponent)
	{
		m_ThornBallComponent->checkModelThronBallLoad();
	}

	if (m_AvatarComponent)
	{
		m_AvatarComponent->UpdateCacheAvatarModelParts();
		m_AvatarComponent->UpdateCacheAvatarSkinModelResParts();
	}
	if (m_AttachmentHandlerComponent)
	{
		m_AttachmentHandlerComponent->Tick(dtime);
	}
	Quaternionf quat = Quaternionf::identity;
	Vector3f pos(0, MAX_FLOAT, 0);
	bool setbindrot = false;
	ClientActor* riding = NULL;

	if (m_Entity)
		updatePosAndBindrot(riding, pos, setbindrot, quat);


	//头部旋转和身体旋转逻辑分离，互不干涉
	//根据相机跟随身体旋转
	if (m_NeedUpdateRenderYawOffset)
	{
		updateRenderYawOffset(dtime);
	}

	if (m_NeedUpdateLookUp)
	{
		updateLookAt(dtime);
	}

	if (m_Entity)
	{
		updateForModelView(dtime, pos, riding, setbindrot, quat);
	}

	updateMusicChatBubble(pos);

	if (!m_OwnerPlayer || !m_OwnerPlayer->IsOnPlatform() || !ClientActor::isNewLerpModel())
	{
		if (m_UIComponent)
		{
			m_UIComponent->updateForOverHead(dtick, pos);
		}
	}

	if (GetWorldManagerPtr() && GetIClientGameManagerInterface()->getICurGame() && GetIClientGameManagerInterface()->getICurGame()->isInGame())
	{
		if (m_UIComponent)
		{
			m_UIComponent->updateForHp(dtime, dtick, pos);
			m_UIComponent->updatemForChatBubble3D(dtime, dtick, pos);
		}
	}

	updateForPlayer(dtick);

	updateShadow();

	updatePlayerCtrl();
}

void ActorBody::updatePlayerCtrl()
{
	if (m_OwnerActor != nullptr && m_OwnerActor == g_pPlayerCtrl)
	{
		if (g_pPlayerCtrl->m_LastTriggerBlock.y >= 0)
		{
			g_pPlayerCtrl->renderWithLastTriggerBlocks();
		}

		if (GET_SUB_SYSTEM(VehicleMgr))
		{
			GET_SUB_SYSTEM(VehicleMgr)->renderVehicleUnendLink();
		}
	}
}

void ActorBody::updatePosAndBindrot(ClientActor*& riding, Rainbow::Vector3f& pos, bool& setbindrot, Rainbow::Quaternionf& quat)
{
	ClientActor* carried = NULL;
	if (m_OwnerActor && !m_IsAttachModelView)
	{
		auto CarryComp = m_OwnerActor->getCarryComponent();
		auto RidComp = m_OwnerActor->getRiddenComponent();
		if (RidComp && RidComp->isRiding() && (riding = RidComp->getRidingActor()) != NULL)
		{
			//ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
			auto ridingComp = riding->getRiddenComponent();
			if (ridingComp)
			{
				pos = ridingComp->getRiddenBindPos(m_OwnerActor);
				setbindrot = ridingComp->getRiddenBindRot(m_OwnerActor, quat);
			}
			if (riding->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE && !m_bIsShapeShift)
			{
				shareShift(true);
			}

		}
		else if (CarryComp && CarryComp->isCarried() && (carried = CarryComp->getCarriedActor()) != NULL)
		{
			auto CarriedComp = carried->getCarryComponent();
			if (CarriedComp)
				pos = CarriedComp->getCarryingBindPos();
		}
		else
		{

			pos = m_OwnerActor->getLocoMotion()->getFramePosition().toVector3();
			pos.y -= m_OwnerActor->getLocoMotion()->m_yOffset;
		}

		auto vehicleComponent = m_OwnerActor->m_pActorBindVehicle;
		if (vehicleComponent)
		{
			vehicleComponent->setBindRot(setbindrot, pos, quat);
		}
	}
}

void ActorBody::updateMusicChatBubble(Rainbow::Vector3f& pos)
{
	/*if (m_shapeAnimEntity)
	m_shapeAnimEntity->update(dtick);*/

	//音乐厅聊天气泡 2021-09-25 codeby: luoshuai
	bool useEditorChatBubble = false;
	if (SandboxChatBubbleManager::GetInstancePtr() && SandboxChatBubbleManager::GetInstancePtr()->getActorShowCustomChatBubble())
	{
		useEditorChatBubble = true;
		if (pos.y != MAX_FLOAT && m_OwnerActor)
		{
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
			int uin = player ? player->getUin() : 0;

			float h = (float)m_OwnerActor->getLocoMotion()->m_BoundHeight;
			Rainbow::Vector3f newPosition = pos + Rainbow::Vector3f(0.0f, h + 70, 0.0f);
			SandboxChatBubbleManager::GetInstancePtr()->updateActorChatBubblePosition(uin, newPosition);
		}
	}


	if (!useEditorChatBubble && m_UIComponent && m_UIComponent->m_MusicClubChatBubbleObj)
	{
		float h = (float)m_OwnerActor->getLocoMotion()->m_BoundHeight;
		if (pos.y != MAX_FLOAT)
		{
			m_UIComponent->m_MusicClubChatBubbleObj->SetPosition(WorldPos::fromVector3(pos + Vector3f(0.0f, h + 70, 0.0f)));
		}
		//m_MusicClubChatBubbleObj->update(dtick);
	}
}

void ActorBody::updateShadow()
{
	bool isInvisible = false;
	ActorLiving* p = dynamic_cast<ActorLiving*>(m_OwnerActor);
	if (p)
	{
		isInvisible = p->isInvisible();
	}

	bool bShadow = false;
	if (m_OwnerActor)
	{
		bShadow = m_OwnerActor->castShadow();

		if (bShadow && g_WorldMgr && g_WorldMgr->m_RuleMgr) {
			int val = (int)g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_ROLESHADOW);
			if (val != 1) {
				bShadow = false;
			}
		}
	}

	if (m_Entity && m_World != NULL && m_World->hasSky() && bShadow && m_OwnerActor->IsVisible() && (!WorldRenderer::m_CurShadowOpen || !GetRenderSetting().GetEnableShadow()) && !isInvisible) // 20210910????????????????? codeby?? keguanqiang
	{
		WCoord center = WorldPos::fromVector3(m_Entity->GetWorldPosition());
		bool flag = m_OwnerActor->IsObject() && !m_OwnerActor->IsEmpty();
		int shadowradius = flag ? 100.0f : m_OwnerActor->getLocoMotion()->m_BoundSize;
		float shadowlevel = 1.0f;
		//if (MINIW::SceneManager::getSingleton().isShadowmapEnable())
		//{
		//	//shadowradius /= 2;
		//	shadowlevel *= 0.3f;
		//}
		if (m_World->getRender())
			m_World->getRender()->addShadow(center, shadowradius, shadowlevel);
	}
}

void ActorBody::setPlayerIndex(int palyerIndex)
{
	m_PlayerIndex = palyerIndex;
	if (getSkinID() == 34)
	{
		if (m_OwnerActor && m_OwnerActor->isPlayer())
		{
			ClientPlayer* player = static_cast<ClientPlayer*>(m_OwnerActor);
			player->sureChangeColorComponent();
		}
	}
}

#pragma endregion