
#include "ActorWheel.h"
#include "world.h"
#include "BlockMaterialMgr.h"
//#include "BlockGeom.h"
//#include "Entity/OgreEntity.h"
//#include "OgreGameScene.h"
#include "ClientActorManager.h"
#include "special_blockid.h"
#include "ActorManager.h"
using namespace MINIW;

//class WheelLocoMotion : public PhysicsLocoMotion
//{
//public:
//	WheelLocoMotion(ClientActor *owner);
//
//	virtual void attachPhysActor(int id) override;
//	virtual void detachPhysActor() override;
//	void attachPhysJoint(RigidDynamicActor *actor);
//	void detachPhysJoint();
//
//	MINIW::Joint *m_PhysJoint;
//};
//
//WheelLocoMotion::WheelLocoMotion(ClientActor *owner) :PhysicsLocoMotion(owner)
//{
//
//}
//
//void WheelLocoMotion::attachPhysActor(int id)
//{
//	if (m_pWorld->isRemoteMode()) return;
//
//	if(m_PhysActor == NULL)
//	{
//		BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(id);
//		BlockGeomTemplate *geom = g_BlockMtlMgr.getGeomTemplate(mtl->getGeomName());
//		BlockGeomMeshInfo meshinfo;
//		geom->getFaceVerts(meshinfo, 0);
//
//		Rainbow::RAWMesh rawWheelMesh;
//		rawWheelMesh.mNbVerts = meshinfo.nverts;
//		rawWheelMesh.mVerts = new PxVec3[rawWheelMesh.mNbVerts];
//		for (int i=0; i<rawWheelMesh.mNbVerts; i++)
//		{
//			rawWheelMesh.mVerts[i].x = meshinfo.pverts[i].pos.x;
//			rawWheelMesh.mVerts[i].y = meshinfo.pverts[i].pos.y;
//			rawWheelMesh.mVerts[i].z = meshinfo.pverts[i].pos.z;
//		}
//
//		Rainbow::Vector3f pos = m_Position.toVector3();
//		m_PhysActor = m_pWorld->m_PhysScene->AddRigidDynamicWheel(pos, Rainbow::Quaternionf::identity, 50, 0.2, 0.2, 0.3, 1000, false, NULL, NULL, true, rawWheelMesh);
//
//		delete [] rawWheelMesh.mVerts;
//	}
//}
//
//void WheelLocoMotion::detachPhysActor()
//{
//	if (m_pWorld->isRemoteMode()) return;
//
//	if(m_PhysActor)
//	{
//		m_pWorld->m_PhysScene->DeleteRigidActor(m_PhysActor);
//		m_PhysActor = NULL;
//	}
//}
//
//void WheelLocoMotion::attachPhysJoint(RigidDynamicActor *actor)
//{
//	if (m_pWorld->isRemoteMode()) return;
//
//	//PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *>(player->getLocoMotion());
//
//	Rainbow::Quaternionf rot;
//	rot.setEulerAngle(0.0f, 0.0f, 0.0f);
//	//int offsetY = (loc->m_BoundHeight - m_BoundHeight) / 2 - 5;
//	//m_PhysJoint = m_pWorld->m_PhysScene->CreateFixJoint(loc->m_PhysActor, Rainbow::Vector3f(0, 0, 0), rot, m_PhysActor, Rainbow::Vector3f(0, offsetY, 130), Rainbow::Quaternionf::identity);
//
//	m_PhysJoint = m_pWorld->m_PhysScene->CreateD6Joint(actor, Rainbow::Vector3f(120, -50, 120), rot, static_cast<RigidDynamicActor *>(m_PhysActor), Rainbow::Vector3f(0, 0, 0), Rainbow::Quaternionf::identity);
//}
//
//void WheelLocoMotion::detachPhysJoint()
//{
//	if (m_pWorld->isRemoteMode()) return;
//
//	if (m_PhysJoint)
//	{
//		m_pWorld->m_PhysScene->DeleteJoint(m_PhysJoint);
//		m_PhysJoint = NULL;
//	}
//}

void ActorWheel::init(int blockId)
{
	ActorPart::init(blockId);

	char path[256];
	sprintf(path, "itemmods/15530/body.omod");
	Rainbow::Entity* entity = g_BlockMtlMgr.getEntity(path);
	if(entity == NULL) return;

	m_Entity = entity;

}

ActorWheel *ActorWheel::create(World *pworld, int x, int y, int z, float yaw, float pitch)
{
	ActorWheel *wheel = SANDBOX_NEW(ActorWheel);
	wheel->init(BLOCK_FRONT_LEFT_WHEEL);
	//wheel->getLocoMotion()->m_Motion = Rainbow::Vector3f(vx, vy, vz);
	if (!pworld) return NULL;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return NULL;
	actorMgr->spawnActor(wheel, x, y, z, yaw, pitch);

	//wheel->m_SpawnPos = wheel->getPosition();
	return wheel;
}

//int ActorWheel::getObjType()
//{
//	return OBJ_TYPE_THROWBLOCK;
//}
//
//void ActorWheel::tick()
//{
//	ClientActor::tick();
//
//}
//
//void ActorWheel::onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum)
//{
//	if(m_Entity)
//	{
//		presult->addRenderable(m_Entity, RL_SCENE, NULL);
//	}
//
//
//}
//
//void ActorWheel::update(float dtime)
//{
//	ClientActor::update(dtime);
//
//	WheelLocoMotion *loc = static_cast<WheelLocoMotion *>(m_LocoMotion);
//
//	if (loc && loc->m_PhysActor && loc->m_PhysActor->GetPxRigidActor())
//	{
//		m_Entity->setPosition(loc->m_UpdatePos);
//		m_Entity->setRotation(loc->m_UpdateRot);
//		m_Entity->update(TimeToTick(dtime));
//	}
//}

//void ActorWheel::enterWorld(World *pworld)
//{
//	ClientActor::enterWorld(pworld);
//
//	static_cast<WheelLocoMotion *>(m_LocoMotion)->checkPhysWorld();
//	static_cast<WheelLocoMotion *>(m_LocoMotion)->attachPhysActor(1083);
//}
//
//void ActorWheel::leaveWorld(bool keep_inchunk)
//{
//	static_cast<WheelLocoMotion *>(m_LocoMotion)->detachPhysActor();
//
//	ClientActor::leaveWorld(keep_inchunk);
//}
//
//flatbuffers::Offset<FBSave::SectionActor> ActorWheel::save(SAVE_BUFFER_BUILDER &builder)
//{
//	auto basedata = saveActorCommon(builder);
//	auto actor = FBSave::CreateActorBall(builder, basedata);
//
//	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorBall, actor.Union());
//
//}
//
//bool ActorWheel::load(const void *srcdata, int version)
//{
//	auto src = reinterpret_cast<const FBSave::ActorBall *>(srcdata);
//	loadActorCommon(src->basedata());
//	init();
//	return true;
//}
