#include "TemperatureComponent.h"
#include "Graphics/ScreenManager.h"
#include "PlayerControl.h"
#include "LuaInterfaceProxy.h"
#include "ActorBody.h"

#include "TemperatureManager.h"
#include "ModelItemMesh.h"
#include "SoundComponent.h"

#include "LivingAttrib.h"
#include "WorldManager.h"
#include "EffectParticle.h"
#include "ClientMob.h"
#include "SandBoxManager.h"
#include "ClientAppProxy.h"

using namespace MNSandbox;
using namespace Rainbow;
using namespace Rainbow::UILib;

const WCoord MovePodDiff[4] = { {10,0,0},{-10,0,0},{0,0,10},{0,0,-10}};
const int MoveIndex[8] = {0,1,1,0,2,3,3,2};

IceCube::IceCube(int buffId, EffectParticle* particle, std::string path, float scale, bool isChangeScale)
	:BuffId(buffId), Particle(particle), Path(path), Scale(scale), IsChangeScale(isChangeScale) {
	if (particle)
		EffectID = particle->_ID;
	else
		EffectID = 0;
}
void IceCube::setParticle(EffectParticle* particle)
{
	Particle = particle;
	if (particle)
		EffectID = particle->_ID;
	else
		EffectID = 0;
}

IMPLEMENT_COMPONENTCLASS(TemperatureComponent)

TemperatureComponent::TemperatureComponent() :
	m_IsInit(false),
	m_IsAffectedTemperature(false),
	m_TestSwitch(false),
	m_TemperatureValue(0),
	m_CurrentBuff(TCBuffType::Suitable),
	m_TextureMaterial(UIRenderer::GetInstance().CreateInstance()),
	m_TextureAlpha(255),
	m_IsTextureAlpha(false),
	m_IsChangeScreenDecal(false),
	m_IsChangeModelTexture(false),
	m_CMTMaxValue(0),
	m_LastPermanentBuffId(0),
	m_LastTriggerBuffId(0),
	m_IsSuitableTemperature(false),
    m_ChangeTemperatureValue(0),
    m_ChangeTemperatureTicks(0),
    m_ChangeTemperatureTickMark(0),
	m_IsPlayerShake(false),
	m_IsPlayerCameraCanMove(true),
	m_PWord(nullptr),
	m_ChangeIsPauseCurrentFrame(false),
    m_PauseCurrentFrame(false)
{
	
	std::vector<std::array<int, 3>> buffAlpha = GetLuaInterfaceProxy().get_lua_const()->temperatureBuffConfig;
	for (size_t i = 0; i < buffAlpha.size(); i++)
	{
		std::array<int, 3> arr = buffAlpha[i];
		if (i == 0 && arr[2] == 88)
		{
			m_TestSwitch = true;
		}
		m_PermanentBuffId[(TCBuffType)i] = arr[0];
		m_TriggerBuffId[(TCBuffType)i] = arr[1];
		m_BuffTextureAlpha[(TCBuffType)i] = arr[2];
	}

	m_gloader = fairygui::GLoader::create();
	m_gloader->retain();
}

void TemperatureComponent::OnInit()
{
	m_IsInit = true;
	if (GetOwnerPlayer())
	{
		m_IsAffectedTemperature = true;
		RemoveAllPermanentBuff();
	}
	else
	{
		if (!GetOwner()) return;
		ClientActor* mob = GetOwner()->ToCast<ClientActor>();
		if (mob)
		{
			const MonsterDef* def = mob->getMonsterDef();
			//�¶ȶ���Щ�������Ӱ�죬��monster���Type�ֶ�ȥ���ƣ���Ӱ���������0 1 2 3 5 8 10 11
			if (def && (def->Type == 0 || def->Type == 1 || def->Type == 2 || def->Type == 3 || def->Type == 5 || def->Type == 8 || def->Type == 10 || def->Type == 11))
			{
				m_IsAffectedTemperature = true;
			}
		}
	}
	
}

void TemperatureComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void TemperatureComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void TemperatureComponent::OnTick()
{
	if (!m_IsInit) OnInit();
	//if (!m_Switch) return;
	if (m_TestSwitch) {
		static int tickNum = 0;
		tickNum++;
		if (tickNum > 99)
		{
			tickNum = 0;
			m_TemperatureValue -= 2;
			if (m_TemperatureValue < -30)
			{
				m_TemperatureValue = 15;
			}
		}
	}
	
	Update();

	UpdateScreenDecals();

	UpdateIceCubes();

	UpdateModelTexture();
	
	UpdateChangeTemperature();

	UpdateTempImpactDuration();

	UpdatePauseCurrentFrame();
}


void TemperatureComponent::Update()
{
	//if (!m_IsAffectedTemperature) return;
	//if (!GetOwner()) return ;
	//auto m_owner = GetOwnerActor();
	//if (!m_owner) return ;
	//auto attrib = static_cast<LivingAttrib*>(m_owner->GetIActorAttrib());
	//if (attrib == NULL) return;
	//if (m_LastPermanentBuffId != 0 && !attrib->hasBuff(m_LastPermanentBuffId))
	//{
	//	m_LastPermanentBuffId = 0;
	//	m_CurrentBuff = TCBuffType::Suitable;
	//}
	//auto buffType = GetBuffType(m_TemperatureValue);
	//if (m_CurrentBuff != buffType)
	//{
	//	m_CurrentBuff = buffType;
	//
	//	RemoveAllPermanentBuff();
	//	m_LastPermanentBuffId = m_PermanentBuffId[m_CurrentBuff];
	//	m_LastTriggerBuffId = m_TriggerBuffId[m_CurrentBuff];
	//	AddBuff(m_LastPermanentBuffId);
	//	AddBuff(m_LastTriggerBuffId);
	//}

	GetRealTemperature();
	m_CurrentBuff = GetBuffTypeSOC(m_TemperatureValue);
}

void TemperatureComponent::RenderUI()
{
	if (m_Texture)
	{
		ScreenManager& screenManager = GetScreenManager();
		int ScreenWidth = screenManager.GetWidth();
		int ScreenHeight = screenManager.GetHeight();
		GetTextureAlpha();
		UIRenderer::GetInstance().BeginDraw(m_TextureMaterial, m_Texture);
		UIRenderer::GetInstance().StretchRect(0, 0, (float)ScreenWidth, (float)ScreenHeight, ColorRGBA32(255, 255, 255, m_TextureAlpha), m_Rect.origin.x, m_Rect.origin.y, m_Rect.size.width, m_Rect.size.height);
		UIRenderer::GetInstance().EndDraw();
	}
}

void TemperatureComponent::GetRealTemperature()
{
	if (!GetOwner()) return;
	IClientActor* m_owner = GetOwnerActor();
	if (!m_owner) return;
	LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(m_owner->GetIActorAttrib());
	if (attrib)
	{
		if (!m_TestSwitch)
		{
			m_TemperatureValue = attrib->getTemperature();
		}
	}
}

World* TemperatureComponent::GetWorld()
{
	if (m_PWord == nullptr)
	{
		if (!GetOwner()) return nullptr;
		IClientActor* actor = GetOwnerActor();
		if (actor)
		{
			m_PWord = actor->getWorld();
		}
	}
	return m_PWord;
}

TCBuffType TemperatureComponent::GetBuffType(float temp)
{
	GetRealTemperature();
	TemperatureManager* tempMgr = static_cast<TemperatureManager*>(GetWorldManagerPtr()->getTemperatureMgr());
	int level = tempMgr->GetTemperatureLevel(temp);
	switch (level)
	{
	case TEMPERATURE_LEVEL_BURN:
		return TCBuffType::Autoignition;
		break;
	case TEMPERATURE_LEVEL_TOPHEAT:
		return TCBuffType::ExtremelyHot;
		break;
	case TEMPERATURE_LEVEL_HEAT:
		return TCBuffType::ScorchingHot;
		break;
	case TEMPERATURE_LEVEL_SUITABLE:
		return TCBuffType::Suitable;
		break;
	case TEMPERATURE_LEVEL_ICE:
		return TCBuffType::Cold;
		break;
	case TEMPERATURE_LEVEL_TOPICE:
		return TCBuffType::ExtremelyCold;
		break;
	case TEMPERATURE_LEVEL_FREEZE:
		return TCBuffType::Freeze;
		break;
	default:
		return TCBuffType::Suitable;
		break;
	}
}

TCBuffType TemperatureComponent::GetBuffTypeSOC(float temp)
{
	// hot: 37 +1 ,37 +3 , 37 + 5
	// cold: 37 -1 ,37 -3 , 37 -5
	// 36~38 为适宜温度
    if (temp >= 38.0f) {
        // 热区域
        if (temp >= 40.0f) {
            return (temp >= 42.0f) ? TCBuffType::Autoignition : TCBuffType::ExtremelyHot;
        }
        return TCBuffType::ScorchingHot;
    } else if (temp > 36.0f) {
        // 适宜温度区域
        return TCBuffType::Suitable;
    } else {
        // 冷区域
        if (temp > 34.0f) {
            return TCBuffType::Cold;
        }
        return (temp > 32.0f) ? TCBuffType::ExtremelyCold : TCBuffType::Freeze;
    }
}

void TemperatureComponent::AddBuff(int buffId) 
{
	if (buffId == 0) return;
	if (!GetOwner()) return ;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return ;
	LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(m_owner->getAttrib());
	if (attrib == NULL) return;

	if (!attrib->hasBuff(buffId))
	{
		attrib->addBuff(buffId, 1);
	}
}

void TemperatureComponent::Remove(int buffId) 
{
	if (buffId == 0) return;
	if (!GetOwner()) return;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return;
	LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(m_owner->getAttrib());
	if (attrib == NULL) return;
	if (attrib->hasBuff(buffId))
	{
		attrib->removeBuff(buffId);
	}
}

void TemperatureComponent::RemoveAllPermanentBuff() 
{
	if (!GetOwner()) return;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return;
	for (size_t i = 0; i < m_PermanentBuffId.size(); i++)
	{
		 int buffId = m_PermanentBuffId[(TCBuffType)i];
		 if (buffId != 0)
		 {
			 LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(m_owner->getAttrib());
			 if (attrib == NULL) return;
			 if (attrib->hasBuff(buffId))
			 {
				 attrib->removeBuff(buffId);
			 }
		 }
	}
}

void TemperatureComponent::UpdateScreenDecals() {
	if (m_IsChangeScreenDecal && m_ScreenDecals.size() > 0)
	{
		m_IsChangeScreenDecal = false;
		ScreenDecal screenDecal = m_ScreenDecals.back();
		//DevUIResourceDef* pResDef = DevUIResourceCsv::getInstance()->get(screenDecal.ImgId);

		//if (!pResDef) return;
		m_Texture = nullptr;
		m_IsTextureAlpha = screenDecal.IsAlpha;
		GetTextureAlpha();
		string strLongId = to_string(screenDecal.ImgId);

		if (screenDecal.ImgId != 0)
		{
			GetClientAppProxy()->DevUISetIconByResIdExProxy(m_gloader, strLongId);
			if (m_gloader && m_gloader->IsContentLoaded())
			{
				Rainbow::SequenceTexture* seqtex = static_cast<Rainbow::SequenceTexture*>(m_gloader->getSequenceTexture());

				//if (seqtex)
				//{
				//}
				//else
				//{
				fairygui::FUISprite* psprite = m_gloader->getContent();
				if (psprite)
				{
					m_Rect = psprite->getTextureRect();
					m_Texture = psprite->getTexture().CastTo<Rainbow::Texture2D>();
				}
			}

			//}

		}
		//core::string imgPath = pResDef->Url;
		//string::size_type pos = imgPath.find(".png", 0);
		//if (pos == -1)
		//{
		//	imgPath = "ui/mobile/texture0/" + pResDef->PackName + "/" + imgPath + ".png";
		//}

		////需要进行路径转换，不然移动端会出现找不到资源的问题
		//ConvertSeparatorsToPosix(imgPath);

		//m_Texture = UIRenderer::GetInstance().CreateTexture(imgPath.c_str());
	}
	else if(m_ScreenDecals.size() == 0)
	{
		m_Texture = nullptr;
	}
}

void TemperatureComponent::SetScreenDecals(int buffId,bool isScreenDecals, int imgId, bool isAlpha)
{
	m_IsChangeScreenDecal = true;
	if (isScreenDecals && GetOwnerPlayer() == g_pPlayerCtrl)
	{
		for (auto it = m_ScreenDecals.begin(); it != m_ScreenDecals.end(); ++it)
		{
			if (it->BuffId == buffId)
			{
				return;
			}
		}
		ScreenDecal screenDecal{ buffId, imgId, isAlpha };
		m_ScreenDecals.push_back(screenDecal);
	}
	else
	{
		m_Texture = nullptr;
		for (auto it = m_ScreenDecals.begin(); it != m_ScreenDecals.end(); ++it)
		{
			if (it->BuffId == buffId)
			{
				m_ScreenDecals.erase(it);
				return;
			}
		}
	}
	
}

void TemperatureComponent::UpdateModelTexture() 
{
	if (!GetOwner()) return;
	auto m_owner = GetOwnerActor();
	if (!m_owner) return;
	if (m_IsChangeModelTexture)
	{
		ActorBody* body = static_cast<ActorBody*>(m_owner->GetIBody());
		if (body && m_CMTValue < m_CMTMaxValue)
		{
			m_CMTValue = m_CMTValue + 0.05f;
			Rainbow::Entity* entity = body->getBodyEntity();
			if (nullptr != entity)
			{
				entity->SetFrozenEffectEnable(true, m_CMTValue);
			}

			MovableObject* weaponObj = body->getWeaponModel();
			if (weaponObj != nullptr)
			{
				weaponObj->SetFrozenEffectEnable(true, m_CMTValue);
			}
			auto pHelmetObj = body->getHelmetModelModel();
			if (nullptr != pHelmetObj)
			{
				pHelmetObj->SetFrozenEffectEnable(true, m_CMTValue);
			}
		}
	}
	else
	{
		if (m_CMTValue != 0.0f)
		{
			m_CMTValue = 0.0f;
			ActorBody* body = static_cast<ActorBody*>(m_owner->GetIBody());
			if (body)
			{
				Rainbow::Entity* entity = body->getBodyEntity();
				if (nullptr != entity)
				{
					entity->SetFrozenEffectEnable(false);
				}

				MovableObject* weaponObj = body->getWeaponModel();
				if (weaponObj != nullptr)
				{
					weaponObj->SetFrozenEffectEnable(false);
				}
				auto pHelmetObj = body->getHelmetModelModel();
				if (nullptr != pHelmetObj)
				{
					pHelmetObj->SetFrozenEffectEnable(false);
				}
			}
		}
	}
}

void TemperatureComponent::UpdateWeaponTexture()
{
	if (m_IsChangeModelTexture)
	{
		m_CMTValue -= 0.1f;
	}
}

void TemperatureComponent::ChangeModelTexture(int buffId, bool isChange, bool isTransition, float minValue, float maxValue)
{
	for (int i = 0; i < m_ModelTextures.size(); i++)
	{
		if (buffId == m_ModelTextures[i].BuffId)
		{
			if (isChange)
			{
				return;
			}
			else
			{
				m_ModelTextures.erase(m_ModelTextures.begin() + i);
				break;
			}
			
		}
	}

	if (isChange)
	{
		float NewMTHigth = 0;
		float nNinVal = 100.0 - minValue;
		float nMaxVal = 100.0 - maxValue;
		float nTempVal = 100.0 - m_TemperatureValue;
		float range = nMaxVal - nNinVal;
		float val = nTempVal - nNinVal;
		float higth = 0;
		if (val > 0 && range > 0)
		{
			higth = val / range;
		}
		NewMTHigth = higth;

		if (!isTransition)
		{
			NewMTHigth = 1.0f;
		}
		else if (higth == 0)
		{
			return;
		}
		ModelTexture mt(buffId, NewMTHigth);
		m_ModelTextures.push_back(mt);
	}
	
	if (m_ModelTextures.size() > 0)
	{
		m_IsChangeModelTexture = true;
		float MaxValue = 0;
		for (int i = 0; i < m_ModelTextures.size(); i++)
		{
			if (m_ModelTextures[i].Value > MaxValue)
			{
				MaxValue = m_ModelTextures[i].Value;
				if (MaxValue == 1.0f)
				{
					break;
				}
			}
		}
		m_CMTMaxValue = MaxValue;
		if (m_CMTMaxValue < m_CMTValue)
		{
			m_CMTValue = m_CMTMaxValue - 0.1f;
		}
	}
	else
	{
		m_IsChangeModelTexture = false;
		m_CMTValue = 1.0f;
	}
}

void TemperatureComponent::UpdateIceCubes() {
	auto world = GetWorld();
	if (!world) return;
	auto eff_mgr = world->getEffectMgr();
	WCoord actorPos = GetOwnerActor()->getPosition();
	for (auto it = m_IceCubes.begin(); it != m_IceCubes.end();) {
		
		WCoord particlePos;
		auto eff = eff_mgr ? dynamic_cast<EffectParticle*>(eff_mgr->getEffectByID(it->EffectID)) : nullptr;

		if (eff == nullptr && !it->Path.empty())
		{
			if (it->Particle)
			{
				// 此处的 it->Particle 已经是野指针
				LOG_INFO("IceCubes hold Particle ptr released id=%d particle=%p", it->EffectID, it->Particle);
			}
			EffectParticle* particle = world->getEffectMgr()->playParticleEffectAsync(it->Path.c_str(), actorPos, 0, 0, 0, true,64);
			if (particle != nullptr)
			{
				particle->setScale(it->Scale);
                //不能设置maxage，否则会被自动回收
                //保持maxage为0，永不回收，外部自己管理
				//particle->setMaxAge(9999999);
				it->setParticle(particle);
			}
		}
		else if(eff->getPosition(particlePos))
		{
			if (particlePos != actorPos)
			{
				eff->setPosition(actorPos);
			}
		}
		it++;
	}
}

void TemperatureComponent::Freeze(int buffId, int particleId, float modelScale, bool isChangeScale)
{

	for (auto it = m_IceCubes.begin(); it != m_IceCubes.end();) {
		if (it->BuffId == buffId)
		{
			return;
		}
		it++;
	}
	float Scale = modelScale;
	ClientMob* actorMob = dynamic_cast<ClientMob*>(GetOwnerActor());
	if (actorMob)
	{
		const MonsterDef* def = actorMob->getDef();
		float maxValue = def->Height;
		if (maxValue < def->Width)
		{
			maxValue = def->Width;
		}
		Scale = maxValue / 200 * Scale;
	}
	
	auto def = GetDefManagerProxy()->getParticleDef(particleId);
	if (def)
	{
		std::string path = "particles/" + def->EffectName + ".ent";
		IceCube iceCube{ buffId,nullptr ,path ,Scale ,isChangeScale };
		m_IceCubes.push_back(iceCube);
	}
}

void TemperatureComponent::Unfreeze(int buffId) 
{
	auto world = GetWorld();
	if (!world) return;
	for (auto it = m_IceCubes.begin(); it != m_IceCubes.end();) {
		if (it->BuffId == buffId)
		{
			EffectParticle *eff = dynamic_cast<EffectParticle*>(world->getEffectMgr()->getEffectByID(it->EffectID));
			if (!it->Path.empty() && eff)
			{
				world->getEffectMgr()->stopParticleEffect(it->Path.c_str(), eff);
			}
			it = m_IceCubes.erase(it);
			return;
		}
		it++;
	}
}

void TemperatureComponent::ClearAllIceCubes()
{
	auto world = GetWorld();
	if (!world) return;
	for (auto it = m_IceCubes.begin(); it != m_IceCubes.end();) 
	{	
		EffectParticle *eff = dynamic_cast<EffectParticle*>(world->getEffectMgr()->getEffectByID(it->EffectID));
		if (!it->Path.empty() && eff)
		{
			world->getEffectMgr()->stopParticleEffect(it->Path.c_str(), eff);
		}
		it = m_IceCubes.erase(it);	
	}
}

void TemperatureComponent::UpdateChangeTemperature()
{
	if (m_ChangeTemperatureValue != 0 && m_ChangeTemperatureTicks > 0)
	{
		if (!GetOwner()) return ;
		ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
		if (!m_owner) return ;
		m_ChangeTemperatureTickMark++;
		if (m_ChangeTemperatureTickMark >= m_ChangeTemperatureTicks)
		{
			m_ChangeTemperatureTickMark = 0;
			LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(m_owner->getAttrib());
			if (attrib)
			{
				attrib->addTemperature(m_ChangeTemperatureValue, m_IsSuitableTemperature);
			}
		}
	}
	else {
		m_ChangeTemperatureTickMark = 0;
	}
}

void TemperatureComponent::ChangeTemperature(float value, int interval, bool isSuitableTemperature)
{
	 m_ChangeTemperatureValue = value;
	 m_ChangeTemperatureTicks = interval*20-2;
	 m_IsSuitableTemperature = isSuitableTemperature;
}

void TemperatureComponent::UpdateTempImpactDuration()
{
	auto eff_mgr = GetWorld()? GetWorld()->getEffectMgr(): nullptr;
	std::vector<int> rmBuffIds{};
	if (!GetOwner()) return ;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return ;
	for (auto it = m_TempImpactDuration.begin(); it != m_TempImpactDuration.end();) {

		//�ڻ� ��Чģ�ͷ����仯
		for (auto it1 = m_IceCubes.begin(); it1 != m_IceCubes.end();) {
			if (it1->IsChangeScale == true && it1->BuffId == it->BuffId)
			{
				if (it->LimitTime == 0)
				{
					float scale = (((1.0 - ((float)it->TickMark / (float)it->PermanentBuffTickNum)) * 0.5) + 0.5) * it1->Scale;
					auto eff = eff_mgr ? dynamic_cast<EffectParticle*>(eff_mgr->getEffectByID(it1->EffectID)) : nullptr;
					if (eff)
					{
						eff->setScale(scale);
					}
				}
			}
			it1++;
		}

		if (m_IsPlayerShake && it->JumpTickMark <= 0)
		{
			if (it->JumpIntervalTickNum > 0 && it->JumpAddTickNum > 0)
			{
				it->JumpTickMark = it->JumpIntervalTickNum;
				it->TickMark += it->JumpAddTickNum;
				
			}
			if (m_PWord && m_PWord->isRemoteMode()) {
				jsonxx::Object context;
				context << "Uin" << g_pPlayerCtrl->getUin();
				SandBoxManager::getSingleton().sendToHost("PB_PLAYER_SHAKE_CH", context.bin(), context.binLen());
			}
		}

		if (it->JumpTickMark > 0)
		{
			LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(m_owner->getAttrib());
			if (pLivingAttrib && pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
			{
				m_IsPlayerCameraCanMove = false;
			}
			PlayerShake();
			it->JumpTickMark--;

			if (it->SoundTick <= 0)
			{
				it->SoundTick = 21;
				std::string path = "particles/freezebreak2.ent";
				GetOwnerActor()->getWorld()->getEffectMgr()->playParticleEffectAsync(path.c_str(), GetOwnerActor()->getPosition(), 21);
				auto sound = m_owner->getSoundComponent();
				if (sound)
				{
					sound->playSound("item.32.break", 0.3f, 0.3f);
				}
			}
		}
		if (it->SoundTick > 0 )
		{
			it->SoundTick--;
		}
		else if (it->JumpTickMark  == 0)
		{
			it->JumpTickMark--;
			std::string path = "particles/freezebreak2.ent";
			GetOwnerActor()->getWorld()->getEffectMgr()->stopParticleEffect(path.c_str(), GetOwnerActor()->getPosition());
			m_IsPlayerCameraCanMove = true;
		}

		LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(m_owner->getAttrib());
		if (attrib)
		{
			
			//auto buffType = GetBuffType(attrib->getFinalPosTemperature());
			//if (buffType != TCBuffType::Suitable)
			{
				int addTicks = 0;

				if (it->IsLowOrHigh)
				{
					addTicks = attrib->getFinalPosTemperature();
				}
				else
				{
					addTicks = -attrib->getFinalPosTemperature();
				}

				it->TickMark += addTicks;

				int index = attrib->getBuffIndex(it->BuffId);
				auto buffinfo = attrib->getBuffInfo(index);
				if (buffinfo.ticks > 0 && buffinfo.ticks <= 9999 && it->TickNum != 0) //和lua使用相同方式判断，大于9999认为是无限buff
				{
					if (it->TickMark < 0) it->TickMark = 0;
					if (it->TickNum <= it->TickMark)
					{
						it->TickMark = 0;
						attrib->AddBuffTick(it->BuffId, -20);
						if (!attrib->hasBuff(it->BuffId))
						{
							rmBuffIds.push_back(it->BuffId);
						}
					}
				}

				if (buffinfo.ticks > 9999 && it->PermanentBuffTickNum != 0)
				{
					if (it->TickMark < 0) it->TickMark = 0;
					if (it->PermanentBuffTickNum <= it->TickMark)
					{
						m_LastPermanentBuffId = 0;
						rmBuffIds.push_back(it->BuffId);
					}
				}
			}
		}
		
		it++;
	}
	
	for (size_t i = 0; i < rmBuffIds.size(); i++)
	{
		Remove(rmBuffIds[i]);
	}

	m_IsPlayerShake = false;
	if (m_TempImpactDuration.size() == 0)
	{
		if (!m_IsPlayerCameraCanMove) m_IsPlayerCameraCanMove = true;
	}
}

void TemperatureComponent::AddTempImpactDuration(TempImpactDuration tempImpactDuration)
{
	for (auto it = m_TempImpactDuration.begin(); it != m_TempImpactDuration.end();) {
		if (it->BuffId == tempImpactDuration.BuffId)
		{
			return;
		}
		it++;
	}
	m_TempImpactDuration.push_back(tempImpactDuration);
}

void TemperatureComponent::RemoveTempImpactDuration(int buffId)
{
	for (auto it = m_TempImpactDuration.begin(); it != m_TempImpactDuration.end();) {
		if (it->BuffId == buffId)
		{
			it = m_TempImpactDuration.erase(it);
			return;
		}
		it++;
	}
}

void TemperatureComponent::ShakeImpactDuration()
{
	m_IsPlayerShake = true;
}


void TemperatureComponent::PlayerShake()
{
	static int mark = 0;
	if (!GetOwner()) return ;
	ClientActor* actor = GetOwner()->ToCast<ClientActor>();
	if (!actor) return ;
	WCoord posDiff =  MovePodDiff[MoveIndex[mark]];
	if (actor)
	{
		mark++;
		if (mark == 8) mark = 0;
		WCoord pos = actor->getPosition() + posDiff;
		actor->setPosition(pos);
	}
}
 
bool TemperatureComponent::PlayerCameraCanMove()
{
	return m_IsPlayerCameraCanMove;
}

void TemperatureComponent::UpdatePauseCurrentFrame()
{
	if (!GetOwner()) return ;
	auto m_owner = GetOwnerActor();
	if (!m_owner) return ;
	ActorBody* body = static_cast<ActorBody*>(m_owner->GetIBody());
	if (body)
	{
		if (m_ChangeIsPauseCurrentFrame)
		{
			m_ChangeIsPauseCurrentFrame = false;

			if (m_PauseCurrentFrame)
			{
				body->setNeedUpdateLookUp(false);
				body->setNeedUpdateRenderYawOffset(false);
			}
			else
			{
				body->setNeedUpdateLookUp(true);
				body->setNeedUpdateRenderYawOffset(true);
			}

			Entity* entity = body->getEntity();
			if (entity) {
				if (m_PauseCurrentFrame) {
					entity->SetTimeScale(0);
				}
				else
				{
					entity->SetTimeScale(1);
				}
			}
			MovableObject* weaponObj = body->getWeaponModel();
			if (nullptr != weaponObj)
			{
				ModelItemMesh* weaponMesh = dynamic_cast<ModelItemMesh*>(weaponObj);
				if (weaponMesh)
				{
					Model* modelItem = weaponMesh->GetModel();
					if (modelItem) {
						IModelAnimationPlayer* itemAnimPlayer = modelItem->GetModelAnimationPlayer();
						if (itemAnimPlayer) {
							if (m_PauseCurrentFrame) {
								itemAnimPlayer->SetTimeScale(0);
							}
							else
							{
								itemAnimPlayer->SetTimeScale(1);
							}
						}
					}

				}
			}
			auto pHelmetObj = body->getHelmetModelModel();
			if (nullptr != pHelmetObj)
			{
				ModelItemMesh* itemMesh = dynamic_cast<ModelItemMesh*>(pHelmetObj);
				if (itemMesh)
				{
					Model* modelItem = itemMesh->GetModel();
					if (modelItem) {
						IModelAnimationPlayer* itemAnimPlayer = modelItem->GetModelAnimationPlayer();
						if (itemAnimPlayer) {
							if (m_PauseCurrentFrame) {
								itemAnimPlayer->SetTimeScale(0);
							}
							else
							{
								itemAnimPlayer->SetTimeScale(1);
							}
						}
					}
				}
			}
		}
	}
}

void TemperatureComponent::PauseCurrentFrame(bool isPause)
{
	m_ChangeIsPauseCurrentFrame = true;
	m_PauseCurrentFrame = isPause;
}

void TemperatureComponent::GetTextureAlpha()
{
	if (m_IsTextureAlpha)
	{
		TemperatureManager* tempMgr = static_cast<TemperatureManager*>(GetWorldManagerPtr()->getTemperatureMgr());
		int level = 0; float min = 0, max = 0;
		tempMgr->GetTemperatureLevelAndVal(m_TemperatureValue, level, min, max);
		float alpha = 0;
		if (m_BuffTextureAlpha.size() == 7 && m_CurrentBuff >= TCBuffType::ExtremelyHot && m_CurrentBuff <= TCBuffType::ExtremelyCold) {
			float maxAlpha = 0;
			float minAlpha = 0;
			alpha = m_BuffTextureAlpha[m_CurrentBuff];
			if (m_CurrentBuff == TCBuffType::ExtremelyHot)
			{
				maxAlpha = m_BuffTextureAlpha[TCBuffType::ScorchingHot];
				minAlpha = m_BuffTextureAlpha[TCBuffType::ExtremelyHot];
			}
			else if (m_CurrentBuff == TCBuffType::ScorchingHot)
			{
				maxAlpha = m_BuffTextureAlpha[TCBuffType::ScorchingHot];
				minAlpha = m_BuffTextureAlpha[TCBuffType::Suitable];
			}
			else if (m_CurrentBuff == TCBuffType::Cold)
			{
				maxAlpha = m_BuffTextureAlpha[TCBuffType::Suitable];
				minAlpha = m_BuffTextureAlpha[TCBuffType::Cold];
			}
			else if (m_CurrentBuff == TCBuffType::ExtremelyCold)
			{
				maxAlpha = m_BuffTextureAlpha[TCBuffType::Cold];
				minAlpha = m_BuffTextureAlpha[TCBuffType::ExtremelyCold];
			}

			if (m_CurrentBuff != TCBuffType::Suitable)
			{
				alpha = minAlpha + ((maxAlpha - minAlpha) * (m_TemperatureValue - min) / (max - min));
				if (0 > alpha || alpha > 100)
				{
					alpha = 0;
				}
			}
			else
			{
				alpha = 0;
			}
		}
		m_TextureAlpha = 255 * (100.0 - alpha) / 100.0;
	}
	else
	{
		m_TextureAlpha = 255;
	}
}

TemperatureComponent::~TemperatureComponent()
{
	ClearAllIceCubes();
	m_PWord = nullptr;
	m_Texture = nullptr;
	CC_SAFE_RELEASE_NULL(m_gloader);
}

