
#ifndef __ActorCubeChest_H__
#define __ActorCubeChest_H__

#include "actors/clientActor/ClientActor.h"
#include "OgrePrerequisites.h"
#include "container_world.h"
namespace Rainbow
{
	class RigidDynamicActor;
}

class BindActorComponent;

class ActorCubeChest : public ClientActor //tolua_exports
{ //tolua_exports
	DECLARE_SCENEOBJECTCLASS(ActorCubeChest)
public:

	ActorCubeChest();
	//tolua_begin
	void init();
	static ActorCubeChest* create(World* pworld, int x, int y, int z, float vx = 0, float vy = 0, float vz = 0,int chestid=0);
	void createEvent();
	//tolua_end
	virtual int getObjType() const override;
	virtual void tick() override;
	virtual void update(float dtime) override;
	//virtual void onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum) override;
	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld(bool keep_inchunk) override;
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata, int version) override;
	virtual void onCollideWithPlayer(ClientActor* player) override;
	virtual void collideWithActor(ClientActor* actor) override;
	virtual void moveToPosition(const WCoord& pos, Rainbow::Quaternionf& rot, int interpol_ticks) override;
	virtual bool canBeCollidedWith()override
	{
		return true;
	}

	virtual int getMass();
	virtual void playBodyEffect(const char* fxname, bool sync = true, float loopPlayTime = -1.0f);
	virtual void playBodyEffectClient(const char* fxname, float scale, float loopPlayTime = -1.0f);
	virtual void stopBodyEffect(const char* fxname, bool sync = true);
	virtual void setMotionChange(const Rainbow::Vector3f& motion, bool addmotion = false, bool changepos = false, bool sync_pos = true) override;

	virtual void resetRound();
	virtual bool interact(ClientActor* pPlayer, bool onshift = false, bool isMobile = false) override;
	virtual bool leftClickInteract(ClientActor* player) override;
	virtual void onClear()override;
	virtual bool managedByChunk() override { return false; }
	//tolua_begin
	 
	void kickedByPlayer(int type, float charge, ClientPlayer* player);

	void playSoundByPhysCollision();

	void dribblingRotate(Rainbow::Vector3f motion, float yaw);
	bool isPhysics();
	int model_id;
	
	static int getitemid() { return ms_itemID; }
	//tolua_end
	virtual void bePushWithPlayer(ClientActor* player) override;
	virtual bool canBePushed() { return !m_Dribbing; }

	virtual int GetItemId() { return ms_itemID; }

	virtual BindActorComponent* getBindActorCom() override;
	long long getBindTargetID(); // 当前被谁绑定(这个球在谁手里)

	void setChestId(int id) { chest_id = id; };

	WorldStorageBox* getStorageBox() { return m_StorageBox; };
 
	float def_Height;
	float def_Width;
	float def_Deepth;
protected:
	virtual ~ActorCubeChest();
	int chest_id;
	void initStorageBox();
	void  intNumber(int n);
	int  popNumber(ChunkRandGen& randgen);
	//存储箱
	AirDropStorageBox* m_StorageBox;
	std::vector<int>m_Numbers;

	Rainbow::Entity* m_BallEntity;

	WCoord m_SpawnPos;
	int m_InvalidCatchTick;

	bool m_Dribbing;
	Rainbow::Vector3f m_DribbingMotion;

	static const int ms_itemID = 13420;
}; //tolua_exports

#endif
