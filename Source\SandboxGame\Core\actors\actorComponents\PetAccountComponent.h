#ifndef __PET_ACCOUNT_COMPONENT_H__
#define __PET_ACCOUNT_COMPONENT_H__

#include "world_types.h"
#include "ActorComponent_Base.h"

//#include <vector>
//#include "WorldRole_generated.h"

class ClientActor;
class ClientPlayer;

class PetAccountComponent : public ActorComponentBase
{
public:
	DECLARE_COMPONENTCLASS(PetAccountComponent)

	PetAccountComponent();
	void summonAccountPet(int petid);
	ClientActor * getCurPet();
	//WORLD_ID  getCurAccountPetObjId();
	//int getCurPetMonsterId();

	void hideAccountPet();
	void showAccountPet();
protected:
	ClientPlayer* m_owner;
	WORLD_ID m_CurAccountPet;//当前宠物objid
	int      m_PetMonsterId;//当前宠物配置id
};


#endif