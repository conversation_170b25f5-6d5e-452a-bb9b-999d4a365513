#include "ForbidJumpEffect.h"

#include "ClientActor.h"
#include "ActorLocoMotion.h"
#include "ClientActorFuncWrapper.h"

using namespace MNSandbox;


void ForbidJumpEffect::executeEffect(ClientActor* pActor)
{
	if (!pActor)
	{
		assert(pActor != nullptr);
		return;
	}


	ActorLocoMotion* locomotion = pActor->getLocoMotion();
	assert(locomotion != nullptr);

	if(locomotion){
		locomotion->setJumping(false);
		if(locomotion->m_Motion.y > 0){
			locomotion->m_Motion.y = 0;
		}
	}
	if(pActor){
		auto functionWrapper = pActor->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setCanFly(false);
		}
		pActor->setFlying(false);
	}
}