#include "ClientActorSnowBall.h"
#include "ActorLocoMotion.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "OgreUtils.h"
#include "Entity/OgreModel.h"
#include "Entity/OgreEntity.h"
#include "BlockScene.h"
#include "ClientActorManager.h"
#include "ClientPlayer.h"
#include "backpack.h"
#include "ActorAttrib.h"
#include "ClientMob.h"
#include "special_blockid.h"
#include "EffectManager.h"
#include "ActorCSProto.h"
#include "ClientItem.h"
#include "BaseItemMesh.h"
#include "ProjectileLocoMotion.h"
#include "DefManagerProxy.h"
#include "OgreRay.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "ClientActorAttract.h"
//#include "OgreRenderTypes.h"
#include "OgrePrerequisites.h"
#include "OgreBezierCurve.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "GameCamera.h"
#include "PlayerControl.h"
#include "CameraModel.h"
#include "Math/Matrix4x4f.h"
#include "MpActorManager.h"
#include "MpActorTrackerEntry.h"
#include "locoMotionComponents/physicsLocoMotion/PhysicsLocoMotion.h"
#include "Core/physics/RigidBaseActor.h"
#include "ClientActorHelper.h"
#include "AttackedComponent.h"
#include "ActorBody.h"
#include "LuaInterfaceProxy.h"
#include "PermitsDef.h"

ClientActorAttract::ClientActorAttract() : m_nState(0), m_nHandItem(0), m_nLifeTick(6*20),
                                           m_BlockModel(NULL), m_nActorObj(0), blockData(0), m_nBlockID(0)
{
	
}

ClientActorAttract::~ClientActorAttract()
{
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_BlockModel);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntityModel);
}

flatbuffers::Offset<FBSave::SectionActor> ClientActorAttract::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	auto startpos = WCoordToCoord3(m_StartPos);
	auto obj = FBSave::CreateActorAttract(builder, basedata, m_ShootingActorID, m_ItemID, &startpos, m_nState, m_nBlockID, blockData, m_nHandItem);
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorAttract, obj.Union());
}


bool ClientActorAttract::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorAttract*>(srcdata);
	loadActorCommon(src->basedata());
	init(src->itemid(), NULL);
	m_ShootingActorID = src->shooter();
	m_StartPos = Coord3ToWCoord(src->startpos());
	m_nState = src->state();
	m_nBlockID = src->blockID();
	m_nActorObj = src->attractActorObj();
	blockData = src->blockData();
	m_nHandItem = src->handitem();
#ifndef IWORLD_SERVER_BUILD
	if (m_nBlockID)
	m_BlockModel = static_cast<BlockMesh *>(ClientItem::createItemModel(m_nBlockID, ITEM_MODELDISP_SCENE));
	if (m_pWorld)
	{
		m_BlockModel->AttachToScene(m_pWorld->getScene());
	}
#endif
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (loco)
	loco->syncPos = getLocoMotion()->getPosition();
	return true;
}

void ClientActorAttract::onImpactWithActor(ClientActor *actor, const std::string& partname)
{
	if(m_nState == 0)
	{
		m_nState = 1;
	}
	onAttackActor(actor);
}

void ClientActorAttract::onAttackActor(ClientActor *actor)
{
	ClientPlayer *player = getShootingPlayer();
	if(actor && player)
	{
		if (actor->getObjId() != m_ShootingActorID)
		{
			OneAttackData atkdata;
			//memset(&atkdata, 0, sizeof(atkdata));
			float atkpoints = 0.f;
			if (m_ProjectileDef != NULL) atkpoints = m_ProjectileDef->AttackValue;
			// 新伤害计算系统 code-by:liya
			if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
			{
				// 近战、远程、元素伤害
				int damageType = m_ProjectileDef->DamageType;
				if (m_ProjectileDef->AttackType == 0)
				{
					if (damageType == 0 || damageType == 2 || damageType == 3)
					{
						assert(false && "Projectiledef damageType error!");
					}
					atkdata.atkTypeNew = (1 << damageType);
					atkdata.atkPointsNew[damageType] = atkpoints;
				}
				// 独立爆炸伤害
				else
				{
					atkdata.atkTypeNew = (1 << ATTACK_EXPLODE);
					if (m_ProjectileDef->DamageType < 4)
					{
						atkdata.explodePoints[ATTACK_PUNCH] = atkpoints;
					}
					else
					{
						atkdata.explodePoints[damageType] = atkpoints;
					}
				}
			}
			else
			{
				atkdata.atktype = ATTACK_RANGE;
				atkdata.atkpoints = atkpoints;
			}
			atkdata.damage_armor = true;
			atkdata.fromplayer = player;

			if (atkpoints != 0 || atkdata.buffId != 0)
			{
				auto component = actor->getAttackedComponent();
				if (component)
				{
					component->attackedFrom(atkdata, this);
				}
			}
		}
		else
		{
			callBack();
			return;
		}

		//吸力塞子收回
		if (m_nState <= 1)
		{
			doTrigger();
			// 这里判断是否为物理actor
			if (actor->getLocoMotion())
			{
				PhysicsLocoMotion*  physxMotion = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
				if(physxMotion && physxMotion->m_PhysActor)
				{
					Rainbow::Vector3f &motion = getLocoMotion()->m_Motion;
					WCoord playernow = player->getPosition();
					playernow.y += 100;
					Rainbow::Vector3f dir = (getLocoMotion()->getPosition() - playernow).toVector3();
					dir  = MINIW::Normalize(dir);
					//PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
					motion = -dir;
					motion *= m_ProjectileDef->InitSpeed; 

					physxMotion->m_PhysActor->SetLinearVelocity(motion * 10.0f);
					return;
				}
			}
			
			if (actor->getObjType() == OBJ_TYPE_MONSTER || actor->getObjType() == OBJ_TYPE_NPC 
				|| actor->getObjType() == OBJ_TYPE_ROLE || actor->getObjType() == OBJ_TYPE_BOAT
				|| actor->getObjType() == OBJ_TYPE_HORSE)
			{
				player->setHookObj(getObjId());
				m_nActorObj = actor->getObjId();
				m_nState = 2;
				Rainbow::Vector3f &motion = getLocoMotion()->m_Motion;
				if (player) {
					WCoord playernow = player->getPosition();
					playernow.y += 100;
					Rainbow::Vector3f dir = (getLocoMotion()->getPosition() - playernow).toVector3();
					dir  = MINIW::Normalize(dir);
					//PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
					motion = -dir;
					
					motion *= m_ProjectileDef->InitSpeed; 

					WCoord coord = getLocoMotion()->getPosition();
					coord += motion;
					getLocoMotion()->setPosition(coord.x, coord.y ,coord.z);
					//移动被勾选的东西
					Rainbow::Vector3f pos = getLocoMotion()->getPosition().toVector3();
					dir *= 20;
					pos += -dir;
					actor->getLocoMotion()->setPosition((int)pos.x, (int)pos.y, (int)pos.z);
					//actor->getLocoMotion()->m_Motion = motion;
					actor->setMotionChange(motion, false, false, false);
				}
				if (actor->getObjType() == OBJ_TYPE_ROLE)
				{
				   ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
				   if (player)
				   {
				      MpActorTrackerEntry *entry = m_pWorld->getMpActorMgr()->getTrackerEntry(player->getObjId());
					  if(entry) entry->sendActorMovementToClient(player->getUin(), actor, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
				   }
				}
			}
			else
			{
				callBack();
				return;
			}
		}
	}
	else
	{
		setNeedClear(); 
		if(player) player->setHookObj(0);  
		m_nState = 3; 
	}
	broadAttribChanges(); 
}

ClientPlayer *ClientActorAttract::getShootingPlayer()
{
   ClientActor *actor = getShootingActor(); 
   if(!actor)
   {
		return NULL;
   }
   ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
   return player;
}

void ClientActorAttract::onImpactWithBlock(const WCoord *blockpos, int face)
{ 
	ClientPlayer *player = getShootingPlayer();
	if(!player)
    {
		return;
    }

	//吸力塞子收回
	if (m_nState <= 1)
	{
		ClientPlayer *player = getShootingPlayer();
		if (player)
			player->setHookObj(getObjId());

		int blockid = m_pWorld->getBlockID(*blockpos);
		int blockdata = m_pWorld->getBlockData(*blockpos);
		if(IsAirBlockID(blockid) || blockid == BLOCK_UNLOAD)
		{
			callBack();
			return;
		}
		// 判断地图是否可修改
		if (player)
		{
			std::vector<CSPermitBitType> csPermitType;
			csPermitType.push_back(CS_PERMIT_PLACE_BLOCK);
			csPermitType.push_back(CS_PERMIT_DESTROY_BLOCK);

			MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorBlock",
				MNSandbox::SandboxContext(nullptr)
				.SetData_Number("uin", player->getUin())
				.SetData_Number("tool", 0)
				.SetData_Number("blockid", blockid)
				.SetData_Usertype<std::vector<CSPermitBitType>>("csPermitType", &csPermitType));
			
			if (result.IsExecSuccessed() && !result.GetData_Bool())
			{
				callBack();
				return;
			}
		}

		const BlockDef *def = GetDefManagerProxy()->getBlockDef(blockid);
		BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
		if (!pmtl) { return; }
		int iAttStatus = m_pWorld->CheckBlockSettingEnable(pmtl, ENABLE_BEPUSHED);
		if (iAttStatus == 0) { return; }

		if (def && def->PushFlag == 2 && iAttStatus == 1)
		{
			callBack();
			return;
		}
		else if (def && def->PushFlag == 1 && pmtl)
		{
			pmtl->dropBlockAsItem(m_pWorld, *blockpos, m_pWorld->getBlockData(*blockpos), BLOCK_MINE_NOTOOL, 1.0f);
			m_pWorld->setBlockAir(*blockpos);
			callBack();
			return;
		}
		else if (pmtl && (pmtl->getDestroyHardness(blockdata, NULL) < 0))
		{
			callBack();
			return;	
		}
		doTrigger();
		m_nBlockID = blockid;
		blockData = m_pWorld->getBlockData(*blockpos);
		getWorld()->setBlockAll(*blockpos, 0, 0);
#ifndef IWORLD_SERVER_BUILD
		m_BlockModel = static_cast<BlockMesh *>(ClientItem::createItemModel(m_nBlockID, ITEM_MODELDISP_SCENE));
		if (m_pWorld)
		{
			m_BlockModel->AttachToScene(m_pWorld->getScene());
		}
#endif
		m_nState = 2;
		ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
		if (loc)
		{
			loc->m_InGround = false;
		}
		broadAttribChanges();
	}
	else if (m_nState == 2)
	{
		callBack();
	}
}

void ClientActorAttract::onCollideWithPlayer(ClientActor*player)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		if (m_nState == 1 || m_nState == 2)
		{
			if (player)
			{
				if (player->getObjId() == m_ShootingActorID)
				{
					//收回
					callBack();
				}
			}
		}
		else if (m_nState == 0 && player->getObjId() != m_ShootingActorID)
		{
			onAttackActor(player);
		}
	}
}

void ClientActorAttract::callBack()
{
	setNeedClear();
	if (m_nState == 3)
		return;
	m_nState = 3; 
	ClientPlayer *player = getShootingPlayer();
	if (player)
	player->setHookObj(0);  
	if (!getWorld()) return ;
	ActorManager* actorMgr = static_cast<ActorManager*>(getWorld()->getActorMgr());
	if (!actorMgr) return ;
	if (m_BlockModel)
	{
		m_BlockModel->SetEnable(false);
	}

	if (m_nBlockID)
	{
		WCoord coord = CoordDivBlock(getPosition());
		int blockid = getWorld()->getBlockID(coord);
		if (IsAirBlockID(blockid) || IsWaterBlockID(blockid))
		{
			getWorld()->setBlockAll(coord, m_nBlockID, blockData);
		}
		else
		{
			for(int i=0; i<6; i++)
			{
				int blockid = getWorld()->getBlockID(NeighborCoord(coord,i));
				if (IsAirBlockID(blockid) || IsWaterBlockID(blockid))
				{
					getWorld()->setBlockAll(NeighborCoord(coord,i), m_nBlockID, blockData);
					break;
				}
			}
		}
	}
	else if (m_nActorObj)
	{
		ClientActor *actor = actorMgr->findActorByWID(m_nActorObj);
		if (actor)
		{
			actor->setMotionChange( Rainbow::Vector3f(0.0f, 0.0f, 0.0f));
			if (actor->getObjType() == OBJ_TYPE_ROLE)
			{
				ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
				if (player)
				{
					MpActorTrackerEntry *entry = m_pWorld->getMpActorMgr()->getTrackerEntry(player->getObjId());
					if(entry) entry->sendActorMovementToClient(player->getUin(), actor, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
				}
			}
		}
	}
	return;
}

void ClientActorAttract::update(float dtime)
{
	ClientActor::update(dtime);

	if (!m_EntityModel)
		return;

	ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	#ifndef IWORLD_SERVER_BUILD
		m_EntityModel->SetPosition(loc->getFramePosition());
	#endif
	float angley = 0;
	float anglep = 180;
	if(m_nState == 1 || m_nState == 2)
	{
		anglep = 0.0f;
		angley = 0;
	}
	#ifndef IWORLD_SERVER_BUILD
		m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw + angley, -getLocoMotion()->m_RotationPitch+anglep, 0);
		m_EntityModel->UpdateTick(Rainbow::TimeToTick(dtime));
	#endif

	PreRender();
}

void ClientActorAttract::tick()
{
	ClientActorProjectile::tick();

	if (m_pWorld->isRemoteMode())
	   return;

	ClientPlayer *player = getShootingPlayer();
	if(!player || player->isDead())
	{
		setNeedClear(); 
		if (player) 
			player->setHookObj(0); 
		m_nState = 3; 
		return;
	}

	if(m_nLifeTick-- == 0)
	{
		callBack();
		return;
	}

	if(m_nHandItem != player->getCurToolID())
	{
		callBack();
		return;
	}

	if(m_nState == 0 && player->getHookObj() == 0)
	{
		player->setHookObj(getObjId());
	}

	if(m_nState == 2)
	{
		WCoord playerpos = player->getLocoMotion()->getPosition();
		WCoord nowpos = getLocoMotion()->getPosition();
		nowpos -= playerpos;
		int len = (int)nowpos.lengthSquared();
		if(len < (100*100))
		{
		  callBack();
		  return;
		}
		if(player->getHookObj() == 0)
		{
			m_nState = 3;
			setNeedClear();
			return;
		}
		 
	    //移动发射物
		Rainbow::Vector3f &motion = getLocoMotion()->m_Motion;
		WCoord playernow = player->getPosition();
		playernow.y += 100;
		Rainbow::Vector3f dir = (getLocoMotion()->getPosition() - playernow).toVector3();
		dir  = MINIW::Normalize(dir);
		//PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
		motion = -dir;
		motion *= m_ProjectileDef->InitSpeed; 
		//移动被勾选的东西
		if (m_nActorObj && m_pWorld)
		{
			if (!m_pWorld) return;
			ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
			if (!actorMgr) return;
			ClientActor *actor = actorMgr->findActorByWID(m_nActorObj);
			if (actor)
			{
			   Rainbow::Vector3f pos = getLocoMotion()->getPosition().toVector3();
			   if (len > 200*200)
			   {
				   dir *= 50;
				   pos += dir;
			   }
			   else
			   {
				   dir *= 20;
				   pos += -dir;
			   }
			   actor->getLocoMotion()->setPosition((int)pos.x, (int)pos.y, (int)pos.z);
			   //actor->getLocoMotion()->m_Motion = motion;
			   actor->setMotionChange(motion, false, false, false);
			}
		}
	}
	else if(m_nState == 0)
	{
		 WCoord nowpos = getLocoMotion()->getPosition();
		 nowpos -= m_StartPos;
		 int len = (int)nowpos.lengthSquared();
		 if(len > (3000*3000))
		 {
			m_nState = 1;
		 }
	}
	else if(m_nState == 1)
	{
		 //移动发射物
		Rainbow::Vector3f &motion = getLocoMotion()->m_Motion;
		Rainbow::Vector3f dir = (getLocoMotion()->getPosition() - player->getPosition()).toVector3();
		dir  = MINIW::Normalize(dir);
		//PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
		motion = -dir;
		motion *= m_ProjectileDef->InitSpeed;  
	}
}

void ClientActorAttract::init(int itemid, ClientActor *shooter)
{
	m_ProjectileDef = GetDefManagerProxy()->getProjectileDef(itemid, true);
	m_ItemID = m_ProjectileDef->ID;
	if(itemid != m_ItemID)
	{
		auto tooDef = GetDefManagerProxy()->getToolDef(m_ItemID);
		if(tooDef)
			m_Durable = tooDef->Duration;
		else
			m_Durable = -1;
	}
#ifndef IWORLD_SERVER_BUILD
	char modelPath[128];
	sprintf(modelPath, "itemmods/%s/body2.omod", m_ProjectileDef->Model.c_str());
	m_EntityModel = g_BlockMtlMgr.getEntity(modelPath);
#endif
	ClientPlayer *player = dynamic_cast<ClientPlayer*>(shooter);
	if(player)
	m_nHandItem = player->getCurToolID();

	if(m_EntityModel && m_EntityModel->GetMainModel())
	{
		m_EntityModel->GetMainModel()->SetInstanceData(Rainbow::Vector4f(1.0f, 1.0f, 0, 0)); 
		if (m_pWorld)
		{
			m_EntityModel->AttachToScene(m_pWorld->getScene());
		}
	}

	ProjectileLocoMotion *locmove = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	locmove->setBound((int)m_ProjectileDef->Bounds, (int)m_ProjectileDef->Bounds);
	locmove->m_yOffset = locmove->m_BoundHeight/2;
	locmove->m_Gravity = m_ProjectileDef->Gravity;
	locmove->m_SpeedDecay = m_ProjectileDef->SpeedDecay;
	locmove->m_TriggerCondition = (TRIGGER_CONDITION)m_ProjectileDef->TriggerCondition;

	m_ShootingActorID = 0;
	m_ImpactTimeMark = -1;
	m_AttachedEffect = 0;

	m_StartPos = WCoord(0,0,0);
	m_AttackPoints = 0;
	m_BuffAttackAdd = 0;
	m_KnockbackStrength = 0;
	m_BuffId = 0;

	// 投掷物发射前，处理技能相关
	processSkillOnInit(shooter);
}

void ClientActorAttract::PreRender()
{
	if (m_EntityModel) 
	{
		Rainbow::WorldPos pos = m_EntityModel->GetPosition();
		if (m_BlockModel)
		{
			Vector3f dir = -getLocoMotion()->m_Motion;
			dir.Normalize();
			dir *= 100;
			pos += dir;
			m_BlockModel->SetPosition(pos);
		}
	}
	else
	{
		return;
	}

	ClientActor *actor = getShootingActor(); 
	if(!actor)
	{
		return;
	}

	WCoord handpos;
	if(actor == g_pPlayerCtrl && g_pPlayerCtrl->getCamera()->getMode() == CAMERA_FPS)
	{
		Rainbow::Vector3f offset(0.0f, 5.0f, 0.0f);
		if(g_pPlayerCtrl->m_CameraModel && g_pPlayerCtrl->m_CameraModel->m_HandModel)
		{
			Rainbow::Matrix4x4f tm = g_pPlayerCtrl->m_CameraModel->m_HandModel->GetAnchorWorldMatrix(101);
			handpos = tm.MultiplyPoint3(offset);
		}
		else
		{
			if(actor->getBody() != NULL)
			{
				handpos = actor->getBody()->getBindPointPos(101, &offset);
			}
			else
				return;
		}
	}
	else
	{
		Rainbow::Vector3f offset(0.0f, 5.0f, 0.0f);
		if(actor->getBody() != NULL)
		{
			handpos = actor->getBody()->getBindPointPos(101, &offset);
		}
		else
			return;
	}

	if (g_pPlayerCtrl && g_pPlayerCtrl->getCamera())
	{
		MINIW::CatmullRomCurve cc;
		Rainbow::Vector3f p1(0.0f, 0.0f, 0.0f);
		Rainbow::Vector3f p2 = (handpos - getLocoMotion()->getPosition()).toVector3();

		Rainbow::Vector3f p0, p3;
		p0 = p1*2.0f - p2;

		Rainbow::Vector3f dir0 = g_pPlayerCtrl->getCamera()->getLookDir();
		Rainbow::Vector3f dir3 = dir0;

		p3 = p2*2.0f - p1;

		cc.setControlPoints(p0, p1, p2, p3);
		cc.setNormals(dir0, dir3);
		m_pWorld->getRender()->getCurveRender()->addCurve(6, cc, getLocoMotion()->getPosition(), 6.0f, 3.0f);
	}
}

void ClientActorAttract::broadAttribChanges()
{
	PB_AttractAttribChangeHC attractAttribChangeHC;
	attractAttribChangeHC.set_objid(getObjId());
	attractAttribChangeHC.set_state(m_nState);
	attractAttribChangeHC.set_blockid(Block::toResID(m_nBlockID));
	attractAttribChangeHC.set_blockexid(Block::toBlockDataEx(m_nBlockID));
	m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ATTRACT_ATTRIB_CHANGE_HC, attractAttribChangeHC, this, false);
}

void ClientActorAttract::setBlockID (unsigned int blockID)
{
	 m_nBlockID = blockID;
	 if (blockID && m_BlockModel == NULL)
	 {
#ifndef IWORLD_SERVER_BUILD
		m_BlockModel = static_cast<BlockMesh *>(ClientItem::createItemModel(m_nBlockID, ITEM_MODELDISP_SCENE));
		if (m_pWorld)
		{
			m_BlockModel->AttachToScene(m_pWorld->getScene());
		}
#endif
	 }
}

void ClientActorAttract::enterWorld(World *pworld)
{
	ClientActor::enterWorld(pworld);

	pworld->addSpecialUnit(this);

	if (m_EntityModel)
	{
		m_EntityModel->AttachToScene(pworld->getScene());
	}

	if (m_BlockModel)
	{
		m_BlockModel->AttachToScene(pworld->getScene());
	}
}

void ClientActorAttract::leaveWorld(bool keep_inchunk)
{
	m_pWorld->removeSpecialUnit(this);

	ClientActor::leaveWorld(keep_inchunk);

	if (m_EntityModel)
	{
		m_EntityModel->DetachFromScene();
	}

	if (m_BlockModel)
	{
		m_BlockModel->DetachFromScene();
	}
}

void ClientActorAttract::getViewBox(CollideAABB &box)
{	
	WCoord pos = getLocoMotion()->getPosition();
	WCoord dir = pos - m_StartPos;
	box.dim = dir;
	box.pos = m_StartPos; 
}
