#include "SoundLogicComponent.h"
#include "SoundComponent.h"
#include "coreMisc.h"
#include "ClientActor.h"
#include "world.h"
#include "ActorLocoMotion.h"

IMPLEMENT_COMPONENTCLASS(SoundLogicComponent)
SoundLogicComponent::SoundLogicComponent() : m_pActor(nullptr), m_InWaterState(InWaterState::NoInWater)
{
}

SoundLogicComponent::~SoundLogicComponent()
{

}

void SoundLogicComponent::OnEnterOwner(::MNSandbox::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);

	m_pActor = static_cast<ClientActor*>(GetOwnerActor());

	BindOnTick();
}

void SoundLogicComponent::OnLeaveOwner(::MNSandbox::SandboxNode* owner)
{
	UnBindOnTick();

	m_pActor = nullptr;

	Super::OnLeaveOwner(owner);
}

void SoundLogicComponent::OnTick()
{
	Super::OnTick();

	UpdateInWaterState();
}

bool SoundLogicComponent::IsBoundsAllInLiquid()
{
	if (!m_pActor || !m_pActor->getWorld())
		return false;

	CollideAABB box;
	m_pActor->getCollideBox(box);

	return m_pActor->getWorld()->isAllWater(box.minPos(), box.maxPos());
}

void SoundLogicComponent::UpdateInWaterState()
{
	if (!m_pActor)
		return;

	// after set up m_InWater
	InWaterState eState = InWaterState::NoInWater;
	if (m_pActor->isInWater())
	{
		if (IsBoundsAllInLiquid())
		{
			eState = InWaterState::AllInWater;
		}
		else
		{
			eState = InWaterState::HalfInWater;
		}
	}
	else
	{
		eState = InWaterState::NoInWater;
	}

	PlaySwimSound(eState);
	PlayEnterLeaveWaterSound(eState);
}

void SoundLogicComponent::PlayEnterLeaveWaterSound(InWaterState eState)
{
	if (eState == m_InWaterState)
		return;

	int iStatus = 0;
	if (eState == InWaterState::HalfInWater && m_InWaterState == InWaterState::AllInWater)
	{
		// 出水
		iStatus = 1;
	}
	else if (eState == InWaterState::AllInWater && m_InWaterState == InWaterState::HalfInWater)
	{
		// 入水
		iStatus = 2;
	}

	m_InWaterState = eState;
	if (iStatus > 0 && m_pActor->getDefID() != 3627)// 飞鱼不使用通用的入水出水声音
	{
		auto sound = m_pActor->getSoundComponent();
		if(sound)
			sound->playSound(iStatus == 2 ? "buff.swim2" : "buff.swim3", 1.0f, 1.0f + (GenRandomFloat() - GenRandomFloat()) * 0.4f);
	}
}

void SoundLogicComponent::PlaySwimSound(InWaterState eState)
{
	if (!m_pActor || eState != m_InWaterState || eState == InWaterState::HalfInWater)
		return;

	auto pLoco = m_pActor->getLocoMotion();
	if (pLoco && pLoco->m_bMoving && pLoco->isInLiquid())
	{
		Vector3f um = pLoco->m_Motion / BLOCK_FSIZE;
		float volume = Sqrt(um.x * um.x * 0.2f + um.y * um.y + um.z * um.z * 0.2f) * 0.35f;
		if (volume > 1.0f) volume = 1.0f;

		volume = 1.0f;
		auto sound = m_pActor->getSoundComponent();
		if (sound)
		{
			if (eState == InWaterState::HalfInWater && m_InWaterState == InWaterState::HalfInWater)
			{
				// 水面游泳
				sound->playSound("buff.swim1", volume, 1.0f + (GenRandomFloat() - GenRandomFloat()) * 0.4f);
			}
			else
			{
				sound->playSound("env.swim", volume, 1.0f + (GenRandomFloat() - GenRandomFloat()) * 0.4f);
			}
		}
	}
}