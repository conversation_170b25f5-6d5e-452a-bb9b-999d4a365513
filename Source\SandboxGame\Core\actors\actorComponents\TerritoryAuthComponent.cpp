#include "TerritoryAuthComponent.h"
#include "ClientPlayer.h"
#include "world.h"
#include "GameNetManager.h"
#include "CommonUtil.h"
#include "SandboxEventDispatcherManager.h"
#include "BlockMaterialMgr.h"
#include "container_territory.h"
#include "BlockTerritory.h"
#define NetType 3
#define TerritoryAuthSelf 1
#define TerritoryAuthCancel 2
#define TerritoryAuthShare 3
#define TerritoryAuthClear 4
#define TerritoryAuthOpenUI 5
#define TerritoryAuthOnMessage 6

IMPLEMENT_COMPONENTCLASS(TerritoryAuthComponent)
TerritoryAuthComponent::TerritoryAuthComponent() : _player(nullptr)
{

}

TerritoryAuthComponent::~TerritoryAuthComponent()
{

}

void TerritoryAuthComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);

	ClientPlayer* client_player = dynamic_cast<ClientPlayer*>(owner);
	if (!client_player)
	{
		LOG_WARNING("check not owner");
		return;
	}

	_player = client_player;
}

void TerritoryAuthComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnLeaveOwner(owner);
}

void TerritoryAuthComponent::OnTick()
{

}

//打开领地柜
void TerritoryAuthComponent::OpenTerritoryChest(const WCoord& pos)
{
	if (!Check(pos))
		return;

	if (!IsTerritoryStorage(pos))
		return;
	_player->triggerBlock(pos, DIR_NEG_X, Rainbow::Vector3f::zero);
}

//设置自己的授权
void TerritoryAuthComponent::SetSelfAuth(const WCoord& pos)
{
	if (!Check(pos))
		return;

	if (!IsTerritoryStorage(pos))
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << TerritoryAuthSelf;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	auto container = GetTerritoryContainer(pos);
	if (!container)
		return;
	if (container->m_OwnerUin == 0) //表示没owner
	{
		container->m_OwnerUin = _player->getUin();
		// 获取容器位置用于标记更新
		BlockTerritory* block = dynamic_cast<BlockTerritory*>(pWorld->getBlockMaterial(pos));
		if (block)
		{
			auto con_pos = BlockTerritory::GetRealTotemIceContainer(pWorld, pos);
			pWorld->markBlockForUpdate(con_pos);
		}
		//TODO: 在这里实现取消授权的逻辑
		//比如将该位置的领地柜设置为只有拥有者可以访问
		//SendClientMessage("Territory storage auth self");
		_player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000728);
	}
}

//取消自己的授权
void TerritoryAuthComponent::CancelSelfAuth(const WCoord& pos)
{
	if (!Check(pos))
		return;

	if (!IsTerritoryStorage(pos))
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << TerritoryAuthCancel;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}
	
	auto container = GetTerritoryContainer(pos);
	if (!container)
		return;
	if (container->IsAuthorized(_player->getUin()))
	{
		container->RemoveAuthorized(_player->getUin()); 

		// 获取容器位置用于标记更新
		BlockTerritory* block = dynamic_cast<BlockTerritory*>(pWorld->getBlockMaterial(pos));
		if (block)
		{
			auto con_pos = BlockTerritory::GetRealTotemIceContainer(pWorld, pos);
			pWorld->markBlockForUpdate(con_pos);
		}
		//TODO: 在这里实现取消授权的逻辑
		//比如将该位置的领地柜设置为只有拥有者可以访问
		//SendClientMessage("Territory storage auth cancelled");
		_player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000718);
	}
}

//分享授权
void TerritoryAuthComponent::ShareAuth(const WCoord& pos, unsigned int targetUin)
{
	if (!Check(pos))
		return;

	if (!IsTerritoryStorage(pos))
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << TerritoryAuthShare;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;
		obj << "targetUin" << targetUin;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	// 获取领地容器
	auto container = GetTerritoryContainer(pos);
	if (!container)
	{
		SendClientMessage("Failed to get territory container");
		return;
	}

	// 检查目标用户是否已经被授权
	if (container->IsAuthorized(static_cast<unsigned int>(targetUin)))
	{
		SendClientMessage("User is already authorized");
		return;
	}

	// 添加授权用户
	container->Addauthorized(static_cast<unsigned int>(targetUin));

	// 获取容器位置用于标记更新
	BlockTerritory* block = dynamic_cast<BlockTerritory*>(pWorld->getBlockMaterial(pos));
	if (block)
	{
		auto con_pos = BlockTerritory::GetRealTotemIceContainer(pWorld, pos);
		pWorld->markBlockForUpdate(con_pos);
	}

	//SendClientMessage("Territory storage auth shared successfully");
	_player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000728);
}

//清空授权
void TerritoryAuthComponent::ClearAuth(const WCoord& pos)
{
	if (!Check(pos))
		return;

	if (!IsTerritoryStorage(pos))
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << TerritoryAuthClear;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	auto container = GetTerritoryContainer(pos);
	if (!container)
		return;
	container->ClearAllauthorizedPlayers(); // 清空所有授权玩家
	// 获取容器位置用于标记更新
	BlockTerritory* block = dynamic_cast<BlockTerritory*>(pWorld->getBlockMaterial(pos));
	if (block)
	{
		auto con_pos = BlockTerritory::GetRealTotemIceContainer(pWorld, pos);
		pWorld->markBlockForUpdate(con_pos);
	}
	//TODO: 在这里实现清空授权的逻辑
	//比如清空该位置领地柜的所有授权用户
	//SendClientMessage("Territory storage auth cleared");
	_player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000720);
}

bool TerritoryAuthComponent::HasAuth(const WCoord& pos, unsigned int targetUin)
{
	if (!Check(pos))
		return false;

	if (!IsTerritoryStorage(pos))
		return false;

	//TODO: 在这里实现检查授权的逻辑
	//比如检查当前玩家是否有权限访问该领地柜
	auto container = GetTerritoryContainer(pos);
	if (!container)
		return false;
	return  container->IsAuthorized(targetUin);
}

bool TerritoryAuthComponent::IsOpenPieMenu(const WCoord& pos)
{
	if (!Check(pos))
		return false;

	if (!IsTerritoryStorage(pos))
		return false;

	// 如果是领地柜且玩家有权限访问，则可以打开饼图菜单
	return true;
	//return HasAuth(pos);
}

bool TerritoryAuthComponent::IsTerritoryStorage(const WCoord& pos)
{
	if (!Check(pos))
		return false;

	World* pWorld = _player->getWorld();
	int blockid = pWorld->getBlockID(pos.x, pos.y, pos.z);
	
	auto pmtl = g_BlockMtlMgr.getSingleton().getMaterial(blockid);
	if (!pmtl)
		return false;

	BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockid);
	if (!blockDef)
		return false;
	
	// 检查是否为支持预览的方块类型
	std::string blockType = blockDef->Type.c_str();

	return blockType == "territory"; // 2411 是领地柜的方块ID，具体ID需要根据实际情况调整

	////TODO: 在这里判断是否为领地柜
	////这里需要根据实际的领地柜方块类型来判断
	////比如检查方块类型是否为领地柜相关的类型
	//return pmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_Storage;
}

TerritoryContainer* TerritoryAuthComponent::GetTerritoryContainer(const WCoord& pos)
{
	if (!Check(pos))
		return nullptr;

	if (!IsTerritoryStorage(pos))
		return nullptr;

	World* pWorld = _player->getWorld();
	BlockTerritory* block = dynamic_cast<BlockTerritory*>(pWorld->getBlockMaterial(pos));
	if (!block)
		return nullptr;
	
	auto con_pos = BlockTerritory::GetRealTotemIceContainer(pWorld, pos);
	auto container = dynamic_cast<TerritoryContainer*>(pWorld->getContainerMgr()->getContainer(con_pos));
	
	return container;
}

//void TerritoryAuthComponent::OpenUI(const WCoord& pos)
//{
//	if (!Check(pos))
//		return;
//
//	if (!IsTerritoryStorage(pos))
//		return;
//
//	World* pWorld = _player->getWorld();
//	if (pWorld->isRemoteMode())
//	{
//		//发主机
//		PB_PlayerCustomCH protoCH;
//		protoCH.set_type(NetType);
//
//		jsonxx::Object obj;
//		obj << "type" << TerritoryAuthOpenUI;
//		obj << "x" << pos.x;
//		obj << "y" << pos.y;
//		obj << "z" << pos.z;
//
//		protoCH.set_data(obj.json());
//
//		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);
//
//		return;
//	}
//
//	// 直接打开UI
//	jsonxx::Object obj;
//	obj << "x" << pos.x;
//	obj << "y" << pos.y;
//	obj << "z" << pos.z;
//
//	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("OpenTerritoryAuthUI",
//		MNSandbox::SandboxContext(nullptr)
//		.SetData_String("data", obj.json()));
//
//#ifdef IWORLD_SERVER_BUILD
//	obj << "type" << TerritoryAuthOpenUI;
//
//	PB_PlayerCustomHC protoHC;
//	protoHC.set_type(NetType);
//
//	protoHC.set_data(obj.json());
//
//	GetGameNetManagerPtr()->sendToClient(_player->getUin(), PB_PLAYER_CUSTOM_HC, protoHC);
//#endif
//}

void TerritoryAuthComponent::SendClientMessage(const std::string& str)
{
#ifdef IWORLD_SERVER_BUILD
	jsonxx::Object obj;
	obj << "type" << TerritoryAuthOnMessage;
	obj << "msg" << str;

	PB_PlayerCustomHC protoHC;
	protoHC.set_type(NetType);

	protoHC.set_data(obj.json());

	GetGameNetManagerPtr()->sendToClient(_player->getUin(), PB_PLAYER_CUSTOM_HC, protoHC);
#else
	CommonUtil::GetInstance().PostInfoTips(str);
#endif
}

bool TerritoryAuthComponent::Check(const WCoord& pos)
{
	if (!_player)
		return false;

	World* pWorld = _player->getWorld();
	if (!pWorld)
		return false;

	return true;
}

void TerritoryAuthComponent::OnNetMessage(const std::string& data)
{
	if (!_player)
		return;

	jsonxx::Object obj;
	obj.parse(data);

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthSelf)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		SetSelfAuth(WCoord(x, y, z));
		return;
	}

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthCancel)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		CancelSelfAuth(WCoord(x, y, z));
		return;
	}

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthShare)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");
		int targetUin = obj.get<jsonxx::Number>("targetUin");

		ShareAuth(WCoord(x, y, z), targetUin);
		return;
	}

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthClear)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		ClearAuth(WCoord(x, y, z));
		return;
	}

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthOpenUI)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		jsonxx::Object uiObj;
		uiObj << "x" << x;
		uiObj << "y" << y;
		uiObj << "z" << z;

		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("OpenTerritoryAuthUI",
			MNSandbox::SandboxContext(nullptr)
			.SetData_String("data", uiObj.json()));

		return;
	}

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthOnMessage)
	{
		SendClientMessage(obj.get<jsonxx::String>("msg"));
		return;
	}
} 