#ifndef __REVIVE_POINT_COMPONENT_H__
#define __REVIVE_POINT_COMPONENT_H__

#include <vector>
#include "world_struct.h"
#include "OgreWCoord.h"
#include "ActorComponent_Base.h"
#include "SandboxGame.h"


class ClientPlayer;
class World;
class EXPORT_SANDBOXGAME RevivePointComponent;
class RevivePointComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(RevivePointComponent)
public:

	RevivePointComponent();
	
	//void onTick();
	//virtual void Tick(float elapse) override;
	virtual void OnBeginPlay() override;

public:
	WCoord getRevivePoint();
	WCoord getSpawnPoint();
protected:
	//bool initNewWorldPoint();
	void syncRevivePoint(int uin);	//同步复活点给客机
	void sendRevivePoint(int uin, int mapid, WCoord& spawnpoint, WCoord& revivepoint);	//发送复活点给客机

	void setRevivePoint(int x, int y, int z);
	void setTriggerRevivePoint(int x, int y, int z);

	void checkPlayReviveEffect();

	WCoord verifyRespawnCoordinates(World *pworld, const WCoord &pt, bool spawnforced);


	void setRevivePoint(const WCoord *pos, bool forced, bool bSetWorldMgr);

	World* teleportHome(int curToolID, WCoord& pos);
	bool replacePlayer(bool bSetWorldMgr, bool bGameMakerRunMode, int curToolID);
	
	bool GetLastInteractSpBlockList(WCoord & pos,int mapid = -1);
	bool getValidAccountWorldPoint(int mapid, World* pworld, WCoord& spawnpoint, WCoord& revivepoint);

	void checkChangeSpawnPoint(const WCoord &blockpos, int mapid);
	void setSpawnPoint(const WCoord &blockpos, bool bSetWorldMgr = true);

	bool getAccountWorldPoint(int mapid, WCoord& spawnpoint, WCoord& revivepoint);
	void setAccountWorldPoint(int mapid, WCoord spawnpoint = WCoord(0, -1, 0), WCoord revivepoint = WCoord(0, -1, 0));
	void playClientReviveEffect(int mapid, WCoord revivepoint);

	WCoord getTriggerRevivePoint()
	{
		return m_TriggerRevivePoint;
	}	

	bool IsInteractSpBlockValid();
	void AddInteractSpBlock(int mapid, int nBlockID, WCoord point);
	void ClearInteractSpBlock();
	void ResetRevivePoint();

protected:
	
	//virtual bool Init() override;
	//virtual void Release() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

private:
	MNSandbox::SandboxResult SANDBOXAPI OnGetRevivePointEx(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnSetRevivePoint(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnGetRevivePoint(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnGetSpawnPoint(MNSandbox::SandboxContext context);

	MNSandbox::SandboxResult SANDBOXAPI OnSetSpawnPoint(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnGetSpawnPointByPriority(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnSetTriggerRevivePoint(MNSandbox::SandboxContext context);

	MNSandbox::SandboxResult SANDBOXAPI OnSetRevivePointEx(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnTriggerChangeSpawnPoint(MNSandbox::SandboxContext context);

	MNSandbox::SandboxResult SANDBOXAPI OnIsInteractSpBlockValid(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnResetRevivePoint(MNSandbox::SandboxContext context);	//

	MNSandbox::SandboxResult SANDBOXAPI OnGetLastInteractSpBlockList(MNSandbox::SandboxContext context);
	//add
	MNSandbox::SandboxResult SANDBOXAPI OnCheckPlayReviveEffect(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnGetValidAccountWorldPoint(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnTeleportHome(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnReplacePlayer(MNSandbox::SandboxContext context);
	
	MNSandbox::SandboxResult SANDBOXAPI OnLoadFromFile(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnLoadFromFileByToggleGameMakerMode(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnSaveToFile(MNSandbox::SandboxContext context);//interactBlock
	MNSandbox::SandboxResult SANDBOXAPI OnInteractBlock(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnInitNewWorldPoint(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnCheckChangeSpawnPoint(MNSandbox::SandboxContext context);

	MNSandbox::SandboxResult SANDBOXAPI OnGetAccountWorldPoint(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnSetAccountWorldPoint(MNSandbox::SandboxContext context);

	MNSandbox::SandboxResult SANDBOXAPI OnRevivePoint2Client(MNSandbox::SandboxContext context);
	MNSandbox::SandboxResult SANDBOXAPI OnSyncRevivePoint(MNSandbox::SandboxContext context);
	//
private:
	WCoord m_RevivePoint; //??
	WCoord m_TriggerRevivePoint;//触发器修改的重生点 
	WCoord m_SpawnPoint; // 个人重生点
	bool m_SpawnForced;// ??
	std::vector<AccountWorldPointInfo>m_AccountWorldPoint;                        //玩家放置,复活点列表
	struct InteractSpBlockInfo
	{
		int nMapID;
		int nBlockID;
		WCoord interactpoint;

		InteractSpBlockInfo()
		{
			nMapID = -1;
			nBlockID = -1;
			interactpoint = WCoord(0, -1, 0);
		}
	};
	InteractSpBlockInfo	m_LastInteractSpBlock;			   //最后交互方块
	std::vector<InteractSpBlockInfo>m_InteractSpBlockList; //交互特殊方块列表(历史复活点)
	bool m_bFirstPlayReviveEffect;//复活特效 --标记 

}; //tolua_exports


#endif