#pragma once
#include "PlayerState.h"
#include "OgreWCoord.h"
#include "PlayerAnimation.h"
class AttackBlockState : public PlayerState  //tolua_exports
{ //tolua_exports
public:
    //tolua_begin
    AttackBlockState(PlayerControl* host);
    virtual ~AttackBlockState();
    virtual void doBeforeEntering() override;
    virtual std::string update(float dtime) override;
    virtual void doBeforeLeaving() override;
    virtual void OnTick(float elapse) final;
    //tolua_end
    virtual bool attackBlock(const WCoord& targetblock, DIG_METHOD_T dgmethod);
private:
    int m_AttackStartMark;
    int m_AttackDuration;
    int m_PlayAttackTicks;
    bool m_AttackComplete;
};  //tolua_exports 