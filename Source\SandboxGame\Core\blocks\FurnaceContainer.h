
#ifndef __WORLDFURNACECONTAINER_H__
#define __WORLDFURNACECONTAINER_H__

#include "ChunkSave_generated.h"
#include "container_world.h"

namespace google {
    namespace protobuf {
        template <typename Element>
        class RepeatedPtrField;
    }
}

namespace game {
    namespace common {
        class PB_ItemData;
    }
}


//因为旧版本，0，1，2位置固定了，新增的往后填
enum GRID_TYPE
{
	GRID_MTL = 0,  //原料
	GRID_FUEL,		//燃料
	GRID_RESULT,	//产物
	GRID_FUEL2,		//燃料2
	GRID_FUEL3,		//燃料3
	GRID_RESULT2,	//产物2
	GRID_RESULT3,
	GRID_RESULT4,
	GRID_RESULT5,
	GRID_RESULT6,
	GRID_MTL2,
	GRID_MTL3,

	GRID_FULL = 30	//总格子数
};

class FurnaceContainer : public WorldContainer //tolua_exports
{ //tolua_exports
public:
	
	enum
	{
		FURNACE_QUALITY_IRON = 798,		//铁炉，itemid：798
		FURNACE_QUALITY_COPPER = 799,	//铜炉，itemid：799
		FURNACE_QUALITY_STONE = 802		//石炉，itemid：802
	};
public:
	//tolua_begin
	FurnaceContainer();
	FurnaceContainer(const WCoord &blockpos);
	virtual ~FurnaceContainer();

	virtual int getObjType() const override
	{
		return OBJ_TYPE_FURNACE;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerFurnace;
	}

	virtual BackPackGrid *index2Grid(int index) override;
	virtual void afterChangeGrid(int index);
	virtual void afterChangeGridByMTL(GRID_TYPE type);
	virtual bool canPutItem(int index);
	virtual void onAttachUI();
	virtual void onDetachUI();

	virtual void dropItems();
	virtual void updateTick();

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER &builder);
	virtual bool load(const void *srcdata);
	virtual int getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos) override;

	virtual float getAttrib(int i)
	{
		if (i == 0) return getMeltTicksPercent(i);
		else if (i > 0 && i < 4)
			return getHeatPercent(i);
		else if (i == 4)
			return m_temperature;
		else if (i == 5)
			return m_isMelting ? 1 : 0;
		else if (i == 6)
			return getMeltTicksPercent(10);
		else if (i == 7)
			return getMeltTicksPercent(11);
		else if (i == 8)
			return m_allProvideHeat;
		else
			return 0;
	}
	virtual int onInsertItem(const BackPackGrid &grid, int num, int params) override;
	bool canInsertItem(const BackPackGrid& grid, int param);
	virtual BackPackGrid *onExtractItem(int params) override;
	virtual int calComparatorInputOverride() override;

	void clear();
	int addItemByCount(int itemid, int num);
	void removeItemByCount(int itemid, int num);

	virtual void enterWorld(World *pworld)
	{
		WorldContainer::enterWorld(pworld);
		registerUpdateTick();
		SetQuality();
	}

	virtual void leaveWorld() override;

	void SetQuality();

	virtual void setTemperatureLev(int lev) override;
	float getHeatPercent(int index);
	float getMeltTicksPercent(int index);

	virtual int getGridNum()
	{
		return GRID_FULL;
	}
	//tolua_end
private:
	void meltOnce(GRID_TYPE type);//溶炼一次
	void addHeatOnce();
	void onHeatOnOff();
	int getCanUseResultGrid(GRID_TYPE type); //获取可以使用产物格子,没有则返回-1；def:原料def
	int getFurnaceResultId(const FurnaceDef *def);
	int getFurnaceResultNum(const FurnaceDef *def);
	void updateTickMTL(GRID_TYPE type, float allProvideHeat, bool &hasprogress);
	void UpdateEffect();
public:
	BackPackGrid m_Grids[GRID_FULL]; //0: mtl,  1: fuel,   2: result
	//tolua_begin
	int m_CurHeat;
	int m_MaxHeat;	
	int m_CurHeat2;
	int m_MaxHeat2;
	int m_CurHeat3;
	int m_MaxHeat3;
	int m_MeltTicks;
	int m_MeltTicks2;
	int m_MeltTicks3;
	bool m_isMelting; //是否正在熔炼
	float m_meltOnce_Time;	//消耗一个材料的时间(tick数, 如200即10秒)
	
	float m_ProvideHeat;//燃料提供热量（每个tick提供的热量）
	float m_ProvideHeat2;
	float m_ProvideHeat3;
	float m_BurnOnceTime;//熔炼一个原料需要的时间
	float m_BurnOnceTime2;//熔炼一个原料需要的时间
	float m_BurnOnceTime3;//熔炼一个原料需要的时间
	float m_allProvideHeat;

	float m_MeltTicksFloat;//当前熔炼原料进度，需要使用float
	float m_MeltTicksFloat2;
	float m_MeltTicksFloat3;

	int m_quality; //熔炉品质，等于熔炉itemId
	int m_temperature; //温度挡位 ，1，2，3递增

	bool isLoad; //是否load过变量
	std::vector<WCoord> m_huoyanPos;
	std::vector<WCoord> m_clearHuoyanPos;
	unsigned int m_bilu_wood_ent_id;
	unsigned int m_scene_halo_id[4];
	//tolua_end
}; //tolua_exports

#endif