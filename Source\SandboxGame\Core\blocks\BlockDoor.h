﻿
#ifndef __BLOCKDOOR_H__
#define __BLOCKDOOR_H__

#include "BlockMaterial.h"
#include "BlockVaryVerticalSlab.h"
#include "defdata.h"
class SocDoorContainer;
class DoorMaterial : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(DoorMaterial)
public:
	//tolua_begin
	DoorMaterial();
	virtual ~DoorMaterial();

	virtual void init(int resid);
	//virtual const char *getGeomName() override;
	virtual const char *getBaseTexName(char *texname, const BlockDef *def, int &gettextype);

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	void drawBox(World* pworld, const WCoord& min, const WCoord& max);

	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f);
	bool canBlocksMovement(World *pworld, const WCoord &blockpos);
	static bool isOpen(int blockdata);
	virtual BlockType BlockTypeId() { return BlockType_Door; }
	//virtual BlockDrawType getDrawType() override
	//{
	//	return BLOCKDRAW_GRASS;
	//}

	virtual int convertDataByRotate(int blockdata, int rotatetype) override;
	virtual void ignoreCheckUpBlockWhenNotify(bool ignore)
	{
		m_ignoreCheckUpBlock = ignore;
	}
	virtual char* getPhisicMeshBitWithWorld(World * pWorld, const WCoord &blockpos, int &precision, int &size);
	virtual char* getPhisicMeshBit(BaseSection *psection, const WCoord &blockpos) override;
	static int ParseDoorDataInVehicle(VehicleWorld *pworld, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror);

	static int ParseDoorData(const BaseSection* sectionData, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror);

	static int ParseDoorData(const SectionDataHandler* sectionData, const WCoord& blockpos, bool& isupper, bool& isopen, bool& mirror);
	virtual WCoord getDownDoorPos(World* pworld, const WCoord& blockpos);
	virtual WCoord getDownDoorPos(const BaseSection* sectionData, const WCoord& blockpos);
	//tolua_end
public:
	int ParseDoorData(World* pworld, const WCoord& blockpos, bool& isupper, bool& isopen, bool& mirror);
	void sendBroadCast(World* pworld, const WCoord& blockpos);
private:
	void onPoweredBlockChange(World *pworld, const WCoord &blockpos, bool open);
	//int ParseDoorData(BaseSection *psection, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror);
	//int ParseDoorData(World *pworld, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror);

	virtual void initGeomName() override;
	virtual void initDrawType() override;
protected:
	//RenderBlockMaterial *m_UpperMtl;
	unsigned int m_upperMtlIndex = UINT_MAX;
	bool m_ignoreCheckUpBlock;
}; //tolua_exports

class BlockSimpleDoor : public DoorMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSimpleDoor)
public:
	//tolua_begin
	virtual void init(int resid) override;
	//virtual const char *getGeomName()
	//{
	//	return GetBlockDef()->Texture2.c_str();
	//}
	const char *getBaseTexName(char *texname, const BlockDef *def, int &gettextype)
	{
		gettextype = GETTEX_WITHDEFAULT;
		sprintf(texname, "%s", def->Texture1.c_str());
		return texname;
	}
	//tolua_end
private:
	virtual void initGeomName() override;
}; //tolua_exports



class BlockKeyDoor :public DoorMaterial
{
	DECLARE_BLOCKMATERIAL(BlockKeyDoor)
public:
	BlockKeyDoor();
	virtual ~BlockKeyDoor();
	virtual void init(int resid) override;

	const char* getBaseTexName(char* texname, const BlockDef* m_Def, int& gettextype)
	{
		gettextype = GETTEX_WITHDEFAULT;
		sprintf(texname, "%s", m_Def->Texture1.c_str());
		return texname;
	}
	virtual bool hasContainer() override
	{
		return true;
	}
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0));
private:
	virtual void initGeomName() override
	{
		m_geomName = "key_door";
	}
};

//放置在方块中间的门
class BlockCenterDoor : public BlockSimpleDoor
{
	DECLARE_BLOCKMATERIAL(BlockCenterDoor)
public:
	void GetCollideData(World* pworld, const WCoord& blockpos, WCoord& originPos, WCoord& minpos, WCoord& maxpos);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos) override;
	virtual void createPickData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos) override;
};

//
class BlockHighDoor : public BlockSimpleDoor
{

	DECLARE_BLOCKMATERIAL(BlockHighDoor)
public:
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0)) override;
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid) override;
	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
	//virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos);
	//virtual char* getPhisicMeshBitWithWorld(World* pWorld, const WCoord& blockpos, int& precision, int& size);
	//virtual char* getPhisicMeshBit(BaseSection* psection, const WCoord& blockpos) override;
};

class BlockSocDoor : public DoorMaterial //tolua_exports
{//tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSocDoor)
public:
	BlockSocDoor();
	virtual ~BlockSocDoor();
	virtual void init(int resid) override;

	const char* getBaseTexName(char* texname, const BlockDef* m_Def, int& gettextype)
	{
		gettextype = GETTEX_WITHDEFAULT;
		sprintf(texname, "%s", m_Def->Texture1.c_str());
		return texname;
	}
	virtual bool hasContainer() override
	{
		return true;
	}
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;

	virtual bool openDoor(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0));

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual void createBlockMeshPreview(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;

	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0));
	virtual int getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player) override;
	virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type,float damage) override;
	virtual int getBlockHP(World* pworld, const WCoord& blockpos) override;
	virtual bool getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf = false) override;
	virtual WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata = -1);
	virtual void ShowBlockCrack(World* pworld, const WCoord& blockpos);
	virtual void ChangeBlockCrackModel(World* pworld, const WCoord& blockpos);
	virtual WorldContainer* repairContainer(const WCoord& blockpos, const int& data);
private:
	SocDoorContainer* getSocDoorContainer(World* pworld, const WCoord& blockpos);
	virtual void initGeomName() override
	{
		m_geomName = m_Def->Texture2.c_str();
	}
};//tolua_exports

class BlockSocAutoDoor : public ModelBlockMaterial //tolua_exports
{//tolua_exports
	DECLARE_SCENEOBJECTCLASS(BlockSocAutoDoor)
public:
	BlockSocAutoDoor();
	virtual ~BlockSocAutoDoor();
	virtual void init(int resid) override;

	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0));

	virtual bool findCorePos(World* pworld, const WCoord& blockpos, WCoord& corepos);

	//virtual char* getPhisicMeshBitWithWorld(World* pWorld, const WCoord& blockpos, int& precision, int& size);
	//virtual char* getPhisicMeshBit(BaseSection* psection, const WCoord& blockpos) override;
	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
	virtual char* getPhisicMeshBit(BaseSection* psection, const WCoord& blockpos) { return NULL; };
	virtual int getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs) { return 0; };
	//virtual int getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, std::vector<Rainbow::Vector3f>& verts, std::vector<unsigned int>& idxs);
	//获得对应世界位置的方块模型的顶点信息，转换为精确碰撞盒数据。type 为精度其值为4和10（现有精度是25，和10精度；越小精度越高运算越多.方块大小为100,25精度就是判断4次，10精度就是判断10次）
	virtual char* getPhisicMeshBitWithWorld(World* pWorld, const WCoord& blockpos, int& precision, int& size) { return NULL; };

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos) override;
	virtual void getMultiPhisicMeshVerts(Section* psection, const WCoord& posInSection, dynamic_array<TriangleBlockPhyData>& physDatas) override;
	virtual void createPickData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos) override;

	void drawBox(World* pworld, const WCoord& min, const WCoord& max);

	void OpenDoor(World* pworld, const WCoord& blockpos);
	void CloseDoor(World* pworld, const WCoord& blockpos);
	virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage) override;
	virtual int getBlockHP(World* pworld, const WCoord& blockpos) override;
	virtual bool getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf = false) override;
	virtual WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata = -1);
	virtual void ShowBlockCrack(World* pworld, const WCoord& blockpos);
	virtual void ChangeBlockCrackModel(World* pworld, const WCoord& blockpos);
	virtual BlockType BlockTypeId() { return BlockType_Door; }
	virtual void initGeomName() override
	{
		m_geomName = m_Def->Texture2.c_str();
	}

private:
	dynamic_array<UInt16> m_physDataIdxs;
};//tolua_exports

class BlockSocDoubleDoor : public BlockSocDoor //tolua_exports
{//tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSocDoubleDoor)
public:
	virtual void initGeomName() override
	{
		m_geomName = m_Def->Texture2.c_str();
	}

	BlockSocDoubleDoor();
	virtual ~BlockSocDoubleDoor();

	WorldContainer* getWorldContainer(World* pworld, const WCoord& blockpos);

	WCoord getContainerPos(World* pworld, const WCoord& blockpos);

	void ParseDoubleDoorData(World* pworld, const WCoord& blockpos,bool &isupper,bool &isopen, bool &mirror,int &dir);

	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid);

	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;

	virtual bool openDoor(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0)) override;

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual void createBlockMeshPreview(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0));
	virtual bool getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf = false) override;
	virtual WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata = -1);
};//tolua_exports

#endif