#include "ParticlesComponent.h"

#include "ClientActor.h"

#include "world.h"
#include "EffectManager.h"
#include "ActorLocoMotion.h"
#include "MpActorManager.h"
#include "GameNetManager.h"
#include "world_types.h"
#include "ClientInfoProxy.h"

using namespace MINIW;
using namespace Rainbow;


void ParticlesComponent::playParticles(ClientActor* pActor, const char *name, int liveticks, const WCoord *offset, bool sync)
{
	playParticlesEx(pActor, name, liveticks, 0, 0, offset, sync);
}

void ParticlesComponent::playParticlesEx(ClientActor* pActor, const char* name, int liveticks, float yaw /*= 0*/, float pitch /*= 0*/, const WCoord* offset /*= NULL*/, bool sync /*= true*/)
{
	char path[256];
	sprintf(path, "particles/%s", name);

	auto OwnerActor = pActor;
	//WCoord pos = getEyePosition();
	WCoord pos = OwnerActor->getLocoMotion() ? OwnerActor->getLocoMotion()->getPosition() : WCoord(0, -1, 0);
	if (offset) pos += *offset;

	auto World = OwnerActor->getWorld();
	if (World)
	{
		World->getEffectMgr()->playParticleEffectAsync(path, pos, liveticks, yaw, pitch, sync);
	}
}

void ParticlesComponent::playAvatarParticles(ClientActor* pActor, int id, int liveticks, const WCoord *offset, bool sync)
{
	auto OwnerActor = pActor;
	WCoord pos = OwnerActor->getLocoMotion() ? OwnerActor->getLocoMotion()->getPosition() : WCoord(0, -1, 0);
	if (offset) pos += *offset;

	auto World = OwnerActor->getWorld();
	if (World)
	{
		char path[256];
		sprintf(path, "entity/avatar/1000_%d/%d.ent", id, id);
		//char zipPath[512] = { 0 };
		// snprintf(zipPath, sizeof(zipPath), "%sdata/http/productions/1000_%d.zip", GetClientInfoProxy()->getDataDir(), id);

		World->getEffectMgr()->playParticleEffectAsync(path, pos, liveticks, 0, 0, sync, 16, 0, Rainbow::ColorQuad(255, 255, 255, 255), NULL);
	}
}
