#ifndef __COSUME_ACCUM_COMPONENT_H__
#define __COSUME_ACCUM_COMPONENT_H__

#include <vector>
#include <map>
#include <string>
#include "ActorComponent_Base.h"


class ClientPlayer;
class World;
class CosumeAccumItem;
class LuaAccumItem;

class CosumeAccumComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(CosumeAccumComponent)
public:
	CosumeAccumComponent();
	virtual ~CosumeAccumComponent();	
	//tolua_begin
	bool addItem(const char* name, int itemid = 0);
	bool addLuaItem(const char* filepath ,int itemid = 0);
	//tolua_end
	//void removeItem();
	static void clearRegisters();
protected:
	/* ????? */
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner) override;
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner) override;

	virtual void initItems(ClientPlayer* playerOwner);
	void clearItems();
private:
	static void initRegisters();
	template<typename T>
	static bool RegisterCreator(const char* type);

	std::vector<CosumeAccumItem*> m_items;
	static std::map<std::string, std::function<CosumeAccumItem*()>> m_registers;
}; //tolua_exports


#endif