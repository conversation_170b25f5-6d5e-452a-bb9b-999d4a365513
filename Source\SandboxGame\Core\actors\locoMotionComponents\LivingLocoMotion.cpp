
#include "LivingLocoMotion.h"
#include "ClientActor.h"
//#include "ClientMob.h"
#include "ActorBody.h"
#include "ActorAttrib.h"
#include "world.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "DefManagerProxy.h"
#include "OgreUtils.h"
//#include "OgreMath.h"
#include "WorldManager.h"
#include "LuaInterfaceProxy.h"
#include "special_blockid.h"
#include "InputInfo.h"
#include "OgrePhysXManager.h"
#include "ActorVillager.h"
#include "ActorHorse.h"
#include "BlockMaterialMgr.h"
#include "PlayerAttrib.h"
#include "IWorldConfigProxy.h"
#include "BlockEnv_Constants.h"
#include "ClientActorFuncWrapper.h"
#include "ActionAttrStateComponent.h"
#include "RiddenComponent.h"
#include "SoundComponent.h"
#include "navigationpath.h"

#include "FireBurnComponent.h"
#include "AttackedComponent.h"
#include "BlockEnvEffectsComponent.h"
#include "OgreEntity.h"
#include "ChargeJumpComponent.h"
#include "PlayerCheat.h"
#include "BlockScene.h"
//#include "special_blockid.h"
//#include "LivingAttrib.h"

using namespace MINIW;
using namespace Rainbow;

static float MOB_JUMPVEL = 50.0f;
static int JUMP_BEGINDIST = 60;

IMPLEMENT_COMPONENTCLASS(LivingLocoMotion)
LivingLocoMotion::LivingLocoMotion()
{
	m_MoveForward = 0;
	m_MoveStrafing = 0;
	m_MoveTargetSpeed = -1.0f;
	m_Motion = Vector3f::zero;
	m_bBoundJump = false;
	m_bIsAIJumping = false;

	m_UseMoveType.Set(0);
	m_UseGravity.Set(0);

	m_JumpTarget = WCoord(0, -1, 0);

	m_nLocType = LandLoc;
	setMoveAbilityFlag(LandLoc, true);

	m_ColliderMotion = Vector3f::zero;

	//---------------------------------------
	m_StopDist = 20;
	m_HasTarget = false;
	m_FearPlayerId = -1;
	m_SpeedMultiple = 1;
	m_MaxSteeringForce = 2000;
	obstacleAvoidanceWeight = 1.0f;
	surfaceAvoidanceWeight = 1.0f;
	fleeWeight = 1.0f;
	wanderWeight = 1.0f;
	pursuitWeight = 1.0f;
	m_smoother = ENG_NEW(Smoother<Vector3f>)(8, Vector3f::zero);
	m_SmoothRotationOn = true;
	m_BehaviorFlag = 0;
	m_StepTick = 0;
	m_SyncSteps = 0;
	m_SyncYaw = 0.0f;
	m_SyncPitch = 0.0f;
	isAutoStop = false;
	m_isLeader = NULL;
	m_TeamNo = 0;
	m_TeamNum = 0;
	//---------------------------------------
	m_Velocity = Rainbow::Vector3f::zero;
	m_SpeedInAir = 0.0f;
	m_SpeedInWater = 0.0f;
	m_jumpRote = true;
}
LivingLocoMotion::~LivingLocoMotion()
{
	ENG_DELETE(m_smoother);
}

void LivingLocoMotion::UpdateClimbTree()
{
	if (m_nLocType == LandLoc || m_nLocType == LandLoc_CanSwin)
	{
		if (getOwnerActor()->getNavigator() == nullptr) return;
		if (!getOwnerActor()->getNavigator()->noPath())
		{
			WCoord dir = m_MoveTarget - m_Position;
			m_Motion.y = dir.y > 0 ? 10.0f : -10.0f;
			m_Motion.x = dir.x > 0 ? 10.0f : -10.0f;
			m_Motion.z = dir.z > 0 ? 10.0f : -10.0f;
			if (Abs(dir.y) < 1.0f)
			{
				dir.y = 0;
				m_Motion.y = 0;
			}
			if (Abs(dir.y) < 10.0f)
			{
				m_Motion.y = dir.y;
			}
			if (Abs(dir.x) < 10.0f)
			{
				m_Motion.x = dir.x;
			}
			if (Abs(dir.z) < 10.0f)
			{
				m_Motion.z = dir.z;
			}
			if (dir.y != 0 || dir.x == 0) m_Motion.x = 0;
			if (dir.y != 0 || dir.z == 0) m_Motion.z = 0;
			CollideAABB box;
			getCollideBox(box);
			WCoord mvec = getIntegerMotion(m_Motion);
			//mvec = m_pActor->getWorld()->moveBoxWalk(box, mvec, 50);
			addRealMove(box, mvec);
			if (!getOwnerActor()->getBody()->hasAnimPlaying(SEQ_RUN))
				getOwnerActor()->playAnim(SEQ_RUN);
			WCoord pos;
			pos = getOwnerActor()->getPosition();
			int x = CoordDivBlock(pos.x);
			int y = CoordDivBlock(pos.y);
			int z = CoordDivBlock(pos.z);
			int blockID = getOwnerActor()->getWorld()->getBlockID(x, y, z);
			if (IsAirBlockID(blockID) && IsLeavesBlockID(getOwnerActor()->getWorld()->getBlockID(x, y + 1, z)))
				getOwnerActor()->setReverse(true);
			else
				getOwnerActor()->setReverse(false);

			for (int i = DIR_NEG_X; i < DIR_NEG_Y; i++)
			{
				WCoord dir = NeighborCoord(CoordDivBlock(pos), i);
				if (IsWoodBlockID(getOwnerActor()->getWorld()->getBlockID(dir)))
				{
					WCoord totarget = BlockCenterCoord(dir) - getOwnerActor()->getPosition();
					float yaw, pitch;
					Direction2PitchYaw(&yaw, &pitch, (totarget).toVector3());
					getOwnerActor()->SyncPosition(pos, yaw, pitch);
				}
			}
		}
	}
}
void LivingLocoMotion::UpdateClimb()
{
	if (getOwnerActor()->getNavigator())
	{
		if (!getOwnerActor()->getNavigator()->noPath())
		{
			WCoord dir = m_MoveTarget - m_Position;
			m_Motion.y = dir.y > 0 ? 30.0f : -20.0f;
			m_Motion.x = dir.x > 0 ? 10.0f : -10.0f;
			m_Motion.z = dir.z > 0 ? 10.0f : -10.0f;
			if (Abs(dir.y) < 1.0f)
			{
				dir.y = 0;
				m_Motion.y = 0;
			}
			if (Abs(dir.y) < 20.0f)
			{
				m_Motion.y = dir.y;
			}
			if (Abs(dir.x) < 20.0f)
			{
				m_Motion.x = dir.x;
			}
			if (Abs(dir.z) < 20.0f)
			{
				m_Motion.z = dir.z;
			}
			if (dir.y != 0 || dir.x == 0) m_Motion.x = 0;
			if (dir.y != 0 || dir.z == 0) m_Motion.z = 0;
			CollideAABB box;
			getCollideBox(box);
			WCoord mvec = getIntegerMotion(m_Motion);
			//mvec = m_pActor->getWorld()->moveBoxWalk(box, mvec, 50);
			addRealMove(box, mvec);
			if (!getOwnerActor()->getBody()->hasAnimPlaying(SEQ_RUN))
				getOwnerActor()->playAnim(SEQ_RUN);
			WCoord pos;
			pos = getOwnerActor()->getPosition();
			int x = CoordDivBlock(pos.x);
			int y = CoordDivBlock(pos.y);
			int z = CoordDivBlock(pos.z);
			for (int i = DIR_NEG_X; i < DIR_NEG_Y; i++)
			{
				WCoord dir = NeighborCoord(CoordDivBlock(pos), i);

				WCoord totarget = BlockCenterCoord(dir) - getOwnerActor()->getPosition();
				float yaw, pitch;
				Direction2PitchYaw(&yaw, &pitch, (totarget).toVector3());
				getOwnerActor()->SyncPosition(pos, yaw, pitch);
			}
		}
	}
}
bool LivingLocoMotion::isMovementStop()
{
	if (getOwnerActor())
	{
		auto attrib = dynamic_cast<LivingAttrib*>(getOwnerActor()->getAttrib());
		if (attrib)
		{
			if (attrib->hasStatusEffect(STATUS_EFFECT_DROP) ||
				attrib->hasStatusEffect(STATUS_EFFECT_PERCIPIENCE)) //无法移动下落)
			{
				return true;
			}
		}
	}
	return false;
}

static float s_InWaterForce = 15.0f;
int GetBoatDepthInLiquid(ActorLocoMotion* locomove, bool onlywater)
{
	BlockMaterial* watermtl = g_BlockMtlMgr.getMaterial(BLOCK_STILL_WATER);
	BlockMaterial* lavamtl = g_BlockMtlMgr.getMaterial(BLOCK_STILL_LAVA);
	CollideAABB box, tmpbox;
	locomove->getCollideBox(box);

	int SEGMENTS = 10;
	int inwater_h = 0;
	for (int i = 0; i < SEGMENTS; i++)
	{
		tmpbox.pos = WCoord(box.minX(), box.minY() + box.dim.y * i / SEGMENTS, box.minZ());
		tmpbox.dim = WCoord(box.dim.x, box.dim.y / SEGMENTS, box.dim.z);
		if (locomove->m_pWorld->isBoxInMaterial(tmpbox, watermtl))
		{
			inwater_h += BLOCK_SIZE / SEGMENTS;
		}
		else if (!onlywater && locomove->m_pWorld->isBoxInMaterial(tmpbox, lavamtl))
		{
			inwater_h += BLOCK_SIZE / SEGMENTS;
		}
	}

	return inwater_h;
}

static float CalLiquidFloatForce(ActorLocoMotion* locomove, bool onlywater, float curmy)
{
	int inwater_h = GetBoatDepthInLiquid(locomove, onlywater);
	if (inwater_h < BLOCK_SIZE)
	{
		return curmy + s_InWaterForce * (inwater_h / BLOCK_FSIZE);
	}
	else
	{
		if (curmy < 0) curmy /= 2.0f;

		return curmy + s_InWaterForce;
	}
}

void LivingLocoMotion::tick()
{
	if (!getOwnerActor())
	{
		return;
	}

	if (m_ActorRoleController)
		checkPhysWorld();

	//处理【【mod玩法】丧尸攀爬AI同步异常】https://www.tapd.cn/22897851/bugtrace/bugs/view/1122897851001109588
	if (m_pWorld && m_pWorld->isRemoteMode()&& m_nLocType == Climbing)
	{
		return;
	}

	if (m_nLocType == LandLoc || m_nLocType == OnWater || m_nLocType == Climbing || m_nLocType == LandLoc_CanSwin)
	{
		ActorLocoMotion::tick();

		m_Motion *= getMoveReduceRatio();
		if (Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
		//if(Abs(m_Motion.y) < 0.5f) m_Motion.y = 0;
		if (Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;
		bool climbing = false;
		if (isMovementBlocked() || isMovementStop())
		{
			clearTarget();
			m_MoveStrafing = 0;
			m_MoveForward = 0;
			m_isJumping = false;
		}
		else
		{
			if (m_MoveTargetSpeed >= 0)
			{

				updateMoveTarget(&climbing);
			}

		}
		checkVortex();
		if (!getOwnerActor()->getClimbing())
		{
			if (climbing && m_nLocType == Climbing)
			{
				UpdateClimb();
			}
			else
			{
				updateJumping();
				LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getOwnerActor()->getAttrib());
				if (pLivingAttrib && pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE) && m_CollideMoveTicks <= 0) //有碰撞可移动
				{
					m_MoveStrafing = 0;
					m_MoveForward = 0;
				}
				else
				{
					m_MoveStrafing *= getMoveReduceRatio();
					m_MoveForward *= getMoveReduceRatio();
				}
				this->GetRiddenActorInputInfo(m_MoveStrafing, m_MoveForward);
				moveEntityWithHeading(m_MoveStrafing, m_MoveForward);
				auto functionWrapper = getOwnerActor()->getFuncWrapper();
				bool isCanFly = functionWrapper ? functionWrapper->getCanFly() : false;
				if (isCanFly)
				{
					if (m_Motion.y < 0) m_Motion.y *= 0.6f;
				}
			}

		}
		else
		{
			UpdateClimbTree();
		}

		collideWithNearbyActors();

		if (m_nLocType == OnWater)
		{
			OnWaterTick();
		}
	}
	else if (m_nLocType == FlyLoc)
	{
		WCoord curpos = m_Position;
		if (m_pWorld->isRemoteMode())
		{
			if (m_SyncSteps > 0)
			{
				m_RotateYaw += WrapAngleTo180(m_SyncYaw - m_RotateYaw) / m_SyncSteps;
				m_RotationPitch += WrapAngleTo180(m_SyncPitch - m_RotationPitch) / m_SyncSteps;
				m_Position += (m_SyncPos - m_Position) / m_SyncSteps;
				m_SyncSteps--;
			}
			else
			{
				m_Position = m_SyncPos;
			}
			syncPhysActorPosByLocPos();

			/*auto pEntity = getOwnerActor()->getBody()->getEntity();
			if (pEntity && pEntity->GetMainModel())
			{
				pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
			}*/
			return;
		}

		if (getOwnerActor()->needClear()) return;


		

		updateRidden();
		updateBindActor();

		handleWaterMovement();

		auto functionWrapper = getOwnerActor()->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(0);
		}

		//-------------ActorLocoMotion tick end------------------


		//-------------LivingLocoMotion tick start------------------
		m_Motion *= 0.98f;
		if (Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
		if (Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;

		if (isMovementBlocked())
		{
			//clearTarget();
			m_isJumping = false;
		}
		else
		{
			if (m_MoveTargetSpeed >= 0) updateMoveTarget();
		}
		checkVortex();


		
		auto RidComp = getOwnerActor()->getRiddenComponent();
		ClientActor* riddenactor = NULL;
		bool isPlayerRiddenMove = false;
		if (RidComp && RidComp->isRidden() && RidComp->getRiddenControl())
		{
			ClientPlayer* pPlayer = getOwnerActor()->ToCast<ClientPlayer>();
			if (pPlayer)
			{
				isPlayerRiddenMove = true;
				ClientActor* riding = RidComp->getRidingActor();
				if (riding)
				{
					curpos = riding->getPosition();
				}
				this->GetRiddenActorInputInfo(m_MoveStrafing, m_MoveForward);
				//具体移动逻辑
				moveEntityWithHeading(m_MoveStrafing, m_MoveForward);
			}
		}

		if (!isPlayerRiddenMove)
		{
			//具体移动逻辑
			moveEntityWithDirection();
		}

		if (m_MoveTargetSpeed > 0)
		{
			curpos.y = CoordDivBlock(curpos.y + BLOCK_SIZE / 2) * BLOCK_SIZE;
			WCoord totarget = m_MoveTarget - curpos;
			Vector3f vec = totarget.toVector3();
			setMoveDir(vec);//转向控制
			// 转向同步
			float yaw, pitch;
			Direction2PitchYaw(&yaw, &pitch, (totarget).toVector3());
			getOwnerActor()->SyncPosition(m_MoveTarget, yaw, (float)pitch, 10.0f);
			m_NavigationRotateYaw = m_RotateYaw;
			m_NavigationRotationPitch = pitch;
		}

		collideWithNearbyActors();
		//-------------LivingLocoMotion tick end------------------
	}
	else if (m_nLocType == AquaticLoc)
	{
		bool isStop = false;
		if (!GetOwner()) return;
		ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
		if (m_owner && getOwnerActor()->getAttrib())
		{
			LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(m_owner->getAttrib());
			if (pLivingAttrib && pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
			{
				isStop = true;
			}
		}

		if (m_pWorld->isRemoteMode())
		{
			//Do position& rotation sync
			if (m_SyncSteps > 0)
			{
				m_Position += (m_SyncPos - m_Position) / m_SyncSteps;
				m_RotateYaw += WrapAngleTo180(m_SyncYaw - m_RotateYaw) / m_SyncSteps;
				m_RotationPitch += WrapAngleTo180(m_SyncPitch - m_RotationPitch) / m_SyncSteps;

				m_SyncSteps--;
			}
			else
			{
				m_Position = m_SyncPos;
			}
			syncPhysActorPosByLocPos();
			auto pEntity = getOwnerActor()->getBody()->getEntity();
			if (pEntity && pEntity->GetMainModel() && !isStop)
			{
				pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
			}
			return;
		}

		//ActorLocoMotion::tick();

		//-------------ActorLocoMotion tick start------------------

		if (getOwnerActor()->needClear()) return;
		checkVortex();
		updateRidden();
		updateBindActor();

		//Check actor in water
		WCoord contract = WCoord(1, 40, 1);
		if (contract.y > m_BoundHeight / 2 - 1) contract.y = m_BoundHeight / 2 - 1;

		WCoord minpos = m_Position - WCoord(m_BoundSize / 2, 0, m_BoundSize / 2) + contract;
		WCoord maxpos = m_Position + WCoord(m_BoundSize / 2, m_BoundHeight, m_BoundSize / 2) - contract;

		Vector3f flowmotion;
		auto isInWater = m_pWorld->getFluidFlowMotion(minpos, maxpos, flowmotion);
		if (!isInWater)
		{
			flowmotion = Vector3f(0, 0, 0);
			WCoord curPos;
			curPos = getPosition();
			int id = getOwnerActor()->getWorld()->getBlockID(CoordDivBlock(curPos));
			///如果此时位置在水下，那么四周加头顶就一定有一个水方块。
			for (int dir = 0; dir < 6; dir++)
			{
				if (!isInWater && dir != 4 && IsWaterBlockID(getOwnerActor()->getWorld()->getBlockID(NeighborCoord(CoordDivBlock(curPos), dir))))
				{
					isInWater = true;
				}
			}
		}
		if (isInWater)
		{
			m_Motion += flowmotion;
			m_InWater = true;

			auto functionWrapper = getOwnerActor()->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setFallDistance(0);
			}


			auto FireBurnComp = getOwnerActor()->getFireBurnComponent();
			if (FireBurnComp)
			{
				FireBurnComp->setFire(0, 0);
			}

		}
		else
		{
			m_InWater = false;
			if (m_ColliderMotion.y > 0)   //不在水中时，m_ColliderMotion.y清零，避免鱼飞起来了；
				m_ColliderMotion.y = 0;
		}

		//-------------ActorLocoMotion tick end------------------


		//-------------LivingLocoMotion tick start------------------
		m_Motion *= 0.98f;
		if (Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
		//if(Abs(m_Motion.y) < 0.5f) m_Motion.y = 0;
		if (Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;

		if (isMovementBlocked())
		{
			//clearTarget();
			m_isJumping = false;
		}
		else
		{
			if (m_MoveTargetSpeed >= 0) updateMoveTarget();
		}
		moveEntityWithDirection();

		collideWithNearbyActors();
		auto pEntity = getOwnerActor()->getBody()->getEntity();
		if (pEntity && pEntity->GetMainModel() && !isStop)
		{
			pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
		}
		if (isAutoStop && m_MoveTarget.distanceTo(m_Position) < 20 && isBehaviorOn(Wander))
		{
			resetVelocity();
			setBehaviorOff(Wander);
		}
		//-------------LivingLocoMotion tick end------------------
	}
}

void LivingLocoMotion::collideWithNearbyActors()
{
    OPTICK_EVENT();
	CollideAABB box;
	getOwnerActor()->getCollideBox(box);

	ClientPlayer* p = getOwnerActor()->ToCast<ClientPlayer>();
	if (p && p->getOPWay() == PLAYEROP_WAY_BASKETBALLER && p->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
	{
		box.expand(BLOCK_SIZE, 0, BLOCK_SIZE);
	}
	else
	{
		box.expand(20, 0, 20);
	}

	static std::vector<IClientActor*>actors;
	actors.clear();
	m_pWorld->getActorsInBoxExclude(actors, box, getOwnerActor());

	bool hasPlayerCollision = hasCollisionBetweenPlayers();
	for(size_t i=0; i<actors.size(); i++)
	{
		if (dynamic_cast<ClientActor*>(actors[i])->canBePushed())
		{
			// 关闭玩家碰撞时，两个玩家不计算碰撞
			bool isplayer = actors[i]->isPlayer();
			bool canCollide = true;
			if (!hasPlayerCollision && p && isplayer)
			{
				canCollide = false;
			}

			if (canCollide && getOwnerActor())
			{
				getOwnerActor()->collideWithActor(actors[i]->GetActor());
			}
		}
	}

	if (m_nLocType == FlyLoc || m_nLocType == AquaticLoc)
	{
		if (actors.size() == 0)
		{
			m_ColliderMotion = Vector3f::zero;
		}
	}
}

void LivingLocoMotion::gotoPosition(const WCoord &pos, float yaw, float pitch)
{
	ActorLocoMotion::gotoPosition(pos, yaw, pitch);
	m_MoveForward = m_MoveStrafing = 0;
	m_isJumping = false;
	clearTarget();
}

bool LivingLocoMotion::prepareJump(int &cooldown)
{
	static_cast<ActorLiving *>(getOwnerActor())->playAnim(SEQ_JUMP);
	cooldown = 10;
	return m_OnGround;
}

void LivingLocoMotion::setMoveForward(float vel)
{
	m_MoveForward = vel;
}

void LivingLocoMotion::setMoveStrafing(float vel)
{
	m_MoveStrafing = vel;
}

const float DEFAULT_MOVEDCAY = 0.91f;
float CalMoveDecay(World *pworld, const WCoord &pos, ClientPlayer *player, bool onground)
{
	//计算摩擦力
	int blockid = -1;
	int blockdata = 0;
	if (onground)
	{
		//脚底下的方块
		// 
		//用于判断苔藓
		blockid = pworld->getBlockID((CoordDivBlock(pos + 2)));//DownCoord
		blockdata = pworld->getBlockData((CoordDivBlock(pos + 2)));
		if ( blockid!=BLOCK_MOSS|| blockid!= BLOCK_MOSS_HUGE)
		{
			blockid = pworld->getBlockID((CoordDivBlock(pos - 2)));//DownCoord
			blockdata = pworld->getBlockData((CoordDivBlock(pos - 2)));
		}
		if (blockid <= 0)
		{
			blockid = pworld->getBlockID((CoordDivBlock(pos - 10)));//DownCoord
			blockdata = pworld->getBlockData((CoordDivBlock(pos - 10)));
		}
		
			
	}
	else if (player && player->getCurOperate() == PLAYEROP_TACKLE)
	{
		blockid = BLOCK_GRASS;
	}

	if (blockid >= 0)
	{
		float movedecay = DEFAULT_MOVEDCAY;
		BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
		auto mtl = g_BlockMtlMgr.getMaterial(blockid);
		if (def)
		{
			float s = def->Slipperiness * 0.6f;
			if (mtl && mtl->getBlockSlipperiness(blockdata))
			{
				s = mtl->getBlockSlipperiness(blockdata) * 0.6f;
			}
			if (pworld->getBlockRaining(CoordDivBlock(pos)))
				s *= 1.2f;

			if (s > 0) movedecay *= s;
		}
		return movedecay;
	}
	else return  -1;
}

void LivingLocoMotion::doJumpTarget()
{
	if (!m_pWorld)
		return;

	WCoord& from = getPosition();
	WCoord& to = m_JumpTarget;

	float yaw, pitch;
	yaw = m_RotateYaw;
	pitch = m_RotationPitch;
	WCoord totarget = to - from;
	//// 转向
	if (m_jumpRote)
	{
		Direction2PitchYaw(&yaw, &pitch, (totarget).toVector3());
	}
	getOwnerActor()->SyncPosition(m_MoveTarget, (int)yaw, (int)pitch, 10.0f);
	m_NavigationRotateYaw = yaw;
	m_NavigationRotationPitch = pitch;

	Vector3f vec = totarget.toVector3();
	setMoveDir(vec);

	float maxPosY = to.y + BLOCK_SIZE;
	if (maxPosY <= from.y)
		maxPosY = from.y+BLOCK_SIZE;

	float g = m_pWorld->getGravity();
	float velocityY = Rainbow::Sqrt(2 * g * (maxPosY - from.y));
	
	float t1 = velocityY / g;
	float t2 = Rainbow::Sqrt(2 * (maxPosY - to.y) / g);

	Vector3f dir = Vector3f(to.x - from.x, 0, to.z - from.z);
	float offset = -40.0f;
	float dist = dir.Length();
	if (dist > 150)
		offset = 30.0f;

	float movedecay = CalMoveDecay(m_pWorld, m_Position, NULL, true);
	float v = 2 * (dist + offset) / (t1 + t2) + Pow(movedecay, (t1 + t2));
	//float v = 2 * (dist + offset) / (t1 + t2);
	/*float movedecay = CalMoveDecay(m_pWorld, m_Position, NULL, true);
	v /= movedecay;*/

	dir.Normalize();

	m_Motion.x = dir.x * v;
	m_Motion.z = dir.z * v;
	m_Motion.y = velocityY;
	//m_Motion /= 0.98;

	m_JumpTarget = WCoord(0, -1, 0);
	setJumping(false);
	m_enforcedJumpTarget = false;
}
BlockEnvEffectsComponent* LivingLocoMotion::getBlockEnvEffectsComponent(bool force) {
    if (!m_bBlockEnvEffectsComponentInited || force) {
        m_bBlockEnvEffectsComponentInited = true;
        ClientActor* pActor = getOwnerActor();
        m_pBlockEnvEffectsComponent = pActor ? pActor->getBlockEnvEffectsComponent() : nullptr;
    }
    return m_pBlockEnvEffectsComponent;
}

void LivingLocoMotion::OnWaterTick()
{
	//------------水面移动后处理 begin------------------------
	float motionlen = Rainbow::Sqrt(m_Motion.x * m_Motion.x + m_Motion.z * m_Motion.z);

	m_Motion.y = CalLiquidFloatForce(this, true, m_Motion.y);

	float newlen = Rainbow::Sqrt(m_Motion.x * m_Motion.x + m_Motion.z * m_Motion.z);

	const float MAX_MOTION_LEN = 52.0f;
	if (newlen > MAX_MOTION_LEN)
	{
		float t = MAX_MOTION_LEN / newlen;
		m_Motion.x *= t;
		m_Motion.z *= t;
	}

	m_RotationPitch = 0.0f;
	Rainbow::Vector3f dpos;
	PitchYaw2Direction(dpos, m_RotateYaw, m_RotationPitch);
	float Dirlen = Rainbow::Sqrt(m_Motion.x * m_Motion.x + m_Motion.z * m_Motion.z);
	dpos = dpos * Dirlen;
	m_Motion.x = dpos.x;
	m_Motion.z = dpos.z;

	//陆地上暂时无法移动
	if (GetBoatDepthInLiquid(this, true) <= 20 && m_OnGround)
	{
		m_Motion *= 0.0f;
	}
	//------------水面移动后处理 end-----------------------------
}

/**
 * @brief 获取跳跃高度
 * 
 * @return float 跳跃高度
 */
float LivingLocoMotion::getJumpHeight(){
	float base = MOB_JUMPVEL;
	auto owner = getOwnerActor();
	if (owner && owner->getAttrib()) {
		base = owner->getAttrib()->getSpeedAtt(Actor_Jump_Speed);
		if (base < 0.0f) { base = MOB_JUMPVEL; }

		auto pLivingAtt = dynamic_cast<LivingAttrib*>(owner->getAttrib());
		if (pLivingAtt && pLivingAtt->isNewStatus()) {
			base += pLivingAtt->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_JUMP_SPEED, base);
		}
		base += pLivingAtt->getEnchantJumpHeightInc();//renjie 增加跳跃能力附魔
	}
	return owner ? (owner->getReverse()? -base: base) : base;
}

void LivingLocoMotion::doJump()
{
	if (m_JumpTarget.y > 0)
	{
		doJumpTarget();
	}
	else
	{
		float base = getJumpHeight();
		m_Motion.y = base;
	}
	
}

float LivingLocoMotion::getGravityFactor(bool up)
{
	return 1.0f;
}

void LivingLocoMotion::updateJetpackMove()
{
	auto RidComp = getOwnerActor()->getRiddenComponent();
	if (m_isJumping && !(RidComp && RidComp->isRiding()))
	{
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(getOwnerActor());
		if (player)
		{
			m_Motion.y += 13;
		}
	}
}
float LivingLocoMotion::getWaterJumpingSpeed()
{
	return GetLuaInterfaceProxy().get_lua_const()->livingWaterJumpBaseSpeed;
}

void LivingLocoMotion::updateJumping(bool isplayerrigidbody /* = false */)
{
	if(m_JumpingTicks > 0) m_JumpingTicks--;

	auto RidComp = getOwnerActor()->getRiddenComponent();
	if (!GetOwner()) return ;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return ;
	auto chargeComp = m_owner->getChargeJumpComponent();
	bool bSleeping = false;
	bool bRestInBed = false;
	bool bChargeEnable = false;
	ClientPlayer *player = dynamic_cast<ClientPlayer *>(getOwnerActor());
	if (player)
	{
		bSleeping = player->isSleeping();
		bRestInBed = player->isRestInBed();
		bChargeEnable = chargeComp && chargeComp->isChargeJumpEnabled();
	}
	if((m_isJumping || m_bIsAIJumping) && !(RidComp && RidComp->isRiding()) &&(!bSleeping && !bRestInBed) && !bChargeEnable && m_CurMovementState != MovementState::OnSlopeFall)
	{
		if (m_enforcedJumpTarget && m_JumpTarget.y > 0) //新需求：无关身处环境，都得跳向目标位置（code by 李元星）
		{
			if (m_JumpingTicks == 0)
			{
				doJumpTarget();
				int cooldown;
				prepareJump(cooldown);
				m_JumpingTicks = cooldown;
			}
		}
		else
		{
			if (m_InWater || m_InLava || m_InHoney || isOnLadder()) {
				if (getOwnerActor()->getReverse())
					m_Motion.y -= getWaterJumpingSpeed();
				else
					m_Motion.y += getWaterJumpingSpeed();
			}
			else {
				//耗牛的逻辑需要修改，先修改耗牛原地跳跃问题 
				ClientMob* currMob = dynamic_cast<ClientMob*>(getOwnerActor());
				if (currMob != nullptr && (currMob->m_Def->ID == 3912 || currMob->m_Def->ID == 3913)) {
					if (m_Motion.x == 0 && m_Motion.z == 0 || m_MoveTargetSpeed < 0) {
						int cooldown = 30;
						m_JumpingTicks = cooldown;
						return;
					}
				}
				int cooldown;
				if (m_JumpingTicks == 0 && prepareJump(cooldown))
				{
					doJump();
					m_JumpingTicks = cooldown;
				}
			}
		}
	}
	else
	{
		if(!isplayerrigidbody)
			m_JumpingTicks = 0;
	}
}

void LivingLocoMotion::autoStep()
{
	PlayerControl *player = dynamic_cast<PlayerControl *>(getOwnerActor());

	if (player && player->m_InputInfo && player->m_InputInfo->jump)
	{
		m_Motion.y = 40.0f * (1.0f + player->getGeniusValue(GENIUS_TWOJUMP));
	}
	else
	{
		m_Motion.y = 40.0f * (1.0f);
	}


}

bool LivingLocoMotion::updateMoveTarget(bool* climbing)
{
	if (m_nLocType == LandLoc || m_nLocType == OnWater || m_nLocType == Climbing || m_nLocType == LandLoc_CanSwin)
	{
		if (!getOwnerActor())
		{
			return false;
		}
		if (!getOwnerActor()->getAttrib())
		{
			return false;
		}
		setMoveForward(0);

		if(m_nLocType != LandLoc_CanSwin)
			setJumping(false);

		WCoord curpos = m_Position;
		auto RidComp = getOwnerActor()->getRiddenComponent();
		if (RidComp && RidComp->isRiding())
		{
			ClientActor* riding = RidComp->getRidingActor();
			if (riding)
			{
				curpos = riding->getPosition();
			}
		}

		curpos.y = CoordDivBlock(curpos.y + BLOCK_SIZE / 2) * BLOCK_SIZE;

		//看向目的地的Y轴不进行舍入，解决生物在某些情况下（如在水中）头疯狂抽搐的问题 by：Jeff
		//curpos.y = curpos.y + BLOCK_SIZE / 2;

		WCoord totarget = m_MoveTarget - curpos;
		if (totarget.lengthSquared() < 100)
		{
			clearTarget();
			return true; //到达目标
		}
		if (climbing)
		{
			if (totarget.y >= 100)
			{
				*climbing = true;
			}
		}

		

		/*PlayerControl* pPlayerCtrl = dynamic_cast<PlayerControl*>(getOwnerActor());
		if (pPlayerCtrl)
		{
			pPlayerCtrl->applyCurCameraConfig();
		}*/

		ClientPlayer* player = getOwnerActor()->ToCast<ClientPlayer>();
		bool playerflying = (player && getOwnerActor()->getFlying());
		bool bInWater = ((m_InWater || m_InLava || m_InHoney) && !playerflying);//在水中或者岩浆中，非飞行模式

		Vector3f vec = totarget.toVector3();
		setMoveDir(vec);//转向控制
		float yaw, pitch;
		Direction2PitchYaw(&yaw, &pitch, (totarget).toVector3());
		m_NavigationRotateYaw = m_RotateYaw;
		m_NavigationRotationPitch = pitch;
		getOwnerActor()->SyncPosition(m_MoveTarget, yaw, (float)pitch, 10.0f);

		float speed = getOwnerActor()->getAttrib()->getMoveSpeed(bInWater ? Actor_Swim_Speed : Actor_Walk_Speed) * m_MoveTargetSpeed;
		auto funcWrapper = getOwnerActor()->getFuncWrapper();
		if (funcWrapper)
		{
			funcWrapper->setAIMoveSpeed(speed);
		}
		if (player)
		{
			player->setMoveControlPitch(pitch);
			player->setMoveControlYaw(yaw);
			player->addMoveStatus(IMT_Forward);
		}
		setMoveForward(speed);
		/*if (getOwnerActor()->getObjType() == OBJ_TYPE_VILLAGER)
		{
			ActorVillager *village = dynamic_cast<ActorVillager *>(getOwnerActor());
			if (village)
			{
				int val = 0;
				if (bInWater)
					village->addHuggerByType("swim", speed);
				else
					village->addHuggerByType("walk", speed);
			}
		}*/
		if (getOwnerActor()->getMoveMode() == ACTORMOVE_JUMP)
		{
			if (player && player->isNewMoveSyncSwitchOn())
				player->addMoveStatus(IMT_Jump);
			else
				setJumping(true);
		}
		else
		{
			int r = JUMP_BEGINDIST + m_BoundSize / 2;
			//int r = BLOCK_SIZE;
			if (((!getOwnerActor()->getReverse() && totarget.y > 0) || (getOwnerActor()->getReverse() && totarget.y < 0))
				&& totarget.x * totarget.x + totarget.z * totarget.z < r * r)
			{
				if (player && player->isNewMoveSyncSwitchOn())
					player->addMoveStatus(IMT_Jump);
				else
					setJumping(true);
				m_JumpTarget = WCoord(0, -1, 0);
			}
		}
		if (climbing && *climbing && m_nLocType == Climbing)
		{

		}
		else
		{
			clearTarget();
		}
		return false;
	}
	
	return true;
}

static float vel_limit_xz = 5.0f;
static float vel_limit_y = 15.0f;

extern int GetActorDepthInLiquid(ActorLocoMotion *locomove, bool onlywater);
static float CalWalkOnLiquidForce(ActorLocoMotion *locomove, float my)
{
	int inwater_h = GetActorDepthInLiquid(locomove, false);
	float minh = BLOCK_FSIZE / 2.0f;
	if(inwater_h < minh)
	{
		return my + 1.0f * (inwater_h*2.0f/minh - 1.0f);
	}
	else
	{
		if(my < 0) my /= 2.0f;

		return my + 30.0f;
	}
}

const int MAX_MOTION = (int)(100*BLOCK_FSIZE);
void CheckMotionValid(Vector3f &motion)
{
	if(((int)motion.x)<-MAX_MOTION || ((int)motion.x)>MAX_MOTION) motion.x = 0;
	if(((int)motion.z)<-MAX_MOTION || ((int)motion.z)>MAX_MOTION) motion.z = 0;
	if(((int)motion.y)<-MAX_MOTION || ((int)motion.y)>MAX_MOTION) motion.y = 0;
}

namespace {
	int s_count = 101;
}
bool LivingLocoMotion::checkMoveTypeCheat()
{
	if (m_UseMoveType.isC())
	{
		if (++s_count > 100)
		{
			s_count = 0;
			PlayerControl* player_control = dynamic_cast<PlayerControl*>(getOwnerActor());
			if (player_control)
			{
				jsonxx::Object log;
				log << "client_movetype_info" << m_UseMoveType.info();
				player_control->SendActionLog2Host(false, "cheat_client_movetype", log.json_nospace());
			}
		}
		
		return true;
	}
	return false;
}

bool LivingLocoMotion::checkGravityCheat()
{
	if (m_UseGravity.isC())
	{
		if (++s_count > 100)
		{
			s_count = 0;
			PlayerControl* player_control = dynamic_cast<PlayerControl*>(getOwnerActor());
			if (player_control)
			{
				jsonxx::Object log;
				log << "client_gravity_info" << m_UseGravity.info();
				player_control->SendActionLog2Host(false, "cheat_client_gravity", log.json_nospace());
			}
		}

		return true;
	}
	return false;
}

void LivingLocoMotion::moveEntityWithHeading(float strafing, float forward)
{
    OPTICK_EVENT();
	setMoveType(0);
	m_UseGravity.Set(0);
	auto RidComp = getOwnerActor()->getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		return;
	}

	auto funcWrapper = getOwnerActor()->getFuncWrapper();
	auto ActionAttrStateComp = getOwnerActor()->getActionAttrStateComponent();
	if (!(funcWrapper && funcWrapper->getCanMove()) || !(ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_MOVE)))
	{
		return;
	}


	bool bNeedFloat = false;
	bool bCanDiving = false;
	bool bDoingRush = false;
	Vector3f speedOffset(0.0f, 0.0f, 0.0f);
	if (m_InWater) {
		bNeedFloat = checkSeatInWaterSkill(1);
		bCanDiving = checkSeatInWaterSkill(2);
		bDoingRush = isDoingRush();
		bool bCanRush = checkSeatInWaterSkill(4);
		if (bNeedFloat) {
			ActorLiving* riddenby = NULL;
			if (RidComp)
			{
				riddenby = dynamic_cast<ActorLiving*>(RidComp->getRiddenByActor());
			}
			if (!riddenby) {
				int inwater_h = GetActorDepthInLiquid(this, true);
				if (inwater_h < BLOCK_SIZE) {
					if (m_Motion.y < -4.0f) { m_Motion.y = -4.0f; }
					m_Motion.y += 6.0f * (inwater_h * 2.0f / BLOCK_FSIZE - 1.0f) + 1.0f;
				}
				else {
					if (m_Motion.y < 0) m_Motion.y /= 2.0f;
					m_Motion.y += 4.0f;
				}

				doMoveStep(m_Motion);
				m_Motion.x *= 0.8f;
				m_Motion.z *= 0.8f;
				return;
			}
		}
		else if ((bCanDiving || bCanRush) && !bDoingRush) {
			ActorLiving* riddenby = NULL;
			if (RidComp)
			{
				riddenby = dynamic_cast<ActorLiving*>(RidComp->getRiddenByActor());
			}
			if (riddenby) {
				LivingLocoMotion* locomove = static_cast<LivingLocoMotion*>(riddenby->getLocoMotion());
				if (locomove && checkCanUpAndDown(locomove->m_RotationPitch) && (locomove->m_MoveForward > 0.000001f ||
					locomove->m_MoveForward < -0.000001f || locomove->m_MoveStrafing > 0.000001f || locomove->m_MoveStrafing < -0.000001f))
				{
					Vector3f dir;
					PitchYaw2Direction(dir, 0, locomove->m_RotationPitch);
					dir.x = 0.0f;
					dir.z = 0.0f;
					m_Motion += 3 * dir;
				}
			}
		}

		if (bNeedFloat || bCanDiving || bDoingRush) {
			ActorLiving* riddenby = NULL;
			if (RidComp)
			{
				riddenby = dynamic_cast<ActorLiving*>(RidComp->getRiddenByActor());
			}
			if (riddenby) {

				float speed = funcWrapper ? funcWrapper->getAIMoveSpeed() : 200.0f;
				float fSpeedFact = speed * (bDoingRush ? 0.4f : 0.07f);
				speedOffset.x = m_Motion.x * fSpeedFact;
				speedOffset.y = bDoingRush ? m_Motion.y * fSpeedFact : 0;
				speedOffset.z = m_Motion.z * fSpeedFact;
			}
		}
	}

	ClientActor* pActor = getOwnerActor();
	BlockEnvEffectsComponent* pBlockEnvEffectsComponent = getBlockEnvEffectsComponent();

	GetIWorldConfigProxy()->VmpBeginUltra("LivingMove");
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(pActor);
	bool playerflying = pActor->getFlying() && player != NULL;
	setMoveType(0);
	m_UseGravity.Set(0);
	//在水中或者岩浆中，非飞行模式
	if ((m_InWater || m_InLava || m_InHoney) && !playerflying)
	{
		bool walkonliquid = (m_InWater && canWalkOnLiquid(true)) || (m_InLava && canWalkOnLiquid(false));
		float speed = funcWrapper ? funcWrapper->getAIMoveSpeed() : 200.0f;
		if (!walkonliquid)
		{
			if (!m_InHoney)
				speed *= GetLuaInterfaceProxy().get_lua_const()->swimming_yidong_beilv;// 2.5f;
			else
				speed *= GetLuaInterfaceProxy().get_lua_const()->swimming_honey_yidong_beilv;
		}
		int oldy = m_Position.y;
		moveFlying(strafing, forward, speed);
		doMoveStep((bNeedFloat || bCanDiving || bDoingRush) ? m_Motion + speedOffset : m_Motion);

		m_Motion *= (bDoingRush ? 0.9f : 0.8f);
		if (walkonliquid || bNeedFloat)
		{
			m_Motion.y = CalWalkOnLiquidForce(this, m_Motion.y);
			if (bNeedFloat) {
				m_Motion.y -= 0.25f;
			}
		}
		else if (!bCanDiving && !bDoingRush) {
			float baseSpeed = 2.0f;
			//水中没落地的时候按 潜行键能按当前装备的装备加速下潜
			if (player != nullptr && m_InWater && !m_OnGround && player->getSneaking())
			{
				baseSpeed += GetLuaInterfaceProxy().get_lua_const()->livingWaterSneakAddSpeed;
				PlayerAttrib* attrib = dynamic_cast<PlayerAttrib*>(pActor->getAttrib());
				if (attrib)
				{
					baseSpeed = attrib->calculateMoveSpeed(baseSpeed, Actor_Swim_Speed);
				}
			}
			m_Motion.y -= baseSpeed;
		}

		if (m_CollidedHorizontally && isOffsetPositionInLiquid(m_Motion.x, m_Motion.y + 60.0f - m_Position.y + oldy, m_Motion.z))
		{
			m_Motion.y = 30.0f;
		}

		if (findBottomBlock(BLOCK_HOTCRYSTAL))
		{
			m_Motion.y = 40;
			ActorLiving* living = dynamic_cast<ActorLiving*>(pActor);
			if (living != nullptr && living->getLivingAttrib())
			{
				living->getLivingAttrib()->addBuff(BOUND_BUFF, 1);
			}
		}
		checkActorInHotZone();
		setMoveType(1);
	}
	else if (playerflying)
	{
		float speed = 1.3f * (funcWrapper ? funcWrapper->getAIMoveSpeed() : 200.0f);
		moveFlying(strafing, forward, speed);
		float movedecay = 0.71f;
		doMoveStep(m_Motion);

		if (g_pPlayerCtrl && g_pPlayerCtrl->m_InputInfo->flyUp == -1) {
			m_Motion.y = calGravityMotionY(m_Motion.y);
		}

		if (player)
			movedecay += player->m_GmFlySpeed;

		m_Motion.y *= getMoveReduceRatio();
		m_Motion.x *= movedecay;
		m_Motion.z *= movedecay;
		setMoveType(2);
	}
	else
	{
		setMoveType(3);
		float movedecay = CalMoveDecay(m_pWorld, m_Position, player, m_OnGround);
		if (movedecay == 0) return;
		float slipper_slow = 0.16277136f / (movedecay * movedecay * movedecay);
		float speed;

		if (movedecay >= 0)
		{
			speed = (funcWrapper ? funcWrapper->getAIMoveSpeed() : 200.0f) * slipper_slow;
		}
		else
		{
			speed = m_JumpMovementFactor;
		}

		if (player && forward < 0) speed *= GetLuaInterfaceProxy().get_lua_const()->houtui_yidong_beilv;

		if ((pBlockEnvEffectsComponent && pBlockEnvEffectsComponent->GetStatus(ENV_THICKET)))//荆棘减速50%
			speed *= 0.5f;

		//具体移动逻辑
		moveFlying(strafing, forward, speed);

		if (isOnLadder())
		{
			m_Motion.x = Rainbow::Clamp(m_Motion.x, -vel_limit_xz, vel_limit_xz);
			m_Motion.z = Rainbow::Clamp(m_Motion.z, -vel_limit_xz, vel_limit_xz);

			if (funcWrapper)
			{
				funcWrapper->setFallDistance(0);
			}

			if (m_Motion.y < -vel_limit_y) m_Motion.y = -vel_limit_y;

			if (pActor->getSneaking() && m_Motion.y < 0)
			{
				m_Motion.y = 0;
			}
		}

		doMoveStep(m_Motion);

		movedecay = CalMoveDecay(m_pWorld, m_Position, player, m_OnGround);
		if (movedecay < 0) movedecay = DEFAULT_MOVEDCAY;

		//推雪球时摩擦力减小
		if (forward == 0 && player && player->getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL && player->getCatchBall())
		{
			movedecay = 0.98f;
		}

		bool isclimbwall = false;
		ActorLiving* living = dynamic_cast<ActorLiving*>(pActor);
		if (living && living->getLivingAttrib())
			isclimbwall = living->getLivingAttrib()->hasStatusEffect(STATUS_EFFECT_CLIMBWALL);
		//m_CollidedHorizontally
		if (((m_CollidedHorizontally || m_isJumping) && isOnLadder()) || (m_CollidedHorizontally && isclimbwall))
		{
			m_Motion.y = 20.0f;
		}

		m_Motion.y = calGravityMotionY(m_Motion.y);

		m_Motion.y *= getMoveReduceRatio();
		m_Motion.x *= movedecay;
		m_Motion.z *= movedecay;
		/*if (player || !m_isJumping)
		{
			m_Motion.x *= movedecay;
			m_Motion.z *= movedecay;
		}*/

		//消耗耙耐久
		ClientMob* pMob = dynamic_cast<ClientMob*>(pActor);
		if (pMob && pMob->getLivingAttrib())
		{
			MobAttrib* attr = static_cast<MobAttrib*>(pMob->getLivingAttrib());
			assert(attr != NULL);
			BackPackGrid* pPackGrid = attr->getEquipGridWithType(EQUIP_LEGGING);

			if (pPackGrid && pPackGrid->def && (pMob->m_Def && ActorHorse::mobCanRake(pMob->m_Def->ID)))
			{
				WCoord pos = getOwnerActor()->getPosition();
				Vector3f vecPos(pos.x, pos.y, pos.z);
				Vector3f dir = Vector3f(0.0f, 0.0f, 0.0f);
				PitchYaw2Direction(dir, m_RotateYaw + 180.0f, 0);
				vecPos += dir * 100.0f;

				int x = CoordDivBlock(vecPos.x);
				int y = CoordDivBlock(vecPos.y);
				int z = CoordDivBlock(vecPos.z);

				int blockID = pActor->getWorld()->getBlockID(x, y - 1, z);

				//带耙变成耕地
				if (blockID == BLOCK_GRASS || blockID == BLOCK_DIRT || blockID == BLOCK_GRASS_WOOD_GRAY || blockID == BLOCK_REDSOIL)
				{
					WCoord blockpos(x, y - 1, z);

					if (blockID == BLOCK_GRASS || blockID == BLOCK_DIRT)
					{
						m_pWorld->setBlockAll(blockpos, BLOCK_FARMLAND, 0);
					}
					else if (blockID == BLOCK_GRASS_WOOD_GRAY)
					{
						m_pWorld->setBlockAll(blockpos, BLOCK_GRASS_WOOD_GRAY_FARMLAND, 0);
					}
					else if (blockID == BLOCK_REDSOIL)
					{
						m_pWorld->setBlockAll(blockpos, BLOCK_FARMLAND_RED, 0);
					}


					//随机消耗耐久
					int ndamageDuration = GenRandomInt(1, 2);
					attr->damageEquipItemWithType(EQUIP_LEGGING, ndamageDuration);

					ActorHorse* pHorse = dynamic_cast<ActorHorse*>(pMob);
					if (pHorse)
					{
						//消耗耐久
						BackPackGrid* PackGrid = pHorse->index2Grid(HORSE_EQUIP_INDEX + 2);
						assert(PackGrid);
						if (PackGrid)
						{
							PackGrid->addDuration(-ndamageDuration);
						}
					}

					//犁地音效
					if (pActor)
					{
						auto sound = pActor->getSoundComponent();
						if (sound)
						{
							sound->playSound("ent.3401.plough", 1.0f, 1.0f);
						}
					}
				}
			}
		}
	}

	CheckMotionValid(m_Motion);

	GetIWorldConfigProxy()->VmpEnd();
}

float LivingLocoMotion::calGravityMotionY(float my)
{
	m_UseGravity.Set(1);

	bool needgravity = true;
	MoveAbilityType actortype = getLocoMotionType();
	if (actortype == FlyLoc)
	{
		needgravity = false;
	}
	if (needgravity)
	{
		float gravity = m_pWorld->getGravity(GRAVITY_LIVING);
		if (getOwnerActor()->getReverse())
		{
			if (my >= 0) my += gravity * getGravityFactor(false);
			else
			{
				my += gravity * getGravityFactor(true);
				if (my > 0 && getGravityFactor(false) < 1.0f) my = 0;
			}
		}
		else
		{
			if (my <= 0) my -= gravity * getGravityFactor(false);
			else
			{
				my -= gravity * getGravityFactor(true);
				if (my < 0 && getGravityFactor(false) < 1.0f) my = 0;
			}
		}

		if (onTheBlock(BLOCK_HOTCRYSTAL))
		{
			my = 40;
			ActorLiving* living = dynamic_cast<ActorLiving*>(getOwnerActor());
			if (living != nullptr && living->getLivingAttrib())
			{
				living->getLivingAttrib()->addBuff(BOUND_BUFF, 1);
			}
		}
		else
		{
			ActorLiving* living = dynamic_cast<ActorLiving*>(getOwnerActor());
			if (living != nullptr)
			{
				if (living->getLivingAttrib()->hasBuff(BOUND_BUFF))
				{
					my += 8.0f;
				}
				else if (living->getLivingAttrib()->hasBuff(SWIMMY_BUFF))	// ********：眩晕buff吹起离地面1格高后高度就不动  codeby： keguanqiang
				{
					if (checkDistByGround(BLOCK_SIZE))
						my = 0.0f;
					else
						my = 10.0f;
				}
				else if (living->getLivingAttrib()->hasBuff(BUBBLE_SLEEP_BUFF))	// 20220603：泡泡睡眠buff吹起离地面3格高后高度就不动  codeby： keguanqiang
				{
					if (checkDistByGround(3 * BLOCK_SIZE))
						my = 0.0f;
					else
						my = 10.0f;
				}
			}
		}
	}


	return my;
}

void LivingLocoMotion::setTarget(const WCoord& target, float speed)
{
	m_MoveTarget = target;
	m_MoveTargetSpeed = speed;
	if (m_nLocType == FlyLoc)
	{
		m_SpeedInAir = speed;
	}
}

void LivingLocoMotion::clearTarget()
{
	m_MoveTargetSpeed = -1.0f;
	m_NavigationRotateYaw = -1.0f;
	m_NavigationRotationPitch = -1.0f;
}

bool LivingLocoMotion::canDoJump()
{
	bool result = false;
	ClientActor* pActor = getOwnerActor();

	auto pBlockEnvEffectsComponent = getBlockEnvEffectsComponent();
	auto RidComp = pActor->getRiddenComponent();
	if (!(RidComp && RidComp->isRiding()))
	{
		if (!m_InWater && !m_InLava && !m_InHoney &&
			!(pBlockEnvEffectsComponent && pBlockEnvEffectsComponent->GetStatus(ENV_THICKET)))
		{
			if (m_OnGround && m_JumpingTicks == 0)
			{
				result = true;
			}
		}
	}
	return result;
}

void LivingLocoMotion::setJumpToTarget(const WCoord& target)
{
	setJumping(true);
	m_JumpTarget = target;
}

void LivingLocoMotion::checkPhysWorld()
{
#ifdef USE_ACTOR_ROLECONTROLLER
	if (!m_pWorld || !m_ActorRoleController)
	{
		return;
	}


	OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Physics);
	WCoord curpos = m_Position;
	WCoord range(SECTION_SIZE / 2, SECTION_SIZE / 2, SECTION_SIZE / 2);
	WCoord minpos = CoordDivSection(curpos - range);
	WCoord maxpos = CoordDivSection(curpos + range);

	static VectorNoFree<WCoord> checkphy;
	checkphy.clear();
	for (int y = minpos.y; y <= maxpos.y; y++)
	{
		for (int z = minpos.z; z <= maxpos.z; z++)
		{
			for (int x = minpos.x; x <= maxpos.x; x++)
			{
				checkphy.push_back(WCoord(x, y, z));
			}
		}
	}

	for (int i = 0; i < (int)m_preCheckPhy.size(); i++)
	{
		int j = 0;
		const WCoord& pos = m_preCheckPhy[i];
		for (; j < (int)checkphy.size(); j++)
		{
			if (pos == checkphy[j])
			{
				break;
			}
		}
		if (j == checkphy.size())
		{
			m_pWorld->updateLeaveSectionPhysics(pos.x, pos.y, pos.z);
		}
	}

	for (int i = 0; i < (int)checkphy.size(); i++)
	{
		int j = 0;
		const WCoord& pos = checkphy[i];
		for (; j < (int)m_preCheckPhy.size(); j++)
		{
			if (pos == m_preCheckPhy[j])
			{
				break;
			}
		}
		if (j == m_preCheckPhy.size())
		{
			m_pWorld->updateEnterSectionPhysics(pos.x, pos.y, pos.z);
		}
		m_pWorld->updateSectionPhysics(pos.x, pos.y, pos.z, false, -1, false);
	}

	m_preCheckPhy.resize(checkphy.size());
	for (int i = 0; i < (int)checkphy.size(); i++)
	{
		m_preCheckPhy[i] = checkphy[i];
	}
#endif
}

//bool LivingLocoMotion::needFullRotation()
//{
//	return m_nLocType == LandLoc && isBehaviorOn(BehaviorType::MoveOnWater);
//}

void LivingLocoMotion::setJumpToTargetEnforced(const WCoord& target)
{
	m_enforcedJumpTarget = true;
	setJumpToTarget(target);
}

void LivingLocoMotion::moveEntityWithDirection()
{
	if (m_nLocType == FlyLoc || m_nLocType == AquaticLoc)
	{
		auto functionWrapper = getOwnerActor()->getFuncWrapper();
		auto comp = getOwnerActor()->getActionAttrStateComponent();
		if (g_WorldMgr && g_WorldMgr->isUGCEditMode() && (!functionWrapper->getCanMove() || !comp->checkActionAttrState(ENABLE_MOVE)))
		{
			return;
		}
	}

	if (m_nLocType == FlyLoc)
	{
		auto functionWrapper = getOwnerActor()->getFuncWrapper();
		auto comp = getOwnerActor()->getActionAttrStateComponent();
		if (g_WorldMgr && g_WorldMgr->isUGCEditMode() && (!functionWrapper->getCanMove() || !comp->checkActionAttrState(ENABLE_MOVE)))
		{
			return;
		}

		// ********：眩晕buff时，不可移动  codeby： keguanqiang
		LivingAttrib* livingAttrib = dynamic_cast<LivingAttrib*>(getOwnerActor()->getAttrib());
		if (livingAttrib && (livingAttrib->hasBuff(SWIMMY_BUFF) || livingAttrib->hasBuff(BUBBLE_SLEEP_BUFF) || livingAttrib->hasBuff(LOSS_OF_CONSCIOUSNESS_BUFF) || livingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE)))
		{
			//存在Buff下落
			if (livingAttrib->hasStatusEffect(STATUS_EFFECT_DROP) || livingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
			{
				//看下面的代码这段逻辑没有用 先注释掉
				/*float movedecay = CalMoveDecay(m_pWorld, m_Position, NULL, m_OnGround);
				if (movedecay == 0) return;
				float slipper_slow = 0.16277136f / (movedecay * movedecay * movedecay);
				float speed;

				if (movedecay >= 0)
				{
					auto funcWrapper = getOwnerActor()->getFuncWrapper();
					speed = funcWrapper ? funcWrapper->getAIMoveSpeed() * slipper_slow : 200.0f;
				}
				else
				{
					speed = m_JumpMovementFactor;
				}*/
				doMoveStep(m_Motion);

				float movedecay = CalMoveDecay(m_pWorld, m_Position, nullptr, m_OnGround);
				if (movedecay < 0) movedecay = 0.91f;

				float gravity = m_pWorld->getGravity(GRAVITY_LIVING);
				if (m_Motion.y <= 0)
					m_Motion.y -= gravity * getGravityFactor(false);
				else
				{
					m_Motion.y -= gravity * getGravityFactor(true);
					if (m_Motion.y < 0 && getGravityFactor(false) < 1.0f)
						m_Motion.y = 0;
				}
				m_Motion.y *= 0.98f;
				m_Motion.x *= movedecay;
				m_Motion.z *= movedecay;
				return;
			}
			if (livingAttrib->hasBuff(SWIMMY_BUFF) ||
				livingAttrib->hasBuff(BUBBLE_SLEEP_BUFF) ||
				livingAttrib->hasStatusEffect(STATUS_EFFECT_PERCIPIENCE)) //无法移动
			{
				return;
			}
		}

		Vector3f motion;
		if (isBehaviorOn(BehaviorType::LashTagert))
		{
			Vector3f desiredDirection = (m_MoveTarget - m_Position).toVector3();
			if (desiredDirection.Length() < (GetSpeedInAir() * 0.05))
			{
				m_Velocity = desiredDirection / 0.05f;
			}
			else
			{
				desiredDirection.NormalizeSafe();
				m_Velocity = desiredDirection * GetSpeedInAir();
			}
		}
		else
		{
			if (m_InWater || m_InLava)
			{
				m_SteeringForce = Vector3f::yAxis * m_MaxSteeringForce;
			}
			else
			{
				calculateSteering();
			}
			Vector3f acceleration = m_SteeringForce / (float)(getOwnerActor()->m_Mass / 1000);

			m_Velocity = m_Velocity + acceleration * 0.05f;

			m_Velocity.Truncate(GetSpeedInAir());
		}
		motion = m_Velocity * 0.05f;
		//旋转代码
		if (m_Velocity.LengthSqr() > 10.0f)
		{
			UpdateRotation();
		}
		//else
		//{
		//	auto pEntity = getOwnerActor()->getBody()->getEntity();
		//	if (pEntity && pEntity->GetMainModel())
		//	{
		//		pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
		//	}
		//}

		WCoord mvec = getIntegerMotion(motion + m_ColliderMotion);
		if (mvec.x == 0 && mvec.y == 0 && mvec.z == 0)
		{
			m_HasTarget = false;
			return;
		}

		if (mvec.lengthSquared() < 10)
		{
			return;
		}

		CollideAABB box;
		getCollideBox(box);

		//m_OnGround = (m_pWorld->moveBox(box, WCoord(0, -1, 0)).y == 0);
		WCoord realMov = m_pWorld->moveBox(box, mvec);
		if (realMov.x == 0 && realMov.y == 0 && realMov.z == 0)
		{
			m_HasTarget = false;
		}
		else //916冒险 2021/08/18 codeby:wudeshen
		{
			auto livingActor = dynamic_cast<ActorLiving*>(getOwnerActor());
			if (livingActor && m_StepTick % 20 == 0)
				livingActor->playStepSound();
			m_StepTick++;
		}
		addRealMove(box, realMov);
		int flags = 0;
		m_CollidedHorizontally = false;
		m_CollidedVertically = false;
		if (realMov.y != mvec.y)
		{
			if (mvec.y < 0)
			{
				setOnGround(true);
				m_Motion.y = 0;
				m_AccumLeftMotion.y = 0;
			}
			m_CollidedVertically = true;
			flags |= 2;
		}
		if (mvec.x != 0 && realMov.x == 0)
		{
			m_AccumLeftMotion.x = 0;
			m_CollidedHorizontally = true;
			flags |= 1;
		}
		if (mvec.z != 0 && realMov.z == 0)
		{
			m_AccumLeftMotion.z = 0;
			m_CollidedHorizontally = true;
			flags |= 1;
		}
	}
	else if (m_nLocType == AquaticLoc)
	{
		LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getOwnerActor()->getAttrib());
		if (pLivingAttrib && pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
		{
			if (m_CollideMoveTicks)
			{
				m_CollideMoveTicks--;
			}
			else
			{
				return;
			}
		}
		//在水中或者岩浆中，非飞行模式
		//if (m_InWater || m_InLava)
		//{
			//Steering behavior
		if (m_InWater || m_InLava)
		{
			calculateSteering();
		}
		else {
			/*if (getOwnerActor()->getDefID() == 3627 && getOwnerActor()->m_Body->hasAnimPlaying(SEQ_FLYINGFISH_FLY))
			{*/
			AccumulateForce(m_SteeringForce, seek(m_MoveTarget) * wanderWeight);
			/*}
			else
			{
				m_SteeringForce = Vector3f::neg_yAxis * m_MaxSteeringForce;
			}*/

		}
		Vector3f acceleration = m_SteeringForce / (float)(getOwnerActor()->m_Mass / 1000);

		m_Velocity = m_Velocity + acceleration * 0.05f;

		m_Velocity.Truncate(GetSpeedInWater());

		Vector3f curMotion = m_Velocity * 0.05f;

		if (m_Velocity.LengthSqr() > 10.0f)
		{
			UpdateRotation();
		}
		checkActorInHotZone();

		WCoord mvec = getIntegerMotion(curMotion + m_ColliderMotion);

		if (mvec.x == 0 && mvec.y == 0 && mvec.z == 0)
		{
			return;
		}

		if (mvec.lengthSquared() < 10)
		{
			return;
		}

		CollideAABB box;
		getCollideBox(box);

		//m_OnGround = (m_pWorld->moveBox(box, WCoord(0, -1, 0)).y == 0);
		WCoord realMov = m_pWorld->moveBox(box, mvec);

		addRealMove(box, realMov);
		int flags = 0;
		m_CollidedHorizontally = false;
		m_CollidedVertically = false;
		if (realMov.y != mvec.y)
		{
			if (mvec.y < 0)
			{
				setOnGround(true);
				m_Motion.y = 0;
				m_AccumLeftMotion.y = 0;
			}
			m_CollidedVertically = true;
			flags |= 2;
		}
		if (mvec.x != 0 && realMov.x == 0)
		{
			m_AccumLeftMotion.x = 0;
			m_CollidedHorizontally = true;
			flags |= 1;
		}
		if (mvec.z != 0 && realMov.z == 0)
		{
			m_AccumLeftMotion.z = 0;
			m_CollidedHorizontally = true;
			flags |= 1;
		}
	}
}

float LivingLocoMotion::GetSpeedInAir()
{
	return m_SpeedInAir * m_SpeedMultiple;
}

void LivingLocoMotion::calculateSteering()
{
	if (m_nLocType == FlyLoc || m_nLocType == AquaticLoc)
	{
		m_SteeringForce = Vector3f::zero;
	}

	if (m_nLocType == AquaticLoc)
	{
		//规避水面
		if (isBehaviorOn(BehaviorType::WaterAvoidance))
		{
			if (getOwnerActor()->getDefID() == 3627 && getOwnerActor()->m_Body->hasAnimPlaying(SEQ_FLYINGFISH_FLY))
			{
				AccumulateForce(m_SteeringForce, seek(m_MoveTarget) * wanderWeight);
			}
			else if (!AccumulateForce(m_SteeringForce, surfaceAvoidance() * surfaceAvoidanceWeight))
			{
				return;
			}
		}

		//规避固体块
		if (isBehaviorOn(BehaviorType::ObstacleAvoidance))
		{
			if (!AccumulateForce(m_SteeringForce, obstacleAvoidance() * obstacleAvoidanceWeight))
			{
				return;
			}
		}

		//逃避Mob
		if (m_FearMobId != -1)
		{
			ClientMob* pTarget = getOwnerActor()->getActorMgr()->findMobByWID((unsigned int)m_FearMobId);

			if (pTarget != NULL)
			{
				AccumulateForce(m_SteeringForce, flee(pTarget->getPosition() * fleeWeight));
			}
		}
	}
	if (m_nLocType == FlyLoc || m_nLocType == AquaticLoc)
	{
		//逃避玩家
		if (m_FearPlayerId != -1)
		{
			ClientPlayer* pTarget = getOwnerActor()->getActorMgr()->findPlayerByUin((unsigned int)m_FearPlayerId);

			if (pTarget != NULL)
			{
				AccumulateForce(m_SteeringForce, flee(pTarget->getPosition() * fleeWeight));
			}
		}

		//Wander到下一个目标点
		if (isBehaviorOn(BehaviorType::Wander))
		{
			if (!AccumulateForce(m_SteeringForce, seek(m_MoveTarget) * wanderWeight))
			{
				return;
			}
		}

		//Pursute
		if (isBehaviorOn(BehaviorType::Pursuit))
		{
			if (!AccumulateForce(m_SteeringForce, seek(m_MoveTarget) * pursuitWeight))
			{
				return;
			}
		}
	}
}

Vector3f LivingLocoMotion::seek(const WCoord& target)
{
	Vector3f desiredDirection = (target - m_Position).toVector3();
	desiredDirection.NormalizeSafe();
	return getSpeed() * desiredDirection - m_Velocity;
}

Rainbow::Vector3f LivingLocoMotion::flee(const WCoord& target)
{
	Vector3f desiredDirection = (m_Position - target).toVector3();
	desiredDirection.NormalizeSafe();
	return getSpeed() * desiredDirection - m_Velocity;
}

Rainbow::Vector3f LivingLocoMotion::pursuit(ClientActor& evader)
{
	Vector3f toEvader = (evader.getLocoMotion()->m_Position - m_Position).toVector3();

	float relativeHeading = DotProduct(m_Velocity, evader.getLocoMotion()->m_Motion);
	//几乎是面朝evader，则直接seek. acos(18) = -0.95f
	if (DotProduct(toEvader, m_Velocity) > 0 && relativeHeading < -0.95f)
	{
		return seek(evader.getLocoMotion()->m_Position);
	}

	//预测追击的时间
	auto funcWrapper = evader.getFuncWrapper();
	float speed = funcWrapper ? funcWrapper->getAIMoveSpeed() : 200.0f;
	float predictionTime = toEvader.Length() / (getSpeed() + speed);
	return seek(evader.getLocoMotion()->m_Position + predictionTime * speed);
}

Rainbow::Vector3f LivingLocoMotion::evade(ClientActor& pursuer)
{
	Vector3f toPursuer = (pursuer.getLocoMotion()->m_Position - m_Position).toVector3();
	auto funcWrapper = pursuer.getFuncWrapper();
	float speed = funcWrapper ? funcWrapper->getAIMoveSpeed() : 200.0f;
	float predictionTime = toPursuer.Length() / (getSpeed() + speed);

	return flee(pursuer.getLocoMotion()->m_Position + predictionTime * speed);
}

Rainbow::Vector3f LivingLocoMotion::surfaceAvoidance()
{
	if (isOffsetPositionInLiquid(m_Motion.x, m_Motion.y + m_BoundHeight * 0.75, m_Motion.z))
	{
		return Vector3f::neg_yAxis * m_MaxSteeringForce;

	}
	else if (isOffsetPositionInLiquid(m_Motion.x, m_Motion.y + 70, m_Motion.z))
	{
		if (m_Velocity.y > 0.25 * GetSpeedInWater())
			return 0.25f * Vector3f::neg_yAxis * m_MaxSteeringForce;
		else
			return Vector3f::zero;
	}
	else if (isOffsetPositionInLiquid(m_Motion.x, m_Motion.y + 100, m_Motion.z))
	{
		if (m_Velocity.y > 0.5 * GetSpeedInWater())
			return 0.5f * Vector3f::neg_yAxis * m_MaxSteeringForce;
		else
			return Vector3f::zero;
	}

	return Vector3f::zero;
}

Rainbow::Vector3f LivingLocoMotion::obstacleAvoidance()
{
	return Vector3f::zero;
}

bool LivingLocoMotion::AccumulateForce(Rainbow::Vector3f& steeringForce, const Rainbow::Vector3f& forceToAdd)
{
	float currentForceSize = steeringForce.Length();

	float forceRemaing = m_MaxSteeringForce - currentForceSize;
	if (forceRemaing <= 0)
	{
		return false;
	}

	float forceToAddSize = forceToAdd.Length();

	if (forceToAddSize < forceRemaing)
	{
		steeringForce += forceToAdd;
	}
	else
	{
		Vector3f forceToAddNorlized = forceToAdd.GetNormalizedSafe();
		steeringForce += forceToAddNorlized * forceRemaing;
	}
	return true;
}

void LivingLocoMotion::UpdateRotation()
{
	if (m_nLocType == FlyLoc || m_nLocType == AquaticLoc)
	{
		float yaw = 0;
		float pitch = 0;

		if (m_SmoothRotationOn)
		{
			Vector3f tmpHead = m_smoother->Update(m_Velocity.GetNormalizedSafe());
			Direction2PitchYaw(&yaw, &pitch, tmpHead);
		}
		else
		{
			Direction2PitchYaw(&yaw, &pitch, m_Velocity);
		}

		m_RotateYaw = yaw;
		m_RotationPitch = Clamp(pitch, -15.0f, 15.0f);
	}

	if (m_nLocType == FlyLoc)  //AquaticLoc没有这段逻辑，因为在tick里做了
	{
		/*auto pEntity = getOwnerActor()->getBody()->getEntity();
		if (pEntity && pEntity->GetMainModel())
		{
			pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
		}*/
	}
}

void LivingLocoMotion::initPosition(const WCoord& pos)
{
	m_Position = pos;
	m_SyncPos = pos;
}

void LivingLocoMotion::SetSmoothRotation(bool b)
{
	m_SmoothRotationOn = b;
}

void LivingLocoMotion::SetBehaviorTypeWeight(BehaviorType type, float weight)
{
	if (m_nLocType == FlyLoc || m_nLocType == AquaticLoc)
	{
		switch (type)
		{
		case BehaviorType::None:
			break;
		case BehaviorType::Seek:
			break;
		case BehaviorType::Flee:
			fleeWeight = weight;
			break;
		case BehaviorType::Arrive:
			break;
		case BehaviorType::Wander:
			break;
		case BehaviorType::Cohesion:
			break;
		case BehaviorType::Separation:
			break;
		case BehaviorType::Allignment:
			break;
		case BehaviorType::ObstacleAvoidance:
			obstacleAvoidanceWeight = weight;
			break;
		case BehaviorType::WaterAvoidance:
			surfaceAvoidanceWeight = weight;
			break;
		case BehaviorType::FollowPath:
			break;
		case BehaviorType::Pursuit:
			pursuitWeight = weight;
			break;
		case BehaviorType::Evade:
			break;
		case BehaviorType::Interpose:
			break;
		case BehaviorType::Hide:
			break;
		case BehaviorType::Flock:
			break;
		case BehaviorType::OffsetPursuit:
			break;
		default:
			break;
		}
	}
}

float LivingLocoMotion::GetSpeedInWater()
{
	return m_SpeedInWater * m_SpeedMultiple;
}

void LivingLocoMotion::tryMovoTo(int x, int y, int z, BehaviorType type)
{
	if (!isBehaviorOn(type))
	{
		setBehaviorOn(type);
	}
	m_MoveTarget.x = x;
	m_MoveTarget.y = y;
	m_MoveTarget.z = z;
}

float LivingLocoMotion::getSpeed()
{
	if (m_nLocType == FlyLoc)
		return GetSpeedInAir();
	else if (m_nLocType == AquaticLoc)
	{
		return GetSpeedInWater();
	}

	return 0.0f;
}

WCoord LivingLocoMotion::getHitBlockPos() {
	WCoord pos(0, 0, 0);
	if (m_CollidedHorizontally == true || m_CollidedVertically == true)
	{

		if (m_InWater || m_InLava)
		{
			calculateSteering();
		}
		else {
			if (getOwnerActor()->getDefID() == 3627 && getOwnerActor()->m_Body->hasAnimPlaying(SEQ_FLYINGFISH_FLY))
			{
				AccumulateForce(m_SteeringForce, seek(m_MoveTarget) * wanderWeight);
			}
			else
			{
				m_SteeringForce = Vector3f::neg_yAxis * m_MaxSteeringForce;
			}
		}
		Vector3f acceleration = m_SteeringForce / (float)(getOwnerActor()->m_Mass / 1000);

		m_Velocity = m_Velocity + acceleration * 0.05f;

		m_Velocity.Truncate(GetSpeedInWater());

		Vector3f m_Motion = m_Velocity * 0.05f;

		CollideAABB box;
		getCollideBox(box);
		WCoord mvec = getIntegerMotion(m_Motion + m_ColliderMotion);
		if (mvec.x == 0 && mvec.y == 0 && mvec.z == 0)
		{
			return pos;
		}

		if (mvec.lengthSquared() < 10)
		{
			return pos;
		}
		pos = m_pWorld->getHitBlockPos(box, mvec);
	}
	return pos;
}

void LivingLocoMotion::calculateMove()
{
	moveEntityWithHeading(m_MoveStrafing, m_MoveForward);
}

void LivingLocoMotion::initMoveAbility(MoveAbilityType type)
{
	if (m_nLocType == type)
		return;
	m_nLocType = type;
	m_MoveAbilityFlag.Reset();
	setMoveAbilityFlag(type, true);
	if (m_nLocType == FlyLoc)
	{
		ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
		if (!actor)
			return;
		ClientMob* mob = actor->ToCast<ClientMob>();
		auto pClientFlyComponent = actor->getClientFlyComponent();
		if (!pClientFlyComponent&&mob)
		{
			mob->cacheClientFlyComponent(mob->CreateComponent<ClientFlyComponent>("ClientFlyComponent"));
		}
	}
	else
	{
		ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
		if (!actor)
			return;
		auto pClientFlyComponent = actor->getClientFlyComponent();
		if (pClientFlyComponent)
		{
			actor->DestroyComponent(pClientFlyComponent);
			ClientMob* mob = actor->ToCast<ClientMob>();
			if(mob)
			mob->cacheClientFlyComponent(nullptr);
		}
	}
}

void LivingLocoMotion::setLocoMotionType(MoveAbilityType type)
{
	if (checkMoveAbilityFlag(type))		//检查是否有这样的能力
		m_nLocType = type;
}

void LivingLocoMotion::GetRiddenActorInputInfo(float& strafing, float& forward)// 玩家骑乘非坐骑生物，生物移动
{
	MonsterDef* monsterDef = nullptr;

	if (m_MonsterDef == nullptr)
	{
		int  actorid = getOwnerActor()->getDefID();
		m_MonsterDef = GetDefManagerProxy()->getMonsterDef(actorid);
	}

	monsterDef = m_MonsterDef;

	if (monsterDef && (monsterDef->TriggerType == 1 || monsterDef->TriggerType == 2)) //暂限制触发器生物和怪物 不影响商业化坐骑
	{
		auto RidComp = getOwnerActor()->getRiddenComponent();
		ClientActor* riddenactor = NULL;
		if (RidComp && RidComp->isRidden())
		{
			if (!RidComp->getRiddenControl()) // 骑乘坐骑不可控制
			{
				return;
			}
			riddenactor = RidComp->getRiddenByActor();
		}
		if (riddenactor && riddenactor->getObjType() == OBJ_TYPE_ROLE)
		{
			LivingLocoMotion* riddenloc = dynamic_cast<LivingLocoMotion*>(riddenactor->getLocoMotion());
			if (riddenloc)
			{
				strafing = riddenloc->m_MoveStrafing * 0.5f;
				forward = riddenloc->m_MoveForward;
				float speed = getOwnerActor()->getAttrib()->getMoveSpeed(Actor_Walk_Speed);
				auto funcWrapper = getOwnerActor()->getFuncWrapper();
				if (funcWrapper)
				{
					funcWrapper->setAIMoveSpeed(speed);
				}

				if (!m_pWorld->isRemoteMode())
				{
					TurnToRiddenActorDir(riddenloc);
				}
			}
		}
	}
}
void LivingLocoMotion::TurnToRiddenActorDir(LivingLocoMotion* riddenloc)// 玩家骑乘非坐骑生物，生物转向
{
	if (!riddenloc) { return; }
	m_RotateYaw = m_PrevRotateYaw = riddenloc->m_RotateYaw;
	m_RotationPitch = m_PrevRotatePitch = riddenloc->m_PrevRotatePitch * 0.5f;
	if (riddenloc->m_PrevRotatePitch > 90)
	{
		m_RotationPitch = m_PrevRotatePitch = (riddenloc->m_PrevRotatePitch - 360.f) * 0.5f;
	}
}

void LivingLocoMotion::attachPhysActor()
{
#ifdef USE_ACTOR_ROLECONTROLLER
	ClientPlayer* player = getOwnerActor()->ToCast<ClientPlayer>();
	if (player)
		return;

	if (m_ActorRoleController == NULL)
	{
		// 优化 2个格山洞：修正值25
		Rainbow::Vector3f pos = m_Position.toVector3() + Rainbow::Vector3f(0.0f, (float)(m_BoundHeight / 2 - 2), 0.0f);
		if (m_pWorld->m_PhysScene == NULL)
		{
			return;
		}
		//ClientPlayer* player = static_cast<ClientPlayer*>(getOwnerActor());
		// 物理胶囊根据系数同步调整
		float boundsize = (float)(m_BoundSize / 2 - 2);
		float boundheight = (float)(m_BoundHeight - 2 * boundsize);
		//把胶囊体高度设在m_BoundHeight中间位置
		pos.y = m_Position.toVector3().y + m_BoundHeight / 2/*  - 3*/;

		//LOG_INFO( "m_BoundHeight=[%d], m_BoundSize=[%d], boundheight=[%f], boundsize=[%f], [%d][%d][%d]", m_BoundHeight, m_BoundSize, boundheight, boundsize, pos.x, pos.y, pos.z );

		if (boundheight <= 0.0f || boundsize <= 0.0f)
			return;

		m_ActorRoleController = m_pWorld->m_PhysScene->CreateRoleCapsuleController(boundheight, boundsize, pos, GetOwnerActor());
		if (m_pWorld->getScene())
			m_pWorld->getScene()->AddGameObject(m_ActorRoleController->GetGameObject());

		MINIW::ScriptVM::game()->callFunction("ApplySurviveGameConfig", "");
		if (m_ActorRoleController && GetWorldManagerPtr() && GetWorldManagerPtr()->m_SurviveGameConfig)
		{
			//if (!m_pWorld->isRemoteMode())
			//{
			//	m_RoleController->SetMassLimit(GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.mass_limit);
			//	if (GetOwnerActor())
			//	{
			//		//ClientPlayer *player = dynamic_cast<ClientPlayer *>(getOwnerActor());
			//		PlayerControl* player_control = GetOwnerT<PlayerControl>();
			//		// 客机
			//		if (player && ((player_control != g_pPlayerCtrl) || (player_control == NULL)))
			//		{
			//			m_RoleController->setIsClient(true);
			//		}
			//	}
			//}
			//else
			//{
			//	m_RoleController->SetMassLimit(0);
			//}
			m_ActorRoleController->SetMassLimit(GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.mass_limit);
			m_ActorRoleController->SetStepOffset(20);

			//m_RoleController->GetGameObject()->AddEvent(Rainbow::Evt_ControllerColliderHit, &PlayerLocoMotion::OnRoleControllerCollision, this);
		}
	}
#endif
}

void LivingLocoMotion::detachPhysActor()
{
#ifdef USE_ACTOR_ROLECONTROLLER
	ClientPlayer* player = getOwnerActor()->ToCast<ClientPlayer>();
	if (player)
		return;

	m_pWorld->m_PhysScene->DeleteRoleController(m_ActorRoleController);
	m_ActorRoleController = NULL;

	//for (int i = 0; i < (int)m_preCheckPhy.size(); i++)
	//{
	//	if (m_pWorld)
	//		m_pWorld->updateLeaveSectionPhysics(m_preCheckPhy[i].x, m_preCheckPhy[i].y, m_preCheckPhy[i].z);
	//}
	//m_preCheckPhy.clear();
#endif
}

void LivingLocoMotion::setMoveType(int val)
{
	m_UseMoveType.Set(val);
}

IMPLEMENT_COMPONENTCLASS(SouvenirMotion)

void SouvenirMotion::attachPhysActor()
{
	if (m_pWorld->isRemoteMode()) return;

	if (m_PhysActor == NULL)
	{
		int offsetY = m_Position.y - CoordDivBlock(m_Position.y) * BLOCK_SIZE;
		Rainbow::Vector3f pos = BlockBottomCenter(CoordDivBlock(m_Position)).toVector3() + Rainbow::Vector3f(0.0f, 40 + offsetY, 0.0f);
		float mass = 9999999999.f;
		WCoord halfexten(40, 40, 40);
		m_PhysActor = m_pWorld->m_PhysScene->AddRigidStaticActor(pos, Rainbow::Quaternionf::identity, halfexten.toVector3(), 0.8f, 0.8f, 0.8f);
		//m_PhysActor = m_pWorld->m_PhysScene->AddRigidStaticActor(pos, Rainbow::Quaternionf::identity, m_BoundHeight / 2.0f, g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_static_friciton, g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_dynamic_friciton, g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_restitution, mass, false, getOwnerActor());
	}
}

void SouvenirMotion::detachPhysActor()
{
	if (m_pWorld->isRemoteMode()) return;

	if (m_PhysActor)
	{
		m_pWorld->m_PhysScene->DeleteRigidActor(m_PhysActor);
		m_PhysActor = NULL;
	}
}

void SouvenirMotion::tick()
{
	LivingLocoMotion::tick();
	if (m_PhysActor)
	{
		Rainbow::Vector3f pos;
		Rainbow::Quaternionf quat;
		m_PhysActor->GetPos(pos, quat);
		m_Motion = pos - m_Position.toVector3();
		m_Position = pos;
		m_Position.y -= 40;
		m_RotateQuat = quat;
		Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, -m_RotateQuat.GetAxisZ());
	}
}
