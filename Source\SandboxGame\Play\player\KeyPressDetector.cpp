#include "KeyPressDetector.h"
#include "PlayerControl.h"
#include "ClientInfoProxy.h"
#include "PCControl.h"

KeyPressDetector::KeyPressDetector(int key)
    : m_keyState({0})
    , m_pressTime(0.0f)
    , m_longPressThreshold(0.5f) // 默认500毫秒
    , m_keyCode(key)
{
}

KeyPressDetector::~KeyPressDetector()
{
}

void KeyPressDetector::update(float dtime)
{
    // 对于PC平台，获取按键状态并更新
    if (!GetClientInfoProxy()->isPC())
    {
        return;
    }

    m_keyState.m_keystatus = g_pPlayerCtrl->m_PCCtrl->GetKey(m_keyCode);

    //没有按键事件触发
    if (!m_keyState.m_isKeyHeld && !m_keyState.m_keystatus && !m_keyState.m_isKeyUp)
        return;
    //上一帧是抬起 恢复状态
    if (!m_keyState.m_keystatus && m_keyState.m_isKeyUp)
    {
        m_keyState.m_isKeyUp = false;
        m_keyState.m_isKeyDown = false;
        m_keyState.m_isLongPress = false;
        m_keyState.m_isShortPress = false;
        m_keyState.m_isKeyHeld = false;
        m_pressTime = 0.0f;
        return;
    }

    //按键按下的状态
    if (m_keyState.m_keystatus)
    {
        m_keyState.m_isKeyUp = false;
        //上一帧是按下那么之后就一直是false
        if (m_keyState.m_isKeyHeld && m_keyState.m_isKeyDown)
        {
            m_keyState.m_isKeyDown = false;
        }
        if (!m_keyState.m_isKeyHeld)
        {
            m_keyState.m_isKeyDown = true;
        }
    }
    else
    {
        m_keyState.m_isKeyUp = true;
        m_keyState.m_isKeyDown = false;
    }
    m_keyState.m_isKeyHeld = m_keyState.m_keystatus;

    // 按键刚被按下
    if (m_keyState.m_isKeyDown)
    {
        LOG_WARNING("m_pressTime = 0");
        m_pressTime = 0.0f;
    }
        
    // 按键正在被按住
    if (m_keyState.m_isKeyHeld)
    {
        m_pressTime += dtime;

        // 检测长按
        if (m_pressTime >= m_longPressThreshold && !m_keyState.m_isLongPress)
        {
            LOG_WARNING("isLongPress");
            m_keyState.m_isLongPress = true;
        }
    }
        
    // 按键刚被释放且之前被按住过
    if (m_keyState.m_isKeyUp)
    {
        // 如果时间短于阈值，则为短按
        if (m_pressTime < m_longPressThreshold)
        {
            LOG_WARNING("isShortPress");
            m_keyState.m_isShortPress = true;
        }

        // 重置计时和长按状态
        m_pressTime = 0.0f;
        m_keyState.m_isLongPress = false;
    }
}

void KeyPressDetector::tick()
{
}

void KeyPressDetector::reset()
{
    m_keyState.m_isKeyDown = false;
    m_keyState.m_isKeyHeld = false;
    m_keyState.m_isKeyUp = false;
    m_keyState.m_isShortPress = false;
    m_keyState.m_isLongPress = false;
    m_pressTime = 0.0f;
    m_keyState.m_keystatus = false;
}
