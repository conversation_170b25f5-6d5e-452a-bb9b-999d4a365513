#include "CosumeAccumComponent.h"
#include "EffectManager.h"
#include "DefManagerProxy.h"
#include "ClientPlayer.h"
#include "world.h"
#include "special_blockid.h"
#include "ActorLocoMotion.h"

#include "PlayerAttrib.h"
#include "OgreScriptLuaVM.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "SandboxSchedulerManager.h"
#include <assert.h>
#include "SoundComponent.h"
#include "ClientActorFuncWrapper.h"
#include "TemperatureManager.h"
#include "WorldManager.h"

using namespace MNSandbox;


class CosumeAccumItem : public MNSandbox::Object//抽象
{
public:
	CosumeAccumItem()
		:m_ownerPlayer(NULL)
		,m_itemID(0)
		,m_accumNum(0)
	{
	}

	virtual void step(){
		if(isSuitCondition()){
			onTrigger();
		}
	};

	void setOwnerPlayer(ClientPlayer* player) {
		m_ownerPlayer = player;
	}

	void setItemID(int itemId) {
		m_itemID = itemId;
	}

protected:
	virtual void onTrigger() = 0;
	virtual bool isSuitCondition() =  0;

protected:
	ClientPlayer*  m_ownerPlayer;
	int            m_itemID;
	int            m_accumNum;
};

class LuaAccumItem : public CosumeAccumItem
{
public:
	LuaAccumItem()
	{
		Event().CreateEventDispatcher("OnInitItem");
		Event().CreateEventDispatcher("OnTrigger");
		Event().CreateEventDispatcher("OnIsSuitCondition");
	}

	void initItem(const char* compName)
	{
		if (compName != NULL)
			LuaPluginMgr().BindLuaPluginByFile(compName); // 绑定插件，通过文件

		SandboxContext args(this);
		args.SetData_Userdata("ClientPlayer", "player", m_ownerPlayer);
		args.SetData_Number("itemId", m_itemID);
		Event().Emit("OnInitItem", args);
	}
protected:
	virtual void onTrigger() override {
		Event().Emit("OnTrigger", SandboxContext(this));
	}
	virtual bool isSuitCondition() override {
		return Event().Emit("OnIsSuitCondition", SandboxContext(this)).IsExecSuccessed();
	}
};

//蓝图
class BluePrintAccumItem :public CosumeAccumItem
{
public:
	BluePrintAccumItem(){
		setItemID(ITEM_BLUEPRINT);
	}
protected:
	virtual void onTrigger() override {
		m_accumNum = 0;

		World *pWorld               = m_ownerPlayer->getWorld();
		ActorLocoMotion *locomotion = m_ownerPlayer->getLocoMotion();

		if(pWorld && locomotion){
			WCoord pos         = locomotion->getPosition();
			Rainbow::Vector3f dir = locomotion->getLookDir();
			dir.y += 0.5;
			pos += WCoord(2 * BLOCK_SIZE * dir);
			pWorld->getEffectMgr()->playParticleEffectAsync("particles/item_zhishiqi.ent", pos, 20, 0, 0, false);
		}

	}
	virtual bool isSuitCondition() override {
		return  m_ownerPlayer->getCurToolID() == m_itemID && m_accumNum++ >= 10;
	}
};

class JetpackCosumeItem : public CosumeAccumItem
{
public:
	JetpackCosumeItem() {
		setItemID(ITEM_JETPACK);
		m_JetpackCosumeAccum = 0.0f;
	}
protected:
	virtual void onTrigger() override {

		m_ownerPlayer->addCurDorsumDuration(m_accumNum);
		m_JetpackCosumeAccum -= m_accumNum;
	
	}
	virtual bool isSuitCondition() override {
		auto functionWrapper = m_ownerPlayer->getFuncWrapper();
		if(functionWrapper && functionWrapper->getJetpackFlying() && m_ownerPlayer->getCurDorsumID() == m_itemID){
			m_JetpackCosumeAccum += 5.0f / 20;
			m_accumNum = int(m_JetpackCosumeAccum);
			return m_accumNum != 0;
		}
		return false;
	}
private:
	float m_JetpackCosumeAccum;
};

class FireRocketCosumeItem : public CosumeAccumItem
{
public:
	FireRocketCosumeItem() {
		setItemID(ITEM_FIRE_ROCKET);
	}
protected:
	virtual void onTrigger() override {
		//temp
		auto functionWrapper = m_ownerPlayer->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(0);
		}
		//
		int equipItemDuration = m_ownerPlayer->getEquipItemDuration(EQUIP_PIFENG);
		//
		if (!( equipItemDuration % 20))
		{
			const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(m_itemID);
			if (tooldef && tooldef->Duration > 0)
			{
				const char* soundName = NULL;
				if (equipItemDuration > (tooldef->Duration * 0.25))
					soundName = "item.12822.fly";
				else
					soundName = "item.12822.count";
				auto sound = m_ownerPlayer->getSoundComponent();
				if (sound)
				{
					sound->playSound(soundName, 1.0f, 1.0f);
				}
			}
		}
		m_ownerPlayer->addCurDorsumDuration(1);
	}
	virtual bool isSuitCondition() override {
		return  m_ownerPlayer->getCurDorsumID() == m_itemID;
	}
};

class SnakeGodWingCosumeItem : public CosumeAccumItem
{
public:
	SnakeGodWingCosumeItem() {
		setItemID(ITEM_SNAKEGOD_WING);
	}
protected:
	virtual void onTrigger() override {
		m_accumNum = 0;
		m_ownerPlayer->addCurDorsumDuration(1);
	}
	virtual bool isSuitCondition() override {
		if(m_ownerPlayer->m_SnakeGodWingFlying && m_ownerPlayer->getCurDorsumID() == m_itemID){
			m_accumNum++;
			return m_accumNum >= 20;
		}
		return false;
	}
};

//氧气背包、防火服等背部装备耐久消耗统一处理
class CommonBackCosumeItem : public CosumeAccumItem
{

protected:
	virtual void onTrigger() override {
		m_accumNum = 0;
		if (m_itemID == ITEM_FIRESAFETY_PACK)
		{
			World* pWorld = m_ownerPlayer->getWorld();
			if (pWorld)
			{
				auto pos = m_ownerPlayer->getPosition();
				auto blockpos = CoordDivBlock(pos);
				float temp = 0.0f;
				int level = TEMPERATURE_LEVEL_NONE;
				pWorld->GetWorldMgr()->getTemperatureMgr()->GetBlockTemperatureAndLevel(pWorld, blockpos, temp, level);
				if (level > TEMPERATURE_LEVEL_HEAT)
				{
					m_ownerPlayer->addCurDorsumDuration(2);
				}
			}
		}
		else
		{
			m_ownerPlayer->addCurDorsumDuration(2);
		}
		//TODO:音效可以做成配置
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(m_itemID);
		if (tooldef && tooldef->Duration > 0)
		{
			const char* soundName = NULL;
			if (m_ownerPlayer->getEquipItemDuration(EQUIP_PIFENG) > (tooldef->Duration * 0.1))
				soundName = "item.12275.blow";
			else
				soundName = "item.12275.warn";//最后1/10发警告声
			auto sound = m_ownerPlayer->getSoundComponent();
			if (sound)
			{
				sound->playSound(soundName, 1.0f, 1.0f);
			}
		}		
	}
	virtual bool isSuitCondition() override {
		if(m_ownerPlayer->getCurDorsumID() == m_itemID){
			m_accumNum++;
			return m_accumNum >= 20;
		}
		return false;
	}	
};

class FireSafetyPackCosumeItem : public CommonBackCosumeItem
{
public:
	FireSafetyPackCosumeItem() {
		setItemID(ITEM_FIRESAFETY_PACK);
	}
protected:
	virtual bool isSuitCondition() override {

		if(m_ownerPlayer->getCurMapID() != MAPID_LIEYANSTAR)
			return false;

		return CommonBackCosumeItem::isSuitCondition();
	}	
};

//氧气背包，在萌眼星则自然消耗，如果在水中则根据时机消耗走 LivingAttrib 消耗逻辑消耗
class OxyGenCosumeItem : public CommonBackCosumeItem
{
public:
	OxyGenCosumeItem() {
		setItemID(ITEM_OXYGEN_PACK);
	}
protected:
	virtual bool isSuitCondition() override {

		if (m_ownerPlayer->getCurMapID() != MAPID_MENGYANSTAR)
			return false;

		return CommonBackCosumeItem::isSuitCondition();
	}
};

class BuffCheckItem : public CosumeAccumItem
{
public:
	BuffCheckItem() {
		setItemID(ITEM_FIRESAFETY_PACK);
	}
protected:
	virtual void onTrigger() override {
		m_accumNum -= 40;
		//温度系统,之前抵抗萌眼星炎热debuff【73001】的功能需要去掉
		//if(m_ownerPlayer->getCurMapID() == MAPID_LIEYANSTAR)
		//{
		//	PlayerAttrib*  playerAttrib = m_ownerPlayer->getPlayerAttrib();
		//	if(playerAttrib){
		//		int bufLevel = m_ownerPlayer->getCurDorsumID() == m_itemID ? 2 : 1;
		//		if(!playerAttrib->hasBuff(HOT_BUFF, bufLevel))
		//			playerAttrib->addBuff(HOT_BUFF, bufLevel);
		//	}
		//}	
	}
	virtual bool isSuitCondition() override {
		return ++m_accumNum > 40;
	}	
};

// 陨魔假面 ITEM_MAGIC_MASK
class MagicMaskItem : public CosumeAccumItem
{
public:
	MagicMaskItem() {
		setItemID(ITEM_MAGIC_MASK);
	}
protected:
	virtual void onTrigger() override {
		m_accumNum = 0;
		auto pAttrib = m_ownerPlayer->getLivingAttrib();
		if (pAttrib != NULL) pAttrib->damageEquipItemWithType(EQUIP_HEAD, 1);
	}
	virtual bool isSuitCondition() override {
		auto pAttrib = m_ownerPlayer->getLivingAttrib();
		if (pAttrib && pAttrib->getEquipItemWithType(EQUIP_HEAD) == m_itemID) {
			m_accumNum++;
			return m_accumNum >= 80;
		}
		return false;
	}
};

IMPLEMENT_COMPONENTCLASS(CosumeAccumComponent)

CosumeAccumComponent::CosumeAccumComponent()
{
	initRegisters();

	Event().CreateEventDispatcher("OnInitItems");
	//Event().CreateEventDispatcher("OnEnter");
	//Event().CreateEventDispatcher("OnLeave");
	//Event().CreateEventDispatcher("OnUpdate");
}

CosumeAccumComponent::~CosumeAccumComponent()
{
	clearItems();
}
void CosumeAccumComponent::OnTick()
{
	for (auto it = m_items.begin(); it != m_items.end(); it++)
		(*it)->step();
}

void CosumeAccumComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	initItems(dynamic_cast<ClientPlayer*> (owner));
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void CosumeAccumComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	clearItems();
	Super::OnLeaveOwner(owner);
}

void CosumeAccumComponent::clearItems()
{
	for (auto it = m_items.begin(); it != m_items.end(); it++)
	{
		CosumeAccumItem* item = *it;
		SANDBOX_DELETE(item);
	}
	m_items.clear();
}

void CosumeAccumComponent::initItems(ClientPlayer* playerOwner)
{
	clearItems();
	if (playerOwner) {

		if (Event().Emit("OnInitItems", SandboxContext(this).SetData_Userdata("ClientPlayer","player" ,playerOwner)).IsExecSuccessed())
			return;
		//default
		addItem("BluePrint");
		addItem("Jetpack");
		addItem("FireRocket");
		addItem("SnakeGodWing");
		addItem("OxyGenPack");
		addItem("CommonBack", ITEM_OXYGEN_MASK);
		addItem("CommonBack", ITEM_FIRESAFETY_PACK);
		addItem("FireSafetyPack");
		addItem("BuffCheck");
		addItem("MagicMask");
	}
}
std::map<std::string, std::function<CosumeAccumItem*()>> CosumeAccumComponent::m_registers;
static bool isInit = false;

void CosumeAccumComponent::initRegisters()
{
	if (isInit)
		return;
	isInit = true;
	RegisterCreator<BluePrintAccumItem>("BluePrint");
	RegisterCreator<JetpackCosumeItem>("Jetpack");
	RegisterCreator<FireRocketCosumeItem>("FireRocket");
	RegisterCreator<SnakeGodWingCosumeItem>("SnakeGodWing");
	RegisterCreator<CommonBackCosumeItem>("CommonBack");
	RegisterCreator<FireSafetyPackCosumeItem>("FireSafetyPack");
	RegisterCreator<OxyGenCosumeItem>("OxyGenPack");
	RegisterCreator<BuffCheckItem>("BuffCheck");
	RegisterCreator<MagicMaskItem>("MagicMask");
	//RegisterCreator<LuaAccumItem>("LuaAccumItem");
	return;
}

void CosumeAccumComponent::clearRegisters()
{
	m_registers.clear();
	isInit = false;
}

bool CosumeAccumComponent::addItem(const char* name, int itemid)
{
	auto creator = m_registers.find(name);
	if (!GetOwner()) return false;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	if (creator != m_registers.end()) {
		CosumeAccumItem* item = creator->second();
		if (item) {
			if (itemid != 0) {
				item->setItemID(itemid);
			}
			item->setOwnerPlayer(m_owner);
			m_items.push_back(item);
			return true;
		}
	}
	return false;
}

bool CosumeAccumComponent::addLuaItem(const char* filepath, int itemid)
{
	LuaAccumItem* item = SANDBOX_NEW(LuaAccumItem);
	if(itemid != 0)
		item->setItemID(itemid);
	if (!GetOwner()) return false;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	item->setOwnerPlayer(m_owner);
	item->initItem(filepath);
	m_items.push_back(item);
	return true;
}

template<typename T>
bool CosumeAccumComponent::RegisterCreator(const char* type) {
	if (type == NULL)
		return false;
	//repeated type will be replaced
	m_registers[type] = []() -> CosumeAccumItem* {
		return static_cast<CosumeAccumItem*> (SANDBOX_NEW(T));
	};
	return true;
}

