#ifndef __CLIENT_ACTOR_FISHHOOK_H__
#define __CLIENT_ACTOR_FISHHOOK_H__

#include "ClientActorProjectile.h"
namespace game {
	namespace hc {
		class PB_GeneralEnterAOIHC;
	}
}

class ClientActorFishhook : public ClientActorProjectile
{
public:
	ClientActorFishhook();

	virtual void onImpactWithActor(ClientActor* actor, const std::string& partname) override;
	virtual void onImpactWithBlock(const WCoord* blockpos, int face) override;
	virtual void onCollideWithPlayer(ClientActor* player) override;

	virtual int saveToPB(game::hc::PB_GeneralEnterAOIHC* pb);
	virtual int LoadFromPB(const game::hc::PB_GeneralEnterAOIHC& pb);

	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata, int version) override;

	virtual int getObjType() const override
	{
		return OBJ_TYPE_FISHHOOK;
	}

	void tick() override;

	void onImpactSolid();
	void onPickEnd();
	void onEnterWater();

	void addGotItem(int itemid);
	void clearGotItem();
	int getGotItemId() { return m_GotItemID; }

	static ClientActorFishhook* shootFishhookAuto(int itemid, World* pworld, const WCoord& pos, const WCoord& targetPos, const Rainbow::Vector3f& motion, long long shooterObjId, int minBlockPosY);

protected:
	virtual ~ClientActorFishhook();

private:
	Rainbow::MovableObject* m_GotModel;		// ��õ���Ʒģ��
	int m_GotItemID;
};

#endif // !__CLIENT_ACTOR_FISHHOOK_H__
