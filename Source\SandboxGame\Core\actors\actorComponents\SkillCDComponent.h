#ifndef __SKILL_CD_COMPONENT_H__
#define __SKILL_CD_COMPONENT_H__

#include <map>

#include "WorldRole_generated.h"
#include "ActorComponent_Base.h"

class ClientPlayer;

namespace game {
	namespace common {
		class PB_SkillCDData;
	}
};

class SkillCDComponent : public ActorComponentBase
{
public:
	DECLARE_COMPONENTCLASS(SkillCDComponent)

	SkillCDComponent();
	~SkillCDComponent();
	void onUpdate(float dttime);

	//int getCurCDCount();

	void setSkillCD(int itemid, float cd);
	void setItemSkillCD(int itemid, float cd);
	float getSkillCD(int itemid);

	void syncSkillCD(int itemid, float cd);
	float getTotalSkillCD(int itemid);

	void saveToPB(game::common::PB_SkillCDData* skillCDData);
	void load(const flatbuffers::Vector<flatbuffers::Offset<FBSave::SkillCDData>> *skillCDData);
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::SkillCDData>>> save(flatbuffers::FlatBufferBuilder &builder);
	int getSkillCDSize() const { return m_SkillCD.size(); }
protected:
	ClientPlayer* m_player;
	std::map<int, float> m_SkillCD; //存放使用了技能的冷却时间  计时结束后清零
};
#endif