#include "OutWorldCheck.h"

#include "ClientActor.h"
#include "world.h"

using namespace MNSandbox;


OutWorldCheck::OutWorldCheck(int ylimit)
:m_YLimit(ylimit)
{

}

bool OutWorldCheck::isInRange(ClientActor* pActor, BlockEnvEffectBase* pEffect)
{
	if (!pActor)
	{
		return false;
	}
	ClientActor* owner = pActor;// context.GetData_Usertype<ClientActor>("owner");
	assert(owner != nullptr);
	World* pWorld      = owner->getWorld();
	assert(pWorld != nullptr);

	const WCoord& pos = owner->getPosition();
	if(!pWorld->isRemoteMode() && pos.y < m_YLimit && !owner->isDead())
		return true;

	return false;
}