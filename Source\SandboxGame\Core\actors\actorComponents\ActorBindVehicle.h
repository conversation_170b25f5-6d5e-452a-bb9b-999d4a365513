#ifndef __ACTOR_BIND_VEHICLE_H__
#define __ACTOR_BIND_VEHICLE_H__

#include "world_types.h"
#include "Math/Vector3f.h"
#include "Math/Quaternionf.h"
#include "ActorComponent_Base.h"
#include "SandboxGame.h"
class ClientActor;
class ActorVehicleAssemble;
class Block;
class EXPORT_SANDBOXGAME ActorBindVehicle;
class ActorBindVehicle : public ActorComponentBase //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(ActorBindVehicle)
public:
	//tolua_begin
	ActorBindVehicle();	
	
	WORLD_ID getVehicleID() { return m_VehicleID;}
	WCoord	getVehicleAttachPos() { return m_VehicleAttachPos;}

    virtual bool getInVehiclePos(Rainbow::Vector3f &pos);

	void updatePosByVehicle();

	Block getVehicleAttachPosBlock();
	void setBindRot(bool &setbindrot, Rainbow::Vector3f &pos, Rainbow::Quaternionf &quat);


	void Bind(long long vehicleObjId, const WCoord &attachpos, bool sendMsg = false);
	void UnBind(bool sendMsg = false);
	//tolua_end
protected:
	virtual bool getInVehicleQuat(Rainbow::Quaternionf &quat);
	ActorVehicleAssemble* getBindVehicle();
	void sendBroadCastBindMsg(long long targetObjId, long long vehicleObjId, const WCoord& pos);

	ClientActor* m_owner;
	WORLD_ID m_VehicleID;//绑定的载具id
	WCoord	m_VehicleAttachPos;//绑定的载具位置
}; //tolua_exports

class PlayerBindVehicle : public ActorBindVehicle //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(PlayerBindVehicle)
public:
	//tolua_begin
	PlayerBindVehicle();
    virtual bool getInVehiclePos(Rainbow::Vector3f &pos) override;
	//tolua_end
protected:
	virtual bool getInVehicleQuat(Rainbow::Quaternionf &quat) override;
}; //tolua_exports

#endif