﻿
#include "BlockBigChest.h"
#include "BlockMaterialMgr.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "WorldProxy.h"
#include "special_blockid.h"
#include "EffectManager.h"
#include "IClientPlayer.h"
#include "IPlayerControl.h"
#include "WorldManager.h"
#include "coreMisc.h"
#include "container_world.h"
#include "container_erosion_storage.h"

using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(BigChestMaterial)

BigChestMaterial::BigChestMaterial()
{

}

BigChestMaterial::~BigChestMaterial()
{

}

void BigChestMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;
	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);

	int boxid = 0;
	int placedir = pblock.getData() & 3;
	int ismirror = (pblock.getData() & 4) > 0;

	if (isHorizontalBigChest(getBlockResID())) boxid = 2;
	if (isVerticalBigChest(getBlockResID())) boxid = ismirror ? 4 : 3;

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);

	SectionSubMesh* psubmesh = poutmesh->getSubMesh(getDefaultMtl());

	BlockGeomMeshInfo meshinfo;
	geom->getFaceVerts(meshinfo, boxid, 1.0f, 0, placedir, ismirror);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, getDefaultMtl()->getUVTile());
}

bool BigChestMaterial::canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)
{
	for (int dir = 0; dir < 6; dir++)
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, dir)) == getBlockResID())
		{
			//return false;
		}
	}

	int canPutFlag = PUT_PLACE_NONE;
	if (isVerticalBigChest(getBlockResID())) canPutFlag = canPutOntoUpDown(pworld, NULL, blockpos);
	if (isHorizontalBigChest(getBlockResID())) canPutFlag = canPutOntoLeftRight(pworld, NULL, blockpos);

	return canPutFlag != PUT_PLACE_NONE;
}

bool BigChestMaterial::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (pworld->isRemoteMode()) return true;

	if (pworld->getContainerMgr() == NULL) return true;

	if (HOME_GARDEN_WORLD == pworld->getMapSpecialType())
		return true;

	WorldContainerMgr* pContainerMgr = pworld->getContainerMgr();
	WorldContainer* pCurrContainer = pContainerMgr->getContainer(blockpos);
	if (pCurrContainer == NULL) return true;

	bool ismajor = true;
	if (pworld->getBlockID(blockpos) == getBlockResID())
	{
		int blockdata = pworld->getBlockData(blockpos);
		int placedir = blockdata & 3;
		int ismirror = (blockdata & 4) > 0;

		WCoord tempPos;
		if (isHorizontalBigChest(getBlockResID()))
		{
			ismajor = !ismirror;
			DirectionType face = DIR_NOT_INIT;
			if (placedir == DIR_NEG_X) face = DIR_POS_Z;
			if (placedir == DIR_POS_X) face = DIR_NEG_Z;
			if (placedir == DIR_NEG_Z) face = DIR_NEG_X;
			if (placedir == DIR_POS_Z) face = DIR_POS_X;

			if (ismirror) //righter
				tempPos = NeighborCoord(blockpos, face);
			else
				tempPos = NeighborCoord(blockpos, ReverseDirection(face));
		}
		else if (isVerticalBigChest(getBlockResID()))
		{
			ismajor = ismirror;
			if (ismirror)
				tempPos = NeighborCoord(blockpos, DIR_NEG_Y);
			else
				tempPos = NeighborCoord(blockpos, DIR_POS_Y);
		}

		if (tempPos != blockpos) //取相邻位置的方块
		{
			if (pworld->getBlockID(tempPos) == getBlockResID())
			{
				WorldContainer* pContainer = pworld->getContainerMgr()->getContainer(tempPos);
				ErosionStorageBox* pStorageBox = dynamic_cast<ErosionStorageBox*>(pContainer);
				if (pStorageBox != NULL)
				{
					if (ismajor) //当前方块为主箱 左方块或下方块
					{
						ErosionStorageBox* pCurrBox = dynamic_cast<ErosionStorageBox*>(pCurrContainer);
						if (pCurrBox != NULL) pCurrBox->append(pStorageBox);
					}
					else
					{
						pStorageBox->append(dynamic_cast<ErosionStorageBox*>(pCurrContainer));
						pCurrContainer = pStorageBox;
					}
				}
			}
		}
	}
	if (pCurrContainer && player)
	{
		pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), "misc.chest_open", 1.0f, GenRandomFloat() * 0.2f + 0.8f);
		player->openContainer(pCurrContainer);		
		GetWorldManagerPtr()->getWorldInfoManager()->openContainerNoticeActorVillager(pworld, player, blockpos);
	}
	return true;
}

void BigChestMaterial::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	if (blockid != m_BlockResID)
		return;
	DirectionType face = DIR_NOT_INIT;
	int blockdata = pworld->getBlockData(blockpos);

	int placedir = blockdata & 3;
	int ismirror = (blockdata & 4) > 0;

	WCoord tempCoord;
	if (isHorizontalBigChest(getBlockResID()))
	{
		if (placedir == DIR_NEG_X) face = DIR_POS_Z;
		if (placedir == DIR_POS_X) face = DIR_NEG_Z;
		if (placedir == DIR_NEG_Z) face = DIR_NEG_X;
		if (placedir == DIR_POS_Z) face = DIR_POS_X;
		if (face == DIR_NOT_INIT) return;
		if (ismirror)
			tempCoord = NeighborCoord(blockpos, face);
		else
			tempCoord = NeighborCoord(blockpos, ReverseDirection(face));
	}
	else if (isVerticalBigChest(getBlockResID()))
	{
		if (ismirror)
			tempCoord = NeighborCoord(blockpos, DIR_NEG_Y);
		else
			tempCoord = NeighborCoord(blockpos, DIR_POS_Y);
	}

	if (pworld->getBlockID(tempCoord) != getBlockResID())
	{
		pworld->setBlockAir(blockpos);

		//工具不展示掉落物
#ifndef BUILD_MINI_EDITOR_APP
		if (!ismirror && GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerMode())
			dropBlockAsItem(pworld, blockpos, blockdata);
#endif
	}
}

bool BigChestMaterial::isMajor(World* pworld, const WCoord& blockpos)
{
	bool ismajor = true;
	int blockdata = pworld->getBlockData(blockpos);
	int ismirror = (blockdata & 4) > 0;

	if (isHorizontalBigChest(getBlockResID()))
	{
		ismajor = !ismirror;
	}
	else if (isVerticalBigChest(getBlockResID()))
	{
		ismajor = ismirror;
	}
	return ismajor;
}

void BigChestMaterial::initGeomName() {
	if (m_Def->Texture2.size() > 0)
		m_geomName = m_Def->Texture2.c_str();
	else
		m_geomName = "box";
}

int BigChestMaterial::canPutOntoUpDown(WorldProxy* pworld, BaseSection* psection, const WCoord& blockpos)
{
	WCoord wcoord(0, 0, 0);
	return canPutOntoUpDown(pworld, psection, blockpos, wcoord);
}

int BigChestMaterial::canPutOntoUpDown(WorldProxy* pworld, BaseSection* psection, const WCoord& blockpos, WCoord& newbpos)
{
	if (pworld == NULL && psection == NULL)
		return PUT_PLACE_NONE;

	Block neighbor;
	DirectionType faces[2] = { DIR_POS_Y , DIR_NEG_Y };
	for (int index = 0; index < 2; index++)
	{
		newbpos = NeighborCoord(blockpos, faces[index]);
		if (pworld == NULL)
			neighbor = psection->getBlock(newbpos);
		else
			neighbor = pworld->getBlock(newbpos);

		if (neighbor == NULL)
		{
			return index + 1;
		}
		else
		{
			auto mgr = g_BlockMtlMgr.getMaterial(neighbor.getResID());
			if (mgr && mgr->isReplaceable())
			{
				return index + 1;
			}
		}
	}

	return PUT_PLACE_NONE;
}

int BigChestMaterial::canPutOntoLeftRight(WorldProxy* pworld, BaseSection* psection, const WCoord& blockpos)
{
	WCoord wcoord(0, 0, 0);
	return canPutOntoLeftRight(pworld, psection, blockpos, wcoord);
}

int BigChestMaterial::canPutOntoLeftRight(WorldProxy* pworld, BaseSection* psection, const WCoord& blockpos, WCoord& newbpos)
{
	if (pworld == NULL && psection == NULL)
		return PUT_PLACE_NONE;

	if (pworld == NULL && psection == NULL)
		return PUT_PLACE_NONE;

	Block pblock;
	Block neighbor;
	int factor[2] = { 1 , -1 };

	for (int index = 0; index < 2; index++)
	{
		newbpos = blockpos;
		if (pworld == NULL)
			pblock = psection->getBlock(blockpos);
		else
			pblock = pworld->getBlock(blockpos);

		int fact = factor[index];
		int placedir = DIR_NEG_X;
		if (GetIPlayerControl()) placedir = GetIPlayerControl()->GetPlayerControlCurPlaceDir();
		if (placedir == DIR_NEG_X) newbpos.z -= 1 * fact;
		if (placedir == DIR_POS_X) newbpos.z += 1 * fact;
		if (placedir == DIR_NEG_Z) newbpos.x += 1 * fact;
		if (placedir == DIR_POS_Z) newbpos.x -= 1 * fact;
		if (newbpos == blockpos) return PUT_PLACE_NONE;

		if (pworld == NULL)
			neighbor = psection->getBlock(newbpos);
		else
			neighbor = pworld->getBlock(newbpos);

		if (neighbor == NULL)
		{
			return index + 3;
		}
		else
		{
			auto mgr = g_BlockMtlMgr.getMaterial(neighbor.getResID());
			if (mgr && mgr->isReplaceable())
			{
				return index + 3;
			}
		}
	}

	return PUT_PLACE_NONE;
}

WorldContainer* BigChestMaterial::createContainer(World* pworld, const WCoord& blockpos)
{
	//return SANDBOX_NEW(WorldStorageBox, blockpos);
	auto  box = SANDBOX_NEW(ErosionStorageBox, blockpos, getBlockResID());
	bool ismajor = isMajor(pworld, blockpos);
	if (!ismajor)
	{
		// 设置为附加箱
		box->setIsNeedErosion(false);
	}
	return box;
}

WCoord BigChestMaterial::getCoreBlockPos(World* pworld, const WCoord& blockpos)
{
	if (pworld->getBlockID(blockpos) != getBlockResID())
		return WCoord(0, -1, 0); // 无效位置

	int blockdata = pworld->getBlockData(blockpos);
	int placedir = blockdata & 3;
	int ismirror = (blockdata & 4) > 0;

	if (isHorizontalBigChest(getBlockResID()))
	{
		// 横箱左边为核心
		if (!ismirror) // 当前是左边，直接返回
		{
			return blockpos;
		}
		else // 当前是右边，找左边的核心
		{
			DirectionType face = DIR_NOT_INIT;
			if (placedir == DIR_NEG_X) face = DIR_POS_Z;
			if (placedir == DIR_POS_X) face = DIR_NEG_Z;
			if (placedir == DIR_NEG_Z) face = DIR_NEG_X;
			if (placedir == DIR_POS_Z) face = DIR_POS_X;

			if (face != DIR_NOT_INIT)
			{
				WCoord corePos = NeighborCoord(blockpos, face);
				if (pworld->getBlockID(corePos) == getBlockResID())
				{
					return corePos;
				}
			}
		}
	}
	else if (isVerticalBigChest(getBlockResID()))
	{
		// 竖箱先上后下 - 上边为核心
		if (ismirror) // 当前是上边，直接返回
		{
			return blockpos;
		}
		else // 当前是下边，找上边的核心
		{
			WCoord corePos = NeighborCoord(blockpos, DIR_POS_Y);
			if (pworld->getBlockID(corePos) == getBlockResID())
			{
				return corePos;
			}
		}
	}

	// 找不到配对的箱子或者当前就是核心，返回当前位置
	return blockpos;
}

WCoord BigChestMaterial::getExtendBlockPos(World* pworld, const WCoord& blockpos)
{
	if (pworld->getBlockID(blockpos) != getBlockResID())
		return WCoord(0, -1, 0); // 无效位置

	int blockdata = pworld->getBlockData(blockpos);
	int placedir = blockdata & 3;
	int ismirror = (blockdata & 4) > 0;

	if (isHorizontalBigChest(getBlockResID()))
	{
		// 横箱右边为扩展
		if (ismirror) // 当前是右边（扩展），直接返回
		{
			return blockpos;
		}
		else // 当前是左边（核心），找右边的扩展
		{
			DirectionType face = DIR_NOT_INIT;
			if (placedir == DIR_NEG_X) face = DIR_POS_Z;
			if (placedir == DIR_POS_X) face = DIR_NEG_Z;
			if (placedir == DIR_NEG_Z) face = DIR_NEG_X;
			if (placedir == DIR_POS_Z) face = DIR_POS_X;

			if (face != DIR_NOT_INIT)
			{
				WCoord extendPos = NeighborCoord(blockpos, ReverseDirection(face));
				if (pworld->getBlockID(extendPos) == getBlockResID())
				{
					return extendPos;
				}
			}
		}
	}
	else if (isVerticalBigChest(getBlockResID()))
	{
		// 竖箱下边为扩展
		if (!ismirror) // 当前是下边（扩展），直接返回
		{
			return blockpos;
		}
		else // 当前是上边（核心），找下边的扩展
		{
			WCoord extendPos = NeighborCoord(blockpos, DIR_NEG_Y);
			if (pworld->getBlockID(extendPos) == getBlockResID())
			{
				return extendPos;
			}
		}
	}

	// 找不到配对的箱子或者当前就是扩展，返回当前位置
	return blockpos;
}

bool BigChestMaterial::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}
	auto corepos = getCoreBlockPos(pworld, blockpos);
	if (corepos.y < 0) return false;

	ErosionContainer* container = dynamic_cast<ErosionContainer*>(pworld->getContainerMgr()->getContainer(corepos));
	if (container)
	{
		//近战伤害衰减99%，枪械伤害衰减90%
		float attenuation_ratio = 1.0f;
		//if (attack_type == ATTACK_PUNCH)
		//	attenuation_ratio = 0.01f; // 近战攻击
		//else if (attack_type == ATTACK_RANGE)
		//	attenuation_ratio = 0.1f; // 远程攻击

		container->addHp(-damage * attenuation_ratio); // 负数扣血
		return true;
	}
	else
		return Super::onBlockDamaged(pworld, corepos, player, attack_type, damage);
	return false;
}

int BigChestMaterial::getBlockHP(World* pworld, const WCoord& blockpos)
{
	auto corepos = getCoreBlockPos(pworld, blockpos);
	if (corepos.y < 0) return 0;
	
	ErosionContainer* container = dynamic_cast<ErosionContainer*>(pworld->getContainerMgr()->getContainer(corepos));
	if (container)
		return container->getHp();
	return Super::getBlockHP(pworld, corepos);
}

bool BigChestMaterial::getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf )
{
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	if (corePos != WCoord(0, -1, 0))
	{
		
		WCoord extendPos = getExtendBlockPos(pworld,corePos);
		blockList.push_back(extendPos);
		if (includeSelf) blockList.push_back(corePos);
		return true;
	}
	return false;
}


WorldContainer* BigChestMaterial::repairContainer(const WCoord& blockpos, const int& data)
{
	auto  box = SANDBOX_NEW(ErosionStorageBox, blockpos, getBlockResID());	
	bool ismajor = true;
	int blockdata = data;
	int ismirror = (blockdata & 4) > 0;

	if (isHorizontalBigChest(getBlockResID()))
	{
		ismajor = !ismirror;
	}
	else if (isVerticalBigChest(getBlockResID()))
	{
		ismajor = ismirror;
	}
	if (!ismajor)
	{
		// 设置为附加箱
		box->setIsNeedErosion(false);
	}
	return box;
}