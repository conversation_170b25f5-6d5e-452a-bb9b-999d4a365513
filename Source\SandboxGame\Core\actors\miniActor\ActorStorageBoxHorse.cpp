#include "ActorStorageBoxHorse.h"
#include "world.h"
#include "ClientPlayer.h"
#include "backpack.h"
using namespace MINIW;

ActorStorageBoxHorse::ActorStorageBoxHorse(void)
{
	m_wStorageBox = NULL;
}

ActorStorageBoxHorse::~ActorStorageBoxHorse(void)
{
	SANDBOX_DELETE(m_wStorageBox);
}

void ActorStorageBoxHorse::enterWorld(World* pworld)
{
	ActorHorse::enterWorld(pworld);

	if (m_wStorageBox)
	{
		m_wStorageBox->enterWorld(m_pWorld);
	}
}

void ActorStorageBoxHorse::onDie()
{
	ActorHorse::onDie();
	dropItems();
}
flatbuffers::Offset<FBSave::SectionActor> ActorStorageBoxHorse::save(SAVE_BUFFER_BUILDER& builder)
{
	if (m_wStorageBox)
	{
		auto mobdata = ActorHorse::saveMob(builder);

		auto pos = FBSave::Coord3(m_wStorageBox->m_BlockPos.x, m_wStorageBox->m_BlockPos.y, m_wStorageBox->m_BlockPos.z);
		auto basedata = FBSave::CreateContainerCommon(builder, m_ObjId, &pos, m_wStorageBox->m_OwnerUin);

		const int NUMGRIDS = sizeof(m_wStorageBox->m_Grids) / sizeof(m_wStorageBox->m_Grids[0]);
		flatbuffers::Offset<FBSave::ItemGrid> items2[NUMGRIDS];
		unsigned char indices[NUMGRIDS];
		int count2 = 0;

		for (int i = 0; i < NUMGRIDS; i++)
		{
			if (!m_wStorageBox->m_Grids[i].isEmpty())
			{
				items2[count2] = m_wStorageBox->m_Grids[i].save(builder);
				indices[count2] = (unsigned char)i;
				count2++;
			}
		}

		auto actor = FBSave::CreateContainerStorage(builder, basedata, builder.CreateVector(items2, count2), builder.CreateVector(indices, count2), m_wStorageBox->m_GridCount);
		auto horse = FBSave::CreateActorStorageBoxHorse(builder, mobdata, actor);
		return saveSectionActor(builder, FBSave::SectionActorUnion_ActorStorageBoxHorse, horse.Union());
	}
	else
	{
		auto mobdata = ActorHorse::saveMob(builder);
		auto horse = FBSave::CreateActorStorageBoxHorse(builder, mobdata);
		return saveSectionActor(builder, FBSave::SectionActorUnion_ActorStorageBoxHorse, horse.Union());
	}
}

// 用flatbuffer格式加载本地的数据
bool ActorStorageBoxHorse::load(const void* srcdata, int version)
{
	if (!srcdata) return false;

	auto src = reinterpret_cast<const FBSave::ActorStorageBoxHorse*>(srcdata);

	if (!src || !ActorHorse::load(src->mobdata(), version))
	{
		return false;
	}

	if (src->containers())
	{
		initStorageBox();
		const FBSave::ContainerCommon* srcdata2 = src->containers()->basedata();
		if (srcdata2->blockpos())
		{
			m_wStorageBox->m_ObjId = srcdata2->wid();
			m_wStorageBox->m_BlockPos = WCoord(srcdata2->blockpos()->x(), srcdata2->blockpos()->y(), srcdata2->blockpos()->z());
			m_wStorageBox->m_OwnerUin = srcdata2->owner();
		}

		auto items = src->containers()->items();
		auto indices = src->containers()->indices();

		for (size_t i = 0; i < items->size(); i++)
		{
			int index = indices->Get(i);
			assert(index >= 0 && index < sizeof(m_wStorageBox->m_Grids) / sizeof(m_wStorageBox->m_Grids[0]));

			m_wStorageBox->m_Grids[index].load(items->Get(i));
		}
		m_wStorageBox->m_GridCount = src->containers()->key();
	}
	return true;
}

// 定时器
void ActorStorageBoxHorse::tick()
{
	ActorHorse::tick();
}

void ActorStorageBoxHorse::update(float dtime)
{
	ActorHorse::update(dtime);
}

void ActorStorageBoxHorse::initStorageBox()
{
	if (!m_wStorageBox)
	{
		m_wStorageBox = SANDBOX_NEW(WorldStorageBox);
		m_wStorageBox->setBaseIndex(STORAGE_START_INDEX);
		m_wStorageBox->m_GridCount = 10;
		m_wStorageBox->m_isRideContainer = true;
		WCoord pos = WCoord(getPosition().x / BLOCK_SIZE, getPosition().y / BLOCK_SIZE, getPosition().z / BLOCK_SIZE);
		m_wStorageBox->m_BlockPos = pos;
		if (m_pWorld)
		{
			m_wStorageBox->enterWorld(m_pWorld);
		}
	}
}

void ActorStorageBoxHorse::openStorageBox()
{
	initStorageBox();
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(getRiddenByActor());
	if (player)
	{
		WCoord pos = WCoord(getPosition().x / BLOCK_SIZE, getPosition().y / BLOCK_SIZE, getPosition().z / BLOCK_SIZE);
		//m_wStorageBox->m_BlockPos = pos;
		player->openContainer(m_wStorageBox);
	}
}

void ActorStorageBoxHorse::dropItems()
{
	if (m_wStorageBox)
	{
		//m_wStorageBox->dropItems(WCoord());

		World* pWorld = getWorld();
		if (!pWorld) return;
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
		if (!actorMgr) return;

		WCoord pos = getLocoMotion()->getPosition();
		pos.y += getLocoMotion()->m_BoundHeight / 2;

		int range = BLOCK_SIZE * 3 / 2;
		pos.x += GenRandomInt(range) - GenRandomInt(range);
		pos.z += GenRandomInt(range) - GenRandomInt(range);


		for (int i = 0; i < STORAGEBOX_CAPACITY; i++)
		{
			if (!m_wStorageBox->m_Grids[i].isEmpty())
			{
				actorMgr->spawnItem(pos, m_wStorageBox->m_Grids[i].getItemID(), m_wStorageBox->m_Grids[i].getNum());
			}
		}

	}
}