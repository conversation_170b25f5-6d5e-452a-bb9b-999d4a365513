
#include "container_signs.h"
#include "BlockScene.h"
#include "BlockSigns.h"
#include "Text3D/Text3D.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
#include "EffectParticle.h"
#include "WorldStringManagerProxy.h"
#include "ClientInfoProxy.h"
//#include "StringDefCsv.h"
#include "DefManagerProxy.h"
#include "chunk.h"

using namespace MNSandbox; 
using namespace Rainbow;
WorldSignsContainer::WorldSignsContainer() : WorldContainer(WCoord(0,0,0), SIGNS_START_INDEX), m_TextInit(false), 
	m_FontType(0), m_TextColor(255,255,255), m_TextObj(NULL), m_ChangeTime(0), m_TextWidth(1)
{
	CreateEvent2();
	m_NeedTick = true;
}

WorldSignsContainer::WorldSignsContainer(const WCoord &blockpos) : WorldContainer(blockpos, SIGNS_START_INDEX), m_TextInit(false),
	m_FontType(0), m_TextObj(NULL), m_ChangeTime(0), m_TextWidth(1)
{
	CreateEvent2();
	m_NeedTick = true;
}

void WorldSignsContainer::CreateEvent2()
{
	typedef ListenerFunctionRef<> Listener1;
	m_listenerWorldSigns1 = SANDBOX_NEW(Listener1, [&]() -> void
		{
			this->ReshText();
		});
	Event2().Subscribe("SignsContainer_ReshText", m_listenerWorldSigns1);

}
void WorldSignsContainer::DestroyEvent2()
{
	SANDBOX_RELEASE(m_listenerWorldSigns1);
}

void WorldSignsContainer::init()
{
#ifndef IWORLD_SERVER_BUILD
	if (m_FontType == 0) m_TextObj = Rainbow::Text3D::Create(14, 130, 75, 40.0f, 20.0f, false, true);
	else if (7 != m_FontType)
	{
		static int dims[] = {40, 35, 30, 25, 20, 18};
		float s = float(dims[m_FontType-1]);
		if (m_TextWidth==2)//2格宽牌匾
		{
			m_TextObj = Rainbow::Text3D::Create(64, 255, 50, 80, 15, false, true);
			m_TextColor.set(173, 126, 66, 255);
		}
		else
		{
			m_TextObj = Rainbow::Text3D::Create(64, 56, 56, s, s, false, true);
		}
		
	}
#endif
	if (m_FontType == 6)
	{
		m_TextColor.set(0, 0, 0, 255);
	}
	if (m_FontType == 7)
	{
		m_TextColor.set(0, 0, 0, 100);//设置透明
	}
	if (m_TextObj)
		m_TextObj->setRenderType(Rainbow::CANNOT_IN_SNAPSHOT);
	countMuralUpdate = 0;
}

WorldSignsContainer::~WorldSignsContainer()
{
	if (m_World)
	{
		m_World->RemoveWorldSignsContainer(m_BlockPos);
	}
	if (m_TextObj) {
		Rainbow::GameObject::Destroy(m_TextObj->GetGameObject());
	}
	DestroyEvent2();
}
void WorldSignsContainer::setMuralFontColor(int r, int g, int b, int a)
{
	m_TextColor.set(r, g, b, a);
	m_TextObj->setVisible(false);
}
int WorldSignsContainer::getObjType() const
{
	return OBJ_TYPE_SIGNS;
}

void WorldSignsContainer::applyBlockDir()
{
	if(m_World->onClient() && m_TextObj)
	{
		Rainbow::WorldPos pos;
		Rainbow::Quaternionf rot;
		BlockSigns::computeTextXform(pos, rot, m_World, static_cast<VehicleWorld*>(m_vehicleWorld), m_BlockPos);

		m_TextObj->SetRotation(rot);
		m_TextObj->SetPosition(pos);
		m_TextObj->update(0);

	}
}

void WorldSignsContainer::setFontColor(int r, int g, int b)
{
	m_TextColor.set(r, g, b, 255);
}

void WorldSignsContainer::ReshText()
{

	if (m_TextObj)
	{
		bool enableShow = false;
		MINIW::ScriptVM::game()->callFunction("CheckEnableShow", "w>b", m_ChangeTime, &enableShow);
		std::string showText = "";
		if (!enableShow)
		{
			if (m_oldLoadText != "")
			{
				showText = m_oldLoadText;
			}
			else 
			{
				char str[512]= { 0 };
				MINIW::ScriptVM::game()->callFunction("GetFilterHideUnmoderatedText", "i>s", m_FontType, str);
				showText = str;
			}
			if (m_loadText != "")
			{
				m_TextObj->setText(showText.c_str());
			}
		}
		else
		{
			m_TextObj->setText(m_loadText.c_str());
		}

	}
}

std::string WorldSignsContainer::dealShowText(const void *srcdata, std::string text)
{
	auto src = reinterpret_cast<const FBSave::ContainerSigns*>(srcdata);
	int lang = GetClientInfoProxy()->getArchiveLang();		//当前选择的语言
	std::string showText = GetClientInfoProxy()->parseTextFromLanguageJson(text, lang);
	if (m_FontType == 0)
	{
		const FBSave::ContainerCommon* commonData = src->basedata();
		const FBSave::Coord3* signsPos = commonData->blockpos();
		char signKey[64];
		sprintf(signKey, "board_%d_%d_%d_%llu", signsPos->x(), signsPos->y(), signsPos->z(), commonData->wid());

		SandboxResult sandboxResult = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("WorldStringTranslateMgr_getTransStrByKey", SandboxContext(nullptr)
			.SetData_Number("type", 7)
			.SetData_String("key", signKey)
			.SetData_String("oldVal", showText)
			.SetData_String("oldMultiLanVal", text));
		if (sandboxResult.IsExecSuccessed())
		{
			showText = sandboxResult.GetData_String();
		}
	}
	GetDefManagerProxy()->filterStringDirect((char*)showText.c_str());
	return showText;
}

void WorldSignsContainer::enterWorld(World *pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	registerUpdateDisplay();

	if (m_World && m_World->onClient() && m_TextObj)
	{
		applyBlockDir();
		float dist = (m_FontType == 0 ? 16 : 32)*BLOCK_FSIZE;
		ReshText();
		m_TextObj->SetVisibleDistance(dist);
		m_TextObj->AttachToScene(m_World->getScene()); 
		pworld->AddWorldSignsContainer(m_BlockPos);
	}
}

void WorldSignsContainer::leaveWorld()
{
	if(m_World->onClient() && m_TextObj)
	{
		if(m_TextObj)
			m_TextObj->DetachFromScene();

	}
	m_World->RemoveWorldSignsContainer(m_BlockPos);
	WorldContainer::leaveWorld();
}

void WorldSignsContainer::updateTick()
{
	if (m_World && m_World->onClient() && m_TextObj)
	{
/*
		if (m_TextColor.r == 255 && m_TextColor.g == 255 && m_TextColor.b == 255)
		{
			int lt = m_World->getBlockLightValue(m_BlockPos);
			int c = 150 * lt / 15 + 50;
			m_TextObj->setTextColor(c, c, c);
		}
		else
		{*/
		int lt = 0;
		if (m_vehicleWorld)
		{
			WCoord pos = m_BlockPos*BLOCK_SIZE;
			auto pVehicle = static_cast<VehicleWorld*>(m_vehicleWorld)->getActorVehicleAssemble();
			if (pVehicle)
			{
				pos = pVehicle->convertWcoord(m_BlockPos);
				//lt = m_World->getBlockLightValue(CoordDivBlock(pos), pVehicle->getBlockID(m_BlockPos));
				lt = m_World->getBlockLightValue(TopCoord(pos), pVehicle->getBlockID(m_BlockPos), false);
				for(int dir=0; dir<4; dir++)
				{
					int lt2 = m_World->getBlockLightValue(NeighborCoord(pos, dir), pVehicle->getBlockID(m_BlockPos), false);
					if(lt < lt2) lt = lt2;
				}
			}
		}
		else
		{
			lt = m_World->getBlockLightValue(m_BlockPos);
		}
		float c = 1.0f * lt / 15;
		m_TextObj->setTextColor(
			(int)(c * m_TextColor.r), 
			(int)(c * m_TextColor.g), 
			(int)(c * m_TextColor.b));
		//}
	}
}

void WorldSignsContainer::updateDisplay(float dtime)
{
	if (m_vehicleWorld)
	{
		applyBlockDir();
	}
	countMuralUpdate = countMuralUpdate + 1;
	if (m_World && countMuralUpdate > 300) {
		countMuralUpdate = 0;
		m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
	}
		
}

void WorldSignsContainer::onAttachUI()
{
	//MINIW::ScriptVM::game()->callFunction("AccelKey_Chat", "");

	//MINIW::InputManager::getSingleton().setInitialInput(getText());
	//MINIW::InputManager::getSingleton().enableIME(true, 0);
}

void WorldSignsContainer::onDetachUI()
{
	//MINIW::InputManager::getSingleton().enableIME(false, 0);
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldSignsContainer::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);
	auto str = builder.CreateString(m_strText);
	auto oldstr = builder.CreateString(m_oldText);
	auto actor = FBSave::CreateContainerSigns(builder, basedata, str, m_TextInit ? 1 : 0, m_FontType, static_cast<uint64_t>(m_ChangeTime), oldstr, m_TextWidth);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerSigns, actor.Union());
}

bool WorldSignsContainer::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerSigns *>(srcdata);
	loadContainerCommon(src->basedata());

	m_TextInit = src->textinit()!=0;
	m_FontType = src->fonttype();
	m_TextWidth = src->textwidth();
	m_loadText = "";
	if (m_ChangeTime < src->changtime())
	{
		m_ChangeTime = src->changtime();
	}
	if (src->oldtext() != NULL)
	{
		m_oldText = src->oldtext()->c_str();
	}
	if (src->text() != NULL)
	{
		m_loadStrText = src->text()->c_str();
	}
	
	init();

	if(m_TextObj)
	{
		m_loadText = dealShowText(srcdata, m_loadStrText);
		if (m_oldText != "")
		{
			m_oldLoadText = dealShowText(srcdata, m_oldText);
		}
		m_TextObj->setText(m_loadText.c_str());
	}
	
	if(src->text() != NULL){
		m_strText = src->text()->c_str();
	}
	return true;
}

const char *WorldSignsContainer::getText() const
{
	 return m_strText.c_str();
}

void WorldSignsContainer::setText(const char *text)
{
	if(m_TextObj)
	{
		//m_TextObj->setText(text);
		//int lang = GetIWorldConfigProxy()->getGameData("lang");		//当前选择的语言
		int lang = GetClientInfoProxy()->getArchiveLang();
		// TODO_yanxiongjian
		std::string showText = GetClientInfoProxy()->parseTextFromLanguageJson(text, lang);
		GetDefManagerProxy()->filterStringDirect((char*)showText.c_str());
		m_TextObj->setText(showText.c_str());

		//统计存档内文本
		char strKey[64];
		sprintf(strKey, "%d%d%d%lld", m_BlockPos.x, m_BlockPos.y, m_BlockPos.z, m_ObjId);
		int wsType = 0;
		if (m_FontType>0)
		{
			wsType = 1;
		}

		GetWorldStringManagerProxy()->insert(strKey, showText, (SAVEFILETYPE)wsType);

		SandboxEventDispatcherManager::GetGlobalInstance().Emit("BlockSync_Text", SandboxContext(nullptr)
			.SetData_String("content", showText)
			.SetData_Number("x", m_BlockPos.x)
			.SetData_Number("y", m_BlockPos.y)
			.SetData_Number("z", m_BlockPos.z));

		//保存chunk数据
		Chunk *pchunk = m_World->getChunk(m_BlockPos);
		if(pchunk)
		{
			pchunk->m_Dirty = true;
		}
	}

	m_TextInit = true;

	//保存数据改变时间
	int serverTime;
	int examineTime;
	
	MINIW::ScriptVM::game()->callFunction("GetServerCurrentTime", ">i", &serverTime);
	MINIW::ScriptVM::game()->callFunction("GetMapExamineTime", ">i", &examineTime);
	if (0 < m_ChangeTime)
	{
	
		if (m_ChangeTime < examineTime)
		{
			m_oldText = m_strText;
		}
	}
	m_strText = text;
	/*else if(m_loadStrText != "")
	{
		if (examineTime > 0)
		{
			m_oldText = m_loadStrText;
		}

	}*/
	
	m_ChangeTime = serverTime;

	m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
}

//属性变化同步到客机
int WorldSignsContainer::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	pAttrInfos->Add(getAttrib(0)); //方块id

	return 3;
}