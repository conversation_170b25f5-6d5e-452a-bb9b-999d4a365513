﻿#ifndef __VACANTVORTEX_COMPONENT_H__
#define __VACANTVORTEX_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "ActorLocoMotion.h"
#include "ClientMob.h"
class ClientMob;

class VacantVortexComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(VacantVortexComponent)

	VacantVortexComponent();
	~VacantVortexComponent();

	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnTick();
	virtual void init();
	virtual void onCollideWithActor(ClientActor* actor);

	virtual void addPlayerCd(long long playerId, int tickNum = -1);

	void setVortexOwnerOjbid(long long nVortexOwnerOjbid)
	{
		m_nVortexOwnerOjbid = nVortexOwnerOjbid;
	}
	long long getVortexOwnerOjbid()
	{
		return m_nVortexOwnerOjbid;
	}
	void setEnterObjid(long long nEnterObjid)
	{
		m_nEnterObjid = nEnterObjid;
	}
	long long getEnterObjid()
	{
		return m_nEnterObjid;
	}
	void setExitObjid(long long nExitObjid)
	{
		m_nExitObjid = nExitObjid;
	}
	long long getExitObjid()
	{
		return m_nExitObjid;
	}
	void setVortexLiveTick(int nVortexLiveTick)
	{
		m_nVortexLiveTick = nVortexLiveTick;
	}
	int getVortexLiveTick()
	{
		return m_nVortexLiveTick;
	}
	virtual bool castShadow()
	{
		return false; // 关闭脚底阴影
	}

	virtual void CreateComponentData(jsonxx::Object& componentData) override;
	virtual void LoadComponentData(const jsonxx::Object& componentData, bool fromArchive) override;

	int m_teleportCd;
	std::map<long long, int> actorTeleportCd;
	long long m_nVortexOwnerOjbid;
	long long m_nEnterObjid; //传送入口id
	long long m_nExitObjid; //传送出口id
	int m_nVortexLiveTick; //虚空漩涡生存时间
protected:
};//tolua_exports

#endif