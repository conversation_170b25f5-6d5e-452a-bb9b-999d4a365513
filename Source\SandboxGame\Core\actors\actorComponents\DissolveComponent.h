﻿#ifndef __DISSOLVE_COMPONENT_H__
#define __DISSOLVE_COMPONENT_H__

#include "ActorComponent_Base.h"
namespace Rainbow
{
	class MaterialInstance;
}
class ClientMob;

class DissolveComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(DissolveComponent)

	DissolveComponent();
	~DissolveComponent();

	void init();

	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnTick();
	virtual void OnEndPlay();

	//tolua_begin
	bool openDissolve(bool dissolve);
	void setDissolveIntensityRate(float rate);
	void setDissolveType(float type);
	//tolua_end

	void syncDissolve();
private:
	void doOpenDissolve();
	void doCloseDissolve();

	void cacheOriginMtl();
	void clearOriginMtl();
	void resetToOriginMtl();
	void updateMainTexFromOriginMtl();
private:
	bool m_openDissolve;				//溶解开关
	float m_dissolveIntensity;			//溶解强度 0-1
	float m_dissolveIntensityRate;		//溶解强度变化率
	float m_dissolveType;				//溶解类型
	float m_curDissolveIntensityRate;	//当前变化率

	int m_stayHideTick;					//持续隐身的帧数

	bool m_shouldEnd;					//如果结束了 也要完成一次完整的溶解 再退出.
	bool m_cacheOriginMtl;

	ClientMob* m_ownerMob;

	std::vector<Rainbow::SharePtr<Rainbow::MaterialInstance>> m_originMtl;
};//tolua_exports

#endif