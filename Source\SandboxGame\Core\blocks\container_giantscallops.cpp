#include "container_giantscallops.h"
#include "coreMisc.h"
#include "ClientActorManager.h"
#include "ActorPearl.h"
#include "LuaInterfaceProxy.h"
#include "BlockMaterialMgr.h"
#include "special_blockid.h"
#include "world.h"

// 张开巨型扇贝方块位置 
const int boxPosArray[4][6][3] = {
    { {0, 0, 0}, {0, 0, -1}, {1, 0, 0}, {1, 0, -1}, {1, 1, 0}, {1, 1, -1} },
    { {0, 0, 0}, {0, 0, 1}, {-1, 0, 0}, {-1, 0, 1}, {-1, 1, 0}, {-1, 1, 1} },
    { {0, 0, 0}, {1, 0, 0}, {0, 0, 1}, {1, 0, 1}, {0, 1, 1}, {1, 1, 1} },
    { {0, 0, 0}, {-1, 0, 0}, {0, 0, -1}, {-1, 0, -1}, {0, 1, -1}, {-1, 1, -1} }
};

// 搜索扇贝的中心位置
const int searchCenterDir[4][3] = {
    {1, 0, 0},
    {0, 0, 1},
    {1, 0, 1},
    {0, 0, 0}
};

GiantScallopsContainer::GiantScallopsContainer() 
    : WorldContainer(WCoord(0, 0, 0), 0), m_iTotalTick(0), m_intervalTick(6000), m_bCrap(false), m_bPearl(false), m_status(GIANT_SCALLOPS_CLOSE)
{
    m_scallops_in_water_tick = GetLuaInterfaceProxy().get_lua_const()->scallops_in_water_tick;
    m_scallops_around_fire_tick = GetLuaInterfaceProxy().get_lua_const()->scallops_around_fire_tick;
    m_scallops_after_dispear_tick = GetLuaInterfaceProxy().get_lua_const()->scallops_after_dispear_tick;
    m_scallops_born_pearl = GetLuaInterfaceProxy().get_lua_const()->scallops_born_pearl;
    m_scallops_born_crap = GetLuaInterfaceProxy().get_lua_const()->scallops_born_crap;
    m_intervalTick = m_scallops_in_water_tick;
    m_NeedTick = true;
}

GiantScallopsContainer::GiantScallopsContainer(const WCoord& blockpos)
    : WorldContainer(blockpos, 0), m_iTotalTick(0), m_intervalTick(6000), m_bCrap(false), m_status(GIANT_SCALLOPS_CLOSE)
{
    m_scallops_in_water_tick = GetLuaInterfaceProxy().get_lua_const()->scallops_in_water_tick;
    m_scallops_around_fire_tick = GetLuaInterfaceProxy().get_lua_const()->scallops_around_fire_tick;
    m_scallops_after_dispear_tick = GetLuaInterfaceProxy().get_lua_const()->scallops_after_dispear_tick;
    m_scallops_born_pearl = GetLuaInterfaceProxy().get_lua_const()->scallops_born_pearl;
    m_scallops_born_crap = GetLuaInterfaceProxy().get_lua_const()->scallops_born_crap;
    m_intervalTick = m_scallops_in_water_tick;
    m_NeedTick = true;
}

GiantScallopsContainer::~GiantScallopsContainer()
{
}

void GiantScallopsContainer::SetStatus(int status)
{
    m_status = status;
}

int GiantScallopsContainer::GetStatus() const
{
    return m_status;
}

FBSave::ContainerUnion GiantScallopsContainer::getUnionType()
{
    return FBSave::ContainerUnion_ContainerGiantScallops;
}

void  GiantScallopsContainer::enterWorld(World* pworld)
{
    WorldContainer::enterWorld(pworld);
    registerUpdateTick();
}

void  GiantScallopsContainer::leaveWorld()
{
    WorldContainer::leaveWorld();
}

int GiantScallopsContainer::getObjType() const
{
    return OBJ_TYPE_GIANT_SCALLOPS;
}

void GiantScallopsContainer::updateTick()
{
    if (m_World == nullptr) {
        return;
    }
    if (m_World->isRemoteMode()) {
        return;
    }
    ActorManager* actorManger = dynamic_cast<ActorManager*>(m_World->getActorMgr());
    if (!actorManger) return;
    m_iTotalTick++;
    bool isDispearMob = false;
    if (m_iTotalTick % m_scallops_after_dispear_tick == 0) {
        if (m_status == GIANT_SCALLOPS_OPEN && (m_bCrap || m_bPearl)) {
            bool bFindMob = false;
            int blockData = m_World->getBlockData(m_BlockPos);
            int blockDir = blockData & 3;
            for (int i = 0; i < 4; i++) {
                auto curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
                std::vector<ClientMob*> nearMobs;
                actorManger->selectNearAllMobs(nearMobs, curPos * BLOCK_SIZE, BLOCK_SIZE + 1);
                for (auto& mob : nearMobs) {
                    if (mob->getDefID() == 3621 ||
                        mob->getObjType() == OBJ_TYPE_PEARL) {
                        bFindMob = true;
                        break;
                    }
                }

                if (bFindMob) {
                    break;
                }
            }

            if (!bFindMob) {
                isDispearMob = true;
            }
        }
    }

    if (m_iTotalTick % m_scallops_around_fire_tick == 0) {
        PlaceEnvrionment placeEnviron = CheckInAir();
        if (placeEnviron != NOT_IN_AIR) {
            if (CheckAroundFire()) {
                int randomNum = GenRandomInt(1, 10);
                if (randomNum <= 6) {
                    if (m_status == GIANT_SCALLOPS_CLOSE) {
                        SetExtraBlock(true);
                        m_status = GIANT_SCALLOPS_OPEN;
                    }
                }
            } else {
                if (m_status == GIANT_SCALLOPS_OPEN) {
                    SetExtraBlock(false);
                    m_status = GIANT_SCALLOPS_CLOSE;
                }
            }
            return;
        }
    }

    if (isDispearMob) {
        m_bCrap = false;
        m_bPearl = false;
        if (m_status == GIANT_SCALLOPS_OPEN) {
            SetExtraBlock(false);
            m_status = GIANT_SCALLOPS_CLOSE;
        }
    }
    
    if (m_iTotalTick >= m_scallops_in_water_tick) {
        m_iTotalTick = 0;
        if (m_World->isRemoteMode()) {
            return;
        }

        PlaceEnvrionment placeEnviron = CheckInWaterAndFluid();
        if (placeEnviron == PlaceEnvrionment::ALL_IN_WATER) {
            int randomNum = GenRandomInt(1, 10);
            if (randomNum <= 5) {
                if (m_status == GIANT_SCALLOPS_OPEN) {
                    SetExtraBlock(false);
                    m_status = GIANT_SCALLOPS_CLOSE;
                } else {
                    SetExtraBlock(true);
                    m_status = GIANT_SCALLOPS_OPEN;
                }
            }
        } else if (placeEnviron == PlaceEnvrionment::IN_OTHER_FLUID) {
            if (m_status == GIANT_SCALLOPS_OPEN) {
                SetExtraBlock(false);
                m_status = GIANT_SCALLOPS_CLOSE;
            }
        }
    }
}

flatbuffers::Offset<FBSave::ChunkContainer> GiantScallopsContainer::save(SAVE_BUFFER_BUILDER& builder)
{
    auto basedata = saveContainerCommon(builder);
    auto containerdata = FBSave::CreateContainerFeedTrough(builder, basedata, m_iTotalTick, m_status, m_bCrap);
    return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerGiantScallops, containerdata.Union());
}

bool GiantScallopsContainer::load(const void* srcdata)
{
    auto src = reinterpret_cast<const FBSave::ContainerGiantScallops*>(srcdata);
    loadContainerCommon(src->basedata());
    m_iTotalTick = src->tick();
    m_status = src->status();
    m_bCrap = src->bCrap();
    return true;
}

GiantScallopsContainer::PlaceEnvrionment GiantScallopsContainer::CheckInWaterAndFluid()
{
    PlaceEnvrionment placeEnvrion = NOT_IN_WATER;
    if (m_World == nullptr) {
        return placeEnvrion;
    }

    int blockData = m_World->getBlockData(m_BlockPos);
    int blockDir = blockData & 3;
    bool bAllTopWater = true;
    if (m_status == GIANT_SCALLOPS_CLOSE) {
        // 闭合扇贝为 2 x 2方块，是否在水中需要判断顶部是否为水
        for (int i = 0; i < 4; i++) {
            WCoord curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
            if (bAllTopWater) {
                WCoord topPos = TopCoord(curPos);
                int topBlockId = m_World->getBlockID(topPos);
                if (!IsWaterBlockID(topBlockId)) {
                    // 优化效率, 携带流体方块判断的逻辑
                    if (IsOtherFluidBlock(topBlockId)) {
                        placeEnvrion = IN_OTHER_FLUID;
                        return placeEnvrion;
                    }
                    bAllTopWater = false;
                    break;
                }
            }
        }

        if (bAllTopWater) {
            placeEnvrion = ALL_IN_WATER;
        } else {
            // 闭合扇贝为 2 x 2方块，是否在水中,当顶部不为水，需要判断四周是否为水或者扇贝方块
            for (int i = 0; i < 4; i++) {
                WCoord curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
                for (int j = 0; j < 4; j++) {
                    int aroundBlockId = m_World->getBlockID(NeighborCoord(curPos, j));
                    if (aroundBlockId == BLOCK_GIANT_SCALLOPS_CLOSE) {
                        continue;
                    }

                    if (!IsWaterBlockID(aroundBlockId)) {
                        if (IsOtherFluidBlock(aroundBlockId)) {
                            placeEnvrion = IN_OTHER_FLUID;
                            return placeEnvrion;
                        }

                        if (aroundBlockId != BLOCK_AIR) {
                            continue;
                        }

                        if (placeEnvrion == ALL_IN_WATER) {
                            placeEnvrion = PART_IN_WATER;
                            break;
                        }
                    } else {
                        if (placeEnvrion == NOT_IN_WATER) {
                            placeEnvrion = ALL_IN_WATER;
                        }
                    }
                }
            }
        }
    } else if (m_status == GIANT_SCALLOPS_OPEN) {
        // 首先判断张开的扇贝第二层两个方块的顶部是否有水
        for (int i = 4; i < 6; i++) {
            WCoord curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
            WCoord topPos = TopCoord(curPos);
            int topBlockId = m_World->getBlockID(topPos);
            if (!IsWaterBlockID(topBlockId)) {
                if (IsOtherFluidBlock(topBlockId)) {
                    placeEnvrion = IN_OTHER_FLUID;
                    return placeEnvrion;
                }
                bAllTopWater = false;
                break;
            }
        }

        if (bAllTopWater) {
            placeEnvrion = ALL_IN_WATER;
        } else {
            for (int i = 4; i < 6; i++) {
                WCoord curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
                for (int j = 0; j < 4; j++) {
                    int aroundBlockId = m_World->getBlockID(NeighborCoord(curPos, j));
                    if (aroundBlockId == BLOCK_GIANT_SCALLOPS_CLOSE) {
                        continue;
                    }

                    if (!IsWaterBlockID(aroundBlockId)) {
                        if (IsOtherFluidBlock(aroundBlockId)) {
                            placeEnvrion = IN_OTHER_FLUID;
                            return placeEnvrion;
                        }

                        if (aroundBlockId != BLOCK_AIR) {
                            continue;
                        }

                        if (placeEnvrion == ALL_IN_WATER) {
                            placeEnvrion = PART_IN_WATER;
                            break;
                        }
                    } else {
                        if (placeEnvrion == NOT_IN_WATER) {
                            placeEnvrion = ALL_IN_WATER;
                        }
                    }
                }
            }


            // 当第二层方块周边都没有水，则继续判断第一层方块周围是否有
            if (placeEnvrion == NOT_IN_WATER) {
                for (int i = 0; i < 4; i++) {
                    WCoord curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
                    for (int j = 0; j < 4; j++) {
                        int aroundBlockId = m_World->getBlockID(NeighborCoord(curPos, j));
                        if (aroundBlockId == BLOCK_GIANT_SCALLOPS_CLOSE) {
                            continue;
                        }
                        if (!IsWaterBlockID(aroundBlockId)) {
                            if (IsOtherFluidBlock(aroundBlockId)) {
                                placeEnvrion = IN_OTHER_FLUID;
                                return placeEnvrion;
                            }
                            if (placeEnvrion == ALL_IN_WATER) {
                                placeEnvrion = PART_IN_WATER;
                                break;
                            }
                        } else {
                            if (placeEnvrion == NOT_IN_WATER) {
                                placeEnvrion = ALL_IN_WATER;
                            }
                        }
                    }
                }
            }
        }
    }

    return placeEnvrion;
}

GiantScallopsContainer::PlaceEnvrionment GiantScallopsContainer::CheckInAir()
{
    PlaceEnvrionment placeEnvrion = NOT_IN_AIR;
    if (m_World == nullptr) {
        return placeEnvrion;
    }

    int blockData = m_World->getBlockData(m_BlockPos);
    int blockDir = blockData & 3;
    if (m_status == GIANT_SCALLOPS_CLOSE) {
        for (int dir = 0; dir < 6; dir++) {
            if (dir == 4) {
                // 跳过底部方块判断
                continue;
            }
            for (int i = 0; i < 4; i++) {
                WCoord itemPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
                int getBlockId = m_World->getBlockID(NeighborCoord(itemPos, dir));
                if (getBlockId == BLOCK_GIANT_SCALLOPS_CLOSE) {
                    continue;
                }
                if (!IsAirBlockID(getBlockId)) {
                    if (placeEnvrion == ALL_IN_AIR) {
                        placeEnvrion = PART_IN_AIR;
                        break;
                    }
                }
                else {
                    if (placeEnvrion == NOT_IN_AIR) {
                        placeEnvrion = ALL_IN_AIR;
                    }
                }
            }
        }
    } else if (m_status == GIANT_SCALLOPS_OPEN) {
        for (int dir = 0; dir < 6; dir++) {
            if (dir == 4) {
                // 跳过底部方块判断
                continue;
            }
            for (int i = 0; i < 6; i++) {
                WCoord itemPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
                int getBlockId = m_World->getBlockID(NeighborCoord(itemPos, dir));
                if (getBlockId == BLOCK_GIANT_SCALLOPS_CLOSE) {
                    continue;
                }
                if (!IsAirBlockID(getBlockId)) {
                    if (placeEnvrion == ALL_IN_AIR) {
                        placeEnvrion = PART_IN_AIR;
                        break;
                    }
                } else {
                    if (placeEnvrion == NOT_IN_AIR) {
                        placeEnvrion = ALL_IN_AIR;
                    }
                }
            }
        }
    }

    return placeEnvrion;
}

bool GiantScallopsContainer::CheckAroundFire()
{
    if (m_World == nullptr) {
        return false;
    }

    int blockDir = m_World->getBlockData(m_BlockPos) & 3;
    if (m_status == GIANT_SCALLOPS_CLOSE) {
        for (int i = 0; i < 4; i++) {
            WCoord curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
            if (CheckAroundBonFireIsFire(curPos, 2, -2, 2) ||
                m_World->hasBlockInRange(BLOCK_FIRE, curPos, 2, -2, 2, 1) ||
                m_World->hasBlockInRange(BLOCK_STILL_LAVA, curPos, 2, -2, 2, 1) ||
                m_World->hasBlockInRange(BLOCK_FLOW_LAVA, curPos, 2, -2, 2, 1)) {
                return true;
            }
        }
    } else if (m_status == GIANT_SCALLOPS_OPEN) {
        for (int i = 0; i < 6; i++) {
            WCoord curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
            if (CheckAroundBonFireIsFire(curPos, 2, -2, 2) ||
                m_World->hasBlockInRange(BLOCK_FIRE, curPos, 2, -2, 2, 1) ||
                m_World->hasBlockInRange(BLOCK_STILL_LAVA, curPos, 2, -2, 2, 1) ||
                m_World->hasBlockInRange(BLOCK_FLOW_LAVA, curPos, 2, -2, 2, 1)) {
                return true;
            }
        }
    }
    return false;
}

bool GiantScallopsContainer::CheckAroundBonFireIsFire(const WCoord& center, int range, int mindy, int maxdy)
{
	int cx = center.x;
	int cy = center.y;
	int cz = center.z;

	for (int z = cz - range; z <= cz + range; z++)
	{
		for (int x = cx - range; x <= cx + range; x++)
		{
			for (int y = cy + mindy; y <= cy + maxdy; y++)
			{
				if (m_World->getBlockID(WCoord(x, y, z)) == BLOCK_BONFIRE)
				{
                    int data = (m_World->getBlockData(WCoord(x, y, z)) & 0x6) >> 1;;
					if (data!=0) return true;
				}
			}
		}
	}
	return false;
}

bool GiantScallopsContainer::IsOtherFluidBlock(int blockId)
{
    if (BlockMaterialMgr::isDriftsand(blockId)/*blockId == BLOCK_STILL_SAND || blockId == BLOCK_FLOW_SAND*/
        || BlockMaterialMgr::isHoney(blockId)//IsHoneyBlock(blockId) 
        || BlockMaterialMgr::isVenom(blockId)/*blockId == BLOCK_STILL_VENOM || blockId == BLOCK_FLOW_VENOM*/) {
        return true;
    }

    return false;
}

void GiantScallopsContainer::SpawnCrap()
{
    ClientActorMgr* actormgr = m_World->getActorMgr()->ToCastMgr();
    if (actormgr != nullptr) {
        int blockDir = m_World->getBlockData(m_BlockPos) & 3;
        auto curPos = m_BlockPos * BLOCK_SIZE + Vector3f(0.0f,30.0f, 0.0f) +
            WCoord(searchCenterDir[blockDir][0], searchCenterDir[blockDir][1], searchCenterDir[blockDir][2]) * BLOCK_SIZE;;
        ClientMob* mob = actormgr->spawnMob(curPos, 3621, false, false);
        if (mob != nullptr) {
            mob->playSaySound();
            mob->SetPlayClearFx(false);
            m_bCrap = true;
        }
    }
}

void GiantScallopsContainer::SpawnPearl()
{
    ClientActorMgr* actormgr = m_World->getActorMgr()->ToCastMgr();
    if (actormgr != nullptr) {
        int blockDir = m_World->getBlockData(m_BlockPos) & 3;
        auto curPos = m_BlockPos * BLOCK_SIZE + Vector3f(0.0f, 30.0f, 0.0f)  +
            WCoord(searchCenterDir[blockDir][0], searchCenterDir[blockDir][1], searchCenterDir[blockDir][2]) * BLOCK_SIZE;
        ClientMob* mob = actormgr->spawnMob(curPos, 3521, false, false);
        if (mob != nullptr) {
            mob->playSaySound();
            m_bPearl = true;
        }
    }
}

void GiantScallopsContainer::SetExtraBlock(bool bPlace)
{
    int blockData = m_World->getBlockData(m_BlockPos);
    int blockDir = blockData & 3;
    if (bPlace) {
        if (m_status == GIANT_SCALLOPS_CLOSE) {
            for (int i = 4; i < 6; i++) {
                auto curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
                m_World->setBlockAll(curPos, BLOCK_GIANT_SCALLOPS_CLOSE, 4 | blockDir);
            }
            DoOpenEvent();
        }
    } else {
        if (m_status == GIANT_SCALLOPS_OPEN) {
            for (int i = 4; i < 6; i++) {
                auto curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
                m_World->setBlockData(curPos, 12, 0);
                m_World->setBlockAir(curPos);
            }
            DoCloseEvent();
        }
    }
}

void GiantScallopsContainer::DoCloseEvent()
{
    if (m_World == nullptr) {
        return;
    }
    ActorManager* actorManger = dynamic_cast<ActorManager*>(m_World->getActorMgr());
    if (!actorManger) return;
    RefreshBlockMesh();
    m_bCrap = false;
    m_bPearl = false;
    int blockData = m_World->getBlockData(m_BlockPos);
    int blockDir = blockData & 3;
    for (int i = 0; i < 4; i++) {
        auto curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
        std::vector<ClientMob *> nearMobs;
        actorManger->selectNearAllMobs(nearMobs, curPos * BLOCK_SIZE, BLOCK_SIZE + 1);
        for (auto& mob : nearMobs) {
            if (mob->getDefID() == 3621) {
                m_bCrap = true;
            }
            mob->setNeedClear();
        }
    }
}

void GiantScallopsContainer::DoOpenEvent()
{
    if (m_World == nullptr) {
        return;
    }

    RefreshBlockMesh();
    if (m_bCrap) {
        SpawnCrap();
        return;
    }

    int randomNum = GenRandomInt(1, 10);
    if (randomNum <= m_scallops_born_pearl) {
        SpawnPearl();
    } else if (randomNum <= m_scallops_born_crap) {
        SpawnCrap();
    } else {
        m_bCrap = false;
        m_bPearl = false;
    }
}

void GiantScallopsContainer::RefreshBlockMesh()
{
    if (m_World != nullptr) {
        m_World->markBlockForUpdate(m_BlockPos, true);
    }
}

void GiantScallopsContainer::DoDestroyEvent()
{
    if (m_World == nullptr) {
        return;
    }
    ActorManager* actorManger = dynamic_cast<ActorManager*>(m_World->getActorMgr());
    if (!actorManger) return;
    int blockData = m_World->getBlockData(m_BlockPos);
    int blockDir = blockData & 3;
    for (int i = 0; i < 4; i++) {
        auto curPos = m_BlockPos + WCoord(boxPosArray[blockDir][i][0], boxPosArray[blockDir][i][1], boxPosArray[blockDir][i][2]);
        std::vector<ClientMob*> nearMobs;
        actorManger->selectNearAllMobs(nearMobs, curPos * BLOCK_SIZE, BLOCK_SIZE * 2);
        for (auto& mob : nearMobs) {
            if (mob->getObjType() == OBJ_TYPE_PEARL) {
                mob->setNeedClear();
            }
        }
    }
}