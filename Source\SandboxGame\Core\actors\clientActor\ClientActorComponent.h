
#ifndef __CLIENTACTORCOMPONENT_H__
#define __CLIENTACTORCOMPONENT_H__
class BlockEnvEffectsComponent_Actor;
class SunHurtComponent;
class CarryComponent;
class RiddenComponent;
class BindActorComponent;
class ToAttackTargetComponent;
class ActorBindVehicle;
class ActorUpdateFrequency;
class ActorInPortal;
class ActorChunkPos;
class AttackedComponent;
class DieComponent;
class SoundComponent;
//class DirectionByDirComponent;
class EffectComponent;
class FindComponent;
class DropItemComponent;
class ActionAttrStateComponent;
class TriggerComponent;
class ParticlesComponent;
class FireBurnComponent;
class FallComponent;
class ClientActorFuncWrapper;
class BaseTargetComponent;
class ActorBallComponent;
class ActorBasketBallComponent;
class ClientSouvenirComponent;
class ThornBallComponent;

#endif