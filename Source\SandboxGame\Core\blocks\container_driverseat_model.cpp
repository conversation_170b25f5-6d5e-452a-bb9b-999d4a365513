#include"container_driverseat_model.h"
#include"BlockMaterialMgr.h"
#include"OgreEntity.h"
#include"BlockScene.h"
#include"ActorVehicleAssemble.h"
#include"ClientInfoProxy.h"
#include"ClientActorHelper.h"
#include "VehicleWorld.h"

ContainerDriverSeatModel::ContainerDriverSeatModel() : ContainerDriverSeat()
{
	m_strOmodID = "";
	m_pEntity = nullptr;
	m_iDir = 0;
	m_tp = TYPE::NORMAL;
	m_iCurAniId = -1;
	m_NeedTick = true;
}

ContainerDriverSeatModel::ContainerDriverSeatModel(const WCoord& blockpos) : ContainerDriverSeat(blockpos)
{
	m_strOmodID = "";
	m_pEntity = nullptr;
	m_iDir = 0;
	m_tp = TYPE::NORMAL;
	m_NeedTick = true;
}
//ContainerActorModel(const WCoord& blockpos, int dir);
ContainerDriverSeatModel::~ContainerDriverSeatModel()
{
	Rainbow::Entity::Destory(m_pEntity);
}

void ContainerDriverSeatModel::init(const string& strResid, const int& dir, const TYPE& tp, const WCoord& core_blockpos)
{
	m_strOmodID = strResid;
	m_iDir = dir;
	m_tp = tp;
	m_coreBlockpos = core_blockpos;
}



void ContainerDriverSeatModel::enterWorld(World* pworld)
{
	ContainerDriverSeat::enterWorld(pworld);
	registerUpdateTick();
	registerUpdateDisplay();

	onEnterWorld(pworld);
	//onEnterWorld(pworld);
}
void ContainerDriverSeatModel::leaveWorld()
{
	ContainerDriverSeat::leaveWorld();
	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
	}
}
void ContainerDriverSeatModel::updateTick()
{
	if (m_tp != TYPE::CORE) return;
	if (!m_vehicleWorld) return;
	WCoord wp;
	WCoord wp1;
	Rainbow::Quaternionf qa;
	auto vehicleworld = static_cast<VehicleWorld*>(m_vehicleWorld);
	wp = vehicleworld->getActorVehicleAssemble()->getRealWorldPosWithPos(m_BlockPos);
	vehicleworld->getActorVehicleAssemble()->getRealWorldPosAndRotate(m_iDir, m_BlockPos, wp1, Rainbow::Quaternionf(0, 0, 0, 1), qa, WCoord(0, 0, 0));
	this->setPose(wp.toVector3(), qa);
}

void ContainerDriverSeatModel::updateDisplay(float dtime)
{
	if (m_tp != TYPE::CORE) return;
	if (m_vehicleWorld)
	{
		auto vehicleworld = static_cast<VehicleWorld*>(m_vehicleWorld);
		WCoord wp;
		WCoord wp1;
		Rainbow::Quaternionf qa;
		wp = vehicleworld->getActorVehicleAssemble()->getRealWorldPosWithPos(m_BlockPos);
		vehicleworld->getActorVehicleAssemble()->getRealWorldPosAndRotate(m_iDir, m_BlockPos, wp1, Rainbow::Quaternionf(0, 0, 0, 1), qa, WCoord(0, 0, 0));
		this->setPose(wp.toVector3(), qa);
		
		auto veh = vehicleworld->getActorVehicleAssemble();
		if (veh && m_pEntity)
		{
			auto vehloc = dynamic_cast<VehicleAssembleLocoMotion*>(veh->getLocoMotion());
			if (vehloc)
			{
				if (GetClientInfoProxy()->isPC())
				{
					if (vehloc->mVehicleController.getSteerLeftKeyPressed())
					{
						this->playAni(100921);
					}
					else if (vehloc->mVehicleController.getSteerRightKeyPressed())
					{
						this->playAni(100922);
					}
					else {
						this->playAni(100100);
					}
				}
				else {
					if (vehloc->mVehicleController.getSteer()  > 0)
					{
						this->playAni(100921);

					}
					else if (vehloc->mVehicleController.getSteer() < 0)
					{
						this->playAni(100922);
					}
					else {
						this->playAni(100100);
					}
				}
			}
		}
	}
	if (m_pEntity)
	{
		Rainbow::Vector4f lightparam(0, 0, 0, 0);
		auto wp = m_pEntity->GetWorldPosition();
		if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(wp));
		lightparam.x += 0.2f;
		lightparam.y += 0.2f;
		m_pEntity->SetInstanceData(lightparam);
		unsigned int dtick = Rainbow::TimeToTick(dtime);
		m_pEntity->Tick(dtick);
	}
}

void ContainerDriverSeatModel::setPose(const Rainbow::Vector3f& worldPos, const Rainbow::Quaternionf& rot)
{
	if (!m_pEntity) return;	
	if (m_tp != TYPE::CORE) return;
	if (!m_vehicleWorld) return;

	auto q =   rot * m_qot;
	Rainbow::Matrix4x4f worldMat4;
	worldMat4.SetTRS(Vector3f(0, 0, 0), q,  Rainbow::Vector3f(1.0, 1.0, 1.0));

	Rainbow::Matrix4x4f localMat4;
	localMat4.SetTRS(m_step.toWorldPos().toVector3(), Rainbow::Quaternionf(0, 0, 0, 1), Rainbow::Vector3f(1.0, 1.0, 1.0));
	//先旋转再位移
	auto mat = localMat4 * worldMat4;

	m_pEntity->SetPosition(worldPos + mat.GetPosition());
	m_pEntity->SetRotation(q);

}

void ContainerDriverSeatModel::onEnterWorld(World* pworld)
{
	//非核心方块不包含模型
	if (m_tp == TYPE::NORMAL) return;


#ifndef IWORLD_SERVER_BUILD	
	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
		Rainbow::Entity::Destory(m_pEntity);
	}

	WCoord pos = m_BlockPos * BLOCK_SIZE;

	WCoord step;
	WCoord center;
	Rainbow::Vector3f angle;
	angle = Rainbow::Vector3f::zero;

	if (m_iDir == DIR_NEG_X)
	{
		angle.y = 90;
		step.z = BLOCK_SIZE;

		center.z = -BLOCK_SIZE * 0.5;
		center.x = -BLOCK_SIZE * 0.5;
	}
	else if (m_iDir == DIR_POS_X)
	{
		angle.y = 270;
		step.x = BLOCK_SIZE;

		center.z = -BLOCK_SIZE * 0.5;
		center.x = -BLOCK_SIZE * 0.5;
	}
	else if (m_iDir == DIR_NEG_Z)
	{
		center.z = -BLOCK_SIZE * 0.5;
		center.x = -BLOCK_SIZE * 0.5;
	}
	else if (m_iDir == DIR_POS_Z)
	{
		angle.y = 180;
		step.x = BLOCK_SIZE;
		step.z = BLOCK_SIZE;

		center.z = -BLOCK_SIZE * 0.5;
		center.x = -BLOCK_SIZE * 0.5;
	}
	else if (m_iDir == DIR_NEG_Y)
	{
		angle.x = 270;
		step.z = BLOCK_SIZE;

		center.z = 0;
		center.x = -BLOCK_SIZE * 0.5;
		center.y = -BLOCK_SIZE * 0.5;;
	}
	else if (m_iDir == DIR_POS_Y)
	{
		angle.x = 90;
		step.y = BLOCK_SIZE;

		center.z = -BLOCK_SIZE;
		center.x = -BLOCK_SIZE * 0.5;
		center.y = -BLOCK_SIZE * 0.5;
	}

	m_pEntity = Rainbow::Entity::Create();

#if ENTITY_MODIFY_MODEL_ASYNC
	m_pEntity->LoadAsync(m_strOmodID.c_str(),false);

#else
	m_pEntity = Rainbow::Entity::Create();
	Rainbow::Model* model = NULL;
	model = g_BlockMtlMgr.getModel(m_strOmodID.c_str());
	if (!model) return;
	m_pEntity->Load(model);

#endif

	m_pEntity->ShowSkins(true);
	m_pEntity->SetRotation(angle.y, angle.x, angle.z);
	pos += step;
	m_pEntity->SetPosition(pos.toWorldPos());

	Rainbow::Quaternionf qot = Rainbow::AngleEulerToQuaternionf(angle);;
	m_step = center;
	m_qot = qot;

	if (m_World)
		m_pEntity->AttachToScene(m_World->getScene());

#endif
}

void ContainerDriverSeatModel::playAni(int aniID, int times)
{
	if (m_pEntity )
	{
		if (m_iCurAniId != aniID)
		{
			m_iCurAniId = aniID;
			m_pEntity->StopAnim();
			m_pEntity->PlayAnim(aniID, times);
		}
	}
}

void ContainerDriverSeatModel::stopAni()
{
	if (m_pEntity)
	{
		m_pEntity->StopAnim();
	}
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerDriverSeatModel::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::SeatPartData>>> seatdatasoffset = 0;
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::SeatKeyData>>> keydataoffset = 0;

	std::vector<flatbuffers::Offset<FBSave::SeatPartData>> linesdata;
	for (int i = 0; i < (int)m_PartDatas.size(); i++)
	{
		SeatPartData action = m_PartDatas[i];
		auto pos = WCoordToCoord3(action.position);
		linesdata.push_back(FBSave::CreateSeatPartData(builder, &pos, action.ischarge));
	}

	std::vector<flatbuffers::Offset<FBSave::SeatKeyData>> keysdata;
	for (auto iter = m_BindKeyData.begin(); iter != m_BindKeyData.end(); iter++)
	{
		for (auto keyiter = iter->second.begin(); keyiter != iter->second.end(); keyiter++)
		{
			auto pos = WCoordToCoord3(keyiter->position);
			keysdata.push_back(FBSave::CreateSeatKeyData(builder, &pos, keyiter->index, iter->first));
		}
	}

	seatdatasoffset = builder.CreateVector(linesdata);
	keydataoffset = builder.CreateVector(keysdata);
	auto partdata = FBSave::CreateContainerDriverSeat(builder,
		basedata,
		seatdatasoffset,
		keydataoffset);

	auto offsetPos = WCoordToCoord3(m_coreBlockpos);

	auto actor = FBSave::CreateContainerDriverSeatModel(builder, partdata, builder.CreateString(m_strOmodID.c_str()), m_iDir, (int)m_tp, &offsetPos);


	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerDriverSeatModel, actor.Union());
}

bool ContainerDriverSeatModel::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerDriverSeatModel*>(srcdata);
	loadContainerCommon(src->basedata()->basedata());
	m_PartDatas.clear();
	if (src->basedata()->linesdata())
	{
		for (int i = 0; i < (int)src->basedata()->linesdata()->size(); i++)
		{
			auto data = src->basedata()->linesdata()->Get(i);
			if (data)
			{
				SeatPartData seatdata;
				seatdata.position = Coord3ToWCoord(data->position());
				seatdata.ischarge = data->ischarge();
				m_PartDatas.push_back(seatdata);
			}
		}
	}
	if (src->basedata()->keysdata())
	{
		for (int i = 0; i < (int)src->basedata()->keysdata()->size(); i++)
		{
			auto data = src->basedata()->keysdata()->Get(i);
			if (data)
			{
				PartKeyData keydata;
				keydata.position = Coord3ToWCoord(data->position());
				keydata.index = data->index();
				int keyid = data->keyid();
				if (m_BindKeyData.find(keyid) != m_BindKeyData.end())
					m_BindKeyData[keyid].push_back(keydata);
				else
				{
					std::vector<PartKeyData> temp;
					temp.push_back(keydata);
					m_BindKeyData.insert(make_pair(keyid, temp));
				}
			}
		}
	}
	m_iDir = src->dir();
	m_strOmodID = src->modelID()->str();
	m_tp = (TYPE)src->blocktype();
	m_coreBlockpos = Coord3ToWCoord(src->corePosition());

	return true;
}
