#ifndef __ITEM_SKILL_COMPONENT_H__
#define __ITEM_SKILL_COMPONENT_H__

#include <vector>
#include <string>

#include "world_types.h"
#include "ActorComponent_Base.h"
namespace Rainbow
{
	class ISound;
	class Vector3f;
}
class ClientActor;
class ClientPlayer;
class PlayerControl;
class MpPlayerControl;
struct ItemSkillDef;


class ItemSkillComponent : public ActorComponentBase
{ 
public:
	DECLARE_COMPONENTCLASS(ItemSkillComponent)

	ItemSkillComponent();

	//void onTick();
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	
	virtual ~ItemSkillComponent();
	
	virtual bool useItemSkill(int itemid, int status, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos,  std::string clientParam);

	int getCurItemSkillID();
	void setCurItemSkillID(int skillid) { m_nCurItemSkillID = skillid;}

	const ItemSkillDef* getCurItemSkillDef();
protected:
	bool useItemSkillOper(int itemid, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos,  std::string clientParam);
	void doActualItemSkillRangeAttack(ClientActor *target, int itemid, float ChargeMove);
	void itemSkillAttackWithPower(int skillId, float shotPower);	
	void playItemSkillLoopEffect(bool stop);
	void playItemSkillLoopSound(bool stop);	

	void notifyUseItemSkill2Tracking(int itemid, int status, int skillid);
protected:
	ClientPlayer* m_owner;
	int m_nCurItemSkillID;
	int m_CurSndItemSkillID;
	int m_CurEffectItemSkillID;
	Rainbow::ISound *m_CurItemSkillSnd;	
}; 


class ControlItemSkillComponent : public ItemSkillComponent
{
public:
	DECLARE_COMPONENTCLASS(ControlItemSkillComponent)

	ControlItemSkillComponent();
	//~ControlItemSkillComponent();
	
	virtual bool useItemSkill(int itemid, int status, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos,  std::string clientParam) override;

protected:
	PlayerControl* m_ownerControl;
};


class MPControlItemSkillComponent : public ControlItemSkillComponent
{
	DECLARE_COMPONENTCLASS(MPControlItemSkillComponent)
public:

	MPControlItemSkillComponent();
	//~MPControlItemSkillComponent();
	
	virtual bool useItemSkill(int itemid, int status, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos,  std::string clientParam) override;
};
#endif