#ifndef _PUSHSNOWBALL_LOCOMOTION_H_
#define _PUSHSNOWBALL_LOCOMOTION_H_

#include "OgrePhysXManager.h"
#include "ClientPlayer.h"
#include "ActorLocoMotion.h"

class PushSnowBallLocomotion : public ActorLocoMotion //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(PushSnowBallLocomotion)
	//tolua_begin
	PushSnowBallLocomotion();
	//tolua_end
	virtual void prepareTick() override;
	virtual void tick() override;
	virtual void update(float dtime) override;
	virtual void updateBindActor() override;
	virtual void getRotation(Rainbow::Quaternionf &quat) override;
	virtual void doPickThrough(ClientActor *excludesactor = nullptr) override;
	virtual void doBlockCollision() override;
	virtual bool needFullRotation() override
	{
		return true;
	}
	virtual void setPosition(int x, int y, int z);	
	//tolua_begin
	void attachPhysActor();
	void detachPhysActor();
	void attachPhysJoint(ClientPlayer *player);
	void detachPhysJoint();
	void checkPhysWorld();

	virtual void OnCollisionEnter(const Rainbow::EventContent* collision);
	//virtual void OnCollisionExit(const Rainbow::EventContent* collision);

	float getSnowBallSpeed()
	{
		return m_Speed;
	}
	void updateBallsize();

	Rainbow::RigidDynamicActor *m_PhysActor;
	MINIW::Joint *m_PhysJoint;

	Rainbow::WorldPos m_UpdatePos;
	Rainbow::Quaternionf m_UpdateRot;

	Rainbow::Quaternionf m_PrevRotateQuat;
	Rainbow::Quaternionf m_RotateQuat;

	int m_PosRotationIncrements;
	WCoord m_ServerPos;
	Rainbow::Quaternionf m_ServerRot;
	bool m_hasPhysActor;
	WCoord m_OldBlockPos;
	float m_Speed;
	//tolua_end
}; //tolua_exports

#endif