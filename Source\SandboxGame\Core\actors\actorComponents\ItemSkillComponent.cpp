#include "ItemSkillComponent.h"
#include "PermitsDef.h"
#include "GunUseComponent.h"
#include "MpActorManager.h"
#include "PlayerControl.h"
#include "backpack.h"
#include "ProjectileFactory.h"
#include "AttackingTargetComponent.h"
#include "GameNetManager.h"
#include "PlayerAttrib.h"
#include "AITargetOwnerHurter.h"
#include "AITargetOwnerHurtee.h"
#include "AIFollowOwner.h"
#include "SprayPaintMgr.h"
#include "GameCamera.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "MiniReportMgrProxy.h"

#include "ActorBody.h"
#include "SandboxIdDef.h"
#include "BlockMaterialMgr.h"
#include "EffectManager.h"
#include "ActorVehicleAssemble.h"
#include "Components/Camera.h"
#include "ClientInfoProxy.h"
#include "CommonUtil.h"
#include "TameComponent.h"
#include "IMiniDeveloperProxy.h"
#include "chunk.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;
extern WCoord GetNearMobSpawnPos(ClientPlayer *player);
IMPLEMENT_COMPONENTCLASS(ItemSkillComponent)

ItemSkillComponent::ItemSkillComponent()
:m_nCurItemSkillID(0),
m_CurSndItemSkillID(0),
m_CurEffectItemSkillID(0),
m_CurItemSkillSnd(NULL)
{
}

ItemSkillComponent::~ItemSkillComponent()
{
	m_owner = NULL;
}

void ItemSkillComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void ItemSkillComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void ItemSkillComponent::OnTick()
{
	//道具技能声音和特效
	if (m_CurItemSkillSnd)
	{
		if (!GetOwner()) return;
		m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
		if (!m_owner) return;
		if (m_CurSndItemSkillID > 0 && m_CurSndItemSkillID != getCurItemSkillID())
			playItemSkillLoopSound(true);
		else
			m_CurItemSkillSnd->setPosition(m_owner->getPosition().toVector3());
	}

	if (m_CurEffectItemSkillID > 0 && m_CurEffectItemSkillID != getCurItemSkillID())
		playItemSkillLoopEffect(true);	
}

bool ItemSkillComponent::useItemSkill(int itemid, int status, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos, std::string clientParam)
{
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (m_owner->isInSpectatorMode())
	{
		return false;
	}

	World *pWorld = m_owner->getWorld();
	if (!pWorld->isRemoteMode() && !m_owner->checkActionAttrState(ENABLE_USEITEM))
	{
		m_owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14004);
		return false;
	}

	int curToolID = m_owner->getCurToolID();
	if (!GetDefManagerProxy()->getGunDef(curToolID))
	{
		if (itemid != curToolID) return false;

		const ItemDef *def = GetDefManagerProxy()->getItemDef(itemid);
		if (def == NULL) return false;
		int i;
		for (i = 0; i < (int)def->SkillID.size(); i++)
		{
			if (def->SkillID[i] == skillid)
			{
				break;
			}
		}
		if (i == def->SkillID.size())
		{
			return false;
		}
	}
	int uin = m_owner->getUin();
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canUseItem",
		SandboxContext(nullptr).SetData_Number("uin", uin).SetData_Number("itemid", itemid));
	bool canUseItemFlag=false;
	if (result.IsExecSuccessed())
	{
		canUseItemFlag = result.GetData_Bool();
	}
	if (!canUseItemFlag) return false;
	float rotateYaw;
	float rotationPitch;
	Direction2PitchYaw(&rotateYaw, &rotationPitch, currentDir);

	GunUseComponent* gunlogic = m_owner->getGunLogical();
	if (gunlogic)
		gunlogic->setGunInfo(10, rotateYaw, rotationPitch, currentEyePos);
	notifyUseItemSkill2Tracking(itemid, status, skillid);
	LivingAttrib* pLivingAttrib = m_owner->getLivingAttrib();
	if (pLivingAttrib) pLivingAttrib->removeBuff(INVULNERABLE_BUFF);
	m_nCurItemSkillID = skillid;
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(m_nCurItemSkillID);
	//ActorBody *body = m_owner->getBody();
	if (skilldef)
	{
		if (status == PLAYEROP_STATUS_BEGIN) //长按开始
		{
			m_owner->setAtkingTarget(NULL);
			if (skilldef->ChargeTime > 0.001 && skilldef->ChargeType > 0)
			{
				if (skilldef->TpsStartAct && m_owner->getBody())
					m_owner->getBody()->playAnim(SEQ_ITEMSKILL_START);

				if (skilldef->StartEffect.size())
				{
					m_owner->playMotion(skilldef->StartEffect.c_str());
				}
				if (skilldef->StartSound.size())
				{
					auto sound = m_owner->getSoundComponent();
					if (sound)
					{
						sound->playSound(skilldef->StartSound.c_str(), 1.0f, 1.0f);
					}
				}

				//m_AttackAnimType = ATTACK_ALL;
				//m_AttackAnimTicks = Rainbow::MAX_INT;;
				m_owner->getAttackingTargetComponent()->setAttackAnim(ATTACK_ALL, Rainbow::MAX_INT);
				m_owner->setOperate(PLAYEROP_USE_ITEM_SKILL, Rainbow::MAX_INT, m_owner->getCurToolID());
				playItemSkillLoopEffect(false);
				playItemSkillLoopSound(false);
			}
			else
			{
				m_owner->m_RangeAttackPower = 1.0f;
				if (!useItemSkillOper(itemid, skillid, currentEyePos, currentDir, blockpos, obj, centerPos,clientParam))
				{
					m_nCurItemSkillID = 0;
					return false;
				}

				//动作音效等
				if (m_owner->getBody()) {
					if (skilldef->TpsAttackAct)
						m_owner->getBody()->playAnim(SEQ_ITEMSKILL_ATTACK);
					else
						m_owner->getBody()->playAnim(SEQ_TOOL_ATTACK);

					if (skilldef->AttackEffect.size())
					{
						m_owner->getBody()->playMotion(skilldef->AttackEffect.c_str(), 0, false, skilldef->AttackEffectTime);
						m_owner->getBody()->setMotionScale(skilldef->AttackEffect.c_str(), skilldef->AttackEffectSize);
					}
				}

				if (skilldef->AttackSound.size())
				{
					auto sound = m_owner->getSoundComponent();
					if (sound)
					{
						sound->playSound(skilldef->AttackSound.c_str(), 1.0f, 1.0f);
					}
				}
					
				m_nCurItemSkillID = 0;
			}
			m_owner->useItemOnTrigger(itemid);
		}
		//松手
		else if (status == PLAYEROP_STATUS_END)
		{
			if (m_owner->isAttacking())
			{
				m_owner->m_RangeAttackPower = 1.0f;
				if (skilldef->ChargeTime > 0.001)
				{
					float at = skilldef->ChargeTime;
					if (at == 0) at = 1.0f;
					m_owner->m_RangeAttackPower = float(m_owner->getOperateTicks()) / (20.0f * at);
					if (m_owner->m_RangeAttackPower > 1.0f)
						m_owner->m_RangeAttackPower = 1.0f;
				}
				useItemSkillOper(itemid, skillid, currentEyePos, currentDir, blockpos, obj, centerPos,clientParam);
				m_owner->getAttackingTargetComponent()->setAttackAnim(ATTACK_ALL);
				//m_owner->setAttackAnim(ATTACK_ALL);//by__Logo	
			}
			playItemSkillLoopEffect(true);
			playItemSkillLoopSound(true);

			m_owner->onOperateEnded();

			//动作音效等
			if (m_owner->getBody()) {
				if (skilldef->TpsAttackAct)
					m_owner->getBody()->playAnim(SEQ_ITEMSKILL_ATTACK);
				else
					m_owner->getBody()->playAnim(SEQ_TOOL_ATTACK);
			}
			if (skilldef->AttackEffect.size())
			{
				// 松手
				if (!m_owner->needHandleWeaponMotionForView(3, skilldef->AttackEffect.c_str()))
				{
					// 20210715 特效播放bug codeby wudeshen
					m_owner->playMotion(skilldef->AttackEffect.c_str(), 1, false, skilldef->AttackEffectTime);
					m_owner->getBody()->setMotionScale(skilldef->AttackEffect.c_str(), skilldef->AttackEffectSize);
				}

			}
			if (skilldef->AttackSound.size())
			{
				auto sound = m_owner->getSoundComponent();
				if (sound)
				{
					sound->playSound(skilldef->AttackSound.c_str(), 1.0f, 1.0f);
				}
			}
			m_nCurItemSkillID = 0;
		}
		else
		{

			playItemSkillLoopEffect(true);
			playItemSkillLoopSound(true);

			if (m_owner->isAttacking())
			{
				m_owner->m_RangeAttackPower = 0.0f;
				m_owner->getAttackingTargetComponent()->setAttackAnim(ATTACK_ALL);
				//m_owner->setAttackAnim(ATTACK_ALL);//by__Logo			
			}

			m_owner->onOperateEnded();
			m_nCurItemSkillID = 0;
		}
	}

	return true;	
}

void ItemSkillComponent::notifyUseItemSkill2Tracking(int itemid, int status, int skillid)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	World* pWorld = m_owner->getWorld();
	if (!pWorld->isRemoteMode())
	{
		PB_ItemSkillUseHC itemSkillUseHC;
		itemSkillUseHC.set_objid(m_owner->getObjId());
		itemSkillUseHC.set_itemid(itemid);
		itemSkillUseHC.set_status(status);
		itemSkillUseHC.set_skillid(skillid);

		pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ITEM_SKILL_USE_HC, itemSkillUseHC, m_owner);
	}	
}

const ItemSkillDef* ItemSkillComponent::getCurItemSkillDef()
{
	int curSkillID = getCurItemSkillID();
	if (curSkillID)
		return GetDefManagerProxy()->getItemSkillDef(curSkillID);
	return NULL;
}

int ItemSkillComponent::getCurItemSkillID()
{
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (g_pPlayerCtrl && g_pPlayerCtrl == m_owner && m_owner->isInSpectatorMode() && m_owner->getSpectatorType() == SPECTATOR_TYPE_FOLLW)
	{
		ClientPlayer* target = g_pPlayerCtrl->getToSpectatorPlayer();
		if (target)
		{
			target->getCurItemSkillID();
		}
	}
	return m_nCurItemSkillID;
}

//extern bool g_EnableReLighting;
bool ItemSkillComponent::useItemSkillOper(int itemid, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos,  std::string clientParam)
{
	//if(m_pWorld->isRemoteMode())
	//{
	//	return ;
	//}
	if (!GetWorldManagerPtr())
	{
		return false;
	}
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(m_nCurItemSkillID);
	if (skilldef)
	{
		m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
		World* pWorld              = m_owner->getWorld();
		PlayerAttrib *playerAttrib = m_owner->getPlayerAttrib();
		BackPack* backpack         = m_owner->getBackPack();	
		ClientActorMgr* actorMgr = pWorld->getActorMgr() ? pWorld->getActorMgr()->ToCastMgr() : nullptr;
		int uin = m_owner->getUin();
		long long objid = m_owner->getObjId();
		if (!pWorld->isRemoteMode())
		{
			//消耗是否足够的判断
			if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
			{
				for (int j = 0; j < (int)skilldef->ItemSkillCosts.size(); j++)
				{
					if (skilldef->ItemSkillCosts[j].CostType == 0)//消耗耐久度
					{
						if (m_owner->getEquipItemDuration(EQUIP_WEAPON) < skilldef->ItemSkillCosts[j].CostVal)
						{
							return false;
						}

						const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(itemid, true);
						//对于不可堆叠的的工具类，消耗耐久
						if (itemdef->StackMax <= 1 && skilldef->ItemSkillCosts[j].CostVal > 0)
						{
							m_owner->addCurToolDuration(-skilldef->ItemSkillCosts[j].CostVal);
						}
					}
					else if (skilldef->ItemSkillCosts[j].CostType == 1 || skilldef->ItemSkillCosts[j].CostType == 3)//消耗饥饿度
					{
						// 若都不显示，则体力值饥饿值都不更新
						if (playerAttrib->strengthFoodShowState() != SFS_Empty)
						{
							if (!playerAttrib->useCompatibleStrength())
							{
								if (playerAttrib->getFoodLevel() < skilldef->ItemSkillCosts[j].CostVal)
								{
									return false;
								}
								playerAttrib->m_FoodLevel -= skilldef->ItemSkillCosts[j].CostVal;
							}
							else
							{
								if (playerAttrib->getStrength() < skilldef->ItemSkillCosts[j].CostVal)
								{
									return false;
								}
								playerAttrib->addStrength((float)-skilldef->ItemSkillCosts[j].CostVal);
							}
						}
					}
					else if (skilldef->ItemSkillCosts[j].CostType == 2)//消耗道具
					{
						if (skilldef->ItemSkillCosts[j].CostTarget > 0)
						{
							int bulletCount = backpack->getItemCountInNormalPack(skilldef->ItemSkillCosts[j].CostTarget);
							//看数量是否够
							if (bulletCount < skilldef->ItemSkillCosts[j].CostVal)
							{
								return false;
							}
							backpack->removeItemInNormalPack(skilldef->ItemSkillCosts[j].CostTarget, skilldef->ItemSkillCosts[j].CostVal);
						}
					}
				}
			}

			//cd判断
			if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
			{
				if (skilldef->CDTime > 0)
				{

					float trggercd = 0.0f;
					if (m_owner)
					{
						trggercd = MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->getItemCdForTrigger(m_owner->getUin(), itemid);
					}
					if (trggercd >= 0.0f) // 如果触发器设置了冷却以触发器为准
					{
						if (m_owner->getSkillCD(itemid) > 0.00001)
						{
							return false;
						}
						m_owner->setSkillCD(itemid, trggercd);
						m_owner->syncSkillCD(itemid, trggercd);
					}
					else
					{
						if (m_owner->getSkillCD(m_nCurItemSkillID + 1000000) > 0.00001)
						{
							return false;
						}
						m_owner->setSkillCD(m_nCurItemSkillID + 1000000, skilldef->CDTime);
						m_owner->syncSkillCD(m_nCurItemSkillID + 1000000, skilldef->CDTime);
					}
				}
			}

			//触发所有方块效果
			if (skilldef->TargetType == 1)
			{
				SprayPaintMgr * sprayPaintMgr = dynamic_cast<SprayPaintMgr*>(g_WorldMgr->getSandboxMgr("SprayPaintMgr"));
				g_EnableReLighting = false;
				//触发所有的方块效果
				for (int i = 0; i < (int)skilldef->SkillFuncions.size(); i++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[i];
					if (functiondef->oper_id == 1 && functiondef->func.blockfun.ActionType == 3)    	//20211020 喷漆 codeby:柯冠强
					{
						if (sprayPaintMgr)
						{
							sprayPaintMgr->doSprayPaint(m_owner, blockpos);
						}
						//SprayPaintMgr::getInstance()->doSprayPaint(m_owner, blockpos);
					}
					else
					{
						for (int j = 0; j < (int)blockpos.size(); j++)
						{
							if (functiondef->oper_id == 1) //方块功能
							{
								if (functiondef->func.blockfun.ActionType == 0)
								{
									WCoord coord = blockpos[j];
									if (skilldef->RangeType == 0 && skilldef->SkillType == 1)//单体
									{
										coord.y--;
									}
									int blockid = pWorld->getBlockID(coord.x, coord.y, coord.z);

									std::vector<CSPermitBitType> csPermitType;
									csPermitType.push_back(CS_PERMIT_USE_ITEM);
									csPermitType.push_back(CS_PERMIT_DESTROY_BLOCK);

									SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorBlock",
										SandboxContext(nullptr)
										.SetData_Number("uin", uin)
										.SetData_Number("tool", m_owner->getCurToolID())
										.SetData_Number("blockid", blockid)
										.SetData_Usertype<std::vector<CSPermitBitType>>("csPermitType",&csPermitType));
									bool canInteractorBlockFlag=false;
									if (result.IsExecSuccessed())
									{
										canInteractorBlockFlag = result.GetData_Bool();
									}
									if (blockid && canInteractorBlockFlag)
									{
										//const BlockDef *def = GetDefManagerProxy()->getBlockDef(blockid);
										BlockMaterial *blockmtl = g_BlockMtlMgr.getMaterial(blockid);
										int blockdata = pWorld->getBlockData(coord);
										if (blockmtl && (blockmtl->getDestroyHardness(blockdata, m_owner) >= 0 || g_WorldMgr->isGodMode() || pWorld->CheckBlockSettingEnable(blockmtl, ENABLE_DESTROYED) == 2))
										{
											if (functiondef->func.blockfun.DropOut == 1)
												pWorld->playerDestroyBlock(coord, BLOCK_MINE_TOOLFIT, 0, 0, m_owner->getUin());
											else
												pWorld->playerDestroyBlock(coord, BLOCK_MINE_NONE, 0, 0, m_owner->getUin());
										}
									}
								}
								else if (functiondef->func.blockfun.ActionType == 1)
								{
									if (pWorld->getBlockID(blockpos[j].x, blockpos[j].y, blockpos[j].z) == 0)
									{
										if (functiondef->func.blockfun.BlockID == BLOCK_HOTCRYSTAL)
										{
											//硫磺金沙向上喷气
											int blockdata = 1 + DIR_POS_Y;
											pWorld->setBlockAll(blockpos[j], functiondef->func.blockfun.BlockID, blockdata, 2);
										}
										else
										{
											pWorld->setBlockAll(blockpos[j], functiondef->func.blockfun.BlockID, 0, 2);
										}
									}
								}
								else if (functiondef->func.blockfun.ActionType == 2)
								{
									WCoord coord = blockpos[j];
									if (skilldef->RangeType == 0 && skilldef->SkillType == 1)//单体
									{
										coord.y--;
									}
									int blockid = pWorld->getBlockID(coord.x, coord.y, coord.z);
									if (blockid)
									{
										auto mtl = g_BlockMtlMgr.getMaterial(blockid);
										const BlockDef *def = GetDefManagerProxy()->getBlockDef(blockid);
										if (def && (def->Hardness >= 0 ||  (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode())))
										if (mtl && (mtl->getBlockHardness() >= 0 || g_WorldMgr->isGodMode()))
										{
											if (functiondef) pWorld->setBlockAll(coord, functiondef->func.blockfun.BlockID, 0, 2);
										}
									}
								}
							}
						}
					}
				}
				g_EnableReLighting = true;
				for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
					if (functiondef->oper_id == 7)//发射投射物
					{
						doActualItemSkillRangeAttack(NULL, functiondef->func.throwfun.ProjectileID, functiondef->func.throwfun.SpeedAdd + 1);
					}
				}
			}
			//触发所有生物效果
			else if (skilldef->TargetType == 0)
			{
				for (int i = 0; i < (int)obj.size(); i++)
				{
					//触发所有的效果
					if (!actorMgr) continue;
					ClientActor *actor = actorMgr->findActorByWID(obj[i]);
					if (actor == NULL)
						continue;

					ActorLiving *live = dynamic_cast<ActorLiving *>(actor);
					if (!live)
					{
						// 物理属性 需要有效果 如果是物理块先不要continue
						// 这里判断是否为物理actor
						PhysicsLocoMotion*  physxMotion = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
						if (!(physxMotion && physxMotion->m_hasPhysActor))
						{
							continue;
						}
					}

					//目标身上加特效
					if (skilldef->EffectTarget.size())
					{
						auto effectComponent = actor->getEffectComponent();
						if (effectComponent)
						{
							effectComponent->playBodyEffect((char *)skilldef->EffectTarget.c_str(), true, skilldef->EffectTargetTime);
							effectComponent->setBodyEffectScale((char*)skilldef->EffectTarget.c_str(), skilldef->EffectTargetSize);
						}
					}

					//播放音效
					if (skilldef->EffectTargetSound.size())
					{
						auto sound = actor->getSoundComponent();
						if (sound)
						{
							sound->playSound(skilldef->EffectTargetSound.c_str(), 1.0f, 1.0f);
						}
					}

					for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
					{
						ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
						if (functiondef->oper_id == 2)	//攻击
						{
							if (functiondef->func.attackfun.AttackVal > 0)
							{
								// 是否有削韧技能
								int touReduce = -1;
								for (int k = 0; k < (int)skilldef->SkillFuncions.size(); k++)
								{
									ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[k];
									// 削韧
									if (functiondef->oper_id == 22)
										touReduce = functiondef->func.toughnessReduceFun.touReduce;

								}
								m_owner->doActualItemSkillAttack(actor, (ATTACK_TYPE)functiondef->func.attackfun.AttackType, functiondef->func.attackfun.AttackVal, touReduce);
							}
							else
								actor->getAttrib()->addHP((float)(-functiondef->func.attackfun.AttackVal));
						}
						else if (functiondef->oper_id == 3)	//加buff
						{
							ActorLiving *live = dynamic_cast<ActorLiving *>(actor);
							if (live && (functiondef->func.bufffun.Odds == 0 || GenRandomInt(100) < functiondef->func.bufffun.Odds))
							{
								if (GetDefManagerProxy()->isCustomStatus(functiondef->func.bufffun.BuffID)) // 自定义效果读取到的id 没有合并数据 lua获取的有合并
								{
									live->getLivingAttrib()->addBuff(functiondef->func.bufffun.BuffID, 1, (int)(functiondef->func.bufffun.Duration * 20), 0, objid);
								}
								else
								{
									live->getLivingAttrib()->addBuff(functiondef->func.bufffun.BuffID / 1000, functiondef->func.bufffun.BuffID % 1000, (int)(functiondef->func.bufffun.Duration * 20), 0, objid);
								}
								live->setBeHurtTarget(m_owner);
								live->setBeAtk(m_owner);
							}
						}
						else if (functiondef->oper_id == 4) //击飞击退
						{
							if (skilldef->RangeType < 2)
							{
								WCoord originpos = m_owner->getPosition();
								WCoord hitcenter(actor->getPosition().x, actor->getPosition().y, actor->getPosition().z);
								Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

								if (skilldef->SkillType == 1 || skilldef->RangeType == 1)
								{
									dp = currentDir;
								}
								float rotateYaw;
								float rotationPitch;
								Direction2PitchYaw(&rotateYaw, &rotationPitch, dp);
								Rainbow::Vector3f dir = Yaw2FowardDir(rotateYaw);
								//Rainbow::Vector3f dir = Yaw2FowardDir(m_LocoMotion->m_RotateYaw);

								// 这里判断是否为物理actor
								PhysicsLocoMotion*  physxMotion = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
								if (physxMotion && physxMotion->m_PhysActor)
								{
									Rainbow::Vector3f addmotion(dir.x*functiondef->func.driftfun.RepelPower*m_owner->m_RangeAttackPower, functiondef->func.driftfun.BlowUpPower*m_owner->m_RangeAttackPower, dir.z*functiondef->func.driftfun.RepelPower*m_owner->m_RangeAttackPower);
									physxMotion->m_PhysActor->SetLinearVelocity(addmotion * 10.0f);
								}
								else
								{
									Rainbow::Vector3f addmotion(dir.x*functiondef->func.driftfun.RepelPower*m_owner->m_RangeAttackPower, functiondef->func.driftfun.BlowUpPower*m_owner->m_RangeAttackPower, dir.z*functiondef->func.driftfun.RepelPower*m_owner->m_RangeAttackPower);
									actor->setMotionChange(addmotion, true);
								}
							}
							else
							{

								WCoord originpos(centerPos.x, centerPos.y, centerPos.z);
								WCoord hitcenter(actor->getPosition().x, actor->getPosition().y, actor->getPosition().z);
								Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();
								if (dp.Length() <= 100)
								{
									dp = currentDir;
									if (functiondef->func.driftfun.RepelPower < 0)
									{
										continue;
									}
								}

								float rotateYaw;
								float rotationPitch;
								Direction2PitchYaw(&rotateYaw, &rotationPitch, dp);
								Rainbow::Vector3f dir = Yaw2FowardDir(rotateYaw);
								//Rainbow::Vector3f dir = Yaw2FowardDir(m_LocoMotion->m_RotateYaw);
								Rainbow::Vector3f addmotion(dir.x*functiondef->func.driftfun.RepelPower*m_owner->m_RangeAttackPower, functiondef->func.driftfun.BlowUpPower*m_owner->m_RangeAttackPower, dir.z*functiondef->func.driftfun.RepelPower*m_owner->m_RangeAttackPower);
								actor->setMotionChange(addmotion, true);
							}
						}
						else if (functiondef->oper_id == 6) //治疗
						{
							/*if(!actor->canAttackByItemSkill(skillid, this))
							{
							continue;
							}*/
							if (functiondef->func.curefun.TreatType == 0)//生命值
							{
								actor->getAttrib()->addHP((float)functiondef->func.curefun.TreatVal);
							}
							else if (functiondef->func.curefun.TreatType == 1)  //饥饿值
							{
								ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
								if (player)
								{
									player->getPlayerAttrib()->m_FoodLevel += functiondef->func.curefun.TreatVal;
								}
							}
							else if (functiondef->func.curefun.TreatType == 2)  //饥饿耐力
							{
								ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
								if (player)
								{
									player->getPlayerAttrib()->m_FoodSatLevel += functiondef->func.curefun.TreatVal;
								}
							}
							else if (functiondef->func.curefun.TreatType == 3)  //氧气值
							{
								live->getLivingAttrib()->addOxygen((float)functiondef->func.curefun.TreatVal);
							}
							else if (functiondef->func.curefun.TreatType == 4)  //体力值
							{
								ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
								if (player)
								{
									player->getPlayerAttrib()->addStrength((float)functiondef->func.curefun.TreatVal);
								}
							}
						}
						else if (functiondef->oper_id == 9)	//把生物弄成宠物蛋效果
						{
							ClientMob *mob = dynamic_cast<ClientMob *>(actor);
							if (mob)
							{
								int EggID = 0;
								MINIW::ScriptVM::game()->callFunction("MonsterToEgg", "i>i", mob->getDef()->ID, &EggID);

								bool isTeamdBySame = true; //若是驯服的生物需是同一主人才能变蛋 code-by:lizb
								if (mob->getTamedOwnerID() > 0)
								{
									isTeamdBySame = mob->getTamedOwnerID() == objid;
								}

								if (EggID != 0 && isTeamdBySame)
								{
									mob->dropEgg(EggID, 1);
									//actor->dropItem(EggID, 1);
									actor->needClear();
								}
							}
						}
					}
				}

				for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
					if (functiondef->oper_id == 7)//发射投射物
					{
						doActualItemSkillRangeAttack(NULL, functiondef->func.throwfun.ProjectileID, functiondef->func.throwfun.SpeedAdd + 1);
					}
				}
			}

			//召唤怪物
			bool haveCallMob = false;
			for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
			{
				ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
				if (functiondef->oper_id == 5)
				{
					haveCallMob = true;
				}
			}
			if (haveCallMob)
			{
				std::vector<WCoord> blockposMob;
				for (int j = 0; j < (int)blockpos.size(); j++)
				{
					if (pWorld->getBlockID(blockpos[j].x, blockpos[j].y, blockpos[j].z) == 0)
					{
						blockposMob.push_back(blockpos[j]);
					}
				}
				//random_shuffle(blockposMob.begin(), blockposMob.end());

				//触发所有的效果
				for (int i = 0; i < (int)skilldef->SkillFuncions.size(); i++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[i];
					if (functiondef->oper_id == 5 && functiondef->func.summonfun.CallNum > 0) //召唤
					{
						int countmob = blockposMob.size() / functiondef->func.summonfun.CallNum + 1;

						int leftMob = functiondef->func.summonfun.CallNum;
						for (int j = 0; j < (int)blockposMob.size(); j = j + countmob)
						{
							ClientMob *newmob = ClientMob::createFromDef(functiondef->func.summonfun.MobID);
							newmob->setTamedOwnerUin((int)objid);
							TameComponent* tameComp = newmob->sureTameComponent();
							if (tameComp)
							{
								tameComp->SetSpawnSkillID(skilldef->ID);
							}
							newmob->getLocoMotion()->gotoPosition(m_owner->getPosition());
							WCoord pos = blockposMob[j] * BLOCK_SIZE;
							actorMgr->spawnActor(newmob, pos, GenRandomFloat()*360.0f, 0);
							newmob->setTeam(m_owner->getTeam());
							if (functiondef->func.summonfun.IsFollow)
							{
								if (!newmob->IsUseAILua())
								{
									newmob->addAiTask<AIFollowOwner>(4, 2.0, 1000, 200, 0);
									newmob->addAiTaskTarget<AITargetOwnerHurtee>(5);
									newmob->addAiTaskTarget<AITargetOwnerHurter>(6);
								}
							}
							else
							{
								if (tameComp)
								{
									tameComp->SetSpawnSkillID(0);
								}
								newmob->setTamedOwnerUin(0);
							}

							if (functiondef->func.summonfun.Duration > 0)
								newmob->setDieTick(functiondef->func.summonfun.Duration * 20);
							leftMob--;
						}

						for (int t = 0; t < leftMob; t++)
						{
							ClientMob *newmob = ClientMob::createFromDef(functiondef->func.summonfun.MobID);
							newmob->setTamedOwnerUin((int)objid);
							newmob->getLocoMotion()->gotoPosition(m_owner->getPosition());
							WCoord pos = GetNearMobSpawnPos(m_owner);
							actorMgr->spawnActor(newmob, pos, GenRandomFloat()*360.0f, 0);
							newmob->setTeam(m_owner->getTeam());
							if (functiondef->func.summonfun.IsFollow)
							{
								if (!newmob->IsUseAILua())
								{
									newmob->addAiTask<AIFollowOwner>(4, 2.0, 1000, 200, 0);
									newmob->addAiTaskTarget<AITargetOwnerHurtee>(5);
									newmob->addAiTaskTarget<AITargetOwnerHurter>(6);
								}
							}
							else
								newmob->setTamedOwnerUin(0);

							if (functiondef->func.summonfun.Duration > 0)
								newmob->setDieTick(functiondef->func.summonfun.Duration * 20);
						}
					}
				}
			}

			//交互对话
			int hasDialogue = false;

			for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
			{
				ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
				if (functiondef->oper_id == 10)	//打开交互对话
				{
					hasDialogue = true;
					if (!m_owner->canShowInteract(functiondef->func.interactfun.type, functiondef->func.interactfun.id, true))
					{
						m_owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 1355);
						hasDialogue = false;
						break;
					}
				}
			}

			if (hasDialogue)
			{
				m_owner->openPlotDialogue(NULL, itemid);
				m_owner->shortcutItemUsed();
			}

			//物理投掷
			for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
			{
				ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
				if (functiondef->oper_id == 12)
				{
					auto gravityActor = m_owner->getCatchGravityActor();
					ClientActorProjectile *actor = dynamic_cast<ClientActorProjectile*>(gravityActor);
					// 这里判断一下是否物理机械actor
					ActorVehicleAssemble* vehicleActor = dynamic_cast<ActorVehicleAssemble*>(gravityActor);
					if (actor || vehicleActor)
					{
						PhysicsLocoMotion *loc;
						if (actor)
							loc = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
						else
							loc = dynamic_cast<PhysicsLocoMotion *>(vehicleActor->getLocoMotion());

						if (loc && loc->m_PhysActor)
						{
							if (actor)
								m_owner->doPutGravityActor(actor);
							else
								m_owner->doPutGravityActor(vehicleActor);


							//onOperateEnded();

							float shotPower;
							shotPower = m_owner->m_RangeAttackPower;
							if (shotPower > 1.0f) shotPower = 1.0f;

							float charge = shotPower;
							float unit = (float)functiondef->func.physicsThrowfun.maxMomentum;
							float motionY = 12;
							float minInitialV = 0;
							//Rainbow::Vector3f dir = Yaw2FowardDir(this->getLocoMotion()->m_RotateYaw);
							//loc->m_Motion = Rainbow::Vector3f(dir.x*(unit*charge + minInitialV), motionY, dir.z*(unit*charge + minInitialV));

							Rainbow::Vector3f dir;
							PitchYaw2Direction(dir, m_owner->getLocoMotion()->m_RotateYaw, m_owner->getLocoMotion()->m_RotationPitch);
							loc->m_Motion = Rainbow::Vector3f(dir.x*(unit*charge + minInitialV), dir.y*(unit*charge + minInitialV), dir.z*(unit*charge + minInitialV));

							RigidBaseActor* rigidactor = loc->m_PhysActor;
							bool angularVelocity = true;
							if (rigidactor)
							{
								const char* actorName = rigidactor->GetGameObject()->GetName();
								if (actorName && strcmp(actorName, "vehicle") == 0)
								{
									angularVelocity = false;
								}
							}
							if (angularVelocity)
							{
								loc->m_PhysActor->SetAngularVelocity(Rainbow::Vector3f(
									(float)(GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.kick_ball_angularX_v),
									(float)(GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.kick_ball_angularY_v),
									(float)(GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.kick_ball_angularZ_v)));
							}

							loc->m_PhysActor->SetLinearVelocity(loc->m_Motion * 10.0f);
							//重力手套发射埋点
							//if (m_nCurItemSkillID == 113)
							//{
								//if (g_pPlayerCtrl && !pWorld->isRemoteMode())
									//g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 30002, "", g_pPlayerCtrl->getCurWorldType(), "12293");
							//}

							//if (charge >= GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.strength_charge)
							//{
							//	ClientActorProjectile *projectile = dynamic_cast<ClientActorProjectile *>(actor);
							//	if (projectile)
							//	{
							//		projectile->playMotion("ball_power_high");
							//	}
							//	else
							//	{
							//		actor->playBodyEffect("ball_power_high");
							//	}
							//}
							//else
							//{
							//	ClientActorProjectile *projectile = dynamic_cast<ClientActorProjectile *>(actor);
							//	if (projectile)
							//	{
							//		projectile->playMotion("ball_power_low");
							//	}
							//	else
							//	{
							//		actor->playBodyEffect("ball_power_low");
							//	}
							//}
						}
						//doPutGravityActor(actor);
						//doActualItemSkillRangeAttack(NULL, actor->GetItemId(), functiondef->func.throwfun.SpeedAdd + 1);
					}

				}
			}

		}

		//物理抓取
		for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
		{
			ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
			if (functiondef->oper_id == 11)
			{
				for (int i = 0; i < (int)obj.size(); i++)
				{
					ClientActor *actor = actorMgr->findActorByWID(obj[i]);
					if (actor == NULL)
						continue;
					PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
					VehicleAssembleLocoMotion* vehicleLoc = dynamic_cast<VehicleAssembleLocoMotion *>(actor->getLocoMotion());
					if (loc && loc->m_hasPhysActor)
					{
						// 这里判断一下是否是物理机械actor
						if (vehicleLoc)
						{
							if (actor->getMass() <= functiondef->func.physicsCatchfun.maxMass)
							{
								m_owner->doCatchGravityActor(actor, functiondef->func.physicsCatchfun.distance, functiondef->func.physicsCatchfun.high);
							}
						}
						else
						{
							// 客机直接catch
							const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(loc->m_ownerItemID);
							if ((pWorld->isRemoteMode() && physicsActorDef && physicsActorDef->Mass <= functiondef->func.physicsCatchfun.maxMass) || loc->m_PhysActor && loc->m_PhysActor->GetMass() <= functiondef->func.physicsCatchfun.maxMass)
							{
								m_owner->doCatchGravityActor(actor, functiondef->func.physicsCatchfun.distance, functiondef->func.physicsCatchfun.high);
							}
						}

						m_owner->setOperate(PLAYEROP_CATCH_GRAVITYACTOR, Rainbow::MAX_INT, skilldef->TpsChargeAct);

						if (skilldef->ChargeEffect.size())
						{
							// 抓取
							if (!m_owner->needHandleWeaponMotionForView(1, skilldef->ChargeEffect.c_str()))
							{
								m_owner->getBody()->playMotion(skilldef->ChargeEffect.c_str(), 30, true, skilldef->ChargeEffectTime);
								m_owner->getBody()->setMotionScale(skilldef->ChargeEffect.c_str(), skilldef->ChargeEffectSize);
							}
						}
					}
				}
			}
		}

		//解锁配方
		for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
		{
			ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
			if (functiondef && functiondef->oper_id == 8)	//解锁配方
			{
				int type = functiondef->func.unlockfun.unlockType;
				if ((type == 0 && GetWorldManagerPtr()->isUnlockItem(functiondef->func.unlockfun.itemID))
					|| (type == 1 && m_owner->isUnlockItem(functiondef->func.unlockfun.itemID)))
				{
					if (m_owner->hasUIControl())
					{
						CommonUtil::GetInstance().PostInfoTips(1352);
					}

					return false;
				}
				if (!pWorld->isRemoteMode())
				{
					m_owner->shortcutItemUsed();
					if (type == 0 && (GetWorldManagerPtr() != NULL) && (functiondef != nullptr))
						GetWorldManagerPtr()->addUnlockItem(functiondef->func.unlockfun.itemID);
				}

				if (type == 1)
					m_owner->addUnlockItem(functiondef->func.unlockfun.itemID);

				if (g_pPlayerCtrl && !pWorld->isRemoteMode())
				{
					char sItemID[10];
					sprintf(sItemID, "%d", itemid);
					//g_pPlayerCtrl->statisticToWorld(uin, 30002, "", g_pPlayerCtrl->getCurWorldType(), sItemID);
				}
			}
		}

		// 巴啦啦魔法棒  20210712  codeby： wudeshen
		for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
		{
			ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
			if (functiondef->oper_id == 16 && !pWorld->isRemoteMode())
			{
				MINIW::ScriptVM::game()->callFunction("Balala_UseSkill", "u[ClientPlayer]u[World]s", m_owner, pWorld, clientParam.c_str());
			}
		}

		//20211026 喷漆埋点 code by:keguanqiang
		if (itemid == ITEM_PAINTTANK)
		{
			if (!pWorld->isRemoteMode())
				GetMiniReportMgrProxy()->standReportEvent("1003", "MINI_TOOL_BAR", "GraffitiButton", "click");
			else
				GetMiniReportMgrProxy()->standReportEvent("1001", "MINI_TOOL_BAR", "GraffitiButton", "click");
		}
		
	}
	return true;
}

void ItemSkillComponent::doActualItemSkillRangeAttack(ClientActor *target, int itemid, float ChargeMove)
{
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(m_nCurItemSkillID);
	if (!skilldef)
	{
		return;
	}
	if (!GetOwner()) return ;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	//int pullticks = m_OperateTicks;
	float shotPower;
	shotPower = m_owner->m_RangeAttackPower;
	if (shotPower > 1.0f) shotPower = 1.0f;

	shotPower = (shotPower*shotPower + shotPower * 2.0f) / 3.0f;

	float comparePower = 0.1f;
	if (GetClientInfoProxy()->isPC())
	{
		comparePower = 0.15f;
	}
	else if (GetClientInfoProxy()->isMobile())
	{
		comparePower = 0.1f;
	}
	else if (GetClientInfoProxy()->isPureServer())
	{
		comparePower = 0.15f;
	}

	if (!m_owner->getWorld()->isRemoteMode() && shotPower >= comparePower)
	{
		itemSkillAttackWithPower(itemid, shotPower*ChargeMove);
	}
}

void ItemSkillComponent::itemSkillAttackWithPower(int ConsumeId, float shotPower)
{
	float pitch = 1.0f / (GenRandomFloat()*0.4f + 1.2f) + shotPower * 0.5f;
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(m_nCurItemSkillID);
	if (!skilldef)
	{
		return;
	}
	if (shotPower > 1.0f) shotPower = 1.0f;
	bool canpickup = false;
	if (ConsumeId)
	{
		if (!GetOwner()) return;
		m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
		if (!m_owner) return;
		if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
		{
			canpickup = true;
		}
		int fireLv = 0;
		bool setfire = m_owner->getLivingAttrib()->getFireAspect(fireLv) > 0;
		ClientActorProjectile *projectile = ProjectileFactory::throwItemByActor(m_owner->getWorld(), m_owner, shotPower, ConsumeId, setfire, canpickup, fireLv);
	}
}
void ItemSkillComponent::playItemSkillLoopEffect(bool stop)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	if (stop)
	{
		m_owner->stopMotion(30);
		m_CurEffectItemSkillID = -1;
		return;
	}

	const ItemSkillDef *skilldef = GetDefManagerProxy()->getItemSkillDef(getCurItemSkillID());
	if (skilldef->ChargeEffect.size())
	{
		// 蓄力
		if (!m_owner->needHandleWeaponMotionForView(2, skilldef->ChargeEffect.c_str()))
		{
			m_owner->getBody()->playMotion(skilldef->ChargeEffect.c_str(), 30, true, skilldef->ChargeEffectTime);
			m_owner->getBody()->setMotionScale(skilldef->ChargeEffect.c_str(), skilldef->ChargeEffectSize);
		}

		m_CurEffectItemSkillID = m_nCurItemSkillID;
	}
}
void ItemSkillComponent::playItemSkillLoopSound(bool stop)
{
	if (stop)
	{
		OGRE_RELEASE(m_CurItemSkillSnd);
		m_CurSndItemSkillID = -1;
		return;
	}

	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	const ItemSkillDef *skilldef = GetDefManagerProxy()->getItemSkillDef(getCurItemSkillID());
	if (skilldef->ChargeSound.size())
	{
		m_CurItemSkillSnd = m_owner->getWorld()->getEffectMgr()->playLoopSound(m_owner->getPosition(), skilldef->ChargeSound.c_str(), 1.0f, 1.0f);
		m_CurSndItemSkillID = m_nCurItemSkillID;
	}
}

//////////////////////
IMPLEMENT_COMPONENTCLASS(ControlItemSkillComponent)

ControlItemSkillComponent::ControlItemSkillComponent():ItemSkillComponent()
{

}

bool ControlItemSkillComponent::useItemSkill(int itemid, int status, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos, std::string clientParam)
{
	m_ownerControl = GetOwnerT<PlayerControl>();
	if (!m_ownerControl->isCurToolUnlocked()) return false;
	if (!GetDefManagerProxy()->checkItemCrc(itemid))
	{
		CommonUtil::GetInstance().PostInfoTips(165);
		return false;
	}

	if (!ItemSkillComponent::useItemSkill(itemid, status, skillid, currentEyePos, currentDir, blockpos, obj, centerPos, clientParam))
	{
		return false;
	}
	else return true;
}
IMPLEMENT_COMPONENTCLASS(MPControlItemSkillComponent)


MPControlItemSkillComponent::MPControlItemSkillComponent():ControlItemSkillComponent()
{

}

bool MPControlItemSkillComponent::useItemSkill(int itemid, int status, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos, std::string clientParam)
{
	if (!ControlItemSkillComponent::useItemSkill(itemid, status, skillid, currentEyePos, currentDir, blockpos, obj, centerPos, clientParam)) return false;

	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (m_owner->getWorld()->isRemoteMode())
	{
		PB_ItemSkillUseCH itemSkillUseCH;
		itemSkillUseCH.set_itemid(itemid);
		itemSkillUseCH.set_status(status);
		itemSkillUseCH.set_skillid(skillid);
		itemSkillUseCH.set_itemindex(m_owner->getCurShortcut() + m_owner->getShortcutStartIndex());

		PB_Vector3* curPos = itemSkillUseCH.mutable_curpos();

		//喷漆特殊化处理：第三人称视角时将相机视角传给主机 by Jeff
		if (itemid == ITEM_PAINTTANK && m_ownerControl && (m_ownerControl->m_ViewMode == CAMERA_TPS_BACK || m_ownerControl->m_ViewMode == CAMERA_TPS_BACK_2))
		{
			
			Rainbow::Ray ray = m_ownerControl->m_pCamera->getEngineCamera()->ViewportPointToRay(Vector2f(m_ownerControl->m_CurMouseX, m_ownerControl->m_CurMouseY));

			if (curPos)
			{
				Vector3f vec3 = ray.m_Origin;
				curPos->set_x((int)vec3.x);
				curPos->set_y((int)vec3.y);
				curPos->set_z((int)vec3.z);
			}
			itemSkillUseCH.set_curdirx(ray.m_Direction.x);
			itemSkillUseCH.set_curdiry(ray.m_Direction.y);
			itemSkillUseCH.set_curdirz(ray.m_Direction.z);

		}
		else
		{
			if (curPos)
			{
				curPos->set_x((int)currentEyePos.x);
				curPos->set_y((int)currentEyePos.y);
				curPos->set_z((int)currentEyePos.z);
			}

			itemSkillUseCH.set_curdirx(currentDir.x);
			itemSkillUseCH.set_curdiry(currentDir.y);
			itemSkillUseCH.set_curdirz(currentDir.z);
		}


		itemSkillUseCH.set_curdirx(currentDir.x);
		itemSkillUseCH.set_curdiry(currentDir.y);
		itemSkillUseCH.set_curdirz(currentDir.z);

	// 巴啦啦魔法棒 -- 2021/07/21 codeby wudeshen
		if (itemid >= ITEM_BALALA_MAGIC1 && itemid <= ITEM_BALALA_MAGIC7)
		{
			char clientParam[256] = "";
			MINIW::ScriptVM::game()->callFunction("GetBalalaSkinTime", "i>s", itemid, clientParam);
			itemSkillUseCH.set_clientparam(clientParam);
		}
		GetGameNetManagerPtr()->sendToHost(PB_ITEM_SKILL_USE_CH, itemSkillUseCH);
	}

	return true;
}