#include "ActorAttribExecute.h"
#include "PlayerControl.h"
#include "PlayerAttrib.h"
#include "AttackedComponent.h"
#include "DieComponent.h"
#include "TriggerComponent.h"
#include "ScriptComponent.h"
#include "ActionAttrStateComponent.h"
#include "WorldManager.h"
#include "SandBoxManager.h"
#include "LuaInterfaceProxy.h"

#define ACTORATTRIBCHECK(p) if(!p)return false;

/***************************************ActorHpExecute*******************************************************************/
bool ActorHpExecute::ExecuteAdd(ActorAttrib* attrib, float& hp, bool overflowable, ActorAttribType type)
{
	ACTORATTRIBCHECK(attrib);
	if (type != ActorAttribType_Hp)
	{
		return false;
	}
	float maxHP = attrib->getMaxHP();
	float limitHP;
	float curLife = attrib->getHP();
	float extraHP = attrib->getExtraHP();
	if (curLife <= 0 && hp > 0) //����ֵΪ0ʱ���Ӳ���Ѫ
		return false;

	bool islive = curLife > 0 || extraHP > 0;
	//���ֱ�Ӳ� ���������
	limitHP = curLife > maxHP ? curLife : maxHP;

	ClientActor* actor = attrib->getOwnerActor();
	if (actor)
	{
		ActorLiving* targetLiving = dynamic_cast<ActorLiving*>(actor);
		if (targetLiving && 0 != hp && curLife != 0)
		{
			targetLiving->setHPProgressDirty();
		}

		if (!(hp >= 0 && curLife >= limitHP)) //Ѫ��������  ����ʾ��ѪЧ��
		{
			actor->addHPEffect(hp);
		}
	}

	if (curLife > maxHP && hp > 0) {
		hp = 0;
	}
	const int old = int(curLife);
	curLife += hp;
	if (actor)
	{
		GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_ACTOR_ATTRIB, SandBoxMgrTypeID::ACTOR_CHANGE_HP, actor->getObjId() & 0xffffffff, (char*)&hp, sizeof(hp));
	}
	auto actionAttrComp = actor->getActionAttrStateComponent();
	bool isAttrState = actionAttrComp ? actionAttrComp->checkActionAttrState(ENABLE_BEKILLED) : false;
	if (islive && curLife <= 0 && actor && !isAttrState)
	{
		curLife = 1;

		//�����ʾ�ܵ������˺�������
		auto pScriptComponent = actor->getScriptComponent();
		if (pScriptComponent)
		{
			pScriptComponent->OnEvent((unsigned int)CE_OnObjDeadlyHurt, true);
		}
	}

	if (curLife > limitHP) curLife = limitHP;
	if (curLife < 0) curLife = 0;
	ExecuteSet(attrib, curLife, overflowable, type);
	if (old == 0 && extraHP > 0)
	{
		attrib->setExtraHP(extraHP + hp);
	}
	else
	{
		// �ܵ��˺�������֮ǰִ�У������˺��¼�����ȡ������������Ϣ
		if (hp < 0 && actor)
		{
			auto attackComp = actor->getAttackedComponent();
			auto triggerComp = actor->getTriggerComponent();
			if (triggerComp)
			{
				triggerComp->beHurtOnTrigger(hp);
			}
			if (attackComp)
			{
				attackComp->recordAttacker(1);	// 1����
			}
		}

		if (islive && curLife == 0 && actor != NULL && extraHP == 0)
		{
			auto attackComp = actor->getAttackedComponent();
			if (attackComp)
			{
				attackComp->recordAttacker(2);	// 2����
			}
			auto dieComp = actor->SureDieComponent();
			if (dieComp)
			{
				dieComp->onDie();
			}
		}
	}

	return true;
}

bool ActorHpExecute::ExecuteSet(ActorAttrib* attrib, float val, bool overflowable, ActorAttribType type)
{
	ACTORATTRIBCHECK(attrib);
	if (type != ActorAttribType_Hp)
	{
		return false;
	}
	ClientActor* actor = attrib->getOwnerActor();
	float maxHP = attrib->getMaxHP();
	float curLife = attrib->getHP();
	float extraHP = attrib->getExtraHP();

	if (actor)
	{
		ActorLiving* targetLiving = dynamic_cast<ActorLiving*>(actor);
		if (targetLiving && curLife != val)
		{
			targetLiving->setHPProgressDirty();
		}
	}

	if (overflowable)
	{
		if (val > attrib->getLimitHP())
		{
			val = attrib->getLimitHP();
		}
	}
	else
	{
		if (val > maxHP)
		{
			val = maxHP;
		}
	}

	const int old = int(curLife);
	curLife = val;
	attrib->initHP(curLife);
	if (actor)
	{
		if (val > maxHP)
		{
			actor->syncAttr(ATTRT_CUR_OVERFLOWABLE_HP, val);
		}
		else
		{
			actor->syncAttr(ATTRT_CUR_HP, val);
		}
		if (g_WorldMgr && actor->isPlayer())//ͬ�������ͻ�Ѫ��ֵ�ı�
			g_WorldMgr->signChangedToSync(actor->getObjId(), BIS_HP);
	}
	//���������ֱ仯ʱ�·�֪ͨ
	if (int(val) != old)
	{
		if (actor == g_pPlayerCtrl)
		{
			//GameEventQue::GetInstance().postPlayerAttrChange();
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
		}

		if (actor) actor->Event2().Emit<int>("changeHPOnTrigger", old);
	}
	
	return true;
}

/***************************************ActorArmorExecute*******************************************************************/
bool ActorArmorExecute::ExecuteAdd(ActorAttrib* attrib, float& val, bool overflowable, ActorAttribType type)
{
	ACTORATTRIBCHECK(attrib);
	float diff = 0.f;
	switch (type)
	{
	case ActorAttribType_Hp:
		if (val >= 0)
		{
			return false;
		}
		if (m_value <= 0)
		{
			return false;
		}
		//����val���Ǹ�ֵ
		diff = val + m_value;
		val += m_value;
		if (fabs(diff) < 1)
		{
			val = 0.f;
			return ExecuteSet(attrib, m_minValue, overflowable, ActorAttribType_Armor);
		}
		else if (diff < 0.f)
		{
			ExecuteSet(attrib, m_minValue, overflowable, ActorAttribType_Armor);
			return false;
		}
		else
		{
			val = 0.f;
			return ExecuteSet(attrib, diff, overflowable, ActorAttribType_Armor);
		}
	case ActorAttribType_Armor:
		return ExecuteSet(attrib, m_value + val, overflowable, ActorAttribType_Armor);
	default:
		//nothing
		;
	}
	return false;
}

bool ActorArmorExecute::ExecuteSet(ActorAttrib* attrib, float val, bool overflowable, ActorAttribType type)
{
	ACTORATTRIBCHECK(attrib);
	switch (type)
	{
	case ActorAttribType_Hp:
		return false;
	case ActorAttribType_Armor:
	{
		int old = m_value;
		ClientActor* actor = attrib->getOwnerActor();
		if (actor)
		{
			ActorLiving* targetLiving = dynamic_cast<ActorLiving*>(actor);
			if (targetLiving && int(val) != old)
			{
				targetLiving->setHPProgressDirty();
				targetLiving->setArmorProgressDirty();
			}

			//if (!(val >= old && old >= m_maxValue)) //Ѫ��������  ����ʾ��ѪЧ��
			//{
			//	actor->addArmorEffect(val);
			//}
		}
		setValue(val);
		//���������ֱ仯ʱ�·�֪ͨ
		if (actor)
		{

			actor->syncAttr(ATTRT_ARMOR, m_value);
			if (actor == g_pPlayerCtrl && int(m_value) != old)
			{
				//GameEventQue::GetInstance().postPlayerAttrChange();
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
			}
		}
		return true;
	}
	default:
		//nothing
		;
	}
	return false;
}

void ActorArmorExecute::setValue(float val)
{
	m_value = val;
	if (m_value > m_maxValue)
	{
		m_value = m_maxValue;
	}
	if (m_value < m_minValue)
	{
		m_value = m_minValue;
	}
}

/***************************************ActorStrengthExecute*******************************************************************/

bool ActorStrengthExecute::ExecuteAdd(ActorAttrib* attrib, float& strength, bool overflowable, ActorAttribType type)
{
	if (strength > 0)
	{
		return false;
	}
	ACTORATTRIBCHECK(attrib);
	PlayerAttrib* playAttrib = dynamic_cast<PlayerAttrib*>(attrib);
	ACTORATTRIBCHECK(playAttrib);
	ClientActor* actor = attrib->getOwnerActor();
	std::vector<StatusAttInfo> vValue;
	playAttrib->getStatusAddAttInfo(BUFFATTRT_CONSUME_STRENGTH_DAMAGE, vValue);

	if (actor && vValue.size() > 0)
	{
		float curHp = attrib->getHP();
		//Ѫ�����ήΪ1
		//�ܵ������˺�,������ hp - (hp -1);
		float val = fabs(strength);
		if (curHp + strength < 1.f)
		{
			// ���Ѫ�������ۣ��Ͳ���Ѫ�� code_by:liya
			val = curHp - 1;
		}

		int uiType = 0;
		int valueType = 0;
		auto attackComp = actor->getAttackedComponent();
		for (auto p = vValue.begin(); p != vValue.end(); p++)
		{
			uiType = p->vValue[0].iType;
			auto enumdef = GetDefManagerProxy()->getBuffEffectEnumDef((int)(p->vValue[0].value));
			int iAtkType = ATTACK_PUNCH;
			if (enumdef)
			{
				iAtkType = enumdef->AttType - BuffAttrType::BUFFATTRT_MELEE_HURT;
				if (enumdef->AttType == BuffAttrType::BUFFATTRT_FIXED_HURT)
				{
					iAtkType = ATTACK_TYPE::ATTACK_FIXED;
				}
				if (enumdef->AttType == BuffAttrType::BUFFATTRT_TRUE_DAMAGE)
				{
					iAtkType = ATTACK_TYPE::TRUE_DAMAGE;

				}
				if (enumdef->AttType == BuffAttrType::BUFFATTRT_ICE_PROPERTY) {
					iAtkType = ATTACK_TYPE::ATTACK_ICE;
				}
			}
			if (val <= std::numeric_limits<float>::epsilon())
			{
				continue;
			}
			OneAttackData atkdata;
			//memset(&atkdata, 0, sizeof(atkdata));
			atkdata.damage_armor = false;
			// ���˺�����ϵͳ code-by:liya
			if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate && ((iAtkType >= ATTACK_PUNCH && iAtkType <= MAX_MAGIC_ATTACK) || iAtkType == PHYSICS_ATTACK))
			{
				const int index = AtkType2ArmorIndex((ATTACK_TYPE)iAtkType);
				atkdata.atkTypeNew = (1 << index);
				if (iAtkType == ATTACK_EXPLODE)
				{
					atkdata.explodePoints[0] = val;
				}
				else
				{
					atkdata.atkPointsNew[index] = val;
				}
			}
			else
			{
				atkdata.atktype = (ATTACK_TYPE)iAtkType;
				atkdata.atkpoints = val;
			}
			atkdata.fromplayer = NULL;
			attackComp->attackedFrom(atkdata, NULL);
		}
	}
	//����ֵ������executeSet��
	return false;
}

bool ActorStrengthExecute::ExecuteSet(ActorAttrib* attrib, float value, bool overflowable, ActorAttribType type)
{
	ACTORATTRIBCHECK(attrib);
	PlayerAttrib* playAttrib = dynamic_cast<PlayerAttrib*>(attrib);
	ACTORATTRIBCHECK(playAttrib);

	const bool isGodMode = g_WorldMgr ? g_WorldMgr->isGodMode() : false;
	float curStrength = playAttrib->getStrength();
	if (isGodMode)
	{
		playAttrib->removeBuff(93);
		return true;
	}
	if (!playAttrib->m_bUseCompatibleStrength || playAttrib->m_StrengthFoodShowState != SFS_Strength)
	{
		playAttrib->removeBuff(93);
		return true;
	}
	if (value <= 0)
	{
		value = 0;
	}
	float limit = playAttrib->getMaxStrength();

	if (value >= limit)
	{
		value = limit;
	}
	if (Abs(value - curStrength) < 0.0001f)
	{
		return true;
	}
	const int old = int(curStrength);
	curStrength = value;

	// playAttrib->setStrengthOnlyInform(curStrength);
	return true;
}


/***************************************ActorThirstExecute*******************************************************************/

bool ActorThirstExecute::ExecuteAdd(ActorAttrib* attrib, float& strength, bool overflowable, ActorAttribType type)
{
	if (strength > 0)
	{
		return false;
	}
	ACTORATTRIBCHECK(attrib);
	PlayerAttrib* playAttrib = dynamic_cast<PlayerAttrib*>(attrib);
	ACTORATTRIBCHECK(playAttrib);
	ClientActor* actor = attrib->getOwnerActor();
	std::vector<StatusAttInfo> vValue;
	playAttrib->getStatusAddAttInfo(BUFFATTRT_CONSUME_STRENGTH_DAMAGE, vValue);

	if (actor && vValue.size() > 0)
	{
		float curHp = attrib->getHP();
		//Ѫ�����ήΪ1
		//�ܵ������˺�,������ hp - (hp -1);
		float val = fabs(strength);
		if (curHp + strength < 1.f)
		{
			// ���Ѫ�������ۣ��Ͳ���Ѫ�� code_by:liya
			val = curHp - 1;
		}

		int uiType = 0;
		int valueType = 0;
		auto attackComp = actor->getAttackedComponent();
		for (auto p = vValue.begin(); p != vValue.end(); p++)
		{
			uiType = p->vValue[0].iType;
			auto enumdef = GetDefManagerProxy()->getBuffEffectEnumDef((int)(p->vValue[0].value));
			int iAtkType = ATTACK_PUNCH;
			if (enumdef)
			{
				iAtkType = enumdef->AttType - BuffAttrType::BUFFATTRT_MELEE_HURT;
				if (enumdef->AttType == BuffAttrType::BUFFATTRT_FIXED_HURT)
				{
					iAtkType = ATTACK_TYPE::ATTACK_FIXED;
				}
				if (enumdef->AttType == BuffAttrType::BUFFATTRT_TRUE_DAMAGE)
				{
					iAtkType = ATTACK_TYPE::TRUE_DAMAGE;

				}
				if (enumdef->AttType == BuffAttrType::BUFFATTRT_ICE_PROPERTY) {
					iAtkType = ATTACK_TYPE::ATTACK_ICE;
				}
			}
			if (val <= std::numeric_limits<float>::epsilon())
			{
				continue;
			}
			OneAttackData atkdata;
			//memset(&atkdata, 0, sizeof(atkdata));
			atkdata.damage_armor = false;
			// ���˺�����ϵͳ code-by:liya
			if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate && ((iAtkType >= ATTACK_PUNCH && iAtkType <= MAX_MAGIC_ATTACK) || iAtkType == PHYSICS_ATTACK))
			{
				const int index = AtkType2ArmorIndex((ATTACK_TYPE)iAtkType);
				atkdata.atkTypeNew = (1 << index);
				if (iAtkType == ATTACK_EXPLODE)
				{
					atkdata.explodePoints[0] = val;
				}
				else
				{
					atkdata.atkPointsNew[index] = val;
				}
			}
			else
			{
				atkdata.atktype = (ATTACK_TYPE)iAtkType;
				atkdata.atkpoints = val;
			}
			atkdata.fromplayer = NULL;
			attackComp->attackedFrom(atkdata, NULL);
		}
	}
	//����ֵ������executeSet��
	return false;
}

bool ActorThirstExecute::ExecuteSet(ActorAttrib* attrib, float value, bool overflowable, ActorAttribType type)
{
	ACTORATTRIBCHECK(attrib);
	PlayerAttrib* playAttrib = dynamic_cast<PlayerAttrib*>(attrib);
	ACTORATTRIBCHECK(playAttrib);

	const bool isGodMode = g_WorldMgr ? g_WorldMgr->isGodMode() : false;
	float curThirst = playAttrib->getThirst();
	if (isGodMode)
	{
		playAttrib->removeBuff(113);
		return true;
	}
	//if (!playAttrib->m_bUseCompatibleStrength || playAttrib->m_StrengthFoodShowState != SFS_Strength)
	//{
	//	playAttrib->removeBuff(93);
	//	return true;
	//}
	if (value <= 0)
	{
		value = 0;
	}
	float limit = playAttrib->getMaxThirst() ;

	if (value >= limit)
	{
		value = limit;
	}
	if (Abs(value - curThirst) < 0.0001f)
	{
		return true;
	}
	const int old = int(curThirst);
	curThirst = value;

	playAttrib->setThirstOnlyInform(curThirst);
	return true;
}


/***************************************ActirPerseveranceExecute*******************************************************************/

bool ActirPerseveranceExecute::ExecuteAdd(ActorAttrib* attrib, float& val, bool overflowable, ActorAttribType type)
{
	ACTORATTRIBCHECK(attrib);
	float diff = 0.f;
	switch (type)
	{
	case ActorAttribType_Strength:
		if (val >= 0)
		{
			return false;
		}
		if (m_value <= 0)
		{
			return false;
		}
		//����val���Ǹ�ֵ
		diff = val + m_value;
		val += m_value;
		if (fabs(diff) < 1)
		{
			//todo�д����۹���
			val = 0.f;
			return ExecuteSet(attrib, m_minValue, overflowable, ActorAttribType_Perseverance);
		}
		else if (diff < 0.f)
		{
			ExecuteSet(attrib, m_minValue, overflowable, ActorAttribType_Perseverance);
			return false;
		}
		else
		{
			val = 0.f;
			return ExecuteSet(attrib, diff, overflowable, ActorAttribType_Perseverance);
		}
	case ActorAttribType_Perseverance:
		return ExecuteSet(attrib, m_value + val, overflowable, ActorAttribType_Perseverance);
	default:
		//nothing
		;
	}
	return false;
}

bool ActirPerseveranceExecute::ExecuteSet(ActorAttrib* attrib, float val, bool overflowable, ActorAttribType type)
{
	ACTORATTRIBCHECK(attrib);
	switch (type)
	{
	case ActorAttribType_Strength:
		return false;
	case ActorAttribType_Perseverance:
	{
		int old = m_value;
		setValue(val);
		//���������ֱ仯ʱ�·�֪ͨ
		ClientActor* actor = attrib->getOwnerActor();
		if (actor)
		{
			actor->syncAttr(ATTRT_PERSEVERANCE, m_value);
			if (actor == g_pPlayerCtrl && int(m_value) != old)
			{
				//GameEventQue::GetInstance().postPlayerAttrChange();
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
			}
		}
		return true;
	}
	default:
		//nothing
		;
	}
	return false;
}

void ActirPerseveranceExecute::setValue(float val)
{
	m_value = val;
	if (m_value > m_maxValue)
	{
		m_value = m_maxValue;
	}
	if (m_value < m_minValue)
	{
		m_value = m_minValue;
	}
}