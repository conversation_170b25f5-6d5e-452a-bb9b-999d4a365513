#ifndef __TO_ATTACK_TARGET_COMPONENT_H__
#define __TO_ATTACK_TARGET_COMPONENT_H__

#include "BaseTargetComponent.h"
#include "SandboxGame.h"

class ClientActor;
class IClientPlayer;
class IClientActor;
class EXPORT_SANDBOXGAME ToAttackTargetComponent;
class ToAttackTargetComponent : public BaseTargetComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(ToAttackTargetComponent)

	//tolua_begin
	ToAttackTargetComponent();
	~ToAttackTargetComponent();

	void updateFaceToTarget(float yawRange, float pitchRange);

	virtual ClientActor* getTarget() override;
	WORLD_ID getTargetId();
	//tolua_end

	void CreateEvent2();
	void DestroyEvent2();
private:
	MNSandbox::AutoRef<MNSandbox::Listener<IClientPlayer*>> m_listenerToAttack1;
	MNSandbox::AutoRef<MNSandbox::Listener<IClientActor*&>> m_listenerToAttack2;
}; //tolua_exports

#endif