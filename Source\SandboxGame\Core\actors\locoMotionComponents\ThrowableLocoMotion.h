#ifndef __THROWA_BLE_LOCOMOTION_H__
#define __THROWA_BLE_LOCOMOTION_H__

#include "ActorLocoMotion.h"

class ThrowableLocoMotion : public ActorLocoMotion
{
public:
	DECLARE_COMPONENTCLASS(ThrowableLocoMotion)

	ThrowableLocoMotion();
	virtual ~ThrowableLocoMotion()
	{

	}

	virtual void tick();
	virtual void setThrowableHeading(const Rainbow::Vector3f &dir, float vel, float deviation);

private:
	void tickInAir();
	void tickInGround();

public:
	bool m_InGround;
	int m_TicksInAir;
	int m_TicksInGround;
	int m_InBlockID;
	int m_InBlockData;
	WCoord m_BlockPos;

	float m_Gravity;
};
#endif