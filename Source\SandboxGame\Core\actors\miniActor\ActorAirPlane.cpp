
#include "ActorAirPlane.h"
#include "BlockMaterialMgr.h"
#include "special_blockid.h"
#include "world.h"
#include "ClientActorLiving.h"
#include "LivingLocoMotion.h"
#include "ClientPlayer.h"
#include "ClientActorManager.h"
#include "OgrePrerequisites.h"
#include "DefManagerProxy.h"
#include "ObserverEventManager.h"
#include "Entity/OgreModel.h"
//#include "OgreGameScene.h"
#include "WorldManager.h"
#include "BlockScene.h"
#include "BoatLocomotion.h"
#include "SandboxListener.h"
#include "RiddenComponent.h"
#include "TriggerComponent.h"
#include "DropItemComponent.h"
#include "SwarmComponent.h"
#include "PlayerControl.h"
#include "ActorBody.h"
#include "ActorAttrib.h"
#include "SandBoxManager.h"
#include "ClientMob.h"
#include "UGCEntity.h"
#include "UGCModelLoader.h"
#include "SandboxGameDef.h"
#include "UgcAssetHeader.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

 
IMPLEMENT_SCENEOBJECTCLASS(ActorAirPlane)
ActorAirPlane::ActorAirPlane() : m_ItemID(0), m_Model(NULL)
{
	createEvent();
 	getLocoMotion()->setBound(60, 150);
	getLocoMotion()->m_yOffset = getLocoMotion()->m_BoundHeight/2;

}

ActorAirPlane::~ActorAirPlane()
{
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_Model);
}

void ActorAirPlane::init(int itemid)
{
	float scale = 2.0f;
	m_ItemID = itemid;
	 
#ifndef IWORLD_SERVER_BUILD
	if (itemid == 14519) {
		m_Model = g_BlockMtlMgr.getModel("entity/110093/body.omod");
		const MonsterDef *def = GetDefManagerProxy()->getMonsterDef(itemid-10000);
		if(def)
		{
			getLocoMotion()->m_HitBoundHeight	= (int)(def->HitHeight*def->ModelScale);
			getLocoMotion()->m_HitBoundWidth	= (int)(def->HitWidth*def->ModelScale );
			getLocoMotion()->m_BoundSize		= (int)(def->Width*def->ModelScale    );
			getLocoMotion()->m_BoundHeight		= (int)(def->Height*def->ModelScale   );
		}

	}
	else if(itemid == 11000122)
	{
		UGCEntity* ugcEntity = UGCEntity::Create();
			ugcEntity->LoadModelAsync("ugcModel/11000122/body.obj", UgcAssetType::OBJ, [this](bool success, UGCModelLoader* modelLoader)->void
				{
					if (success && modelLoader)
					{
						Rainbow::Model* model = modelLoader->GetModel();
						if (model)
						{
							m_Model = model;
						}
					}
				});
			scale = 1000.0f;
			{
				getLocoMotion()->m_HitBoundHeight	= (int)(0.33 * scale);
				getLocoMotion()->m_HitBoundWidth	= (int)(1.0 *   scale);
				getLocoMotion()->m_BoundSize		= (int)(1.0 *   scale);
				getLocoMotion()->m_BoundHeight		= (int)(0.33 *   scale);
				getLocoMotion()->m_yOffset = getLocoMotion()->m_BoundHeight / 2;
			}
	}
	

	if (!m_Model)
		return;
	
	// 设置模型整体缩放
    m_Model->SetScale(Vector3f(scale, scale, scale));
    
#else
	if(itemid == 14519)
	{
		const MonsterDef *def = GetDefManagerProxy()->getMonsterDef(itemid-10000);
		if(def)
		{
			getLocoMotion()->m_HitBoundHeight = def->HitHeight*def->ModelScale;
			getLocoMotion()->m_HitBoundWidth = def->HitWidth*def->ModelScale;
			getLocoMotion()->m_BoundSize = def->Width*def->ModelScale;
			getLocoMotion()->m_BoundHeight = def->Height*def->ModelScale;
		}
	}
	else if(itemid == 11000122)
	{
		getLocoMotion()->m_HitBoundHeight = 0.33 * scale;
		getLocoMotion()->m_HitBoundWidth = 1.0 * scale;
		getLocoMotion()->m_BoundSize = 1.0 * scale;
		getLocoMotion()->m_BoundHeight = 0.33 * scale;
		getLocoMotion()->m_yOffset = getLocoMotion()->m_BoundHeight / 2;
	}
	
	m_Model = NULL;
#endif
}

void ActorAirPlane::createEvent()
{

}

flatbuffers::Offset<FBSave::SectionActor> ActorAirPlane::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	 

	auto actor = FBSave::CreateActorAirPlane(builder, basedata, m_ItemID);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorAirPlane, actor.Union());
}

bool ActorAirPlane::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorAirPlane *>(srcdata);
	loadActorCommon(src->basedata());

	init(src->itemid());

	return true;
}

ActorAirPlane *ActorAirPlane::create(int itemid, World *pworld, int x, int y, int z)
{
	ActorAirPlane *plane = SANDBOX_NEW(ActorAirPlane);
	plane->init(itemid);
	if (!pworld) return NULL;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return NULL;
	actorMgr->spawnActor(plane, x, y, z, 0.0f, 0.0f);
	return plane;
}

bool ActorAirPlane::interact(ClientActor *player, bool onshift/* =false */, bool isMobile)
{
	return false;
}



void ActorAirPlane::tick()
{
	ClientActor::tick();

	/*if (getPosition().y < -64 * BLOCK_SIZE)
	{
		kill();
	}
	*/

	if(!m_pWorld->isRemoteMode())//服务器执行
	{
		std::vector<IClientActor *>actors;
		CollideAABB bbox;
		getCollideBox(bbox);
		bbox.expand(20, 0, 20);
		m_pWorld->getActorsInBoxExclude(actors, bbox, this);
		for(size_t i=0; i<actors.size(); i++)
		{
			ClientActor* actor = actors[i]->GetActor();
			
			if (actor->canBePushed() && dynamic_cast<ActorAirPlane *>(actor) != NULL)
			{
				actor->applyActorCollision(this);
			}
		}

		WCoord pos = getPosition();
		for(int i=0; i<4; i++)
		{
			int x = CoordDivBlock(pos.x + (i%2)*80 - 40);
			int z = CoordDivBlock(pos.z + (i/2)*80 - 40);

			for(int j=0; j<2; j++)
			{
				int y = CoordDivBlock(pos.y) + j;
				int blockid = m_pWorld->getBlockID(x, y, z);

				if(blockid == BLOCK_SNOW)
				{
					m_pWorld->setBlockAir(WCoord(x,y,z));
				}
				/*
				else if(blockid == BLOCK_WATER)
				{
					m_pWorld->destroyBlock(x, y, z, true);
				}*/
			}
		}
	}
}

void ActorAirPlane::update(float dtime)
{
	ClientActor::update(dtime);

	if(m_Model)
	{
		m_Model->SetPosition(getLocoMotion()->getFramePosition());

		float t = getLocoMotion()->m_TickPosition.m_TickOffsetTime/GAME_TICK_TIME;
		float yaw = Rainbow::Lerp(getLocoMotion()->m_PrevRotateYaw, getLocoMotion()->m_RotateYaw, t);
		float pitch = Rainbow::Lerp(getLocoMotion()->m_PrevRotatePitch, getLocoMotion()->m_RotationPitch, t);

		m_Model->SetRotation(yaw, -pitch, 0.0f);

		m_Model->UpdateTick(TimeToTick(dtime));
	}
}

int ActorAirPlane::getObjType() const
{
	return OBJ_TYPE_AIR_PLANE;
}

void ActorAirPlane::enterWorld(World* pworld)
{
	ClientActor::enterWorld(pworld);
	if (m_pWorld && m_Model)
	{
		if (m_pWorld->getScene())
			m_pWorld->getScene()->AddGameObject(m_Model->GetGameObject());
	}
}

void ActorAirPlane::leaveWorld(bool keep_inchunk)
{
	ClientActor::leaveWorld(keep_inchunk);
	if (m_Model)
	{
		m_Model->DetachFromScene();
	}
}

 