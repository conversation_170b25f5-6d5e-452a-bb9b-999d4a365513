#ifndef __TOTEM_COMPONENT_H__
#define __TOTEM_COMPONENT_H__

#include "ActorComponent_Base.h"

class TotemComponent : public ActorComponentBase//tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(TotemComponent)

	//tolua_begin
	TotemComponent();
	virtual void OnTick() override;
	//tolua_end

	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
protected:
	bool isInsideTotem();
	bool isNeedAddOxygenBuff();
	bool hasActivatedFurnaceOxy();	//是否有激活的氧气提炼装置.
	int m_TotemTicks;					//用来找图腾	
	bool m_ReportLock;
}; //tolua_exports

#endif