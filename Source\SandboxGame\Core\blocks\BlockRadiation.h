#ifndef __RADIATION_BLOCK_H__
#define __RADIATION_BLOCK_H__

#include "BlockMaterial.h"
#include "OgreWCoord.h"

/**
 * 辐射方块
 * 具有辐射值属性，可以对周围环境产生辐射影响
 */
class BlockRadiation : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
    DECLARE_BLOCKMATERIAL(BlockRadiation)
    
public:
    //tolua_begin
    BlockRadiation();
    virtual ~BlockRadiation();
    
    // 基础功能实现
    virtual void init(int resid) override;
    virtual void blockTick(World* pworld, const WCoord& blockpos) override;
    virtual void onBlockAdded(World* pworld, const WCoord& blockpos) override;
    virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
    virtual void onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *bywho) override;
    virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint = Rainbow::Vector3f(0, 0, 0)) override;
    virtual void onPlayRandEffect(World *pworld, const WCoord &blockpos) override;
    virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
    virtual unsigned char getBlockLightSrc(int iBlockData = 0) override;
    
    // 更新辐射效果
    void UpdateRadiationEffect(World* pworld, const WCoord& blockpos);
    
    // 启用/关闭辐射源
    void EnableRadiation(World* pworld, const WCoord& blockpos);
    void DisableRadiation(World* pworld, const WCoord& blockpos);
    void ToggleRadiation(World* pworld, const WCoord& blockpos);
    
    // 检查环境条件
    bool CanRadiateInEnvironment(World* pworld, const WCoord& blockpos);
    
    // 静态方法
    static void StopRadiation(World* pworld, const WCoord& blockpos);
    static void StopRadiationEffect(World* pworld, const WCoord& effectpos);

    //tolua_end
    
protected:
    virtual void initGeomName() override;
    virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world) override;
    virtual int getProtoBlockGeomID(int *idbuf, int *dirbuf) override;
    
    bool RadiationEnabledGet(bool& value) const;
    void RadiationEnabledSet(const bool& value);
    
    // 更新辐射管理器中的信息
    void RegisterWithRadiationManager(World* pworld, const WCoord& blockpos, int radiation, float decay);
    void UnregisterFromRadiationManager(World* pworld, const WCoord& blockpos);
    
private:
    int m_RadiationSourceId;   // 在辐射管理器中的源ID
    bool m_RadiationEnabled;   // 辐射是否启用
    int m_TickInterval;        // Tick间隔
    
public:
    DECLARE_BLOCKMATERIAL_INSTANCE_BEGIN(BlockRadiation)
        DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Radiation, float)
        DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_RadiationRadius, int)
        DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_RadiationEnabled, bool)
        
        float GetRadiationValue() const;
        void SetRadiationValue(const float& value);
        int GetRadiationRadiusValue() const;
        void SetRadiationRadiusValue(const int& value);
        bool GetRadiationEnabledValue() const;
        void SetRadiationEnabledValue(const bool& value);
private:
    float m_Radiation;         // 辐射值
    int m_RadiationRadius;     // 辐射影响半径
    bool m_RadiationEnabled;   // 辐射是否启用

    DECLARE_BLOCKMATERIAL_INSTANCE_END(BlockRadiation)
}; //tolua_exports

#endif // __RADIATION_BLOCK_H__ 