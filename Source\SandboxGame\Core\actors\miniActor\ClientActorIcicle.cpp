#include "ClientActorIcicle.h"
#include "world.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "ClientItem.h"
#include "ModelItemMesh.h"
#include "world.h"
#include "BlockScene.h"
#include "SandboxIdDef.h"
#include "ClientActorManager.h"
#include "ProjectileLocoMotion.h"
#include "BlockMaterialMgr.h"
#include "SandboxCoreSubsystem.h"
#include "SoundComponent.h"
#include "DropItemComponent.h"
#include "DefManagerProxy.h"
#include "ClientActorHelper.h"
#include "Math/Quaternionf.h"
#include "SandBoxManager.h"
#include "ActorBody.h"
#include "WorldManager.h"
#include "ActorManager.h"
using namespace MINIW;


class IcicleLocoMotion : public ProjectileLocoMotion
{
	DECLARE_COMPONENTCLASS(IcicleLocoMotion)
public:
	IcicleLocoMotion();
	virtual void tick();
};

IMPLEMENT_COMPONENTCLASS(IcicleLocoMotion)

IcicleLocoMotion::IcicleLocoMotion() : ProjectileLocoMotion()
{
	//setBound(25, 25);
	//m_yOffset = m_BoundHeight / 2;
}

void IcicleLocoMotion::tick()
{
	ClientActorIcicle* projectile = dynamic_cast<ClientActorIcicle*>(GetOwnerActor());
	if (projectile == nullptr) {
		return;
	}
	if (!GetOwner()) return;
	if (GetOwnerActor()->needClear()) return;
	if (m_pWorld == NULL) return;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return ;
	//����
	ActorLocoMotion::tick();
	if (m_pWorld->isRemoteMode())
	{
		m_RotateYaw = syncYaw;
		m_RotationPitch = syncPitch;

		if (m_InterplateStep > 0)
		{
			m_Position = m_Position + (syncPos - m_Position) / m_InterplateStep;
			m_InterplateStep--;
		}
		else
		{
			m_Position = syncPos;
		}
	}
	else
	{
		WCoord mvec = getIntegerMotion(m_Motion);
		if (mvec.x == 0 && mvec.y == 0 && mvec.z == 0)
		{
			m_Motion *= (1 - 0.2f);
			m_Motion.y -= m_Gravity;
			return;
		}
		MINIW::WorldRay ray;
		ActorExcludes excludes;
		IntersectResult inter_result1, inter_result2;
		IntersectResult* presult;
		int intertype = 0;
		if (projectile->m_ProjectileDef)
		{
			if (intertype == 0)
			{
				intertype = projectilePickAll(mvec, ray, &inter_result2, excludes, PICK_METHOD_SOLID);
				presult = &inter_result2;
			}
		}
		if (intertype == 1)
		{
			//����ƫ��
			Rainbow::Vector3f targetpos = ray.m_Origin.toVector3() + ray.m_Dir * presult->collide_t;
			mvec = Rainbow::Vector3f::zero;
			m_Position = WCoord(targetpos);
			WCoord blockPos = presult->block;

			auto material = g_BlockMtlMgr.getMaterial(m_pWorld->getBlockID(blockPos));
			projectile->onImpactWithBlock(&blockPos, presult->face);
			//if (material && material->EXEC_USEMODULE(OnActorCollidedWithBlock, m_pWorld, blockPos, m_OwnerActor))
			if (material && material->onActorCollidedWithBlock(m_pWorld, blockPos, GetOwnerActor()))
			{
				m_owner->clearCollideBlock();
				m_owner->setCollideBlockState(blockPos, m_InBlockID);
			}
			else
			{
				m_owner->clearCollideBlock();
			}
		}
		else if (intertype == 2)
		{
			//����ƫ��
			Rainbow::Vector3f targetpos = ray.m_Origin.toVector3() + ray.m_Dir * presult->collide_t;
			//ͬһ��team��actor������onImpactWithActor
			//const ProjectileDef* projectileDef = ProjectileDefCsv::getInstance()->get(projectile->GetItemId());

			//projectile->m_TriggerPos = inter_result.actor->getEyePosition().toVector3();
			ClientActor* owner = projectile->getShootingActor();
			if (owner)
			{
				WCoord blockPos = CoordDivBlock(presult->actor->getPosition());
				blockPos.y -= 1;
				SandBoxManager::getSingleton().DoEvent(SandBoxMgrEventID::EVENT_PROJECTILE_COLLIDE_BLOCK, 0, owner->getObjId() & 0xffffffff, (char*)&blockPos, sizeof(blockPos));
			}
			m_Position = targetpos;
			projectile->onImpactWithActor(presult->actor->GetActor(), "");
		}

		m_Motion.y -=  5;
		setNoClip(pushOutOfBlocks(m_Position));

		WCoord prevpos = m_Position;
		doMoveStep(m_Motion);


	}
}


ClientActorIcicle::ClientActorIcicle()
{
	m_dropBlockNum = 0;
	m_StartPos = WCoord(0, 0, 0);
	CreateComponent<IcicleLocoMotion>("CoconutLocoMotion");
}
ClientActorIcicle::~ClientActorIcicle()
{

}

flatbuffers::Offset<FBSave::SectionActor> ClientActorIcicle::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveActorCommon(builder);
	ProjectileLocoMotion* loco = static_cast<ProjectileLocoMotion*>(getLocoMotion());
	auto quat = QuaternionToSave(loco->m_RotateQuat);
	auto obj = FBSave::CreateActorIcicle(builder, basedata, m_ShootingActorID, m_ItemID, &quat);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorIcicle, obj.Union());
}

bool ClientActorIcicle::load(const void* srcdata, int version)
{
	m_LiveTicks = 0;
	auto src = reinterpret_cast<const FBSave::ActorThornBall*>(srcdata);
	init(src->itemid());
	loadActorCommon(src->basedata());
	m_ShootingActorID = src->shooter();
	playMotion(m_ProjectileDef->TailEffect);
	IcicleLocoMotion* loco = dynamic_cast<IcicleLocoMotion*>(getLocoMotion());
	if (loco)
	{
		loco->syncPos = getLocoMotion()->getPosition();
		if (src->rotatequat())
			loco->m_RotateQuat = QuaternionFromSave(src->rotatequat());
		else
		{
			loco->m_RotateQuat = Rainbow::AngleEulerToQuaternionf(Rainbow::Vector3f(loco->m_RotationPitch, loco->m_RotateYaw, 0));
		}

		loco->m_CanTrigger = false;
		if (loco->needFullRotation())
		{
			m_ServerYawCmp = loco->m_RotateQuat.ToUInt32();
		}
		else
		{
			loco->syncYaw = loco->m_RotateYaw;
			loco->syncPitch = loco->m_RotationPitch;
		}
	}
	return true;
}

void ClientActorIcicle::init(int itemid, ClientActor* shooter)
{
	ClientActorProjectile::init(itemid, shooter);
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion*>(getLocoMotion());
	if (loco)
		loco->syncPos = getLocoMotion()->getPosition();
	loco->m_yOffset = 0;
	int  n = m_dropBlockNum;
	loco->setBound(100, 100);
	loco->setNoClip(false);
}


void ClientActorIcicle::onImpactWithActor(ClientActor* actor, const std::string& partname)
{
	doImpactActor(actor);
}

void ClientActorIcicle::onImpactWithBlock(const WCoord* blockpos, int face)
{
	if (m_ItemID == ITEM_WEAK_ICICLE)
	{
		if (m_pWorld)
		{
			int blockid = m_pWorld->getBlockID(*blockpos);
			const BlockDef* blockdef = GetDefManagerProxy()->getBlockDef(blockid);
			float blockHardness = 1.0f * m_dropBlockNum;
			if (blockHardness > 4.0f)
			{
				blockHardness = 4.0f;
			}
			if (blockdef && (blockdef->Hardness > 0) && (blockdef->Hardness < blockHardness))
			{
				m_pWorld->destroyBlock(*blockpos, BLOCK_MINE_NONE);
			}
		}
	}

	setNeedClearEx();

	doTrigger();
}

void ClientActorIcicle::doImpactActor(ClientActor* actor)
{
	World* pworld = getWorld();
	if (pworld == nullptr || !g_WorldMgr)
	{
		return;
	}
	doAttack(actor);
	setNeedClearEx();
	doTrigger();
}

void ClientActorIcicle::doAttack(ClientActor* actor)
{
	if (m_ProjectileDef == nullptr)
	{
		return;
	}
	//�������
	int fDistance = abs(this->m_StartPos.y - actor->getPosition().y) / BLOCK_SIZE;
	if (fDistance > 60)//������
	{
		fDistance = 60;
	}
	float damage = 5 * m_dropBlockNum * fDistance / 2;
	OneAttackData atkdata;
	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;
	atkdata.atktype = ATTACK_ANVIL;
	atkdata.atkpoints = damage;
	atkdata.fromplayer = nullptr;
	
	do  // �Ʒ�actor->getBody()���±���, �����ж� 2023.11.29 by huanglin
	{
		if (actor->getBody())
		{
			int itemid = actor->getBody()->getCurShowEquipItemId(EQUIP_HEAD);
			auto toolDef = GetDefManagerProxy()->getToolDef(itemid);
			if (toolDef && toolDef->Level >= 5)//װ���ȼ����ڵ����ѺϽ� ˲ʱ����
			{
				atkdata.buffId = m_ProjectileDef->BuffId / 1000;
				atkdata.buffLevel = 3;
				break;
			}
		}

		if (damage >= 10.0f)//�˺����ڵ���10�� ѣ��
		{
			atkdata.buffId = m_ProjectileDef->BuffId / 1000;
			atkdata.buffLevel = 1;
		}
		
	} while (0);
	
	actor->attackedFrom(atkdata, this);
}

void ClientActorIcicle::onCollideWithPlayer(ClientActor* actor)
{
	doImpactActor(actor);
}

ClientActorIcicle* ClientActorIcicle::shootIcicleAuto(int itemid, World* pworld, const WCoord& pos, int num)
{
	if (pworld == nullptr) return nullptr;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return nullptr;

	ClientActorIcicle* projectile = ENG_NEW(ClientActorIcicle)();
	projectile->init(itemid);
	projectile->m_dropBlockNum = num;
	actorMgr->spawnActor(projectile, pos, 0, 0);
	projectile->m_StartPos = projectile->getPosition();

	ProjectileLocoMotion* locomove = static_cast<ProjectileLocoMotion*>(projectile->getLocoMotion());
	float speed = GetDefManagerProxy()->getProjectileDef(itemid)->InitSpeed;
	locomove->setRotatePitch(90);
	projectile->playMotion(projectile->m_ProjectileDef->TailEffect);
	projectile->m_AttackPoints = projectile->m_ProjectileDef->AttackValue;
	if (projectile->m_ProjectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}
	return projectile;
}