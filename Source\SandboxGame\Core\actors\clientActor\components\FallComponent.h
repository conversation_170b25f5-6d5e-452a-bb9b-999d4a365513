#ifndef __FALL_COMPONENT_H__
#define __FALL_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "SandboxGame.h"

class ClientActor;
class EXPORT_SANDBOXGAME FallComponent;
class FallComponent
{ 
public:
	static float getFallHurtSubtract_Base();
	static float getFallHurtRate_Base();
	static void calFallMotion_Base(ClientActor* pActor, float motiony, bool onground);
	static void fall_Base(ClientActor* pActor, float fallh);

	static float getFallHurtSubtract(ClientActor* pActor);
	static float getFallHurtRate(ClientActor* pActor);
	static void calFallMotion(ClientActor* pActor, float motiony, bool onground);
	static void fall(ClientActor* pActor, float fallh);
}; 
#endif