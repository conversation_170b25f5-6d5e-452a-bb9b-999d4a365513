#pragma once

#include "BlockMesh.h"
#include "ClientActorProjectile.h"

class ClientActorImpulse :public ClientActorProjectile //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ClientActorImpulse();
	flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	bool load(const void *srcdata, int version) override;
	void onImpactWithActor(ClientActor *actor, const std::string& partname);
	void onImpactWithBlock(const WCoord *blockpos, int face);
	virtual void onCollideWithPlayer(ClientActor*player) override;
	void tick();
	void update(float dtime);
	void init(int itemid, ClientActor *shooter = nullptr);
	//void onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum);
	ClientPlayer *getShootingPlayer();
	void onAttackActor(ClientActor *actor);
	bool passActor() { return true;}
	virtual bool supportSaveToPB()
	{
		return false;
	}
	static ClientActorImpulse *shootImpulseAuto(int itemid, World *pworld, const WCoord &pos, const Rainbow::Vector3f &dir, float speed, float deviation, long long shooterObjId);
	//tolua_end
protected:
	~ClientActorImpulse();
private:
	int m_nLifeTick;
	int m_nAttackCount;
}; //tolua_exports

