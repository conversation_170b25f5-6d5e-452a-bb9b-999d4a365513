#include "container_coagulation.h"
#include "ThermalSpringManager.h"
#include "EffectManager.h"
#include "ClientActorHelper.h"
#include "BlockMaterialMgr.h"
#include "SandboxIdDef.h"
#include "world.h"
#include "chunk.h"
#include "ClientPlayer.h"
CoagualationContainer::CoagualationContainer() : WorldContainer(WCoord(0, 0, 0), 0), isContactLava(false), m_iTotalTick(0), m_iBlowTick(0), m_iCoreBlock(false), m_CoreBlockPos(WCoord(0, 0, 0)), m_HotMaxHeight(-1)
{
	m_NeedTick = true;
}

CoagualationContainer::CoagualationContainer(const WCoord& blockpos) : WorldContainer(blockpos, 0), isContactLava(false), m_iTotalTick(0), m_iBlowTick(0), m_iCoreBlock(false), m_CoreBlockPos(WCoord(0, 0, 0)), m_HotMaxHeight(-1)
{
	m_HavePlayEffect = false;
	m_NeedTick = true;
}
CoagualationContainer::~CoagualationContainer()
{
}

int CoagualationContainer::getObjType() const
{
	return OBJ_TYPE_COAGULATION;
}

CoagualationContainer* CoagualationContainer::sureContainer(World* pworld, const WCoord& blockpos)
{
	CoagualationContainer* container = dynamic_cast<CoagualationContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (!container)
	{
		container = SANDBOX_NEW(CoagualationContainer, blockpos);
		if (!container) return container;
		Chunk* pchunk = pworld->getChunk(blockpos);
		if (pchunk)
		{
			pchunk->addContainer(container);
			pworld->getContainerMgr()->addContainerByChunk(container);
		}
		else
		{
			SANDBOX_DELETE(container);
		}
	}
	return container;
}

void CoagualationContainer::updateBlockMtl(int data) //刷新黑凝浆块贴图及状态 
{
	if (m_World) {
		//m_World->setBlockData(m_BlockPos, 13);
		for (int i = 0; i < 3; i++) {
			for (int j = 0; j < 3; j++) {
				if (m_World->getBlockID(m_BlockPos + WCoord(-1 + i, 0, 1 - j)) == BLOCK_BLACK_COAGULATION) {
					m_World->setBlockData(m_BlockPos + WCoord(-1 + i, 0, 1 - j), data);
					setCoreBlockPos(m_BlockPos, m_BlockPos + WCoord(-1 + i, 0, 1 - j));
				}
				else
				{
					m_CoreBlockPos = m_BlockPos;
					initBlockMtl();
					break;
				}

			}
		}
		for (int i = 1; i < 3; i++) {
			if (m_World->getBlockID(m_BlockPos + WCoord(-1, i, 0)) == BLOCK_BLACK_COAGULATION) {
				m_World->setBlockData(m_BlockPos + WCoord(-1, i, 0), data);
				setCoreBlockPos(m_BlockPos, m_BlockPos + WCoord(-1, i, 0));
			}
			else
			{
				m_CoreBlockPos = m_BlockPos;
				initBlockMtl();
				break;
			}

			if (m_World->getBlockID(m_BlockPos + WCoord(1, i, 0)) == BLOCK_BLACK_COAGULATION) {
				m_World->setBlockData(m_BlockPos + WCoord(1, i, 0), data);
				setCoreBlockPos(m_BlockPos, m_BlockPos + WCoord(1, i, 0));
			}
			else
			{
				m_CoreBlockPos = m_BlockPos;
				initBlockMtl();
				break;
			}

			if (m_World->getBlockID(m_BlockPos + WCoord(0, i, -1)) == BLOCK_BLACK_COAGULATION) {
				m_World->setBlockData(m_BlockPos + WCoord(0, i, -1), data);
				setCoreBlockPos(m_BlockPos, m_BlockPos + WCoord(0, i, -1));
			}
			else
			{
				m_CoreBlockPos = m_BlockPos;
				initBlockMtl();
				break;
			}

			if (m_World->getBlockID(m_BlockPos + WCoord(0, i, 1)) == BLOCK_BLACK_COAGULATION) {
				m_World->setBlockData(m_BlockPos + WCoord(0, i, 1), data);
				setCoreBlockPos(m_BlockPos, m_BlockPos + WCoord(0, i, 1));
			}
			else
			{
				m_CoreBlockPos = m_BlockPos;
				initBlockMtl();
				break;
			}

		}
	}
}

void CoagualationContainer::initBlockMtl(const WCoord corePos) { //初始化热泉结构的黑凝浆方块
	if (m_World) {
		WCoord CoreBlockPos = m_CoreBlockPos;
		//m_World->setBlockData(m_BlockPos, 13);
		for (int i = 0; i < 3; i++) {
			for (int j = 0; j < 3; j++) {
				if (m_World->getBlockID(CoreBlockPos + WCoord(-1 + i, 0, 1 - j)) == BLOCK_BLACK_COAGULATION) {
					m_World->setBlockData(CoreBlockPos + WCoord(-1 + i, 0, 1 - j), 1);

					setCoreBlockPos(corePos, CoreBlockPos + WCoord(-1 + i, 0, 1 - j));
				}
			}
		}

		for (int i = 1; i < 3; i++) {
			if (m_World->getBlockID(CoreBlockPos + WCoord(-1, i, 0)) == BLOCK_BLACK_COAGULATION) {
				m_World->setBlockData(CoreBlockPos + WCoord(-1, i, 0), 1);

				setCoreBlockPos(corePos, CoreBlockPos + WCoord(-1, i, 0));
			}
			if (m_World->getBlockID(CoreBlockPos + WCoord(1, i, 0)) == BLOCK_BLACK_COAGULATION) {
				m_World->setBlockData(CoreBlockPos + WCoord(1, i, 0), 1);

				setCoreBlockPos(corePos, CoreBlockPos + WCoord(1, i, 0));
			}
			if (m_World->getBlockID(CoreBlockPos + WCoord(0, i, -1)) == BLOCK_BLACK_COAGULATION) {
				m_World->setBlockData(CoreBlockPos + WCoord(0, i, -1), 1);

				setCoreBlockPos(corePos, CoreBlockPos + WCoord(0, i, -1));
			}
			if (m_World->getBlockID(CoreBlockPos + WCoord(0, i, 1)) == BLOCK_BLACK_COAGULATION) {
				m_World->setBlockData(CoreBlockPos + WCoord(0, i, 1), 1);

				setCoreBlockPos(corePos, CoreBlockPos + WCoord(0, i, 1));
			}
		}
	}
	if (corePos == WCoord(0, 0, 0))
	{
		m_iBlowTick = 0;
		m_iCoreBlock = false;
		m_CoreBlockPos = WCoord(0, 0, 0);
	}

}


void CoagualationContainer::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
}

void CoagualationContainer::setCoreBlockPos(const WCoord& CoreBlockPos, const WCoord& blockpos)
{
	CoagualationContainer* container_blow = CoagualationContainer::sureContainer(m_World, blockpos);
	if (container_blow) {
		container_blow->m_CoreBlockPos = CoreBlockPos;
	}
}

bool CoagualationContainer::isWaterBlock(World* pworld, const WCoord& blockpos)
{
	return BlockMaterialMgr::isWater(pworld->getBlockID(blockpos + WCoord(0, 1, 0)));
	//return pworld->getBlockID(blockpos + WCoord(0, 1, 0)) == BLOCK_STILL_WATER || pworld->getBlockID(blockpos + WCoord(0, 1, 0)) == BLOCK_FLOW_WATER;
}

void CoagualationContainer::leaveWorld()
{
	GetThermalSpringMgr().tryRemoveThermalSpring(m_World, m_CoreBlockPos);
	WorldContainer::leaveWorld();
}

void CoagualationContainer::clear()
{
	m_iCoreBlock = false;
	m_iTotalTick = 0;
	m_iBlowTick = 0;
	isContactLava = false;
	m_CoreBlockPos = WCoord(0, 0, 0);
}

void CoagualationContainer::updateTick()
{
	if (m_iCoreBlock == true) { //核心方块刷新周围方块的data
		m_iBlowTick = m_iBlowTick + 1;
		ChunkIndex index(BlockDivSection(m_BlockPos.x), BlockDivSection(m_BlockPos.z));
		ThermalSpringChunkData* springChunk = GetThermalSpringMgr().getThermalSpringChunkData(index.x, index.z);
		if (springChunk == NULL)
		{
			CoagualationContainer* container_blow = dynamic_cast<CoagualationContainer*>(m_World->getContainerMgr()->getContainer(m_BlockPos));
			if (container_blow && GetThermalSpringMgr().isHotSpring(m_World, m_BlockPos) == true)
			{
				GetThermalSpringMgr().tryConsistThermalSpring(m_World, m_BlockPos);
			}

		}
		//m_CoreBlockPos = m_BlockPos;
		if (m_iBlowTick > 0 && m_iBlowTick < 150) {
			updateBlockMtl(2);
		}
		else if (m_iBlowTick >= 150 && m_iBlowTick < 300) {
			updateBlockMtl(3);
		}

		else if (m_iBlowTick >= 300 && m_iBlowTick < 400) {
			updateBlockMtl(4);
			m_CoreBlockPos = m_BlockPos;
			m_HotMaxHeight = 0;
			playEffects();
		}
		else if (m_iBlowTick >= 400) {
			m_iBlowTick = 1;
			updateBlockMtl(2);
			m_HavePlayEffect = false;
			m_HotMaxHeight = -1;
		}
		if (isWaterBlock(m_World, m_CoreBlockPos) == false)
		{
			GetThermalSpringMgr().tryRemoveThermalSpring(m_World, m_CoreBlockPos);
		}
	}
	if (m_World->getBlockID(m_BlockPos) == BLOCK_BLACK_COAGULATION && m_CoreBlockPos == WCoord(0, 0, 0) && !m_World->isRemoteMode())
	{
		m_World->setBlockData(m_BlockPos, 1);
	}
	if (m_World->getBlockID(m_BlockPos) == BLOCK_BLACK_COAGULATION && m_CoreBlockPos != WCoord(0, 0, 0) && !m_World->isRemoteMode())
	{
		CoagualationContainer* container_blow = dynamic_cast<CoagualationContainer*>(m_World->getContainerMgr()->getContainer(m_CoreBlockPos));
		if (container_blow)
		{
			if (container_blow->m_iCoreBlock == false)
			{
				m_World->setBlockData(m_BlockPos, 1);
			}
		}
		if (!container_blow)
		{
			m_CoreBlockPos = WCoord(0, 0, 0);
			m_World->setBlockData(m_BlockPos, 1);
		}
	}
	//if (m_CoreBlockPos!= WCoord(0, 0, 0))
	//{
	//	CoagualationContainer* container_blow = dynamic_cast<CoagualationContainer*>(m_World->getContainerMgr()->getContainer(m_CoreBlockPos));
	//	if (container_blow&&container_blow->m_iCoreBlock == false)
	//	{
	//		m_CoreBlockPos = WCoord(0, 0, 0);
	//		m_World->setBlockData(m_BlockPos, 1);
	//	}
	//	else if (!container_blow)
	//	{
	//		m_CoreBlockPos = WCoord(0, 0, 0);
	//		m_World->setBlockData(m_BlockPos, 1);
	//	}
	//}

}

flatbuffers::Offset<FBSave::ChunkContainer> CoagualationContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);
	const FBSave::Coord3 pos = WCoordToCoord3(m_CoreBlockPos);
	auto containerdata = FBSave::CreateContainerCoagulation(builder, basedata, isContactLava, m_iTotalTick, 0, 0, 0, &pos, m_iBlowTick, m_HotMaxHeight);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerCoagulation, containerdata.Union());
}

bool CoagualationContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerCoagulation*>(srcdata);
	loadContainerCommon(src->basedata());
	isContactLava = src->isContactLava();
	m_iTotalTick = src->tick();
	if (src->corePos() != NULL)
	{
		m_CoreBlockPos = Coord3ToWCoord(src->corePos());
	}
	if (m_CoreBlockPos == m_BlockPos)
	{
		m_iBlowTick = src->blowtick();
		m_HotMaxHeight = src->hotmaxheight();
		m_iCoreBlock = true;
		GetThermalSpringMgr().addNewThermalSpring(m_BlockPos, this, true);
	}

	if (!isContactLava && m_CoreBlockPos == WCoord(0, 0, 0) && !m_iCoreBlock)
	{
		return false;
	}

	return true;
}
void CoagualationContainer::playEffects()
{
	if (m_iCoreBlock && m_HavePlayEffect == false)
	{
		WCoord waterBlockPos = m_BlockPos;
		int i = 1;
		int id = 0;
		for (; i < 5; i++)
		{
			waterBlockPos += WCoord(0, 1, 0);
			id = m_World->getBlockID(waterBlockPos);
			if (!BlockMaterialMgr::isWater(id))/*(id != BLOCK_STILL_WATER && id != BLOCK_FLOW_WATER)*/
			{
				break;
			}
			m_HavePlayEffect = true;
			
			m_World->getEffectMgr()->playParticleEffectAsync("particles/10021.ent", BlockCenterCoord(waterBlockPos), 100, 0, 0, true);
			m_World->getEffectMgr()->playSound(waterBlockPos * BLOCK_FSIZE, "env.boiling_water", 1.0f, 1.0f);
		}
		m_HotMaxHeight = i - 1;
	}
}

bool CoagualationContainer::isInHotZone(const WCoord& blockpos)
{
	if (m_iBlowTick >= 300 && m_iBlowTick < 400)
	{
		if (m_HotMaxHeight >= 0)
		{
			if (blockpos.x == m_BlockPos.x && blockpos.z == m_BlockPos.z)
			{
				if (blockpos.y >= m_BlockPos.y + 1 && blockpos.y <= m_BlockPos.y + m_HotMaxHeight + 1)
				{
					return true;
				}
			}
		}
	}
	return false;
}
