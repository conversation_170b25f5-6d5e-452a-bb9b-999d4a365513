﻿#ifndef __CLIENTAQUATIC_COMPONENT_H__
#define __CLIENTAQUATIC_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "ActorLocoMotion.h"
#include "ClientMob.h"
class ClientMob;

class ClientAquaticComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(ClientAquaticComponent)

	ClientAquaticComponent();
	~ClientAquaticComponent();

	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void tick();
	virtual bool init();
	int getWaterUseInterval();
	virtual ActorLocoMotion* newLocoMotion();
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder, flatbuffers::Offset<FBSave::ActorMob>& mobdata);
	virtual int saveToPB(PB_GeneralEnterAOIHC* pb);
	virtual int LoadFromPB(const PB_GeneralEnterAOIHC& pb);

	virtual void applyActorCollision(ClientActor* actor);
	virtual bool load(const void* srcdata, int version);
	virtual bool canDespawn();
	virtual void onClear();
	virtual void onDie();
	virtual void moveToPosition(const WCoord& pos, float yaw, float pitch, int interpol_ticks);
	virtual bool attackedFrom(OneAttackData& atkdata, ClientActor* attacker);
	float getSwimSpeed();
	ClientPlayer* selectNearPlayer(int range, int height);
	void actorElasticCollision(ClientActor* actor);
	void emitSpawnSharkPoint();

	int shark_source;//鲨鱼来源
	int m_DroughtTolerance;
	int m_fromAttackUin;
protected:
	//ClientMob* m_owner;


};//tolua_exports

#endif