#include "SunHurtComponent.h"
#include "SandboxEventDispatcherManager.h"
#include "ClientActor.h"
#include "world.h"
#include "ActorLocoMotion.h"
#include "SandboxSchedulerManager.h"
#include "RiddenComponent.h"
#include "EffectComponent.h"
using namespace MNSandbox;


IMPLEMENT_COMPONENTCLASS(SunHurtComponent)

SunHurtComponent::SunHurtComponent(bool canPlaySunHurtEffet)
:m_canPlaySunHurtEffet(canPlaySunHurtEffet)
,m_SunHurt(false)
{
	m_SunFleeTick = 50 + (int)(GenRandomFloat() * 50);
}

void SunHurtComponent::OnTick()
{
	if(m_SunHurt) 
		updateSunHurt();
}

void SunHurtComponent::updateSunHurt()
{
	if (!GetOwner()) return ;
	ClientActor* owner = GetOwner()->ToCast<ClientActor>();
	if (!owner) return ;

	World* pWorld = owner->getWorld();
	ActorLocoMotion* locoMotion = owner->getLocoMotion();
	if(owner->needClear() || !pWorld || !pWorld->isDaytime() || !locoMotion){
		return;
	}

	float bright = locoMotion->getBrightness();
	WCoord blockpos = CoordDivBlock(locoMotion->getPosition());
	if (bright>0.5f && pWorld->canBlockSeeTheSky(blockpos.x, blockpos.y, blockpos.z))
	{
		--m_SunFleeTick;
		if (m_SunFleeTick < 0)
		{
			//attackedFromType(ATTACK_SUN, 0);
			auto RidComp = owner->getRiddenComponent();
			if (RidComp && RidComp->isRiding())
			{
				RidComp->mountActor(NULL);
			}
			owner->setNeedClear(40);
			if(m_canPlaySunHurtEffet)
			{
				auto effectComponent = owner->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect(BODYFX_DISAPPEAR);
				}
				
			}
		}
	}
}

void SunHurtComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	//add
	owner->Event().SubscribeEventWithCreateEvent("sunhurt_set", this, (SandboxClassCallback)&SunHurtComponent::OnSetSunHurt, "sunhurtComp_1");
	owner->Event().SubscribeEventWithCreateEvent("sunhurt_get", this, (SandboxClassCallback)&SunHurtComponent::OnGetSunHurt, "sunhurtComp_2");

	Super::OnEnterOwner(owner);

	BindOnTick();
}

void SunHurtComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	SchedulerMgr().DestroySchedulerByName("sunhurtComp_1");
	SchedulerMgr().DestroySchedulerByName("sunhurtComp_2");
	Super::OnLeaveOwner(owner);
}

SandboxResult SANDBOXAPI SunHurtComponent::OnSetSunHurt(SandboxContext context)
{
	m_SunHurt = context.GetData_Bool();
	return SandboxResult(this, true);
}

SandboxResult SANDBOXAPI SunHurtComponent::OnGetSunHurt(SandboxContext context)
{
	return SandboxResult(this, m_SunHurt);
}
