#include "ClientAvatarSummonMob.h"

void ClientAvatarSummonMob::setAccountBind(int uin)
{
	m_BindUIN = uin;
}

ClientAvatarSummonMob *ClientAvatarSummonMob::createFromDef(int monsterid)
{
	ClientAvatarSummonMob *mob = SANDBOX_NEW(ClientAvatarSummonMob);

	if (mob == NULL) return NULL;
	if (!mob->init(monsterid))
	{
		LOG_SEVERE("mob init failed: monsterid = %d", monsterid);
		return NULL;
	}
	return mob;
}