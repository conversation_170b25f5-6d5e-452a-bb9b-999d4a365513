#include"ContainerModel.h"
#include"BlockMaterialMgr.h"
#include"OgreEntity.h"
#include"BlockScene.h"
#include"ClientActorHelper.h"
#include"ActorVehicleAssemble.h"
#include "VehicleWorld.h"
ContainerModel::ContainerModel() : WorldContainer(WCoord(0, 0, 0), EMITTER_START_INDEX)
{
	m_strOmodID = "";
	m_pEntity = nullptr;
	m_iDir = 0;
	m_tp = TYPE::NORMAL;
	m_iCurAniId = -1;
	m_NeedTick = true;
}
//ContainerActorModel(const WCoord& blockpos, int dir);
ContainerModel::~ContainerModel()
{
	
} 

void ContainerModel::init(const string& strResid,const int& dir,const TYPE& tp,const WCoord& core_blockpos)
{
	m_strOmodID = strResid;
	m_iDir = dir;
	m_tp = tp;
	m_coreBlockpos = core_blockpos;
}



void ContainerModel::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	registerUpdateDisplay();

	onEnterWorld(pworld);
	//onEnterWorld(pworld);
}

void ContainerModel::leaveWorld()
{
	WorldContainer::leaveWorld();
	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
	}
}
void ContainerModel::updateTick()
{
	if (m_tp != TYPE::CORE) return;
	if (!m_vehicleWorld) return;	
	WCoord wp;
	WCoord wp1;
	Rainbow::Quaternionf qa;
	auto vehicleworld = static_cast<VehicleWorld*>(m_vehicleWorld);
	wp = vehicleworld->getActorVehicleAssemble()->getRealWorldPosWithPos(m_BlockPos);
	vehicleworld->getActorVehicleAssemble()->getRealWorldPosAndRotate(m_iDir, m_BlockPos, wp1, Rainbow::Quaternionf(0, 0, 0, 1), qa,WCoord(0,0,0));
	this->setPose(wp.toVector3(), qa);
}

void ContainerModel::updateDisplay(float dtime)
{
	if (m_tp != TYPE::CORE) return;
	if (m_vehicleWorld)
	{
		WCoord wp;
		WCoord wp1;
		Rainbow::Quaternionf qa;
		auto vehicleworld = static_cast<VehicleWorld*>(m_vehicleWorld);
		wp = vehicleworld->getActorVehicleAssemble()->getRealWorldPosWithPos(m_BlockPos);
		vehicleworld->getActorVehicleAssemble()->getRealWorldPosAndRotate(m_iDir, m_BlockPos, wp1, Rainbow::Quaternionf(0, 0, 0, 1), qa, WCoord(0, 0, 0));
		this->setPose(wp.toVector3(), qa);
	}
	if (m_pEntity)
	{
		Rainbow::Vector4f lightparam(0, 0, 0, 0);
		auto wp = m_pEntity->GetWorldPosition();
		if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(wp));
		lightparam.x += 0.2f;
		lightparam.y += 0.2f;
		m_pEntity->SetInstanceData(lightparam);
		unsigned int dtick = Rainbow::TimeToTick(dtime);
		m_pEntity->UpdateTick(dtick);
	}
	
}

void ContainerModel::setPose(const Rainbow::Vector3f& worldPos, const Rainbow::Quaternionf& rot)
{
	if (!m_pEntity) return;
	if (m_tp != TYPE::CORE) return;
	if (!m_vehicleWorld) return;

	auto q =   rot * m_qot;
	Rainbow::Matrix4x4f worldMat4;
	worldMat4.SetTRS(Rainbow::Vector3f(0, 0, 0), q, Rainbow::Vector3f(1.0f, 1.0f, 1.0f));
	
	Rainbow::Matrix4x4f localMat4;
	localMat4.SetTRS(m_step.toWorldPos().toVector3(), Rainbow::Quaternionf(0, 0, 0, 1), Rainbow::Vector3f(1.0, 1.0, 1.0));
	//先旋转再位移
	auto mat =   localMat4 * worldMat4;
	
	m_pEntity->SetPosition( worldPos + mat.GetPosition());
	m_pEntity->SetRotation( q );

}

void ContainerModel::onEnterWorld(World* pworld)
{
	//非核心方块不包含模型
	if (m_tp == TYPE::NORMAL) return;

#ifndef IWORLD_SERVER_BUILD	
	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
	}

	WCoord pos = m_BlockPos * BLOCK_SIZE;
	WCoord step;
	WCoord center;
	Rainbow::Vector3f angle;
	angle = Rainbow::Vector3f::zero;

	if (m_iDir == DIR_NEG_X)
	{
		angle.y = 90;
		step.z = BLOCK_SIZE;

		center.z = -BLOCK_SIZE * 0.5;
		center.x = -BLOCK_SIZE * 0.5;
	}
	else if (m_iDir == DIR_POS_X)
	{
		angle.y = 270;
		step.x = BLOCK_SIZE;

		center.z = -BLOCK_SIZE * 0.5;
		center.x = -BLOCK_SIZE * 0.5;
	}
	else if (m_iDir == DIR_NEG_Z)
	{
		center.z = -BLOCK_SIZE * 0.5;
		center.x = -BLOCK_SIZE * 0.5;
	}
	else if (m_iDir == DIR_POS_Z)
	{
		angle.y = 180;
		step.x = BLOCK_SIZE;
		step.z = BLOCK_SIZE;

		center.z = -BLOCK_SIZE * 0.5;
		center.x = -BLOCK_SIZE * 0.5;
	}
	else if (m_iDir == DIR_NEG_Y)
	{
		angle.x = 270;
		step.z = BLOCK_SIZE;

		center.z = 0;
		center.x = -BLOCK_SIZE * 0.5;
		center.y = -BLOCK_SIZE * 0.5;;
	}
	else if (m_iDir == DIR_POS_Y)
	{
		angle.x = 90;
		step.y = BLOCK_SIZE;

		center.z = -BLOCK_SIZE;
		center.x = -BLOCK_SIZE * 0.5;
		center.y = -BLOCK_SIZE * 0.5;
	}

	m_pEntity = Rainbow::Entity::Create();

#if ENTITY_MODIFY_MODEL_ASYNC
	m_pEntity->LoadAsync(m_strOmodID.c_str(),true);
#else
	Rainbow::Model* model = NULL;
	model = g_BlockMtlMgr.getModel(m_strOmodID.c_str());
	if (!model) return;
	m_pEntity->Load(model);
#endif

	m_pEntity->ShowSkins(true);
	m_pEntity->SetRotation(angle.y, angle.x, angle.z);
	pos += step;
	m_pEntity->SetPosition(pos.toWorldPos());

	Rainbow::Quaternionf qot = Rainbow::AngleEulerToQuaternionf(angle);
	m_step = center;
	m_qot = qot;

	if (m_World)
		m_pEntity->AttachToScene(m_World->getScene());

#endif
}

void ContainerModel::playAni(int aniID, int times)
{
	if (m_pEntity )
	{
		if (m_iCurAniId != aniID)
		{
			m_iCurAniId = aniID;
			m_pEntity->StopAnim();
			m_pEntity->PlayAnim(aniID, times);
		}
		
	}
}
void ContainerModel::stopAni()
{
	if (m_pEntity)
	{
		m_pEntity->StopAnim();
	}
}



flatbuffers::Offset<FBSave::ChunkContainer> ContainerModel::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);
	auto offsetPos = WCoordToCoord3(m_coreBlockpos);
	
	auto actor = FBSave::CreateContainerModel(builder,basedata, builder.CreateString(m_strOmodID.c_str()) ,m_iDir,(int)m_tp,&offsetPos);


	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerModel, actor.Union());
}

bool ContainerModel::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerModel*>(srcdata);
	loadContainerCommon(src->basedata());

	m_iDir = src->dir();
	m_strOmodID = src->modelID()->str();
	m_tp = (TYPE)src->blocktype();
	m_coreBlockpos = Coord3ToWCoord(src->corePosition());

	return true;
}
