#include "HPProgressComponent.h"
#include "IClientGameManagerInterface.h"
#include "PlayerControl.h"

#include "ActorHorse.h"

#include "GameCamera.h"
#include "PlayerAttrib.h"

#include "RiddenComponent.h"
#include "ClientInfoProxy.h"
#include "LuaInterfaceProxy.h"
#include "ActorVehicleAssemble.h"
#include "SandBoxManager.h"
#include "ModPackMgr.h"
using namespace Rainbow;

IMPLEMENT_COMPONENTCLASS(HPProgressComponent)

HPProgressComponent::HPProgressComponent()
:m_WasAttackedType(ATTACK_ALL)
,m_nInjuredTypeForAITree(0)	//给行为树判断受伤类型，0为没有受伤，1为受到场景伤害，2为受到活物伤害
,m_isHPProgressEnabled(false)
,m_lastAttackTime(0)
,m_lastAttackedTime(0)
,m_isHPProgressDirty(true)
,m_currentHurtIsCritical(false)
,m_lastMainPlayerTeamID(-1)
,m_playerHPSwitchFlag(-1)
,m_playerDamagewitch(-1)
, m_showHP(true)
{

}
void HPProgressComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	m_owner = dynamic_cast<ActorLiving*>(owner);
	if (m_owner){
		m_owner->bindHPProgress(this);
	}
	Super::OnEnterOwner(owner);
}
void HPProgressComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	ActorLiving *living = dynamic_cast<ActorLiving*>(owner);
	if (living) {
		living->bindHPProgress(nullptr);
	}
	Super::OnLeaveOwner(living);
}
void HPProgressComponent::onAttackedFrom(OneAttackData &atkdata, ClientActor *attacker)
{
	if (attacker)
	{
		m_nInjuredTypeForAITree = 2;
	}
	else
	{
		m_nInjuredTypeForAITree = 1;
	}

	m_currentHurtIsCritical = atkdata.critical || atkdata.isAttackHead;
}



void HPProgressComponent::addHPEffect(float hp)
{
	updateLastAttackTime();
	setHPProgressDirty();

	//不足1的变化不显示飘字
	if (hp > -1 && hp < 1)
	{
		return;
	}

	if (!GetIClientGameManagerInterface()->getICurGame() || !GetIClientGameManagerInterface()->getICurGame()->isInGame())
	{
		return;
	}

	//玩家开关 0关 1开`
	enum {
		PLAYER_HP_EFFECT_SWITCH_CLOSE,
		PLAYER_HP_EFFECT_SWITCH_OPEN
	};

	//if (m_playerDamagewitch == -1)
	{
		m_playerDamagewitch = GetClientInfoProxy()->getGameData("damageswitch");
	}

	if (m_playerDamagewitch == PLAYER_HP_EFFECT_SWITCH_CLOSE) return;

	//0:不显示 1：仅显示自己伤害 2：显示我方伤害 3 显示所有伤害  非开发者默认显示所有伤害
	enum
	{
		SWITCH_NOTHING,
		SWITCH_SELF,
		SWITCH_TEAMMATE, 
		SWITCH_ALL 
	};

	//地图开关
	int tmpOpId = 0;
	float val = SWITCH_ALL;  

	if (!g_pPlayerCtrl || !g_pPlayerCtrl->getWorld())
	{
		return;
	}

	if (GetWorldManagerPtr())
	{
		GetWorldManagerPtr()->getRuleOptionID(GMRULE_SHOW_DAMAGE, tmpOpId, val);
	}

	int customtype = ModPackMgr::GetInstancePtr()->GetDamageFloatTxtCode();
	if (customtype  >= 0) 
	{
		val = customtype;
	}

	//m_owner = GetOwnerActorLiving();
	
	PlayerControl* player = dynamic_cast<PlayerControl*>(GetIClientGameManagerInterface()->getICurGame()->getIMainPlayer());

	ClientPlayer *currentPlayer = dynamic_cast<ClientPlayer *>(m_owner);
	ClientMob *currentMob = dynamic_cast<ClientMob *>(m_owner);
	bool isPlayerOrOwn = false;

	if (!player)
	{
		return;
	}

	if (currentPlayer == player)
	{
		if (g_pPlayerCtrl->getViewMode() == CAMERA_FPS) //第一视角不飘字
		{
			return;
		}
	}

	// 判断不需要显示的情况直接返回

	if (val == SWITCH_NOTHING) //0:不显示
	{
		return;
	}
	else if (hp<0)  // hp<0 加血都显示不需要判断
	{
		if (!GetIClientGameManagerInterface()->getICurGame() || !GetIClientGameManagerInterface()->getICurGame()->isInGame())
		{
			return;
		}

		bool isAboutSelf = false;

		if (currentPlayer)
		{
			if (currentPlayer != player && currentPlayer->getBeHurtTarget() != player)
			{
				isAboutSelf = false;
			}
			else
			{
				isAboutSelf = true;
			}

			if (currentPlayer == player)
			{
				isPlayerOrOwn = true;
			}
		}
		else if (currentMob)
		{
			ClientMob* from = dynamic_cast<ClientMob *>(currentMob->getBeHurtTarget());
			ClientPlayer *fromPlayer = dynamic_cast<ClientPlayer *>(currentMob->getBeHurtTarget());
			if (currentMob->getTamedOwnerID() == player->getUin())
			{
				isAboutSelf = true;
				isPlayerOrOwn = true;
			}
			else if (from && from->getTamedOwnerID() == player->getUin())
			{
				isAboutSelf = true;
			}
			else if (fromPlayer && (fromPlayer == player))
			{
				isAboutSelf = true;
			}
			else
			{
				isAboutSelf = false;
			}
		}
		else
		{
			//boss 或其他
			ClientMob* from = dynamic_cast<ClientMob *>(m_owner->getBeHurtTarget());
			ClientPlayer *fromPlayer = dynamic_cast<ClientPlayer *>(m_owner->getBeHurtTarget());
			if (from && from->getTamedOwnerID() == player->getUin())
			{
				isAboutSelf = true;
			}
			else if (fromPlayer && (fromPlayer == player))
			{
				isAboutSelf = true;
			}
			else
			{
				isAboutSelf = false;
			}
		}

		if (val == SWITCH_SELF && !isAboutSelf)//1：仅显示自己相关伤害
		{
			return;
		}

		if (val == SWITCH_TEAMMATE && !isAboutSelf)
		{
			ActorLiving* from = dynamic_cast<ActorLiving *>(m_owner->getBeHurtTarget());
			if (m_owner->isSameTeam(player) || (from && player->isSameTeam(from)))
			{
			}
			else
			{
				auto game = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);

				if (!game)
				{
					return;
				}

				bool isOwnerFriend = false;
				if (currentMob)
				{
					int id = currentMob->getTamedOwnerID();
					if (id > 0)
					{
						if (player->isSameTeam(game->getPlayerByUin(id)))
						{
							isOwnerFriend = true;
						}
					}
				}

				if (!isOwnerFriend)
				{
					ClientMob* fromMob = dynamic_cast<ClientMob *>(from);

					if (fromMob)
					{
						int id = fromMob->getTamedOwnerID();
						if (id > 0)
						{
							if (!player->isSameTeam(game->getPlayerByUin(id)))
							{
								return;
							}
						}
						else
						{
							return;
						}
					}
					else
					{
						return;
					}
				}
			}

		}

	}

	ActorBody *body = m_owner->getBody();
	if (!body)
	{
		return;
	} 

	char buf[100] = {0};

	HPChangeEffectConfig* hpEffectCfg = &GetWorldManagerPtr()->m_SurviveGameConfig->hpchangeeffectcfg;
	Rainbow::ColorQuad color = Rainbow::ColorQuad(hpEffectCfg->color_else_r, hpEffectCfg->color_else_g, hpEffectCfg->color_else_b, hpEffectCfg->color_else_a);
	int fontSize = hpEffectCfg->font_size;

	if (hp > 0)
	{
		sprintf(buf, "+%d",int(hp + 0.00001));
		color.r = hpEffectCfg->color_add_r;
		color.g = hpEffectCfg->color_add_g;
		color.b = hpEffectCfg->color_add_b;
		color.a = hpEffectCfg->color_add_a;
	}
	else
	{
		sprintf(buf, "%d", int(hp - 0.00001));

		if (m_currentHurtIsCritical)
		{
			color.r = hpEffectCfg->color_critical_r;
			color.g = hpEffectCfg->color_critical_g;
			color.b = hpEffectCfg->color_critical_b;
			color.a = hpEffectCfg->color_critical_a;

			fontSize = hpEffectCfg->font_size_critical;
		}
		else
		{
			if (isPlayerOrOwn)
			{
				color.r = hpEffectCfg->color_self_r;
				color.g = hpEffectCfg->color_self_g;
				color.b = hpEffectCfg->color_self_b;
				color.a = hpEffectCfg->color_self_a;
			}
		}
	}

	body->createHPChangeText(buf, fontSize, color, currentPlayer == player);

}

void HPProgressComponent::updateLastAttackTime()
{
	m_lastAttackTime = time(NULL);
}

void HPProgressComponent::setHPProgressDirty()
{
	m_isHPProgressDirty = true;
}


void HPProgressComponent::livingHPtick()
{
	//根据玩家设置以及地图设置确定是否要显示血条
	//玩家开关 0关 1开
	enum {
		PLAYER_HP_PROGRESS_SWITCH_CLOSE,
		PLAYER_HP_PROGRESS_SWITCH_OPEN

	};

	//1是开启，2是关闭  非开发者默认开启
	enum {
		MAP_HP_PROGRESS_SWITCH_OPEN = 1,
		MAP_HP_PROGRESS_SWITCH_CLOSE = 2
	};

	//if (m_playerHPSwitchFlag == -1)
	{
#ifdef DEDICATED_SERVER
		static bool s_bloodswtich = GetClientInfoProxy()->getGameData("bloodswitch");
		m_playerHPSwitchFlag = s_bloodswtich;
#else
        static Rainbow::NoFreeFixedString bloodswitch("bloodswitch");
		m_playerHPSwitchFlag = GetClientInfoProxy()->getGameData(bloodswitch);
#endif
        static Rainbow::NoFreeFixedString damageswitch("damageswitch");
		m_playerDamagewitch = GetClientInfoProxy()->getGameData(damageswitch);

	}

	//地图开关
	int tmpOpId = 0;
	float val = PLAYER_HP_PROGRESS_SWITCH_OPEN;

	if (GetWorldManagerPtr())
	{
		GetWorldManagerPtr()->getRuleOptionID(GMRULE_SHOW_BLOOD_BAR, tmpOpId, val);
	}

	bool oriHPProEnabled = m_isHPProgressEnabled;

	if (m_playerHPSwitchFlag == PLAYER_HP_PROGRESS_SWITCH_OPEN && val == MAP_HP_PROGRESS_SWITCH_OPEN)
	{
		m_isHPProgressEnabled = true;
	}
	else
	{
		m_isHPProgressEnabled = false;
	}

	if (oriHPProEnabled != m_isHPProgressEnabled) m_isHPProgressDirty = true;

	if (g_pPlayerCtrl)
	{
		int mainPlayerTeamID = g_pPlayerCtrl->getTeam();
		if (m_lastMainPlayerTeamID == -1)
		{
			m_lastMainPlayerTeamID = mainPlayerTeamID;
		}

		if (m_lastMainPlayerTeamID != mainPlayerTeamID) //主角队伍发生变化,他看到的其他人或者生物也要变
		{
			m_lastMainPlayerTeamID = mainPlayerTeamID;
			m_isHPProgressDirty = true;
		}
	}

	if (checkNeedTickFactorForHPProgress()) m_isHPProgressDirty = true;


	if (m_isHPProgressDirty)
	{
		updateHPProgress();
		m_isHPProgressDirty = false;
	}
}

void HPProgressComponent::mobHPTick()
{
	bool oriHPVisible = false;
	if (!GetOwner()) return ;
	m_owner = dynamic_cast<ActorLiving*>(GetOwner());
	if (!m_owner) return ;
	ActorBody *body = m_owner->getBody();
	if (body)
	{
		oriHPVisible = body->getHPVisible();
	}

	int HPKeepShowTime = 10;
	int nowTime = time(NULL);
	int lastFlightingTime = m_lastAttackTime > m_lastAttackedTime ? m_lastAttackTime : m_lastAttackedTime;
	if (nowTime - lastFlightingTime > HPKeepShowTime && oriHPVisible)
	{
		m_isHPProgressDirty = true;
	}
}
void HPProgressComponent::setHpVisible(bool visible)
{
	m_showHP = visible;
	//主机同步到客机
	ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
	if (actor)
	{
		World* pworld = GetOwnerActor()->getWorld();
		/*actorbody->playMotion(path, loopPlayTime, m_OffsetPosition, m_rote, isLoop);*/
		if (pworld && !pworld->isRemoteMode())
		{
			jsonxx::Object context;
			char objid_str[128];
			sprintf(objid_str, "%lld", actor->getObjId());
			context << "visible" << visible;
			SandBoxManager::getSingleton().sendBroadCast("PB_SETHPVISIBLE_HC", context.bin(), context.binLen());
		}
	

		ActorBody* body = actor->getBody();
		if (body)
		{
			body->setHPVisible(m_showHP);
		}
	}
}
IMPLEMENT_COMPONENTCLASS(MobHPProgressComponent)

MobHPProgressComponent::MobHPProgressComponent():HPProgressComponent(),m_lastTamedOwnerTeamId(-1)
{

}

bool MobHPProgressComponent::checkNeedTickFactorForHPProgress()
{
	m_owner = dynamic_cast<ActorLiving*>(GetOwner());
	m_ownerMob = dynamic_cast<ClientMob*>(GetOwner());
	World* pWorld = m_owner->getWorld();
	ClientActorMgr* actorMgr = nullptr;
	if (pWorld)
	{
		actorMgr = pWorld->getActorMgr() ? pWorld->getActorMgr()->ToCastMgr() : nullptr;
	}
	if (pWorld && m_ownerMob->getTamedOwnerID() > 0)
	{
		ActorLiving * living = dynamic_cast<ActorLiving*>(actorMgr->findActorByWID(m_ownerMob->getTamedOwnerID()));

		if (living)
		{
			int nowTeamId = living->getTeam();
			if (nowTeamId != m_lastTamedOwnerTeamId)
			{
				m_lastTamedOwnerTeamId = nowTeamId;
				return true;
			}
		}
	}

	return false;
}

void MobHPProgressComponent::updateHPProgress()
{
	if (!m_showHP)
		return;

	bool isNeedChange = false;

	bool oriHPVisible = false;

	if (!GetOwner()) return;
	m_owner = dynamic_cast<ActorLiving*>(GetOwner());
	if (!m_owner) return;
	World* pWorld = m_owner->getWorld();
	ClientActorMgr* actorMgr = nullptr;
	if (pWorld)
	{
		actorMgr = pWorld->getActorMgr() ? pWorld->getActorMgr()->ToCastMgr() : nullptr;
	}

	ActorBody *body = m_owner->getBody();
	if (body)
	{
		oriHPVisible = body->getHPVisible();
	}


	if ((m_isHPProgressEnabled && !oriHPVisible) || (!m_isHPProgressEnabled && oriHPVisible))
	{
		isNeedChange = true;
	}

	if (m_isHPProgressEnabled || isNeedChange)
	{
		//云服后面的执行逻辑函数都是空函数，云服会有宕机所以屏蔽
#ifdef IWORLD_SERVER_BUILD
		return;
#endif // IWORLD_SERVER_BUILD

		if (GetIClientGameManagerInterface()->getICurGame() && GetIClientGameManagerInterface()->getICurGame()->isInGame() && GetWorldManagerPtr())
		{
			PlayerControl * player = dynamic_cast<PlayerControl*>(GetIClientGameManagerInterface()->getICurGame()->getIMainPlayer());
			bool invisible = player ? player->isInvisible() : false;
			HPProgressConfig *hpConfig = &GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg;
			if (m_isHPProgressEnabled && !invisible)	// ********：隐身时不显示血条  codeby： keguanqiang
			{
				if (body)
				{
					body->setHPColor(hpConfig->color_enemy_r, hpConfig->color_enemy_g, hpConfig->color_enemy_b, hpConfig->color_enemy_a); //默认是敌人的颜色
					body->setHPTextrueName("img_blood_strip_board_b.png","img_blood_red.png");
					ActorHorse* actorHorse = dynamic_cast<ActorHorse*> (m_owner);
					int bindId = 0;
					if (actorHorse)
					{
						bindId = actorHorse->getAccountBindId();
					}

					//自己的坐骑不显示血量
					//if (player && player->isRiding() && player->getRidingActorObjId() == m_ObjId)
					//只要是被骑就不显示血条
					auto RidComp = m_owner->getRiddenComponent();
					if(RidComp && RidComp->isRidden())
					{
						body->setHPVisible(false);
						//骑的生物刷新血条
						World* pWorld = m_owner->getWorld();
						if(pWorld && actorMgr)
						{
							/*
							ActorLiving * living = dynamic_cast<ActorLiving*>( pWorld->getActorMgr()->findActorByWID(RidComp->getRiddenByActorID()));
							if (living)
							{
								living->setHPProgressDirty();
							}
							*/

							int numPos = RidComp->getNumRiddenPos();
							numPos = max(numPos, (int)1);
							for (size_t i = 0; i < numPos; i++)
							{
								ActorLiving* living = dynamic_cast<ActorLiving*>(actorMgr->findActorByWID(RidComp->getRiddenByActorID(i)));
								if (living)
								{
									living->setHPProgressDirty();
								}
							}
						}

						return;
					}

					if (!GetOwner()) return;
					m_ownerMob = dynamic_cast<ClientMob*>(GetOwner());
					if (!m_ownerMob) return;
					//已经是驯服状态了
					if (m_ownerMob->getTamedOwnerID() > 0)
					{
						if (player)
						{
							if (m_ownerMob->getTamedOwnerID() == player->getUin())
							{
								body->setHPColor(hpConfig->color_self_r, hpConfig->color_self_g, hpConfig->color_self_b, hpConfig->color_self_a);
								body->setHPTextrueName("img_blood_strip_board_b.png", "img_blood_green.png");
							}
							else
							{
								auto game = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);

								if (game && game->getPlayerByUin(m_ownerMob->getTamedOwnerID()))
								{
									if (player->isSameTeam(game->getPlayerByUin(m_ownerMob->getTamedOwnerID())))
									{
										body->setHPColor(hpConfig->color_teammate_r, hpConfig->color_teammate_g, hpConfig->color_teammate_b, hpConfig->color_teammate_a);
										body->setHPTextrueName("img_blood_strip_board_b.png", "img_blood_blue.png");
									}
								}
							}
						}
					}
					else if (bindId > 0 && player)
					{
						if (bindId == player->getUin())
						{
							body->setHPColor(hpConfig->color_self_r, hpConfig->color_self_g, hpConfig->color_self_b, hpConfig->color_self_a);
							body->setHPTextrueName("img_blood_strip_board_b.png", "img_blood_green.png");
						}
						else
						{
							auto game = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);
							if(game)
							{
								if (player->isSameTeam(game->getPlayerByUin(bindId)))
								{
									body->setHPColor(hpConfig->color_teammate_r, hpConfig->color_teammate_g, hpConfig->color_teammate_b, hpConfig->color_teammate_a);
									body->setHPTextrueName("img_blood_strip_board_b.png", "img_blood_blue.png");
								}
							}
						}
					}
					else if (m_owner->getTeam() > 0)
					{
						if (player && m_owner->isSameTeam(player))
						{
							body->setHPColor(hpConfig->color_teammate_r, hpConfig->color_teammate_g, hpConfig->color_teammate_b, hpConfig->color_teammate_a);
							body->setHPTextrueName("img_blood_strip_board_b.png", "img_blood_blue.png");
						}
					}

					ActorAttrib* attr = m_owner->getAttrib();

					int hp = (int)ceil(attr->getHP());
					if (attr->getHP() < 0)
					{
						hp = 0;
					}

					body->setHPVale(hp, attr->getMaxHP());
					if(!m_ownerMob->isDead())
					{
						body->setHPVisible(true);
					}

					//没有组队的中立生物 10s内没有攻击或者被攻击  则隐藏血条
					//if (m_ownerMob->getTeam() == 0 && m_ownerMob->getTamedOwnerID() == 0 && bindId == 0)  
					if(m_ownerMob->getTeam() == 0 || (!player || (player->getTeam() != m_ownerMob->getTeam())))//改成所有的生物, 如果不是跟玩家同队伍，10s内没有攻击或者被攻击  则隐藏血条
					{
						int nowTime = time(NULL);
						int lastFlightingTime = m_lastAttackTime > m_lastAttackedTime ? m_lastAttackTime : m_lastAttackedTime;
						if (nowTime - lastFlightingTime > 10)
						{
							body->setHPVisible(false);
						}
					}
				}
			}
			else
			{
				if (body)
				{
					body->setHPVisible(false);
				}
			}
		}
	}
}
IMPLEMENT_COMPONENTCLASS(PlayerHPProgressComponent)

PlayerHPProgressComponent::PlayerHPProgressComponent():HPProgressComponent()
{

}

void PlayerHPProgressComponent::updateHPProgress()
{
	bool isNeedChange = false;

	bool oriHPVisible = false;

	if (!GetOwner()) return;
	m_owner = dynamic_cast<ActorLiving*>(GetOwner());
	if (!m_owner) return;


	ActorBody *body = m_owner->getBody();
	if (body)
	{
		oriHPVisible = body->getHPVisible();
	}

	if ((m_isHPProgressEnabled && !oriHPVisible) || (!m_isHPProgressEnabled && oriHPVisible))
	{
		isNeedChange = true;
	}

	if (oriHPVisible || isNeedChange)
	{
		//云服后面的执行逻辑函数都是空函数，云服会有宕机所以屏蔽
#ifdef IWORLD_SERVER_BUILD
		return;
#endif // IWORLD_SERVER_BUILD

		if (GetIClientGameManagerInterface()->getICurGame() && GetWorldManagerPtr() && GetWorldManagerPtr()->m_SurviveGameConfig && GetIClientGameManagerInterface()->getICurGame()->isInGame())
		{
			if (!GetOwner()) return;
			m_ownerPlayer = dynamic_cast<ClientPlayer*>(GetOwner());
			if (!m_ownerPlayer) return;
			//根据玩家设置以及地图设置确定是否要显示血条
			HPProgressConfig *hpConfig = &GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg;
			PlayerControl * player = dynamic_cast<PlayerControl*>(GetIClientGameManagerInterface()->getICurGame()->getIMainPlayer());
			if (m_isHPProgressEnabled && m_owner != player && !player->isInvisible())	// ********：隐身时不显示血条  codeby： keguanqiang
			{

				ActorBody *body = m_owner->getBody();
				if (body)
				{
					if (player && m_owner->isSameTeam(player))
					{
						body->setHPColor(hpConfig->color_teammate_r, hpConfig->color_teammate_g, hpConfig->color_teammate_b, hpConfig->color_teammate_a);
						body->setHPTextrueName("img_blood_strip_board_b.png", "img_blood_blue.png");
					}
					else
					{
						body->setHPColor(hpConfig->color_enemy_r, hpConfig->color_enemy_g, hpConfig->color_enemy_b, hpConfig->color_enemy_a);
						body->setHPTextrueName("img_blood_strip_board_b.png", "img_blood_red.png");
					}

					auto RidComp = m_owner->getRiddenComponent();
					if(!(RidComp && RidComp->isRiding()))
					{
						PlayerAttrib* attr = m_ownerPlayer->getPlayerAttrib();
						int hp = attr->getHP();
						if (attr->getHP() < 0)
						{
							hp = 0;
						}
						else if (attr->getHP() > 0 && attr->getHP() < 1) //策划不希望显示0血生物还活着
						{
							hp = 1;
						}
						body->setHPVisible(true);
						body->setHPVale(hp, attr->getMaxHP());
						if (GetLuaInterfaceProxy().shouldUseNewHpRule())
						{
							int armor = (int)ceil(attr->getArmor());
							body->setHpExtraValue(armor);
							if (armor <= 0)
							{
								body->setHpTextDisplay(1);
							}
						}
						else
						{
							body->setHpTextDisplay(0);
						}
					}
					else  //显示被骑生物的血量
					{
						ActorVehicleAssemble* vehicle = NULL;
						if (RidComp)
						{
							vehicle = dynamic_cast<ActorVehicleAssemble*>(RidComp->getRidingActor());
						}
						
						if (vehicle)
						{
							PlayerAttrib* attr = m_ownerPlayer->getPlayerAttrib();
							int hp = (int)ceil(attr->getHP());
							if (attr->getHP() < 0)
							{
								hp = 0;
							}

							body->setHPVale(hp, attr->getMaxHP());
							body->setHPVisible(true);
						}
						else {
							ActorLiving *living = NULL;
							if (RidComp)
							{
								living = dynamic_cast<ActorLiving *>(RidComp->getRidingActor());
							}
						
							if (living)
							{
								ActorAttrib* attr = living->getAttrib();

								int hp = (int)ceil(attr->getHP());
								if (attr->getHP() < 0)
								{
									hp = 0;
								}

								body->setHPVale(hp, attr->getMaxHP());
								body->setHPVisible(true);
							}
						}
					}
				}
			}
			else
			{
				ActorBody *body = m_owner->getBody();
				if (body)
				{
					body->setHPVisible(false);
				}
			}
		}
	}
}

PlayerArmorProgressComponent::PlayerArmorProgressComponent(ClientPlayer* owner) :HPProgressComponent(), m_ownerPlayer(owner)
{

}

void PlayerArmorProgressComponent::updateHPProgress()
{
	bool isNeedChange = false;

	bool oriHPVisible = false;

	ActorBody *body = m_owner->getBody();
	if (body)
	{
		oriHPVisible = body->getArmorVisible();
	}

	if ((m_isHPProgressEnabled && !oriHPVisible) || (!m_isHPProgressEnabled && oriHPVisible))
	{
		isNeedChange = true;
	}

	if (oriHPVisible || isNeedChange)
	{
		//云服后面的执行逻辑函数都是空函数，云服会有宕机所以屏蔽
#ifdef IWORLD_SERVER_BUILD
		return;
#endif // IWORLD_SERVER_BUILD

		if (g_WorldMgr && g_WorldMgr->m_SurviveGameConfig && GetIClientGameManagerInterface()->getICurGame() && GetIClientGameManagerInterface()->getICurGame()->isInGame())
		{
			//拿取配置
			ArmorProgressConfig *hpConfig = &g_WorldMgr->m_SurviveGameConfig->armorprogresscfg;
			PlayerControl* player = dynamic_cast<PlayerControl*>(GetIClientGameManagerInterface()->getICurGame()->getIMainPlayer());
			if (m_isHPProgressEnabled  && m_owner != player && m_ownerPlayer && !m_ownerPlayer->isInvisible())	// ********：隐身时不显示血条  codeby： keguanqiang
			{

				ActorBody *body = m_owner->getBody();
				if (body)
				{
					if (player && m_owner->isSameTeam(player))
					{
						body->setArmorColor(hpConfig->color_teammate_r, hpConfig->color_teammate_g, hpConfig->color_teammate_b, hpConfig->color_teammate_a);
						body->setArmorTextrueName("", "img_shield_blue.png");
					}
					else
					{
						body->setArmorColor(hpConfig->color_enemy_r, hpConfig->color_enemy_g, hpConfig->color_enemy_b, hpConfig->color_enemy_a);
						body->setArmorTextrueName("", "img_shield_red.png");
					}
					PlayerAttrib* attr = m_ownerPlayer->getPlayerAttrib();
					float oriHp = 0.f;
					if(attr)
					   oriHp = attr->getArmor();
					int hp = oriHp;
					if (oriHp <= 0)
					{
						hp = 0;
					}
					else if (oriHp > 0 && oriHp < 1)
					{
						hp = 1;
					}
					if (hp <= 0)
					{
						body->setArmorVisible(false);
					}
					else
					{
						//int curLife = ceil(attr->getHP());
						body->setArmorVisible(true);

						body->setArmorVale(hp, 100, 0);
					}
				}
			}
			else
			{
				ActorBody *body = m_owner->getBody();
				if (body)
				{
					body->setArmorVisible(false);
				}
			}
		}
	}
}

void PlayerArmorProgressComponent::addHPEffect(float hp)
{
	//什么也不做
	;
}