#include "RadiationComponent.h"
#include "world.h"
#include "ClientPlayer.h"
#include "EffectParticle.h"
#include "RadiationManager.h"
#include "WorldManager.h"
#include "LivingAttrib.h"
#include "ClientAppProxy.h"
#include "PlayerControl.h"

#include "Graphics/ScreenManager.h"
#include "UILib/UIRenderer.h"

using namespace Rainbow;
using namespace Rainbow::UILib;

RadiationEffect::RadiationEffect(int buffId, EffectParticle* particle, std::string path, float scale, bool isChangeScale)
    : BuffId(buffId), Particle(particle), Path(path), Scale(scale), IsChangeScale(isChangeScale), EffectID(0)
{
}

void RadiationEffect::setParticle(EffectParticle* particle)
{
    Particle = particle;
}

IMPLEMENT_COMPONENTCLASS(RadiationComponent)

RadiationComponent::RadiationComponent()
    : m_IsInit(false)
    , m_IsAffectedRadiation(true)
    , m_RadiationValue(0.0f)
    , m_CurrentBuff(RCBuffType::Safe)
    , m_TextureMaterial(UIRenderer::GetInstance().CreateInstance())
    , m_TextureAlpha(0)
    , m_IsTextureAlpha(false)
    , m_IsChangeScreenDecal(false)
    , m_IsChangeModelTexture(false)
    , m_CMTMaxValue(0.0f)
    , m_CMTValue(0.0f)
    , m_IsSafeRadiation(true)
    , m_ChangeRadiationValue(0.0f)
    , m_ChangeRadiationTicks(0)
    , m_ChangeRadiationTickMark(0)
    , m_IsPlayerShake(false)
    , m_IsPlayerCameraCanMove(true)
    , m_pWorld(nullptr)
    , m_ChangeIsPauseCurrentFrame(false)
    , m_PauseCurrentFrame(false)
    , m_gloader(nullptr)
    , m_AccumulatedRadiation(0.0f)
    , m_AccumulationTick(0)
    , m_RadiationProtection(0.0f)
{
	m_gloader = fairygui::GLoader::create();
	m_gloader->retain();    
}

RadiationComponent::~RadiationComponent()
{
    m_Texture = nullptr;
    CC_SAFE_RELEASE_NULL(m_gloader);
}

void RadiationComponent::OnInit()
{
    // 初始化辐射等级对应的Buff配置
    m_BuffTextureAlpha[RCBuffType::Safe] = 60;
    m_BuffTextureAlpha[RCBuffType::LowRadiation] = 90;
    m_BuffTextureAlpha[RCBuffType::MediumRadiation] = 127;
    m_BuffTextureAlpha[RCBuffType::HighRadiation] = 190;
    m_BuffTextureAlpha[RCBuffType::LethalRadiation] = 250;

    m_IsInit = true;
}

void RadiationComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
    ActorComponentBase::OnEnterOwner(owner);
    BindOnTick();
}

void RadiationComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
    UnBindOnTick();
    ActorComponentBase::OnLeaveOwner(owner);
    
    ClearAllRadiationEffects();
}

void RadiationComponent::OnTick()
{
    if (!m_IsInit) {
        OnInit();
    }

    Update();
    UpdateScreenDecals();
    UpdateModelTexture();
    UpdateRadiationEffects();
    UpdateChangeRadiation();
    UpdateRadiationImpact();
    UpdatePauseCurrentFrame();
}

void RadiationComponent::Update()
{
    GetRealRadiation();
        
    RCBuffType newBuff = GetBuffType(m_RadiationValue);
    if (m_CurrentBuff != newBuff)
    {
        m_CurrentBuff = newBuff;
        
        m_IsChangeScreenDecal = true;
        m_IsChangeModelTexture = true;
    }
}

World* RadiationComponent::GetWorld()
{
	if (m_pWorld == nullptr)
	{
		if (!GetOwner()) return nullptr;
		IClientActor* actor = GetOwnerActor();
		if (actor)
		{
			m_pWorld = actor->getWorld();
		}
	}
	return m_pWorld;
}

void RadiationComponent::GetRealRadiation()
{
    World* world = GetWorld();
    if (!world) return;
    
    IClientActor* pOwner = GetOwnerActor();
    if (!pOwner) return;
    
	LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(pOwner->GetIActorAttrib());
	if (attrib)
	{
        m_RadiationValue = attrib->getRadiation();

	}    
}

RCBuffType RadiationComponent::GetBuffType(float rad)
{
    if (rad <= 0.0f)
        return RCBuffType::Safe;
    else if (rad <= 100.0f)
        return RCBuffType::LowRadiation;
    else if (rad <= 200.0f)
        return RCBuffType::MediumRadiation;
    else if (rad <= 300.0f)
        return RCBuffType::HighRadiation;
    else
        return RCBuffType::LethalRadiation;
}

void RadiationComponent::UpdateScreenDecals()
{
    if (m_IsChangeScreenDecal && m_ScreenDecals.size() > 0)
    {
        m_IsChangeScreenDecal = false;
        RadiationScreenDecal screenDecal = m_ScreenDecals.back();
        
        m_Texture = nullptr;
        m_IsTextureAlpha = screenDecal.IsAlpha;
        GetTextureAlpha();
        string strLongId = to_string(screenDecal.ImgId);

        if (screenDecal.ImgId != 0)
        {
            GetClientAppProxy()->DevUISetIconByResIdExProxy(m_gloader, strLongId);
            if (m_gloader && m_gloader->IsContentLoaded())
            {
                Rainbow::SequenceTexture* seqtex = static_cast<Rainbow::SequenceTexture*>(m_gloader->getSequenceTexture());

                fairygui::FUISprite* psprite = m_gloader->getContent();
                if (psprite)
                {
                    m_Rect = psprite->getTextureRect();
                    m_Texture = psprite->getTexture().CastTo<Rainbow::Texture2D>();
                }
            }
        }
    }
    else if(m_ScreenDecals.size() == 0)
    {
        m_Texture = nullptr;
    }
}

void RadiationComponent::SetScreenDecals(int buffId, bool isScreenDecals, int imgId, bool isAlpha)
{
    m_IsChangeScreenDecal = true;
    if (isScreenDecals && GetOwnerPlayer() == g_pPlayerCtrl)
    {
        // 检查是否已存在相同BuffId的贴花
        for (auto it = m_ScreenDecals.begin(); it != m_ScreenDecals.end(); ++it)
        {
            if (it->BuffId == buffId)
            {
                return;
            }
        }
        // 添加新的屏幕贴花
        RadiationScreenDecal screenDecal{ buffId, imgId, isAlpha };
        m_ScreenDecals.push_back(screenDecal);
    }
    else
    {
        // 移除指定BuffId的贴花
        m_Texture = nullptr;
        for (auto it = m_ScreenDecals.begin(); it != m_ScreenDecals.end(); ++it)
        {
            if (it->BuffId == buffId)
            {
                m_ScreenDecals.erase(it);
                return;
            }
        }
    }
}

void RadiationComponent::UpdateModelTexture()
{
    if (!m_IsChangeModelTexture)
        return;
        
    m_IsChangeModelTexture = false;
    
    // 更新角色模型的纹理效果
    // 实际实现需要调用相关的模型纹理API
}

void RadiationComponent::UpdateWeaponTexture()
{
    // 更新武器纹理效果
    // 实际实现需要调用相关的武器纹理API
}

void RadiationComponent::ChangeModelTexture(int buffId, bool isChange, bool isTransition, float minValue, float maxValue)
{
    if (!isChange)
        return;
        
    // 改变模型纹理
    m_IsChangeModelTexture = true;
}

void RadiationComponent::UpdateRadiationEffects()
{
    // 更新辐射视觉效果
    for (auto& effect : m_RadiationEffects)
    {
        // 更新粒子效果
        if (effect.Particle)
        {
            // 更新位置等
            IClientActor* pOwner = GetOwnerActor();
            if (pOwner)
            {
                // 更新粒子位置到角色位置
            }
        }
    }
}

void RadiationComponent::AddRadiationEffect(int buffId, int particleId, float modelScale, bool isChangeScale)
{
    // 添加辐射视觉效果
    for (auto& effect : m_RadiationEffects)
    {
        if (effect.BuffId == buffId)
        {
            // 已存在同ID的效果，更新
            return;
        }
    }
    
    // 创建新的辐射效果
    // 实际实现需要调用相关的特效API
    EffectParticle* particle = nullptr;
    
    // 添加到列表
    m_RadiationEffects.push_back(RadiationEffect(buffId, particle, "", modelScale, isChangeScale));
}

void RadiationComponent::RemoveRadiationEffect(int buffId)
{
    // 移除指定buffId的辐射效果
    for (auto it = m_RadiationEffects.begin(); it != m_RadiationEffects.end();)
    {
        if (it->BuffId == buffId)
        {
            // 释放资源
            if (it->Particle)
            {
                // 销毁粒子效果
                it->Particle = nullptr;
            }
            it = m_RadiationEffects.erase(it);
        }
        else
        {
            ++it;
        }
    }
}

void RadiationComponent::ClearAllRadiationEffects()
{
    // 清除所有辐射效果
    for (auto& effect : m_RadiationEffects)
    {
        if (effect.Particle)
        {
            // 销毁粒子效果
            effect.Particle = nullptr;
        }
    }
    
    m_RadiationEffects.clear();
}

void RadiationComponent::UpdateChangeRadiation()
{
    // 处理辐射值的动态变化
    if (m_ChangeRadiationTicks <= 0)
        return;
        
    m_ChangeRadiationTickMark++;
    if (m_ChangeRadiationTickMark >= m_ChangeRadiationTicks)
    {
        m_ChangeRadiationTicks = 0;
        m_ChangeRadiationTickMark = 0;
        m_ChangeRadiationValue = 0.0f;
        return;
    }
    
    // 应用辐射变化值
    m_RadiationValue += m_ChangeRadiationValue;
    if (m_RadiationValue < 0.0f)
    {
        m_RadiationValue = 0.0f;
    }
}

void RadiationComponent::ChangeRadiation(float value, int interval, bool isSafeRadiation)
{
    // 设置辐射变化
    m_IsSafeRadiation = isSafeRadiation;
    m_ChangeRadiationValue = value / interval;
    m_ChangeRadiationTicks = interval;
    m_ChangeRadiationTickMark = 0;
}

void RadiationComponent::UpdateRadiationImpact()
{
    // 更新辐射影响持续时间
    for (auto it = m_RadiationImpacts.begin(); it != m_RadiationImpacts.end();)
    {
        // 更新计时
        it->TickMark++;
        
        // 检查是否到期
        if (it->LimitTime > 0 && it->TickMark >= it->LimitTime)
        {
            // 移除Buff
            it = m_RadiationImpacts.erase(it);
        }
        else
        {
            ++it;
        }
    }
}

void RadiationComponent::AddRadiationImpact(RadiationImpactDuration radiationImpact)
{
    // 添加辐射影响持续时间
    for (auto& impact : m_RadiationImpacts)
    {
        if (impact.BuffId == radiationImpact.BuffId)
        {
            // 已存在，更新
            impact = radiationImpact;
            return;
        }
    }
    
    // 添加新的影响
    m_RadiationImpacts.push_back(radiationImpact);
    
    // 应用相关Buff
}

void RadiationComponent::RemoveRadiationImpact(int buffId)
{
    // 移除指定影响
    for (auto it = m_RadiationImpacts.begin(); it != m_RadiationImpacts.end();)
    {
        if (it->BuffId == buffId)
        {
            // 移除相关Buff
            it = m_RadiationImpacts.erase(it);
        }
        else
        {
            ++it;
        }
    }
}

void RadiationComponent::PlayerShake()
{
    // 玩家震动效果
    m_IsPlayerShake = true;
    
    // 实际实现应调用相关的震动效果API
}

bool RadiationComponent::PlayerCameraCanMove()
{
    return m_IsPlayerCameraCanMove;
}

void RadiationComponent::UpdatePauseCurrentFrame()
{
    if (!m_ChangeIsPauseCurrentFrame)
        return;
        
    m_ChangeIsPauseCurrentFrame = false;
    
    // 实现暂停当前帧的功能
    // 实际实现应调用相关的暂停API
}

void RadiationComponent::PauseCurrentFrame(bool isPause)
{
    m_PauseCurrentFrame = isPause;
    m_ChangeIsPauseCurrentFrame = true;
}

void RadiationComponent::GetTextureAlpha()
{
    // 获取纹理Alpha值
    m_TextureAlpha = m_BuffTextureAlpha[m_CurrentBuff];
}

void RadiationComponent::RenderUI()
{
	if (m_Texture)
	{
		ScreenManager& screenManager = GetScreenManager();
		int ScreenWidth = screenManager.GetWidth();
		int ScreenHeight = screenManager.GetHeight();
		GetTextureAlpha();
		UIRenderer::GetInstance().BeginDraw(m_TextureMaterial, m_Texture);
		UIRenderer::GetInstance().StretchRect(0, 0, (float)ScreenWidth, (float)ScreenHeight, ColorRGBA32(255, 255, 255, m_TextureAlpha), m_Rect.origin.x, m_Rect.origin.y, m_Rect.size.width, m_Rect.size.height);
		UIRenderer::GetInstance().EndDraw();
	}
}
