
#ifndef __CLIENTACTORTHROWABLE_H__
#define __CLIENTACTORTHROWABLE_H__

#include "ClientActor.h"

class BaseItemMesh;

class ClientActorThrowable : public ClientActor //tolua_exports
{ //tolua_exports
	DECLARE_SCENEOBJECTCLASS(ClientActorThrowable)
public:
	//tolua_begin
	ClientActorThrowable();

	void init(int itemid);

	static ClientActorThrowable *throwItem(World *pworld, ClientActor *shootactor, int itemid);
	static ClientActorThrowable *throwItemAuto(World *pworld, int itemid, const WCoord &pos, const Rainbow::Vector3f &dir, float speed, float deviation, long long shooterObjId);

	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool load(const void *srcdata, int version) override;

	//virtual void onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum);
	virtual void update(float dtime);
	virtual int getObjType()const override;
	virtual void onCollideWithPlayer(ClientActor *player) override;

	void setShootingActor(ClientActor *actor);
	virtual ClientActor *getShootingActor();

	bool NeedTickForever() override {
		return true;
	}

	void onImpact(ClientActor *impact_actor, const WCoord *blockpos);
	//tolua_end
public:
	//tolua_begin
	float m_BaseAtk;
	//tolua_end
protected:
	virtual ~ClientActorThrowable();

	BaseItemMesh *m_Model;
	WORLD_ID m_ShootingActorID;
	int m_ItemID;
}; //tolua_exports

#endif