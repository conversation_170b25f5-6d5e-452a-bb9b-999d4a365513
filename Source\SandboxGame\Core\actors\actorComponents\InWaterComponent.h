#ifndef __ACTOR_IN_WATER_H__
#define __ACTOR_IN_WATER_H__

//#include "OgreWCoord.h"
#include "ActorComponent_Base.h"


class ClientActor;
class ClientPlayer;
class ActorHorse;

class World;

class InWaterComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(InWaterComponent)
public:
	//tolua_begin
	InWaterComponent();
	InWaterComponent(ClientActor *p)
	{

	}
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnBeginPlay() override;

	virtual ClientPlayer* GetOwnerClientPlayer() override;
	virtual ClientActor* GetOwnerClientActor() override;
	//tolua_end
protected:	
	virtual void onEnterWater() {};
	void playEnterWaterSound();
protected:
	bool m_IsInWaterLastTick;
}; //tolua_exports

class InWaterComponent_Horse : public InWaterComponent //tolua_exports
{ //tolua_exports
    DECLARE_COMPONENTCLASS(InWaterComponent_Horse)
protected:	
	virtual void onEnterWater() override;
}; //tolua_exports

class InWaterComponent_Player : public InWaterComponent //tolua_exports
{ //tolua_exports
    DECLARE_COMPONENTCLASS(InWaterComponent_Player)
protected:	
	virtual void onEnterWater() override;
}; //tolua_exports
#endif