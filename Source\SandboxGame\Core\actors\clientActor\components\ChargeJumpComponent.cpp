
#include "ChargeJumpComponent.h"
#include "world_types.h"
#include "ClientActor.h"
#include "world.h"
#include "ClientPlayer.h"
#include "PlayerAttrib.h"
#include "PlayerLocoMotion.h"
#include "ActorBodySequence.h"
#include "InputInfo.h"
#include "MusicManager.h"
#include "PlayerControl.h"
//#include <utility/obj_parser.h>

using namespace MINIW;

#define MIN_SPEED 40
#define MAX_CHARGE_SPEED 160
IMPLEMENT_COMPONENTCLASS(ChargeJumpComponent)
ChargeJumpComponent::ChargeJumpComponent()
{
	m_CurCharge = -1;
	m_nCurChargeJumpSpeed = -1;
	m_minRebounceSpeed = 5.f;
	m_curCameZoom = false;
}

ChargeJumpComponent::~ChargeJumpComponent()
{

}

void ChargeJumpComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	ClientActor* pOwnerActor = dynamic_cast<ClientActor*>(owner);
	if (pOwnerActor)
	{
		pOwnerActor->BindChargeJumpComponent(this);
	}
	
	m_minRebounceSpeed = 20.f;
}

void ChargeJumpComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	ClientActor* pOwnerActor = dynamic_cast<ClientActor*>(owner);
	if (pOwnerActor)
	{
		pOwnerActor->BindChargeJumpComponent(nullptr);
	}
}

void ChargeJumpComponent::startCharge()
{
	if (m_CurCharge >= 0)
		return;
	
	m_CurCharge = 0;
	m_nCurChargeJumpSpeed = m_CurCharge;
}

void ChargeJumpComponent::endCharge()
{
	if (m_CurCharge >= m_nCurChargeJumpSpeed)
	{
		m_nCurChargeJumpSpeed = m_CurCharge;
	}

	if (m_nCurChargeJumpSpeed > 0 && m_nCurChargeJumpSpeed < MIN_SPEED)
	{
		m_nCurChargeJumpSpeed = MIN_SPEED;
	}

	if (m_nCurChargeJumpSpeed < 0)
		return;

	if (m_CurCharge < 0)
		return;
	if (!GetOwner()) return ;
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!player) return ;

	LivingLocoMotion* locmotion = dynamic_cast<LivingLocoMotion*>(player->getLocoMotion());
	bool bOnGround = locmotion && locmotion->m_OnGround;
	if (!bOnGround) //落到方块上再回弹
		return;

	//弹射
	bounce();
}

void ChargeJumpComponent::bounce()
{
	if (m_nCurChargeJumpSpeed <= 0)
		return;
	
	ClientActor* actor = static_cast<ClientActor*>(GetOwnerActor());
	if (!actor)
		return;

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
	
	if (player) //玩家
	{
		PlayerLocoMotion* locmotion = dynamic_cast<PlayerLocoMotion*>(player->getLocoMotion());
		if (locmotion)
		{
			player->getLocoMotion()->m_Motion.y = m_nCurChargeJumpSpeed;
			player->playAnim(SEQ_JUMP);
			Rainbow::GetMusicManager().PlaySound("sounds/blocks/mushroom_duang.ogg", player->getPosition().toVector3(), 1.0f, 1.0f);
		}
	}
	else
	{ //其他生物
		ClientActor* actor = GetOwner()->ToCast<ClientActor>();
		if (!actor) return ;
		ActorLocoMotion* locmotion = dynamic_cast<ActorLocoMotion*>(actor->getLocoMotion());
		if (locmotion)
		{
			locmotion->m_Motion.y = m_nCurChargeJumpSpeed;
			actor->playAnim(SEQ_JUMP);
			Rainbow::GetMusicManager().PlaySound("sounds/blocks/mushroom_duang.ogg", actor->getPosition().toVector3(), 1.0f, 1.0f);
		}
	}

	m_CurCharge = -1;
}

void ChargeJumpComponent::resetBounceState()
{
	m_CurCharge = -1;
	m_nCurChargeJumpSpeed = -1;
}

void ChargeJumpComponent::tickCharge()
{
	if (m_CurCharge < 0) //还在弹跳过程中不给蓄力
		return;
	
	//跳跃蓄力
	if (m_CurCharge >= 0)
	{
		m_CurCharge += MAX_CHARGE_SPEED / (MIN_SPEED);
		if (m_CurCharge >= MAX_CHARGE_SPEED)
		{
			m_CurCharge = MAX_CHARGE_SPEED;
		}
	}
}

void ChargeJumpComponent::rebounce(float movY, float fallDist)
{
	float rebounceSpeed = abs(movY) * (Sqrt(fallDist / 2900.f) + 0.65f);
	//MINIW::ScriptVM::game()->callFunction("BlockVoidMushroom_GetRebounceSpeed", "ff>f", abs(movY), fallDist, &rebounceSpeed);

	//LOG_INFO("qiwi test rebounce speed:%f", rebounceSpeed);
	m_nCurChargeJumpSpeed = rebounceSpeed;
	
	if (m_nCurChargeJumpSpeed >= m_minRebounceSpeed)
	{
		bounce();
	}
	else
	{
		resetBounceState();
		return;
	}
}

bool ChargeJumpComponent::canRebounce()
{
	return m_nCurChargeJumpSpeed > 0;
}

bool ChargeJumpComponent::isChargeJumpEnabled()
{
	if (!GetOwner()) return false;
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!player) return false;

	LivingLocoMotion* locmotion = dynamic_cast<LivingLocoMotion*>(player->getLocoMotion());
	bool bOnGround = locmotion && locmotion->m_OnGround;
	if (bOnGround )
	{
		if(player->isStandOnVoidMushroom())
			return true;
	}

	return false;
}

void ChargeJumpComponent::updateChargeCameraZoom(GameCamera* camera, bool bChargeRelease)
{
	if (bChargeRelease)
	{
		m_curCameZoom = true;
		camera->setZoomInOut(camera->getFov() - 15, 2 * 20, 6);
	}
	else
	{
		m_curCameZoom = false;
		camera->disableZoom();
	}
}

bool ChargeJumpComponent::isChargeCameraZoom()
{
	return m_curCameZoom;
}

float ChargeJumpComponent::getChargeProgress()
{
	int t = m_nCurChargeJumpSpeed;
	if (t < 0) t = 0;
	return float(t) / MAX_CHARGE_SPEED;
}

void ChargeJumpComponent::updateChargeState(bool b)
{
	if (b)
	{
		startCharge();
	}
	else
	{
		endCharge();
	}
}

float ChargeJumpComponent::getCurCharge()
{
	return m_CurCharge;
}

float ChargeJumpComponent::getMaxChargeSpeed()
{
	return MAX_CHARGE_SPEED;
}

void ChargeJumpComponent::updatePlayerChargeJump(bool jump)
{
	bool isChargeEnable = isChargeJumpEnabled();
	if (isChargeEnable)
	{
		updateChargeState(jump); //更新蓄力状态
	}

	ClientPlayer* p = static_cast<ClientPlayer*>(GetOwnerPlayer());
	if (g_pPlayerCtrl && g_pPlayerCtrl->m_pCamera && p && p->getUin() == g_pPlayerCtrl->getUin())
	{
		GameCamera* pCamera = g_pPlayerCtrl->m_pCamera;
		if (isChargeEnable)
		{
			updateChargeCameraZoom(pCamera, jump); //更新镜头缩放
		}
		else if (isChargeCameraZoom())
		{
			updateChargeCameraZoom(pCamera, false); //更新镜头缩放
		}
	}
}