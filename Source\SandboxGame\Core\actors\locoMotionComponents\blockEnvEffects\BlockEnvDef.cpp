#include "BlockEnvDef.h"

#include "BlockEnvEffectBase.h"
#include "BlockRangeCheckBase.h"
#include "ClientActor.h"

#include "SandboxContext.h"

using namespace MNSandbox;

bool BlockEnvST::execute(ClientActor* owner)
{

	SandboxContext context;
	if (pRange && pRange->isInRange(owner, pEffect)) {
		if (pEffect)
			//context.SetData_Usertype<ClientActor>("owner", owner);
			pEffect->executeEffect(owner);
		return true;
	}
	return false;
}