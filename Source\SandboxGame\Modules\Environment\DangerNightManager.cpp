#include "DangerNightManager.h"
#include "IPlayerControl.h"
#include "WorldManager.h"
#include "EffectManager.h"
#include "world.h"
#include "SkyPlane.h"
#include "WorldRender.h"
#include "Environment.h"
#include "SummonMonsterSiegeMgr.h"
#include "BlockMaterialMgr.h"
#include "SandboxIdDef.h"
#include "coreMisc.h"

const int hour_20 = 14000;
const int hour_5  = 23000;

DangerNightManager::DangerNightManager()
{
}

DangerNightManager::~DangerNightManager()
{
}

DangerNightManager* DangerNightManager::create()
{
	if (g_WorldMgr)
	{
		DangerNightManager* mgr = ENG_NEW(DangerNightManager)();
		g_WorldMgr->addManager("DangerNightMgr", mgr);
		mgr->init();
		return mgr;
	}

	return nullptr;
}

void DangerNightManager::init()
{
	m_Active = false;
	m_IsEnable = true;
	m_tickTimer = 0;

	m_effectPlayBlocks.clear();
	m_effectPlayBlocks.push_back(29);
	m_effectPlayBlocks.push_back(100);
	m_effectPlayBlocks.push_back(233);
	m_effectPlayBlocks.push_back(25);
	m_effectPlayBlocks.push_back(104);
	m_effectPlayBlocks.push_back(3);
	m_effectPlayBlocks.push_back(123);

	initVoidMonsterDef();
}

void DangerNightManager::onDestroy()
{
	DangerNightManager* mgr = this;
	OGRE_DELETE(mgr);
}

const int leafNeedMax = 8;
int leafNeedSet[leafNeedMax] = { 218, 219, 220, 221, 223, 255, 395, 1717 };
static bool s_isVoidNightTipsEverShown = false;

void DangerNightManager::onTick()
{
	if (!m_Active)
	{
		return;
	}
	else
	{
		bool isHoursInRange = checkHours();
		if (isHoursInRange)
		{
			// 夜晚开启
			if (!m_IsNight)
			{
				setIsDangerNight(true);
			}
		}
		else 
		{
			// 进入白天
			if (m_IsNight)
			{
				setIsDangerNight(false);
			}

			// 如果从虚空之夜回到白天，再改回普通天气
			if (m_VoidNightSkyLerp)
			{
				tickSky(false);
			}

			// 如果是当天要出现虚空之夜，播放前置特效
			// 每四天开启虚空之夜
			int day = g_WorldMgr->getWorldTimeDay();
			if (isVoidNightDay(day))
			{
				if (!m_IsPreVoidNight)
				{
					m_IsPreVoidNight = true;
					s_isVoidNightTipsEverShown = false;
					m_particleEffectTickCurMax = m_particleEffectTickMax;
				}
			}
			else
			{
				m_IsPreVoidNight = false;
			}

			if (m_IsPreVoidNight)
			{
				m_tickTimer += 1;
				tickNormalNightEffect(0);

				int hour = (int)(g_WorldMgr->getHours());
				// 虚空之夜的这一天的8点钟要给个提示
				if(m_voidNightHintTime == hour && !s_isVoidNightTipsEverShown)
				{
					s_isVoidNightTipsEverShown = true;
					if (g_WorldMgr && (g_WorldMgr->isSurviveMode() || g_WorldMgr->isCreateRunMode()))
					{
						MINIW::ScriptVM::game()->callString("GetInst('BotConversationsMgr'):GetInst():TriggerForce(4000001)");
					}
				}
			}

			// 白天标志位不应是true修正一下
			if (m_IsVoidNight)
			{
				m_IsVoidNight = false;
			}

			//叶子变色需求暂时不上了
			//规避一些会设置时间的特殊情况
			/*if (!m_LeafNormalFormatSet)
			{
				int time = g_WorldMgr->getWorldTimeDay();
				if (time >= 0 && time < hour_20)
				{
					bool haveNull = false;
					for (int i = 0; i < leafNeedMax; i++)
					{
						auto mtl = g_BlockMtlMgr.getMaterial(leafNeedSet[i]);
						if (mtl != NULL)
						{
							if (mtl->getDefaultMtl())
							{
								mtl->getDefaultMtl()->setLeafColor(0);
							}
							else
							{
								haveNull = true;
							}
						}
					}
					if (!haveNull)
					{
						m_LeafNormalFormatSet = true;
						m_LeafVoidFormatSet = false;
					}
				}
			}*/
			return;
		}
	}

	// 进入夜晚
	m_tickTimer += 1;
	float times = g_WorldMgr->getHours();
	updateEffectState();

	// 第一次虚空之夜是第二天，随后每四天开启虚空之夜
	int day = g_WorldMgr->getWorldTimeDay();
	if (isVoidNightDay(day))
	{
		if (!m_IsVoidNight)
		{
			m_IsVoidNight = true;
			m_VoidNightSkyLerp = true;
			m_VoidNightLerpRate = 0.0f;

			//重置怪物攻城
			if (GetIPlayerControl())
			{
				World* pWorld = GetIPlayerControl()->getIWorld();
				if (pWorld)
				{
					SummonMonsterSiegeMgr* pMonsterSiegeMgr = dynamic_cast<SummonMonsterSiegeMgr*>(pWorld->GetMonsterSiegeMgr());
					if (pMonsterSiegeMgr)
					{
						pMonsterSiegeMgr->ResetSummonMonsterSiege();
					}
				}
			}
		}
	}
	else
	{
		if (m_IsVoidNight)
		{
			m_IsVoidNight = false;
			m_VoidNightLerpRate = 1.0f;
			tickSky(false);
		}
	}

	//叶子变色需求暂时不上了
	//叶子变化颜色
	//还是在第一天
	//if (m_IsVoidNight)
	//{
	//	int time = g_WorldMgr->getWorldTimeDay();
	//	if (!m_LeafVoidFormatSet && time < hour_5)
	//	{
	//		if (time >= hour_20 && abs(time - m_LeafLastTick) > 20)
	//		{
	//			float lerp = 0;
	//			int dis = time - hour_20;
	//			//从20:00开始算的
	//			if (dis <= 0)
	//			{
	//				//lerp = 0;
	//			}
	//			else if (dis >= 1000)
	//			{
	//				lerp = 1;
	//				m_LeafVoidFormatSet = true;
	//				m_LeafNormalFormatSet = false;
	//			}
	//			else
	//			{
	//				lerp = dis / 1000.f;
	//			}
	//			m_LeafLastTick = time;
	//			for (int i = 0; i < leafNeedMax; i++)
	//			{
	//				auto mtl = g_BlockMtlMgr.getMaterial(leafNeedSet[i]);
	//				if (mtl != NULL)
	//				{
	//					mtl->getDefaultMtl()->setLeafColor(lerp);
	//				}
	//			}
	//		}
	//	}
	//	else if (!m_LeafNormalFormatSet)
	//	{
	//		int time = g_WorldMgr->getWorldTimeDay();
	//		if (time >= hour_5 && abs(time - m_LeafLastTick) > 20)
	//		{
	//			float lerp = 1;
	//			int dis = time - hour_5;
	//			//从5:00开始算的
	//			if (dis <= 0)
	//			{
	//				//lerp = 0;
	//			}
	//			else if (dis >= 960)
	//			{
	//				lerp = 0;
	//				m_LeafVoidFormatSet = true;
	//				m_LeafVoidFormatSet = false;
	//			}
	//			else
	//			{
	//				lerp = 1 - (dis / 1000.f);
	//			}
	//			m_LeafLastTick = time;
	//			for (int i = 0; i < leafNeedMax; i++)
	//			{
	//				auto mtl = g_BlockMtlMgr.getMaterial(leafNeedSet[i]);
	//				if (mtl != NULL)
	//				{
	//					mtl->getDefaultMtl()->setLeafColor(lerp);
	//				}
	//			}
	//		}
	//	}
	//}

	if (!m_CanTriggeringMonsterSiege && day >= 30)
	{
		m_CanTriggeringMonsterSiege = true;
	}

	tickNormalNightEffect(m_CurEffectState);

	if (m_VoidNightSkyLerp)
	{
		tickSky(true);
	}
	//TickVoidNight();
}

void DangerNightManager::onGameModeChange(int iGameMode)
{
	// worldMgr创造模式转冒险也开启
	if (g_WorldMgr && (g_WorldMgr->isSurviveMode() || g_WorldMgr->isCreateRunMode()))
	{
		setActive(true);
	}
}

void DangerNightManager::onEnterWorld(MNSandbox::Object* pWorld)
{
	if (g_WorldMgr && (g_WorldMgr->isAdventureMode()))
	{
		setActive(true);
	}
}

void DangerNightManager::onLeaveWorld()
{
	setActive(false);
}

void DangerNightManager::setActive(bool flag)
{
	if (!m_IsEnable)
	{
		m_Active = false;
		m_IsNight = false;
		return;
	}

	m_Active = flag;
	if (m_Active)
	{
	}
	else
	{
		m_IsNight = false;
	}
}

void DangerNightManager::setIsEanble(bool flag, bool isActive /* = true */)
{
	m_IsEnable = flag;
	setActive(isActive);
}

void DangerNightManager::setFogRangeInfo(float fogStartStart, float fogEndStart, float fogStartEnd, float fogEndEnd)
{
	m_fogCurStartRange = fogStartStart;
	m_fogCurEndRange = fogEndStart;
	m_fogStartRangeStart = fogStartStart;
	m_fogStartRangeEnd = fogStartEnd;
	m_fogEndRangeStart = fogEndStart;
	m_fogEndRangeEnd = fogEndEnd;
}

bool DangerNightManager::canStartMonsterSiege()
{
	// 虚空之夜才开启攻城
	if (!m_IsVoidNight)
	{
		return false;
	}

	// 0时以后才开启攻城
	bool checkTimeFlag = false;
	if (g_WorldMgr)
	{
		int hour = g_WorldMgr->getHours();
		if (hour >= m_monsterSiegeStartHour && hour < m_endHour)
		{
			checkTimeFlag = true;
		}
	}

	return checkTimeFlag;
}

void DangerNightManager::initVoidMonsterDef()
{
	m_voidNightMonsters.clear();
	char pTextNew[512] = "";
	ScriptVM::game()->callFunction("GetVoidMonsterDef", ">s", pTextNew);
	char* pTok = strtok(pTextNew, ",");
	if (!pTok)
	{
		return;
	}

	int id = atoi(pTok);
	m_voidNightMonsters.push_back(id);
	while (pTok)
	{
		pTok = strtok(NULL, ",");
		if (pTok)
		{
			id = atoi(pTok);
			m_voidNightMonsters.push_back(id);
		}
	}

	m_voidNightMonsterGenWeights.clear();
	ScriptVM::game()->callFunction("GetVoidMonsterWeightDef", ">s", pTextNew);
	pTok = strtok(pTextNew, ",");
	if (!pTok)
	{
		return;
	}

	id = atoi(pTok);
	m_voidNightMonsterGenWeights.push_back(id);
	while (pTok)
	{
		pTok = strtok(NULL, ",");
		if (pTok)
		{
			id = atoi(pTok);
			m_voidNightMonsterGenWeights.push_back(id);
		}
	}
}

void DangerNightManager::tickNormalNightEffect(int effectState)
{
	// 每隔一段时间选区附近指定方块播放特效
	if (m_tickTimer % m_particleEffectTickCurMax == 0 &&
		(m_IsVoidNight || m_IsPreVoidNight))
	{
		playParticleEffectForNight();
	}

	if (!m_IsPreVoidNight)
	{
		// 氛围加深
		if (effectState == 1)
		{
			m_fogCurStartRange = Rainbow::Lerp(m_fogStartRangeStart, m_fogStartRangeEnd, m_fogLerpAlpha);
			if (m_fogCurStartRange <= m_fogStartRangeEnd)
			{
				m_fogCurStartRange = m_fogStartRangeEnd;
			}

			m_fogCurEndRange = Rainbow::Lerp(m_fogEndRangeStart, m_fogEndRangeEnd, m_fogLerpAlpha);
			if (m_fogCurEndRange <= m_fogEndRangeEnd)
			{
				m_fogCurEndRange = m_fogEndRangeEnd;
			}
		}
		// 氛围减弱
		else if (effectState == 2)
		{
			m_fogCurStartRange = Rainbow::Lerp(m_fogStartRangeEnd, m_fogStartRangeStart, m_fogLerpAlpha);
			if (m_fogCurStartRange >= m_fogStartRangeStart)
			{
				m_fogCurStartRange = m_fogStartRangeStart;
			}

			m_fogCurEndRange = Rainbow::Lerp(m_fogEndRangeEnd, m_fogEndRangeStart, m_fogLerpAlpha);
			if (m_fogCurEndRange >= m_fogEndRangeStart)
			{
				m_fogCurEndRange = m_fogEndRangeStart;
			}
		}
		else
		{
			m_fogCurStartRange = m_fogStartRangeEnd;
			m_fogCurEndRange = m_fogEndRangeEnd;
		}
	}

	tickEffectList();
}

void DangerNightManager::tickEffectList()
{
	for (int i = m_effectList.size() - 1; i >= 0; i--)
	{
		m_effectList[i].tick -= 1;
		if (m_effectList[i].tick <= 0)
		{
			m_effectList.erase(m_effectList.begin() + i);
		}
	}
}

void DangerNightManager::tickSky(bool isEnter /* = true */)
{
	if (!GetIPlayerControl() || !GetIPlayerControl()->getIWorld() ||
		!GetIPlayerControl()->getIWorld()->getRender())
	{
		return;
	}

	WorldRenderer *pRenderer = GetIPlayerControl()->getIWorld()->getRender();
	Rainbow::SkyPlane* sky = pRenderer->getSky();
	if (!sky)
	{
		m_VoidNightSkyLerp = false;
		return;
	}

	if (sky)
	{
		// 下一天气不再是虚空之夜或者晴天，证明实际的天气有变化，不再lerp
		if (sky->GetNextWeatherType() != Rainbow::VOID_NIGHT &&
			sky->GetNextWeatherType() != Rainbow::WEATHER_SUNSHINE)
		{
			m_VoidNightLerpRate = 1.0f;
			m_VoidNightSkyLerp = false;
			return;
		}
		
		Rainbow::WeatherType nextWeather = isEnter ? Rainbow::VOID_NIGHT : Rainbow::WEATHER_SUNSHINE ;
		sky->SetTerrainWeatherLerp(m_VoidNightLerpRate, nextWeather);
		m_VoidNightLerpRate += 0.01f;
		//m_lightFactor = m_weatherLerpChange * factor;
		if (m_VoidNightLerpRate > 0.99f)
		{
			m_VoidNightLerpRate = 1.0f;
			sky->SetWeatherType(nextWeather);
			sky->setSkyMoonVisible(!isEnter);
			sky->SetTerrainWeatherLerp(0.f, nextWeather);
			m_VoidNightSkyLerp = false;
		}
	}
}

bool DangerNightManager::checkHours()
{
	if (g_WorldMgr)
	{
		int hour = g_WorldMgr->getHours();
		if (hour >= m_startHour || hour < m_endHour)
		{
			return true;
		}
	}

	return false;
}

void DangerNightManager::setIsDangerNight(bool flag)
{
	if (flag && m_IsEnable)
	{
		m_IsNight = true;
		m_IsPreVoidNight = false;

		// 尝试获取雾效距离数据
		if (GetIPlayerControl() && GetIPlayerControl()->getIWorld())
		{
			World* pWorld = GetIPlayerControl()->getIWorld();
			if (pWorld->m_Environ)
			{
				float start = -1.0f;
				float end = -1.0f;
				pWorld->m_Environ->getBiomeFogRangeByLv(0, start, end);
				setFogRangeInfo(pWorld->m_Environ->m_BiomeFogStart, pWorld->m_Environ->m_BiomeFogEnd,
					start, end);
			}
		}

		m_tickTimer = 0;
		m_preMinute = 0;
		m_particleEffectTickCurMax = m_particleEffectTickMax;
	}
	else
	{
		m_IsNight = false;

		if (m_IsVoidNight)
		{
			m_VoidNightSkyLerp = true;
			m_VoidNightLerpRate = 0.0f;
		}

		// 白天虚空之夜也关闭
		m_IsVoidNight = false;

		m_tickTimer = 0;
		m_effectList.clear();
	}
}

void DangerNightManager::playParticleEffectForNight()
{
	if (GetIPlayerControl())
	{
		// 高度上从上往下，水平方向从玩家位置向四周搜索，合适的位置播放特效
		bool hasPlay = false;
		WCoord playerPos = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
		Rainbow::Vector3f playerLookAt = GetIPlayerControl()->getLookDir();
		World* pWorld = GetIPlayerControl()->getIWorld();

		int dis = particleEffectRange.x;
		for (int i = playerPos.x - particleEffectRange.x; i < playerPos.x + particleEffectRange.x; i += dis)
		{
			for (int j = playerPos.z - particleEffectRange.z; j < playerPos.z + particleEffectRange.z; j += dis)
			{
				WCoord cpos = WCoord(0, 0, 0);
				cpos.x = i + GenRandomInt(0, 8);
				cpos.z = j + GenRandomInt(0, 8);
		
				// 同一个水平位置，只能播一个特效
				bool isRepeat = false;
				for (size_t i = 0; i < m_effectList.size(); i++)
				{
					WCoord effectPos = m_effectList[i].pos;
					float dist = (cpos.x * cpos.x + cpos.z * cpos.z) - (effectPos.x * effectPos.x + effectPos.z * effectPos.z);

					if (dist <= 16)
					{
						isRepeat = true;
						break;
					}
				}

				if (isRepeat)
				{
					continue;
				}

				cpos.y = playerPos.y + 3;
				if (!pWorld->getBlock(cpos).isAir())//超过玩家高度的方块不播放特效
					continue;
				for (int y = playerPos.y + 2; y >= playerPos.y - 6; y--)
				{
					cpos.y = y;

					hasPlay = processOneBlockForPlayEffect(pWorld, cpos);
					//hasPlay = processOneBlockForPlayEffect(pWorld, playerPos);
					if (hasPlay) return;
				}
			}
		}
	}
}

void DangerNightManager::playParticleEffect(World* pWorld, const WCoord& blockPos, const char* effectStr)
{
	if (!pWorld || ! effectStr)
	{
		return;
	}

	WCoord pos = blockPos *BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2);
	pWorld->getEffectMgr()->playParticleEffectCommonAsync(5, effectStr, pos, m_particleEffectTickMax, 0, 0, true,
		16, 0, Rainbow::ColorQuad(255, 255, 255, 255), NULL, true);

	WCoord tempPos = blockPos;
	tempPos.y -= 1;
	DangerNightEffectInfo info;
	info.pos = tempPos;
	info.tick = m_particleEffectTickMax;
	m_effectList.push_back(info);
}

bool DangerNightManager::processOneBlockForPlayEffect(World* pWorld, const WCoord & blockPos)
{
	int nBlockID = pWorld->getBlockID(blockPos);
	// 保证上方没有方块
	if (nBlockID > 0)
	{
		WCoord upBlockPos = blockPos;
		upBlockPos.y += 1;
		for (int targetId : m_effectPlayBlocks)
		{
			if (targetId == nBlockID)
			{
				// 针对虚空之夜播放更强力的特效
				// 普通夜晚的特效暂时取消
				//if (!m_IsVoidNight)
				//{
					const char* effect = "particles/void_spread.ent";
					playParticleEffect(pWorld, upBlockPos, effect);
				//}
				//else
				//{
				//	const char* effect = "particles/void_spread_entity.ent";
				//	playParticleEffect(pWorld, upBlockPos, effect);
				//}
				return true;
			}
		}
	}

	return false;
}


void DangerNightManager::updateEffectState()
{
	m_CurEffectState = 0;
	if (!g_WorldMgr)
	{
		return;
	}

	float times = g_WorldMgr->getHours();
	int hour = (int)times;
	int minute = (times - hour) * 60;
	// 氛围加深
	if (hour >= m_startHour && hour <= m_effectStrongerEndHour)
	{
		m_CurEffectState = 1;
		// 每半小时改变一次特效播放间隔
		if (m_preMinute != minute && (minute == 0 || minute == 30))
		{
			int num = (hour - m_startHour) * 2 + (minute == 30 ? 1 : 0);
			m_particleEffectTickCurMax = m_particleEffectTickMax - m_particleEffectStrongerDeltaTick * num;
		}

		m_fogLerpAlpha = (hour - m_startHour + minute / 60.0f) / (m_effectStrongerEndHour - m_startHour);
	}
	// 氛围减弱
	else if (hour >= m_effectWeakerStartHour && hour <= m_endHour)
	{
		// 持续获取外部的雾效距离保证 夜晚结束时能够对接上
		updateFogInfo();

		m_CurEffectState = 2;
		// 每半小时改变一次特效播放间隔
		if (m_preMinute != minute && (minute == 0 || minute == 30))
		{
			int num = (m_endHour - hour) * 2 - (minute == 30 ? 1 : 0);
			m_particleEffectTickCurMax = m_particleEffectTickMax - m_particleEffectWeakerDeltaTick * num;
		}
		else
		{
			m_particleEffectTickCurMax = m_particleEffectTickStableMax;
		}

		m_fogLerpAlpha = (hour - m_effectWeakerStartHour + minute / 60.0f) / (m_endHour - m_effectWeakerStartHour);
	}
	else
	{
		m_particleEffectTickCurMax = m_particleEffectTickStableMax;
	}

	// 容错处理
	if (m_particleEffectTickCurMax <= 0 )
	{
		m_particleEffectTickCurMax = m_particleEffectTickMax;
	}

	m_preMinute = minute;
}

void DangerNightManager::updateFogInfo()
{
	// 尝试获取雾效距离数据
	if (GetIPlayerControl() && GetIPlayerControl()->getIWorld())
	{
		World* pWorld = GetIPlayerControl()->getIWorld();
		if (pWorld->m_Environ)
		{
			m_fogStartRangeStart = pWorld->m_Environ->m_BiomeFogStart;
			m_fogEndRangeStart = pWorld->m_Environ->m_BiomeFogEnd;
			//pWorld->m_Environ->getBiomeFogRange(m_fogStartRangeStart, m_fogEndRangeStart);
		}
	}
}

bool DangerNightManager::isVoidNightDay(int day)
{
	if (day == m_firstVoidNightDeltaDay)
	{
		return true;
	}
	else if (day > m_firstVoidNightDeltaDay)
	{
		return (day - m_firstVoidNightDeltaDay)%m_voidNightDeltaDay == 0;
	}

	return false;
}

//random block tick检查block 虚空之夜变异和还原
bool DangerNightManager::checkBlockMutations(World* pworld, const WCoord& blockpos)
{
	if (!g_WorldMgr || !pworld)
		return false;

	int blockid = pworld->getBlockID(blockpos);
	BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);

	if (mtl == nullptr) return false;

	int hour = g_WorldMgr->getHours();
	
	if (g_WorldMgr->isVoidNight())
	{
		return mtl->beginVoidNightMutant(pworld, blockpos); // 开始变异
	}
	else
	{
		mtl->beginVoidNightResume(pworld, blockpos);
	}

	return false;
}

bool DangerNightManager::checkBlockResume(World* pworld, const WCoord& blockpos, const int resumeID)
{
	if (!g_WorldMgr || !pworld)
		return false;

	
	if (g_WorldMgr->isVoidNight())
	{
		return false;
	}
	int blockdata = pworld->getBlockData(blockpos) & 4;
	if (blockdata > 0) //不可逆的虚空植物
	{
		return false;
	}
	
	if (resumeID )
	{
		bool isEmptyBlock = pworld->getBlockData(blockpos) & 8;
		if (!isEmptyBlock)
		{
			pworld->setBlockAll(blockpos, resumeID, 0, kBlockUpdateFlagNeedUpdate);
		}
		else
		{
			pworld->setBlockAll(blockpos, 0, 0, kBlockUpdateFlagNeedUpdate);
		}
			
		return true;
	}
	else
	{
		int blockid = pworld->getBlockID(blockpos);
		if (blockid == BLOCK_VOID_MUSHROOM || blockid == BLOCK_VOID_MUSHROOM_CAP) //虚空菇不走直接还原，走checkBlockMutations的void random tick 还原
		{
	
			if(50 >= GenRandomInt(101)) //加快还原速度，3s 检测一次，50的概率走直接还原
				return false;
		}
		
		BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
		return mtl->beginVoidNightResume(pworld, blockpos);
	}
}

void DangerNightManager::playMutateEffect(World* pworld, const WCoord& blockpos)
{
	WCoord pos = blockpos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2);
	pworld->getEffectMgr()->playParticleEffectCommonAsync(5, "particles/void_spread_entity.ent", pos, 60, 0, 0, true, 16, 0, Rainbow::ColorQuad(255, 255, 255, 255), NULL, true);
}