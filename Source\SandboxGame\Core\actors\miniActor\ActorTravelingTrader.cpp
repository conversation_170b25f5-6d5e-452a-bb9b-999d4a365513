﻿#include "ActorTravelingTrader.h"
#include "SandboxIdDef.h"
#include "ClientActorHelper.h"
#include "WorldManager.h"
#include "coreMisc.h"
#include "PlayerControl.h"
#include "BTBlackboard.h"
#include "LivingLocoMotion.h"
#include "ActorBody.h"
#include "BlockMaterialMgr.h"
#include "BlockFurniture.h"
#include "BlockBed.h"
#include "LivingAttrib.h"
#include "backpack.h"
#include "special_blockid.h"
#include "Entity/OgreEntity.h"
#include "BehaviorTreeInstance.h"
#include "SoundComponent.h"

IMPLEMENT_SCENEOBJECTCLASS(ActorTravelingTrader)

//#define Test_Test_Test

//居住等级、在家？
static constexpr auto BBkey_LV = "bbkey_housingLv", BBkey_InHome = "bbkey_inHome";

ActorTravelingTrader* ActorTravelingTrader::current_ActorTravelingTrader = nullptr;

ActorTravelingTrader* ActorTravelingTrader::Get(World* pworld)
{
#ifdef Test_Test_Test
	return nullptr;
#endif // Test_Test_Test
	
	if (!(pworld && pworld->GetWorldMgr() && pworld->getActorMgr()))
		return nullptr;

	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return nullptr;

	if (current_ActorTravelingTrader)
		return current_ActorTravelingTrader;

	WCoord pos;
	TravelingTraderInfo& info = pworld->GetWorldMgr()->GetTravelingTraderInfo();
	if (info.lastInHomePos.y >= 0)
	{
		pos = info.lastInHomePos;
	}
	else
	{
		//复活点
		pos = pworld->GetWorldMgr()->getRevivePointEx(pworld);
		if (pos.y < 0) pos = pworld->GetWorldMgr()->getSpawnPointEx(pworld);
		//20格 - 32格 范围随机
		ChunkRandGen* randGen = GetDefaultRandGen();
		int rx = pos.x + GenRandomInt(20, 32) * (randGen->nextBool() ? 1 : -1);
		int rz = pos.z + GenRandomInt(20, 32) * (randGen->nextBool() ? 1 : -1);
		pos = WCoord(rx, pos.y, rz);
		WCoord pos_down = DownCoord(pos);

		bool find = false;
		bool isEmpty = pworld->isAirBlock(pos);
		bool isEmptyBottom = pworld->isAirBlock(pos_down);
		if (isEmpty)
		{
			if (isEmptyBottom)
			{
				//往下找
				do
				{
					pos = pos_down;
					pos_down = DownCoord(pos_down);
					if (pos_down.y < 0) break;
					isEmpty = isEmptyBottom;
					isEmptyBottom = pworld->isAirBlock(pos_down);
					if (!isEmptyBottom)
					{
						find = true;
						break;
					}
				} while (true);
			}
			else
			{
				find = true;
			}
		}
		else
		{
			//往上找
			do
			{
				pos_down = pos;
				pos = TopCoord(pos);
				if (pos.y > 255) break;
				isEmptyBottom = isEmpty;
				isEmpty = pworld->isAirBlock(pos);
				if (isEmpty)
				{
					find = true;
					break;
				}
			} while (true);
		}
		if (!find)
			return nullptr;

		pos = BlockBottomCenter(pos);
	}
	ClientMob* mob = actorMgr->spawnMob(pos, 3022, false, false);
	assert(mob);
	if (mob && mob->IsKindOf<ActorTravelingTrader>())
	{
		current_ActorTravelingTrader = mob->ToCast<ActorTravelingTrader>();
		return current_ActorTravelingTrader;
	}
	return nullptr;
}

int ActorTravelingTrader::getObjType() const
{
	return OBJ_TYPE_TRAVELING_TRADER_NPC;
}

flatbuffers::Offset<FBSave::SectionActor> ActorTravelingTrader::save(SAVE_BUFFER_BUILDER& builder)
{
	auto mobdata = ClientMob::saveMob(builder);
	bool bodyShow = getBody() == nullptr ? false : getBody()->getIsShow();
	auto trader = FBSave::CreateActorTravelingTrader(builder, mobdata, m_styleId, isSleeping(), bodyShow);
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorTravelingTrader, trader.Union());
}

bool ActorTravelingTrader::load(const void* srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorTravelingTrader*>(srcdata);
	if (!src)
		return false;

	m_styleId_fromLoad = src->styleID();
	m_sleeping_fromLoad = src->sleeping();
	m_bodyShow_fromLoad = src->bodyShow();
	return ClientMob::todoLoad(src->mobdata(), version);
}

ActorTravelingTrader::ActorTravelingTrader()
{
	current_ActorTravelingTrader = this;
}

ActorTravelingTrader::~ActorTravelingTrader()
{
	current_ActorTravelingTrader = nullptr;
}

void ActorTravelingTrader::enterWorld(World* pworld)
{
	ClientMob::enterWorld(pworld);
	if (!(pworld && pworld->GetWorldMgr()))
		return;

	if (pworld->isRemoteMode())
	{
		if (IsLoadModelFinished() && getBody())
		{
			if (m_sleeping_fromLoad && getBody()->getCurAnim(0) != SEQ_LAYDOWN)
			{
				getBody()->setCurAnim(SEQ_LAYDOWN, 0);
			}
			getBody()->show(m_bodyShow_fromLoad);
		}
		else
		{
			m_WaitModelLoadingForSetModelStyle = true;
		}
	}
	else
	{
		//设置home
		WCoord pos = pworld->GetWorldMgr()->getRevivePointEx(pworld);
		if (pos.y < 0) pos = pworld->GetWorldMgr()->getSpawnPointEx(pworld);
		pos = BlockBottomCenter(pos);
		setHome(24 * BLOCK_SIZE, pos.x, pos.y, pos.z);
	}
}

void ActorTravelingTrader::leaveWorld(bool keep_inchunk)
{
	ClientMob::leaveWorld(keep_inchunk);
}

bool ActorTravelingTrader::attackedFrom(OneAttackData& atkdata, ClientActor* attacker)
{
	bool ret = ClientMob::attackedFrom(atkdata, attacker);
	if (m_pWorld && m_pWorld->isRemoteMode())
		return ret;

	SoundComponent* soundComp = getSoundComponent();
	if (soundComp)
	{
		soundComp->playSound("ent.3200.scare", 1, 1, 2);
	}
	if (m_btree)
	{
		m_btree->Reset();
		if (getSitting())
		{
			setSitting(false);
			standUp();
		}
	}
	return ret;
}

void ActorTravelingTrader::onDie()
{
	ClientMob::onDie();
	if (m_pWorld && m_pWorld->isRemoteMode())
		return;

	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret && m_pWorld && m_pWorld->getContainerMgr())
	{
		WCoord bedpos = info.bedPos;
		if (IsBindedBed(bedpos.x, bedpos.y, bedpos.z))
		{
			BedLogicHandle::setBedOccupied(m_pWorld, bedpos, false);
			WorldBed* container = dynamic_cast<WorldBed*>(m_pWorld->getContainerMgr()->getContainer(bedpos));
			if (container)
			{
				container->setBindActor(0);
				container->setBedStatus(ENUM_BED_STATUS_NO_BIND_ACOTR);
			}
		}
		info.bedPos = WCoord::zero;
		info.housingLevel = 0;
		info.inHome = false;
		info.lastTimePoint = 0;
		info.nextTimeLength = 0;
		info.hunger = 0;
		info.lastInHomePos = WCoord(0, -1, 0);
	}
}

void ActorTravelingTrader::tick()
{
	ClientMob::tick();
	if (m_WaitModelLoadingForSetModelStyle && IsLoadModelFinished())
	{
		m_WaitModelLoadingForSetModelStyle = false;
		SetModelStyle(m_styleId);
		//客机等待模型加载完
		if (m_pWorld && m_pWorld->isRemoteMode() && getBody())
		{
			if (m_sleeping_fromLoad && getBody()->getCurAnim(0) != SEQ_LAYDOWN)
			{
				getBody()->setCurAnim(SEQ_LAYDOWN, 0);
			}
			getBody()->show(m_bodyShow_fromLoad);
		}
	}

	if (m_pWorld && m_pWorld->isRemoteMode())
		return;

	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret && m_bb)
	{
		//居住等级
		int bb_housingLv;
		m_bb->GetData_Number(BBkey_LV, bb_housingLv);
		if (bb_housingLv != info.housingLevel)
		{
			BTLuaData data;
			data.SetValue_Number(info.housingLevel);
			m_bb->SetDataLua(BBkey_LV, &data);
		}
		//在家？
		BTLuaData* luaData = nullptr;
		m_bb->GetDataLua(BBkey_InHome, luaData);
		if (luaData && luaData->GetType() == BTLuaDataType_Bool)
		{
			bool bb_inhome = luaData->GetValue_Bool();
			if (bb_inhome != info.inHome)
			{
				BTLuaData data;
				data.SetValue_Bool(info.inHome);
				m_bb->SetDataLua(BBkey_InHome, &data);
			}
		}
		//在家时，更新位置
		if (info.inHome && info.lastInHomePos.y >= 0)
			info.lastInHomePos = getPosition();

		//寻找床？
		if (info.hunger == 0)
		{
			if (info.housingLevel <= 0)
				setHeadIconByPath("", "items/icon828", 60, 60);
			else
				setHeadIconByPath("", "", 0, 0);
		}
	}
}

void ActorTravelingTrader::sitInChair(const WCoord blockpos)
{
	if (!getLocoMotion()) return;
	getLocoMotion()->m_yOffset = 0;

	if (m_pWorld && m_pWorld->blockExists(blockpos))
	{
		int blockdata = m_pWorld->getBlockData(blockpos);
		int dir = blockdata & 3;
		BedLogicHandle::setBedOccupied(m_pWorld, blockpos, true);
		WCoord playerpos = blockpos * BLOCK_SIZE + WCoord(50, 60, 50);
		getLocoMotion()->setPosition(playerpos.x, playerpos.y, playerpos.z);
		float YawArray[4] = { 90.0f, -90.0f, 0.0f, 180.0f };
		getLocoMotion()->m_RotateYaw = YawArray[dir];
		getLocoMotion()->m_RotationPitch = 0;
	}
	else
	{
		WCoord playerpos = blockpos * BLOCK_SIZE + WCoord(50, 50, 50);
		getLocoMotion()->setPosition(playerpos.x, playerpos.y, playerpos.z);
		getLocoMotion()->m_RotateYaw = 0.0f;
		getLocoMotion()->m_PrevRotatePitch = 0;
	}
	setSitting(true);
	getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
	if (getBody()) getBody()->resetPos();
}

void ActorTravelingTrader::sitInBackround()
{
	if (!getLocoMotion()) return;
	getLocoMotion()->m_yOffset = 0;
	WCoord Pos = this->getPosition();
	getLocoMotion()->setPosition(Pos.x, Pos.y, Pos.z);
	getLocoMotion()->m_RotateYaw = 0.0f;
	getLocoMotion()->m_PrevRotatePitch = 0;
	setSitting(true);
	getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
	if (getBody()) getBody()->resetPos();
}

void ActorTravelingTrader::standUp()
{
	if (!getLocoMotion()) return;
	updateBound();
	getLocoMotion()->m_yOffset = 0;
	WCoord blockpos = CoordDivBlock(getPosition());
	WCoord newpos = blockpos;
	if (!m_pWorld) return;
	BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(m_pWorld->getBlockID(blockpos));
	BlockChair* chair = dynamic_cast<BlockChair*>(mtl);
	if (chair)
	{
		BedLogicHandle::setBedOccupied(m_pWorld, blockpos, false);
		bool succeed = BedLogicHandle::getNearestEmptyChunkCoordinates(newpos, m_pWorld, blockpos, 0);
		if (!succeed)
		{
			newpos = TopCoord(blockpos);
		}
		getLocoMotion()->gotoPosition(BlockBottomCenter(newpos), getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
	}
	else
	{
		newpos = TopCoord(blockpos);
		getLocoMotion()->gotoPosition(BlockBottomCenter(newpos), getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
	}
}

//获取绑定床的坐标
WCoord ActorTravelingTrader::getBedBindPos()
{
	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
	{
		return info.bedPos;
	}
	else
	{
		return WCoord::zero;
	}
}

//绑定床
void ActorTravelingTrader::setBedBindPos(WCoord pos)
{
	if (!m_pWorld) return;

	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
	{
		info.bedPos = pos;
		info.housingLevel = FindBlockNumberByType(pos.x, pos.y, pos.z, 8, 14) >= 5 ? 2 : 1;
		info.inHome = true;
		info.lastTimePoint = m_pWorld->GetWorldMgr()->getWorldTimeDay();
		info.nextTimeLength = GenRandomInt(2, 3);
	}
}

//是否是我的绑定床
bool ActorTravelingTrader::IsBindedBed(int blockPosX, int blockPosY, int blockPosZ)
{
	if (!m_pWorld) return false;

	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
	{
		WCoord bedPos = WCoord(blockPosX, blockPosY, blockPosZ);
		BlockMaterial* mtl = m_pWorld->getBlockMaterial(bedPos);
		if (!mtl || !IsBedBlock(mtl->getBlockResID()))
		{
			return false;
		}
		if (info.housingLevel > 0 && info.bedPos == bedPos)
		{
			info.housingLevel = FindBlockNumberByType(bedPos.x, bedPos.y, bedPos.z, 8, 14) >= 5 ? 2 : 1;
			return true;
		}
	}
	return false;
}

//刷新游商的上次记录的时间点
void ActorTravelingTrader::ResetTimePoint()
{
	if (!m_pWorld) return;

	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
	{
		info.lastTimePoint = m_pWorld->GetWorldMgr()->getWorldTimeDay();
	}
}

//重置居住等级0
void ActorTravelingTrader::ResetHousingLv()
{
	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
	{
		info.housingLevel = 0;
		info.bedPos.setElement(0, 0, 0);
		info.hunger = 0;
		info.lastTimePoint = 0;
		info.nextTimeLength = 0;
	}
}

//是否绑定了床位，且在家中
bool ActorTravelingTrader::IsInHomeReal()
{
	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
	{
		if (info.housingLevel > 0 && info.inHome)
			return true;
	}
	return false;
}

unsigned char ActorTravelingTrader::GetHungerState()
{
	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
	{
		return info.hunger;
	}
	return 0;
}

void ActorTravelingTrader::SetHungerState(unsigned char state, bool isDo)
{
	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
	{
		info.hunger = state;
		//赶紧吃
		if (isDo && state == 2 && info.housingLevel > 0 && info.inHome && m_btree)
		{
			m_btree->Reset();
			if (getSitting())
			{
				setSitting(false);
				standUp();
			}
		}
		//吃饱了，早点滚
		if (isDo && state == 3 && info.housingLevel > 0 && info.inHome && info.nextTimeLength > 0)
		{
			info.nextTimeLength--;
		}
	}
}

int ActorTravelingTrader::GetHoursFromeLastDo()
{
	int ret_hours = 0;
	if (m_pWorld && m_pWorld->GetWorldMgr())
	{
		WorldManager* wMgr = m_pWorld->GetWorldMgr();
		TravelingTraderInfo& info = wMgr->GetTravelingTraderInfo();
		int hours = wMgr->getHours();
		if (info.housingLevel == 0 && info.inHome)
		{
			ret_hours = hours - 7;
		}
		else
		{
			hours -= 6;
			if (hours < 0) hours += 24;
			ret_hours = (wMgr->getWorldTimeDay() - info.lastTimePoint) * 24 + hours;
		}
	}
	if (ret_hours < 0) ret_hours = 0;
	return ret_hours;
}

bool ActorTravelingTrader::GiveFood(ClientPlayer* player)
{
	if (!player)
		return false;

	auto* foodDef = GetDefManagerProxy()->getFoodDef(player->getCurToolID());
	if (!foodDef || foodDef->ChoosePriority <= 0) //不是能吃的食物
		return false;

	auto* livingAttr = player->getLivingAttrib();
	if (livingAttr)
	{
		auto* grid = livingAttr->getEquipGrid(EQUIP_WEAPON);
		if (grid && grid->getItemID() > 0)
		{
			int putNum = grid->getNum();
			if (putNum > 0)
			{
				int num = putNum - 1;
				if (num <= 0)
					grid->clear();
				else
					grid->setNum(num);

				if (player->getBody())
					livingAttr->applyEquips(player->getBody(), EQUIP_WEAPON);

				if (player->getBackPack())
				{
					int index = player->getBackPack()->getShortcutStartIndex() + player->getCurShortcut();
					player->getBackPack()->afterChangeGrid(index);
				}
				return true;
			}
		}
	}
	return false;
}

int ActorTravelingTrader::GetCurBiomeId(int& posX, int& posY, int& posZ)
{
	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
	{
		posX = info.biomePos.x;
		posY = info.biomePos.y;
		posZ = info.biomePos.z;
		return info.biome;
	}
	return -1;
}

void ActorTravelingTrader::SetModelStyle(int id)
{
	Rainbow::Entity* entity = getEntity();
	if (entity)
	{
		Rainbow::Model* pModel = entity->GetMainModel();
		if (pModel && IsLoadModelFinished())
		{
			char skname[128];
			for (int i = 1; i <= 6; i++)
			{
				sprintf(skname, "body%d", i);
				pModel->ShowSkin(skname, false);
			}
			if (id >= 1 && id <= 6)
			{
				sprintf(skname, "body%d", id);
				pModel->ShowSkin(skname, true);
			}
		}
		else
		{
			m_WaitModelLoadingForSetModelStyle = true;
			if (m_styleId_fromLoad >= 0)
			{
				m_styleId = m_styleId_fromLoad;
				m_styleId_fromLoad = -1;
			}
			else
				m_styleId = id;
		}
	}
}

WCoord ActorTravelingTrader::GetHomePos()
{
	if (getLocoMotion())
		return getLocoMotion()->getHomePosition();
	else
		return WCoord::zero;
}

WCoord ActorTravelingTrader::GetLastInHomePos()
{
	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
		return info.lastInHomePos;
	else
		return WCoord::zero;
}

void ActorTravelingTrader::EnableSyncPosition()
{
	bool ret = false;
	TravelingTraderInfo& info = GetInfo(ret);
	if (ret)
		info.lastInHomePos.y = 0;
}

//===================================================================================================

TravelingTraderInfo& ActorTravelingTrader::GetInfo(bool& ret)
{
	if (m_pWorld && m_pWorld->GetWorldMgr())
	{
		ret = true;
		return m_pWorld->GetWorldMgr()->GetTravelingTraderInfo();
	}
	if (GetWorldManagerPtr())
	{
		ret = true;
		return GetWorldManagerPtr()->GetTravelingTraderInfo();
	}
	ret = false;
	static TravelingTraderInfo temp;
	return temp;
}

int ActorTravelingTrader::FindBlockNumberByType(int posx, int posy, int posz, int range, int editType)
{
	int retNum = 0;
	if (!m_pWorld)
		return retNum;

	for (int z = posz - range; z <= posz + range; z++)
	{
		for (int x = posx - range; x <= posx + range; x++)
		{
			for (int y = posy - range; y <= posy + range; y++)
			{
				int blockId = m_pWorld->getBlockID(x, y, z);
				const ItemDef* itemDef = GetDefManagerProxy()->getItemDef(blockId);
				if (itemDef && itemDef->OriginEditType == editType)
				{
					//int blockData = m_pWorld->getBlockData(x, y, z);
					//LOG_INFO(u8"[家具数据] blockId: %d, blockData: %d", blockId, blockData);
					++retNum;
				}
			}
		}
	}
	return retNum;
}