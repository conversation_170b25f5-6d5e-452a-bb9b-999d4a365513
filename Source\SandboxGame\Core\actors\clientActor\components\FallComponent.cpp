#include "FallComponent.h"
#include "ClientActor.h"
#include "world.h"
#include "chunk.h"
#include "ClientActorManager.h"
#include "ConstAtLua.h"
#include "DefManagerProxy.h"
#include "GameModeDef.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "ClientActorFuncWrapper.h"
#include "SoundComponent.h"
#include "AttackedComponent.h"
#include "LuaInterfaceProxy.h"
#include "ClientPlayer.h"
#include "ClientActorIcicle.h"
#include "MusicManager.h"
#include "SandboxIdDef.h"
#include "ActorLocoMotion.h"
#include "ActorAttrib.h"
#include "ICloudProxy.h"
using namespace MINIW;

static std::string fallHurtSubtractEvent = "getFallHurtSubtract";
float FallComponent::getFallHurtSubtract(ClientActor *pActor)
{
	float ret = 0.0f;
	bool succ = pActor->Event2().Emit<float&>(fallHurtSubtractEvent, ret);
	if (succ)
	{
		return ret;
	}
	return getFallHurtSubtract_Base();
}

static std::string fallHurtRateEvent = "getFallHurtRate";
float FallComponent::getFallHurtRate(ClientActor* pActor)
{
	float ret = 0.0f;
	bool succ = pActor->Event2().Emit<float&>(fallHurtRateEvent, ret);
	if (succ)
	{
		return ret;
	}
	return getFallHurtRate_Base();
}

static std::string fallMotionEvent = "calFallMotion";
void FallComponent::calFallMotion(ClientActor* pActor, float motiony, bool onground)
{
	bool succ = pActor->Event2().Emit<float, bool>(fallMotionEvent, motiony, onground);
	if (succ)
	{
		return ;
	}
	calFallMotion_Base(pActor, motiony, onground);
}

static std::string fallEvent = "fall";
void FallComponent::fall(ClientActor* pActor, float fallh)
{
	bool succ = pActor->Event2().Emit<float>(fallEvent, fallh);
	if (succ)
	{
		return;
	}
	fall_Base(pActor, fallh);
}

float FallComponent::getFallHurtSubtract_Base()
{
	
	return 0.0f;
}

float FallComponent::getFallHurtRate_Base()
{
	return 1.0f;
}

void FallComponent::calFallMotion_Base(ClientActor* pActor, float motiony, bool onground)
{
	auto ownerActor = pActor;
	auto funcWrapper = ownerActor->getFuncWrapper();
	if (funcWrapper)
	{
		float fallDistance = funcWrapper->getFallDistance();
		if (motiony < 0)
		{
			fallDistance -= motiony;
			funcWrapper->setFallDistance(fallDistance);
		}
		else if(!onground)
		{
			funcWrapper->setFallDistance(0);
		}

		if (funcWrapper->getJetpackFlying())
		{
			funcWrapper->setFallDistance(0);
		}
		fallDistance = funcWrapper->getFallDistance();
		if (onground && fallDistance > 0)
		{
			int immuneFall = funcWrapper->getImmuneFall();
			immuneFall--;
			if (immuneFall <= 0)
			{
				fall(pActor, fallDistance);
			}
			funcWrapper->setFallDistance(0);
		}
	}
}

void FallComponent::fall_Base(ClientActor* pActor, float fallh)
{
	ClientActor *owner = pActor;
	fallh /= BLOCK_FSIZE;
	float atkpoints = 0;

	float fall_rate = 1.0f;
	if (g_WorldMgr && g_WorldMgr->m_RuleMgr && owner->getObjType() == OBJ_TYPE_ROLE)
	{
		fall_rate = g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_GRAVITYFACTOR);
	}

	if (owner->getObjType() == OBJ_TYPE_ROLE && fallh >= GetLuaInterfaceProxy().get_lua_const()->fall_hurt_ext) //将这里的高度判断改成配置的值，不固定3 code_by:huangfubin 2021.5.2
	{
		atkpoints = ((fallh*getFallHurtRate(pActor) - 
			GetLuaInterfaceProxy().get_lua_const()->fall_hurt_ext - getFallHurtSubtract(pActor)) * 
			GetLuaInterfaceProxy().get_lua_const()->falling_shanghai_beilv + 
			GetLuaInterfaceProxy().get_lua_const()->fall_hurt_ext2)*fall_rate; // modify by null 跌落伤害*5
	}
	else
	{
		atkpoints = ((fallh*getFallHurtRate(pActor) -
			GetLuaInterfaceProxy().get_lua_const()->fall_hurt_ext - getFallHurtSubtract(pActor)) * 
			GetLuaInterfaceProxy().get_lua_const()->falling_shanghai_beilv)*fall_rate; // modify by null 跌落伤害*5
	}

	if (atkpoints > 0 && owner->needWalkEffects())
	{
		Rainbow::GetICloudProxyPtr()->SimpleSLOG("uin %llu fall hurt %f, fallh %f, fall_rate %f, getFallHurtSubtract %f, getFallHurtRate %f, falling_shanghai_beilv %d", 
		                                         owner->getObjId(), atkpoints, fallh, fall_rate, getFallHurtSubtract(pActor), getFallHurtRate(pActor), GetLuaInterfaceProxy().get_lua_const()->falling_shanghai_beilv);

		auto sound = owner->getSoundComponent();
		if (sound)
		{
			if (atkpoints > 4)
			{
				sound->playSound("misc.fallbig", 1.0f, 1.0f);
			}
			else
			{
				sound->playSound("misc.fallsmall", 1.0f, 1.0f);
			}
		}

		WCoord pos = owner->getLocoMotion() ? owner->getLocoMotion()->getPosition() : WCoord(0, -1, 0);
		pos.y -= 5;
		int blockid = owner->getWorld() ? owner->getWorld()->getBlockID(CoordDivBlock(pos)) : 0;
		if (blockid > 0)
		{
			const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);
			if (blockdef)
			{
				auto sound = owner->getSoundComponent();
				if (sound)
				{
					sound->playSound(blockdef->WalkSound, GSOUND_FALLGROUND);
				}
			}
		}

		if (blockid == BLOCK_WEAK_ICICLE)//跌落到冰凌上有额外伤害
		{
			atkpoints = atkpoints + atkpoints / 2;
		}

		int objType = owner->getObjType();
		if (g_WorldMgr && g_WorldMgr->m_RuleMgr)
		{
			// 跌落伤害开关默认开启 玩家、生物会受到高空跌落的伤害
			float optionVal = g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_GRAVITY_HURT);
			if (optionVal == 0 && (objType == OBJ_TYPE_ROLE || objType == OBJ_TYPE_MONSTER ||
				objType == OBJ_TYPE_NPC || objType == OBJ_TYPE_CREATURE)) {
				return; //玩家、生物
			}
			else 
			{
				ActorAttrib* attr = owner->getAttrib();
				if (attr && attr->hasImmuneType(attr->getImmuneTypeByAttackType(ATTACK_FALLING)))//对象有设置免疫伤害
				{
					return;
				}
			}
		}
		if (objType == OBJ_TYPE_ROLE)
		{
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(owner);
			if (player) {
				if (player->getCurDorsumID() == ITEM_SNAKEGOD_WING) //蛇神之翼
				{
					return;
				}
			}
		}
		auto component = owner->getAttackedComponent();
		if (component)
		{
			component->attackedFromType_Base(ATTACK_FALLING, atkpoints);
		}
	}

	//计算玩家落地触发冰凌掉落
	WCoord playerpos = CoordDivBlock(owner->getLocoMotion()->getPosition());
	int cx = playerpos.x;
	int cy = playerpos.y;
	int cz = playerpos.z;
	int range = 6;//玩家周围6格范围
	int maxLength = 6 * 6 * 6;
	vector<WCoord> iciclePos;
	for (int z = cz - range; z <= cz + range; z++)
	{
		for (int x = cx - range; x <= cx + range; x++)
		{
			for (int y = cy; y <= cy + range; y++)
			{
				WCoord blockpos = WCoord(x, y, z);
				if ((blockpos - playerpos).lengthSquared() <= maxLength)
				{
					if (owner->getWorld()->getBlockID(blockpos) == BLOCK_WEAK_ICICLE)
					{
						iciclePos.emplace_back(blockpos);
						break;
					}
				}
			}
		}
	}

	int probability = (2 * fallh) > 60 ? 60 : int(2 * fallh); //落地高度 *2 的概率
	probability += 30;
	for (int i = 0; i < iciclePos.size(); i++)
	{
		if (GenRandomInt(100) < probability)
		{
			int blockcount = 0;
			int upblockcount = 0;
			vector<WCoord> posArray;
			WCoord _downblockpos = iciclePos[i];
			while (owner->getWorld()->getBlockID(_downblockpos) == BLOCK_WEAK_ICICLE)
			{
				posArray.emplace_back(_downblockpos);
				_downblockpos = _downblockpos - WCoord(0, 1, 0);
				blockcount++;
			}
			WCoord _upblockpos = iciclePos[i] + WCoord(0, 1, 0);
			while (owner->getWorld()->getBlockID(_upblockpos) == BLOCK_WEAK_ICICLE)
			{
				posArray.emplace_back(_upblockpos);
				_upblockpos = _upblockpos + WCoord(0, 1, 0);
				blockcount++;
				upblockcount++;
			}
			//下方有至少1格空间则坠落
			if (blockcount > 0 && owner->getWorld()->getBlockID(_downblockpos) == BLOCK_AIR)
			{
				for (int i = 0; i < posArray.size(); i++)
				{
					owner->getWorld()->setBlockAir(posArray[i]);
				}
				WCoord shootPos = (upblockcount > 0) ? DownCoord(_upblockpos) : iciclePos[i];
				auto pos = BlockBottomCenter(shootPos);
				Rainbow::GetMusicManager().PlaySound("sounds/item/32/prepare.ogg", pos.toVector3(), 1.0f, 1.0f);
				ClientActorIcicle::shootIcicleAuto(ITEM_WEAK_ICICLE, owner->getWorld(), BlockBottomCenter(shootPos), blockcount);
			}
		}
	}
}