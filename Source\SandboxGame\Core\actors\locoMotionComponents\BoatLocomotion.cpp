
#include "BoatLocomotion.h"
#include "BlockMaterialMgr.h"
#include "special_blockid.h"
#include "world.h"
#include "ClientActorLiving.h"
#include "LivingLocoMotion.h"
#include "ClientPlayer.h"
#include "ClientActorManager.h"
#include "ActorManager.h"
#include "LivingAttrib.h"
#include "ActorBoat.h"

#include "RiddenComponent.h"
#include "SandboxEventDispatcherManager.h"

using namespace MINIW;
using namespace Rainbow;
static Vector3f s_MotionDecay(0.9f, 0.98f, 0.9f);
static float s_InWaterForce = 1.0f;

int GetActorDepthInLiquid(ActorLocoMotion *locomove, bool onlywater)
{
	BlockMaterial* inkWaterMtl = NULL;// g_BlockMtlMgr.getMaterial(BLOCK_INK_STILL_WATER);
	BlockMaterial *watermtl = g_BlockMtlMgr.getMaterial(BLOCK_STILL_WATER);
	BlockMaterial *lavamtl = g_BlockMtlMgr.getMaterial(BLOCK_STILL_LAVA);
	CollideAABB box, tmpbox;
	locomove->getCollideBox(box);

	int SEGMENTS = 5;
	int inwater_h = 0;
	for(int i=0; i<SEGMENTS; i++)
	{
		tmpbox.pos = WCoord(box.minX(), box.minY()+box.dim.y*i/SEGMENTS, box.minZ());
		tmpbox.dim = WCoord(box.dim.x, box.dim.y/SEGMENTS, box.dim.z);
		if(locomove->m_pWorld->isBoxInMaterial(tmpbox, watermtl) || locomove->m_pWorld->isBoxInMaterial(tmpbox, inkWaterMtl))
		{
			inwater_h += BLOCK_SIZE/SEGMENTS;
		}
		else if(!onlywater && locomove->m_pWorld->isBoxInMaterial(tmpbox, lavamtl))
		{
			inwater_h += BLOCK_SIZE/SEGMENTS;
		}
	}

	return inwater_h;
}

static float CalLiquidFloatForce(ActorLocoMotion *locomove, bool onlywater, float curmy)
{
	int inwater_h = GetActorDepthInLiquid(locomove, onlywater);
	if(inwater_h < BLOCK_SIZE)
	{
		return curmy + s_InWaterForce * (inwater_h*2.0f/BLOCK_FSIZE - 1.0f);
	}
	else
	{
		if(curmy < 0) curmy /= 2.0f;

		return curmy + 0.7f;
	}
}
IMPLEMENT_COMPONENTCLASS(BoatLocomotion)

void BoatLocomotion::tick()
{
	ActorLocoMotion::tick();

	WCoord oldpos = m_Position;
	float motionlen = Rainbow::Sqrt(m_Motion.x*m_Motion.x + m_Motion.z*m_Motion.z);

	if(m_pWorld->isRemoteMode() && static_cast<ActorBoat *>(getOwnerActor())->m_HostMotivate)
	{
		if(boatPosRotationIncrements > 0)
		{
			m_Position = m_Position + (boatPos - m_Position)/boatPosRotationIncrements;
			m_RotateYaw = m_RotateYaw + WrapAngleTo180(boatYaw - m_RotateYaw)/boatPosRotationIncrements;
			m_RotationPitch = m_RotationPitch + (boatPitch - m_RotationPitch)/boatPosRotationIncrements;

			boatPosRotationIncrements--;
		}
		else
		{
			// y �ٶ���0 ��ֹ��Ư�����뿪ˮ�� by pyf
			m_Motion.y = 0.0f;

			m_Position = m_Position + getIntegerMotion(m_Motion);

			if(m_OnGround) m_Motion *= 0.5f;

			m_Motion *= s_MotionDecay;
		}
	}
	else
	{
		m_Motion.y = CalLiquidFloatForce(this, true, m_Motion.y);
		
		auto RidComp = getOwnerActor()->getRiddenComponent();
		ActorLiving *riddenby = NULL;
		if (RidComp)
		{
			riddenby = dynamic_cast<ActorLiving *>(RidComp->getRiddenByActor());
			if (RidComp->getNumRiddenPos() > 1)
			{
				if (m_OldMotion.x != 0 && m_OldMotion.z != 0)
				{
					if (m_OldMotion.x != m_Motion.x || m_OldMotion.z != m_Motion.z)
					{
						m_Motion.x = m_OldMotion.x;
						m_Motion.z = m_OldMotion.z;
					}
				}
			}
		}
		if(riddenby)
		{
			LivingLocoMotion *locomove = static_cast<LivingLocoMotion *>(riddenby->getLocoMotion());
			if(locomove->m_MoveForward > 0)
			{				
				Vector3f dir;
				PitchYaw2Direction(dir, locomove->m_RotateYaw, 0);
				m_Motion += dir * (speedMultiplier * 0.05f);
				if (RidComp && RidComp->getNumRiddenPos() > 1)
				{
					m_OldMotion = m_Motion;
				}							
			}

			//˫�����ͣ�º�һֱ���ƶ�����
			//���Ӷ���λ���жϣ��޸�Ƥ��������ˮ���ƶ����� by��Jeff
			if (RidComp && RidComp->getNumRiddenPos()>1 && locomove->m_MoveStrafing == 0 && locomove->m_MoveForward == 0) {
				m_Motion = Vector3f(0, m_Motion.y, 0);
				m_OldMotion = Vector3f(0, m_OldMotion.y, 0);
			}
		}

		float newlen = Rainbow::Sqrt(m_Motion.x*m_Motion.x + m_Motion.z*m_Motion.z);

		const float MAX_MOTION_LEN = 1500.0f;
		if(newlen > MAX_MOTION_LEN)
		{
			float t = MAX_MOTION_LEN / newlen;
			m_Motion.x *= t;
			m_Motion.z *= t;
			newlen = MAX_MOTION_LEN;
		}

		if(newlen>motionlen && speedMultiplier<MAX_MOTION_LEN)
		{
			speedMultiplier += (MAX_MOTION_LEN - speedMultiplier) / MAX_MOTION_LEN;

			if(speedMultiplier > MAX_MOTION_LEN)
			{
				speedMultiplier = MAX_MOTION_LEN;
			}
		}
		else
		{
			speedMultiplier -= (speedMultiplier - MAX_MOTION_LEN/5.0f) / MAX_MOTION_LEN;

			if(speedMultiplier < MAX_MOTION_LEN/5.0f)
			{
				speedMultiplier = MAX_MOTION_LEN/5.0f;
			}
		}
		m_Motion += m_ExtraMotion;
		if(m_OnGround) m_Motion *= 0.5f;

		doMoveStep(m_Motion);
		m_ExtraMotion = Rainbow::Vector3f(0, 0, 0);
		if(m_CollidedHorizontally && motionlen > 20.0f)
		{
			//һ���ٶ���ײ�Ƿ��ƻ�? 
		}
		else
		{
			m_Motion *= s_MotionDecay;
		}

		m_RotationPitch = 0.0f;


		float targetyaw = m_RotateYaw;

		//Vector3f dpos = (m_Position-oldpos).toVector3();
		Vector3f dpos = m_Motion;
		dpos.y = 0;
		if (dpos.LengthSqr() > 10.0f)
		{
			Direction2PitchYaw(&targetyaw, NULL, dpos);
		}

		m_RotateYaw += Clamp(WrapAngleTo180(targetyaw - m_RotateYaw), -20.0f, 20.0f);
		

		//���ƶ�����������������ĵ���
		WORLD_ID ridden = 0;
		if (getOwnerActor()->getRiddenComponent())
		{
			ridden = getOwnerActor()->getRiddenComponent()->getRiddenByActorID();
		}
		
		if (ridden != 0)
		{
			BIOME_TYPE biome_type = m_pWorld->getBiomeType(m_Position.x / BLOCK_SIZE, m_Position.z / BLOCK_SIZE);
			if (biome_type == BIOME_OCEAN || biome_type == BIOME_DEEPOCEAN)
			{
				int point = 2;
				ClientActorMgr* mgr = getOwnerActor()->getActorMgr();
				ClientPlayer* cPlayer = dynamic_cast<ClientPlayer*>(mgr->findActorByWID(ridden));
				if (cPlayer)
				{

					/*double oldLen = MINIW::Sqrt(BoatOldPos.x * BoatOldPos.x / 10000 + BoatOldPos.z * BoatOldPos.z / 10000);
					double newLen = MINIW::Sqrt(m_Position.x * m_Position.x/10000 + m_Position.z * m_Position.z/10000);
					moveDistance += abs(oldLen - newLen);
					*/
					double Dis_x = BoatOldPos.x - m_Position.x;
					double Dis_z = BoatOldPos.z - m_Position.z;
					double Dis = Dis_x * Dis_x + Dis_z * Dis_z;
					moveDistance = Rainbow::Sqrt(Dis);
					if (moveDistance > 141.42)
					{
						BoatOldPos = m_Position;
						//moveDistance = (int)moveDistance % 141;
						//WORLD_ID ridden = getOwnerActor()->getRiddenByActorID();

						if (cPlayer->getLivingAttrib()->hasBuff(1028))
						{
							point = 1;
						}
						MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("spawnSharkPoint", MNSandbox::SandboxContext(nullptr).SetData_Number("point", point));
					}
				}
			}
		}
	}
}
