

#include "FurnaceContainer.h"
#include "IClientActor.h"
#include "DefManagerProxy.h"
#include "PlayerControl.h"
#include "IPlayerControl.h"
#include "ActorCSProto.h"
#include "ClientItem.h"
#include "ClientActorManager.h"
#include "GameNetManager.h"
#include "LuaInterfaceProxy.h"
#include "BlockMaterialMgr.h"

#include "ObserverEvent.h"
#include "ObserverEventManager.h"
//#include "GameEvent.h"
//#include "TaskSubSystem.h"
#include "SandboxIdDef.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "SandboxCoreDriver.h"
#include "world.h"
static int ox = 0;
static int oy = 30;
static int oz = 0; 

EXPORT_SANDBOXENGINE extern int CheckInsertItemIntoArray(BaseContainer* container, BackPackGrid* gridarray, int arraylen, const BackPackGrid& grid);

FurnaceContainer::FurnaceContainer() : WorldContainer(FURNACE_START_INDEX), m_bilu_wood_ent_id(0)
{
	for (int i = 0; i < GRID_FULL; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}

	m_CurHeat = 0;
	m_MaxHeat = 1;
	m_CurHeat2 = 0;
	m_MaxHeat2 = 1;
	m_CurHeat3 = 0;
	m_MaxHeat3 = 1;
	m_MeltTicks = 0;
	m_MeltTicks2 = 0;
	m_MeltTicks3 = 0;
	m_isMelting = false;
	m_meltOnce_Time = 200.00;
	m_quality = FURNACE_QUALITY_STONE;
	m_temperature = 1;
	isLoad = false;
	m_ProvideHeat = 0.0;
	m_ProvideHeat2 = 0.0;
	m_ProvideHeat3 = 0.0;
	m_BurnOnceTime = 0.00;
	m_BurnOnceTime2 = 0.00;
	m_BurnOnceTime3 = 0.00;
	m_allProvideHeat = 0.0;
	m_MeltTicksFloat = 0.0;
	m_MeltTicksFloat2 = 0.0;
	m_MeltTicksFloat3 = 0.0;
	m_NeedTick = true;
	m_huoyanPos.clear();
	for (int i = 0; i < 4; i++)
	{
		m_scene_halo_id[i] = 0;
	}
}

FurnaceContainer::FurnaceContainer(const WCoord &blockpos) : WorldContainer(blockpos, FURNACE_START_INDEX), m_bilu_wood_ent_id(0)
{
	for (int i = 0; i < GRID_FULL; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}

	m_CurHeat = 0;
	m_MaxHeat = 1;
	m_CurHeat2 = 0;
	m_MaxHeat2 = 1;
	m_CurHeat3 = 0;
	m_MaxHeat3 = 1;
	m_MeltTicks = 0;
	m_MeltTicks2 = 0;
	m_MeltTicks3 = 0;
	m_isMelting = false;
	m_meltOnce_Time = 200.00;
	m_temperature = 1;
	m_quality = FURNACE_QUALITY_STONE;
	isLoad = false;
	m_ProvideHeat = 0.0;
	m_ProvideHeat2 = 0.0;
	m_ProvideHeat3 = 0.0;
	m_BurnOnceTime = 0.00;
	m_BurnOnceTime2 = 0.00;
	m_BurnOnceTime3 = 0.00;
	m_allProvideHeat = 0.0;
	m_MeltTicksFloat = 0.0;
	m_MeltTicksFloat2 = 0.0;
	m_MeltTicksFloat3 = 0.0;
	m_NeedTick = true;
	m_huoyanPos.clear();
	for (int i = 0; i < 4; i++)
	{
		m_scene_halo_id[i] = 0;
	}
}

FurnaceContainer::~FurnaceContainer()
{
	
}

void FurnaceContainer::SetQuality()
{
	if (m_World)
		m_quality = m_World->getBlockID(m_BlockPos);
	if (m_quality != FURNACE_QUALITY_IRON && m_quality != FURNACE_QUALITY_COPPER)
		m_quality = FURNACE_QUALITY_STONE;
	else
	{
		if (isLoad) return;
		if (m_quality == FURNACE_QUALITY_COPPER)
			m_temperature = 2;
		else
			m_temperature = 3;
	}
}


void FurnaceContainer::meltOnce(GRID_TYPE type)
{
	assert(m_Grids[type].getNum() > 0);
	const FurnaceDef *def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[type].getItemID());
	if (def == NULL) return;

	GetDefManagerProxy()->checkCrcCode(CRCCODE_FURNACE);

	if (g_pPlayerCtrl)
	{
		//g_pPlayerCtrl->addAchievement(1, ACHIEVEMENT_FURNACEITEM, def->Result, 1);
		g_pPlayerCtrl->addAchievement(1, ACHIEVEMENT_FURNACEITEM, getFurnaceResultId(def), getFurnaceResultNum(def)); //code_by:huangfubin 不同温度的产物不一样
		g_pPlayerCtrl->addOWScore(def->Score);
	}
	/*if (TaskSubSystem::GetTaskSubSystem())
	{
		TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_FURNACE_ITEM, m_BlockPos * BLOCK_SIZE, getFurnaceResultId(def), 0, getFurnaceResultNum(def));
	}*/
	WCoord pos = m_BlockPos * BLOCK_SIZE;
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("type", TASKSYS_FURNACE_ITEM).
			SetData_Userdata("WCoord", "trackPos", &pos).
			SetData_Number("target1", getFurnaceResultId(def)).
			SetData_Number("target2", 0).
			SetData_Number("goalnum", getFurnaceResultNum(def));
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
	}
	int index = getCanUseResultGrid(type);
	if (index != -1)
	{
		SetBackPackGrid(m_Grids[index], getFurnaceResultId(def), m_Grids[index].getNum() + getFurnaceResultNum(def));
		afterChangeGrid(m_BaseIndex + index);
	}

	SetBackPackGridWithClear(m_Grids[type], def->MaterialID, m_Grids[type].getNum() - 1);

	/*if (m_Grids[GRID_MTL].getNum() <= 0 && m_Grids[GRID_MTL2].getNum() <= 0 && m_Grids[GRID_MTL3].getNum() <= 0)
		m_isMelting = false;*/

	if (type == GRID_MTL)
	{
		m_MeltTicks = 0;
		m_MeltTicksFloat = 0.0;
	}
	else if (type == GRID_MTL2)
	{
		m_MeltTicks2 = 0;
		m_MeltTicksFloat2 = 0.0;
	}
	else if (type == GRID_MTL3)
	{
		m_MeltTicks3 = 0;
		m_MeltTicksFloat3 = 0.0;
	}

	afterChangeGrid(m_BaseIndex + type);

	ObserverEvent obevent;
	obevent.SetData_Position(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
	obevent.SetData_Furance(def->ID);
	ObserverEventManager::getSingleton().OnTriggerEvent("Furnace.end", &obevent);
}

void FurnaceContainer::addHeatOnce()
{
	if (m_Grids[GRID_MTL].getNum() > 0 || m_Grids[GRID_MTL2].getNum() > 0 || m_Grids[GRID_MTL3].getNum() > 0)
	{
		int grid_idx = getCanUseResultGrid(GRID_MTL);  //格子index
		int grid_idx2 = getCanUseResultGrid(GRID_MTL2);  //格子index
		int grid_idx3 = getCanUseResultGrid(GRID_MTL3);  //格子index
		if ((m_CurHeat == 0 && m_CurHeat2 == 0 && m_CurHeat3 == 0) && grid_idx == -1 && grid_idx2 == -1 && grid_idx3 == -1)
		{
			m_isMelting = false;
			return;
		}

		const FurnaceDef *def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_FUEL].getItemID());
		const FurnaceDef *def2 = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_FUEL2].getItemID());
		const FurnaceDef *def3 = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_FUEL3].getItemID());
		const FurnaceDef *curDef = NULL;
		const FurnaceDef *curDef2 = NULL;
		const FurnaceDef *curDef3 = NULL;

		bool condition11 = m_Grids[GRID_FUEL].getNum() >= 0 && def != NULL && def->Heat > 0;
		bool condition2 = m_Grids[GRID_FUEL2].getNum() >= 0 && def2 != NULL && def2->Heat > 0;
		bool condition3 = m_Grids[GRID_FUEL3].getNum() >= 0 && def3 != NULL && def3->Heat > 0;

		m_isMelting = true;

		if (m_CurHeat == 0)
		{
			m_ProvideHeat = 0.0;
			if (condition11)
				curDef = def;
		}
		if (m_CurHeat2 == 0)
		{
			m_ProvideHeat2 = 0.0;
			if (condition2)
				curDef2 = def2;
		}
		if (m_CurHeat3 == 0)
		{
			m_ProvideHeat3 = 0.0;
			if (condition3)
				curDef3 = def3;
		}
		if ((m_CurHeat == 0 && m_CurHeat2 == 0 && m_CurHeat3 == 0) && (!condition11 && !condition2 && !condition3))
		{
			m_isMelting = false;
		}


		if (m_isMelting == false) return;

		if (curDef)
		{
			m_CurHeat = curDef->Heat;
			m_MaxHeat = curDef->Heat;
			m_ProvideHeat = curDef->ProvideHeat;			

			if (curDef->ContainerID > 0)
				SetBackPackGridWithClear(m_Grids[GRID_FUEL], curDef->ContainerID, 1);
			else
				SetBackPackGridWithClear(m_Grids[GRID_FUEL], m_Grids[GRID_FUEL].getItemID(), m_Grids[GRID_FUEL].getNum() - 1);

			afterChangeGrid(m_BaseIndex + GRID_FUEL);
		}
		if (curDef2)
		{
			m_CurHeat2 = curDef2->Heat;
			m_MaxHeat2 = curDef2->Heat;
			m_ProvideHeat2 = curDef2->ProvideHeat;
			
			if (curDef2->ContainerID > 0)
				SetBackPackGridWithClear(m_Grids[GRID_FUEL2], curDef2->ContainerID, 1);
			else
				SetBackPackGridWithClear(m_Grids[GRID_FUEL2], m_Grids[GRID_FUEL2].getItemID(), m_Grids[GRID_FUEL2].getNum() - 1);

			afterChangeGrid(m_BaseIndex + GRID_FUEL2);
		}
		if (curDef3)
		{
			m_CurHeat3 = curDef3->Heat;
			m_MaxHeat3 = curDef3->Heat;
			m_ProvideHeat3 = curDef3->ProvideHeat;
			
			if (curDef3->ContainerID > 0)
				SetBackPackGridWithClear(m_Grids[GRID_FUEL3], curDef3->ContainerID, 1);
			else
				SetBackPackGridWithClear(m_Grids[GRID_FUEL3], m_Grids[GRID_FUEL3].getItemID(), m_Grids[GRID_FUEL3].getNum() - 1);

			afterChangeGrid(m_BaseIndex + GRID_FUEL3);
		}
	
	}


	if ((m_CurHeat == 0 && m_CurHeat2 == 0 && m_CurHeat3 == 0) && m_Grids[GRID_MTL].getNum() <= 0 && m_Grids[GRID_MTL2].getNum() <= 0 && m_Grids[GRID_MTL3].getNum() <= 0)
	{
		m_isMelting = false;

	}
}

BackPackGrid *FurnaceContainer::index2Grid(int index)
{
	assert(index>=m_BaseIndex && index<m_BaseIndex + GRID_FULL);

	return &m_Grids[index-m_BaseIndex];
}

void FurnaceContainer::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);
	//notifyChange2Openers(index, false);
	
	afterChangeGridByMTL(GRID_MTL);
	afterChangeGridByMTL(GRID_MTL2);
	afterChangeGridByMTL(GRID_MTL3);

	/*if (m_Grids[GRID_MTL].getNum() <= 0 && m_Grids[GRID_MTL2].getNum() <= 0 && m_Grids[GRID_MTL3].getNum() <= 0)
	{
		m_isMelting = false;
	}*/

	m_NeedSave = true;
}
void FurnaceContainer::afterChangeGridByMTL(GRID_TYPE type)
{
	if (m_Grids[type].getNum() > 0)
	{
		const FurnaceDef *def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[type].getItemID());
		if (def)
		{
			int grid_idx = getCanUseResultGrid(type);  //格子index
			int result_id = getFurnaceResultId(def); //对应不同熔炉产物itemid

			if (grid_idx != -1 && result_id > 0)
			{
				if (m_isMelting == true)
				{
					if (m_Grids[grid_idx].isEmpty() || 0 == m_Grids[grid_idx].enough)
					{
						SetBackPackGrid(m_Grids[grid_idx], 0, 0, -1, 0, 0);
					}
					return;
				}

				addHeatOnce();
			}
		}
	}
	else
	{
		if (type == GRID_MTL)
		{
			m_MeltTicks = 0;
			m_MeltTicksFloat = 0.0;
		}
		else if (type == GRID_MTL2)
		{
			m_MeltTicks2 = 0;
			m_MeltTicksFloat2 = 0.0;
		}
		else if (type == GRID_MTL3)
		{
			m_MeltTicks3 = 0;
			m_MeltTicksFloat3 = 0.0;
		}
		
		notifyChange2Openers(-1, true);		
	}

	m_NeedSave = true;
}

void FurnaceContainer::onAttachUI()
{
	m_AttachToUI = true;

	for (int i = 0; i < GRID_FULL; i++)
	{
		// GameEventQue::GetInstance().postBackpackChange(FURNACE_START_INDEX + i);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", FURNACE_START_INDEX + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}
	// GetGameEventQue().postBackPackAttribChange();
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_ATTRIB_CHANGE", sandboxContext);
}

void FurnaceContainer::onDetachUI()
{
	m_AttachToUI = false;
}

bool FurnaceContainer::canPutItem(int index)
{
	if(index == m_BaseIndex + GRID_RESULT || index >= m_BaseIndex + GRID_RESULT2 && index <= m_BaseIndex + GRID_RESULT6)
		return false;
	else return true;
}

void FurnaceContainer::onHeatOnOff()
{
	if (m_vehicleWorld != nullptr) { return; }
	int blockdata = m_World->getBlockData(m_BlockPos);
	int blockid = m_World->getBlockID(m_BlockPos);
	int newblockdata = blockdata;
	if((m_CurHeat | m_CurHeat2 | m_CurHeat3) == 0 && !m_isMelting){

		newblockdata = blockdata & 3;
		m_World->setBlockData(m_BlockPos, newblockdata, 2);
		m_World->setBlockTemperature(m_BlockPos, 0);
	}
	else {
		newblockdata = blockdata | 4;
		m_World->setBlockData(m_BlockPos, newblockdata, 2);
		m_World->setBlockTemperature(m_BlockPos, BlockMaterial::getTemperatureValue(blockid));
		
	}
	if (newblockdata != blockdata)
	{
		UpdateEffect();
	}
}

void FurnaceContainer::dropItems()
{
	for (int i = 0; i < GRID_FULL; i++)
	{
		dropOneItem(m_Grids[i]);
	}
}

void FurnaceContainer::updateTickMTL(GRID_TYPE type, float allProvideHeat, bool &hasprogress)
{
	if (m_isMelting && getCanUseResultGrid(type) != -1)
	{		
		if (type == GRID_MTL)
		{
			if (m_MeltTicksFloat == 0.0)
			{
				const FurnaceDef *furnacedef = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[type].getItemID());
				if (furnacedef)
				{
					ObserverEvent obevent;
					obevent.SetData_Position(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
					obevent.SetData_Furance(furnacedef->ID);
					ObserverEventManager::getSingleton().OnTriggerEvent("Furnace.begin", &obevent);
				}
			}

			if (m_BurnOnceTime / allProvideHeat < 20)//原料熔炼至少tick次数
			{
				allProvideHeat = m_BurnOnceTime / 20;
			}
			m_MeltTicksFloat += allProvideHeat;
			m_MeltTicks = m_MeltTicksFloat;
			if (m_MeltTicksFloat >= m_BurnOnceTime)
			{
				meltOnce(type);
			}			
		}
		else if (type == GRID_MTL2)
		{
			if (m_MeltTicksFloat2 == 0.0)
			{
				const FurnaceDef *furnacedef = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[type].getItemID());
				if (furnacedef)
				{
					ObserverEvent obevent;
					obevent.SetData_Position(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
					obevent.SetData_Furance(furnacedef->ID);
					ObserverEventManager::getSingleton().OnTriggerEvent("Furnace.begin", &obevent);
				}
			}
			if (m_BurnOnceTime2 / allProvideHeat < 20)//原料熔炼至少tick次数
			{
				allProvideHeat = m_BurnOnceTime2 / 20;
			}
			m_MeltTicksFloat2 += allProvideHeat;
			m_MeltTicks2 = m_MeltTicksFloat2;
			if (m_MeltTicksFloat2 >= m_BurnOnceTime2)
			{
				meltOnce(type);
			}
		}
		else if (type == GRID_MTL3)
		{
			if (m_MeltTicksFloat3 == 0.0)
			{
				const FurnaceDef *furnacedef = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[type].getItemID());
				if (furnacedef)
				{
					ObserverEvent obevent;
					obevent.SetData_Position(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
					obevent.SetData_Furance(furnacedef->ID);
					ObserverEventManager::getSingleton().OnTriggerEvent("Furnace.begin", &obevent);
				}
			}
			if (m_BurnOnceTime3 / allProvideHeat < 20)//原料熔炼至少tick次数
			{
				allProvideHeat = m_BurnOnceTime3 / 20;
			}
			m_MeltTicksFloat3 += allProvideHeat;
			m_MeltTicks3 = m_MeltTicksFloat3;
			if (m_MeltTicksFloat3 >= m_BurnOnceTime3)
			{
				meltOnce(type);
			}
		}
		hasprogress = true;
	}
}
void FurnaceContainer::UpdateEffect()
{
	int blockid = m_World->getBlockID(m_BlockPos, true);
	BlockMaterial* blockMtrl = blockid > 0 ? g_BlockMtlMgr.getMaterial(blockid) : nullptr;
	if (blockMtrl)
	{
		blockMtrl->DoOnPlayRandEffect(m_World, m_BlockPos);//onPlayRandEffect(m_World, blockpos);
	}
}
void FurnaceContainer::updateTick()
{
	if(m_World->isRemoteMode()) return;

	bool hasprogress = false;
	bool oldmelting = m_isMelting;
	int oldmeltticks = m_MeltTicks;
	int oldmeltticks2 = m_MeltTicks2;
	int oldmeltticks3 = m_MeltTicks3;
	int oldcurheat = m_CurHeat + m_CurHeat2 + m_CurHeat3;
	int oldmaxheat = m_MaxHeat + m_MaxHeat2 + m_MaxHeat3;
	float lodprovideheat = m_ProvideHeat + m_ProvideHeat2 + m_ProvideHeat3;
	float allProvideHeat = m_ProvideHeat + m_ProvideHeat2 + m_ProvideHeat3;

	bool isHasLava = false;
	for (int i = 0; i < 4; i++)
	{
		WCoord tempPos = NeighborCoord(m_BlockPos, i);	
		WCoord huoyanPos = BlockBottomCenter(tempPos) + WCoord(0, 10, 0);

		if (m_World && m_World->getBlockID(tempPos) == 746)//查找熔炉周围是否有熔岩雕像
		{					
			isHasLava = true;	

			if (m_World->getEffectMgr()->getEffectByID(m_scene_halo_id[i]) == NULL)
			{																	
				auto eff = m_World->getEffectMgr()->playParticleEffectAsync("particles/scene_halo.ent", huoyanPos, 0);
				if (eff)
					m_scene_halo_id[i] = eff->_ID;
			}	

			
			WCoord effectPos = BlockCenterCoord(m_BlockPos);
			if (m_quality == FURNACE_QUALITY_IRON || m_quality == FURNACE_QUALITY_COPPER)
			{
				effectPos = effectPos + WCoord(ox, oy, oz);
			}
			if (m_isMelting && m_World->getEffectMgr()->getEffectByID(m_bilu_wood_ent_id) == NULL)
			{		
				auto eff = m_World->getEffectMgr()->playParticleEffectAsync("particles/bilu_wood.ent", effectPos, 0);
				if (eff)
					m_bilu_wood_ent_id = eff->_ID;
			}						
		}
		else
		{
			if (m_scene_halo_id[i] && m_World->getEffectMgr()->getEffectByID(m_scene_halo_id[i]) != NULL)
			{
				m_World->getEffectMgr()->stopParticleEffectByID(m_scene_halo_id[i]);
			}
		}
	}	

	if (!m_isMelting || !isHasLava)
	{
		if (m_bilu_wood_ent_id && m_World->getEffectMgr()->getEffectByID(m_bilu_wood_ent_id) != NULL)
		{
			m_World->getEffectMgr()->stopParticleEffectByID(m_bilu_wood_ent_id);
		}
		
	}


	if (isHasLava)
	{
		allProvideHeat += 6;//提供热量加成
	}

	m_allProvideHeat = allProvideHeat;

	updateTickMTL(GRID_MTL, allProvideHeat, hasprogress);
	updateTickMTL(GRID_MTL2, allProvideHeat, hasprogress);
	updateTickMTL(GRID_MTL3, allProvideHeat, hasprogress);
	bool needCheckHeat = false;
	if (m_CurHeat > 0)
	{
		m_CurHeat--;
		needCheckHeat = true;
		hasprogress = true;
		/*if (m_CurHeat == 1)
		{
			SetBackPackGridWithClear(m_Grids[GRID_FUEL], m_Grids[GRID_FUEL].getItemID(), m_Grids[GRID_FUEL].getNum() - 1);
			afterChangeGrid(m_BaseIndex + GRID_FUEL);
		}*/
	}
	if (m_CurHeat2 > 0)
	{
		m_CurHeat2--;
		needCheckHeat = true;
		hasprogress = true;

		/*if (m_CurHeat2 == 1)
		{
			SetBackPackGridWithClear(m_Grids[GRID_FUEL2], m_Grids[GRID_FUEL2].getItemID(), m_Grids[GRID_FUEL2].getNum() - 1);
			afterChangeGrid(m_BaseIndex + GRID_FUEL2);
		}*/
	}
	if (m_CurHeat3 > 0)
	{
		m_CurHeat3--;
		needCheckHeat = true;
		hasprogress = true;

		/*if (m_CurHeat3 == 1)
		{
			SetBackPackGridWithClear(m_Grids[GRID_FUEL3], m_Grids[GRID_FUEL3].getItemID(), m_Grids[GRID_FUEL3].getNum() - 1);
			afterChangeGrid(m_BaseIndex + GRID_FUEL3);
		}*/
	}

	onHeatOnOff();

	if (m_isMelting)
	{
		if (m_CurHeat == 0 || m_CurHeat2 == 0 || m_CurHeat3 == 0)
			addHeatOnce();
	}

	if(hasprogress)
	{
		notifyChange2Openers(-1, true);
		//GameEventQue::GetInstance().postBackPackAttribChange();
	}

	if (oldmelting != m_isMelting || oldmeltticks != m_MeltTicks || oldmeltticks2 != m_MeltTicks2 || oldmeltticks3 != m_MeltTicks3 || oldcurheat != m_CurHeat + m_CurHeat2 + m_CurHeat3 || oldmaxheat != m_MaxHeat + m_MaxHeat2 + m_MaxHeat3 || lodprovideheat != m_ProvideHeat + m_ProvideHeat2 + m_ProvideHeat3)
	{
		m_NeedSave = true;
	}
}

float FurnaceContainer::getHeatPercent(int index)
{
	if (m_MaxHeat == 0) m_MaxHeat = 1;
	if (m_MaxHeat2 == 0) m_MaxHeat2 = 1;
	if (m_MaxHeat3 == 0) m_MaxHeat3 = 1;
	if (index == 1)
		return float(m_CurHeat) / float(m_MaxHeat);
	else if (index == 2)
		return float(m_CurHeat2) / float(m_MaxHeat2);
	else if (index == 3)
		return float(m_CurHeat3) / float(m_MaxHeat3);
	else
		return 0.0f;
	return 0.0f;
}

float FurnaceContainer::getMeltTicksPercent(int index)
{
	if (index == 0)
	{
		if (m_BurnOnceTime == 0.00)
		{
			return 0.0f;
		}
		return float(m_MeltTicksFloat / m_BurnOnceTime);
	}
	else if (index == 10)
	{
		if (m_BurnOnceTime2 == 0.00)
		{
			return 0.0f;
		}
		return float(m_MeltTicksFloat2 / m_BurnOnceTime2);
	}
	else if (index == 11)
	{
		if (m_BurnOnceTime3 == 0.00)
		{
			return 0.0f;
		}
		return float(m_MeltTicksFloat3 / m_BurnOnceTime3);
	}
	return 0.0f;
}

void FurnaceContainer::clear()
{
	for (int i = 0; i < GRID_FULL; i++) {
		BackPackGrid& grid = m_Grids[i];
		if (!grid.isEmpty()) {
			grid.clear();
			afterChangeGrid(grid.getIndex());
		}
	}
}

int FurnaceContainer::addItemByCount(int itemid, int num)
{
	const FurnaceDef *def = GetDefManagerProxy()->getFurnaceDefByMaterialID(itemid);
	if (def == NULL) return -1;

	int index = m_BaseIndex;
	BackPackGrid *dest;
	if (def->Heat > 0) {
		dest = &m_Grids[GRID_MTL];
		index += GRID_MTL;
	}
	else {
		dest = &m_Grids[GRID_FUEL];
		index += GRID_FUEL;
	}

	if (!dest->isEmpty() && dest->getItemID() != itemid) { return -1; }

	int addnum = num;
	if (dest->isEmpty()) {
		SetBackPackGrid(*dest, itemid, num);
		afterChangeGrid(index);
	}
	else {
		addnum = dest->addNum(num);
	}

	return addnum;
}

void FurnaceContainer::removeItemByCount(int itemid, int num)
{
	for (int i = 0; i < GRID_FULL; i++)
	{
		BackPackGrid *grid = index2Grid(i + m_BaseIndex);
		if (grid && grid->getItemID() == itemid)
		{
			if (num >= grid->getNum())
			{
				num -= grid->getNum();
				grid->addNum(-grid->getNum());
				grid->clear();
				afterChangeGrid(grid->getIndex());
			}
			else
			{
				grid->addNum(-num);
				afterChangeGrid(grid->getIndex());
				return;
			}
		}
	}
}

flatbuffers::Offset<FBSave::ChunkContainer> FurnaceContainer::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	flatbuffers::Offset<FBSave::ItemGrid> grids[GRID_FULL];
	for (int i = 0; i < GRID_FULL; i++)
	{
		grids[i] = m_Grids[i].save(builder);
	}

	auto items = builder.CreateVector(grids, GRID_FULL);

	m_MeltTicks = m_MeltTicksFloat;
	m_MeltTicks2 = m_MeltTicksFloat2;
	m_MeltTicks3 = m_MeltTicksFloat3;

	auto actor = FBSave::CreateContainerFurnace(builder, basedata, items, m_CurHeat, m_MaxHeat, m_MeltTicks, m_isMelting, m_CurHeat2, m_MaxHeat2, m_CurHeat3, m_MaxHeat3, m_temperature, m_ProvideHeat, m_ProvideHeat2, m_ProvideHeat3, m_MeltTicks2, m_MeltTicks3);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerFurnace, actor.Union());
}

EXPORT_SANDBOXENGINE extern int g_BackgridCheckNumMethod;

bool FurnaceContainer::load(const void *srcdata)
{
	isLoad = true;
	auto src = reinterpret_cast<const FBSave::ContainerFurnace *>(srcdata);
	loadContainerCommon(src->basedata());

	m_CurHeat = src->curheat();
	m_MaxHeat = src->maxheat();
	m_MeltTicks = src->meltticks();
	m_isMelting = src->melting()!=0;

	//新增内容
	m_CurHeat2 = src->curheat2();
	m_MaxHeat2 = src->maxheat2();
	m_CurHeat3 = src->curheat3();
	m_MaxHeat3 = src->maxheat3();
	m_temperature = src->temperature();

	m_ProvideHeat = src->provideHeat();
	m_ProvideHeat2 = src->provideHeat2();
	m_ProvideHeat3 = src->provideHeat3();
	m_MeltTicks2 = src->meltticks2();
	m_MeltTicks3 = src->meltticks3();

	m_MeltTicksFloat = m_MeltTicks;
	m_MeltTicksFloat2 = m_MeltTicks2;
	m_MeltTicksFloat3 = m_MeltTicks3;

	if (m_quality == 0)
		m_quality = FURNACE_QUALITY_STONE;

	if (m_temperature == 0)
		m_temperature = 1;
	//end

	g_BackgridCheckNumMethod = 1;
	for (unsigned int i = 0; i < src->items()->size(); i++)
	{
		if (i >= GRID_FULL) 
		{
			break;
		}
		m_Grids[i].load(src->items()->Get(i));
	}
	g_BackgridCheckNumMethod = 0;


	/******************???????ProvideHeat??********************/
	const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_FUEL].getItemID());
	const FurnaceDef* def2 = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_FUEL2].getItemID());
	const FurnaceDef* def3 = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_FUEL3].getItemID());
	bool condition11 = m_Grids[GRID_FUEL].getNum() >= 0 && def != NULL && def->Heat > 0;
	bool condition2 = m_Grids[GRID_FUEL2].getNum() >= 0 && def2 != NULL && def2->Heat > 0;
	bool condition3 = m_Grids[GRID_FUEL3].getNum() >= 0 && def3 != NULL && def3->Heat > 0;
	if (m_CurHeat > 0 && condition11)
	{
		m_ProvideHeat = def->ProvideHeat;
	}
	if (m_CurHeat2 > 0 && condition2)
	{
		m_ProvideHeat2 = def2->ProvideHeat;
	}
	if (m_CurHeat3 > 0 && condition3)
	{
		m_ProvideHeat3 = def3->ProvideHeat;
	}
	/********************???????ProvideHeat??******************/

	//onHeatOnOff();
	return true;
}

int FurnaceContainer::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (int i = 0; i < GRID_FULL; i++)
		{
			if (m_Grids[i].isEmpty()) continue;

			storeGridData(pItemInfos->Add(), &m_Grids[i]);
		}
	}

	pAttrInfos->Add(getMeltTicksPercent(0));
	pAttrInfos->Add(getHeatPercent(1));
	pAttrInfos->Add(getHeatPercent(2));
	pAttrInfos->Add(getHeatPercent(3));
	pAttrInfos->Add(m_temperature);
	pAttrInfos->Add(m_isMelting);
	pAttrInfos->Add(getMeltTicksPercent(10));
	pAttrInfos->Add(getMeltTicksPercent(11));
	pAttrInfos->Add(m_allProvideHeat);

	return GRID_FULL;
}

int FurnaceContainer::onInsertItem(const BackPackGrid &grid, int num, int params)
{
	const ItemDef *itemDef = GetDefManagerProxy()->getItemDef(grid.getItemID());
	if (itemDef == NULL) return -1;

	const FurnaceDef *def = GetDefManagerProxy()->getFurnaceDefByMaterialID(grid.getItemID());
	if(def == NULL) return -1;
	if (def->Type > 0) return -1;

	int dir = params & 7;
	BackPackGrid *dest;
	if (dir == DIR_POS_Y)
	{
		if (def->Result == 0)
			return -1;


		if ((m_Grids[GRID_MTL].getItemID() == grid.getItemID() && m_Grids[GRID_MTL].getNum() < itemDef->StackMax) || m_Grids[GRID_MTL].getNum() == 0)
			dest = &m_Grids[GRID_MTL];
		else if (m_quality != FURNACE_QUALITY_STONE && ((m_Grids[GRID_MTL2].getItemID() == grid.getItemID() && m_Grids[GRID_MTL2].getNum() < itemDef->StackMax) || m_Grids[GRID_MTL2].getNum() == 0))
			dest = &m_Grids[GRID_MTL2];
		else if (m_quality == FURNACE_QUALITY_IRON && ((m_Grids[GRID_MTL3].getItemID() == grid.getItemID() && m_Grids[GRID_MTL3].getNum() < itemDef->StackMax) || m_Grids[GRID_MTL3].getNum() == 0))
			dest = &m_Grids[GRID_MTL3];
		else
			return -1;
	}
	else
	{
		if(def->Heat <= 0) return -1;
		if ((m_Grids[GRID_FUEL].getItemID() == grid.getItemID() && m_Grids[GRID_FUEL].getNum() < itemDef->StackMax) || m_Grids[GRID_FUEL].getNum() == 0)
			dest = &m_Grids[GRID_FUEL];
		else if (m_quality != FURNACE_QUALITY_STONE && ((m_Grids[GRID_FUEL2].getItemID() == grid.getItemID() && m_Grids[GRID_FUEL2].getNum() < itemDef->StackMax) || m_Grids[GRID_FUEL2].getNum() == 0))
			dest = &m_Grids[GRID_FUEL2];
		else if (m_quality == FURNACE_QUALITY_IRON && ((m_Grids[GRID_FUEL3].getItemID() == grid.getItemID() && m_Grids[GRID_FUEL3].getNum() < itemDef->StackMax) || m_Grids[GRID_FUEL3].getNum() == 0))
			dest = &m_Grids[GRID_FUEL3];
		else
			dest = &m_Grids[GRID_FUEL];
	}

	return InsertItemIntoArray(this, dest, 1, grid, num);
}

bool FurnaceContainer::canInsertItem(const BackPackGrid& grid, int param)
{
	const ItemDef* itemDef = GetDefManagerProxy()->getItemDef(grid.getItemID());
	if (itemDef == NULL) return false;

	const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(grid.getItemID());
	if (def == NULL) return false;
	if (def->Type > 0) return false;

	int dir = param & 7;
	BackPackGrid* dest;
	if (dir == DIR_NEG_Y)  //这个dir是输入方向，策划设定原料只有从上往下传输才能输送到冶炼台 code by :keguanqiang
	{
		if (def->Result == 0)
			return false;
		
		if ((m_Grids[GRID_MTL].getItemID() == grid.getItemID() && m_Grids[GRID_MTL].getNum() < itemDef->StackMax) || m_Grids[GRID_MTL].getNum() == 0)
			dest = &m_Grids[GRID_MTL];
		else if (m_quality != FURNACE_QUALITY_STONE && ((m_Grids[GRID_MTL2].getItemID() == grid.getItemID() && m_Grids[GRID_MTL2].getNum() < itemDef->StackMax) || m_Grids[GRID_MTL2].getNum() == 0))
			dest = &m_Grids[GRID_MTL2];
		else if (m_quality == FURNACE_QUALITY_IRON && ((m_Grids[GRID_MTL3].getItemID() == grid.getItemID() && m_Grids[GRID_MTL3].getNum() < itemDef->StackMax) || m_Grids[GRID_MTL3].getNum() == 0))
			dest = &m_Grids[GRID_MTL3];
		else
			return false;
	}
	else
	{
		if (def->Heat <= 0) return false;
		if ((m_Grids[GRID_FUEL].getItemID() == grid.getItemID() && m_Grids[GRID_FUEL].getNum() < itemDef->StackMax) || m_Grids[GRID_FUEL].getNum() == 0)
			dest = &m_Grids[GRID_FUEL];
		else if (m_quality != FURNACE_QUALITY_STONE && ((m_Grids[GRID_FUEL2].getItemID() == grid.getItemID() && m_Grids[GRID_FUEL2].getNum() < itemDef->StackMax) || m_Grids[GRID_FUEL2].getNum() == 0))
			dest = &m_Grids[GRID_FUEL2];
		else if (m_quality == FURNACE_QUALITY_IRON && ((m_Grids[GRID_FUEL3].getItemID() == grid.getItemID() && m_Grids[GRID_FUEL3].getNum() < itemDef->StackMax) || m_Grids[GRID_FUEL3].getNum() == 0))
			dest = &m_Grids[GRID_FUEL3];
		else
			dest = &m_Grids[GRID_FUEL];
	}

	return CheckInsertItemIntoArray(this, dest, 1, grid) <= 0;
}

BackPackGrid *FurnaceContainer::onExtractItem(int params)
{
	if(!m_Grids[GRID_RESULT].isEmpty())
		return &m_Grids[GRID_RESULT];
	for (int i = GRID_RESULT2; i <= GRID_RESULT6; i++)
	{
		if (m_Grids[i].isEmpty()) continue;
		return &m_Grids[i];
	}
	if (m_Grids[GRID_FUEL].getItemID() == ITEM_BUCKET || m_Grids[GRID_FUEL].getItemID() == ITEM_TITANIUM_BUCKET) return &m_Grids[GRID_FUEL];
	if (m_Grids[GRID_FUEL2].getItemID() == ITEM_BUCKET || m_Grids[GRID_FUEL2].getItemID() == ITEM_TITANIUM_BUCKET) return &m_Grids[GRID_FUEL2];
	if (m_Grids[GRID_FUEL3].getItemID() == ITEM_BUCKET || m_Grids[GRID_FUEL3].getItemID() == ITEM_TITANIUM_BUCKET) return &m_Grids[GRID_FUEL3];
	return NULL;
}

int FurnaceContainer::calComparatorInputOverride()
{
	return CalculateItemsComparatorInput(&m_Grids[0], 3);
}


int FurnaceContainer::getCanUseResultGrid(GRID_TYPE type)
{
	if (m_Grids[type].getNum() <= 0) return -1;
	const FurnaceDef *def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[type].getItemID());

	if (!def)
	{
		return -1;
	}
	//int grid_idx = -1;  //格子index,-1 表示没有空余
	int result_id = def->Result; //对应不同熔炉产物itemid
	int result_num = getFurnaceResultNum(def);
	
	if (type == GRID_MTL)
	{
		m_BurnOnceTime = def->BurnTime;
	}
	else if(type == GRID_MTL2)
	{
		m_BurnOnceTime2 = def->BurnTime;
	}
	else if (type == GRID_MTL3)
	{
		m_BurnOnceTime3 = def->BurnTime;
	}
	 
	const ItemDef *resultdef = GetDefManagerProxy()->getItemDef(def->Result);

	if (resultdef == NULL) return -1;

	GRID_TYPE grid_type = GRID_RESULT;
	if (m_quality == FURNACE_QUALITY_IRON)
	{
		grid_type = GRID_RESULT3;
	}
	else if (m_quality == FURNACE_QUALITY_COPPER)
	{
		grid_type = GRID_RESULT2;
	}

	bool isHadResultItem = false;
	for (int i = GRID_RESULT; i <= grid_type; i++)
	{
		if (i == GRID_FUEL2 || i == GRID_FUEL3) continue;

		if ((m_Grids[i].getItemID() == result_id && (m_Grids[i].getNum() + result_num) <= resultdef->StackMax))
		{
			isHadResultItem = true;
		}
		
	}


	for (int i = GRID_RESULT; i <= grid_type; i++)
	{
		if (i == GRID_FUEL2 || i == GRID_FUEL3) continue;

		if (m_Grids[i].isEmpty() || 0 == m_Grids[i].enough)
		{
			if (isHadResultItem)
			{
				continue;
			}
		}

		if (m_Grids[i].isEmpty() || 0 == m_Grids[i].enough || (m_Grids[i].getItemID() == result_id && (m_Grids[i].getNum() + result_num) <= resultdef->StackMax))
			return i;
	}

	return -1;
}

int FurnaceContainer::getFurnaceResultId(const FurnaceDef *def)
{
	if (def == NULL) return 0;
	int result_id = def->Result; //对应不同熔炉产物itemid
	return result_id;
}

void FurnaceContainer::setTemperatureLev(int lev)
{

}

int FurnaceContainer::getFurnaceResultNum(const FurnaceDef *def)
{
	//旧版本原来没有resultnum，统一默认1
	if (def == NULL) return 1;
	int num = def->ResultNum; //对应不同熔炉产物itemid
	return num;
}


void FurnaceContainer::leaveWorld() 
{	
	for (int i = 0; i < 4; i++)
	{		
		if (m_scene_halo_id[i] && m_World->getEffectMgr()->getEffectByID(m_scene_halo_id[i]))
		{
			m_World->getEffectMgr()->stopParticleEffectByID(m_scene_halo_id[i]);
		}
	}

	if (m_bilu_wood_ent_id && m_World->getEffectMgr()->getEffectByID(m_bilu_wood_ent_id))
	{
		m_World->getEffectMgr()->stopParticleEffectByID(m_bilu_wood_ent_id);
	}

	WorldContainer::leaveWorld();
}