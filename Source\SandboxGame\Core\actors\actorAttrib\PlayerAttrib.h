#ifndef __PLAYERATTRIB_H__
#define __PLAYERATTRIB_H__

#include "LivingAttrib.h"
#include "ActorAttrib.h"
#include "system/SandboxGame.h"
#include "ClientPlayer.h"
#include "PlayerDownedStateAttrib.h"

class ClientPlayer;
class ThirstValue;
class CraftingQueue;
class FoodValue;

//tolua_begin
enum PlayerItemAttType
{
	Disable_Throw = 1,
	Disable_Drop = 2,
};
enum StrengthFoodShowState
{
	SFS_Empty = 0,
	SFS_Food = 1,
	SFS_Strength = 2,
};
//玩法模式设置, 玩家基础属性
struct PlayerBaseAttrSetter {
	int m_nMaxHP;
	int m_nMaxStrength;
	int m_nMaxFoodLevel;
	int m_nMaxThirst;       // 最大口渴值
	int m_nAttackPhy;
	int m_nAttackMagic;
	int m_nDefPhy;
	int m_nDefMagic;
	int m_nMoveSpeed;
};
//tolua_end

//等级经验模式
class PlayerAttrib;
class PlayerLevelMode { //tolua_exports
public:
	//tolua_begin
	PlayerLevelMode(PlayerAttrib* pOwnerAttr);
	void AddExp(int nExp);
	int GetCurLevel();
	int GetCurExp();
	int GetSumExp() {
		return m_nGainedSumExp;
	}
	
	//加载fb时用
	void SetSumExp(int nSumExp) {
		m_nGainedSumExp = nSumExp;
	}
	void SetCurExp(int nExp) {
		m_nCurExp = nExp;
	}
	void SetCurLevel(int nLevel) {
		m_nCurLevel = nLevel;
	}
	//tolua_end
private:
	int m_nGainedSumExp;	//历史获取的总经验
	int m_nCurExp;			//当前累计的经验
	int m_nCurLevel;		//当前等级

	PlayerAttrib* m_pOwnerAttr;
}; //tolua_exports


class EXPORT_SANDBOXGAME PlayerAttrib : public LivingAttrib{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(PlayerAttrib)
	/* 进入owner */
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	/* 离开owner */
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	PlayerAttrib(ClientPlayer* player);

	void CreateEvent2();
	void DestroyEvent2();

	//tolua_begin
	void initialize();
	bool isInitialized();
	virtual ~PlayerAttrib();

	virtual void revive();
	virtual void clearrevive(int cleartype);

	virtual bool isEquipOxygenpack() override;
	virtual bool damageOxygenpack(int value);

	void actorApplyEquips(ActorBody* body, EQUIP_SLOT_TYPE t);
	virtual void applyEquips(ActorBody* body, EQUIP_SLOT_TYPE t = MAX_EQUIP_SLOTS, bool takeoffAble = true);//装备模型挂件（参数就是给装备位置）by charles xie
	virtual void equip(EQUIP_SLOT_TYPE t, int itemid, int durable, int toughness, int maxdurable);
	virtual void equip(EQUIP_SLOT_TYPE t, BackPackGrid* itemgrid);
	virtual int getEquipItem(EQUIP_SLOT_TYPE t) override; //not_auto_export_to_lua
	virtual int getEquipItemWithType(EQUIP_SLOT_TYPE t) override;
	virtual int damageEquipItem(EQUIP_SLOT_TYPE t, int damage);
	virtual int damageEquipItemWithType(EQUIP_SLOT_TYPE t, int damage);
	virtual void dropEquipItems();
	virtual void dropEquipItemsToChest(int itemid);
	virtual BackPackGrid *getEquipGrid(EQUIP_SLOT_TYPE t);//not_auto_export_to_lua 按装备栏index获取数据
	virtual BackPackGrid* getEquipGridWithType(EQUIP_SLOT_TYPE t); //只有获取武器 调用和上面的接口一样的(按装备类型获取数据)
	//玩家角色水压值会受到装备影响
	virtual int getCalculatedWaterPressure() override; // 获得计算各项处理后的水压影响值

	float getEquipViewLightBright();

	virtual void SetCurBuildingId(int buildingId);//建筑蓝图ID

	//=====================================================================//
	void addHP(float hp, bool overflowable = false) override;

	/**
	@brief 经buff计算后的最大值
	*/
	float getMaxHP() override;
	//=====================================================================//

	virtual float getKnockback(ATTACK_TYPE atktype, ATTACK_TARGET_TYPE targettype) override; //击退

	/**
	@brief 玩法模式切换后下发通知函数
	*/
	void onToggleGameMode() override;

	void autoAddCurShortcutItem(int itemid);
	int getEquipItemDuration(EQUIP_SLOT_TYPE t);
	int getEquipItemMaxDuration(EQUIP_SLOT_TYPE t);
	bool checkIfItemHasAttAction(int iItemid, int iAttType);
	void addItemAttAction(int itematt);
	void getItemAttAction(std::vector<int>& vItemAttList);
	//=====================================================================//
	float getStrength();
	/**
	@brief 原始的最大值。下溢出同。
	*/
	float getBasicMaxStrength();

	/**
	@brief 经过buff、天赋计算后的最大值。下溢出同。
	*/
	float getMaxStrength();
	
	float getBasicOverflowStrength();
	float getOverflowStrength();

	/**
	@brief	添加体力
	- 攻击：每次攻击扣除一定值
	- 疾跑跳跃：每次跑跳扣除一定值
	- 二段跳：每次二段跳扣除一定值
	*/
	void addStrength(float value);
	void setStrength(float value);
	void setBasicMaxStrength(float max);
	void setMaxStrength(float max);
	void setBasicOverflowStrength(float overflow);
	void setOverflowStrength(float overflow);
	void setStrengthRestoreFactor(float factor);
	void setMaxStrengthForExhaustion(float max);
	void setMaxPercentageOfStrengthForExhaustion(float maxPercentage);
	void addBasicStrengthRestore(float restore);
	void setBasicStrengthRestore(float restore);
	float getBasicStrengthRestore();

	void setMaxThirst(float max);

	void setThirst(float value);
	void setBasicMaxThirst(float max);

	bool dropCurrentHandItem();
	/**
	@brief 这个函数仅仅只是简单的设置口渴值和广播
	*/
	void setThirstOnlyInform(float value);

	/**
	@brief	判断是否疲劳。目前低于最大值的百分比即触发疲劳，并添加buff。
	*/
	bool isExhausted();

	/**
	@brief	体力消耗行为判断体力值是否充足
	*/
	bool isStrengthEnough(float consumption);
	
	/**
	@brief	检查持续性操作是否符合体力或疲劳相关条件
	*/
	void checkPersistentOperation();
	//=====================================================================//

	int getFoodLevel();
	int getFoodSatLevel();
	void eatFood(int itemid, bool inbackpack=true);
	void drinkWater(int waterVolume, int waterType, int itemid);//waterType对应drinkType
    void SetDefenceBuffStatus(int itemId, int status);

	/**
	@deprecated
	*/
	void useStamina(STAMINA_METHOD method, float v=1.0f);
	int getExp();	
	void setExp(int exp);
	void addExp(int e);

	unsigned long  getStarDebuffTime()//星星优化：连续使用星星的惩罚
	{
		return m_StarDebuffTime;
	}
	void setStarDebuffTime(int time)
	{
		m_StarDebuffTime = time;
	}
	int  getStarDebuffStage()//星星优化：连续使用星星的惩罚阶段
	{
		return m_StarDebuffStage;
	}
	void setStarDebuffStage(int stage)
	{
		m_StarDebuffStage = stage;
	}
	//经验等级
	void addLevelModelExp(int nExp);//增加等级经验
	int getCurLevel();				//当前等级
	int getCurLevelExp();			//获取当前等级经验
	void setCurLevel(int curLevel);	//设置当前等级(触发器用)
	void setCurLevelExp(int curExp);//设置当前经验(触发器用)
	int getMaxLevel();				//当前设置的最大等级
	int getLevelExp(int nLevel);	//获取从(nLevel-1)升级到(nLevel)所需的经验值
	int getLevelSumExp(int nLevel);	//获取0--nLevel级的所有经验之和

	//玩法模式:基础属性设置
	void initPlayerBaseAttr(int nTeamId = -1, bool revive = false);
	int getPlayerBaseAttr(int attrType);
	void setPlayerBaseAttr(int attrType, int val);

	int getCurShotcut();
	void setCurShotcut(int index);
	void ApplyWeaponEnchantEffect(ActorBody *body);
	void SyncWeaponEnchantEffect();
	void setMoveSpeed(float fSpeed);	//设置移动速度
	virtual float getMoveSpeed(int type = 0); //not_auto_export_to_lua
	float calculateMoveSpeed(float basespeed, int type);
	int getEquipAddSpeed(int type);
	void setFoodLevel(int foodLevel);
	void onCurToolUsed(int num = -1, bool ignore_durable = false, bool needAutoAdd = true); //ignore_durable: 忽略耐久， 直接减1
	void setItemAttAction(int iItemid, int iAttType, bool bActive);
	void setFoodMaxLevel(int maxVal);
	int getFoodMaxLevel();
	void onDrinkWaterBug(int drinkType);
	bool IsEnableEatFood() {return m_CanEatFood;};
	void setEnableEatFood(bool b) {m_CanEatFood = b;};

	/**
	@deprecated
	@brief	目前开发者专用的兼容旧版开关
	*/
	void toggleUseCompatibleStrength(bool enable);

	/**
	@deprecated
	@brief	目前开发者专用的兼容旧版开关
	*/
	bool useCompatibleStrength();

	/**
	@brief	设置饥饿值体力值显示状态，0为都不显示 1饥饿值 2体力值
	*/
	void setStrengthFoodShowState(int flag);

	/**
	@brief	获取饥饿值体力值显示状态，0为都不显示 1饥饿值 2体力值
	*/
	int strengthFoodShowState();

	float getFood();
	float getMaxFood();

	float getThirst();
	float getMaxThirst();
	float getBasicMaxThirst();
	float getBasicOverflowThirst();
	float getOverflowThirst();


	#ifdef IWORLD_DEV_BUILD
	//tolua_end
		std::string toString() override;
	//tolua_begin
	#endif

	void tick() override;

	/**
	@brief	是否正在蓄力
	*/
	bool isChargingForItem();

	/**
	@brief	是否在液体中
	*/
	bool isInLiquid();

	/**
	@brief	获取每秒蓄力的消耗
	*/
	float getChargeConsumptionPerSecond();
	
	/*
	@deprecated
	*/
	void setBuffFoodSatLevel(float fSatLevel);

	/**
	@brief 属性变化时，立即更新最大值
	*/
	virtual void dropEquipItemsToJar(int itemid);
	virtual void updateStrength() override;
	virtual void updateOverflowStrength() override;

	/**
	@brief 毅力相关
	*/
	void addPerseverance(float val);
	void setPerseverance(float val, bool force = true);
	float getPerseverance();
	int equipSlot2Index(EQUIP_SLOT_TYPE t);
	/**
	@brief 复活时的体力,血量
	*/
	float getReviveHp();
	float getReviveStrength();

	/**
	@brief	获取玩家属性
	*/
	float getPlayerAttribute(PlayerAttributeType type);
	/*
	*	获取是否属性变身状态
	*/
	virtual bool getAttrShapeShift() override { return m_isAttrShapeShift; }
	//tolua_end
	
	virtual void setSpeedAtt(int type, float v);
	virtual float getSpeedAtt(int type);
	void setLowerBodyWaterPressuer(int val) { m_LowerBodyWaterPressure = val; }
	int getLowerBodyWaterPressuer() { return m_LowerBodyWaterPressure; }
	// GM指令专用
	void addHPDirectly(float hp);

	void addFoodLevel(float val, bool isSendMsg = true);
	void addThirst(float value, bool isSendMsg = true);
	
	virtual void execEffect(BuffAttrType type, std::vector<StatusAttInfo>& vSAInfo) override;

	/**
	@brief	判断静止
	*/
	bool isStill();

	bool getComputerOrderIsUsed(int itemId)//判断电脑方块指令是否已经使用  对应着luaconst的computerItemAddList
	{
		for (int i = 0; i < ComputerOrderUsed.size(); i++)
		{
			if (ComputerOrderUsed[i] == itemId)
			{
				return true;
			}
		}
		return false;
	}
	bool addComputerOrderIsUsed(int itemId)//设置电脑方块指令已经使用
	{
		if (getComputerOrderIsUsed(itemId)) return false;
		ComputerOrderUsed.push_back(itemId);
		return true;
	}

	std::vector<int> getComputerOrderVec()
	{
		return ComputerOrderUsed;
	}

	virtual void setOffLine(bool b) { m_isOffline = b; };
	virtual bool getOffLine() override {
		return m_isOffline;
	};

	void SetBlueprintPlaceDirIdx(int idx);//设置建筑蓝图放置方向索引
	void SetRotateBuildingPreview();//设置旋转建筑预览
	int getRotateBuildingPreview();
protected:
	float getMoveBaseSpeed(int type);
	/**
	@deprecated
	*/
	virtual void staminaUsed(float v);

	// 伤害值 ，造成伤害者
	virtual void damageArmor(float points, ClientActor *attacker); 

	void temperatureTick_Server();
	void temperatureTick_Client();
	
	void radiationTick_Server();
	void radiationTick_Client();
	
	void foodTick_Server();
	void foodTick_Client();

	float calculateTotalModification(PlayerAttributeType attributeType);

	/**
	@brief	持续恢复HP
	*/
	void hpRestoreTick();

	/**
	@brief	
	- 跑步：跑步时每秒持续扣除
	- 游泳：游泳时每秒持续扣除
	- 破坏方块：每秒破坏持续扣除
	- 武器蓄力：蓄力时每秒持续扣除
	  - 武器蓄力包括——蓄力拉弓、武器技能、法杖施法；
	  - 使用道具蓄力时，道具的技能CD取消，仅消耗体力
	  - 以上武器数值支持独立配置，在tooldef表增加蓄力体力消耗字段；
	*/
	bool needConsumingStrength();
	bool needConsumingThirst();

	void backPackTick();
	void strengthTick();
	void checkNeedConsumeStrength();//检查蓄力后是否需要持续消耗体力
	void checkNeedConsumeThirst();//检查蓄力后是否需要持续消耗体力

	bool shouldApplyBuff(int buffId, int buffLevel);
	/**
	@brief	检查属性buff
	*/
	void checkAttributeBuffs(const PlayerAttributeType type);

	void showAttributeTips();

	/**
	@brief	持续恢复体力
	*/
	void strengthRestoreTick();
	//=====================================================================//
	
	/**
	@brief	通知疲劳状态
	*/
	void onExhaustion();

	virtual bool attackedFrom(OneAttackData &atkdata, ClientActor *attacker = NULL);
	/**
	@brief	检查当前体力值,并赋予玩家对应buff. 老代码里的没有用这个函数替换
	*/
	void checkStrengthBuff();	
	/**
	@brief	检查当前口渴值,并赋予玩家对应buff. 老代码里的没有用这个函数替换
	*/
	void checkThirstBuff();

	/*
*	是否拥有属性变身 buff
*/
	virtual bool isHasAttrShapeShiftBuff(int buffid, int bufflv) override;
	/*
	*	设置属性变身状态，属性来自 mobid 得生物属性
	*/
	virtual void setAttrShapeShift(bool change, int mobid = 0) override;

	/*
	*/
	void waterPressureTick();


	/*
	口渴tick
	*/
	void thirstTick_Server();
	void thirstTick_Client();

	/**
	@brief	持续恢复体力
	*/
	void thirstRestoreTick();

public:
	//tolua_begin
	float m_FoodLevel; //饥饿值

	/**
	@deprecated
	*/
	float m_FoodSatLevel;  //饥饿耐力

	/**
	@deprecated
	*/
	float m_MaxFoodLvl;//最大饥饿值
	
	/**
	@deprecated
	*/
	float m_UsedStamina; //消耗的体力累积

	/**
	@deprecated
	*/
	int m_FoodAccumTick; //饥饿计时器
	/**
	@deprecated
	@brief	使用体力代替饥饿值。兼容旧版本显示。
	@pb PB_PlayerArchEntityCH
	@pb PB_RoleData
	@fbs RoleArchData
	@fbs ActorPlayer
	@struct ArchiveEntity：只写不读？存基础值
	*/
	bool m_bUseCompatibleStrength;

	/**
	@brief 标记体力值饥饿值显示状态，0为都不显示 1饥饿值 2体力值
	@		由于m_bUseCompatibleStrength是布尔值，且已经落地。
	@       为了支持第三种情况，加入这个值 -- by chenshaobin 2023.8.17
	@pb PB_PlayerArchEntityCH
	@pb PB_RoleData
	@fbs RoleArchData
	@fbs ActorPlayer
	*/
	int m_StrengthFoodShowState;


	BackPack *m_Backpack;

	int tickCount; 
	int m_CurShotcut; //当前正在使用的快捷栏位
	int m_CurShotcutEdit; //编辑模式当前正在使用的快捷栏位
	bool m_CanEatFood; //是否可以吃食物

	volatile bool m_bInitialized;

	PlayerLevelMode* m_pLevelMode;			//等级经验模式
	PlayerBaseAttrSetter* m_pBaseAttrSetter; //玩法模式, 基础属性设置
	//tolua_end

	std::vector<ActorBuff> m_oldBuffs;// 属性变身后存取旧有身上的buff
	float m_oldLife;
	float m_oldStrength;
	float m_oldFoodLevel;
	int m_WaterDepth;// 水压系统得水深
	int m_LowerWaterDepth;// 下半身水深

	CraftingQueue* m_CraftingQueue;
	int getCurBuildingId();
private:
	/**
	@brief	运行期间存在大量调用。直接与ClientPlayer互绑
	*/
	ClientPlayer* m_ClientPlayer;


	volatile float m_fStrength;
	
	/**
	@brief	基础最大体力
	*/
	float m_fBasicMaxStrength;
	
	/**
	@brief	最大体力
	*/
	float m_fMaxStrength;
	
	/**
	@brief	基础体力溢出区。与HP的溢出区的功能有差异
	*/
	float m_fBasicOverflowStrength;
	
	/**
	@brief	体力溢出区
	*/
	float m_fOverflowStrength;
	
	/**
	@brief	体力恢复倍率。以基础体力恢复速度为准。
	*/
	float m_fStrengthRestoreFactor;
	
	/**
	@brief	基础体力恢复速度。当前默认1点。由客户端固定默认值。
	*/
	float m_fBasicStrengthRestore;
	
	/**
	@brief	使用道具蓄力每秒消耗体力
	*/
	float m_fStrengthConsumptionOfChargingPerSecond;
	
	/**
	@brief	疲劳时的体力最大值。分母为最大值。
	*/
	float m_fMaxStrengthForExhaustion;
	
	/**
	@deprecated
	@brief	疲劳时的体力最大百分比。分母为最大值。
	*/
	float m_fMaxPercentageOfStrengthForExhaustion;
	
	/**
	@brief	当体力清零时，开启疲劳状态。恢复到固定百分比后取消。
	*/
	bool m_bIsExhausted;
	
	/**
	@brief	HP恢复计时标记
	*/
	int m_StillnessTick;
	
	/**
	@brief	体力恢复计时标记
	*/
	int m_StrengthTick;

	/**
	@brief	自然恢复计时标记
	*/
	int m_NaturalTick;
	
	unsigned int m_Exp; //
	std::map<int, int> m_ItemAttMap; //玩家道具特殊属性std::map  [itemid, attaction]
	
	/**
	@brief	HP恢复周期
	*/
	static const int MIN_STILL_TICK;
	
	/**
	@brief	体力恢复周期
	*/
	static const int MIN_STRENGTH_CONSUMPTION_TICK;

	/**
	@brief	与体力相关的execute
	*/
	std::vector<ActorAttribType> m_StrengthExecute;

	/**
	@brief 脱离体力透支
	*/
	int m_maxOverDraw;

	/**
	@brief 体力透支
	*/
	int m_minOverDraw;

	/**
	@brief 复活后的生命值.这个为百分比
	*/
	int m_actor_revive_hp;

	/**
	@brief 复活的体力值.这个为百分比
	*/
	int m_actor_revive_strength;
	
	float m_fSpeedProtect[Actor_Speed_Type_Count];
	bool m_bSpeedProtected[Actor_Speed_Type_Count];

	/*
	是否属性变身，获得一套生物属性来替代当前一些属性
*/
	bool m_isAttrShapeShift;
	float m_oldBasicMaxHP;
	float m_oldBasicMaxStrength;
	int m_AttrShapeShiftTick;

	int m_LowerBodyWaterPressure;// 人物下半身水压
	int m_WaterPressureTick;
	int m_Lowerm_WaterPressureTick;
	unsigned long  m_StarDebuffTime;//星星debuff时间
	int  m_StarDebuffStage;//星星debuff时间
	bool m_chekckNeedConsumeStrength;//蓄力时是否扣除体力标记
	std::vector<int> ComputerOrderUsed;//电脑命令获得的物品

	/**
	@brief	口渴恢复计时标记
	*/
	int m_ThirstTick;

	bool m_chekckNeedConsumeThirst;//蓄力时是否扣除口渴标记
	//volatile float m_fThirst;
	///**
	//@brief	基础最大口渴
	//*/
	//float m_fBasicMaxThirst;

	///**
	//@brief	最大口渴
	//*/
	//float m_fMaxThirst;

	///**
	//@brief	基础口渴溢出区。与HP的溢出区的功能有差异
	//*/
	//float m_fBasicOverflowThirst;

	///**
	//@brief	口渴溢出区
	//*/
	//float m_fOverflowThirst;

	/**
	@brief	口渴恢复倍率。以基础体力恢复速度为准。
	*/
	float m_fThirstRestoreFactor;

	/**
	@brief	基础口渴恢复速度。当前默认1点。由客户端固定默认值。
	*/
	float m_fBasicThirstRestore;

	/**
	@brief	使用道具蓄力每秒消耗口渴
	*/
	float m_fThirstConsumptionOfChargingPerSecond;

	/**
	@brief	与口渴相关的execute
	*/
	std::vector<ActorAttribType> m_ThirstExecute;
	/*
	* 口渴值
	*/
	MNSandbox::AutoRef<ThirstValue> m_ThirstValue;
	MNSandbox::AutoRef<FoodValue> m_FoodValue;

	std::map<int, float> m_FoodCostMap;
	std::map<int, float> m_ThirstCostMap;

	float m_lastFoodValue;
	float m_lastThirstValue;

	float m_MaxRadiation;
	uint64_t m_lastRadiationRecoverTime;

	uint64_t m_lastTickTime;
	uint64_t m_lastTickTimeClient;

	bool m_isOffline;

	// 通知监听
	MNSandbox::AutoRef<MNSandbox::Listener<bool&, int, bool>> m_listenerEatFood;
	MNSandbox::AutoRef<MNSandbox::Listener<bool&, int, int, int>> m_listenerDrinkWater;
	MNSandbox::AutoRef<MNSandbox::Listener<int&, int>> m_listenerPlayAttrib1;
	MNSandbox::AutoRef<MNSandbox::Listener<int, int, int, World*>> m_listenerPlayAttrib2;
	MNSandbox::AutoRef<MNSandbox::Listener<float&>> m_listenerPlayAttrib3;
	MNSandbox::AutoRef<MNSandbox::Listener<int&>> m_listenerPlayAttrib4;
	MNSandbox::AutoRef<MNSandbox::Listener<float&>> m_listenerPlayAttrib5;
	MNSandbox::AutoRef<MNSandbox::Listener<int&>> m_listenerPlayAttrib6;
	MNSandbox::AutoRef<MNSandbox::Listener<float&, int&, int&>> m_listenerPlayAttrib8;
	MNSandbox::AutoRef<MNSandbox::Listener<int&, bool&, float&, int&, IClientPlayer*, float, float>> m_listenerPlayAttrib9;

public:

	bool isPlayerDowned();
	PlayerDownedStateAttrib* getDownedStateAttrib() { return m_pDownedStateAttrib; };

	virtual void setHP(float hp, bool overflowable = false) override;
	// 在 PlayerAttrib 类的公共接口添加:
	virtual void addHPByTrueDamage(float hp, bool overflowable = false) override;
	/**
	 * @brief 检查是否满足进入重伤状态的条件，并在满足时进入重伤状态
	 * @param damageAmount 本次受到的伤害值
	 * @return 是否成功进入重伤状态
	 */

	void addHPByTrueDamage2(float hp, bool overflowable = false);
	bool checkAndEnterDownedState(float damageAmount);

	/**
	 * @brief 检查玩家是否处于死亡状态
	 * @return 如果玩家死亡返回true，否则返回false
	 */
	bool isDead() ;

private:
	PlayerDownedStateAttrib* m_pDownedStateAttrib;  // 倒地状态系统

	int m_CurBuilldingId; //建筑蓝图ID
	int m_BlueprintPlaceDirIdx; //旋转建筑预览
};//tolua_exports

#endif
