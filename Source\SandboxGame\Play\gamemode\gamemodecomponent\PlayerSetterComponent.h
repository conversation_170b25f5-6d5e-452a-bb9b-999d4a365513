﻿
#ifndef __PLAYER_SETTER_COMPONENT_H__
#define __PLAYER_SETTER_COMPONENT_H__

#include "SandboxComponent.h"
#include "SandboxCoreFactorys.h"
#include "GameModeDef.h"
#include "WorldSave_generated.h"


//玩家设置组件（挂载于GameMode）
class PlayerSetterComponent : public MNSandbox::SceneComponent//tolua_exports
{//tolua_exports
	DECLARE_COMPONENTCLASS(PlayerSetterComponent)
public:
	//tolua_begin
	PlayerSetterComponent();
	virtual ~PlayerSetterComponent();

	const char* getBaseModelID() { return m_BaseModelId.c_str(); }
	float getBaseModelScale() { return m_BaseModelScale; }
	void setBaseModel(const char* modelId, float scale = 1.0f);

	const char* getBaseModelData() { return m_BaseModelData.c_str(); }
	void setBaseModelData(const char* modelJsonData);
	void setBaseModelDataById(const std::string& modelId);

	float getBaseAttr(PRIME_ATTR_TYPE attrType);
	void setBaseAttr(PRIME_ATTR_TYPE attrType, float attrValue);

	GameBaseItem* getBaseItem(int index);
	void setBaseItem(int index, int itemId, int num = 1);

	void InitAttrWithDefualt(int ctype = 0); //默认值

	// 设置权限状态
	bool getPermitState(int pType);
	void setPermitState(int pType, bool enable);

	// 设置功能开启
	bool getCustomFuncState(int fType);
	void setCustomFuncState(int fType, bool enable);

	bool getIsStrengthUsed() { return m_StrengthUsed; }
	void setStrengthUsed(bool usedflag) { m_StrengthUsed = usedflag; }

	int getStrengthFoodShowState() { return m_StrengthFoodShowState; }
	void setStrengthFoodShowState(int usedflag) { m_StrengthFoodShowState = usedflag; }
	

	// FlatBuffer相关
	void load(const FBSave::PlayerSetterData* src, int version = 0);
	flatbuffers::Offset<FBSave::PlayerSetterData> save(flatbuffers::FlatBufferBuilder &builder);

	//tolua_end
public:
	//tolua_begin
	//为兼容自定义模型ID改成string
	std::string m_BaseModelId;
	int m_BaseModelType;
	float m_BaseModelScale;

	std::string m_BaseModelData;//客机需拿到完整的模型数据

	short m_BaseAttrs[MAX_PATTR_TYPE];	 //初始属性
	std::map<int, GameBaseItem*> m_BaseItems;//初始物品

	unsigned int m_BasePermitStates; //初始权限
	unsigned int m_CustomFuncStates; //启用对应功能

	bool m_StrengthUsed;	//启用体力值
	int m_StrengthFoodShowState; // 饥饿值体力值显示状态 0为都不显示 1饥饿值 2体力值
	//tolua_end
};//tolua_exports

#endif
