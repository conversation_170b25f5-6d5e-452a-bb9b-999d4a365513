#include "container_onequarterblock.h"
#include "Collision.h"
#include "DefManagerProxy.h"
//#include "FullyCustomModelMgr.h"
//#include "BlockMaterialMgr.h"
//#include "BlockScene.h"
//#include "WorldManager.h"
//#include "DefManagerProxy.h"
//#include "File/FileManager.h"
#include "ClientActorDef.h"
#include "world.h"
#include "CoreCommonDef.h"

using namespace MINIW;

ContainerOneQuarterBlock::ContainerOneQuarterBlock() : WorldContainer(), m_iPlaceType(-1), m_bNeedDel(false)
{
	m_OneQuarterBlockDatas.clear();
	m_NeedTick = true;
}

ContainerOneQuarterBlock::ContainerOneQuarterBlock(const WCoord &blockpos) : WorldContainer(blockpos, 0), m_iPlaceType(-1), m_bNeedDel(false)
{
	m_OneQuarterBlockDatas.clear();
	m_NeedTick = true;
}

ContainerOneQuarterBlock::~ContainerOneQuarterBlock()
{
	m_OneQuarterBlockDatas.clear();
}

int ContainerOneQuarterBlock::getObjType() const
{
	return OBJ_TYPE_ONEQUARTERBLOCK;
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerOneQuarterBlock::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::OneQuarterBlockData>>> blockdataoffset = 0;

	std::vector<flatbuffers::Offset<FBSave::OneQuarterBlockData>> blocksdata;
	for (size_t i = 0; i < m_OneQuarterBlockDatas.size(); i++)
	{
		blocksdata.push_back(FBSave::CreateOneQuarterBlockData(builder, m_OneQuarterBlockDatas[i].posindex));
	}

	blockdataoffset = builder.CreateVector(blocksdata);

	auto container = FBSave::CreateContainerOneQuarterBlock(builder, basedata, m_iPlaceType, blockdataoffset);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerOneQuarterBlock, container.Union());
}

bool ContainerOneQuarterBlock::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerOneQuarterBlock *>(srcdata);
	loadContainerCommon(src->basedata());
	m_iPlaceType = src->placetype();

	m_OneQuarterBlockDatas.clear();

	if (src->onequarterinfo())
	{
		for (size_t i = 0; i < src->onequarterinfo()->size(); i++)
		{
			auto data = src->onequarterinfo()->Get(i);
			if (data)
			{
				OneQuarterBlockData blockdata;
				blockdata.posindex = data->posindex();

				//添加重复判断 by：Jeff
				bool isIndexRepeat = false;
				for (size_t i = 0; i < m_OneQuarterBlockDatas.size(); i++)
				{
					if (m_OneQuarterBlockDatas[i].posindex == blockdata.posindex)
					{
						isIndexRepeat = true;
						break;
					}

				}
				if (!isIndexRepeat)
				{
					m_OneQuarterBlockDatas.push_back(blockdata);
				}
			}
		}
	}

	return true;
}

void ContainerOneQuarterBlock::enterWorld(World *pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();

	auto blockdef = GetDefManagerProxy()->getBlockDef(pworld->getBlockID(m_BlockPos));
	if (!blockdef || blockdef->Type != "onequarter")
	{
		m_bNeedDel = true;
		return;
	}
}

void ContainerOneQuarterBlock::updateTick()
{
	if (m_bNeedDel && m_World)
	{
		m_World->getContainerMgr()->destroyContainer(m_BlockPos);
		return;
	}
}

void ContainerOneQuarterBlock::changeDataByRotateBluePrint(int rotatetype)
{
	for (size_t i = 0; i < m_OneQuarterBlockDatas.size(); i++)
	{
		auto &data = m_OneQuarterBlockDatas[i];
		if (m_iPlaceType == OQ_PLACE_TYPE_DIR_Z || m_iPlaceType == OQ_PLACE_TYPE_DIR_X)
		{
			if ((rotatetype == MIRROR_90 || rotatetype == ROTATE_180) ||
				(m_iPlaceType == OQ_PLACE_TYPE_DIR_Z && (rotatetype == MIRROR_0 || rotatetype == ROTATE_270)) ||
				(m_iPlaceType == OQ_PLACE_TYPE_DIR_X && rotatetype == MIRROR_180 || rotatetype == ROTATE_90))
			{
				if (data.posindex % 2)
					data.posindex -= 1;
				else
					data.posindex += 1;
			}
		}
	}
}

void ContainerOneQuarterBlock::convertRotateByBluePrint(int rotatetype)
{
	if (m_iPlaceType == OQ_PLACE_TYPE_DIR_Z || m_iPlaceType == OQ_PLACE_TYPE_DIR_X)
	{
		changeDataByRotateBluePrint(rotatetype);

		if (rotatetype == ROTATE_90 || rotatetype == MIRROR_270 || rotatetype == ROTATE_270 || rotatetype == MIRROR_90)
		{
			if (m_iPlaceType == OQ_PLACE_TYPE_DIR_Z)
				m_iPlaceType = OQ_PLACE_TYPE_DIR_X;
			else  
				m_iPlaceType = OQ_PLACE_TYPE_DIR_Z;

		}		
		
		if(m_World)
			m_World->markBlockForUpdate(m_BlockPos);
	}
}

int ContainerOneQuarterBlock::getPlacePosIndex(const Rainbow::Vector3f &colpoint, bool placeinto, int face)
{
	int posIndex = -1;
	if (placeinto)
	{
		if (m_iPlaceType == OQ_PLACE_TYPE_DIR_Y)
		{
			if ((face == DIR_POS_X && colpoint.z > 0.5) ||
				(face == DIR_NEG_Z && colpoint.x <= 0.5))
			{
				posIndex = 0;
			}
			else if((face == DIR_NEG_X && colpoint.z <= 0.5) ||
				(face == DIR_POS_Z && colpoint.x > 0.5))
			{
				posIndex = 1;
			}
			else if ((face == DIR_POS_X && colpoint.z <= 0.5) ||
				(face == DIR_POS_Z && colpoint.x <= 0.5))
			{
				posIndex =2;
			}
			else if ((face == DIR_NEG_X && colpoint.z > 0.5) ||
				(face == DIR_NEG_Z && colpoint.x > 0.5))
			{
				posIndex = 3;
			}
		}
		else if (m_iPlaceType == OQ_PLACE_TYPE_DIR_Z)
		{
			if ((face == DIR_POS_X && colpoint.y <= 0.5) ||
				(face == DIR_POS_Y && colpoint.x <= 0.5))
			{
				posIndex = 0;
			}
			else if ((face == DIR_NEG_X && colpoint.y <= 0.5) ||
				(face == DIR_POS_Y && colpoint.x > 0.5))
			{
				posIndex = 1;
			}
			else if ((face == DIR_POS_X && colpoint.y > 0.5) ||
				(face == DIR_NEG_Y && colpoint.x <= 0.5))
			{
				posIndex = 2;
			}
			else if ((face == DIR_NEG_X && colpoint.y > 0.5) ||
				(face == DIR_NEG_Y && colpoint.x > 0.5))
			{
				posIndex = 3;
			}
		}
		else if (m_iPlaceType == OQ_PLACE_TYPE_DIR_X)
		{
			if ((face == DIR_POS_Z && colpoint.y <= 0.5) ||
				(face == DIR_POS_Y && colpoint.z <= 0.5))
			{
				posIndex = 0;
			}
			else if ((face == DIR_NEG_Z && colpoint.y <= 0.5) ||
				(face == DIR_POS_Y && colpoint.z > 0.5))
			{
				posIndex = 1;
			}
			else if ((face == DIR_POS_Z && colpoint.y > 0.5) ||
				(face == DIR_NEG_Y && colpoint.z <= 0.5))
			{
				posIndex = 2;
			}
			else if ((face == DIR_NEG_Z && colpoint.y > 0.5) ||
				(face == DIR_NEG_Y && colpoint.z > 0.5))
			{
				posIndex = 3;
			}
		}
	}
	else
	{
		if (m_iPlaceType == OQ_PLACE_TYPE_DIR_Y)
		{
			if (((face == DIR_NEG_Y || face == DIR_POS_Y) && colpoint.x <= 0.5 && colpoint.z <= 0.5) ||
				(face == DIR_NEG_X && colpoint.z <= 0.5) ||
				(face == DIR_NEG_Z && colpoint.x <= 0.5))
			{
				posIndex = 2;
			}
			else if (((face == DIR_NEG_Y || face == DIR_POS_Y) && colpoint.x <= 0.5 && colpoint.z > 0.5) ||
				(face == DIR_NEG_X && colpoint.z > 0.5) ||
				(face == DIR_POS_Z && colpoint.x <= 0.5))
			{
				posIndex = 0;
			}
			else if (((face == DIR_NEG_Y || face == DIR_POS_Y) && colpoint.x > 0.5 && colpoint.z <= 0.5) ||
				(face == DIR_POS_X && colpoint.z <= 0.5) ||
				(face == DIR_NEG_Z && colpoint.x > 0.5))
			{
				posIndex = 1;
			}
			else if (((face == DIR_NEG_Y || face == DIR_POS_Y) && colpoint.x > 0.5 && colpoint.z > 0.5) ||
				(face == DIR_POS_X && colpoint.z > 0.5) ||
				(face == DIR_POS_Z && colpoint.x > 0.5))
			{
				posIndex = 3;
			}
		}
		else if (m_iPlaceType == OQ_PLACE_TYPE_DIR_Z)
		{
			if (((face == DIR_NEG_Z || face == DIR_POS_Z) && colpoint.x <= 0.5 && colpoint.y <= 0.5) ||
				(face == DIR_NEG_Y && colpoint.x <= 0.5) ||
				(face == DIR_NEG_X && colpoint.y <= 0.5) )
			{
				posIndex = 0;
			}
			else if (((face == DIR_NEG_Z || face == DIR_POS_Z) && colpoint.x > 0.5 && colpoint.y <= 0.5) ||
				(face == DIR_NEG_Y && colpoint.x > 0.5) ||
				(face == DIR_POS_X && colpoint.y <= 0.5))
			{
				posIndex = 1;
			}
			else if (((face == DIR_NEG_Z || face == DIR_POS_Z) && colpoint.x <= 0.5 && colpoint.y > 0.5) ||
				(face == DIR_POS_Y && colpoint.x <= 0.5) ||
				(face == DIR_NEG_X && colpoint.y > 0.5))
			{
				posIndex = 2;
			}
			else if (((face == DIR_NEG_Z || face == DIR_POS_Z) && colpoint.x > 0.5 && colpoint.y > 0.5) ||
				(face == DIR_POS_Y && colpoint.x > 0.5) ||
				(face == DIR_POS_X && colpoint.y > 0.5))
			{
				posIndex = 3;
			}
		}
		else if (m_iPlaceType == OQ_PLACE_TYPE_DIR_X)
		{
			if (((face == DIR_NEG_X || face == DIR_POS_X) && colpoint.z <= 0.5 && colpoint.y <= 0.5) ||
				(face == DIR_NEG_Y && colpoint.z <= 0.5) ||
				(face == DIR_NEG_Z && colpoint.y <= 0.5))
			{
				posIndex = 0;
			}
			else if(((face == DIR_NEG_X || face == DIR_POS_X) && colpoint.z > 0.5 && colpoint.y <= 0.5) ||
				(face == DIR_NEG_Y && colpoint.z > 0.5) ||
				(face == DIR_POS_Z && colpoint.y <= 0.5))
			{
				posIndex = 1;
			}
			else if (((face == DIR_NEG_X || face == DIR_POS_X) && colpoint.z <= 0.5 && colpoint.y > 0.5) ||
				(face == DIR_POS_Y && colpoint.z <= 0.5) ||
				(face == DIR_NEG_Z && colpoint.y > 0.5))
			{
				posIndex = 2;
			}
			else if (((face == DIR_NEG_Z || face == DIR_POS_Z) && colpoint.z > 0.5 && colpoint.y > 0.5) ||
				(face == DIR_POS_Y && colpoint.z > 0.5) ||
				(face == DIR_POS_Z && colpoint.y > 0.5))
			{
				posIndex = 3;
			}
		}
	}

	return posIndex;
}

void ContainerOneQuarterBlock::addBlockDataByPlace(int dir, const Rainbow::Vector3f &colpoint, bool placeinto, int face)
{
	if (m_iPlaceType < 0)
	{
		if (dir == DIR_POS_Y || dir == DIR_NEG_Y)
		{
			m_iPlaceType = OQ_PLACE_TYPE_DIR_Y;
		}
		else if (dir == DIR_NEG_X || dir == DIR_POS_X)
		{
			m_iPlaceType = OQ_PLACE_TYPE_DIR_Z;
		}
		else if (dir == DIR_NEG_Z || dir == DIR_POS_Z)
		{
			m_iPlaceType = OQ_PLACE_TYPE_DIR_X; 
		}
	}

	int posIndex = getPlacePosIndex(colpoint, placeinto, face);

	if (posIndex >= 0)
	{
		//添加重复判断 by：Jeff
		for (size_t i = 0; i < m_OneQuarterBlockDatas.size(); i++)
		{
			if (m_OneQuarterBlockDatas[i].posindex == posIndex)
				return;
		}
		OneQuarterBlockData data;
		data.posindex = posIndex;

		m_OneQuarterBlockDatas.push_back(data);
	}
}

bool ContainerOneQuarterBlock::canPlacedAgain(const Rainbow::Vector3f &colpoint, bool placeinto, int face)
{
	if (m_iPlaceType < 0)
		return false;
	int posIndex = getPlacePosIndex(colpoint, placeinto, face);

	if (posIndex < 0)
		return false;

	for (size_t i = 0; i < m_OneQuarterBlockDatas.size(); i++)
	{
		if (m_OneQuarterBlockDatas[i].posindex == posIndex)
			return false;
	}

	return true;
}

int ContainerOneQuarterBlock::getBlockGeomID(int *idbuf, int *dirbuf)
{
	for (size_t i = 0; i < m_OneQuarterBlockDatas.size(); i++)
	{
		if (i >= 32) //有数组越界崩溃，上层传入的数组长度为32，已经在push m_OneQuarterBlockDatas的时候加入了重复判断，这里再加个保护 by：Jeff
		{
			return 32;
		}
		auto &data = m_OneQuarterBlockDatas[i];
		if (m_iPlaceType == OQ_PLACE_TYPE_DIR_Y)
		{

			idbuf[i] = m_iPlaceType;
			dirbuf[i] = data.posindex;
		}
		else
		{
			idbuf[i] = data.posindex / 2 + 1;
			dirbuf[i] = data.posindex % 2 + m_iPlaceType/2*2;
		}
	}

	return m_OneQuarterBlockDatas.size();
}

bool ContainerOneQuarterBlock::createCollideData(CollisionDetect *coldetect, const WCoord &pos)
{
	if (m_iPlaceType < 0)
		return false;

	for (size_t i = 0; i < m_OneQuarterBlockDatas.size(); i++)
	{
		auto &data = m_OneQuarterBlockDatas[i];

		if (m_iPlaceType == OQ_PLACE_TYPE_DIR_Y)
		{
			if (data.posindex == 0)
			{
				coldetect->addObstacle(pos+WCoord(0, 0, 50), (pos + WCoord(50, BLOCK_SIZE, BLOCK_SIZE)));
			}
			else if (data.posindex == 1)
			{
				coldetect->addObstacle(pos + WCoord(50, 0, 0), (pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, 50)));
			}
			else if (data.posindex == 2)
			{
				coldetect->addObstacle(pos, (pos + WCoord(50, BLOCK_SIZE, 50)));
			}
			else if (data.posindex == 3)
			{
				coldetect->addObstacle(pos + WCoord(50, 0, 50), (pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE)));
			}
		}
		else if (m_iPlaceType == OQ_PLACE_TYPE_DIR_Z)
		{
			if (data.posindex == 0)
			{
				coldetect->addObstacle(pos, (pos + WCoord(50, 50, BLOCK_SIZE)));
			}
			else if (data.posindex == 1)
			{
				coldetect->addObstacle(pos + WCoord(50, 0, 0), (pos + WCoord(BLOCK_SIZE, 50, BLOCK_SIZE)));
			}
			else if (data.posindex == 2)
			{
				coldetect->addObstacle(pos + WCoord(0, 50, 0), (pos + WCoord(50, BLOCK_SIZE, BLOCK_SIZE)));
			}
			else if (data.posindex == 3)
			{
				coldetect->addObstacle(pos + WCoord(50, 50, 0), (pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE)));
			}
		}
		else if (m_iPlaceType == OQ_PLACE_TYPE_DIR_X)
		{
			if (data.posindex == 0)
			{
				coldetect->addObstacle(pos, (pos + WCoord(BLOCK_SIZE, 50, 50)));
			}
			else if (data.posindex == 1)
			{
				coldetect->addObstacle(pos + WCoord(0, 0, 50), (pos + WCoord(BLOCK_SIZE, 50, BLOCK_SIZE)));
			}
			else if (data.posindex == 2)
			{
				coldetect->addObstacle(pos + WCoord(0, 50, 0), (pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, 50)));
			}
			else if (data.posindex == 3)
			{
				coldetect->addObstacle(pos + WCoord(0, 50, 50), (pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE)));
			}
		}
	}

	return true;
}