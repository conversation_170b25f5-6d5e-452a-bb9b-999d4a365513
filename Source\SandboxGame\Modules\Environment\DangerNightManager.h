
#ifndef __DANGERNIGHTMANAGER_H__
#define __DANGERNIGHTMANAGER_H__

#include "SandboxMgrBase.h"
#include "world_types.h"
#include "Math/Color.h"
#include "DangerNightManagerInterface.h"
class World;
class SummonMonsterSiegeMgr;

struct DangerNightEffectInfo
{
	WCoord pos;
	int tick;
};

/*******************
*	危险夜晚管理类
*	author:: chenshaobin
*******************/

class DangerNightManager : public SandboxMgrBase, public DangerNightManagerInterface//tolua_exports
{//tolua_exports
public:
	DangerNightManager();
	virtual ~DangerNightManager();

	static DangerNightManager* create();
	void init();
	virtual void onDestroy();
	virtual void onTick();
	virtual void onGameModeChange(int iGameMode);//游戏模式切换
	virtual void onEnterWorld(MNSandbox::Object* pWorld);//进入地图
	virtual void onLeaveWorld();//离开地图
	void setActive(bool flag);
	//DangerNightManagerInterface
	virtual void setIsEanble(bool flag, bool isActive = true) override;

	void setFogRangeInfo(float fogStartStart, float fogEndStart, float fogStartEnd, float fogEndEnd);

	virtual float getCurFogEndRange() const
	{
		return m_fogCurEndRange;
	}

	virtual float getCurFogStartRange() const
	{
		return m_fogCurStartRange;
	}

	virtual bool isDangerNight() const
	{
		if (!m_IsEnable || !m_Active)
		{
			return false;
		}

		return m_IsNight;
	}

	bool canStartMonsterSiege();

	virtual bool isVoidNight() const override
	{
		return m_IsVoidNight;
	}
	bool isPreVoidNight() const
	{
		return m_IsPreVoidNight;
	}

	virtual const std::vector<int>& getVoidMonstertIDs() const 
	{
		return m_voidNightMonsters;
	}
	virtual const std::vector<int>& getVoidMonstertGenWeightDelta() const
	{
		return m_voidNightMonsterGenWeights;
	}
	virtual bool checkBlockMutations(World* pworld, const WCoord& blockpos) override;
	bool checkBlockResume(World* pworld, const WCoord& blockpos, const int resumeID = 0);
	void playMutateEffect(World* pworld, const WCoord& blockpos);
protected:

	void initVoidMonsterDef();
	// 更新
	void tickNormalNightEffect(int effectState);
	void tickEffectList();
	void tickSky(bool isEnter = true);
	//void TickVoidNight();
	// 检测当前游戏时间是否在夜晚的开启范围内
	bool checkHours();
	void setIsDangerNight(bool flag);
	void reshChunkBlock();
	void playParticleEffectForNight();
	void playParticleEffect(World* pWorld, const WCoord& blockPos, const char *effectStr);
	bool processOneBlockForPlayEffect(World* pWorld, const WCoord& blockPos);
	void updateEffectState();
	void updateFogInfo();
	bool isVoidNightDay(int day);

	bool m_Active = false;				// 功能是否开启，满足
	bool m_IsEnable = false;			// 额外标志位控制，是否开启危险夜晚
	bool m_IsNight = false;				// 夜晚标志
	bool m_IsVoidNight = false;			// 虚空之夜标志
	bool m_IsPreVoidNight = false;		// 前置虚空之夜标志
	bool m_CanTriggeringMonsterSiege = false;  // 允许开启怪物攻城
	//叶子变色需求暂时不上了
	//bool m_LeafVoidFormatSet = false;     //是否还需要再设置叶子虚空状态了
	//bool m_LeafNormalFormatSet = false;       //是否还需要再设置叶子为普通状态
	//int  m_LeafLastTick = 0;
	// 特效更新状态，0，稳定状态； 1，氛围加深； 2，氛围减弱
	int m_CurEffectState = 0;

	bool m_VoidNightSkyLerp = false;	// 虚空之夜
	float m_VoidNightLerpRate = 0.0f;	// 天空盒过度进度

	WCoord particleEffectRange = { 8, 10, 8 };
	int m_tickTimer = 0;
	int m_particleEffectTickCurMax = 60;
	int m_particleEffectTickMax = 60;
	// 稳定状态下的特效播放间隔
	int m_particleEffectTickStableMax = 12;
	int m_startHour = 18;
	int m_endHour = 6;
	int m_voidNightHintTime = 8;
	// 第一次虚空之夜的事件（第二天）
	int m_firstVoidNightDeltaDay = 2;
	// 第二次及以后虚空之夜的间隔时间
	int m_voidNightDeltaDay = 4;
	int m_monsterSiegeStartHour = 0;
	int m_effectStrongerEndHour = 22;
	int m_effectWeakerStartHour = 4;
	int m_particleEffectStrongerDeltaTick = 6;
	int m_particleEffectWeakerDeltaTick = 12;
	float m_fogCurStartRange = 0;
	float m_fogCurEndRange = 0;
	float m_fogStartRangeStart = 0;
	float m_fogEndRangeStart = 0;
	float m_fogStartRangeEnd = 0;
	float m_fogEndRangeEnd = 0;
	float m_fogLerpAlpha = 0.0f;
	int m_preMinute = 0;
	std::vector<DangerNightEffectInfo> m_effectList;

	//ColorRGBAf m_fogColor = { 1.0f, 0.0f, 0.0f, 1.0f };
	//Rainbow::ColorRGBAf m_SkyTopColor = { 0.2f, 0.0549f, 0.25098f, 1.0f };
	//Rainbow::ColorRGBAf m_SkyMidColor = { 0.2f, 0.0549f, 0.25098f, 1.0f };
	//Rainbow::ColorRGBAf m_SkyBottomColor = { 0.2f, 0.0549f, 0.25098f, 1.0f };

	//  - 沙子   ID：29
	//	- 草块   ID：100
	//	- 红土   ID：233
	//	- 礁石   ID：25
	//	- 石头   ID：104
	//	- 静态水 ID：3
	//	- 自然冰 ID：123
	std::vector<int> m_effectPlayBlocks;
	std::vector<int> m_voidNightMonsters;
	std::vector<int> m_voidNightMonsterGenWeights;
};//tolua_exports



#endif // __DANGERNIGHTMANAGER_H__