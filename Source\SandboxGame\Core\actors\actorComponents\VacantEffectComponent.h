#ifndef __VACANTEFFECT_COMPONENT_H__
#define __VACANTEFFECT_COMPONENT_H__

#include "BaseClass/SharePtr.h"
#include "ActorComponent_Base.h"
#include "ClientActor.h"
#include "math/CCGeometry.h"

namespace Rainbow {
    class MaterialInstance;
}
namespace fairygui
{
    class GLoader;
}

class VacantEffectComponent : public ActorComponentBase
{
public:
	DECLARE_COMPONENTCLASS(VacantEffectComponent)

    VacantEffectComponent();

	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
   
    void RenderUI();
    void start();
	~VacantEffectComponent();
protected:
    
private:
    int m_TextureAlpha;
    Rainbow::SharePtr<Rainbow::Texture2D> m_Texture;
    Rainbow::SharePtr<Rainbow::MaterialInstance> m_TextureMaterial;
    fairygui::GLoader* m_gloader;
    cocos2d::Rect m_Rect;
    int m_nShowTick;
};

#endif