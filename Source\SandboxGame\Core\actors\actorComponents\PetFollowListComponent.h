#ifndef __PET_FOLLOW_LIST_COMPONENT_H__
#define __PET_FOLLOW_LIST_COMPONENT_H__

#include <vector>
#include "WorldRole_generated.h"
#include "ActorComponent_Base.h"

class ClientMob;


class PetFollowListComponent : public ActorComponentBase
{
public:
	DECLARE_COMPONENTCLASS(PetFollowListComponent)

	PetFollowListComponent();

	void load(const flatbuffers::Vector<int64_t> * tamedFollows);
	const std::vector<int64_t>& getList() const { return m_TamedMobFollowObjidList;};
	bool addMobToTamedFollows(ClientMob* mob);
	void removeMobFromTamedFollows(ClientMob* mob);
	bool isInTamedFollows(ClientMob * mob);
protected:
	std::vector<int64_t> m_TamedMobFollowObjidList;
};


#endif