#include "ActorVortex.h"
#include "LuaInterfaceProxy.h"
#include "RiddenComponent.h"
#include "ClientPlayer.h"
#include "world.h"
#include "EffectManager.h"

IMPLEMENT_COMPONENTCLASS(VortexLocomotion)

static bool isActorMount(ClientActor* actor)
{
	auto RidComp = actor->getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		return true;
	}
	return actor->isSleeping() || actor->isRestInBed() || actor->getUsingEmitter() || actor->getSitting();
}

void VortexLocomotion::moveEntityWithHeading(float strafing, float forward)
{
	return;
}

void VortexLocomotion::tick()
{
	return;
}

bool VortexLocomotion::canWalkOnLiquid(bool iswater)
{
	return true;
}


ActorVortex::ActorVortex()
{
	;
}

bool ActorVortex::init(int monsterid)
{
	auto lua = GetLuaInterfaceProxy().get_lua_const();
	if (lua)
	{
		if (monsterid == 3242)
		{
			m_speed = lua->vortex_smaller_strain;
			m_maxDis = 300;
			m_spread = 100;
			//setNeedClear(40);
			m_maxAge = 40;
		}
		else
		{
			m_speed = lua->vortex_bigger_strain;
			m_maxDis = 700;
			m_spread = 300;
			//setNeedClear(100);
			m_maxAge = 100;
		}
	}
	return ClientMob::init(monsterid);
	//setObjType();
}

ActorLocoMotion* ActorVortex::newLocoMotion()
{
	return CreateComponent<VortexLocomotion>("VortexLocomotion");
}

bool ActorVortex::isBigger()
{
	return getDefID() == 3243;
}

void ActorVortex::tick()
{
	if (m_maxAge < m_LiveTicks)
	{
		setNeedClear();
		return;
	}
	auto& vortexPos = getPosition();
	auto mgr = getActorMgr();
	if (mgr && m_pWorld)
	{
		std::vector<ClientActor*> vortexs;
		vortexs.reserve(5);
		mgr->selectNearActors(vortexPos, 7 * BLOCK_SIZE, [](IClientActor* actor) {
			if (!actor)
			{
				return false;
			}
			return actor->getObjType() != OBJ_TYPE_VORTEX && !isActorMount(actor->ToCast<ClientActor>());
		}, [&vortexs](IClientActor* actor) {
			vortexs.emplace_back(actor->ToCast<ClientActor>());
		});
		for_each(vortexs.begin(), vortexs.end(), [this, &vortexPos](ClientActor* actor)
		{
			//ActorVortex* v = static_cast<ActorVortex*>(actor);
			if (actor && actor->getLocoMotion())
			{
				Rainbow::Vector3f actorToVortex;
				WCoord actorPos = actor->getPosition();
				actorToVortex.x = vortexPos.x - actorPos.x;
				actorToVortex.y = vortexPos.y - actorPos.y;
				actorToVortex.z = vortexPos.z - actorPos.z;
				/*if (actor->getObjType() == OBJ_TYPE_DROPITEM)
				{
					if (actorToVortex.LengthSqr() <= getSpreadSqt())
					{
						actor->setNeedClear();
					}
				}*/
				if (actorToVortex.LengthSqr() < getMaxDisSqt())
				{
					actorToVortex.NormalizeSafe();
					actorToVortex *= getSpeed();
					actor->getLocoMotion()->addVortexMotion(actorToVortex);
				}
			}
		});
	}
}

flatbuffers::Offset<FBSave::SectionActor> ActorVortex::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveMob(builder);
	auto npc = FBSave::CreateActorVortex(builder, basedata);
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorVortex, npc.Union());
}

bool ActorVortex::load(const void* srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorVortex*>(srcdata);
	if (!src || !ClientMob::load(src->mobdata(), version))
	{
		return false;
	}
	return true;
}

void ActorVortex::enterWorld(World* pworld)
{
	ClientMob::enterWorld(pworld);
	if (pworld && pworld->getEffectMgr())
	{
		if (isBigger())
		{
			pworld->getEffectMgr()->playParticleEffectCommonAsync(1, "prefab/particles/wl_chuansong_02.prefab",
				getPosition(), m_maxAge, 0, 0, false, 32);
		}
		else
		{
			pworld->getEffectMgr()->playParticleEffectCommonAsync(1, "prefab/particles/wl_chuansong_03.prefab",
				getPosition(), m_maxAge, 0, 0, false, 32);
		}
	}
}

void ActorVortex::leaveWorld(bool keep_inchunk)
{
	if (m_pWorld && m_pWorld->getEffectMgr())
	{
		if (isBigger())
		{
			m_pWorld->getEffectMgr()->stopParticleEffect("prefab/particles/wl_chuansong_02.prefab",
				getPosition(), false);
		}
		else
		{
			m_pWorld->getEffectMgr()->stopParticleEffect("prefab/particles/wl_chuansong_03.prefab",
				getPosition(), false);
		}
	}
	ClientMob::leaveWorld(keep_inchunk);
}