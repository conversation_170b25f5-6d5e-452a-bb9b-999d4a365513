#ifndef __CLIENT_ACTOR_BASE_LUA_H__
#define __CLIENT_ACTOR_BASE_LUA_H__

#include <vector>
#include <string>

#include "actors/clientActor/ClientActor.h"

class ClientActorBaseLua : public ClientActor { //tolua_exports
	DECLARE_SCENEOBJECTCLASS(ClientActorBaseLua)
public :
	ClientActorBaseLua();
	virtual ~ClientActorBaseLua();
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool load(const void *srcdata, int version) override;
	virtual int getObjType() const override;
}; //tolua_exports

#endif
