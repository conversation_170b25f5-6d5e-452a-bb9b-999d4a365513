
#include "IClientActor.h"
#include "container_collectingpipe.h"
#include "world.h"
#include "ClientItem.h"
#include "DefManagerProxy.h"
#include "ActorCSProto.h"
#include "container_pipeline.h"
#include "container_detectionpipe.h"

#define MAX_COLLECTING_PIPE_GRIDS  5

EXPORT_SANDBOXENGINE extern int CheckInsertItemIntoArray(BaseContainer* container, BackPackGrid* gridarray, int arraylen, const BackPackGrid& grid);

CollectingPipeContainer::CollectingPipeContainer() : WorldContainer(WCoord(0,0,0), 0), m_TransferTicks(-1)
{
	m_Grids.resize(MAX_COLLECTING_PIPE_GRIDS);
	checkItemTick = 0;
	for(size_t i=0; i<m_Grids.size(); i++) m_Grids[i].reset(m_BaseIndex+i);
	m_NeedTick = true;
}

CollectingPipeContainer::CollectingPipeContainer(const WCoord &blockpos) : WorldContainer(blockpos, 0), m_TransferTicks(-1)
{
	m_Grids.resize(MAX_COLLECTING_PIPE_GRIDS);
	checkItemTick = 0;
	for(size_t i=0; i<m_Grids.size(); i++) m_Grids[i].reset(m_BaseIndex+i);
	m_NeedTick = true;
}

CollectingPipeContainer::~CollectingPipeContainer()
{
}

int CollectingPipeContainer::getObjType() const
{
	return OBJ_TYPE_COLLECTING_PIPE;
}

void CollectingPipeContainer::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);

	registerUpdateTick();
}

void CollectingPipeContainer::leaveWorld()
{
	WorldContainer::leaveWorld();
}

void CollectingPipeContainer::updateTick()
{
	if(m_World->isRemoteMode()) return;

	m_TransferTicks--;
	if(m_TransferTicks > 0) return;
	m_TransferTicks = 0;
	m_BlockPosEx = m_BlockPos;

	bool r1 = outputItems();
	bool r2 = inputItems();
	if(r1 || r2)
	{
		m_TransferTicks = 8;
	}
}

bool CollectingPipeContainer::outputItems()
{
	int blockdata = m_World->getBlockData(m_BlockPos);
	int dir = blockdata;
	WCoord pos = NeighborCoord(m_BlockPos, dir);

	WorldContainer *container = m_World->getContainerMgr()->getContainer(pos);
	if(container)
	{
		ContainerPipeline* containerPipe = dynamic_cast<ContainerPipeline*>(container);
		if (containerPipe && dir != ReverseDirection(containerPipe->m_Dir))
		{
			return false;
		}
		for(size_t i=0; i<m_Grids.size(); i++)
		{
			if(!m_Grids[i].isEmpty())
			{
				int n = container->onInsertItem(m_Grids[i], 1, ReverseDirection(dir));
				if(n <= 0) continue;
				//if(n == 0) break;

				onSubtractItem(&m_Grids[i], 1);
				return true;
			}
		}
	}
	return false;
}

bool CollectingPipeContainer::inputItems()
{
	if (m_World == NULL) return false;

	if (m_World->getContainerMgr() == NULL) return false;

	int blockdata = m_World->getBlockData(m_BlockPos);
	int dir = blockdata;
	WCoord wpos = NeighborCoord(m_BlockPosEx, ReverseDirection(dir));
	WCoord pos = NeighborCoord(m_BlockPos, ReverseDirection(dir));

	WorldContainer *container = m_World->getContainerMgr()->getContainer(pos);
	if(container)
	{
		BackPackGrid *grid = container->onExtractItem(ReverseDirection(dir));
		if(grid && onInsertItem(*grid, 1, dir)>0)
		{
			container->onSubtractItem(grid, 1);
			return true;
		}
	}
	else
	{
		checkItemTick -= 1;
		if (checkItemTick > 0)
		{
			return false;
		}
		checkItemTick = GenRandomInt(0, 20);
		CollideAABB box;
		box.setPosDim(wpos * BLOCK_SIZE, WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
		std::vector<IClientActor*>items;
		m_World->getActorsOfTypeInBox(items, box, OBJ_TYPE_DROPITEM);
		if (!items.empty())
		{
			ClientItem* item = dynamic_cast<ClientItem*>(items[0]);
			assert(item);
			BackPackGrid* src = &item->m_ItemData;//��Ʒ��Ϣ������BackPackGrid
			if (src && src->getAttracted() == false)
			{
				src->setAttracted(true);
				int n = onInsertItem(item->m_ItemData, item->m_ItemData.getNum(), dir);
				if (n > 0)
				{
					item->onSubtractItem(n, 0);
					return true;
				}
			}
		}
	}

	return false;
}

void CollectingPipeContainer::dropItems()
{
	for(size_t i=0; i<m_Grids.size(); i++)
	{
		dropOneItem(m_Grids[i]);
	}
}

int CollectingPipeContainer::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (size_t i = 0; i < m_Grids.size(); i++)
		{
			if (m_Grids[i].isEmpty()) continue;

			storeGridData(pItemInfos->Add(), &m_Grids[i]);
		}
	}
	return m_Grids.size();
}

int CollectingPipeContainer::onInsertItem(const BackPackGrid &grid, int num, int params)
{
	return InsertItemIntoArray(this, &m_Grids[0], int(m_Grids.size()), grid, num);
}

bool CollectingPipeContainer::canInsertItem(const BackPackGrid& grid, int param)
{
	return CheckInsertItemIntoArray(this, &m_Grids[0], int(m_Grids.size()), grid) <= 0;
}

BackPackGrid *CollectingPipeContainer::onExtractItem(int params)
{
	for(size_t i=0; i<m_Grids.size(); i++)
	{
		if(!m_Grids[i].isEmpty()) return &m_Grids[i];
	}
	return NULL;
}

int CollectingPipeContainer::calComparatorInputOverride()
{
	return CalculateItemsComparatorInput(&m_Grids[0], int(m_Grids.size()));
}

bool CollectingPipeContainer::checkPutItem(int itemid, int num)
{
	const ItemDef *def = GetDefManagerProxy()->getItemDef(itemid);
	if (!def) return false;

	int putnum = 0;

	for (int i = 0; i < MAX_COLLECTING_PIPE_GRIDS; i++)
	{
		BackPackGrid &grid = m_Grids[i];
		if (grid.isEmpty())
		{
			putnum += def->StackMax;
			if (putnum >= num) return true;
		}
		if (grid.getItemID() == itemid)
		{
			putnum += (def->StackMax - grid.getNum());
			if (putnum >= num) return true;
		}
	}
	return false;
}

BackPackGrid *CollectingPipeContainer::getGridByItemID(int itemid)
{
	for (int i = 0; i < MAX_COLLECTING_PIPE_GRIDS; i++)
	{
		BackPackGrid &grid = m_Grids[i];
		if (grid.getItemID() == itemid)
		{
			return &grid;
		}
	}

	return NULL;
}

bool CollectingPipeContainer::checkEmptyGrid(int resid)
{
	for(int i=0; i<MAX_COLLECTING_PIPE_GRIDS; i++)
	{
		BackPackGrid &grid = m_Grids[i];
		if(grid.isEmpty())
		{
			return true;
		}
		if(grid.getItemID() == resid)
		{
			return true;
		}
	}
	return false;
}
int CollectingPipeContainer::addItem_byGridCopyData(const GridCopyData& grid)
{
	return InsertItemIntoArray_byGridCopyData(this, &m_Grids[0], MAX_COLLECTING_PIPE_GRIDS,  grid);
}

//int CollectingPipeContainer::addItem(int resid, int num, int durable /* = -1 */, int toughness/* = -1 */, int enchantnum/* =0 */, const int enchants[]/* =0 */, void* userdata, const char* userdata_str)
//{
//	return InsertItemIntoArray(this, &m_Grids[0], MAX_COLLECTING_PIPE_GRIDS, resid, num, durable, toughness, enchantnum, enchants, userdata, userdata_str);
//}

BackPackGrid *CollectingPipeContainer::index2Grid(int index)
{
	assert(index>=m_BaseIndex && size_t(index)<m_BaseIndex+m_Grids.size());

	return &m_Grids[index-m_BaseIndex];
}


bool CollectingPipeContainer::canPutItem(int index)
{
	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> CollectingPipeContainer::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);
	flatbuffers::Offset<FBSave::ItemGrid> items[32];
	unsigned char indices[32];
	int count = 0;

	for(size_t i=0; i<m_Grids.size(); i++)
	{
		if(!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			indices[count] = (unsigned char)i;
			count++;
		}
	}

	auto actor = FBSave::CreateContainerCollectingPipe(builder, basedata, builder.CreateVector(items, count), builder.CreateVector(indices,count), m_Grids.size(), m_TransferTicks, 0);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerCollectingPipe, actor.Union());
}

bool CollectingPipeContainer::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerFunnel *>(srcdata);
	loadContainerCommon(src->basedata());

	auto items = src->items();
	auto indices = src->indices();

	for(size_t i=0; i<items->size(); i++)
	{
		int index = indices->Get(i);
		assert(index>=0 && size_t(index)<m_Grids.size());

		m_Grids[index].load(items->Get(i));
	}

	m_TransferTicks = src->transferticks();
	return true;
}

void CollectingPipeContainer::clear()
{
	int maxgrids = getGridNum();
	for (int i = 0; i < maxgrids; i++){
		BackPackGrid& grid = m_Grids[i];
		if (!grid.isEmpty()) {
			grid.clear();
			afterChangeGrid(grid.getIndex());
		}
	}
}

int CollectingPipeContainer::addItemByCount(int itemid, int num)
{
	GridCopyData copydata;
	copydata.resid = itemid;
	copydata.num = num;
	return addItem_byGridCopyData(copydata);
}

void CollectingPipeContainer::removeItemByCount(int itemid, int num)
{
	int maxgrids = getGridNum();
	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid *grid = index2Grid(i + m_BaseIndex);
		if (grid && grid->getItemID() == itemid)
		{
			if (num >= grid->getNum())
			{
				num -= grid->getNum();
				grid->addNum(-grid->getNum());
				grid->clear();
				afterChangeGrid(grid->getIndex());
			}
			else
			{
				grid->addNum(-num);
				afterChangeGrid(grid->getIndex());
				return;
			}
		}
	}
}
