#include "ChangeColorComponent.h"
#include "ActorBody.h"
#include "world.h"
#include "GameNetManager.h"
#include "PlayerControl.h"
#include "CameraModel.h"
#include "SandboxIdDef.h"
#include "BlockMaterialBase.h"

IMPLEMENT_COMPONENTCLASS(ChangeColorComponent)

ChangeColorComponent::ChangeColorComponent()
	:m_DestSkinColor(0)
	,m_CurSkinColor(0)
	,m_ColorIncrements(0.0f)
	,m_CheckSkinColorTicks(0)
{

}

void ChangeColorComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void ChangeColorComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void ChangeColorComponent::OnTick()
{
	if (!GetOwner()) return ;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	World* pWorld = m_owner->getWorld();
	if (!pWorld->isRemoteMode())
	{
		//变色龙皮肤 判断脚下方块颜色
		m_CheckSkinColorTicks++;
		if (m_CheckSkinColorTicks == 30)
		{
			m_CheckSkinColorTicks = 0;

			WCoord blockPos = CoordDivBlock(m_owner->getPosition());
			auto blockId = pWorld->getBlockID(blockPos);
			if (blockId <= BLOCK_AIR || blockId == BLOCK_ALIEN_AIR) //身处空气方块时，取脚下方块颜色
				blockPos = blockPos - WCoord(0, 1, 0);

			checkChangeColor(blockPos);
		}
	}
	gradualChangeColor();
}

void ChangeColorComponent::onHandlePlayerBodyColor2Client(const PB_PlayerBodyColorHC &playerBodyColorHC)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (m_owner && m_owner->getBody())
	{
		setColorIncrements(0);
		setDestSkinColor(playerBodyColorHC.destcolor());
		setCurSkinColor(playerBodyColorHC.curcolor());
	}
}

void ChangeColorComponent::checkChangeColor(WCoord& blockpos)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	ActorBody* body = m_owner->getBody();
	World* pWorld = m_owner->getWorld();
	int uin = m_owner->getUin();
	if (body == NULL)
		return;

	if (body->getSkinID() != 34) //变色龙皮肤
		return;

	int blockid = pWorld->getBlockID(blockpos);

	auto* blockDef = GetDefManagerProxy()->getBlockDef(blockid);
	if (!blockDef)
		return;

	unsigned int color = ((blockDef->MiniColor & 0xff) << 16) | (blockDef->MiniColor & 0xff00) | ((blockDef->MiniColor & 0xff0000) >> 16);
	if (blockid == 100 && pWorld)
	{
		auto* biome = pWorld->getBiome(blockpos.x, blockpos.z);

		auto* blockDef = GetDefManagerProxy()->getBlockDef(blockid);
		if (biome && blockDef)
		{
			/*char name[256];
			sprintf(name, "%s_top", blockDef->Texture1);
			ShareTexture2D   *grasstex = RenderBlockMaterial::loadBlockTexture(name, blockDef->gamemod, 1);
			if (grasstex)
			{
				LockResult lr;
				unsigned char *grassbits = (unsigned char *)grasstex->lock(0, 0, true, lr);

				ColorQuad biomeColor = ColorQuad(biome->GrassColor);
				unsigned char *colorbits = (unsigned char *)&biomeColor;
				int b = grassbits[0] * colorbits[0] / 255;
				int g = grassbits[1] * colorbits[1] / 255;
				int r = grassbits[2] * colorbits[2] / 255;
				grasstex->unlock(0, 0);

				color = ColorQuad(r, g, b).c;
			}*/
			color = biome->GrassColor;
		}
	}
	else if (pWorld)
	{
		BlockMaterial* mtl = pWorld->getBlockMaterial(blockpos);
		if (mtl && mtl->isColorableBlock())
		{
			auto blockcolor = mtl->getBlockColor(pWorld->getBlockData(blockpos));
			color = Rainbow::ColorQuad(blockcolor.r, blockcolor.g, blockcolor.b).c;
		}
	}

	int bodyColor = body->getBodyColor();
	if (color != m_DestSkinColor) //当前方块的颜色和目标颜色不一致
	{
		m_DestSkinColor = color;
		m_ColorIncrements = 0;
		m_CurSkinColor = bodyColor;
		if (m_CurSkinColor == 0)
			m_CurSkinColor = 10024447;

		//TODO同步给客机
		PB_PlayerBodyColorHC playerBodyColorHC;
		playerBodyColorHC.set_destcolor(m_DestSkinColor);
		playerBodyColorHC.set_curcolor(m_CurSkinColor);
		playerBodyColorHC.set_uin(uin);

		GetGameNetManagerPtr()->sendBroadCast(PB_PLAYER_BODY_COLOR_HC, playerBodyColorHC);
	}
}

void ChangeColorComponent::gradualChangeColor()
{
	if (!GetOwner()) return ;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	ActorBody* body = m_owner->getBody();
	if (body == NULL)
		return;

	if (body->getSkinID() != 34) //变色龙皮肤
		return;

	unsigned int bodyColor = body->getBodyColor();
	if (m_DestSkinColor == 0 || bodyColor == m_DestSkinColor)
		return;

	m_ColorIncrements += 0.05f / 1;

	Rainbow::ColorQuad destColor = Rainbow::ColorQuad(m_DestSkinColor);
	Rainbow::ColorQuad srcColor = Rainbow::ColorQuad(m_CurSkinColor);

	float t = m_ColorIncrements / 1;
	if (t > 1)
		t = 1.0f;

	Rainbow::ColorQuad lerpColor = Lerp(srcColor, destColor, t);
	setColor(lerpColor.c);
}

void ChangeColorComponent::setColor(unsigned int color)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	ActorBody* body = m_owner->getBody();
	if (body)
	{
		body->setBodyColor(color, false);
		if (m_owner->hasUIControl() && g_pPlayerCtrl->m_CameraModel)
			g_pPlayerCtrl->m_CameraModel->applyBodyColor(color);
	}
}

void ChangeColorComponent::setDestSkinColor(unsigned int color)
{
	m_DestSkinColor = color;
}
void ChangeColorComponent::setCurSkinColor(unsigned int color)
{
	m_CurSkinColor = color;
}
void ChangeColorComponent::setColorIncrements(float increments)
{
	m_ColorIncrements = increments;
}