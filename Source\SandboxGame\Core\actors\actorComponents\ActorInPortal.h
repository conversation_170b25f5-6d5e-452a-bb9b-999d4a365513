#ifndef __ACTOR_IN_PORTAL_H__
#define __ACTOR_IN_PORTAL_H__

//#include "OgreWCoord.h"
#include "ActorComponent_Base.h"
#include "SandboxGame.h"

class ClientActor;
class ClientPlayer;

class EXPORT_SANDBOXGAME ActorInPortal;
class ActorInPortal : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(ActorInPortal)

	void CreateEvent2();
	void DestroyEvent2();
	//tolua_begin
	ActorInPortal();
	virtual ~ActorInPortal();
	//virtual void onTick();
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

	virtual int getPortalTransferTime()const;
	virtual int getPortalCooldown()const;

	void setInPortal();
	bool isInPortal();	

	void setInPortalStatus(bool bVal){
		m_InPortal = bVal;
	}
	//tolua_end
protected:
	bool m_InPortal;
	int m_InPortalCooldown;
	int m_InPortalTime;
	MNSandbox::AutoRef<MNSandbox::Listener<int&>> m_listenerPortal1;
	//ClientActor* m_owner;
}; //tolua_exports

class PlayerInPortal : public ActorInPortal //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(PlayerInPortal)
public:
	//tolua_begin
	PlayerInPortal();
	virtual void OnTick() override;
	virtual int getPortalTransferTime()const override;
	virtual int getPortalCooldown()const override;
	//tolua_end
private:
	//ClientPlayer* m_player;
}; //tolua_exports

#endif