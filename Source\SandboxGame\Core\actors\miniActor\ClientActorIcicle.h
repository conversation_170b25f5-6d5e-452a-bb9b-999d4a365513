#ifndef __CLIENTACTOR_ICICLE_H__
#define __CLIENTACTOR_ICICLE_H__
#include <vector>
#include "ClientActorProjectile.h"
//冰凌
class ClientActorIcicle : public ClientActorProjectile { //tolua_exports
public:
	static ClientActorIcicle* shootIcicleAuto(int itemid, World* pworld, const WCoord& pos, int num);
	//tolua_begin
	ClientActorIcicle();
	virtual ~ClientActorIcicle();
	virtual void init(int itemid, ClientActor* shooter = nullptr);

	virtual void onImpactWithActor(ClientActor* actor, const std::string& partname);
	virtual void onImpactWithBlock(const WCoord* blockpos, int face);
	virtual void onCollideWithPlayer(ClientActor* actor);
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata, int version) override;
	virtual int getObjType() const override
	{
		return OBJ_TYPE_COCONUT_PRO;
	}
	//tolua_end
private:
	void	doAttack(ClientActor* actor);
	void	doImpactActor(ClientActor* actor);
	int     m_dropBlockNum;//掉落的方块数量
}; //tolua_exports

#endif

