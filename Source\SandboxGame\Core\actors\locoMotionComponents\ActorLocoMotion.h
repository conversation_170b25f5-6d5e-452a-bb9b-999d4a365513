#ifndef __ACTORLOCOMOTION_H__
#define __ACTORLOCOMOTION_H__

#include "world_types.h"
#include "Math/Vector3f.h"
//#include "Entity/OgreEntity.h"

#include "SandboxFlags.h"
#include "ActorComponent_Base.h"
#include "defdata.h"
#include "SandboxGame.h"
#include "player/AntiTypes.h"
#include "IActorLocoMotion.h"

namespace Rainbow {
	class BoxSphereBound;
}
namespace MINIW {
	class RoleController;
}

class ClientActor;
class EXPORT_SANDBOXGAME ActorLocoMotion;
class ActorLocoMotion :public ActorComponentBase, public IActorLocoMotion //tolua_exports
{ //tolua_exports
public:
	enum MovementState
	{
		Other,
		OnSlope,				//坡上
		OnSlopeFall,			//坡上掉落
		WalkUpHalfBlockHeight,	//走上半格高
		JumpOnBlockHeight,		//跳上1格高
		AdjustLandingOffset,	//落地调整偏差
		StopUphill				//阻止上坡  角色控制器在遇到大于60°的坡时，直接不给力，避免抖动
	};
	DECLARE_COMPONENTCLASS(ActorLocoMotion)

	static void CheckMotionValid(Rainbow::Vector3f& motion);

	void CreateEvent2();
	void DestroyEvent2();

	//tolua_begin
	ActorLocoMotion();
	virtual ~ActorLocoMotion();
	//H_USEMODULELONELY_NOPARAM(getLocoMotionComponent, ActorComponentBase*,NULL);
	virtual void prepareTick() override;
	/* 进入owner */
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	/* 离开owner */
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

	virtual void tick();
	virtual void update(float dtime);
	virtual void updateTick();

	virtual void gotoPosition(const WCoord &pos, float yaw, float pitch);
	virtual void initPosition(const WCoord& pos);
	void gotoPosition(const WCoord &pos)
	{
		gotoPosition(pos, m_RotateYaw, m_RotationPitch);
	}

	virtual void getCollideBox(CollideAABB &box);
	virtual void getCollideBox(Rainbow::AABB& box);
	virtual void getCollideBoxMatchPhysBox(CollideAABB& box);

	virtual void getBoundingBoxLocal(Rainbow::BoxSphereBound& bound);

	virtual void getHitCollideBox(CollideAABB &box);
	virtual void getCollideBoxs(std::vector<CollideAABB>& boxs);

	std::vector<TypeCollideAABB>& getOrInitBaseCollide();
	virtual void getMultiTypeCollidBoxs(std::vector<TypeCollideAABB>& boxs);

	virtual void combatAttackCollideBox(CollideAABB& box) override;
	virtual void getHitCollideBox(Rainbow::AABB& box);

	virtual void setBound(int height, int width)
	{
		m_BoundHeight = height;
		m_BoundSize = width;
	}

	virtual void setBoundCuboid(int height, int width, int depth)
	{
		m_BoundHeight = height;
		m_BoundSize = width;
		m_BoundDepth = depth;
	}

	void setAttackBound(int height, int width, int thickness)
	{
		m_HitBoundHeight = height;
		m_HitBoundWidth = width;
		m_HitBoundThickness = thickness;
	}
	void setBaseCollideBoxs(const std::vector<TypeCollideAABB>& boxs, const std::vector<TypeCollideAABB>& sneakboxs);

	int getBoundMinY()//miny of bound box
	{
		return m_Position.y - m_yOffset;
	}

	void setMoveDir(const Rainbow::Vector3f &dir);
	void setRotateRaw(float r);

	virtual void setRotatePitch(float p);
	virtual WCoord& getPosition()
	{
		return m_Position;
	}

	virtual void setPosition(int x, int y, int z);
	virtual void setPosition(const WCoord pos);
	virtual void setMotion(const Rainbow::Vector3f &motion)
	{
		m_Motion = motion;
	}

	virtual void setJumping(bool b) override;

	virtual bool getJumping() override
	{
		return m_isJumping;
	}
	void setAIJumping(bool b)
	{
		m_bIsAIJumping = b;
	}
	bool m_bIsAIJumping;
	bool m_isJumping;
	int m_JumpingTicks;

	WCoord getHomePosition()
	{
		return m_HomePosition;
	}

	void setHomePosition(WCoord pos)
	{
		m_HomePosition = pos;
	}

	void onEnterWorld(World *pworld);
	void onDie();
	void updatePosition(const WCoord &pos); //from network

	float getBrightness();

	void hitBack(const WCoord &from);
	virtual void addMotion(float dx, float dy, float dz);
	virtual Rainbow::Vector3f getLookDir();
	void setLookDir(float pitch, float yaw);



	bool findRandTargetBlockTowards(WCoord &pos, int range, int yrange, const WCoord &vec);
	bool findRandTargetBlock(float& x, float& y, float& z, int range, int yrange, const WCoord* pvec, int nTryTimes = 10);
	bool findRandTargetBlock(WCoord& pos, int range, int yrange, const WCoord* pvec, int nTryTimes = 10);

	virtual bool handleWaterMovement();
	virtual bool handleLavaMovement();
	//bool handleHoneyMovement();
	//bool handlePoisonMovement(); //是否踩在毒液方块上
	void handleBlockMovement();
	//bool handleDriftsandMovement();
	//bool handleVenomMovement();
	//virtual bool isOnThicket();
	bool handleDriftsandMovement();
	bool handleVenomMovement();
	virtual bool isOnLadder();
	virtual bool isOnAirWall();
	bool isInLiquid();

	virtual Rainbow::WorldPos getFramePosition() override
	{
		return m_TickPosition.getPos(m_Position);
	}
	float getFrameRotateYaw()
	{
		float t = m_TickPosition.m_TickOffsetTime / GAME_TICK_TIME;
		return Rainbow::Lerp(m_PrevRotateYaw, m_RotateYaw, t);
	}

	bool pushOutOfBlocks(const WCoord &pos);
	bool isInsideOpaqueBlock();
	bool isInsideOpaqueBlock(const WCoord &pos);
	bool getOneNoOpaqueBlock(WCoord* pos);
	bool isInsideNoOxygenBlock();
	bool isBlockInsideOxygen(int blockid, WCoord blockpos);
	bool isInSpaceAirBlock();

	virtual bool canWalkOnLiquid(bool iswater)
	{
		return false;
	}
	virtual void moveFlying(float strafing, float forward, float speed);
	virtual void doPickThrough(ClientActor *excludesactor = nullptr);
	virtual void getRotation(Rainbow::Quaternionf &quat);
	virtual void setRotation(Rainbow::Quaternionf quat) {}
	virtual bool canRotateXZ() { return false; }
	virtual bool needFullRotation(); //需要完整得quaternion来代表旋转
	virtual void addRealMove(const CollideAABB &box, WCoord &realmov);
	virtual int doMoveStep(const Rainbow::Vector3f &motion);//bit0: stop xz,   bit1: stop y
	bool isOffsetPositionInLiquid(float x, float y, float z);
	virtual void doBlockCollision();
	void doNearBlockCollision();
	bool onTheBlock(int blockID);
	bool findBottomBlock(int blockID);
	virtual bool findBottomBlock(int blockID, WCoord& blockpos, WORLD_ID &vehicleID);
	bool checkDistByGround(int dist);	// 20210926：检测离地面距离够不够  codeby： keguanqiang
	// 树叶能踩上去的问题
	bool findBottomLeafBlock();
	bool onThePhysicsActor();

	WCoord getIntegerMotion(const Rainbow::Vector3f &motion);
	virtual bool getOnGround()
	{
		return m_OnGround;
	}
	virtual void setOnGround(bool on_ground)
	{
		m_OnGround = on_ground;
	}

	void getMotion(float& x, float& y, float& z)
	{
		x = m_Motion.x;
		y = m_Motion.y;
		z = m_Motion.z;
	}
	const Rainbow::Vector3f& getMotion()
	{
		return m_Motion;
	}
	void getFramePos(int& x, int& y, int& z)
	{
		x = m_TickPosition.getPos(m_Position).x;
		y = m_TickPosition.getPos(m_Position).y;
		z = m_TickPosition.getPos(m_Position).z;
	}
	bool onGround(){return m_OnGround;}

	bool getWorldDirectionByDir(float& x, float& y, float& z);
	bool getWorldDirectionByAngle(float localyaw, float localpitch, float& x, float& y, float& z);
	bool getWorldDirectionByYawDir(float& x, float& y, float& z);
	bool getWorldDirectionByYawAngle(float localyaw, float localpitch, float& x, float& y, float& z);
	//tolua_end

	virtual void getCheatCollideBox(CollideAABB &box, const WCoord &pos);
	virtual void fallMotion(float realMovY, float movY, bool onRebound);
	void leaveWorld();
	virtual void playWalkOnLiquidEffect(bool iswater);
	bool noClip();
	void setNoClip(bool clip);
	bool UseNoClipMove() { return m_UseNoClipMove; }
	virtual void preMove(){}

	bool isOffsetBoundsAllInLiquid(float x, float y, float z);

	// 相对方向转换为世界方向
	Rainbow::Vector3f getWorldDirection(float localyaw, float localpitch, const Rainbow::Vector3f& localdir);
	// 相对角度转世界角度
	float localYawToGlobalYaw(float localyaw);
	float localPitchToGlobalPitch(float localpitch);
	void getCircularActors(std::vector<ClientActor*>& actors, int radius);
	void getFacedActors(std::vector<ClientActor*>& actors, const Rainbow::Vector3f& dir, int range, int width);
	void getFanShapedAreaFacedActors(std::vector<ClientActor*>& actors, const Rainbow::Vector3f& dir, int range, int height, int angle, const WCoord* pOrigin = NULL);
	/// <summary>
	/// 漩涡影响生物的motin
	/// </summary>
	/// <returns></returns>
	Rainbow::Vector3f getVortexMotion() { return m_vortexMotion; }
	void resetVortexMotion() { m_vortexMotion.SetZero(); }
	void addVortexMotion(const Rainbow::Vector3f& m) { m_vortexMotion += m; }

	bool isSlopeFalling() { return  m_CurMovementState == MovementState::OnSlopeFall; }

#pragma region IActorLocoMotion
	virtual MNSandbox::EventObjectManager* GetEvent2() override;
	virtual int GetBoundSize() override
	{
		return m_BoundSize;
	}
	virtual int GetBoundHeight() override
	{
		return m_BoundHeight;
	}
	virtual void SetRotateYaw(float yaw) override
	{
		m_RotateYaw = yaw;
	}
	virtual float GetRotateYaw() override
	{
		return m_RotateYaw;
	}
	virtual float GetRotationPitch() override
	{
		return m_RotationPitch;
	}
	virtual void SetMotion(const Rainbow::Vector3f& motion) override
	{
		m_Motion = motion;
	}
	virtual Rainbow::Vector3f GetMotion() override
	{
		return m_Motion;
	}

	virtual bool IsPhysActor() override;

	virtual float GetPrevRotateYaw() override
	{
		return m_PrevRotateYaw;
	}
	virtual TickPosition GetTickPosition() override
	{
		return m_TickPosition;
	}
	virtual int GetYOffset() override
	{
		return m_yOffset;
	}
	virtual bool GetInLava() override
	{
		return m_InLava;
	}

	virtual bool GetInHurt() override
	{
		return m_InHurt;
	}
	virtual bool GetIsBlocked() override
	{
		return m_bIsBlocked;
	}

	virtual void SetPosition(const WCoord& positon) override
	{
		m_Position = positon;
	}
	virtual void BroadVehicleAssembleLocoMotionData(game::hc::PB_VehicleMoveHC* vehicleMoveHC, IClientActor* actor) override {}
	virtual void SetVehicleAssembleLocoMotionData(std::vector<WCoord>& m_vLastPosition, bool& syncpos, std::vector<Rainbow::Quaternionf>& m_vLastRot, bool& rot_enough, float QUAT_SQ_VEHICLE, int DPSQ) override {}
	virtual void setTarget(const WCoord& target, float speed) {};
#pragma endregion

protected:
	// 	void updateGravity();
	bool isBoundsAllInLiquid();

	//检查生物是否在加热区域
	virtual int checkActorInHotZone();
	void setSlamByThermalSpring(bool b) { m_IsSlamByThermalSpring = b; }
	bool getIsSlamByThermalSpring() { return m_IsSlamByThermalSpring; }
	/// <summary>
	/// 检查周围是否有漩涡, 改变移动方向
	/// </summary>
	virtual void checkVortex();
protected:
	virtual bool isMovementBlocked();
	void updateRidden();
	virtual void updateBindActor();
	virtual void setInWater(bool inwater);


	virtual bool hasCollisionBetweenPlayers();

	void syncPhysActorPosByLocPos();
public:
	ClientActor * getOwnerActor();
	// 是否使用过
	AntiSetting::ProctectVal m_UseOnGround;

	bool m_OnGround;//站在地上, 不受重力影响

	bool m_InWater;  // 玩家角色请使用setInWater来设置此变量
	bool m_InLava;
	bool m_InHoney;

	bool m_OnBlockFrontEdge;//是否在方块边缘靠前的位置
	bool m_OnBlockLeftEdge;//是否在方块边缘靠左的位置
	bool m_FrontBlockLeftIsEmpty;// 前一个方块的左边没有方块
	bool m_FrontBlockRightIsEmpty;
	bool m_CanDriftWithSkateboard; //可以使用滑板漂移动作
	bool m_OnBlockRightEdge;//是否在方块边缘靠右的位置

	//tolua_begin
	float m_RotateYaw;
	float m_RotationPitch;
	float m_RotateRoll;
	float m_PrevRotateYaw;
	float m_PrevRotatePitch;

	float m_NavigationRotateYaw;
	float m_NavigationRotationPitch;

	int m_BoundSize;
	int m_BoundHeight;
	int m_BoundDepth;
	int m_yOffset;

	int m_HitBoundWidth;
	int m_HitBoundHeight;
	int m_HitBoundThickness;

	WCoord m_Position;
	WCoord m_OldPosition;

	WCoord m_HomePosition;

	TickPosition m_TickPosition;

	Rainbow::Vector3f m_Motion; //当前移动速度, 用于各种运动的位置移动累加
    Rainbow::Vector3f m_AccumLeftMotion; //记录位移计算中小数部分的偏差
    Rainbow::Vector3f m_MotionStatus; //仅仅用于统计移动速度
	Rainbow::Vector3f m_ExtraMotion;// 处理特殊移动需求

	World *m_pWorld;
	
	bool m_InWaterReal;
	bool m_InPoison; //是否在毒液上
	bool m_InHurt;//站在有伤害的方块上
	bool m_InDriftsand;
	bool m_InRun;	//奔跑
	bool m_OnLadder;    // 梯子
	bool m_OnVehicle;   // 载具
	bool m_bMoving;
	//tolua_end

	float m_JumpMovementFactor;
	bool m_CollidedHorizontally;
	bool m_CollidedVertically;

	bool m_IsEnchClimb; //附魔爬墙中
	bool m_IsEnchFall; //附魔速降中
	bool m_bIsBlocked; //被阻挡着不能移动

	bool m_IsInBoatWaterMask;// 是否在船仓去水渲染里

	enum MovementAbility
	{
		Movement_Water = 0,
		Movement_Ability_Max
	};
	MNSandbox::FlagsInt m_MovementAbility;

	float m_DistanceWalked;
	float m_NextStepDistance;

	Rainbow::Quaternionf m_PrevRotateQuat;
	Rainbow::Quaternionf m_RotateQuat;
	std::vector<WCoord> m_preCheckPhy;

	bool m_IsCollideBackward{ false }; //碰撞是否触发回退
	int  m_CollideMoveTicks = 0; //碰撞多少个tick可移动 1049 buff

	
	// 客机实体位置和旋转平滑处理
	Rainbow::WorldPos m_UpdatePos;
	Rainbow::Quaternionf m_UpdateRot;
	int m_PosRotationIncrements = 0;
	WCoord m_ServerPos;
	Rainbow::Quaternionf m_ServerRot;

protected:
	MovementState m_CurMovementState;
	MINIW::RoleController* m_ActorRoleController;
private:
	bool m_noClip;   // 统一外部修改，不再开放，便于作弊检测
	bool m_UseNoClipMove;
	bool m_IsSlamByThermalSpring;
	unsigned m_clipProtected;
	Rainbow::Vector3f m_vortexMotion; //各种漩涡累加起来
	ClientActor* m_OwnerActor;

	float m_fSlopeFallSpeed;

	// 基础碰撞盒
	std::vector<TypeCollideAABB> m_BaseCollideBoxs;
	std::vector<TypeCollideAABB> m_SneakCollideBoxs;

	// 通知监听
	MNSandbox::AutoRef<MNSandbox::Listener<int, float>> m_listenerBlockCatapulte;
}; //tolua_exports

extern const float MOTION2VELOCITY;
#endif
