#include "BallLocomotion.h"
#include "ClientPlayer.h"
#include "BlockMaterialMgr.h"
#include "PlayerLocoMotion.h"
#include "special_blockid.h"


#include "BallLocomotion.h"
#include "BindActorComponent.h"
#include "BlockScene.h"
#include "ActorBall.h"

using namespace MINIW;
using namespace Rainbow;
IMPLEMENT_COMPONENTCLASS(BallLocoMotion)
BallLocoMotion::BallLocoMotion() : m_PhysActor(NULL), m_PhysJoint(NULL), m_hasPhysActor(false)
{
	m_PosRotationIncrements = 0;
}

void BallLocoMotion::attachPhysActor()
{
	if (m_pWorld->isRemoteMode()) return;

	if(m_PhysActor == NULL)
	{
		Rainbow::Vector3f pos = m_Position.toVector3() + Rainbow::Vector3f(0.0f, (float)m_yOffset, 0.0f);
		float mass = g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_mass*g_WorldMgr->m_SurviveGameConfig->physxconfig.mass_scale;
		m_PhysActor = m_pWorld->m_PhysScene->AddRigidDynamicActor(pos, Rainbow::Quaternionf::identity, m_BoundHeight/2.0f, g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_static_friciton, g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_dynamic_friciton, /*g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_restitution*/0.1f, mass, false, getOwnerActor());
		if (m_pWorld->getScene())
			m_pWorld->getScene()->AddGameObject(m_PhysActor->GetGameObject());

		m_PhysActor->SetLinearDamping(g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_linear_damping);
		if(g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_Angular_damping >= 0)
			m_PhysActor->SetAngularDamping(g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_Angular_damping);

		m_PhysActor->SetLinearVelocity(m_Motion * MOTION2VELOCITY);
		m_hasPhysActor = true;
		m_PhysActor->GetGameObject()->AddEvent(Rainbow::Evt_CollisionEnter, &BallLocoMotion::OnCollisionEnter, this);
	}
}


void BallLocoMotion::OnCollisionEnter(const Rainbow::EventContent* collision)
{
	ClientActor* ownerActor = getOwnerActor();
	if (ownerActor)
	{
		ActorBall* ball = static_cast<ActorBall*>(ownerActor);
		ball->playSoundByPhysCollision();
	}
}

void BallLocoMotion::detachPhysActor()
{
	if (m_pWorld->isRemoteMode()) return;

	if(m_PhysActor)
	{
		m_pWorld->m_PhysScene->DeleteRigidActor(m_PhysActor);
		m_PhysActor = NULL;

		m_hasPhysActor = false;
	}
}

void BallLocoMotion::attachPhysJoint(ClientPlayer *player)
{
	if (m_pWorld->isRemoteMode()) return;

	PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *>(player->getLocoMotion());

	Rainbow::Quaternionf rot;
	//rot.setEulerAngle(0.0f, 0.0f, 0.0f);
	rot = AngleEulerToQuaternionf(Vector3f(0.0f, 0.0f, 0.0f));
	int offsetY = (loc->m_BoundHeight - m_BoundHeight) / 2 - 5;
	m_PhysJoint = m_pWorld->m_PhysScene->CreateFixJoint(
		loc->m_PhysActor, 
		Vector3f(0, 0, 0), 
		rot, 
		m_PhysActor, 
		Vector3f(0, (float)offsetY, 130), 
		Quaternionf::identity);
}

void BallLocoMotion::detachPhysJoint()
{
	if (m_pWorld->isRemoteMode()) return;

	if (m_PhysJoint)
	{
		m_pWorld->m_PhysScene->DeleteJoint(m_PhysJoint);
		m_PhysJoint = NULL;
	}
}

void BallLocoMotion::prepareTick()
{
	ActorLocoMotion::prepareTick();

	m_PrevRotateQuat = m_RotateQuat;
}

void BallLocoMotion::tick()
{
	ActorLocoMotion::tick();

	if (m_pWorld->isRemoteMode())
	{
		if(m_PosRotationIncrements > 0)
		{
			m_Position = m_Position + (m_ServerPos - m_Position)/m_PosRotationIncrements;
			//m_RotateQuat.slerp(m_RotateQuat, m_ServerRot, 1.0f/m_PosRotationIncrements);
			m_RotateQuat = Slerp(m_RotateQuat, m_ServerRot, 1.0f / m_PosRotationIncrements);

			Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, -m_RotateQuat.GetAxisZ());

			m_PosRotationIncrements--;
		}
		return;
	}

	if(m_PhysActor)
	{
		Rainbow::Vector3f pos;
		Rainbow::Quaternionf quat;
		m_PhysActor->GetPos(pos, quat);

		m_Motion = pos - m_Position.toVector3();
		m_Position = pos;
		m_RotateQuat = quat;
		Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, -m_RotateQuat.GetAxisZ());

		checkPhysWorld();
	}
	else
	{
		m_Motion *= 0.98f;
		if(Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
		//if(Abs(m_Motion.y) < 0.5f) m_Motion.y = 0;
		if(Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;

		doMoveStep(m_Motion);
		auto bindAComponent = getOwnerActor()->getBindActorCom();

		if(bindAComponent && !bindAComponent->getTarget())
			m_Motion.y -= m_pWorld->getGravity(GRAVITY_ITEM);
	}
}

void BallLocoMotion::update(float dtime)
{
	ActorLocoMotion::update(dtime);

	m_UpdateRot = Slerp(m_PrevRotateQuat, m_RotateQuat, m_TickPosition.m_TickOffsetTime/GAME_TICK_TIME);
	m_UpdatePos = getFramePosition();
}

void BallLocoMotion::getRotation(Rainbow::Quaternionf &quat)
{
	quat = m_RotateQuat;
}

void BallLocoMotion::doPickThrough(ClientActor *excludesactor/* =nullptr */)
{
	if (m_pWorld->isRemoteMode()) return;

	WCoord mvec = getIntegerMotion(m_Motion);
	if (m_OnGround && mvec.y < 0)
		mvec.y = 0;
	if (mvec.length() < 100)
	{
		return;
	}

	MINIW::WorldRay ray;
	ray.m_Origin = m_Position.toWorldPos();
	ray.m_Dir = mvec.toVector3();
	ray.m_Range = ray.m_Dir.Length();
	ray.m_Dir /= ray.m_Range;

	ActorExcludes excludes;
	excludes.addActor(getOwnerActor());
	if (excludesactor)
		excludes.addActor(excludesactor);

	IntersectResult presult;
	WorldPickResult intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
	if (intertype == WorldPickResult::BLOCK) //block
	{
		int blockID = m_pWorld->getBlockID(presult.block);

		WCoord pos = CoordDivBlock(m_Position + mvec);

		if (presult.block != pos)
		{
			if (blockID == BLOCK_COLLIDER || blockID == BLOCK_MOBCOLLIDER || blockID == BLOCK_BALLCOLLIDER)
			{
				g_BlockMtlMgr.getMaterial(blockID)->DoOnActorCollidedWithBlock( getOwnerActor()->getWorld(), presult.block, getOwnerActor());
				// 观察者事件接口
				//ObserverEvent_Block obevent(presult.block.x, presult.block.y, presult.block.z, blockID, (long long)getOwnerActor()->getObjId());
				//ObserverEventManager::getSingleton().OnTriggerEvent("Block.ActorCollide", &obevent);
			}
		}
	}
	else if (intertype == WorldPickResult::ACTOR) //actor
	{
		WCoord pos = (m_Position + mvec) / BLOCK_SIZE;
		if (presult.actor->getPosition() != pos)
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(presult.actor);
			if (player)
			{
				getOwnerActor()->onCollideWithPlayer(player);
			}
			else
			{
				getOwnerActor()->collideWithActor(presult.actor->GetActor());
			}
		}
	}
}

void BallLocoMotion::checkPhysWorld()
{
	WCoord curpos = m_Position;
	WCoord range(SECTION_SIZE, SECTION_SIZE, SECTION_SIZE);
	WCoord minpos = CoordDivSection(curpos-range);
	WCoord maxpos = CoordDivSection(curpos+range);

	std::vector<WCoord> checkphy;
	for(int y=minpos.y; y<=maxpos.y; y++)
	{
		for(int z=minpos.z; z<=maxpos.z; z++)
		{
			for(int x=minpos.x; x<=maxpos.x; x++)
			{
				//m_pWorld->updateSectionPhysics(x, y, z);
				checkphy.push_back(WCoord(x, y, z));
			}
		}
	}

	for (int i = 0; i<(int)m_preCheckPhy.size(); i++)
	{
		 int j = 0;
		 for (; j<(int)checkphy.size(); j++)
		 {
			if (m_preCheckPhy[i] == checkphy[j])
			{
				break;
			}
		 }
		 if (j == checkphy.size())
		 {
			m_pWorld->updateLeaveSectionPhysics(m_preCheckPhy[i].x, m_preCheckPhy[i].y, m_preCheckPhy[i].z);
		 }
	}

	for (int i = 0; i<(int)checkphy.size(); i++)
	{
		 int j = 0;
		 for (; j<(int)m_preCheckPhy.size(); j++)
		 {
			if (checkphy[i] == m_preCheckPhy[j])
			{
				break;
			}
		 }
		 if (j == m_preCheckPhy.size())
		 {
			m_pWorld->updateEnterSectionPhysics(checkphy[i].x, checkphy[i].y, checkphy[i].z);
		 }
		 m_pWorld->updateSectionPhysics(checkphy[i].x, checkphy[i].y, checkphy[i].z);
	}
	m_preCheckPhy = checkphy;
}

void BallLocoMotion::updateBindActor()
{
	auto bindAComponent = getOwnerActor()->m_pBindActorComponent;
	if (bindAComponent)
	{
		ClientActor *binding = bindAComponent->getTarget();
		if (binding)
		{
			//if (m_pWorld->isRemoteMode()) return;
			Rainbow::Vector3f dir = Yaw2FowardDir(binding->getLocoMotion()->m_RotateYaw);

			/*Vector3f bindingPos = binding->getPosition().toVector3();
			Vector3f ballPos = getOwnerActor()->getPosition().toVector3() + getOwnerActor()->m_OffsetPos;
			Vector3f tmp = ballPos - bindingPos;
			float distance = tmp.length();

			if (distance > 200.0f)
			{
				distance = 200;
				getOwnerActor()->setBindInfo(-binding->getObjId(), WCoord(0, 0, 0));
			}
			else
			{
				Vector3f curPos = bindingPos + dir*distance;
				WCoord int_pos = WCoord(curPos);
				getOwnerActor()->m_OffsetPos = curPos - int_pos.toVector3();
				int_pos.y += (m_BoundHeight / 2);
				setPosition(int_pos.x, int_pos.y, int_pos.z);
			}*/
				
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(binding);
			if (player)
			{
				WCoord pos1 = player->getPosition();
				float distance = player->getLocoMotion()->m_BoundSize + BLOCK_SIZE*0.5f;
				WCoord pos = pos1 + WCoord(dir*(distance));
				pos.y += (m_BoundHeight / 2);
				//setPosition(pos.x, pos.y, pos.z);
				int blockid = m_pWorld->getBlockID(CoordDivBlock(pos));
				if (blockid > 0)
				{
					BlockMaterial* blockMat = m_pWorld->getBlockMaterial(CoordDivBlock(pos));
					if (blockMat && blockMat->canBlocksMovement(m_pWorld, CoordDivBlock(pos)))
					{
						pos = pos1;// +WCoord(dir*(BLOCK_FSIZE*1.0f));
						pos.y += (m_BoundHeight / 2);
					}
				}
				setPosition(pos.x, pos.y, pos.z);
			}
		}
	}
}

void BallLocoMotion::setPosition(int x, int y, int z)
{
	m_OldPosition = m_Position;
	m_Position.x = x;
	m_Position.y = y;
	m_Position.z = z;
	if (m_PhysActor)
		m_PhysActor->SetPos(Rainbow::Vector3f((float)x, (float)y, (float)z), m_RotateQuat);
}
 