﻿#include "container_socdoor.h"

#include "ClientItem.h"
#include "world.h"
#include "BaseItemMesh.h"
#include "BlockScene.h"
#include "special_blockid.h"
#include "DefManagerProxy.h"
#include "block_tickmgr.h"
#include "BlockMaterialMgr.h"
#include "EffectManager.h"
#include "VehicleWorld.h"
#include "WorldManager.h"
#include "ModelItemMesh.h"
#include "OgreModel.h"
#include "ModelRenderer.h"
#include "Components/Renderer.h"

using namespace MINIW;
using namespace Rainbow;

SocDoorContainer::SocDoorContainer():m_ItemMesh(nullptr)
{
	_lock.Reset();
}

SocDoorContainer::SocDoorContainer(const WCoord& blockpos, const int blockId) :ErosionContainer(blockpos, blockId) , m_ItemMesh(nullptr)
{
	_lock.Reset();
}

SocDoorContainer::~SocDoorContainer()
{
	//DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh);
	DeleteLock();
}

void SocDoorContainer::enterWorld(World* pworld)
{
	ErosionContainer::enterWorld(pworld);

#ifndef IWORLD_SERVER_BUILD
	if (!m_World) return;
	//没有锁尝试创建
	if (!m_ItemMesh) CreateLock();
	//没有创建成功
	if (!m_ItemMesh) return;
	//钥匙锁只要一个模型显示就行
	if (_lock.type != 2) return;
	int blockdata = m_World->getBlockData(m_BlockPos);
	int placedir = blockdata & 3;
	bool isopen = (blockdata & 8) != 0;
	//上锁状态显示红色
	if (_lock.status == 1)
	{
		setLightStatus(0, true);
		setLightStatus(1, false);
	}
	//未上锁状态显示红色
	if (_lock.status == 0)
	{
		setLightStatus(0, false);
		setLightStatus(1, true);
	}

	//开门后是红色
	if (isopen)
	{
		setLightStatus(0, true);
		setLightStatus(1, false);
	}

#endif
}

void SocDoorContainer::leaveWorld()
{
	ErosionContainer::leaveWorld();
}

flatbuffers::Offset<FBSave::ChunkContainer> SocDoorContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	//auto basedata = saveContainerCommon(builder);
	auto basedata = ErosionContainer::saveContainerErosion(builder);

	auto lockdata = FBSave::CreateSocLock(builder,
		_lock.type,
		_lock.status,
		_lock.mainid,
		_lock.lockid,
		_lock.lockpassword,
		_lock.main_lockpassword,
		builder.CreateVector(_lock.mainids.data(), _lock.mainids.size())
		);
	auto actor = FBSave::CreateContainerSocDoor(builder, basedata, lockdata);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerSocDoor, actor.Union());
}

bool SocDoorContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerSocDoor*>(srcdata);
	if (!src)
		return false;
	//loadContainerCommon(src->basedata());
	ErosionContainer::load(src->basedata());

	auto lockdata = src->lock();
	_lock.type = lockdata->type();
	_lock.status = lockdata->status();
	_lock.mainid = lockdata->mainid();
	_lock.lockid = lockdata->lockid();
	_lock.lockpassword = lockdata->lockpassword();
	_lock.main_lockpassword = lockdata->main_lockpassword();

	for (size_t i = 0; i < lockdata->mainids()->size(); i++)
	{
		int uin = lockdata->mainids()->Get(i);
		_lock.mainids.push_back(uin);
	}

	return true;
}

void SocDoorContainer::dropItems()
{
	BlockMaterial* pmtl = getBlockMtl();
	if (!pmtl)
	{
		return;
	}
	//todo 看看要不要掉密码锁
	//if (m_hasKey)
	//{
	//	pmtl->doDropItem(m_World, m_BlockPos, ITEM_KEY, 1);
	//}
}

void SocDoorContainer::CreateLock()
{
#ifndef IWORLD_SERVER_BUILD
	if (_lock.type == 0) return;

	DeleteLock();

	if (_lock.type == 1)
		m_ItemMesh = createItemMesh(6001);

	if (_lock.type == 2)
		m_ItemMesh = createItemMesh(6002);

	RePosLock();
#endif
}

void SocDoorContainer::DeleteLock()
{
#ifndef IWORLD_SERVER_BUILD
	if (m_ItemMesh)
	{
		GameObject::Destroy(m_ItemMesh);
		m_render = nullptr;
		m_ItemMesh = nullptr;
	}
#endif
}

void SocDoorContainer::RePosLock()
{
	if (!m_ItemMesh) return;

	int blockdata = m_World->getBlockData(m_BlockPos);
	int placedir = blockdata & 3;
	bool isopen = (blockdata & 8) != 0;
	LOG_WARNING("SocDoorContainer::CreateLock placedir = %d isopen = %d", placedir, (int)isopen);
	int offsety = 80;
	int close_offsetx = 80;
	int close_offset_mirrorx = BLOCK_SIZE - close_offsetx;
	int open_offsetx = 12; //12.5
	int open_offset_mirrorx = BLOCK_SIZE - open_offsetx;
	//close的 x y z rot open的 x y z rot
	std::unordered_map<int, std::vector<int>> configpos{
		{ DIR_NEG_X, {
			0, offsety, close_offset_mirrorx, 90,
			close_offsetx, offsety, open_offset_mirrorx, 0
		} },
		{ DIR_POS_X, {
			BLOCK_SIZE, offsety, close_offsetx, 270,
			close_offset_mirrorx, offsety, open_offsetx, 180
		} },
		{ DIR_NEG_Z, {
			close_offsetx, offsety, 0, 0,
			open_offsetx, offsety, close_offsetx, 270
		} },
		{ DIR_POS_Z, {
			close_offset_mirrorx, offsety, BLOCK_SIZE, 180,
			open_offset_mirrorx, offsety, close_offset_mirrorx, 90
		} },
	};

	const std::vector<int>& current_pos = configpos[placedir];
	WCoord wpos = m_BlockPos * BLOCK_SIZE;
	int start_index = isopen ? 4 : 0;
	wpos.x += current_pos[start_index + 0];
	wpos.y += current_pos[start_index + 1];
	wpos.z += current_pos[start_index + 2];

	WorldPos pos = wpos.toWorldPos();
	if (m_ItemMesh && m_World)
	{
		auto trans = m_ItemMesh->GetComponent<Transform>();
		trans->SetLocalPosition(pos.toVector3());
		trans->SetLocalRotation(AngleEulerToQuaternionf(Vector3f(0, current_pos[start_index + 3], 0)));

		m_ItemMesh->SetActive(false);
		m_World->getScene()->AddGameObject(m_ItemMesh);
		m_ItemMesh->SetActive(true);
	}
}

void SocDoorContainer::setLightStatus(int light, bool status)
{
	if (_lock.type != 2) return;
	int mat_index = light + 1;
	if (mat_index > 2) return;
	auto mat = m_render->GetMaterial(mat_index);
	if (!mat) return;
	std::string texpath;
	//红灯
	if (mat_index == 1)
	{
		if (status)
		{
			texpath = "itemmods/6002/Material/tex_codedlock_redlight_on.png";
		}
		else
		{
			texpath = "itemmods/6002/Material/tex_codedlock_redlight_off.png";
		}
	}
	else
	{
		if (status)
		{
			texpath = "itemmods/6002/Material/tex_codedlock_greenlight_on.png";
		}
		else
		{
			texpath = "itemmods/6002/Material/tex_codedlock_greenlight_off.png";
		}
	}

	auto tex = GetAssetManager().LoadAsset<Texture2D>(texpath.c_str());
	mat->SetTexture("g_DiffuseTex", tex.Get());
}

Rainbow::GameObject* SocDoorContainer::createItemMesh(int itemid)
{
	Rainbow::GameObject* go = nullptr;
	std::string filepath = "itemmods/" + std::to_string(itemid) + "/body.prefab";
	SharePtr<Rainbow::Prefab> prefabfile = GetAssetManager().LoadAsset<Rainbow::Prefab>(filepath.c_str());
	if (prefabfile.IsValid() && prefabfile->IsLoaded())
	{
		Rainbow::Object* cloneObject = prefabfile->CreatObjectInstance();
		Rainbow::GameObject* prefabgo = static_cast<Rainbow::GameObject*>(cloneObject);

		m_render = prefabgo->GetComponent<Renderer>();

		//在套一层go,保证编辑器的Transform
		go = Rainbow::GameObject::Create();
		prefabgo->GetTransform()->SetParent(go->GetTransform());
	}

	return go;
}
