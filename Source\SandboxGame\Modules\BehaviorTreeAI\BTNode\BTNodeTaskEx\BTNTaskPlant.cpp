﻿/**********************************************
*	FUNC:	行为树任务拓展节点(样本)
*	FILE:	BTNTaskDigBlock
*	BY:		chenzh
*	TIME:	2020-6-16
*/
#include "BTNodeDef.h"
#include "BTNTaskPlant.h"
#include "BTNodeRunData.h"
#include "BTBlackboard.h"
#include "ClientMob.h"
#include "DefManagerProxy.h"
#include "world.h"
#include "EffectManager.h"
#include "container_backpack.h"
//
#include "special_blockid.h"
#include "Text3D/Text3D.h"
#include "AIFunctionMgr.h"
#include "json/jsonxx.h"
#include <assert.h>
#include "Common/OgreShared.h"
#include "ActorVillager.h"
#include "PathEntity.h"
#include "Plugin.h"
#include "SandboxCoreDriver.h"
#include "ClientActorFuncWrapper.h"
#include "ActorBodySequence.h"
#include "navigationpath.h"

#include "OgreScriptLuaVM.h"
#include "BehaviorTreeInstance.h"

using namespace MNSandbox;
const std::string BTNTaskPlant::ms_paramkey_block_pos_key = "block_pos_key";
const std::string BTNTaskPlant::ms_paramkey_speed = "speed";
const std::string BTNTaskPlant::ms_paramkey_dist = "dist";
const std::string BTNTaskPlant::ms_paramkey_plant_type = "plant_type";
const std::string BTNTaskPlant::ms_paramkey_isauto_change_land = "isauto_change_land";

 const int BTNTaskPlant::mi_paramkey_block_pos_key = 1;
 const int BTNTaskPlant::mi_paramkey_speed = 2;
 const int BTNTaskPlant::mi_paramkey_dist = 3;
 const int BTNTaskPlant::mi_paramkey_plant_type = 4;
 const int BTNTaskPlant::mi_paramkey_isauto_change_land = 5;

//--------------------------------------------------------
BTNTaskPlant::BTNTaskPlant()
	: m_self(NULL)
	, m_targetpos_key("")
	, m_fSpeed(0)
	,m_iDist(0)
	,m_iItemID(0)
	, m_iToolID(0)
	,m_TargetPos(WCoord(0, -1, 0))
	,m_nType(1)
	, m_bIsMove(false)
	, m_nWorkRange(0)
	, m_nAutoChangeLand(0)
{

}

BTNTaskPlant::~BTNTaskPlant()
{
}
int  BTNTaskPlant::InitValue(BTNodeBase* node)
{
	BTNTaskPlant* tnode = static_cast<BTNTaskPlant*>(node);
	m_targetpos_key = tnode->m_targetpos_key;
	m_fSpeed = tnode->m_fSpeed;
	m_iDist = tnode->m_iDist;
	m_nType = tnode->m_nType;
	m_nAutoChangeLand = tnode->m_nAutoChangeLand;
	return BTNodeTask::InitValue(tnode);
}
bool BTNTaskPlant::SetParam(const std::string& key, const BTLuaData& data)
{
	if (key.compare(ms_paramkey_block_pos_key) == 0)
	{
		if (data.GetType() == BTLuaDataType_String)
		{
			m_targetpos_key = data.GetValue_String();
			return true;
		}
	}
	else if (key.compare(ms_paramkey_speed) == 0)
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			m_fSpeed = (float)data.GetValue_Number();
			return true;
		}
	}
	else if (key.compare(ms_paramkey_dist) == 0)
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			m_iDist = (int)data.GetValue_Number();
			return true;
		}
	}
	else if (key.compare(ms_paramkey_plant_type) == 0)
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			m_nType = (int)data.GetValue_Number();
			return true;
		}
	}
	else if (key.compare(ms_paramkey_isauto_change_land) == 0)
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			m_nAutoChangeLand = (int)data.GetValue_Number();
			return true;
		}
	}
	return BTNodeTask::SetParam(key, data);
}

bool BTNTaskPlant::SetParam(const int& key, const BTLuaData& data)
{
	switch (key)
	{
	case mi_paramkey_block_pos_key:
	{
		if (data.GetType() == BTLuaDataType_String)
		{
			m_targetpos_key = data.GetValue_String();
			return true;
		}
	}
		break;
	case mi_paramkey_speed:
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			m_fSpeed = (float)data.GetValue_Number();
			return true;
		}
	}
		break;
	case mi_paramkey_dist:
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			m_iDist = (int)data.GetValue_Number();
			return true;
		}
	}
		break;
	case mi_paramkey_plant_type:
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			m_nType = (int)data.GetValue_Number();
			return true;
		}
	}
		break;
	case mi_paramkey_isauto_change_land:
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			m_nAutoChangeLand = (int)data.GetValue_Number();
			return true;
		}
	}
		break;
	default:
		break;
	}

	return BTNodeTask::SetParam(key, data);
}

bool BTNTaskPlant::ActivateCondition(const BTNodeRunData& rundata)
{
	if (!BTNodeTask::ActivateCondition(rundata))
		return false;

	m_self = dynamic_cast<ClientMob*>(m_AttachObj);

	if (!m_self)
		return false;

	Rainbow::Vector3f Pos;
	bool isSetTarge = false;
	if (rundata.GetBlackboard() && rundata.GetBlackboard()->GetData_Vector3(m_targetpos_key.c_str(), Pos))
	{
		isSetTarge = true;
		
	}
	else if (GetValPos(mi_paramkey_block_pos_key, rundata,Pos))
	{
		isSetTarge = true;
	}
	if (isSetTarge)
	{
		m_TargetPos = Pos;

		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("AIFunctionMgr_IsBindWithPoint",
			SandboxContext(nullptr)
			.SetData_Number("m_nType", m_nType)
			.SetData_Usertype("m_self", m_self)
			.SetData_Usertype("m_TargetPos", &m_TargetPos));
		if (result.IsExecSuccessed())
		{
			m_nWorkRange = (int)result.GetData_Number("m_nWorkRange");
		}
	}
	GetValNum(mi_paramkey_speed, rundata, m_fSpeed);
	GetValNum(mi_paramkey_dist, rundata, m_iDist);
	GetValNum(mi_paramkey_isauto_change_land, rundata, m_nAutoChangeLand);

	auto living = dynamic_cast<ActorLiving*>(m_self);
	auto livingAttr = living != nullptr ? living->getLivingAttrib() : nullptr;
	if (!livingAttr)
		return false;

	BackPackGrid* pweapon = livingAttr->getEquipGrid(EQUIP_WEAPON);
	if (!pweapon)
		return false;

	//临时代码
// 	if (1 == m_nType)
// 	{
// 		PackContainer* bags = m_self->getBags();
// 		if (bags)
// 		{
// 			bags->clear();
// 			if (!getItemIDInBagWithType(1))
// 			{
// 				bags->addItem(212, 1);
// 				bags->addItem(253, 1);
// 				bags->addItem(213, 1);
// 				bags->addItem(214, 1);
// 				bags->addItem(216, 1);
// 			}
// 			if (!checkBagItemWithId(2))
// 			{
// 				bags->addItem(11023, 1);
// 			}
// 		}
// 	}
// 	else if (2 == m_nType)
// 	{
// 		PackContainer* bags = m_self->getBags();
// 		bags->clear();
// 		if (bags)
// 		{
// 			if (!checkBagItemWithId(3))
// 			{
// 				bags->addItem(11033, 1);
// 			}
// 
// 			if (!checkBagItemWithId(11404))
// 			{
// 				bags->addItem(11404, 1);
// 			}
// 			if (!checkBagItemWithId(236))
// 			{
// 				bags->addItem(236, 1);
// 			}
// 			if (!checkBagItemWithId(241))
// 			{
// 				bags->addItem(241, 1);
// 			}
// 			if (!checkBagItemWithId(11400))
// 			{
// 				bags->addItem(11400, 1);
// 			}
// 		}
// 	}

	return true;
}

bool BTNTaskPlant::RunBefore(const BTNodeRunData& rundata)
{
	if (!m_self)
		return false;
	if (m_TargetPos.y < 0)
		return false;
	if (!m_self->getMobAttrib())
		return false;
	if (!m_self->getLocoMotion())
		return false;

	if (m_bIsMove)
	{
		return m_bIsMove;
	}
	AIFunctionMgr* aiModule = GET_SUB_SYSTEM(AIFunctionMgr);
	auto functionWrapper = m_self->getFuncWrapper();
	if (1 == m_nType)
	{
		WCoord plantPoses[8] =
		{
			WCoord(m_TargetPos.x + m_nWorkRange , m_TargetPos.y, m_TargetPos.z),
			WCoord(m_TargetPos.x + m_nWorkRange , m_TargetPos.y, m_TargetPos.z + m_nWorkRange) ,
			WCoord(m_TargetPos.x, m_TargetPos.y, m_TargetPos.z + m_nWorkRange) ,
			WCoord(m_TargetPos.x - m_nWorkRange , m_TargetPos.y, m_TargetPos.z + m_nWorkRange) ,
			WCoord(m_TargetPos.x - m_nWorkRange , m_TargetPos.y, m_TargetPos.z) ,
			WCoord(m_TargetPos.x - m_nWorkRange , m_TargetPos.y, m_TargetPos.z - m_nWorkRange) ,
			WCoord(m_TargetPos.x, m_TargetPos.y, m_TargetPos.z - m_nWorkRange) ,
			WCoord(m_TargetPos.x + m_nWorkRange, m_TargetPos.y, m_TargetPos.z - m_nWorkRange)
		};
		m_iItemID = getItemIDInBagWithType(1);
		m_iToolID = getItemIDInBagWithType(2);
		if (m_iItemID)
		{
			for (int i = 0; i < 8; i++)
			{
				int y = plantPoses[i].y;
				for (int j = -2; j <= 2; j++)
				{
					plantPoses[i].y = y + j;
					int blockId = 0;
					int airBlock = 0;
					if (aiModule)
					{
						blockId = aiModule->GetBlockId(plantPoses[i].x, plantPoses[i].y - 1, plantPoses[i].z);
						airBlock = aiModule->GetBlockId(plantPoses[i].x, plantPoses[i].y, plantPoses[i].z);
					}
					if (!airBlock)
					{
						if (103 == blockId || 121 == blockId)
						{
							if (functionWrapper)
							{
								functionWrapper->setCanSwimming(true);
							}
							if (m_self->getNavigator() && m_self->getNavigator()->tryMoveTo(plantPoses[i].x * BLOCK_SIZE + 50, plantPoses[i].y * BLOCK_SIZE, plantPoses[i].z * BLOCK_SIZE + 50, m_fSpeed, m_nWorkRange * 4 * BLOCK_SIZE, BLOCK_SIZE))
							{
								m_self->playAnim(SEQ_WALK);
								m_TargetPos = plantPoses[i];
								return changeWeaponWithId((int)m_iItemID);
							}
							else
							{
								break;
							}
						}
						else if (1 == m_nAutoChangeLand && (100 == blockId || 101 == blockId ||
							120 == blockId /*冻土块*/
							|| 115 == blockId/*敲碎用*/))
						{
							if (m_iToolID)
							{
								if (functionWrapper)
								{
									functionWrapper->setCanSwimming(true);
								}
								if (m_self->getNavigator() && m_self->getNavigator()->tryMoveTo(plantPoses[i].x * BLOCK_SIZE + 50, plantPoses[i].y * BLOCK_SIZE, plantPoses[i].z * BLOCK_SIZE + 50, m_fSpeed, m_nWorkRange * 4 * BLOCK_SIZE, BLOCK_SIZE))
								{
									m_self->playAnim(SEQ_WALK);
									m_TargetPos = plantPoses[i];
									return changeWeaponWithId((int)m_iToolID);
								}
								else
								{
									break;
								}
							}
							else
							{
								break;
							}
						}
						else
						{
							break;
						}
					}
				}
			}
		}
		else
		{
			return false;
		}
	}
	else if (2 == m_nType)
	{
		std::map<int, long long> mEnableBlock;
		mEnableBlock[4] = 0;
		mEnableBlock[102] = 0;
		mEnableBlock[399] = 0;
		bool isSearch = false;
		if (checkBagItemWithId(11404))
		{
			isSearch = true;
			mEnableBlock[4] = 11404;
		}
		int seedIdlist[3] = { 236,241,11400 };
		for (int i = 0; i < 3; i++)
		{
			if (checkBagItemWithId(seedIdlist[i]))
			{
				isSearch = true;
				mEnableBlock[102] = seedIdlist[i];
				mEnableBlock[399] = seedIdlist[i];
				break;
			}
		}
		if (isSearch)
		{
			m_iToolID = getItemIDInBagWithType(3);
			bool isHoel = 1 == m_nAutoChangeLand && mEnableBlock[102] && m_iToolID;
			for (int x = -m_nWorkRange; x <= m_nWorkRange; x++)
			{
				for (int z = -m_nWorkRange; z <= m_nWorkRange; z++)
				{
					WCoord tmpPos = m_TargetPos;
					tmpPos.x += x;
					tmpPos.z += z;
					for (int y = -2; y <= 2; y++)
					{
						tmpPos.y = m_TargetPos.y + y;
						int blockId = 0;
						int airBlock = 0;
						if (aiModule)
						{
							blockId = aiModule->GetBlockId(tmpPos.x, tmpPos.y - 1, tmpPos.z);
							airBlock = aiModule->GetBlockId(tmpPos.x, tmpPos.y, tmpPos.z);
						}
						if (!airBlock)
						{
							auto iter = mEnableBlock.find(blockId);
							if (iter != mEnableBlock.end())
							{
								if (4 == blockId)
								{
									int bottomBlockId = 0;
									if (aiModule)
									{
										bottomBlockId = aiModule->GetBlockId(tmpPos.x, tmpPos.y - 2, tmpPos.z);
									}
									if (100 != bottomBlockId && 101 != bottomBlockId)
									{
										break;
									}
									if (functionWrapper)
									{
										functionWrapper->setAvoidWater(false);
									}
								}
								if (iter->second)
								{
									if (functionWrapper)
									{
										functionWrapper->setCanSwimming(true);
									}
									if (m_self->getNavigator() && m_self->getNavigator()->tryMoveTo(tmpPos.x * BLOCK_SIZE, tmpPos.y * BLOCK_SIZE, tmpPos.z * BLOCK_SIZE, m_fSpeed, m_nWorkRange * 4 * BLOCK_SIZE))
									{
										m_self->playAnim(SEQ_WALK);
										m_TargetPos = tmpPos;
										m_iItemID = iter->second;
										return changeWeaponWithId((int)m_iItemID);
									}
									else
									{
										break;
									}
								}
								else
								{
									break;
								}
							}
							else if (isHoel && (100 == blockId || 101 == blockId))
							{

								if (functionWrapper)
								{
									functionWrapper->setCanSwimming(true);
								}
								if (m_self->getNavigator() && m_self->getNavigator()->tryMoveTo(tmpPos.x * BLOCK_SIZE, tmpPos.y * BLOCK_SIZE, tmpPos.z * BLOCK_SIZE, m_fSpeed, m_nWorkRange * 4 * BLOCK_SIZE))
								{
									m_self->playAnim(SEQ_WALK);
									m_TargetPos = tmpPos;
									m_iItemID = mEnableBlock[102];
									return changeWeaponWithId((int)m_iToolID);
								}
								else
								{
									break;
								}
							}
							else
							{
								break;
							}
						}
					}
				}
			}
		}
		return false;
	}

	return false;
}

void  BTNTaskPlant::SendDataToDebug()
{
	if (!IsDebugMode())
	{
		return;
	}
	ScriptVM* LuaVM = MINIW::ScriptVM::game();
	int treeid = GetBehaviorTreeInstance()->GetInstanceID();
	int id = 0;
	LuaVM->callFunction("BTDebugBegainSendData", ">i", &id);
	if (id > 0)
	{
		BTLuaData temp;
		//m_TargetPos
		temp.SetValue_Vector3(m_TargetPos.x, m_TargetPos.y, m_TargetPos.z);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();
		temp.SetValue_Number(m_fSpeed);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();

		temp.SetValue_Number(m_iDist);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();
		temp.SetValue_Number(m_nType);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();

		temp.SetValue_Number(m_nAutoChangeLand);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();

		LuaVM->callFunction("BTDebugEndSendData", "isi", treeid, m_nodeid.c_str(), id);
	}
}

BTNODERESULT BTNTaskPlant::RunActive(const BTNodeRunData& rundata)
{
	// do somthing ...
	if (!m_self) return BTNODERESULT_FAIL;

	NavigationPath* npath = m_self->getNavigator();
	PathEntity* pathentity = npath ? npath->getPath() : NULL;
	if (pathentity)
	{
		if (pathentity->isFinished())
		{
			m_bIsMove = false;

			return planting();
		}
		else
		{
			m_bIsMove = true;
			return BTNODERESULT_WAIT;
		}
	}
	else
	{
		m_bIsMove = false;
		return planting();
	}
	return BTNODERESULT_FAIL;
}

void BTNTaskPlant::OnStart(const BTNodeRunData& rundata)
{
	BTNodeTask::OnStart(rundata);
}

void BTNTaskPlant::OnEnd()
{
	if (!m_self || !m_self->getNavigator() || !m_self->getWorld())
		return;
	// disactived
	if (m_self && m_self->getNavigator())
	{
		m_self->getNavigator()->clearPathEntity();
	}
	m_TargetPos = WCoord(0, -1, 0);
	BTNodeTask::OnEnd();
}


long long BTNTaskPlant::getItemIDInBagWithType(int type)
{
	PackContainer *bags = m_self->getBags();
	if (!bags)
	{
		return 0;
	}
	if (2 == type || 3 == type)
	{
		auto living = dynamic_cast<ActorLiving*>(m_self);
		auto livingAttr = living != NULL ? living->getLivingAttrib() : NULL;
		if (livingAttr)
		{
			BackPackGrid *grid = livingAttr->getEquipGrid(EQUIP_WEAPON);
			if (grid)
			{
				if (2 == type && IsShovelID(grid->getItemID()))
				{
					return grid->getItemID();
				}
				else if (3 == type && IsHoelID(grid->getItemID()))
				{
					return grid->getItemID();
				}
			}
		}
	}
	for (int i = 0; i < bags->getGridCount(); i++)
	{
		BackPackGrid *grid = bags->index2Grid(i);
		if (!grid || grid->isEmpty() || grid->getNum() <= 0)
			continue;
		int itemid = grid->getItemID();
		if (1 == type)
		{
			if (!isSapling(itemid))
			{
				continue;
			}
		}
		else if (2 == type)
		{
			if (!IsShovelID(itemid))
			{
				continue;
			}
		}
		else if (3 == type)
		{
			if (!IsHoelID(itemid))
			{
				continue;
			}
		}
		else
		{
			return 0;
		}

		return itemid;
	}
	return 0;
}

bool BTNTaskPlant::changeWeaponWithId(int id)
{
	auto living = dynamic_cast<ActorLiving*>(m_self);
	auto livingAttr = living != nullptr ? living->getLivingAttrib() : nullptr;
	if (livingAttr)
	{
		BackPackGrid* pweapon = livingAttr->getEquipGrid(EQUIP_WEAPON);
		if (pweapon)
		{
			if (pweapon->getItemID() != id)
			{
				PackContainer* bags = m_self->getBags();
				if (bags)
				{
					BackPackGrid* pto = bags->getGridByItemID(id);
					if (pto)
					{
						//从背包里找到了满足条件的工具 交换到手上
						BackPackGrid tmp;
						tmp.setItem(*pto);
						pto->setItem(*pweapon);
						pweapon->setItem(tmp);

						if (m_self->getBody())
						{
							livingAttr->applyEquips(m_self->getBody(), EQUIP_WEAPON);
							return true;
						}
					}
				}
			}
			else
			{
				return true;
			}
		}
	}
	return false;
}

bool BTNTaskPlant::checkBagItemWithId(int id)
{
	PackContainer *bags = m_self->getBags();
	if (!bags)
	{
		return false;
	}
	// 手持
	if (m_self->getEquipItem(EQUIP_WEAPON) == id)
	{
		return true;
	}
	// 背包
	for (int i = 0; i < bags->getGridCount(); i++)
	{
		BackPackGrid *grid = bags->index2Grid(i);
		if (!grid || grid->isEmpty() || grid->getNum() <= 0)
			continue;
		int itemid = grid->getItemID();
		if (id == itemid)
		{
			return true;
		}
	}
	return false;
}

BTNODERESULT BTNTaskPlant::planting()
{
	auto living = dynamic_cast<ActorLiving*>(m_self);
	WCoord pos = CoordDivBlock(living->getPosition()/* - WCoord(BLOCK_HALFSIZE, 0, BLOCK_HALFSIZE)*/);

	/*if (m_TargetPos != pos)
	{
	return BTNODERESULT_FAIL;
	}*/
	WCoord blockPos = m_TargetPos;
	blockPos.y--;
	m_self->faceWorldPos(blockPos, 180.0f, 180.0f);

	BLOCK_DATA_TYPE toolId = m_self->getEquipItem(EQUIP_WEAPON);
	auto* itemDef = GetDefManagerProxy()->getItemDef(toolId);
	if (itemDef)
	{
		if (1 == m_nType)
		{
			bool notEntered = true;
			//定义了脚本
			if (!itemDef->UseScript.empty() && (itemDef->UseTarget == ITEM_USE_CLICKBLOCK || itemDef->UseTarget == ITEM_USE_CLICKLIQUID))
			{
				m_self->playAnim(SEQ_ATTACK);

				bool scriptHandled = false;
				int setBlockAllRet = 0;

				// 锄头和铲子的逻辑被改动较大，没有使用统一脚本处理，导致这里只能一一列举(写死在代码中)……
				// 并且，现在锄头和铲子在野人AI中，是没有做行为的。
				// 挖坑种植等逻辑是糅合在一起得，改起来太繁琐，暂时先不改吧  chenzihang
				if (IsHoelID(toolId))
				{
					//锄头
					MINIW::ScriptVM::game()->callFunction("Hoe_OnUse_ByProgressFinish",
														  "u[ClientMob]u[World]iiii>bi",
														  m_self,
														  m_self->getWorld(),
														  blockPos.x,
														  blockPos.y,
														  blockPos.z,
														  (int)DIR_POS_Y,
														  &scriptHandled,
														  &setBlockAllRet);
				}
				else if (IsShovelID(toolId))
				{
					//铲子
					MINIW::ScriptVM::game()->callFunction("Scoop_OnUse_ByProgressFinish",
														  "u[ClientMob]u[World]iiii>bi",
														  m_self,
														  m_self->getWorld(),
														  blockPos.x,
														  blockPos.y,
														  blockPos.z,
														  (int)DIR_POS_Y,
														  &scriptHandled,
														  &setBlockAllRet);
				}
				else
				{
					MINIW::ScriptVM::game()->callFunction(itemDef->UseScript.c_str(),
														  "u[ClientMob]u[World]iiii>b",
														  m_self,
														  m_self->getWorld(),
														  blockPos.x,
														  blockPos.y,
														  blockPos.z,
														  (int)DIR_POS_Y,
														  &scriptHandled);
				}

				notEntered = false;

				if (scriptHandled)
				{
					if (!changeWeaponWithId((int)m_iItemID))
					{
						return BTNODERESULT_SUCCESS;
					}
					else
					{
						toolId = m_self->getEquipItem(EQUIP_WEAPON);
						if (!GetDefManagerProxy()->getItemDef(toolId)) return BTNODERESULT_SUCCESS;
					}
					// 						return BTNODERESULT_SUCCESS;
				}
				else
				{
					return BTNODERESULT_FAIL;
				}
			}

			//摆块
			if (toolId < SOC_BLOCKID_MAX/* || (toolId >= EX_BLOCKID_MIN && toolId < EX_BLOCKID_MAX)*/)//SOC 方块ID 范围 0-4095 不支持扩展id
			{
				notEntered = false;

				bool result = m_self->placeBlock(toolId, blockPos.x, blockPos.y + 1, blockPos.z, DIR_POS_Y, (float)blockPos.x, (float)blockPos.y, (float)blockPos.z);
				if (result)
				{
					if (m_self->getObjType() == OBJ_TYPE_VILLAGER)
					{
						ActorVillager* village = static_cast<ActorVillager *>(m_self);
						if (village)
							village->addHuggerByType("plant");
					}
					m_self->playAnim(SEQ_ATTACK);
					return BTNODERESULT_SUCCESS;
				}
				else
				{
					return BTNODERESULT_FAIL;
				}
			}

			if (notEntered)
			{
				return BTNODERESULT_FAIL;
			}
		}
		else if (2 == m_nType)
		{
			if (!itemDef->UseScript.empty() && (itemDef->UseTarget == ITEM_USE_CLICKBLOCK || itemDef->UseTarget == ITEM_USE_CLICKLIQUID))
			{
				m_self->playAnim(SEQ_ATTACK);

				bool scriptHandled = false;
				MINIW::ScriptVM::game()->callFunction(itemDef->UseScript.c_str(), "u[ClientMob]u[World]iiii>b", m_self, m_self->getWorld(), blockPos.x, blockPos.y, blockPos.z, (int)DIR_POS_Y, &scriptHandled);

				if (scriptHandled)
				{
					if (IsHoelID(toolId))
					{
						changeWeaponWithId((int)m_iItemID);
						toolId = (int)m_iItemID;
					}
					else
					{
						return BTNODERESULT_SUCCESS;
					}
				}
				else
				{
					return BTNODERESULT_FAIL;
				}
			}

			//摆块
			if (toolId < SOC_BLOCKID_MAX/* || (toolId >= EX_BLOCKID_MIN && toolId < EX_BLOCKID_MAX)*/)//SOC 方块ID 范围 0-4095 不支持扩展id
			{
				bool result = m_self->placeBlock(toolId, blockPos.x, blockPos.y + 1, blockPos.z, DIR_POS_Y, (float)blockPos.x, (float)blockPos.y, (float)blockPos.z);
				if (result)
				{
					if (m_self->getObjType() == OBJ_TYPE_VILLAGER)
					{
						ActorVillager* village = static_cast<ActorVillager *>(m_self);
						if (village)
							village->addHuggerByType("plant");
					}
					m_self->playAnim(SEQ_ATTACK);
					return BTNODERESULT_SUCCESS;
				}
				else
				{
					return BTNODERESULT_FAIL;
				}
			}
		}
	}
	else
	{
		return BTNODERESULT_FAIL;
	}
	return BTNODERESULT_FAIL;
}
