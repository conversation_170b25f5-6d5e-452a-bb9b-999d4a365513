#include "ClientActorFishhook.h"
#include "world.h"
#include "FishhookLocomotion.h"
#include "FishingComponent.h"
#include "ClientActorLiving.h"
#include "ClientItem.h"
#include "ModelItemMesh.h"
#include "Entity/OgreEntity.h"
#include "proto_common.pb.h"
#include "proto_hc.pb.h"
#include "defdata.h"
#include "ActorManager.h"
using namespace game::common;
using namespace game::hc;

ClientActorFishhook::ClientActorFishhook()
	:m_GotModel(nullptr), m_GotItemID(0)
{
	CreateComponent<FishhookLocomotion>("FishhookLocomotion");
}

ClientActorFishhook::~ClientActorFishhook()
{
	clearGotItem();
}

void ClientActorFishhook::onImpactWithActor(ClientActor* actor, const std::string& partname)
{

}

void ClientActorFishhook::onImpactWithBlock(const WCoord* blockpos, int face)
{
	int blockid = m_pWorld->getBlockID(*blockpos);
	int blockdata = m_pWorld->getBlockData(*blockpos);
}

void ClientActorFishhook::onCollideWithPlayer(ClientActor* player)
{
}

int ClientActorFishhook::saveToPB(PB_GeneralEnterAOIHC* pb)
{
	PB_ActorFishhook* actorFishhook = pb->mutable_actorfishhook();
	PB_ActorProjectile* actorMob = actorFishhook->mutable_actorprojectile();
	saveToProjectilePB(actorMob);
	FishhookLocomotion* loco = dynamic_cast<FishhookLocomotion*>(getLocoMotion());
	if (m_GotItemID)
	{
		actorFishhook->set_resultid(m_GotItemID);
	}
	return 0;
}

int ClientActorFishhook::LoadFromPB(const PB_GeneralEnterAOIHC& pb)
{
	const PB_ActorFishhook& actorFishhook = pb.actorfishhook();
	const PB_ActorProjectile& actorMob = actorFishhook.actorprojectile();
	const PB_ActorCommon& actorCommon = actorMob.basedata();
	int ret = loadPBActorCommon(actorCommon);
	if (ret != 0)
		return ret;
	LoadFromProjectilePB(actorMob);
	int itemId = actorFishhook.resultid();
	if (itemId)
	{
		addGotItem(itemId);
	}
	return 0;
}

flatbuffers::Offset<FBSave::SectionActor> ClientActorFishhook::save(SAVE_BUFFER_BUILDER& builder)
{
	return flatbuffers::Offset<FBSave::SectionActor>();
}

bool ClientActorFishhook::load(const void* srcdata, int version)
{
	return false;
}

void ClientActorFishhook::tick()
{
	ClientActorProjectile::tick();
	ModelItemMesh* gotModelMesh = dynamic_cast<ModelItemMesh*>(m_GotModel);
	if (gotModelMesh && gotModelMesh->getEntity())
	{
		Rainbow::Vector4f lightparam(0, 0, 0, 0);
		WCoord center = getPosition();
		center.y += getLocoMotion()->m_BoundHeight / 2;
		if (m_pWorld) m_pWorld->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(center));
		gotModelMesh->getEntity()->SetInstanceData(lightparam);
	}
}

void ClientActorFishhook::onImpactSolid()
{
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(getActorMgr());
	if (!actorMgr) return ;
	ActorLiving* pLiving = dynamic_cast<ActorLiving*>(getActorMgr()->findActorByWID(getMasterObjId()));
	if (pLiving)
	{
		auto* pCom = pLiving->m_pFishingComponent;
		if (pCom)
		{
			pCom->endFishing();
		}
	}
}

void ClientActorFishhook::onPickEnd()
{
	ActorLiving* pLiving = dynamic_cast<ActorLiving*>(getActorMgr()->findActorByWID(getMasterObjId()));
	if (pLiving)
	{
		auto* pCom = pLiving->m_pFishingComponent;
		if (pCom)
		{
			pCom->onFishingDone();
		}
	}
}

void ClientActorFishhook::onEnterWater()
{
	ActorLiving* pLiving = dynamic_cast<ActorLiving*>(getActorMgr()->findActorByWID(getMasterObjId()));
	if (pLiving)
	{
		auto* pCom = pLiving->getFishingComponent();
		if (pCom)
		{
			pCom->onDoFishing();
		}
	}
}

void ClientActorFishhook::addGotItem(int itemid)
{
	clearGotItem();
	m_GotModel = ClientItem::createItemModel(itemid, ITEM_MODELDISP_SCENE);
	
	if (m_GotModel)
	{
		m_GotModel->SetPosition(Rainbow::Vector3f{ 0,-30,0 });
		if (m_EntityModel)
		{
			m_EntityModel->BindObject(101, m_GotModel);
		}
	}

	m_GotItemID = itemid;
}

void ClientActorFishhook::clearGotItem()
{
	if (m_GotModel)
	{
		if (m_EntityModel) m_EntityModel->UnbindObject(m_GotModel);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_GotModel);
	}
	m_GotItemID = 0;
}

ClientActorFishhook* ClientActorFishhook::shootFishhookAuto(int itemid, World* pworld, const WCoord& pos, const WCoord& targetPos, const Rainbow::Vector3f& motion, long long shooterObjId, int minBlockPosY)
{
	if (pworld == nullptr) return nullptr;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return nullptr;
	ClientActorFishhook* projectile = ENG_NEW(ClientActorFishhook)();
	projectile->init(itemid);
	actorMgr->spawnActor(projectile, pos, 0, 0);
	projectile->m_StartPos = projectile->getPosition();

	FishhookLocomotion* locomove = static_cast<FishhookLocomotion*>(projectile->getLocoMotion());
	locomove->drop(targetPos, motion, minBlockPosY);

	projectile->playMotion(projectile->m_ProjectileDef->TailEffect);
	projectile->m_AttackPoints = projectile->m_ProjectileDef->AttackValue;
	if (projectile->m_ProjectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}
	projectile->setMasterObjId(shooterObjId);
	return projectile;
}
