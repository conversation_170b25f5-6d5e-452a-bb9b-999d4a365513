
#ifndef _HORSE_LOCOMOTION_H_
#define _HORSE_LOCOMOTION_H_

#include "LivingLocoMotion.h"

class HorseLocomotion : public LivingLocoMotion //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(HorseLocomotion)
	//tolua_begin
	HorseLocomotion() : m_StartJumpTicks(0)
		, m_StartJumpForce(0), m_bSpecialGravity(false), m_fUp_gravity(1.0f), m_fDown_gravity(1.0f)
	{

	}
	virtual ~HorseLocomotion() {}
	virtual float getGravityFactor(bool up); //up ? 上升: 下降
	virtual void getCollideBox(CollideAABB &box);
	void setSpecialGravity(float up_gravity, float down_gravity);
	bool isFloatageing();

	int m_StartJumpTicks; //
	float m_StartJumpForce;

	bool m_bSpecialGravity;
	float m_fUp_gravity;
	float m_fDown_gravity;
	//tolua_end
	virtual void playWalkOnLiquidEffect(bool iswater);
protected:
	virtual void doJump() override;
	virtual void tick() override;
	virtual void moveEntityWithHeading(float strafing, float forward);
	virtual bool canWalkOnLiquid(bool iswater);
	virtual bool checkSeatInWaterSkill(int bit);
	virtual bool checkCanUpAndDown(float fRotationPitch);
	virtual bool isDoingRush();
	virtual bool ignoreMoveEntityWithHeading();    // 20210804：部分坐骑的移动逻辑不在moveEntityWithHeading里处理  codeby： keguanqiang
}; //tolua_exports



#endif