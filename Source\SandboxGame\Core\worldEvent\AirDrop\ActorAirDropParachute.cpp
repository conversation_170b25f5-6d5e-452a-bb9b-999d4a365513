#include "ActorAirDropParachute.h"
#include "ClientPlayer.h"
#include "BallLocomotion.h"
#include "PlayerAttrib.h"
#include "ParticlesComponent.h"
#include "Utilities/Logs/LogAssert.h"
#include "ClientActorManager.h"
#include "ActorManager.h"
#include "WorldManager.h"
#include "world.h"
#include "OgreTimer.h"
#include "system/SandboxActorSubsystem.h"


IMPLEMENT_SCENEOBJECTCLASS(ActorAirDropParachute)
using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

ActorAirDropParachute::ActorAirDropParachute() : ActorBall()
{
    // 构造函数可以添加额外的初始化逻辑
    m_damageHP = 0;
}

void ActorAirDropParachute::setDamageHP(int damageHP)
{
    m_damageHP = damageHP;
}

void ActorAirDropParachute::onCollideWithPlayer(ClientActor* player)
{
    // 获取玩家ID
    long long playerId = player->getObjId();

    // 获取当前时间
    unsigned int currentTick = Timer::getSystemTick();

    // 检查该玩家是否在冷却时间内
    auto it = m_playerCollideTimers.find(playerId);
    if (it != m_playerCollideTimers.end())
    {
        unsigned int lastCollideTick = it->second;
        if (currentTick - lastCollideTick < COLLIDE_COOLDOWN)
        {
            // 如果该玩家在冷却时间内，直接返回，不处理碰撞
            LOG_INFO("ActorAirDropParachute: 玩家 %lld 在冷却时间内，忽略碰撞。上次碰撞: %u, 当前: %u",
                     playerId, lastCollideTick, currentTick);
            return;
        }
    }

    // 更新该玩家的最后碰撞时间
    m_playerCollideTimers[playerId] = currentTick;

    // 输出日志，包含时间信息
    LOG_INFO("ActorAirDropParachute: 空投箱碰到了玩家 %lld! 时间: %u", playerId, currentTick);

    // 调用父类方法继续处理碰撞逻辑
    ActorBall::onCollideWithPlayer(player);

    // 在碰撞位置创建爆炸效果
    if (m_pWorld && !m_pWorld->isRemoteMode())
    {
        // 获取碰撞位置（使用降落伞的位置）
        WCoord explosionPos = getPosition();
        // 参数：位置、半径、是否半圆爆炸、伤害值、是否破坏方块、是否来自技能
        float damageValue = m_damageHP; // 设置较高的伤害值
        m_pWorld->createExplosionNew(nullptr, explosionPos, 3, false, damageValue, false, false);

        LOG_INFO("ActorAirDropParachute: 在位置 (%d, %d, %d) 创建爆炸效果!",
                 explosionPos.x, explosionPos.y, explosionPos.z);
    }

    // 通过炸弹扣除玩家生命值无需主动调用ClientPlayer
    // 将ClientActor转换为ClientPlayer
    // ClientPlayer* pPlayer = dynamic_cast<ClientPlayer*>(player);
    // if (pPlayer != nullptr)
    // {
    //     // 获取玩家的属性组件
    //     PlayerAttrib* pAttrib = dynamic_cast<PlayerAttrib*>(pPlayer->getAttrib());
    //     if (pAttrib != nullptr)
    //     {
    //         // 减少玩家生命值50点
    //         //LOG_INFO("ActorAirDropParachute: 对玩家 %lld 造成50点伤害! 时间: %u", playerId, currentTick);
    //         //pAttrib->addHP(-50.0f);

    //         // 播放受伤音效
    //         pPlayer->playHurtSound();

    //         // 添加受伤特效
    //         ParticlesComponent::playParticles(pPlayer, "hit.ent");
    //     }
    // }
}

ActorAirDropParachute* ActorAirDropParachute::create(World* pworld, int x, int y, int z, float vx, float vy, float vz)
{
    ActorAirDropParachute* actor = SANDBOX_NEW(ActorAirDropParachute);
    actor->model_id = 11000051;
    actor->init();
    actor->getLocoMotion()->m_Motion = Rainbow::Vector3f(vx, vy, vz);
    if (!pworld) return actor;

    ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
    if (!actorMgr) return actor;

    actorMgr->spawnActor(actor, x, y, z, 0.0f, 0.0f);
    actor->m_SpawnPos = actor->getPosition();
    return actor;
}