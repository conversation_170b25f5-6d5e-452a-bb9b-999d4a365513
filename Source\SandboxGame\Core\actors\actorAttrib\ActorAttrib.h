
#ifndef __ACTORATTRIB_H__
#define __ACTORATTRIB_H__

#include "AttribTypes.h"
#include <vector>
#include <map>
#include "SandboxSceneObject.h"
#include "ActorComponent_Base.h"
#include "system/SandboxGame.h"
#include "IActorAttrib.h"


#define OLD_ATTRIBUTES

class ClientActor;
class ActorAttribExecute;
class HPValue;
class EXPORT_SANDBOXGAME ActorAttrib;
class ActorAttrib : public ActorComponentBase , public IActorAttrib//tolua_exports
{//tolua_exports
public:
	DECLARE_COMPONENTCLASS(ActorAttrib)
#ifdef IWORLD_DEV_BUILD
	virtual std::string toString();
#endif
	/* 进入owner */
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	/* 离开owner */
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

	void CreateEvent2();
	void DestroyEvent2();

	//tolua_begin
	ActorAttrib();
	virtual ~ActorAttrib();

	virtual void revive();
	virtual void tick();
	virtual bool attackedFrom(OneAttackData &atkdata, ClientActor *attacker=NULL);
	virtual void onDie();
	/**
	@brief 玩法模式切换后下发通知函数
	*/
	virtual void onToggleGameMode() {}

	void resetMaxLife(float v);
	

	bool isDead();


	virtual float getMoveSpeed(int type = 0);

	virtual float getSpeedInAir()
	{
		return 0;
	}

	virtual float getFlySpeed()
	{
		return 0;
	}
	
	//=====================================================================//
	virtual void addHP(float hp, bool overflowable = false) override;
	virtual void addHPByTrueDamage(float hp, bool overflowable = false);
	virtual void setHP(float hp, bool overflowable = false) override;
	void initHP(float hp) { m_Life = hp; }
	virtual float getHP() override;
	void setHpForTrigger(float hp, bool overflowable = false);
	
	void setBasicMaxHP(float maxHP);

	void setExtraHP(float hp);
	virtual float getExtraHP() override;

	/**
	@brief 原始的最大值
	*/
	float getBasicMaxHP();

	void setMaxHP(float maxHP);
	void initMaxHP(float maxHP);

	virtual float getMaxHP();

	void setHPRecover(float recover);
	float getHPRecover();
	void setBasicOverflowHP(float overflow);
	float getBasicOverflowHP();
	void setOverflowHP(float overflow);
	virtual float getOverflowHP();
	float getLimitHP();
	//=====================================================================//

	int immuneToFire() //0: 没有,  1: 免疫火buff, 2:免疫火和岩浆
	{
		return m_immuneToFire;
	}

	virtual void setImmuneToFire(int pFlag) override{
		m_immuneToFire = pFlag;
	}

	int getAttackType(int attacktype) 
	{
		if (m_iAttackType < 0) { return attacktype; }

		return m_iAttackType;
	}
	int getAttackType()
	{
		return m_iAttackType;
	}
	void setAttackType(int iAttackType)
	{
		m_iAttackType = iAttackType;
	}

	bool hasImmuneType(int iImmuneType) { return (m_iImmuneType&iImmuneType) > 0; }
	int getImmuneTypeByAttackType(int iImmuneType);
	void resetImmuneType() { m_iImmuneType = 0; }
	void setImmuneType(int iImmuneType, bool bAdd);


	bool hasImmuneAttackType(int iImmuneType) { return (m_iImmuneAttackType&iImmuneType) > 0; }
	void resetImmuneAttackType() { m_iImmuneAttackType = 0; }
	void setImmuneAttackType(int iImmuneType, bool bAdd);

	virtual void setSpeedAtt(int type, float v);
	virtual float getSpeedAtt(int type);

	int getImmuneType() { return m_iImmuneType; }

	int getImmuneAttackType() { return m_iImmuneAttackType; }
	int getHurtResistantTime() { return m_HurtResistantTime; }
	float getMaxHurtInResistant() { return m_MaxHurtInResistant; }
	ClientActor *getOwnerActor() {return m_OwnerActor;}
	//=====================================================================//

	void addArmor(float val);
	virtual float getArmor() override;
	void setArmor(float v, bool force = true);
	void setMaxArmor(float v);
	float getMaxArmor() { return m_maxArmor; }
	bool isUnsighted();
	void setUnsighted(bool isUnsighted);
	//tolua_end
	virtual void setOffLine(bool b) { assert(0);/*玩家的才有的*/ };
	virtual bool getOffLine() override { return false; };

	ActorAttribExecute* getExtraExecute(ActorAttribType);
	bool isValidSpeedType(int type){return type >= Actor_Walk_Speed && type < Actor_Speed_Type_Count;}
public:
	//tolua_begin
	static bool m_DisplayHurtNumber;
	//tolua_end
protected:
	ClientActor *m_OwnerActor;
	char m_unsighted;

	/**
	@brief HP
	*/
	volatile float m_Life;

#ifdef OLD_ATTRIBUTES
	/**
	@brief 基础最大HP
	*/
	float m_fBasicMaxHP;

	/**
	@brief 最大HP
	*/
	float m_fMaxHP;
#else
	MNSandbox::AutoRef<HPValue> mHPValue;
#endif
	/**
	@brief 基础溢出HP
	*/
	float m_fBasicOverflowHP;

	/**
	@brief 溢出HP
	*/
	float m_fOverflowHP;

	/**
	@brief HP恢复速度倍率
	*/
	float m_fRecover;

	/**
	@brief 护盾值
	*/
	float m_armor;
	/**
	@brief 护盾最大值
	*/
	float m_maxArmor;

	/**
	@brief 额外生命值
	*/
	float m_extraLife;


	int m_immuneToFire;

	int m_HurtResistantTime;
	float m_MaxHurtInResistant;

	int m_iAttackType;
	int m_iImmuneType;
	int m_iImmuneAttackType;
	float m_fSpeed[5];//0 is walk, 1 is run, 2 is sneak, 3 is swim, 4 is jump
	std::map<ActorAttribType, ActorAttribExecute*>  m_attribExecute; //属性映射表
	std::vector<ActorAttribType>  m_hpExecute;
	// 通知监听
	MNSandbox::AutoRef<MNSandbox::Listener<>> m_listenerBlockCoagulation;
};//tolua_exports

#endif
