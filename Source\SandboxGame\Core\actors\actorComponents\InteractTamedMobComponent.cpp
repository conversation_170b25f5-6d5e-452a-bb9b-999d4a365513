#include "InteractTamedMobComponent.h"
#include "PlayerControl.h"

#include "world.h"
#include "GameNetManager.h"
#include "backpack.h"
#include "ActorDesertBusinessman.h"
#include "MpActorManager.h"
#include "special_blockid.h"

IMPLEMENT_COMPONENTCLASS(InteractTamedMobComponent)

InteractTamedMobComponent::InteractTamedMobComponent()
:m_InteractTamedMobID(0)
{
	 
}

void InteractTamedMobComponent::InteractMobPack(const std::string& name, const std::string& param, long long mobID)
{
	m_InteractTamedMobID = mobID;

	if (!GetOwner()) return ;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;

	if (!m_owner->hasUIControl() || g_pPlayerCtrl->getObjId() != m_owner->getObjId())
	{
		LOG_INFO("SEND PB_INTERACT_MOBPACK_HC %d", m_owner->getObjId());
		PB_InteractMobPackHC msg;
		msg.set_uiname(name.c_str());
		msg.set_param(param.c_str());
		msg.set_mobid(mobID);
		GetGameNetManagerPtr()->sendToClient(m_owner->getObjId(), PB_INTERACT_MOBPACK_HC, msg);
		return;
	}
	MINIW::ScriptVM::game()->callFunction("CppOpenUI", "ss", name.c_str(), param.c_str());
}
bool InteractTamedMobComponent::InteractMobItem(int fromIndex, int toIndex)
{
	if (!GetOwnerPlayer()) return false;
	m_owner = static_cast<ClientPlayer*>(GetOwnerPlayer());
	World* pWorld = m_owner->getWorld();
	if (!pWorld) return false;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
	if (!actorMgr) return false;

	BackPack* backpack = m_owner->getBackPack();
	ClientMob* mob = actorMgr->findMobByWID(m_InteractTamedMobID);
	if (!mob || !mob->getBags() || mob->isDead() || mob->needClear())
		return false;
	int playerIndex = 0, mobIndex = 0;
	BackPackGrid* fromGrid = NULL;
	BackPackGrid* toGrid = NULL;
	if (fromIndex >= TAMED_MOB_START_INDEX && fromIndex < TAMED_MOB_START_INDEX + 1000)
	{
		fromGrid = mob->getBags()->index2Grid(fromIndex - TAMED_MOB_START_INDEX);
	}
	else
	{
		if (fromIndex >= 0)
			fromGrid = backpack->index2Grid(fromIndex);
	}
	if (toIndex >= TAMED_MOB_START_INDEX && toIndex < TAMED_MOB_START_INDEX + 1000)
	{
		toGrid = mob->getBags()->index2Grid(toIndex - TAMED_MOB_START_INDEX);
	}
	else
	{
		if (toIndex >= 0)
			toGrid = backpack->index2Grid(toIndex);
	}
	if (!fromGrid || !toGrid)
		return false;


	if (!pWorld->isRemoteMode())
	{
		if (fromGrid->getItemID() == toGrid->getItemID())
		{
			if (IsDyeableBlock(fromGrid->getItemID()) && fromGrid->getUserdataStr()!= toGrid->getUserdataStr())
			{
				BackPackGrid tmp;
				tmp.setItem(*fromGrid);
				fromGrid->setItem(*toGrid);
				toGrid->setItem(tmp);
			}
			else if (toGrid->def)
			{
				int num = fromGrid->getNum();
				int maxnum = toGrid->def->StackMax - toGrid->getNum();
				if (maxnum <= 0) return false;
				if (num > maxnum)  num = maxnum;
				toGrid->addNum(num);
				if (num >= fromGrid->getNum())
					fromGrid->clear();
				else
					fromGrid->addNum(-num);
			}
			else
			{
				return false;
			}
		}
		else
		{
			BackPackGrid tmp;
			tmp.setItem(*fromGrid);
			fromGrid->setItem(*toGrid);
			toGrid->setItem(tmp);
		}
		if (toIndex < TAMED_MOB_START_INDEX)
			backpack->afterChangeGrid(toIndex);
		if (fromIndex < TAMED_MOB_START_INDEX)
			backpack->afterChangeGrid(fromIndex);

		//生物背包内交换物品后需要通知UI刷新 by：Jeff
		if (fromIndex >= TAMED_MOB_START_INDEX && fromIndex < TAMED_MOB_START_INDEX + 1000 && toIndex >= TAMED_MOB_START_INDEX && toIndex < TAMED_MOB_START_INDEX + 1000)
		{
			//ge GameEventQue::GetInstance().postBackpackChange(TAMED_MOB_START_INDEX);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("grid_index", TAMED_MOB_START_INDEX);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
			}
		}

		PB_UpdateMobBackpackHC msg;
		msg.set_mobid(m_InteractTamedMobID);
		RepeatedPtrField<PB_ItemData>* itemInfos = msg.mutable_iteminfo();
		if (toIndex >= TAMED_MOB_START_INDEX)
		{
			PB_ItemData *itemData = itemInfos->Add();
			itemData->set_index(toGrid->getIndex());
			if (toGrid->def)
				itemData->set_itemid(toGrid->def->ID);
			itemData->set_num(toGrid->getNum());
			itemData->set_durable(toGrid->getDuration());
			itemData->set_maxdurable(toGrid->getMaxDuration());
			itemData->set_toughness(toGrid->getToughness());
			itemData->set_userdata((int)(size_t)toGrid->userdata); //(char)
			toGrid->saveRunesAndEnchants(itemData);
			toGrid->saveDataComponentPB(itemData);
		}
		if (fromIndex >= TAMED_MOB_START_INDEX)
		{
			PB_ItemData *itemData = itemInfos->Add();
			itemData->set_index(fromGrid->getIndex());
			if (fromGrid->def)
				itemData->set_itemid(fromGrid->def->ID);
			itemData->set_num(fromGrid->getNum());
			itemData->set_durable(fromGrid->getDuration());
			itemData->set_maxdurable(fromGrid->getMaxDuration());
			itemData->set_toughness(fromGrid->getToughness());
			itemData->set_userdata((int)(size_t)fromGrid->userdata); //(char)
			fromGrid->saveRunesAndEnchants(itemData);
			fromGrid->saveDataComponentPB(itemData);
		}
		pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_UPDATE_MOB_BACKPACK_HC, msg, mob, true);
	}
	return true;
}
void InteractTamedMobComponent::moveMobItem(int gridIndex, int moveType,int toGridIndex)
{
	if (!GetOwner()) return;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	World* pWorld = m_owner->getWorld();
	BackPack* backpack = m_owner->getBackPack();
	if (!pWorld) return;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
	if(!actorMgr) return;

	ClientMob* mob = actorMgr->findMobByWID(m_InteractTamedMobID);
	if (!mob || !mob->getBags() || mob->isDead() || mob->needClear())
		return;
	BackPackGrid* moveToGrid;
	if (toGridIndex != -1)
	{
		moveToGrid = backpack->index2Grid(toGridIndex);
	}
	else
	{
		moveToGrid = backpack->index2Grid(MOUSE_PICKITEM_INDEX);
	}
	if (!moveToGrid)
		return;
	int toGrididx = toGridIndex ==-1 ? MOUSE_PICKITEM_INDEX : toGridIndex;
	BackPackGrid* mobGrid = mob->getBags()->index2Grid(gridIndex - TAMED_MOB_START_INDEX);
	if (!mobGrid)
		return;

	long long Masterid = 0;
	if (mobGrid->getNum() > 0 && mob->getDefID() == 3823 && (Masterid = mob->getMasterObjId()) != 0)//骆驼物品被偷特殊处理 by：Jeff
	{
		ActorDesertBusInessMan* BusInessMan = dynamic_cast<ActorDesertBusInessMan*>(actorMgr->findMobByWID(Masterid));
		if (BusInessMan)
		{
			BusInessMan->playerStealGoods(m_owner->getUin());
		}
	}

	if (moveToGrid->getNum() > 0)
	{
		if (moveToGrid->getItemID() == mobGrid->getItemID())
		{
			if (IsDyeableBlock(moveToGrid->getItemID()) && moveToGrid->getUserdataStr() != mobGrid->getUserdataStr())
			{
				BackPackGrid tmp;
				tmp.setItem(*moveToGrid);
				moveToGrid->setItem(*mobGrid);
				mobGrid->setItem(tmp);
			}
			else if (!mobGrid->isEmpty() && mobGrid->getNum() > 0)
			{
				int num = moveToGrid->getNum();
				int maxnum = mobGrid->def->StackMax - mobGrid->getNum();
				if (maxnum <= 0) return;
				if (num > maxnum)  num = maxnum;

				mobGrid->addNum(num);
				if (num >= moveToGrid->getNum())
					moveToGrid->clear();
				else
					moveToGrid->addNum(-num);
			}
		}
		else
		{
			BackPackGrid tmp;
			tmp.setItem(*mobGrid);
			mobGrid->setItem(*moveToGrid);
			moveToGrid->setItem(tmp);
		}
		backpack->afterChangeGrid(toGrididx);
	}
	else
	{
		int moveNum = 1;
		if (moveType == 2)
		{
			moveNum = mobGrid->getNum();
		}
		backpack->moveItem(mobGrid, moveNum, toGrididx, TAMED_MOB_START_INDEX);
	}

	//生物背包内交换物品后需要通知UI刷新
	if (gridIndex >= TAMED_MOB_START_INDEX)
	{
		//ge GameEventQue::GetInstance().postBackpackChange(TAMED_MOB_START_INDEX);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", TAMED_MOB_START_INDEX);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}

	if (!pWorld->isRemoteMode())
	{
		PB_UpdateMobBackpackHC msg;
		msg.set_mobid(m_InteractTamedMobID);
		RepeatedPtrField<PB_ItemData>* itemInfos = msg.mutable_iteminfo();
		for (unsigned int i = 0; i < mob->getBags()->m_Grids.size(); i++)
		{
			BackPackGrid synGrid = mob->getBags()->m_Grids[i];
			PB_ItemData *itemData = itemInfos->Add();
			itemData->set_index(i);
			if (synGrid.def)
				itemData->set_itemid(synGrid.def->ID);
			itemData->set_num(synGrid.getNum());
			itemData->set_durable(synGrid.getDuration());
			itemData->set_maxdurable(synGrid.getMaxDuration());
			itemData->set_toughness(synGrid.getToughness());
			itemData->set_userdata((int)(size_t)synGrid.userdata); //(char)
			synGrid.saveRunesAndEnchants(itemData);
			synGrid.saveDataComponentPB(itemData);
		}
		pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_UPDATE_MOB_BACKPACK_HC, msg, mob, true);

	}
}

void InteractTamedMobComponent::PickMobBackpack(int gridIndex, int moveType, int toGridIndex)
{
	if (!GetOwner()) return ;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	World* pWorld = m_owner->getWorld();
	if (pWorld->isRemoteMode())
	{
		PB_MoveMobBackpackItemCH msg;
		msg.set_gridindex(gridIndex);
		msg.set_movetype(moveType);
		GetGameNetManagerPtr()->sendToHost(PB_MOVE_MOBBACKPACKITEM_CH, msg);
		return;
	}
	moveMobItem(gridIndex, moveType, toGridIndex);
}
IMPLEMENT_COMPONENTCLASS(MPInteractTamedMobComponent)

MPInteractTamedMobComponent::MPInteractTamedMobComponent()
:InteractTamedMobComponent()
{

}

bool MPInteractTamedMobComponent::InteractMobItem(int fromIndex, int toIndex)
{
	if (!GetOwner()) return false;
	m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	World* pWorld = m_owner->getWorld();
	if (!pWorld) return false;
	ActorManager* actorMgr = static_cast<ActorManager*>(pWorld->getActorMgr());
	BackPack* backpack = m_owner->getBackPack();
	if (pWorld->isRemoteMode())
	{
		ClientMob* mob = actorMgr->findMobByWID(m_InteractTamedMobID);
		if (!mob || !mob->getBags() || mob->isDead() || mob->needClear())
			return false;
		int playerIndex = 0, mobIndex = 0;
		BackPackGrid* fromGrid = NULL;
		BackPackGrid* toGrid = NULL;
		if (fromIndex >= TAMED_MOB_START_INDEX && fromIndex < TAMED_MOB_START_INDEX + 1000)
		{
			fromGrid = mob->getBags()->index2Grid(fromIndex - TAMED_MOB_START_INDEX);
		}
		else
		{
			if (fromIndex >= 0)
				fromGrid = backpack->index2Grid(fromIndex);
		}
		if (toIndex >= TAMED_MOB_START_INDEX && toIndex < TAMED_MOB_START_INDEX + 1000)
		{
			toGrid = mob->getBags()->index2Grid(toIndex - TAMED_MOB_START_INDEX);
		}
		else
		{
			if (toIndex >= 0)
				toGrid = backpack->index2Grid(toIndex);
		}
		if (!fromGrid || !toGrid)
			return false;

		PB_InteractMobBackpackItemCH msg;
		msg.set_fromindex(fromIndex);
		msg.set_toindex(toIndex);
		GetGameNetManagerPtr()->sendToHost( PB_INTERACT_MOBBACKPACKITEM_CH, msg);
		return true;
	}
	return InteractTamedMobComponent::InteractMobItem(fromIndex, toIndex);
}

