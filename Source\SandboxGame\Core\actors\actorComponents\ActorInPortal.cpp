#include "ActorInPortal.h"

#include "ClientActor.h"
#include "chunk.h"
#include "world.h"
#include "ClientPlayer.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include <assert.h>
#include "RiddenComponent.h"
#include "SandboxIdDef.h"

IMPLEMENT_COMPONENTCLASS(ActorInPortal)

using namespace MNSandbox;
ActorInPortal::ActorInPortal():m_InPortal(false),m_InPortalCooldown(0),m_InPortalTime(0)
{
	CreateEvent2();
}
ActorInPortal::~ActorInPortal()
{
	DestroyEvent2();
}
void ActorInPortal::CreateEvent2()
{
	typedef ListenerFunctionRef<int&> Listener1;
	m_listenerPortal1 = SANDBOX_NEW(Listener1, [&](int& eyeblock) -> void
		{
			if (this->isInPortal())
			{
				eyeblock = BLOCK_PORTAL;
			}
		});
	Event2().Subscribe("Portal_eyeblock", m_listenerPortal1);

}
void ActorInPortal::DestroyEvent2()
{
	SANDBOX_RELEASE(m_listenerPortal1);
}

int ActorInPortal::getPortalTransferTime()const 
{
	return 0;
}
int ActorInPortal::getPortalCooldown()const
{
	return 900;
}

void ActorInPortal::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void ActorInPortal::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void ActorInPortal::OnTick()
{
	if (!GetOwner()) return ;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return ;
	assert(m_owner != nullptr);
	World* pworld = m_owner->getWorld();
	if(!pworld)
		return;
	if(pworld->onServer())
	{
		int transfertime = getPortalTransferTime();
		if(m_InPortal)
		{
			m_InPortalTime++;
			auto RidComp = m_owner->getRiddenComponent();
			if( !(RidComp && RidComp->isRiding()) && m_InPortalTime>=transfertime)
			{
				m_InPortalTime = transfertime;
				m_InPortalCooldown = getPortalCooldown();

				m_owner->teleportMap(pworld->getCurMapID()==0 ? 1 : 0);
			}

			m_InPortal = false;
		}
		else
		{
			if(m_InPortalTime > 0) m_InPortalTime -= 4;
			if(m_InPortalTime < 0) m_InPortalTime = 0;
		}

		if(m_InPortalCooldown > 0) m_InPortalCooldown--;
	}
}

void ActorInPortal::setInPortal()
{
	if(m_InPortalCooldown > 0)
	{
		m_InPortalCooldown = getPortalCooldown();
	}
	else
	{	
		m_InPortal = true;
	}
}

bool ActorInPortal::isInPortal()
{
	return m_InPortal;
}	



IMPLEMENT_COMPONENTCLASS(PlayerInPortal)

PlayerInPortal::PlayerInPortal():ActorInPortal()
{

}

void PlayerInPortal::OnTick()
{
	if (!GetOwner()) return ;
	ClientPlayer* m_player = dynamic_cast<ClientPlayer*>(GetOwner());
	assert(m_player != nullptr);
	if (!m_player) return ;
	
	int inportal_time = m_InPortalTime;

	ActorInPortal::OnTick();

	if(!m_player->getWorld())
		return;

	if (m_player->getWorld()->isRemoteMode()){
		return;
	}
	if (inportal_time <= 0 && m_InPortalTime > 0)
	{
		auto effectComponent = m_player->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect(BODYFX_PORTAL);
		}
		auto sound = m_player->getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.portal.portal", 1.0f, 1.0f);
		}
	}
	else if (inportal_time > 0 && m_InPortalTime <= 0)
	{
		auto effectComponent = m_player->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect(BODYFX_PORTAL);
		}
		m_player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, m_player->getWorld()->getCurMapID() == 0 ? 94 : 93);
	}		
}

int PlayerInPortal::getPortalTransferTime()const  
{
	return 5;
}

int PlayerInPortal::getPortalCooldown()const  
{
	return 10;
}