#pragma once

#include "BlockEnvEffectBase.h"

class AddBufEffect : public BlockEnvEffectBase
{
public:
	AddBufEffect(int buffid, int bufflv, int customticks = -1, int buffInstanceId = 0, long long objid = 0) 
		:m_buffid(buffid),m_bufflv(bufflv),m_customticks(customticks),m_buffInstanceId(buffInstanceId),m_objid(objid)
	{

	}

	virtual void executeEffect(ClientActor* pActor) override;
private:
	int m_buffid;
	int m_bufflv;
	int m_customticks;
	int m_buffInstanceId;
	long long m_objid;
};