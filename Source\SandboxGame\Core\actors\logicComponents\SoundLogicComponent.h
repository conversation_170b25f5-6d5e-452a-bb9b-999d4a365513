
#ifndef _SOUNDLOGIC_COMPONENT_H_
#define _SOUNDLOGIC_COMPONENT_H_

#include "ActorComponent_Base.h"

// 用于播放游泳和入水音效
enum class InWaterState
{
	NoInWater = 0,	// 不在水中
	HalfInWater = 1,	// 一半在水中，一半不在
	AllInWater = 2,		// 完全在水中
};

class ClientActor;
class SoundLogicComponent : public ActorComponentBase
{
	DECLARE_COMPONENTCLASS(SoundLogicComponent)
public:
	SoundLogicComponent();
	~SoundLogicComponent();

	virtual void OnEnterOwner(::MNSandbox::SandboxNode* owner) override;
	virtual void OnLeaveOwner(::MNSandbox::SandboxNode* owner) override;
	virtual void OnTick() override;

private:
	void UpdateInWaterState();//只在角色和水生生物身上有
	void PlayEnterLeaveWaterSound(InWaterState eState);//只在角色和水生生物身上有
	void PlaySwimSound(InWaterState eState);
	bool IsBoundsAllInLiquid();

private:
	InWaterState m_InWaterState;
	ClientActor* m_pActor;
};
#endif