#ifndef __FIRE_BURN_COMPONENT_H__
#define __FIRE_BURN_COMPONENT_H__

#include <map>
#include "ActorTypes.h"
#include "ActorComponent_Base.h"

namespace Rainbow {
	class ISound;
}

class ClientActor;
class FireBurnComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(FireBurnComponent)
	FireBurnComponent();
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	//tolua_begin
	virtual void setFire(int buffid, int bufflv, int ticks = -1, long long fromObjid = 0);
	//tolua_end
	void setFire_Base(int buffid, int bufflv, int ticks = -1, long long fromObjid = 0);

protected:

}; //tolua_exports
#endif