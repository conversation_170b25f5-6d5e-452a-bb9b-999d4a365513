#pragma once
#include "actors/miniActor/ActorBall.h"
#include <map>

// 空投伞
class ActorAirDropParachute : public ActorBall {

    DECLARE_SCENEOBJECTCLASS(ActorAirDropParachute)
public:
    ActorAirDropParachute();
    virtual ~ActorAirDropParachute() = default;
    void setDamageHP(int damageHP);
    // 重写碰撞处理方法
    virtual void onCollideWithPlayer(ClientActor* player) override;

    // 创建实例的静态方法
    static ActorAirDropParachute* create(World* pworld, int x, int y, int z, float vx = 0, float vy = 0, float vz = 0);

private:
    // 使用映射表记录每个玩家的最后碰撞时间
    // 键为玩家ID，值为最后碰撞时间
    std::map<long long, unsigned int> m_playerCollideTimers;

    // 碰撞冷却时间（毫秒）
    static const unsigned int COLLIDE_COOLDOWN = 3000;

    // 空投伞的伤害
    int m_damageHP;
};