#ifndef __COMBAT_CHECK_EXTEND__
#define __COMBAT_CHECK_EXTEND__
#include <vector>
#include "boundaryGeometry/BoundaryHolder.h"
#include "SandboxGame.h"
#include "Math/Vector3f.h"

class IClientActor;

class EXPORT_SANDBOXGAME CombatCheckExtend
{
public:
	//攻击范围内是否有敌人
	static bool actorHaveEnemyInAttackRange(IClientActor* actor, const Rainbow::Vector4f& boxInfo);

	//获取攻击的mob, 结果储存在mobs里, 会先将mobs clear. false 表示获取失败.
	static bool getActorAttackMobs(IClientActor* actor, std::vector<IClientActor*>& mobs, const Rainbow::Vector4f& boxInfo);
	//获取攻击范围内能打到的人.也就是会做射线检测.会先将mobs clear. false 表示获取失败.
	static bool actorHaveCanAttakEnemy(IClientActor* actor, std::vector<IClientActor*>& mobs, const Rainbow::Vector4f& boxInfo);
	//获取生物目前的攻击盒,如果是非怪物和玩家,返回的包围盒左下点为(0, 0, 0).范围为0. boxInfo: xyz:包围盒长宽高, w:包围盒偏移
	static BoundaryGeometryHolder getActorCurAttackBox(IClientActor* actor, const Rainbow::Vector4f& boxInfo);
	//获取当前actor的包围盒
	static BoundaryGeometryHolder getActorBox(IClientActor* actor);
	static std::vector<BoundaryGeometryHolder> getMultiActorBox(IClientActor* actor);

	/*static bool boxCheckCollide(const WrapBoxBody& body, World* world);
	static bool boxCollideActors(const WrapBoxBody& body, World* world, std::vector<IClientActor*>& mobs);*/

	//检测actor与一个box的碰撞.
	static bool checkActorBoxCollide(IClientActor* actor1, const BoundaryGeometryHolder& box2);

	//actor1 是否能看到 actor2, 也就是之间是否有阻挡
	static bool actorCanSeeActor(IClientActor* actor1, IClientActor* actor2);

	//根据holder来检测周围生物
	//range < 0 时使用holder自己的范围
	static bool getNeighborMobs(World* pworld, const BoundaryGeometryHolder& holder, std::vector<IClientActor*>& mobs, IClientActor* actor = NULL, int range = -1);

	//根据holder来拿取周围方块
	//range < 0 时使用holder自己的范围
	static bool getNeighborBlocks(World* pworld, const BoundaryGeometryHolder& holder, std::list<WCoord>& blocks, int range = -1);
#ifdef ENABLE_PLAYER_CMD_COMMAND
	static void drawActorBox(IClientActor* actor, const Rainbow::Vector4f& boxInfo = Rainbow::Vector4f(0, 0, 0, 0));
#endif

#ifdef ENABLE_PLAYER_CMD_COMMAND
	static void drawTLBoxActorBox(IClientActor* actor,const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo);
	static void drawTLBox(World* pworld, const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo);
#endif
	//获取固定点的盒子
	static BoundaryGeometryHolder getPosBox(const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo);
	static BoundaryGeometryHolder getDrawPosBox(const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo);
	//当前这两个盒子只会根据人物身子旋转，不会根据人物头部上下旋转
	static BoundaryGeometryHolder getActorBox(IClientActor* actor, const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo);
	static BoundaryGeometryHolder getDrawActorBox(IClientActor* actor, const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo);
	//获取盒子里面生物
	//pos 世界坐标
	//static bool getBoxMobs(World* pworld,const Rainbow::Vector3f& pos, std::vector<ClientActor*>& mobs, const Rainbow::Vector3f& boxInfo);
	//生物盒子获取生物
	//pos相对生物位置
	//static bool getActorBoxMobs(ClientActor* actor, const Rainbow::Vector3f& pos, std::vector<ClientActor*>& mobs, const Rainbow::Vector3f& boxInfo);
	//获取固定盒子里面方块
	//pos 世界坐标
	//static bool getBoxBlocks(World* pworld, const Rainbow::Vector3f& pos, std::list<WCoord>& blocks, const Rainbow::Vector3f& boxInfo);
	//生物获取盒子里面方块
	//pos相对生物位置
	//static bool getActorBoxBlocks(ClientActor* actor, const Rainbow::Vector3f& pos, std::list<WCoord>& blocks, const Rainbow::Vector3f& boxInfo);


	static  int  GetCross(const WCoord& p1, const WCoord& p2, int x, int z)
	{
		return (p2.x - p1.x) * (z - p1.z) - (x - p1.x) * (p2.z - p1.z);
	}

	static bool IsInRect(int x, int z, const std::vector< WCoord>& rect);

private:
	//static bool checkActorBoxCollideAABB(IClientActor* actor1, const WrapBoxBody& box2);
	//static bool checkActorBoxCollideOBB(IClientActor* actor1, const WrapBoxBody& box2);
};

#endif