#include "BlockEnvEffectsComponent_Actor.h"

//#include "BlockEnvEffectBase.h"
//#include "BlockRangeCheckBase.h"
#include "BlockEnv_Constants.h"

#include "PointCheck.h"
#include "BoundCoordCheck.h"

#include "ForbidJumpEffect.h"

#include "SandboxIdDef.h"//BLOCK_THICKET
#include "OgreShared.h"//OGRE_DELETE

IMPLEMENT_COMPONENTCLASS(BlockEnvEffectsComponent_Actor)

BlockEnvEffectsComponent_Actor::BlockEnvEffectsComponent_Actor()
{
	RegisterRange(R_ON_THICKET, SANDBOX_NEW(PointCheck,1, BLOCK_THICKET));
	RegisterEffect<ForbidJumpEffect>(FORBID_JUMP);
	Add(R_ON_THICKET, FORBID_JUMP, ENV_THICKET);

	//RegisterRange(R_IS_INHURT, new PointCheck(3, BL<PERSON><PERSON>_BONFIRE, BLOCK_CACTUS, BLOCK_THICKET));
	//RegisterRange(R_IN_LAVA, new BoundCoordCheck(10, 40, 2, BLOCK_STILL_LAVA, BLOCK_FLOW_LAVA));
	//RegisterRange(R_IN_HONEY, new BoundCoordCheck(10, 40, 2, BLOCK_STILL_HONEY, BLOCK_FLOW_HONEY));
	//RegisterRange(R_IN_POISON, new BoundCoordCheck(10, 40, 1, BLOCK_POISON));
	//RegisterRange("water",   new PointPosCheck(BLOCK_THICKET));
}