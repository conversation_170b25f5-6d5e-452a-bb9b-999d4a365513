
#ifndef __RIVER_LANTERN_LOCOMOTION_H__
#define __RIVER_LANTERN_LOCOMOTION_H__

#include "ActorLocoMotion.h"
class RiverLanternLocomotion : public ActorLocoMotion
{
public:
	DECLARE_COMPONENTCLASS(RiverLanternLocomotion)

	RiverLanternLocomotion() :  boatYaw(0), boatPitch(0)
	{
		boatPosRotationIncrements = 0;
		speedMultiplier = 5.0f;
	}

	virtual ~RiverLanternLocomotion()
	{

	}
	virtual void initPosition(const WCoord& pos) final;
	virtual void tick();

public:
	int boatPosRotationIncrements;
	WCoord boatPos;
	float boatYaw;
	float boatPitch;
	float speedMultiplier;
};

#endif