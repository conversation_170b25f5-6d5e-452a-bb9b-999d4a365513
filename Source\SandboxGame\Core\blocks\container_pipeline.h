#ifndef __CONTAINER_PIPELINE_H__
#define __CONTAINER_PIPELINE_H__

#include "container_electric_elemet.h"

class ContainerPipeline: public ContainerElectricElement
{
public:
	ContainerPipeline(): m_transferObjId(-1), m_Dir(0), m_transferDir(0), m_connectDir(0), m_tricks(-1), m_curTranDir(-1), m_itemId(0), m_checkStep(20), m_checkCount(0)
	{
		//SetBackPackGrid(m_Grids, 0, 0);
		m_NeedTick = true;
	}

	ContainerPipeline(const WCoord& blockpos, int baseindex = 0) 
		:ContainerElectricElement(blockpos, baseindex), m_transferObjId(-1), m_Dir(0), m_transferDir(0), m_connectDir(0), m_tricks(-1), m_curTranDir(-1), m_itemId(0), m_checkStep(20), m_checkCount(0)
	{
		//SetBackPackGrid(m_Grids, 0, 0);
		m_NeedTick = true;
	}

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual flatbuffers::Offset<FBSave::ContainerPiplineSave> savePipeLine(SAVE_BUFFER_BUILDER& builder);
	virtual bool load(const void* srcdata) override;
	virtual int getObjType() const override
	{
		return OBJ_TYPE_BOX;
	}
	virtual FBSave::ContainerUnion getUnionType() override
	{
		return FBSave::ContainerUnion_ContainerPiplineSave;
	}
	virtual bool canBeginTransfer();
	virtual void beginTransfer(long long objid);
	virtual bool updateItemTransferMotion(WCoord& postion, IClientActor* actor);
	virtual void endTransfer();
	virtual void updateTick() override;
	virtual void enterWorld(World* pworld) override;
	virtual void setDirConnect(int dir, bool connected);
	virtual bool isDirConnect(int dir);
	virtual void setDirHasTansfered(int dir, bool transfered);
	virtual bool isDirHasTransfered(int dir);
	virtual bool isShouldTransfered(int dir);
	virtual void checkDirConnect();
	virtual bool isTransfering();
	virtual void checkTransferDir();
	virtual int onInsertItem(const BackPackGrid& grid, int num, int params) override;
	virtual void clear() override;
	void checkCurTransferDir();
protected:
	void initTransferDir();
private:
	bool outputItems();
public:
	//连接的方向
	unsigned short int m_connectDir;
	//运输过的方向
	unsigned short int m_transferDir;
	//正在运输的方向
	short int m_curTranDir;
	//自己的方向
	unsigned short int m_Dir;
	//行走的trick数
	short int m_tricks;
	long long m_transferObjId;
	int m_itemId;
	int m_checkStep;
	int m_checkCount;
};

#endif 