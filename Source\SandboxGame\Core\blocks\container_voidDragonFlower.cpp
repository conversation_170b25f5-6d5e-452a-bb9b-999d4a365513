#include "container_voidDragonFlower.h"
#include "SandboxGameStep.h"
#include "world.h"
#include "ClientActor.h"
#include "SandBoxManager.h"
#include "GameNetManager.h"
#include "LuaInterfaceProxy.h"
#include "EffectManager.h"
#include "ActorManager.h"
//1s更新一次map
#define UPDATE_MAP_TICK 1000
//2s攻击一次
#define ATTACK_TICK 2000
//4s刷新一次map容器
#define REFRESH_MAP 4000

DragonFlowerContainer::DragonFlowerContainer()
{
	m_checkTick = 0;
	m_attackTick = 0;
	m_clearTick = 0;
} 

DragonFlowerContainer::DragonFlowerContainer(WCoord BlockPos, int dir):SimpleModleBaseContainer(BlockPos, 200397, dir)
{
	m_checkTick = 0;
	m_attackTick = 0;
	m_clearTick = 0;
	m_NeedTick = true;
}

void DragonFlowerContainer::updateTick()
{
	if (!m_World)
	{
		return;
	}
	SimpleModleBaseContainer::updateTick();
	int ticks = (MNSandbox::g_TickElapse) * 1000;
	if (m_World->isRemoteMode())
	{
		m_checkTick += ticks;
		if (m_checkTick > UPDATE_MAP_TICK)
		{
			playAnim(100, 1);
			m_checkTick = 0;
		}
		return;
	}
	m_checkTick += ticks;
	m_attackTick += ticks;
	m_clearTick += ticks;
	std::vector<IClientActor*> actors;
	ClientActor* attackActor = NULL;
	std::vector<std::pair<WORLD_ID, uint>> newMapActors;
	bool needCheck = m_checkTick > UPDATE_MAP_TICK;
	bool needAttack = m_attackTick > ATTACK_TICK;
	bool needClear = m_clearTick > REFRESH_MAP;
	if (needAttack || needClear)
	{
		needCheck = true;
	}
	if (needCheck)
	{
		playAnim(100, 1);
		auto mgr = dynamic_cast<ActorManager*>(m_World->getActorMgr());
		if (mgr)
		{
			actors.reserve(5);
			mgr->selectNearActors(BlockCenterCoord(m_BlockPos), 3 * BLOCK_SIZE, [](IClientActor* actor) {
				if (!actor)
				{
					return false;
				}
				return actor->getObjType() != OBJ_TYPE_VORTEX;
			}, [&actors](IClientActor* actor) {
				actors.emplace_back(actor);
			});
		}
		newMapActors.reserve(actors.size());
		int objid = 0;
		for (auto& p : actors)
		{
			objid = p->getObjId();
			auto f = m_actors.find(objid);
			if (f != m_actors.end())
			{
				f->second += m_checkTick;
				if (needAttack && f->second > 2000 && attackActor == NULL)
				{
					f->second = 0; 
					attackActor = p->GetActor();
				}
				newMapActors.emplace_back(std::pair<WORLD_ID, int>({ objid,f->second}));
			}
			else
			{
				m_actors.insert({ objid, 0 });
				newMapActors.emplace_back(std::pair<WORLD_ID, int>({ objid,0 }));
			}
		}
		m_checkTick = 0;
	}
	if (needAttack)
	{
		m_attackTick = 0;
		if (attackActor)
		{
			OneAttackData atkdata;
			//memset(&atkdata, 0, sizeof(atkdata));
			atkdata.damage_armor = true;
			// 新伤害计算系统 code-by:liya
			if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
			{
				atkdata.atkTypeNew = (1 << ATTACK_PUNCH);
				atkdata.atkPointsNew[ATTACK_PUNCH] = 10;
				atkdata.knockback = 1;
				atkdata.atkpos.y = 1;
			}
			else
			{
				atkdata.atktype = ATTACK_PUNCH;
				atkdata.atkpoints = 10;
				atkdata.knockback = 1;
				atkdata.atkpos.y = 1;
			}
			atkdata.fromplayer = NULL;
			attackActor->attackedFrom(atkdata, NULL);
			
			//播放攻击动画
			//旋转, 只转x,z平面的
			WCoord actorPosition = attackActor->getPosition();
			WCoord blockPos = BlockCenterCoord(m_BlockPos);
			actorPosition -= blockPos;
			Rainbow::Vector2f v(actorPosition.x, actorPosition.z);
			v.NormalizeSafe();
			float angle = Rainbow::ACosToAngle(Rainbow::DotProduct(v, Rainbow::Vector2f(0, -1)));
			auto crossV = Rainbow::CrossProduct(Rainbow::Vector3f(0, 0, -1), Rainbow::Vector3f(v.x, 0, v.y));
			if (crossV.y < 0)
			{
				angle = -angle;
			}
			playAttackAnim(angle);
			//发给客机
			jsonxx::Object object;
			object << "x" << m_BlockPos.x;
			object << "y" << m_BlockPos.y;
			object << "z" << m_BlockPos.z;
			object << "angle" << angle;
			int size = 0;
			unsigned char* p = NULL;
			object.saveBinary(p, size);
			SandBoxManager::getSingletonPtr()->sendBroadCast((char*)("VOID_DRAGON_FLOWER_ATTACK"), p, size);
			free(p);
		}
	}
	if (needClear)
	{
		m_clearTick = 0;
		m_actors.clear();
		for (auto& p : newMapActors)
		{
			m_actors[p.first] = p.second;
		}
	}
}

void DragonFlowerContainer::enterWorld(World* pworld)
{
	SimpleModleBaseContainer::enterWorld(pworld);
	registerUpdateTick();
	playAnim(100);
}

void DragonFlowerContainer::playAttackAnim(int angle)
{
	if (!m_World || !m_World->getEffectMgr())
	{
		return;
	}
	setRotation(Rainbow::Vector3f(0, angle, 0));
	playAnim(103, 1);
	m_World->getEffectMgr()->playSound(BlockCenterCoord(m_BlockPos), "blocks.dragongrass_on", 1, 1, 1, -1);
}

bool DragonFlowerContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerDragonFlower *>(srcdata);
	if (!SimpleModleBaseContainer::load(src->basedata()))
	{
		return false;
	}
	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> DragonFlowerContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveSimpleModel(builder);

	auto actor = FBSave::CreateContainerDragonFlower(builder, basedata);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerDragonFlower, actor.Union());
}
