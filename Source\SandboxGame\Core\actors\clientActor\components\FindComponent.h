#ifndef __FIND_COMPONENT_H__
#define __FIND_COMPONENT_H__
#include "OgreWCoord.h"
#include "world_types.h"
#include "ActorComponent_Base.h"
//#include "ClientActor.h"
#include <vector>

class ClientActor;
class FindComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(FindComponent)
	FindComponent();
	~FindComponent();
	//tolua_begin
	bool findFarBlock(WCoord &blockpos, int blockid, float findRange);
	bool findNearestBlockFast(WCoord &blockpos, int blockid, int blockRange);
	bool findNearestBlock(int &x, int &y, int &z, int blockid, int blockRange);
	//tolua_end
	bool findNearestBlock(WCoord &blockpos, int blockid, int blockRange, bool(*valid)(ClientActor*, WCoord &, int, std::string) = NULL, std::string extend = "", bool randomsort = false);
	bool findNearestBlockList(WCoord &blockpos, std::set<int> *blocklist, int blockRange, bool(*valid)(ClientActor*, WCoord &, int, std::string) = NULL, std::string extend = "", bool randomsort = false);
	bool findNearestActor(WORLD_ID &actorid, int findRange, int objtype, int id = 0, int actorRange = Rainbow::MAX_INT, bool(*valid)(ClientActor*, ClientActor*, const std::string&) = NULL, const std::string& extend = "", bool randomsort = false);
	bool findNearestActorWithNoType(WORLD_ID& actorid, int findRange, int id = 0, int actorRange = Rainbow::MAX_INT, bool(*valid)(ClientActor*, ClientActor*, const std::string&) = NULL, const std::string& extend = "", bool randomsort = false);
	bool findNearActorsWithNoType(std::vector<ClientActor*>& actorList, int findRange, int id = 0, int actorRange = Rainbow::MAX_INT, bool(*valid)(ClientActor*, ClientActor*, const std::string&) = NULL, const std::string& extend = "", bool randomsort = false);

protected:
	bool __new_findNearestActor(WORLD_ID& actorid, int findRange, int objtype, int id = 0, int actorRange = Rainbow::MAX_INT, bool(*valid)(ClientActor*, ClientActor*, const std::string&) = NULL, const std::string& extend = "", bool randomsort = false);
	bool __deprecated_findNearestActor(WORLD_ID& actorid, int findRange, int objtype, int id = 0, int actorRange = Rainbow::MAX_INT, bool(*valid)(ClientActor*, ClientActor*, const std::string&) = NULL, const std::string& extend = "", bool randomsort = false);
	bool __new_findNearestActorWithNoType(WORLD_ID& actorid, int findRange, int id = 0, int actorRange = Rainbow::MAX_INT, bool(*valid)(ClientActor*, ClientActor*, const std::string&) = NULL, const std::string& extend = "", bool randomsort = false);
	bool __new_findNearActorsWithNoType(std::vector<ClientActor*>& actorList, int findRange, int id = 0, int actorRange = Rainbow::MAX_INT, bool(*valid)(ClientActor*, ClientActor*, const std::string&) = NULL, const std::string& extend = "", bool randomsort = false);
}; //tolua_exports
#endif