	
#ifndef __BACKPACK_H__
#define __BACKPACK_H__

#include "container_backpack.h"
#include "SandboxGame.h"
#include "IBackpack.h"

class ClientPlayer;
class WorldContainer;
struct GameInitItem;
struct CraftingDef;
class EXPORT_SANDBOXGAME BackPack;
class BackPack : public IBackPack { //tolua_exports
public:
	//tolua_begin
	BackPack(ClientPlayer *player);
	~BackPack();

	void validateItems();//检查物品数目是否超过范围

	//soc 拾取东西进背包逻辑
	int addItemWithPickUp_bySocGridCopyData(const GridCopyData& gridcopydata,bool isshowtips = true);

	int addItemWithPickUp_bySocGridCopyData(int resid, int num);

	int addItemWithPickUp_byGridCopyData(const GridCopyData& gridcopydata);//捡到背包,含符文属性信息 code by:tanzhenyu
	////tolua_end
	
	bool  isExtBackPack(int resid);//是否是扩展背包
	int getPickUpIndex(const GridCopyData& gridcopydata,int& sec,int& thr); //获取到拾取的Index如果小于0不需要处理  返回为第一步处理的index sec 第二步处理的 thr第三步需要处理index

	//int addItemWithPickUp(int resid, int num, int duration, int toughness, int enchantnum, const int enchants[], void *userdata=0, const char *userdata_str="");
	////tolua_begin
	int lootItem(int fromIndex, int num); //试图捡到背包 返回剩余数量,拿不下了
	void sortPack(int base_index, bool isChange=true); // 请求排序背包
	void sortStorageBox(); // 请求排序箱子

	bool doCrafting(int craftingid, int *remainNum = NULL, int num = 1);					//移动端的制作物品处理
	bool doPlayerPreDeductCraftMaterials(int craftingid, int num = 1);				    	//移动端的制作物品处理
	bool doPlayerCraftFromWithhold(int craftID, int* remainNum = NULL, int num = 1);
	bool doPlayerReturnPreDeductedMaterialsByCraft(int craftID, int resultNu);

	void clearEnchant(int index); //仅backpack内部使用

	int addItem_byGameInitItem(GameInitItem* item);//含符文属性信息 code by:tanzhenyu
	int addItem_byGridCopyData(const GridCopyData& gridcopydata, int priorityType);//含符文属性信息 code by:tanzhenyu
	int addItem_byGrid(int resid, int num, const BackPackGrid& grid, int priorityType);
	////tolua_end
	//int addItem(int resid, int num, int priorityType, int enchantnum, const int enchants[], void *userdata = 0, const char *userdata_str = "");				//priorityType 1快捷栏优先于背包 2背包优先于快捷栏
	//int addItemWithTunestone(int resid, int num, int priorityType, int tunestonenum, const int tunestones[], void *userdata = 0, const char *userdata_str = "");				//priorityType 1快捷栏优先于背包 2背包优先于快捷栏
	////tolua_begin
	void clearRune(int index);//清除符文信息 code by:tanzhenyu
	void discardItem(int index, int num); // 试图丢弃物品
	bool enchant(int gridindex, int enchantid);
	bool addRune(int gridindex, const GridRuneItemData &one);//添加符文code by:tanzhenyu
	bool replaceRune(int gridindex, const GridRuneItemData &one, int runeIndex);//替换符文code by:tanzhenyu
	int getRuneNum(int index);//当前格子的物品 镶嵌的符文数量 code by:tanzhenyu
	const GridRuneItemData* getRuneItem(int gridindex, int runeIndex);//获取当前格子的物品指定下标的的符文数据 code by:tanzhenyu
	int getGridItem(int index); // 获取背包内物品的资源ID
	int getGridNum(int index);
	int getGridDuration(int index);
	int getGridMaxDuration(int index);
	int getGridEnchantNum(int index);
	int getGridEnchantId(int index, int idIndex);
	unsigned int getGridEnchantColor(int index);
	int getGridToolType(int index); //如果不是tool, 返回-1
	int getGridEnough(int index);
	int getGridUserdata(int index);
	const char* getGridUserdataStr(int index);
	const char* getGridSidStr(int index); //获取格子的服务器id 家园版本 非无限方块 才有值 其他为""
	std::string getModItemName(int index);
	std::string getModItemDesc(int index);
	std::string getModExtradata(int index);
	std::string getGridInfo(int index);
	jsonxx::Object getGridJsonxxInfo(int index);
	bool setGridInfo(int index,const char * info);
	bool setGridJsonxxInfo(int index,  jsonxx::Object*  info);
	int getGridSortId(int index);	
	int getGridCount(int baseIndex);
	const char *getGridItemName(int index);
	int getGridMaxStack(int index);
	
	void setItem(int resid, int grid_index, int num = 1, const char *sid_str = "");					//创造模式背包和物品栏交换的时候用这个接口
	void addItemToEquip(int resid, int grid_index, int num = 1);		//添加道具到装备栏
	void removeItemFromEquip(int grid_index, int num=1);		//销毁道具
	void setItemWithoutLimit(int resid, int grid_index, int num, const char *userdata_str, const char *sid_str = "");  //非创造模式也可以用

	void doRepair(int durable);

	bool showRecipeProduct();
	int updateProductContainer(int base_index, int level = 1, bool updateAll = false, int sortFunc = 0);								//更新背包物品中可制作的物品(材料不足也算)
	int updateCookBookProductContainer(long long uin, int base_index, bool updateAll = false, int sortFunc = 0);								//更新背包物品中可制作食谱的物品(材料不足也算)
	//tolua_end
	void setBackPackData(std::map<int, int>& data);
	void setBackPackDataForCraft(std::map<int, int>& data, std::string* strHex = nullptr);

	bool addCraftProduct(const CraftingDef *def, PackContainer *productContainer, int base_index, int level, bool updateAll = false, std::map<int, int>* backpackData = nullptr, std::string* strHex = nullptr);
	bool addCookbookProduct(const CraftingDef* def, PackContainer* productContainer, int base_index, bool updateAll = false);

	//tolua_begin
	int updateCraftContainer(int resultID, int base_index, int enough, int makeNum = 1);		//更新合成栏
	int updateCookBookContainer(int resultID, int base_index, int enough, int makeNum = 1);
	//IBackPack
	virtual int addItem(int resid, int num, int priorityType=1) override;				//priorityType 2快捷栏优先于背包 1背包优先于快捷栏
	//void replaceItem(int index, int resid, int num, int durable, int enchantNum = 0, int *enchantIds = 0, void *userdata = 0, const char *userdata_str = "");
	void replaceItem_byGridCopyData(const GridCopyData& gridcopydata, int index);//含符文属性信息 code by:tanzhenyu
	void replaceItemByNum(int index, int resid, int num);
	void removeItem(int grid, int num);  //grid>=SHOTCUT_START_INDEX表示在快捷栏
	//从背包中减去num*itemid，包括快捷栏和背包
	int removeItemInNormalPack(int itemid, int num);
	void clearPackByType(int index, const std::string &reason = "");
	void clearPack(const std::string &reason = "");
	bool shiftMoveItem(int fromgrid, int gridtype);
	//IBackPack
	virtual bool moveItem(int fromindex, int toindex, int num) override;
	void swapItem(int fromIndex, int toIndex); //试图交换物品
	bool canPutItem(int index);


	void setCreateModeShortCut();

	bool enoughGridForItem(int itemid, int num);
	int enoughGridForItemMaxNum(int itemid, int num); //道具能提取到背包的最大数量
	int getShorCutEmptyGridNum();
	//IBackpack
	virtual BackPackGrid *index2Grid(int index) override;
	//获取背包和快捷栏中item总共的数量
	virtual int getItemCountInNormalPack(int itemid);
	void searchNormalPack(GridVisitor* visitor);
	//获取背包和快捷栏中相同group item总共的数量
	int getSameGroupItemCountInNormalPack(int itemid);
	//背包刷新
	void afterChangeGrid(int gridindex);
	//判断家园编辑模式
	bool isHomeLandGameMakerMode();
	//获取快捷栏索引
	int getShortcutStartIndex();
	//在新编辑模式下，暂时使用老的快捷栏的界面和交互
	void setTmpShortcutMode(bool isOpen)
	{
		m_tmpShortcutMode = isOpen;
	};
	bool isTmpShortcutMode()
	{
		return m_tmpShortcutMode;
	};
	//新编辑模式下40 其余都是8
	int getShortcutGridCount();
	int getEmptyBagIndex();
	int getEmptyShortcutIndex();
	
	int tryAddItem_byGrid(int resid, int num, BackPackGrid* data);//添加item到背包 含符文信息 code by:tanzhenyu
	virtual int tryAddItem_byGridCopyData(const GridCopyData& gridcopydata);//添加item到背包 含符文信息 code by:tanzhenyu
	std::string getExtBackPackInfo(); //获取扩展背包数据
	void loadExtBackPackInfo(const std::string& info);//加载扩展背包数据

	////tolua_end
	//int tryAddItem(int resid, int num, int durable, int toughness, int enchantnum, int enchants[], void *userdata = 0, const char *userdata_str = "");
	////tolua_begin
	bool moveItem(BackPackGrid * srcgrid, int &num, int toindex, int fromindex);
	void placeItem(int fromgrid, int togrid, int num=1);							//放置物品到相应的格子	
	virtual BaseContainer *getContainer(int index) override;
	int takeItemFrom(int gindex, int num, bool delsrc = false);
	int addStorageItem(int index, int num, int openContainerBase=STORAGE_START_INDEX);
	void mendItem(int index, int mendAmount);

	int findItemInNormalPack(int itemid); //如果存在, 返回index, 否则返回-1

	void mergePack(int base_index, bool isChange=true);
	bool mergeItem(int fromIndex, int toIndex);
	//IBackPack
	virtual void attachContainer(BaseContainer *container) override;
	virtual void detachContainer(BaseContainer *container) override;

	//触发器事件:箱子中有道具被放入或取出
	void ItemChangeForTrigger(bool isPutIn, int girdindex, int itemid, int itemnum);
	void ItemChangeForTrigger(bool isPutIn, WCoord& blockpos, int blockid, int itemid, int itemnum);

	//将新编辑模式的当前页同步到玩法模式的快捷栏
	void copyCurShotcutToOldEdit(int fromIndex, int toIndex);
	//将玩法模式的快捷栏同步到新编辑模式的当前页
	void copyOldEditToCurShotcut(int fromIndex, int toIndex);
	//将资源背包的n(最多8个)个道具放入新编辑模式的当前页，从左往右挤，右边的可以被挤掉（超出当前页直接删除）
	void insertItemsToCurShotcutEdit(int fromIndex, int toIndex, std::vector<int> items);

	//判断快捷栏item的数量
	int numberOfItemInShortcut(int itemid);
	//获取特殊类型道具的特殊名字 code-by:lizb
	static std::string getSpecialItemName(int itemId, const char* userdata_str);
	//tolua_end
	
	static bool isBackpackIndex(int index);
	static bool isBackpackIndexExt(int index); //在isBackpackIndex的基础上扩展 EQUIP_START_INDEX 
	PackContainer* getPack(int index);

protected:
	ClientPlayer *mPlayer;
	BaseContainer *m_Containers[MAX_PACKINDEX_TYPE];

private:
	bool validServerBackPackLog();

	bool insertItemToCurShotcutEdit(int itemId, int start, std::vector<int>& shortcuts);
	bool m_tmpShortcutMode;

}; //tolua_exports


#endif