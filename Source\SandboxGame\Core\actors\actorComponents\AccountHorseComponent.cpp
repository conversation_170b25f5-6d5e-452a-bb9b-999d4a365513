#include "AccountHorseComponent.h"

#include "ClientPlayer.h"
#include "world.h"

#include "WorldManager.h"
#include "GameNetManager.h"
#include "ClientActorManager.h"
//#include "ActorDragon.h"
#include "ActorDragonMount.h"
#include "ActorMoonMount.h"
#include "ActorDouDuMount.h"
#include "ActorPumpkinHorse.h"
#include "ActorHorse.h"
#include "ActorShapeShiftHorse.h"
#include "ActorPackHorse.h"
#include "ActorStorageBoxHorse.h"
#include "BlockMaterialBase.h"
#include "ActorAttrib.h"
#include "ActorLocoMotion.h"
#include "EffectManager.h"

IMPLEMENT_COMPONENTCLASS(AccountHorseComponent)

AccountHorseComponent::AccountHorseComponent(): m_CurAccountHorse(0)
{
	
}

void AccountHorseComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void AccountHorseComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void AccountHorseComponent::OnTick()
{
	size_t count = 0;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return;
	for (size_t i = 0; i < m_AccountHorses.size(); i++)
	{
		AccountHorseInfo &info = m_AccountHorses[i];
		if (info.liveticks < 0)
		{
			info.liveticks++;
			if (info.liveticks >= 0 || (m_owner->getAge() % 20) == 0)
				notifyAccountHorse2Self(info.horseid);
		}
		else
		{
			count++;
		}
	}
	if (count == m_AccountHorses.size())
	{
		UnBindOnTick();
	}
}

extern WCoord GetNearMobSpawnPos(ClientPlayer *player);
void AccountHorseComponent::summonAccountHorse(int horseid)
{
	if (!GetOwner()) return ;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;

	World* pWorld = m_owner->getWorld();

	if (getAccountHorseLiveAge(horseid) < 0) return;

	if (m_CurAccountHorse != 0)
	{
		ClientActor *actor = dynamic_cast<ActorManager*>(pWorld->getActorMgr())->findActorByWID(m_CurAccountHorse);
		if (actor) actor->setNeedClear();
	}

	ActorHorse *horse = NULL;
	if (horseid >= DRAGON_MOUNT_ID_LEVEL_0 && horseid <= DRAGON_MOUNT_ID_LEVEL_2)
	{
		horse = SANDBOX_NEW(ActorDragonMount);
	}
	else if (horseid >= STAR_MOUNT_ID_LEVEL_0 && horseid <= STAR_MOUNT_ID_LEVEL_2)
	{
		horse = SANDBOX_NEW(ActorMoonMount);
	}
	else if ((horseid >= DOUDU_MOUNT_ID_LEVEL_0 && horseid <= MUMU_MOUNT_ID_LEVEL_1) || (horseid >= NINECOLORDEER_MOUNT_ID_LEVEL_0 && horseid <= NINECOLORDEER_MOUNT_ID_LEVEL_1))
	{
		horse = SANDBOX_NEW(ActorDouDuMount);
		if (!horse)
		{
			return;
		}
	}
	else if (::ActorPumpkinHorse::isPumpkinHorse(horseid)) // 如果是南瓜车
	{ 
		horse = SANDBOX_NEW(ActorPumpkinHorse);
	}
	else if (::ActorPackHorse::isPackHorse(horseid)) { // 如果是驯服骆驼
		horse = SANDBOX_NEW(ActorPackHorse);
	}
	else if (::ActorStorageBoxHorse::isStorageBoxHorse(horseid)) { // 储物箱坐骑
		horse = SANDBOX_NEW(ActorStorageBoxHorse);
	}
	else
	{
		horse = SANDBOX_NEW(ActorHorse);
	}

	if (!horse->init(horseid))
	{
		horse->release();
		return;
	}

	size_t i = 0;
	for (; i < m_AccountHorses.size(); i++)
	{
		AccountHorseInfo &obj = m_AccountHorses[i];
		if (obj.horseid == horseid)
		{
			ActorAttrib *attr = horse->getAttrib();
			if (obj.hp > 0) attr->addHP(obj.hp - attr->getHP());
			if (obj.shieldcoolingticks > 0) horse->setShieldCoolingTicks(obj.shieldcoolingticks);
			for (size_t j = 0; j < 3; j++)
			{
				if (obj.equips[j] > 0)
					horse->equipByIndex(j, obj.equips[j]);
			}
			break;
		}
	}

	if (i == m_AccountHorses.size())
	{
		AccountHorseInfo obj;
		memset(&obj, 0, sizeof(obj));
		obj.horseid = horseid;
		obj.hp = horse->getAttrib()->getHP();
		m_AccountHorses.push_back(obj);
	}

	horse->setAccountBind(m_owner->getUin());
	WCoord pos = GetNearMobSpawnPos(m_owner);

	dynamic_cast<ActorManager*>(pWorld->getActorMgr())->spawnActor(horse, pos, GenRandomFloat()*360.0f, 0);
	m_CurAccountHorse = horse->getObjId();
	sendUIDisplayHorseData();
	pWorld->getEffectMgr()->playParticleEffectAsync("particles/acchorse.ent", pos, 40);
}

ClientActor* AccountHorseComponent::summonShapeShiftHorse(int horseid)
{
	if(!GetOwner()) return NULL;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return NULL;
	World* pWorld = m_owner->getWorld();
	int uin = m_owner->getUin();
	if (horseid == 3494) {//牛魔
		m_owner->hideSpecailTryShapeAnim();
	}

	if (m_CurAccountHorse != 0)
	{
		ClientActor *actor = dynamic_cast<ActorManager*>(pWorld->getActorMgr())->findActorByWID(m_CurAccountHorse);
		if (actor) actor->setNeedClear();
	}

	float yaw = 0.0f;
	float pitch = 0.0f;
	LivingLocoMotion* locomove = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	if (locomove)
	{
		yaw = locomove->m_RotateYaw;
		pitch = locomove->m_RotationPitch;
	}
	ActorShapeShiftHorse* horse = SANDBOX_NEW(ActorShapeShiftHorse);
	if (!horse->init(horseid))
	{
		horse->release();
		return NULL;
	}

	horse->initRotateYaw(yaw);
	horse->initRotatePitch(pitch);



	size_t i = 0;
	for (; i < m_AccountHorses.size(); i++)
	{
		AccountHorseInfo &obj = m_AccountHorses[i];
		if (obj.horseid == horseid)
		{
			ActorAttrib *attr = horse->getAttrib();
			if (obj.hp > 0) attr->addHP(obj.hp - attr->getHP());

			for (size_t j = 0; j < 3; j++)
			{
				if (obj.equips[j] > 0)
					horse->equipByIndex(j, obj.equips[j]);
			}
			break;
		}
	}

	if (i == m_AccountHorses.size())
	{
		AccountHorseInfo obj;
		memset(&obj, 0, sizeof(obj));
		obj.horseid = horseid;
		obj.hp = horse->getAttrib()->getHP();
		m_AccountHorses.push_back(obj);
	}

	horse->setAccountBind(uin);
	WCoord pos = m_owner->getPosition();

	//变身的时候碰撞盒可能会扩大
	int ownerSize = m_owner->getLocoMotion()->m_BoundSize;
	int horseSize = horse->getLocoMotion()->m_BoundSize;
	if (horseSize > ownerSize)
	{
		float dffValue = (float)(horseSize - ownerSize) + 10.0f;
		Rainbow::Vector3f dir = m_owner->getLocoMotion()->getLookDir();
		dir.y = 0.0f;
		dir.NormalizeSafe();
		//前后检测
		WCoord posOffset = pos + WCoord(dir * dffValue);
		int blockId = pWorld->getBlockID(CoordDivBlock(posOffset));
		BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockId);
		if (blockDef && (blockDef->MoveCollide == 1 || blockDef->PhyCollide == 1))
		{
			pos -= WCoord(dir * dffValue);
		}
		else
		{
			posOffset = pos - WCoord(dir * dffValue);
			blockId = pWorld->getBlockID(CoordDivBlock(posOffset));
			blockDef = GetDefManagerProxy()->getBlockDef(blockId);
			if (blockDef && (blockDef->MoveCollide == 1 || blockDef->PhyCollide == 1))
			{
				pos += WCoord(dir * dffValue);
			}
		}
		//左右检测
		Rainbow::Matrix4x4f matrix;
		matrix.SetAxisAngle(Rainbow::Vector3f::yAxis, Rainbow::Deg2Rad(90.0f));
		dir = matrix.MultiplyVector3(dir).GetNormalizedSafe();
		posOffset = pos + WCoord(dir * dffValue);
		blockId = pWorld->getBlockID(CoordDivBlock(posOffset));
		blockDef = GetDefManagerProxy()->getBlockDef(blockId);
		if (blockDef && (blockDef->MoveCollide == 1 || blockDef->PhyCollide == 1))
		{
			pos -= WCoord(dir * dffValue);
		}
		else
		{
			posOffset = pos - WCoord(dir * dffValue);
			blockId = pWorld->getBlockID(CoordDivBlock(posOffset));
			blockDef = GetDefManagerProxy()->getBlockDef(blockId);
			if (blockDef && (blockDef->MoveCollide == 1 || blockDef->PhyCollide == 1))
			{
				pos += WCoord(dir * dffValue);
			}
		}
	}

	dynamic_cast<ActorManager*>(pWorld->getActorMgr())->spawnActor(horse, pos, yaw, 0);
	m_CurAccountHorse = horse->getObjId();

	return horse;
}
void AccountHorseComponent::updateAccountHorse(int horseid, float hp, int addlivetick, int shieldcoolingticks)
{
	for (size_t i = 0; i < m_AccountHorses.size(); i++)
	{
		AccountHorseInfo &obj = m_AccountHorses[i];
		if (obj.horseid == horseid)
		{
			obj.hp = hp;
			obj.shieldcoolingticks = shieldcoolingticks;
			if (addlivetick < 0)
			{
				obj.liveticks = addlivetick;
				notifyAccountHorse2Self(horseid);
				BindOnTick();
			}
			else
			{
				obj.liveticks += addlivetick;
				if ((obj.liveticks % 20) == 0) notifyAccountHorse2Self(horseid);
			}
			return;
		}
	}
}

void AccountHorseComponent::notifyAccountHorse2Self(int horseid)
{		
	if (!GetOwner()) return ;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	if (!m_owner->hasUIControl())
	{
		PB_AccountHorseHC accountHorseHC;
		accountHorseHC.set_horseid(horseid);
		accountHorseHC.set_synctype(ACCHORSE_SYNC_LIVETICKS);
		accountHorseHC.set_syncdata(getAccountHorseLiveAge(horseid));

		GetGameNetManagerPtr()->sendToClient(m_owner->getUin(), PB_ACCOUNT_HORSE_HC, accountHorseHC);
	}
}

void AccountHorseComponent::resetAccountHorseLiveTick(int horseid, int t)
{
	BindOnTick();
	for (size_t i = 0; i < m_AccountHorses.size(); i++)
	{
		AccountHorseInfo &obj = m_AccountHorses[i];
		if (obj.horseid == horseid)
		{
			obj.liveticks = t;
			return;
		}
	}

	AccountHorseInfo obj;
	memset(&obj, 0, sizeof(obj));
	obj.horseid = horseid;
	obj.liveticks = t;
	m_AccountHorses.push_back(obj);
}

void AccountHorseComponent::setAccountHorseEquip(int horseid, int index, int itemid)
{
	for (size_t i = 0; i < m_AccountHorses.size(); i++)
	{
		AccountHorseInfo &obj = m_AccountHorses[i];
		if (obj.horseid == horseid)
		{
			assert(index >= 0 && index <= 2);
			obj.equips[index] = itemid;
			break;
		}
	}
}
int AccountHorseComponent::getAccountHorseLiveAge(int horseid)
{
	for (size_t i = 0; i < m_AccountHorses.size(); i++)
	{
		AccountHorseInfo &obj = m_AccountHorses[i];
		if (obj.horseid == horseid)
		{
			return obj.liveticks;
		}
	}
	return 0;
}
void AccountHorseComponent::clearAccountHorseLiveAge(int horseid)
{
	for (size_t i = 0; i < m_AccountHorses.size(); i++)
	{
		AccountHorseInfo &obj = m_AccountHorses[i];
		if (obj.horseid == horseid)
		{
			obj.liveticks = 0;
			notifyAccountHorse2Self(horseid);
			return;
		}
	}
}

flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::AccountHorseData>>> AccountHorseComponent::save(flatbuffers::FlatBufferBuilder &builder)
{
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::AccountHorseData>>> horsesoffset = 0;
	if (!m_AccountHorses.empty())
	{
		std::vector<flatbuffers::Offset<FBSave::AccountHorseData>> horses;
		for (size_t i = 0; i < m_AccountHorses.size(); i++)
		{
			const AccountHorseInfo& src = m_AccountHorses[i];
			horses.push_back(FBSave::CreateAccountHorseData(builder, src.horseid, src.hp, src.liveticks, src.equips[0], src.equips[1], src.equips[2]));
		}
		horsesoffset = builder.CreateVector(horses);
	}
	return horsesoffset;
}

void AccountHorseComponent::load(const FBSave::ActorPlayer* wrole)
{
	if (wrole->horses())
	{
		m_AccountHorses.resize(wrole->horses()->size());
		for (size_t i = 0; i < wrole->horses()->size(); i++)
		{
			auto src = wrole->horses()->Get(i);
			AccountHorseInfo& dest = m_AccountHorses[i];

			dest.horseid = src->horseid();
			dest.hp = src->hp();
			dest.liveticks = src->liveticks();
			dest.equips[0] = src->saddle();
			dest.equips[1] = src->armor();
			dest.equips[2] = src->rake();

			if (wrole->version() == 0) dest.hp *= 5.0f;
		}
	}
	m_CurAccountHorse = wrole->curhorse();
}

void AccountHorseComponent::accountHorseEgg()
{
	ActorHorse *horse = NULL;
	if (!GetOwner()) return ;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	World* pWorld = m_owner->getWorld();
	if (m_CurAccountHorse == 0 || (horse = dynamic_cast<ActorHorse *>(pWorld->getActorMgr()->iFindActorByWID(m_CurAccountHorse))) == NULL)
	{
		m_owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 386);
		return;
	}

	const HorseDef *def = horse->getHorseDef();
	if (def->EggGenTicks > 0 && def->EggBlock > 0 && getAccountHorseLiveAge(def->ID) < def->EggGenTicks)
	{
		m_owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 382);
		return;
	}

	Rainbow::Vector3f dir = Yaw2FowardDir(horse->getLocoMotion()->m_RotateYaw);

	WCoord pos1 = horse->getPosition();
	WCoord pos2 = pos1 - WCoord(dir*(BLOCK_FSIZE*2.0f));

	WCoord minpos = CoordDivBlock(Min(pos1, pos2));
	WCoord maxpos = CoordDivBlock(Max(pos1, pos2));

	for (int z = minpos.z; z <= maxpos.z; z++)
	{
		for (int x = minpos.x; x <= maxpos.x; x++)
		{
			WCoord blockpos(x, minpos.y, z);

			if (pWorld->doesBlockHaveSolidTopSurface(blockpos))
			{
				if (pWorld->getBlockMaterial(TopCoord(blockpos))->isReplaceable())
				{
					clearAccountHorseLiveAge(def->ID);
					pWorld->setBlockAll(TopCoord(blockpos), def->EggBlock, 0);
					m_owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 392);

					return;
				}
			}
			else if (pWorld->doesBlockHaveSolidTopSurface(DownCoord(blockpos)))
			{
				if (pWorld->getBlockMaterial(blockpos)->isReplaceable())
				{
					clearAccountHorseLiveAge(def->ID);
					pWorld->setBlockAll(blockpos, def->EggBlock, 0);
					m_owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 392);
					return;
				}
			}
		}
	}

	m_owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 391);
}

void AccountHorseComponent::sendUIDisplayHorseData()
{
	ClientPlayer* m_owner = static_cast<ClientPlayer*>(GetOwnerPlayer());
	PB_UIDisplayHorseHC UIDisplayHorseHC;
	UIDisplayHorseHC.set_horseobjid(m_CurAccountHorse);
	UIDisplayHorseHC.set_playerobjid(m_owner->getObjId());
	GetGameNetManagerPtr()->sendBroadCast(PB_UIDISPLAYHORSE_HC, UIDisplayHorseHC);
}

IMPLEMENT_COMPONENTCLASS(MPAccountHorseComponent)
MPAccountHorseComponent::MPAccountHorseComponent()
:AccountHorseComponent()
{

}

void MPAccountHorseComponent::summonAccountHorse(int horseid)
{
	if (!GetOwner()) return ;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	World* pWorld = m_owner->getWorld();
	if(pWorld->isRemoteMode())
	{
		PB_AccountHorseCH accountHorseCH;
		accountHorseCH.set_horseid(horseid);
		accountHorseCH.set_cmdtype(ACCHORSE_CMD_SUMMON);
		accountHorseCH.set_cmddata(0);

		GetGameNetManagerPtr()->sendToHost(PB_ACCOUNT_HORSE_CH, accountHorseCH);
	}
	else AccountHorseComponent::summonAccountHorse(horseid);
}

void MPAccountHorseComponent::accountHorseEgg()
{
	if (!GetOwner()) return ;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return ;
	World* pWorld = m_owner->getWorld();
	if(pWorld->isRemoteMode())
	{
		PB_AccountHorseCH accountHorseCH;
		accountHorseCH.set_horseid(0);
		accountHorseCH.set_cmdtype(ACCHORSE_CMD_LAYEGG);
		accountHorseCH.set_cmddata(0);

		GetGameNetManagerPtr()->sendToHost(PB_ACCOUNT_HORSE_CH, accountHorseCH);
	}
	else AccountHorseComponent::accountHorseEgg();	
}