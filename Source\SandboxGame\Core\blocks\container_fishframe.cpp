#include "container_fishframe.h"
#include "SandboxIdDef.h"
#include "world.h"
#include "BlockMaterialBase.h"

#define MAX_FISH 3
#define DRYTIME	600

// 新鲜的鱼
static std::unordered_set<int> s_FreshFish{ ITEM_FRESH_FISH, 12703, 12704, 12535 };
// 晒干的鱼
static std::unordered_set<int> s_DriedFish{ ITEM_DRY_FISH, 12707, 12708 ,12537 };
// 对应关系 
static std::unordered_map<int, int> s_Fresh2Dried{
	{ ITEM_FRESH_FISH, ITEM_DRY_FISH },
	{ 12703, 12707 },
	{ 12704, 12708 },
	{ 12535, 12537 },
};

bool FishFrameContainer::isFreshFish(int itemId)
{
	return s_FreshFish.find(itemId) != s_FreshFish.end();
}

bool FishFrameContainer::isDriedFish(int itemId)
{
	return s_DriedFish.find(itemId) != s_DriedFish.end();
}

int FishFrameContainer::getDriedItemId(int freshItemId)
{
	auto it = s_Fresh2Dried.find(freshItemId);
	if (it != s_Fresh2Dried.end())
	{
		return it->second;
	}
	return 0;
}

FishFrameContainer::FishFrameContainer()
{
	mFishItems.resize(MAX_FISH);
	mDryTime.resize(MAX_FISH);
	m_NeedTick = true;
}

FishFrameContainer::FishFrameContainer(const WCoord& blockpos)
	:WorldContainer(blockpos, 0)
{
	mFishItems.resize(MAX_FISH);
	mDryTime.resize(MAX_FISH);
	m_NeedTick = true;
}

FishFrameContainer::~FishFrameContainer()
{
}

void FishFrameContainer::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);

	registerUpdateTick();
}

void FishFrameContainer::updateTick()
{
	for (int i = 0; i < MAX_FISH; i++)
	{
		if (isFreshFish(mFishItems[i]))
		{
			mDryTime[i]++;
			if (mDryTime[i] >= DRYTIME)
			{
				mFishItems[i] = getDriedItemId(mFishItems[i]);
				mDryTime[i] = 0;
				m_World->setBlockData(m_BlockPos, m_World->getBlockData(m_BlockPos) ^ 8, 2);
			}
		}
	}
}

flatbuffers::Offset<FBSave::ChunkContainer> FishFrameContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto fishState = builder.CreateVector<int32_t>(&mFishItems[0], mFishItems.size());
	auto dryTime = builder.CreateVector<int32_t>(&mDryTime[0], mDryTime.size());
	auto actor = FBSave::CreateContainerFishFrame(builder, basedata, fishState, dryTime);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerFishFrame, actor.Union());
}

bool FishFrameContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerFishFrame*>(srcdata);
	if (!src)
		return false;

	loadContainerCommon(src->basedata());

	auto fishState = src->fishState();
	if (fishState)
	{
		for (int i = 0; i < MAX_FISH; i++)
		{
			if (i < (int)fishState->size())
			{
				mFishItems[i] = fishState->Get(i);
				// 兼容原有设计，可以考虑去掉
				if (mFishItems[i] == (int)FishState::FRESH)
				{
					mFishItems[i] = ITEM_FRESH_FISH;
				}
				else if (mFishItems[i] == (int)FishState::DRIED)
				{
					mFishItems[i] = ITEM_DRY_FISH;
				}
				// 兼容end
			}
			else
			{
				mFishItems[i] = 0;
			}
		}
	}

	auto dryTime = src->dryTime();
	if (dryTime)
	{
		for (int i = 0; i < MAX_FISH; i++)
		{
			if (i < (int)dryTime->size())
			{
				mDryTime[i] = dryTime->Get(i);
			}
			else
			{
				mDryTime[i] = 0;
			}
		}
	}

	return true;
}

void FishFrameContainer::dropItems()
{
	BlockMaterial* pmtl = getBlockMtl();
	if (!pmtl)
	{
		return;
	}
	std::vector<int> items2Drop;
	for (int i = 0; i < MAX_FISH; i++)
	{
		if (mFishItems[i])
		{
			items2Drop.push_back(mFishItems[i]);
		}
	}
	for(auto itemId : items2Drop)
	{
		pmtl->doDropItem(m_World, m_BlockPos, itemId, 1); 
	}
}

int FishFrameContainer::getFishItem(int idx)
{
	if (idx >= 0 && idx < MAX_FISH && idx < mFishItems.size())
	{
		return mFishItems[idx];
	}
	return -1;
}

bool FishFrameContainer::addFreshFish(int itemId)
{
	if (!isFreshFish(itemId))
	{
		return false;
	}
	for (int i = 0; i < MAX_FISH; i++)
	{
		if (!mFishItems[i])
		{
			mFishItems[i] = itemId;
			mDryTime[i] = 0;
			m_World->setBlockData(m_BlockPos, m_World->getBlockData(m_BlockPos) ^ 8, 2);
			return true;
		}
	}
	return false;
}

int FishFrameContainer::getAllSaltFish(std::vector<int>& output)
{
	int cnt = 0;
	output.resize(3);
	for (int i = 0; i < MAX_FISH; i++)
	{
		if (isDriedFish(mFishItems[i]))
		{
			output[cnt] = mFishItems[i];
			mFishItems[i] = 0;
			cnt++;
		}
	}
	if (cnt)
	{
		m_World->setBlockData(m_BlockPos, m_World->getBlockData(m_BlockPos) ^ 8, 2);	// 刷新显示
	}
	return cnt;
}

bool FishFrameContainer::removeFish(int idx)
{
	if (idx >= 0 && idx < MAX_FISH)
	{
		if (mFishItems[idx])
		{
			mFishItems[idx] = 0;
			m_World->setBlockData(m_BlockPos, m_World->getBlockData(m_BlockPos) ^ 8, 2);
			return true;
 		}
	}
	return false;
}

bool FishFrameContainer::hasEmptySlot()
{
	for (int i = 0; i < MAX_FISH; i++)
	{
		if (!mFishItems[i])
		{
			return true;
		}
	}
	return false;
}
