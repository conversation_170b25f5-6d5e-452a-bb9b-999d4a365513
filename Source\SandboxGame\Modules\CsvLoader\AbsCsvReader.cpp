#include "AbsCsvReader.h"
#include "OgreScriptLuaVM.h"
#include "OgreUtils.h"
#include "IWorldConfigProxy.h"
 
using MINIW::CSVParser;

int AbsCsvReader::s_CurLanguage = 0;// LANGUAGE_ZH;

const char* AbsCsvReader::ColumnLang(const CSVParser::TableLine &row, const char *col)
{
	return row[col].Str();
}

void AbsCsvReader::setLanguage(int language)
{
	s_CurLanguage = language;
}

AbsCsvReader::AbsCsvReader() : m_bHasLoaded(false)
{
	
}

AbsCsvReader::~AbsCsvReader()
{
}

bool AbsCsvReader::reload()
{
    m_bHasLoaded = false;
    return load();
}	

bool AbsCsvReader::load()
{
	if (m_bHasLoaded)
	{
		return true;
	}
	 
	CSVParser parser;
	char filepath[64] = {0};
	getPath(filepath);
	if (!parser.Load(filepath, true))
	{
		LOG_WARNING("csv load %s failed.", filepath);
		return false;
	}
	onParse(parser);	

	m_bHasLoaded = true;
	return true;
}

bool AbsCsvReader::hasLoaded()
{
    return m_bHasLoaded;
}

void AbsCsvReader::getPath(char filepath[64])
{
	 
    const char* filename = getName();
	 
	sprintf(filepath, "csvdef/utf8/%s.csv", filename);

}


 
