#pragma once

#include "container_erosion.h"

// 用于减少 ErosionContainer 伤害和血量处理的重复代码的宏定义

#define IMPLEMENT_EROSION_DAMAGE_FUNCTIONS(ClassName) \
    bool ClassName::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage) \
    { \
        if (pworld->isRemoteMode()) \
        { \
            return true; \
        } \
        \
        ErosionContainer* container = dynamic_cast<ErosionContainer*>(pworld->getContainerMgr()->getContainer(blockpos)); \
        if (container) \
        { \
            /* 近战伤害衰减99%，枪械伤害衰减90% */ \
            float attenuation_ratio = 1.0f; \
            /*if (attack_type == ATTACK_PUNCH) \
                attenuation_ratio = 0.01f; // 近战攻击 \
            else if (attack_type == ATTACK_RANGE) \
                attenuation_ratio = 0.1f; // 远程攻击*/ \
            \
            container->addHp(-damage * attenuation_ratio); /* 负数扣血 */ \
            return true; \
        } \
        else \
            return Super::onBlockDamaged(pworld, blockpos, player, attack_type, damage); \
    } \
    \
    int ClassName::getBlockHP(World* pworld, const WCoord& blockpos) \
    { \
        ErosionContainer* container = dynamic_cast<ErosionContainer*>(pworld->getContainerMgr()->getContainer(blockpos)); \
        if (container) \
            return container->getHp(); \
        return Super::getBlockHP(pworld, blockpos); \
    }

// 用于多块方块（需要获取核心位置）的宏定义
#define IMPLEMENT_MULTIBLOCK_EROSION_DAMAGE_FUNCTIONS(ClassName) \
    bool ClassName::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage) \
    { \
        if (pworld->isRemoteMode()) \
        { \
            return true; \
        } \
        auto corepos = getCoreBlockPos(pworld, blockpos); \
        if (corepos.y < 0) return false; \
        \
        ErosionContainer* container = dynamic_cast<ErosionContainer*>(pworld->getContainerMgr()->getContainer(corepos)); \
        if (container) \
        { \
            /* 近战伤害衰减99%，枪械伤害衰减90% */ \
            float attenuation_ratio = 1.0f; \
            /*if (attack_type == ATTACK_PUNCH) \
                attenuation_ratio = 0.01f; // 近战攻击 \
            else if (attack_type == ATTACK_RANGE) \
                attenuation_ratio = 0.1f; // 远程攻击*/ \
            \
            container->addHp(-damage * attenuation_ratio); /* 负数扣血 */ \
            return true; \
        } \
        else \
            return Super::onBlockDamaged(pworld, corepos, player, attack_type, damage); \
    } \
    \
    int ClassName::getBlockHP(World* pworld, const WCoord& blockpos) \
    { \
        auto corepos = getCoreBlockPos(pworld, blockpos); \
        if (corepos.y < 0) return 0; \
        \
        ErosionContainer* container = dynamic_cast<ErosionContainer*>(pworld->getContainerMgr()->getContainer(corepos)); \
        if (container) \
            return container->getHp(); \
        return Super::getBlockHP(pworld, corepos); \
    } 