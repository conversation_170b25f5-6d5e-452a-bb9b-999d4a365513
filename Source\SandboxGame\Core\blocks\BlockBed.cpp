
#include "BlockBed.h"
#include "BlockMaterialMgr.h"
//#include "OgreMaterial.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "Environment.h"
#include "ActorManagerInterface.h"
#include "IClientPlayer.h"
#include "ChunkGenerator.h"
//#include "GameEvent.h"
#include "DefManagerProxy.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
//#include "ActorVillager.h"

#include "BlockMaterialMgr.h"
#include "OgreUtils.h"
#include "IActorBody.h"
#include "SandboxIdDef.h"
#include "ClientPlayer.h"
#include "SocRevivePointComponent.h"
#include "special_blockid.h"
#include "ZmqProxy.h"
//#include "ActorTravelingTrader.h"

using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(BlockBed)
IMPLEMENT_BLOCKMATERIAL(SleepingBag)
IMPLEMENT_BLOCKMATERIAL(BlockSanrioBed)
//IMPLEMENT_BLOCKINSTANCE(BlockBed)

bool BedLogicHandle::IsBedOccupied(World* pworld, const WCoord& blockpos, int blockdata)
{
	return getMaterial(pworld, blockpos)->IsBedOccupied(pworld, blockpos, blockdata);
}

void BedLogicHandle::setBedOccupied(World* pworld, const WCoord& blockpos, bool occupied)
{
	getMaterial(pworld, blockpos)->setBedOccupied(pworld, blockpos, occupied);
}

bool BedLogicHandle::getNearestEmptyChunkCoordinates(WCoord& ret, World* pworld, const WCoord& blockpos, int loopcount)
{
	return getMaterial(pworld, blockpos)->getNearestEmptyChunkCoordinates(ret, pworld, blockpos, loopcount);
}

void BedLogicHandle::getEyePosInBed(World* pworld, const WCoord& sleeppos, WCoord& eyepos, Rainbow::Vector3f& lookdir)
{
	WCoord blockpos = CoordDivBlock(sleeppos);
	getMaterial(pworld, blockpos)->getEyePosInBed(pworld, sleeppos, eyepos, lookdir);
}

WORLD_ID BedLogicHandle::getBindActor(World* pworld, const WCoord& blockpos)
{
	return getMaterial(pworld, blockpos)->getBindActor(pworld, blockpos);
}

void BedLogicHandle::setBindActor(World* pworld, const WCoord& blockpos, WORLD_ID bindactor)
{
	getMaterial(pworld, blockpos)->setBindActor(pworld, blockpos, bindactor);
}

WCoord BedLogicHandle::getSleepPosition(World* pworld, const WCoord& blockpos)
{
	return getMaterial(pworld, blockpos)->getSleepPosition(pworld, blockpos);
}

WorldContainer* BedLogicHandle::getCoreContainer(World* pworld, const WCoord& blockpos)
{
	return getMaterial(pworld, blockpos)->getCoreContainer(pworld, blockpos);
}

bool BedLogicHandle::IsSleepNeedHide(World* pworld, const WCoord& blockpos)
{
	BedLogicInterface* bedlogicinterface = getMaterial(pworld, blockpos);

	if (!bedlogicinterface)
	{
		LOG_WARNING("getMaterial == null");
		return false;
	}

	return bedlogicinterface->IsSleepNeedHide(pworld, blockpos);
}

BedLogicInterface* BedLogicHandle::getMaterial(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return dynamic_cast<BedLogicInterface*>(g_BlockMtlMgr.getMaterial(BLOCK_BED));
	BedLogicInterface* inter = dynamic_cast<BedLogicInterface*>(g_BlockMtlMgr.getMaterial(pworld->getBlockID(blockpos)));
	if (inter)
	{
		return inter;
	}

	int blockId = pworld->getBlockID(blockpos);
	if (IsBedNotSleepBagBlock(blockId)) { //正常的木床睡觉时不隐藏模型
		return dynamic_cast<BedLogicInterface*>(g_BlockMtlMgr.getMaterial(blockId));
	}

	return dynamic_cast<BedLogicInterface*>(g_BlockMtlMgr.getMaterial(BLOCK_BED));
}


BlockBed::BlockBed() : m_ignoreCheckUpBlock(false)
{

}

inline bool IsBedHead(int blockdata)
{
	return (blockdata&4) != 0;
}

bool BlockBed::IsBedOccupied(World* pworld, const WCoord& blockpos, int blockdata)
{
	return (blockdata&8) != 0;
}

static int footBlockToHeadBlockMap[4][2] = {
	{1, 0}, {-1,0}, {0, 1}, {0, -1}
};

void BlockBed::getEyePosInBed(World *pworld, const WCoord &sleeppos, WCoord &eyepos, Rainbow::Vector3f &lookdir)
{
	WCoord blockpos = CoordDivBlock(sleeppos);
	int dir = pworld->getBlockData(blockpos) & 3;

	int dx = footBlockToHeadBlockMap[dir][0];
	int dz = footBlockToHeadBlockMap[dir][1];

	eyepos = BlockCenterCoord(blockpos) + WCoord(dx*3*BLOCK_HALFSIZE, BLOCK_HALFSIZE, dz*3*BLOCK_HALFSIZE);
	lookdir = (BlockCenterCoord(blockpos) - eyepos).toVector3();
	lookdir = MINIW::Normalize(lookdir);
	eyepos = eyepos - 100.0f * lookdir;//调整观察位置
}

int BlockBed::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int blockdata = sectionData->getBlock(blockpos).getData();

	if(IsBedHead(blockdata)) idbuf[0] = 1;
	else idbuf[0] = 0;

	dirbuf[0] = blockdata & 3;

	return 1;
}

void BlockBed::init(int resid)
{
	ModelBlockMaterial::init(resid);
	//属性初始化
	SetToggle(BlockToggle_HasContainer, false);
}

void BlockBed::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}


//const char *BlockBed::getGeomName()
//{
//	return GetBlockDef()->Texture2.c_str();
//}

int BlockBed::doSleep(World *pworld, IClientPlayer *player,const WCoord & blockpos) const
{
	if (g_WorldMgr && !g_WorldMgr->canSleepToSkipNight())
	{
		return STRDEF_SLEEP_NOTIPS;
	}	int state = player->sleepInVehicleBed(blockpos, pworld);
    return state > -1 ? state : player->sleepInBed(blockpos);}

bool BlockBed::onTrigger(World *pworld, const WCoord &input_blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	if (!player || !pworld) 
	{
		return false;
	}

	if(pworld->onServer())
	{
		WCoord blockpos = input_blockpos;

		int blockdata = pworld->getBlockData(blockpos);
		WorldBed * container = NULL;
		if(IsBedHead(blockdata))
		{
			int dir = blockdata & 3;

			blockpos.x -= footBlockToHeadBlockMap[dir][0];
			blockpos.z -= footBlockToHeadBlockMap[dir][1];
			if(pworld->getBlockID(blockpos) != getBlockResID()) 
			{
				return true;
			}

			blockdata = pworld->getBlockData(blockpos); //get bed foot data
			container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(blockpos));
		}
		else
		{
			container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(blockpos));
		}

		//LOG_WARNING("%d", container->m_OwnerUin);
		if(pworld->getChunkProvider()->canRespawnHere())
		{
			if (IsBedOccupied(pworld, blockpos, blockdata))
			{
				auto actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
				if(actorMgr&&actorMgr->getOccupiedPlayer(blockpos, ACTORFLAG_SLEEP))
				{
					return true;
				}
				//有野人在睡觉的床也不能躺
				if (container && container->getBindActor()>0)
				{
					return true;
				}

				setBedOccupied(pworld, blockpos, false);
			}
			int ret = doSleep(pworld,player,blockpos);

			if(ret == 0)
			{
				addSocRevivePoint(pworld, player, blockpos);
				setBedOccupied(pworld, blockpos, true);
			}
			else
			{
				player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, ret);
			}
		}
		else
		{
			WCoord center = BlockCenterCoord(blockpos);
			pworld->setBlockAir(blockpos);

			int dir = blockdata & 3;
			blockpos.x += footBlockToHeadBlockMap[dir][0];
			blockpos.z += footBlockToHeadBlockMap[dir][1];
			if(pworld->getBlockID(blockpos) == getBlockResID())
			{
				pworld->setBlockAir(blockpos);

				WCoord center2 = BlockCenterCoord(blockpos);
				center = (center + center2) / 2;
			}
// 			if (pworld->isVehicleWorld())
			{
				VehicleWorld* vworld = dynamic_cast<VehicleWorld*>(pworld);
				if(vworld)
				{
					ActorVehicleAssemble* pAssemble = vworld->getActorVehicleAssemble();
					if(pAssemble)
					{
						int groupId = pAssemble->getPartGroupIdWithPos(blockpos);
						if (groupId < 0)
						{
							groupId = 0; 
						}

						center = pAssemble->convertWcoord(Rainbow::Vector3f((float)blockpos.x, (float)blockpos.y, (float)blockpos.z), true, groupId);
					}
				}
			}

			pworld->createExplosion(NULL, center, 5, true, true);
		}
	}

	return true;
}

void BlockBed::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	if(m_ignoreCheckUpBlock)
		return;
	if (blockid != getBlockResID())
		return;
	int blockdata = pworld->getBlockData(blockpos);
	int ishead = blockdata & 4;
	int placedir = blockdata & 3;

	if(ishead)
	{
		if(pworld->getBlockID(NeighborCoord(blockpos, placedir)) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
		}
	}
	else
	{
		if(pworld->getBlockID(NeighborCoord(blockpos, ReverseDirection(placedir))) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
			dropBlockAsItem(pworld, blockpos, blockdata);
		}
	}
}

void BlockBed::setBedOccupied(World *pworld, const WCoord &blockpos, bool occupied)
{
	int blockdata = pworld->getBlockData(blockpos);

	if(occupied) blockdata |= 8;
	else blockdata &= ~8;

	pworld->setBlockData(blockpos, blockdata, 2);

    if(!IsBedHead(blockdata))//床尾
    {
        int dir = blockdata & 3;
        WCoord headpos = blockpos;
        headpos.x += footBlockToHeadBlockMap[dir][0];
        headpos.z += footBlockToHeadBlockMap[dir][1];
        
		if (!IsSleepingbagBlock(pworld->getBlockID(headpos)))
            return;
        int headdata = pworld->getBlockData(headpos);
        if(occupied) headdata |= 8;
        else headdata &= ~8;
        pworld->setBlockData(headpos, headdata, 2);
    
        if(!pworld || !pworld->getActorMgr())
            return;
		//为了避免部分大的模型穿透,睡觉的时候 直接隐藏
		pworld->getActorMgr()->BlockBedBodyShow(blockpos, occupied);
    }
}

inline bool CanStandOnBlock(World *pworld, int x, int y, int z)
{
	
	auto block = pworld->getBlock(WCoord(x, y, z));
	if (!block.isEmpty())
	{
		return block.moveCollide() != 1;
	}
	int blockid = pworld->getBlockID(x, y, z);
	BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	if(def){
		return def->MoveCollide!=1;
	}else{
		return false;
	}
}

bool BlockBed::getNearestEmptyChunkCoordinates(WCoord &ret, World *pworld, const WCoord &blockpos, int loopcount)
{
	int blockdata = pworld->getBlockData(blockpos);
	int dir = blockdata & 3;

	for(int i = 0; i <= 1; ++i)
	{
		int ox = blockpos.x + footBlockToHeadBlockMap[dir][0] * i - 1;
		int oz = blockpos.z + footBlockToHeadBlockMap[dir][1] * i - 1;
		int ex = ox + 2;
		int ez = oz + 2;

		for (int x = ox; x <= ex; ++x)
		{
			for (int z = oz; z <= ez; ++z)
			{
				if(pworld->doesBlockHaveSolidTopSurface(WCoord(x, blockpos.y-1, z)) 
					&& CanStandOnBlock(pworld, x, blockpos.y, z) && CanStandOnBlock(pworld, x, blockpos.y+1, z))
				{
					if(loopcount <= 0)
					{
						ret = WCoord(x, blockpos.y, z);
						return true;
					}

					loopcount--;
				}
			}
		}
	}

	if(CanStandOnBlock(pworld,blockpos.x,blockpos.y+1,blockpos.z) && CanStandOnBlock(pworld,blockpos.x,blockpos.y+2,blockpos.z))
	{
		ret = blockpos;
		return true;
	}
	else return false;
}

void BlockBed::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */)
{
	int ishead = blockdata & 4;
	if(!ishead) ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance);
}

void BlockBed::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	//coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE+WCoord(BLOCK_SIZE,BLOCK_SIZE/2,BLOCK_SIZE));
	coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE+WCoord(BLOCK_SIZE,8,BLOCK_SIZE));
}

int BlockBed::convertDataByRotate(int blockdata, int rotatetype)
{
	return this->commonConvertDataByRotateWithBit(blockdata, rotatetype, 3, 12);
}

void BlockBed::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
	if (pworld)
	{
		removeSocRevivePoint(pworld, blockpos);

		WorldBed * bedContainer = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(blockpos));
		if (bedContainer)
		{
			bedContainer->stopBedEffectByBlockdata(blockdata);

			//同时清理世界数据记录
			if (GetWorldManagerPtr() && bedContainer->getBindActor())
			{
				if (GetWorldManagerPtr()->getWorldInfoManager()->getVillageBedStatus(blockpos, bedContainer->getBindActor())!=ENUM_BED_STATUS_NO_BIND_ACOTR)
				{
					GetWorldManagerPtr()->getWorldInfoManager()->removeVillageBedRelationship(bedContainer->getBindActor());
				}
			}

			pworld->getContainerMgr()->destroyContainer(blockpos);
		}
	}
}

void BlockBed::addSocRevivePoint(World* pworld, IClientPlayer* player, const WCoord& blockpos)
{
	ClientPlayer* client_player = dynamic_cast<ClientPlayer*>(player);
	SocBedMgr* bedmgr = pworld->GetBedMgr();
	if (client_player && bedmgr)
	{
		SocRevivePointComponent* SocRevive = client_player->GetComponent<SocRevivePointComponent>();
		SocBedData* beddata = bedmgr->GetSocBedDataByPos(blockpos);
		if (!beddata)
		{
			beddata = bedmgr->AddBed(blockpos, pworld->getBlockID(blockpos));
		}
		if (beddata->owner == 0 && bedmgr->SetSocBedDataOwner(blockpos, client_player->getUin()))
		{
			SocRevive->WritePoint(blockpos);
#ifdef IWORLD_SERVER_BUILD
			//save database
			int uin = 1000;// 不是存储到玩家 所以填1000
			WorldManager* worldMgr = GetWorldManagerPtr();
			long long owid = worldMgr->getWorldId();
			miniw::bed_data data;
			data.set_x(blockpos.x);
			data.set_y(blockpos.y);
			data.set_z(blockpos.z);
			data.set_itemid(pworld->getBlockID(blockpos));
			data.set_owner(beddata->owner);

			std::string savedata;
			data.SerializeToString(&savedata);
			g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveBedData, savedata.c_str(), savedata.length(), owid, uin);
#endif
		}
	}
}

void BlockBed::removeSocRevivePoint(World* pworld, const WCoord& blockpos)
{
	SocBedMgr* bedmgr = pworld->GetBedMgr();
	if (!bedmgr)
	{
		LOG_WARNING("BlockBed::removeSocRevivePoint not bedmgr");
		return;
	}

	SocBedData* beddata = bedmgr->GetSocBedDataByPos(blockpos);
	if (!beddata)
	{
		LOG_WARNING("BlockBed::removeSocRevivePoint not beddata");
		return;
	}

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(g_WorldMgr->getPlayerByUin(beddata->owner));
	if (!player)
	{
		LOG_WARNING("BlockBed::removeSocRevivePoint get player error %d", beddata->owner);
		return;
	}

	if (!bedmgr->GetSocBedDataByPos(blockpos))
	{
		return;
	}

	SocRevivePointComponent* SocRevive = player->GetComponent<SocRevivePointComponent>();
	if (SocRevive)
	{
		SocRevive->RemovePoint(blockpos);
	}
	bedmgr->RemoveBed(blockpos);
#ifdef IWORLD_SERVER_BUILD
	int uin = 1000;// 不是存储到玩家 所以填1000
	WorldManager* worldMgr = GetWorldManagerPtr();
	long long owid = worldMgr->getWorldId();
	miniw::bed_data data;
	data.set_x(blockpos.x);
	data.set_y(blockpos.y);
	data.set_z(blockpos.z);
	data.set_itemid(pworld->getBlockID(blockpos));
	data.set_owner(-1); //-1是删除

	std::string savedata;
	data.SerializeToString(&savedata);
	g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveBedData, savedata.c_str(), savedata.length(), owid, uin);
#endif
}

WORLD_ID BlockBed::getBindActor(World *pworld, const WCoord &blockpos)
{
	WorldBed* container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		return container->getBindActor();
	}
	return 0;
}

void BlockBed::setBindActor(World *pworld, const WCoord &blockpos, WORLD_ID bindactor)
{
	WorldBed* container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (!container)
	{
		container = SANDBOX_NEW(WorldBed, blockpos);
		pworld->getContainerMgr()->spawnContainer(container);
		container->setBindActor(bindactor);
	}
	else
	{
		container->setBindActor(bindactor);
	}

	pworld->getActorMgr()->BlockBedBindActor(blockpos, bindactor, container);
	
}

WCoord BlockBed::getSleepPosition(World* pworld, const WCoord& blockpos)
{
	return blockpos * BLOCK_SIZE + WCoord(50, 25, 50);
}

WorldContainer* BlockBed::getCoreContainer(World* pworld, const WCoord& blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	WorldBed* container = NULL;
	if (IsBedHead(blockdata))
	{
		int dir = blockdata & 3;
		WCoord corePos = blockpos;
		corePos.x -= footBlockToHeadBlockMap[dir][0];
		corePos.z -= footBlockToHeadBlockMap[dir][1];
		if (pworld->getBlockID(corePos) != m_BlockResID)
		{
			return NULL;
		}

		container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(corePos));
	}
	else
	{
		container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(blockpos));
	}
	return container;
}

//bool BlockBed::hasContainer()
//{
//	return true;
//}

SectionMesh *BlockBed::createBlockProtoMesh(int protodata)
{

	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getDefaultMtl(), true);

	BlockGeomMeshInfo meshinfo;

	getGeom(0)->getFaceVerts(meshinfo, 0);
	WCoord pos(0, -BLOCK_SIZE / 2, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos);

	getGeom(0)->getFaceVerts(meshinfo, 1);
	WCoord pos1(0, +BLOCK_SIZE / 2, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos1);

	return pmesh;
}

void BlockBed::blockTick(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
	{
		return;
	}

	pworld->getActorMgr()->BlockBedForPlayer(blockpos);
}

bool BlockBed::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage)
{
	// 获取床的核心位置（床脚位置）
	int blockdata = pworld->getBlockData(blockpos);
	WCoord corePos = blockpos;
	
	// 如果这是床头，需要获取床脚的位置作为核心位置
	if (IsBedHead(blockdata))
	{
		int dir = blockdata & 3;
		corePos.x -= footBlockToHeadBlockMap[dir][0];
		corePos.z -= footBlockToHeadBlockMap[dir][1];
		
		if (pworld->getBlockID(corePos) != getBlockResID())
		{
			return false;
		}
	}
	
	// 基于核心位置调用基类的默认处理逻辑
	bool result = ModelBlockMaterial::onBlockDamaged(pworld, corePos, player, attack_type, damage);
	
	// 如果床被破坏，需要移除复活点
	if (result)
	{
		int hp = getBlockHP(pworld, corePos);
		if (hp <= 0)
		{
			//removeSocRevivePoint(pworld, corePos);
		}
	}
	
	return result;
}

int BlockBed::getBlockHP(World* pworld, const WCoord& blockpos)
{
	// 床的HP是基于床脚位置的数据
	int blockdata = pworld->getBlockData(blockpos);
	WCoord corePos = blockpos;
	
	// 如果这是床头，需要获取床脚的位置
	if (IsBedHead(blockdata))
	{
		int dir = blockdata & 3;
		corePos.x -= footBlockToHeadBlockMap[dir][0];
		corePos.z -= footBlockToHeadBlockMap[dir][1];
		
		if (pworld->getBlockID(corePos) != getBlockResID())
		{
			return 0;
		}
	}
	
	// 调用基类方法获取HP
	return ModelBlockMaterial::getBlockHP(pworld, corePos);
}

bool BlockBed::getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf)
{
	// 床是由两个方块组成的，需要获取床头和床脚的位置
	int blockdata = pworld->getBlockData(blockpos);
	WCoord footPos = blockpos;
	WCoord headPos = blockpos;
	
	if (IsBedHead(blockdata))
	{
		// 当前是床头，计算床脚位置
		int dir = blockdata & 3;
		footPos.x -= footBlockToHeadBlockMap[dir][0];
		footPos.z -= footBlockToHeadBlockMap[dir][1];
	}
	else
	{
		// 当前是床脚，计算床头位置
		int dir = blockdata & 3;
		headPos.x += footBlockToHeadBlockMap[dir][0];
		headPos.z += footBlockToHeadBlockMap[dir][1];
	}
	
	// 验证床头和床脚都是有效的床方块
	if (pworld->getBlockID(footPos) == getBlockResID() && 
		pworld->getBlockID(headPos) == getBlockResID())
	{
		// 添加床头和床脚到列表
		if (includeSelf)
		{
			blockList.push_back(blockpos);
		}
		
		if (footPos != blockpos)
		{
			blockList.push_back(footPos);
		}
		if (headPos != blockpos)
		{
			blockList.push_back(headPos);
		}
		
		return true;
	}
	
	return false;
}


IMPLEMENT_BLOCKMATERIAL_INSTANCE_BEGIN(BlockBed)
	IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(BlockBed, R_Dir, int)(0, "Dir", "Block", &BlockBedInstance::GetBlockDir, &BlockBedInstance::SetBlockDir);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_END()



int SleepingBag::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
     int blockdata = sectionData->getBlock(blockpos).getData();

    if(IsBedOccupied(world,blockpos,blockdata))//已经占用
        idbuf[0] = IsBedHead(blockdata)?2:0;
    else
        idbuf[0] = IsBedHead(blockdata)?3:1; 
    dirbuf[0] = blockdata & 3;
    return 1;
}

void SleepingBag::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE+WCoord(BLOCK_SIZE,BLOCK_SIZE/2,BLOCK_SIZE));
}


int BlockSanrioBed::doSleep(World* pworld, IClientPlayer* player, const WCoord& blockpos) const
{
	if (g_WorldMgr && !g_WorldMgr->canSleepToSkipNight())
	{
		return STRDEF_SLEEP_NOTIPS;
	}
	int state = player->sleepInVehicleBed(blockpos, pworld);

	return state > -1 ? state : player->sleep(blockpos);
}

void BlockSanrioBed::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	coldetect->addObstacle(blockpos * BLOCK_SIZE, blockpos * BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE / 2, BLOCK_SIZE));
}

bool BlockSanrioBed::canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)
{
	for (int dir = 0; dir < 6; dir++)
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, dir)) == m_BlockResID)
		{
			return false;
		}
	}
	return true;
}

WCoord BlockSanrioBed::getSleepPosition(World* pworld, const WCoord& blockpos)
{
	if (pworld != NULL)
	{
		int blockId = pworld->getBlockID(blockpos);
		if (blockId == BLOCK_BED_SANRIO_TWINSTAR) //双子星的床稍高
		{
			return blockpos * BLOCK_SIZE + WCoord(50, 65, 50);
		}
		else if (blockId == BLOCK_BED_SANRIO_KUROMI || blockId == BLOCK_BED_SANRIO_CINNAMOROLL) //酷洛米/大耳狗床稍低
		{
			return blockpos * BLOCK_SIZE + WCoord(50, 50, 50);
		}
	}
	return blockpos * BLOCK_SIZE + WCoord(50, 55, 50);
}