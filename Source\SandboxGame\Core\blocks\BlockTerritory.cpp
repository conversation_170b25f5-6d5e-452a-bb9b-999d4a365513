#include "BlockTerritory.h"
#include "BlockMaterialMgr.h"
#include "special_blockid.h"
#include "world.h"
#include "ClientPlayer.h"
#include "WorldManager.h"
#include "container_world.h"
#include "ClientActorManager.h"
#include "Collision.h"
#include "BlockGeom.h"
#include "GameEffectManager.h"
//#include "OgreMaterial.h"
#include "ObserverEventManager.h"
#include "DefManagerProxy.h"
#include "MpActorManager.h"
#include "IPlayerControl.h"
#include "PlayerControl.h"
#include "SandboxEventDispatcherManager.h"
#include "GameNetManager.h"
#include "ClientInfoProxy.h"
#include "ClientPlayer.h"
#include "RevivePointComponent.h"
#include "container_territory.h"
#include "BlockRegionReplicator.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "GameCamera.h"
#include "ActorVillager.h"
#include "backpack.h"
#include "basesection.h"
#include "WorldProxy.h"
#include "ZmqProxy.h"
#include <sstream> // ���Ӷ� std::ostringstream ��֧��
#include <random> // ���Ӷ� UUID ���ɵ�֧��
#include <iomanip> // ���Ӷ� std::hex ��֧��
IMPLEMENT_BLOCKMATERIAL(BlockTerritory)
using namespace MINIW;
using namespace MNSandbox;

BlockTerritory::BlockTerritory() 
{

}

BlockTerritory::~BlockTerritory()
{
}

void BlockTerritory::init(int resid)
{
	ModelBlockMaterial::init(resid);

	SetToggle(BlockToggle_HasContainer, true);
}

//void BlockTerritory::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance)
//{
//	
//}

bool BlockTerritory::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!pworld || !pworld->getContainerMgr() || !playerTmp || !playerTmp->getBackPack() || !GetWorldManagerPtr() || !GetWorldManagerPtr()->getWorldInfoManager())
	{
		return false;
	}
	auto con_pos = BlockTerritory::GetRealTotemIceContainer(pworld, blockpos);
	TerritoryContainer* container = dynamic_cast<TerritoryContainer*>(pworld->getContainerMgr()->getContainer(con_pos));
	if (container)
	{
		if (container->m_OwnerUin == 0)//�����Լ�����ص�������
		{
			container->m_OwnerUin = playerTmp->getUin();
			pworld->markBlockForUpdate(con_pos);
			playerTmp->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000728);
			return true;
		}
		playerTmp->openContainer(container);
	}

	return true;
}

void BlockTerritory::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (!pworld || !pworld->getContainerMgr() || !player || !GetWorldManagerPtr() || !GetWorldManagerPtr()->getWorldInfoManager())
	{
		return;
	}
	int blockdata = pworld->getBlock(blockpos).getData();
	if (getPartIndex(blockdata) != 0) return;
	TerritoryContainer* container = dynamic_cast<TerritoryContainer*>(pworld->getContainerMgr()->getContainer(GetRealTotemIceContainer(pworld, blockpos)));

	if (container)
	{
		container->SetUin(player->getUin());
	}
	
}

WorldContainer* BlockTerritory::createContainer(World* pworld, const WCoord& blockpos)
{
	if (!pworld || !pworld->getContainerMgr())
	{
		return nullptr;
	}
	if (getPartIndex(pworld->getBlockData(blockpos)) != 0)
	{
		return nullptr;
	}
	TerritoryContainer* container = dynamic_cast<TerritoryContainer*>(pworld->getContainerMgr()->getContainer(GetRealTotemIceContainer(pworld, blockpos)));
	if (container) 
	{
		return container;
	}
	else 
	{
		TerritoryContainer* container = ENG_NEW(TerritoryContainer)(GetRealTotemIceContainer(pworld, blockpos));
		return container;
	}
}

void BlockTerritory::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	int blockdata = data.m_SharedSectionData->getBlock(blockpos).getData();
	if (getPartIndex(blockdata) != 0) return;
	ModelBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
}

void BlockTerritory::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	coldetect->addObstacle(blockpos * BLOCK_SIZE, blockpos * BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

void BlockTerritory::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	//import-finish
	if (!pworld || !pworld->getContainerMgr())
	{
		return;
	}
	
	int blockdata = pworld->getBlockData(blockpos);
	int partindex = getPartIndex(blockdata);
	int placedir = 5;

	//if (2 == partindex) //���ϵ��������ڶ�����ʧ
	//{
	//	if (pworld->getBlockID(NeighborCoord(blockpos, ReverseDirection(placedir))) != m_BlockResID)
	//	{
	//		pworld->setBlockAir(blockpos);
	//	}
	//}
	//else 
	if (1 == partindex) //�м�ĸ��Ӹ����1�͵�3����ʧ
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, ReverseDirection(placedir))) != m_BlockResID )
			//||
			//pworld->getBlockID(NeighborCoord(blockpos, placedir)) != m_BlockResID)
		{
			pworld->setBlockAir(blockpos);
		}
	}
	else //���µĸ��Ӹ����2��������ʧ
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, placedir)) != m_BlockResID)
		{
			pworld->setBlockAir(blockpos);
		}
		
	}
	TerritoryContainer* container = dynamic_cast<TerritoryContainer*>(pworld->getContainerMgr()->getContainer(GetRealTotemIceContainer(pworld, blockpos)));
	if (container) 
	{

	}
}

void BlockTerritory::onBlockAdded(World* pworld, const WCoord& blockpos)
{
	if (!pworld || !pworld->getContainerMgr() || !GetWorldManagerPtr() || !GetWorldManagerPtr()->getWorldInfoManager())
	{
		return;
	}
	int blockdata = pworld->getBlock(blockpos).getData();
	if (getPartIndex(blockdata) != 0) return;
	TerritoryContainer* container = dynamic_cast<TerritoryContainer*>(pworld->getContainerMgr()->getContainer(GetRealTotemIceContainer(pworld, blockpos)));
	if (!container) 
	{
		container = ENG_NEW(TerritoryContainer)(GetRealTotemIceContainer(pworld, blockpos));
	}
}

void BlockTerritory::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	if (!pworld || !pworld->getContainerMgr() || !pworld->getActorMgr() || !GetWorldManagerPtr() || !GetWorldManagerPtr()->getWorldInfoManager())
	{
		return;
	}
	if (GetRealTotemIceContainer(pworld, blockpos) != blockpos) 
	{
		return;
	}
	TerritoryContainer* container = dynamic_cast<TerritoryContainer*>(pworld->getContainerMgr()->getContainer(GetRealTotemIceContainer(pworld, blockpos)));
	if (!container)
		return;

	removeTerritory(pworld, container->getUuid(), container->m_OwnerUin, blockpos);

/*	IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(container->m_OwnerUin);
	if (iplayer)
	{
		if (auto player= iplayer->GetPlayer())
		{
			player->removeTerritory(blockpos);
		}
	}*/	
}

void BlockTerritory::OnRecordBlockOwner(IClientPlayer* player, World* pworld, const WCoord& blockpos)
{
	if (!pworld || !player || !pworld->getContainerMgr() || !GetWorldManagerPtr() || !GetWorldManagerPtr()->getWorldInfoManager())
	{
		return;
	}
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	TerritoryContainer* container = dynamic_cast<TerritoryContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		container->SetUin(player->getUin());
		//playerTmp->addTerritory(blockpos);
	}
	else
	{
		container = SANDBOX_NEW(TerritoryContainer, blockpos);
		pworld->getContainerMgr()->spawnContainer(container);
		container->SetUin(player->getUin());
		//playerTmp->addTerritory(blockpos);
	}
	auto new_string = GenUuid();
	addTerritory(pworld, new_string,(unsigned int)player->getUin(), blockpos);
	if (container)
	{
		container->SetUuid(new_string);
		container->enterWorld(pworld); //ˢ��һ����Ч
#ifdef BUILD_TEST
		// 仅服务端压力测试使用
		if (!pworld->isRemoteMode()) {
			if (GetClientInfoProxy()->getGameData("game_env") == 1)
			{
				// 调用容器的onBlockPlacedBy方法来处理预设物品逻辑
				container->onBlockPlacedBy(pworld, GetRealTotemIceContainer(pworld, blockpos), player);
			}
		}
#endif
	}
}
bool BlockTerritory::canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)
{
	if (!pworld)
	{
		return false;
	}
	//import-finish
	//ռ3��ߣ�3�����ж��������ܷ���
	WCoord secPos = TopCoord(blockpos);
	WCoord thiPos = TopCoord(secPos);
	if (//!IsAirBlockID(pworld->getBlockID(thiPos)) ||
		!IsAirBlockID(pworld->getBlockID(secPos)) ||
		//!IsAirBlockID(pworld->getBlockID(blockpos)) ||
		pworld->getBlockID(DownCoord(blockpos)) == BLOCK_AIR)
	{
		return false;
	}

	return true;
}


int BlockTerritory::getPartIndex(int blockdata)
{
	//import-finish
	if (8 & blockdata)
	{
		return 2;
	}
	else if (4 & blockdata)
	{
		return 1;
	}
	return 0;
}

int BlockTerritory::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	if (!world || !world->getContainerMgr())
	{
		return 0;
	}
	int blockdata = sectionData->getBlock(blockpos).getData();
	int partidx = getPartIndex(blockdata);

	int nGeomNum;
	idbuf[0] = 0;
	dirbuf[0] = blockdata & 3;
	nGeomNum = 1;
	if (world && world->getContainerMgr()) 
	{
		WCoord wpos = sectionData->getOrigin() + blockpos;
		TerritoryContainer* container = dynamic_cast<TerritoryContainer*>(world->getContainerMgr()->getContainer(GetRealTotemIceContainer(world, wpos)));
		if (container)
		{
			if (container->GetHaveAward())
			{
				idbuf[1] = 1;
				dirbuf[1] = dirbuf[0];
				idbuf[2] = 2;
				dirbuf[2] = dirbuf[0];
				nGeomNum = 3;
			}
		}
	}
	
	return nGeomNum;
}

void BlockTerritory::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}


WCoord BlockTerritory::GetRealTotemIceContainer(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
	{
		return blockpos;
	}
	int data = pworld->getBlockData(blockpos);
	if (8 & data)
	{
		return DownCoord(DownCoord(blockpos));
	}
	if (4 & data)
	{
		return DownCoord(blockpos);
	}
	return blockpos;
}

void BlockTerritory::addTerritory(World* pworld, const std::string& gen_uuid, const unsigned int& m_OwnerUin, const WCoord& pos)
{
#ifdef IWORLD_SERVER_BUILD
	int uin = 1000;// 这是存储的数据 这里是1000
	WorldManager* worldMgr = GetWorldManagerPtr();
	long long owid = worldMgr->getWorldId();
	miniw::territory_permissions pb;
	pb.set_room_id(std::to_string(owid));
	pb.set_owner_uin(m_OwnerUin);
	pb.set_territory_id(gen_uuid);
	pb.set_position(std::to_string(pos.x) + "," + std::to_string(pos.y) + "," + std::to_string(pos.z));
	pb.set_status("active");
	std::string reqData;
	pb.SerializeToString(&reqData);
	g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveTerritoryPermissions, reqData.c_str(), reqData.length(), owid, uin);
#endif
}

void BlockTerritory::removeTerritory(World* pworld, const std::string& uuid, const unsigned int& m_OwnerUin, const WCoord& pos)
{
#ifdef IWORLD_SERVER_BUILD
	int uin = 1000;// 这是存储的数据 这里是1000
	WorldManager* worldMgr = GetWorldManagerPtr();
	long long owid = worldMgr->getWorldId();
	miniw::territory_permissions pb;
	pb.set_room_id(std::to_string(owid));
	pb.set_owner_uin(-1);// -1表示删除
	pb.set_territory_id(uuid);
	pb.set_position(std::to_string(pos.x) + "," + std::to_string(pos.y) + "," + std::to_string(pos.z));
	pb.set_status("deleted");
	std::string reqData;
	pb.SerializeToString(&reqData);
	g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveTerritoryPermissions, reqData.c_str(), reqData.length(), owid, uin);
#endif
}

//gameserver ����UUID
std::string BlockTerritory::GenUuid()
{
	// ʹ�ñ�׼��UUID4��ʽ���ַ���
	std::random_device rd;
	std::mt19937 gen(rd());
	std::uniform_int_distribution<> dis(0, 15);
	std::uniform_int_distribution<> dis2(8, 11);

	std::ostringstream oss;
	oss << std::hex;
	
	// ����UUID4��ʽ: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
	// ����8λ
	for (int i = 0; i < 8; i++) {
		oss << dis(gen);
	}
	oss << "-";
	
	// ����4λ
	for (int i = 0; i < 4; i++) {
		oss << dis(gen);
	}
	oss << "-";
	
	// ����4λ (��һλ�̶���4)
	oss << "4";
	for (int i = 0; i < 3; i++) {
		oss << dis(gen);
	}
	oss << "-";
	
	// ����4λ (��һλ��8-b)
	oss << dis2(gen);
	for (int i = 0; i < 3; i++) {
		oss << dis(gen);
	}
	oss << "-";
	
	// ����12λ
	for (int i = 0; i < 12; i++) {
		oss << dis(gen);
	}
	
	return oss.str();
}