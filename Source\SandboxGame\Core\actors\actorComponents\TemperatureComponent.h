﻿#ifndef __TEMPERATURE_COMPONENT_H__
#define __TEMPERATURE_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "ClientActor.h"
#include "math/CCGeometry.h"

namespace Rainbow {
    class MaterialInstance;
}
namespace fairygui
{
    class GLoader;
}

class EffectParticle;

enum class TCBuffType :int {
    
    Autoignition = 0,     //自燃
    ExtremelyHot,         //极热
    ScorchingHot,         //炎热
    Suitable,             //适宜
    Cold,                 //寒冷
    ExtremelyCold,        //极寒
    Freeze,               //冰冻
    FreezeIce,            //冻结冰
};

struct TempImpactDuration
{
    bool IsLowOrHigh;       //是否是低温否则是高温 true:低温 false:高温
    int  BuffId;
    int  TickNum;    
    int  JumpAddTickNum; 
    int  JumpIntervalTickNum;
    int  PermanentBuffTickNum;
    int  LimitTime; //持续模式（0=无限时间 大于0=有限时间）
    float  TickMark;
    int  JumpTickMark;
    int  SoundTick;
    TempImpactDuration():IsLowOrHigh(false), BuffId(0), TickNum(0), JumpAddTickNum(0), JumpIntervalTickNum(0), PermanentBuffTickNum(0), LimitTime(0),TickMark(0), JumpTickMark(0),SoundTick(0) {}
};

struct IceCube
{
    int  BuffId;
    EffectParticle* Particle;  // 建议使用EffectID来获取Effect, 避免使用 Particle
    int EffectID;  // 建议使用EffectID来获取Effect, 避免使用 Particle
    std::string Path;
    float Scale;
    bool IsChangeScale;
    IceCube() :BuffId(0), Particle(nullptr), Path(""), Scale(0), IsChangeScale(false), EffectID(0){}
    IceCube(int buffId, EffectParticle* particle, std::string path, float scale, bool isChangeScale);
    void setParticle(EffectParticle* particle);
};

struct ScreenDecal
{
    int  BuffId;
    int  ImgId;
    bool IsAlpha;
    ScreenDecal(int buffId, int  imgId, bool isAlpha)
        :BuffId(buffId), ImgId(imgId), IsAlpha(isAlpha) {}
};

struct ModelTexture
{
    int BuffId;
    float Value;
    ModelTexture(int buffId, float value)
        :BuffId(buffId), Value(value) {}
};

class World;

class TemperatureComponent : public ActorComponentBase 
{
public:
	DECLARE_COMPONENTCLASS(TemperatureComponent)

	TemperatureComponent();

	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

    void Update();
   
    void RenderUI();

    void GetRealTemperature();
    World* GetWorld();

    TCBuffType GetBuffType(float temp);
    TCBuffType GetBuffTypeSOC(float temp);

    void AddBuff(int buffId);
    void Remove(int buffId);
    void RemoveAllPermanentBuff();

    void UpdateScreenDecals();
    void SetScreenDecals(int buffId,bool isScreenDecals, int imgId = 0, bool isAlpha = false);

    void UpdateModelTexture();
    void UpdateWeaponTexture(); //刷新手持道具
    void ChangeModelTexture(int buffId,bool isChange,bool isTransition = false, float minValue = 0, float maxValue = 0);

    void UpdateIceCubes();
    void Freeze(int buffId, int particleId, float modelScale, bool isChangeScale);
    void Unfreeze(int buffId);
    void ClearAllIceCubes();
    
    void UpdateChangeTemperature();
    void ChangeTemperature(float value,int interval,bool isSuitableTemperature = false);

    // 1062 buff 温度影响持续时间
    void UpdateTempImpactDuration();
    void AddTempImpactDuration(TempImpactDuration tempImpactDuration);
    void RemoveTempImpactDuration(int buffId);
    void ShakeImpactDuration();
    void PlayerShake();
    bool PlayerCameraCanMove();

    void UpdatePauseCurrentFrame();
    void PauseCurrentFrame(bool isPause);

    void GetTextureAlpha();

	~TemperatureComponent();
protected:
    void OnInit();
    
private:
    bool       m_IsInit;
    bool       m_IsAffectedTemperature;
    bool       m_TestSwitch;
	float      m_TemperatureValue;
	TCBuffType m_CurrentBuff;
    std::map<TCBuffType, int> m_PermanentBuffId;
    std::map<TCBuffType, int> m_TriggerBuffId;
    std::map<TCBuffType, int> m_BuffTextureAlpha;

    Rainbow::SharePtr<Rainbow::Texture2D>  m_Texture;
    Rainbow::SharePtr<Rainbow::MaterialInstance> m_TextureMaterial;
    int  m_TextureAlpha;
    bool m_IsTextureAlpha;
    bool m_IsChangeScreenDecal;
    std::vector<ScreenDecal>m_ScreenDecals;

    std::vector<IceCube>m_IceCubes;

    std::vector<ModelTexture>m_ModelTextures;
    bool m_IsChangeModelTexture;
    float m_CMTMaxValue;
    float m_CMTValue;

    int m_LastPermanentBuffId;
    int m_LastTriggerBuffId;

    bool m_IsSuitableTemperature;
    float  m_ChangeTemperatureValue;
    int  m_ChangeTemperatureTicks;
    int  m_ChangeTemperatureTickMark;
  
    std::vector<TempImpactDuration> m_TempImpactDuration;
    bool m_IsPlayerShake;
    bool m_IsPlayerCameraCanMove;

    World* m_PWord;

    bool m_ChangeIsPauseCurrentFrame;
    bool m_PauseCurrentFrame;

    fairygui::GLoader* m_gloader;
    cocos2d::Rect m_Rect;


};

#endif