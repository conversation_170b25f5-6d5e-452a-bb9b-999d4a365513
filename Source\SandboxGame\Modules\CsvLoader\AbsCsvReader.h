#ifndef __ABS_CSV_READER_H__
#define __ABS_CSV_READER_H__ 1
#include "CsvParser.h"

class ICsvLoadConfig;

using namespace MINIW;

class AbsCsvReader { //tolua_exports
	
protected:

    /**
    @brief 获取当前csv表格的无后缀名字
    */
    virtual const char* getName() = 0;
	
    /**
    @brief 获取文件路径。按需重写。如filterstring、random_names、minicoin等
    */
	virtual void getPath(char filepath[64]);

public:

    /**
    @brief 加载。根据配置获取文件路径、判空、判断是否加载等统一操作。
    */
    bool load();

    bool reload();

    bool hasLoaded();

    /**
    @brief 具体解析逻辑
    */
    virtual void onParse(CSVParser& parser) = 0;

    /**
    @brief 清理table
    */
    virtual void onClear() = 0;
	
    /**
    @brief 类名和暴露给Lua的类的名字。供统一注册Lua接口使用。
    */
    virtual const char* getClassName() = 0;

    virtual ~AbsCsvReader();
	
    /**
    @brief 多语言字串读取
    */
	static const char* ColumnLang(const MINIW::CSVParser::TableLine &row, const char *col);

	static void setLanguage(int language);

protected:

    AbsCsvReader();
    
private:

    volatile bool m_bHasLoaded;

	static int s_CurLanguage;
	
	static const char* s_LandPrefix[];

     
}; //tolua_exports
#endif//__ABS_CSV_READER_H__