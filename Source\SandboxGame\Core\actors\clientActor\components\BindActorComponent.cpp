#include "BindActorComponent.h"
#include "ClientActorManager.h"
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "world.h"
#include "MpActorManager.h"
#include "GameNetManager.h"

#include "ActorAttrib.h"
#include "ActorBody.h"
#include "ActorBall.h"
#include "LivingAttrib.h"
#include "LivingLocoMotion.h"//SouvenirMotion
#include "ActorBasketBall.h"//BasketBallLocoMotion
#include "PhysicsLocoMotion.h"
#include "BallLocomotion.h"
#include "BasketBallLocoMotion.h"
#include "ClientItem.h"
#include "BaseItemMesh.h"
#include "Entity/OgreEntity.h"
#include <assert.h>
#include "ActorPushSnowBall.h"
#include "PushSnowBallLocomotion.h"
#include "ActorManager.h"
IMPLEMENT_COMPONENTCLASS(BindActorComponent)

using namespace MNSandbox;

BindActorComponent::BindActorComponent()
:BaseTargetComponent()
{
	CreateEvent2();
}

BindActorComponent::~BindActorComponent()
{
	DestroyEvent2();
}
void BindActorComponent::CreateEvent2()
{
	typedef ListenerFunctionRef<int, int*> Listener1;
	m_listenerBindActor1 = SANDBOX_NEW(Listener1, [&](int count , int* uinList) -> void {
		if ( this->getTarget() != NULL)
		{
			for (int i = 0; i < count; ++i)
			{
				this->sendBindMsg(uinList[i]);
			}
		}
		});
	Event2().Subscribe("BindActor_sendMsg", m_listenerBindActor1);


	typedef ListenerFunctionRef<IClientActor*> Listener2;
	Listener2* listener2 = SANDBOX_NEW(Listener2, [&](IClientActor* actor) -> void {
		actor = (this->getTarget());
		});
	Event2().Subscribe("BindActor_getTarget", listener2);
}

void BindActorComponent::DestroyEvent2()
{
	SANDBOX_RELEASE(m_listenerBindActor1);
}
ClientActor* BindActorComponent::getCatchBall()
{
	if (!GetOwner()) return nullptr;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return nullptr;
	World *pWorld = pOwner->getWorld();
	if(pWorld == nullptr)
		return nullptr;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
		if (!actorMgr) return nullptr;
	size_t len = m_BindChildren.size();
	for (size_t i = 0; i < len; i++)
	{
		ClientActor *actor =actorMgr->findActorByWID(m_BindChildren[i]);
		if (dynamic_cast<ActorBall *>(actor) || dynamic_cast<ActorBasketBall*>(actor) || dynamic_cast<ActorPushSnowBall*>(actor))
			return actor;
	}
	return nullptr;
}

ClientActor* BindActorComponent::getCatchGravityActor()
{
	if (!GetOwner()) return nullptr;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return nullptr;
	World* pWorld = pOwner->getWorld();
	if (pWorld == nullptr)
		return nullptr;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
	if (!actorMgr) return nullptr;
	size_t len = m_BindChildren.size();
	for (size_t i = 0; i < len; i++)
	{
		ClientActor *actor = actorMgr->findActorByWID(m_BindChildren[i]);
		if (actor)
		{
			PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
			if (loc && loc->m_PhysActor)
			{
				return actor;
			}
		}
	}
	return nullptr;	
}

void BindActorComponent::setBindInfo(WORLD_ID objIdBind, const WCoord &bindPos, bool bindoperate)
{//ClientActor::setBindInfo
	if (!GetOwner()) return ;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return ;
	World* pWorld = pOwner->getWorld();
	if (pWorld == nullptr)
		return ;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
	if (!actorMgr) return ;

	if (!pWorld->isRemoteMode() || bindoperate)		//单机主机或者是客机收到了主机通知能绑定了，则绑定
	{
		setUnbindPos();
		bool unbind = objIdBind < 0;
		m_Target = unbind ? 0 : objIdBind;
		m_OffetBind = bindPos;
		WORLD_ID actorId = unbind ? -objIdBind : objIdBind;
		auto binding = actorMgr->findActorByWID(actorId);
		if (binding)
		{
			auto bindAComponent = binding->getBindActorCom();
			if (bindAComponent)
			{
				bindAComponent->setBindChildren(pOwner->getObjId(), unbind);
			}
		}
	}	

	if (!pWorld->isRemoteMode())
	{
		PB_ActorBindHC actorBindHC;
		actorBindHC.set_actorid(pOwner->getObjId());
		actorBindHC.set_actoridbind(objIdBind);
		actorBindHC.mutable_offetbind()->set_x(bindPos.x);
		actorBindHC.mutable_offetbind()->set_y(bindPos.y);
		actorBindHC.mutable_offetbind()->set_z(bindPos.z);
		pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_BIND_HC, actorBindHC, pOwner, true);
	}	
}

void BindActorComponent::unbind()
{//MpGameSurvive::onPlayerLeave
	//GravityGunUseState::update 100 ,[163]
	if (!GetOwner()) return;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return;
	World* pWorld = pOwner->getWorld();
	if (pWorld == nullptr)
		return;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
	if (!actorMgr) return;

	for (unsigned int i=0; i< m_BindChildren.size(); i++)
	{
		auto binding = actorMgr->findActorByWID(m_BindChildren[i]);
		if (binding)
		{
			auto bindAComponent = binding->getBindActorCom();
			if (bindAComponent)
			{
				bindAComponent->setBindInfo(-pOwner->getObjId(), WCoord(0, 0, 0));
			}
		}
	}
}

bool BindActorComponent::unbindWithCheckPhys()
{
	if (!GetOwner()) return false;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return false;
	World* pWorld = pOwner->getWorld();
	if (pWorld == nullptr)
		return false;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
	if (!actorMgr) return false;
	for (unsigned int i=0; i< m_BindChildren.size(); i++)
	{
		auto binding = actorMgr->findActorByWID(m_BindChildren[i]);
		if (binding){
			auto bindAComponent = binding->getBindActorCom();
			if (bindAComponent)
			{
				bindAComponent->setBindInfo(-pOwner->getObjId(), WCoord(0, 0, 0));
			}

			PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(binding->getLocoMotion());
			if (loc && loc->m_hasPhysActor)
			{
				return true;
			}			
		}
	}
	return false;
}

bool BindActorComponent::hasBindChildren()
{//ActionIdleState::doSkillUse 263,270
//ActionIdleState::updateForCurrentTool  2022
	return m_BindChildren.size() > 0;
}

void BindActorComponent::updatePosByBindActor()
{
	ClientActor *binding = getTarget();
	if (!GetOwner()) return;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return;

		if (binding)
		{
			ActorLocoMotion *locomotion = binding->getLocoMotion();
			ActorLocoMotion* mLocomotion = pOwner->getLocoMotion();
			if (locomotion && mLocomotion)
			{
				mLocomotion->m_Motion.Set(0, 0, 0);
				WCoord pos = locomotion->getPosition() + m_OffetBind;
				mLocomotion->setPosition(pos.x, pos.y, pos.z);
			}
		}
	//}	
}

/*
ClientPlayer* BindActorComponent::getBindPlayer()
{
	ClientActor* actor = getTarget();
	if(actor)
		return dynamic_cast<ClientPlayer*>(actor);
	return NULL;
}
*/

void BindActorComponent::sendBindMsg(int uin)//PB_ACTOR_BIND_HC
{
	if (!GetOwner()) return;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return;
	PB_ActorBindHC actorBindHC;
	actorBindHC.set_actorid(pOwner->getObjId());
	actorBindHC.set_actoridbind(m_Target);
	PB_Vector3* offetBind = actorBindHC.mutable_offetbind();
	offetBind->set_x(m_OffetBind.x);
	offetBind->set_y(m_OffetBind.y);
	offetBind->set_z(m_OffetBind.z);
	GameNetManager::getInstance()->sendToClient(uin, PB_ACTOR_BIND_HC, actorBindHC);
}	
void BindActorComponent::setBindChildren(WORLD_ID objId, bool unbind)
{
	if (unbind)
	{
		for (auto it = m_BindChildren.begin(); it != m_BindChildren.end(); it++)
		{
			if (*it == objId)
			{
				m_BindChildren.erase(it);
				break;
			}
		}
	}
	else
	{
		m_BindChildren.push_back(objId);
	}
}


int BindActorComponent::bindItemToAnchor(int itemId, int anchorId)
{
	if (!GetOwner()) return -1;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return -1;
	auto* pWorld = pOwner->getWorld();
	if (!pWorld)
	{
		return -1;
	}

	m_bindUId++;
	int bindId = m_bindUId;

	BaseItemMesh* obj = nullptr;

#ifndef IWORLD_SERVER_BUILD
	obj = ClientItem::createItemModel(itemId, ITEM_MODELDISP_SCENE);
#endif

	unbindItem(bindId);
	m_BindItems[bindId] = obj;

	if (obj)
	{
		pOwner->getBody()->getEntity()->BindObject(anchorId, obj);
	}

	if (!pWorld->isRemoteMode())	// 主机
	{
		PB_BindItemToActorHC pbHC;
		pbHC.set_playerid(pOwner->getObjId());
		pbHC.set_isbind(true);
		pbHC.set_bindid(bindId);
		pbHC.set_itemid(itemId);
		pbHC.set_anchorid(anchorId);
		GameNetManager::getInstance()->sendBroadCast(PB_BIND_ITEM_TO_ACTOR_HC, pbHC);
	}

	return bindId;
}

void BindActorComponent::unbindItem(int bindId)
{
	if (!GetOwner()) return ;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return ;
	World* pWorld = pOwner->getWorld();
	if (pWorld == nullptr)
		return ;

	auto it = m_BindItems.find(bindId);
	if (it != m_BindItems.end())
	{
		auto* obj = it->second;
		if (obj)
		{
			pOwner->getBody()->getEntity()->UnbindObject(obj);
			DESTORY_GAMEOBJECT_BY_COMPOENT(obj);
		}
		m_BindItems.erase(it);
	}

	if (!pWorld->isRemoteMode())	// 主机
	{
		PB_BindItemToActorHC pbHC;
		pbHC.set_playerid(pOwner->getObjId());
		pbHC.set_isbind(false);
		pbHC.set_bindid(bindId);
		GameNetManager::getInstance()->sendBroadCast(PB_BIND_ITEM_TO_ACTOR_HC, pbHC);
	}
}

void BindActorComponent::unbindAllItems()
{
	if (!GetOwner()) return ;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return ;

	for (auto it = m_BindItems.begin(); it != m_BindItems.end(); it++)
	{
		auto* obj = it->second;
		if (obj)
		{
			pOwner->getBody()->getEntity()->UnbindObject(obj);
			DESTORY_GAMEOBJECT_BY_COMPOENT(obj);
		}
	}
	m_BindItems.clear();
}

void BindActorComponent::bindItemFromHost(int bindId, int itemId, int anchorId)
{
	if (!GetOwner()) return ;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return ;

	unbindItem(bindId);

	m_bindUId = max(m_bindUId, bindId + 1);

	BaseItemMesh* obj = nullptr;
	// 其实这里不会在云服上跑。。
#ifndef IWORLD_SERVER_BUILD
	obj = ClientItem::createItemModel(itemId, ITEM_MODELDISP_SCENE);
#endif

	m_BindItems[bindId] = obj;
	if (obj)
	{
		pOwner->getBody()->getEntity()->BindObject(anchorId, obj);
	}
}

void BindActorComponent::unbindItemFromHost(int bindId)
{
	unbindItem(bindId);
}

BaseItemMesh* BindActorComponent::getBindItem(int bindId)
{
	auto it = m_BindItems.find(bindId);
	if (it != m_BindItems.end())
	{
		return it->second;	// 云服上可能为NULL的
	}
	return nullptr;
}

IMPLEMENT_COMPONENTCLASS(ActorBallComponent)

ActorBallComponent::ActorBallComponent()
:BindActorComponent()
{

}

void ActorBallComponent::setBindInfo(WORLD_ID objIdBind, const WCoord &bindPos, bool bindoperate)
{//ActorBall::setBindInfo
	if (!GetOwner()) return ;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return ;
	World* pWorld = pOwner->getWorld();
	if (pWorld == nullptr)
		return ;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
	if (!actorMgr) return ;
	BindActorComponent::setBindInfo(objIdBind, bindPos, bindoperate);

	if (!pWorld->isRemoteMode() || bindoperate)		//单机主机或者是客机收到了主机通知能绑定了，则绑定
	{
		WORLD_ID actorId = objIdBind < 0 ? -objIdBind : objIdBind;
		auto binding = actorMgr->findActorByWID(actorId);
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(binding);
		if (player)
		{
			//	player->getBody()->setNeedUpdateBallEffect(true);
			player->updateBallEffect();
			if (!pWorld->isRemoteMode())
			{
				if (objIdBind > 0)
				{
					player->getLivingAttrib()->removeBuff(FOOTBALLWAY_BUFF);
					player->getLivingAttrib()->addBuff(FOOTBALLWAY_BUFF, 2);
				}
				else
				{
					player->getLivingAttrib()->removeBuff(FOOTBALLWAY_BUFF);
					player->getLivingAttrib()->addBuff(FOOTBALLWAY_BUFF, 1);
				}
			}
		}
	}

#ifdef USE_PHYSX
	auto locomotion = pOwner->getLocoMotion();
	if(objIdBind > 0)
	{
		static_cast<BallLocoMotion *>(locomotion)->detachPhysActor();
	}
	else
	{
		static_cast<BallLocoMotion *>(locomotion)->attachPhysActor();
	}
#endif	
}

//////////////////////////////////////////
IMPLEMENT_COMPONENTCLASS(ActorBasketBallComponent)

ActorBasketBallComponent::ActorBasketBallComponent()
:BindActorComponent()
{

}

void ActorBasketBallComponent::setBindInfo(WORLD_ID objIdBind, const WCoord &bindPos, bool bindoperate)
{//ActorBasketBall::setBindInfo 
	if (!GetOwner()) return ;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return ;
	World* pWorld = pOwner->getWorld();
	if (pWorld == nullptr)
		return ;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
	if (!actorMgr) return ;
	BindActorComponent::setBindInfo(objIdBind, bindPos, bindoperate);
	m_ownerBasketBall = GetOwnerT<ActorBasketBall>();
	if (!pWorld->isRemoteMode() || bindoperate)		//单机主机或者是客机收到了主机通知能绑定了，则绑定
	{
		WORLD_ID actorId = objIdBind < 0 ? -objIdBind : objIdBind;
		auto binding = actorMgr->findActorByWID(actorId);
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(binding);		
		m_ownerBasketBall->onBindPlayer(player, objIdBind > 0);
	}

#ifdef USE_PHYSX
	auto loco = dynamic_cast<BasketBallLocoMotion *>(pOwner->getLocoMotion());
	if (!loco) { return; }//
	if(objIdBind > 0)
	{
		m_ownerBasketBall->resetBallRotation();
		loco->detachPhysActor();
	}
	else
	{
		loco->attachPhysActor();
	}
#endif	
}

void ActorBasketBallComponent::leaveWorld(bool keep_inchunk)
{//ActorBasketBall::leaveWorld
	if (!GetOwner()) return;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return;
#ifdef USE_PHYSX
	static_cast<BasketBallLocoMotion*>(pOwner->getLocoMotion())->detachPhysActor();
#endif
	auto bindingActor = getTarget();
	if (bindingActor)
	{
		auto bindAComponent = bindingActor->getBindActorCom();
		if (bindAComponent)
		{
			bindAComponent->setBindChildren(pOwner->getObjId(), true);
		}
	}	
}

void ActorBasketBallComponent::setUnbindPos()
{
	m_ownerBasketBall = GetOwnerT<ActorBasketBall>();
	m_ownerBasketBall->adjustBallPos(m_ownerBasketBall->m_iResult);
}


///////////////////////////ActorPushSnowBallComponent
IMPLEMENT_COMPONENTCLASS(ActorPushSnowBallComponent)

ActorPushSnowBallComponent::ActorPushSnowBallComponent()
	:BindActorComponent()
{

}

void ActorPushSnowBallComponent::setBindInfo(WORLD_ID objIdBind, const WCoord& bindPos, bool bindoperate)
{
	if (!GetOwner()) return ;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return ;
	World* pWorld = pOwner->getWorld();
	if (pWorld == nullptr)
		return ;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());
	if (!actorMgr) return ;

	BindActorComponent::setBindInfo(objIdBind, bindPos, bindoperate);

	if (!pWorld->isRemoteMode() || bindoperate)		//单机主机或者是客机收到了主机通知能绑定了，则绑定
	{
		WORLD_ID actorId = objIdBind < 0 ? -objIdBind : objIdBind;
		auto binding = actorMgr->findActorByWID(actorId);
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(binding);
		if (player)
		{
			player->updateBallEffect();		
			if (!pWorld->isRemoteMode())
			{
				if (objIdBind > 0)
				{
					player->getLivingAttrib()->removeBuff(PUSHSNOWBALLWAY_BUFF);
					player->getLivingAttrib()->addBuff(PUSHSNOWBALLWAY_BUFF, 2);
				}
				else
				{
					player->getLivingAttrib()->removeBuff(PUSHSNOWBALLWAY_BUFF);
					player->getLivingAttrib()->addBuff(PUSHSNOWBALLWAY_BUFF, 1);
				}
			}
		}
	}

#ifdef USE_PHYSX
	auto locomotion = pOwner->getLocoMotion();
	if (objIdBind > 0)
	{
		static_cast<PushSnowBallLocomotion*>(locomotion)->detachPhysActor();
	}
	else
	{		
		Rainbow::Vector3f motion(0, 0, 0);
		auto binding = actorMgr->findActorByWID(-objIdBind);
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(binding);
		if (player)
		{
			motion = player->getLocoMotion()->m_Motion;		
		}
		PushSnowBallLocomotion* loco = static_cast<PushSnowBallLocomotion*>(locomotion);
		if (loco)
		{
			loco->attachPhysActor();
			if (loco->m_PhysActor)
			{
				loco->m_PhysActor->SetLinearVelocity(motion);
			}			
		}		
	}
#endif	
}

//////////////////////////////////////////
IMPLEMENT_COMPONENTCLASS(ClientSouvenirComponent)

ClientSouvenirComponent::ClientSouvenirComponent()
:BindActorComponent()
{

}

void ClientSouvenirComponent::leaveWorld(bool keep_inchunk)
{//ClientSouvenir::leaveWorld
	if (!GetOwner()) return ;
	ClientActor* pOwner = GetOwner()->ToCast<ClientActor>();
	if (!pOwner) return ;
	#ifdef USE_PHYSX
	static_cast<SouvenirMotion *>(pOwner->getLocoMotion())->detachPhysActor();
	#endif
	auto bindingActor = getTarget();
	if (bindingActor)
	{
		auto bindAComponent = bindingActor->getBindActorCom();
		if (bindAComponent)
		{
			bindAComponent->setBindChildren(pOwner->getObjId(), true);
		}
	}	
}
