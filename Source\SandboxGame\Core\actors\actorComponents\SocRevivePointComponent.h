#pragma once

#include "ActorComponent_Base.h"

#include "SandboxGame.h"

class EXPORT_SANDBOXGAME SocRevivePointComponent : public ActorComponentBase //tolua_exports
{//tolua_exports
	DECLARE_COMPONENTCLASS(SocRevivePointComponent)
protected:
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
public:
	SocRevivePointComponent();
	~SocRevivePointComponent();
	
	void OnNetMessage(const std::string& data);

	void OnPlayerEnter();

	//tolua_begin
	void WritePoint(const WCoord& pos);

	void RemovePoint(const WCoord& pos);

	void SelectPosint(int x,int y,int z);

	void GivePosint(int x, int y, int z);

	inline WCoord GetSelectPos() {
		m_isselect = false;
		return m_selectPos;
	};

	inline bool IsSelect() {
		return m_isselect;
	};

	std::string GetPoints();

	void ReqAllPosints();

	//tolua_end

	virtual void OnTick() override;
private:
	//server
	void OnCheckPosition(int x, int y, int z);
	void OnReqAllPosits();
	void OnGivePosint(int x, int y, int z);

	//client
	void OnAddPosition(int x, int y, int z,int time,int itemid);
	void OnDelPosition(int x, int y, int z);
	void OnCheckPositionRet(int ret,const std::string &msg);
	void OnReqAllPointsRet(const std::string &posints);
private:
	MNSandbox::SandboxResult SANDBOXAPI interactBlockEnd(MNSandbox::SandboxContext context);

protected:
	std::vector<WCoord> m_positions;
	WCoord m_selectPos;
	bool m_isselect;
};//tolua_exports