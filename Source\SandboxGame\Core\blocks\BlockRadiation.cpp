#include "BlockRadiation.h"
#include "BlockMaterialMgr.h"
#include "world.h"
#include "section.h"
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "worldData/WorldManager.h"
#include "Environment/RadiationManager.h"
#include "SandboxGame.h"
#include "EventHandleEx.h"
#include "EffectParticle.h"
#include "chunk.h"
#include "block_tickmgr.h"

IMPLEMENT_BLOCKMATERIAL(BlockRadiation)

// 反射系统属性实现
IMPLEMENT_BLOCKMATERIAL_INSTANCE_BEGIN(BlockRadiation)
    IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(BlockRadiation, R_Radiation, float)(0, "Radiation", "Data", &BlockRadiationInstance::GetRadiationValue, &BlockRadiationInstance::SetRadiationValue);
    IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(BlockRadiation, R_RadiationRadius, int)(1, "RadiationRadius", "Data", &BlockRadiationInstance::GetRadiationRadiusValue, &BlockRadiationInstance::SetRadiationRadiusValue);
    IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(BlockRadiation, R_RadiationEnabled, bool)(2, "RadiationEnabled", "Data", &BlockRadiationInstance::GetRadiationEnabledValue, &BlockRadiationInstance::SetRadiationEnabledValue);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_END()

BlockRadiation::BlockRadiation()
    : m_RadiationSourceId(-1)
    , m_RadiationEnabled(true)
    , m_TickInterval(60)
{
    // 设置方块基本属性
    SetToggle(BlockToggle_RandomTick, true);     // 启用随机tick
    SetToggle(BlockToggle_CanTickImmediate, false);
    SetToggle(BlockToggle_IsSolid, true);        // 设为固体方块
    SetToggle(BlockToggle_IsOpaqueCube, true);   // 不透明
}

BlockRadiation::~BlockRadiation()
{
}

void BlockRadiation::init(int resid)
{
    ModelBlockMaterial::init(resid);
    
    m_tickInterval = 60;
}

void BlockRadiation::initGeomName()
{
    m_geomName = "radiation_block";
}

void BlockRadiation::blockTick(World* pworld, const WCoord& blockpos)
{
    if (!pworld || !pworld->getBlockTickMgr()) { return; }
    
    // 检查辐射管理器是否激活
    bool isRadiationActive = pworld->GetWorldMgr()->getRadiationMgr()->GetRadiationActive();
    int iBlockData = pworld->getBlockData(blockpos);
    //bool isEnabled = (iBlockData & 1) > 0;
    
    // 如果辐射功能已激活，检查环境条件
    if (isRadiationActive)
    {
        // 随机触发辐射激活检查
        if (GenRandomInt(100) < 50) // 50%几率触发检查
        {
            // int blockId = pworld->getBlockID(blockpos);
            // BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockId);

            // // 获取方块实例
            // if (material) {
            //     MNSandbox::AutoRef<BlockMaterial::BlockInstance> instance = material->GetBlockInstance();
                
            //     // 转换为辐射方块实例
            //     BlockRadiation::BlockInstance* radiationInstance = dynamic_cast<BlockRadiation::BlockInstance*>(instance.get());
            //     if (radiationInstance) {
            //         //float radiation = radiationInstance->GetRadiationValue();
            //         radiationInstance->SetRadiationValue(100.0f);
            //     }
            // }

            // float rad = 0.0f;
            // int level = 0;
            // pworld->GetWorldMgr()->getRadiationMgr()->GetBlockRadiationAndLevel(pworld, blockpos, rad, level);
            
            // // 如果环境辐射已经很高，有几率自动激活辐射源
            // if (level >= 3) // 高辐射或更高
            // {
            //     if (GenRandomInt(100) < 20) // 20%几率激活
            //     {
            //         EnableRadiation(pworld, blockpos);
            //         iBlockData |= 1; // 设置启用位
            //         pworld->setBlockAll(blockpos, getBlockResID(), iBlockData);
            //     }
            // }
        }
    }
    
    // 检查是否可以在当前环境中辐射
    if (m_RadiationEnabled && !CanRadiateInEnvironment(pworld, blockpos))
    {
        // 环境不允许辐射，禁用辐射
        DisableRadiation(pworld, blockpos);
        iBlockData &= ~1; // 清除启用位
        pworld->setBlockAll(blockpos, getBlockResID(), iBlockData);
    }
    else if (m_RadiationEnabled)
    {
        // 更新辐射效果
        UpdateRadiationEffect(pworld, blockpos);
    }
    
    // 重新安排下一次tick
    pworld->getBlockTickMgr()->scheduleBlockUpdate(blockpos, getBlockResID(), getTickInterval() + GenRandomInt(20));
}

void BlockRadiation::onBlockAdded(World* pworld, const WCoord& blockpos)
{
    ModelBlockMaterial::onBlockAdded(pworld, blockpos);
    
    Chunk* pchunk = pworld->getChunk(blockpos);
    if (!pchunk) { return; }
    
    WCoord offset = blockpos - pchunk->m_Origin;
    pchunk->addSearchBlock(offset.x, offset.y, offset.z, getBlockResID());
    
    if (m_RadiationEnabled)
    {
        BlockDef* blockdef = GetDefManagerProxy()->getBlockDef(getBlockResID());
        if (blockdef)
        {
            if (blockdef->TempSrc > 0)
            {
                LOG_INFO("BlockRadiation::onBlockAdded: pos(%d, %d, %d), rad(%d)", blockpos.x, blockpos.y, blockpos.z, blockdef->TempSrc);
                RegisterWithRadiationManager(pworld, blockpos, blockdef->TempSrc, blockdef->TempAtten);
            }
        }
    }
    
    pworld->getBlockTickMgr()->scheduleBlockUpdate(blockpos, getBlockResID(), getTickInterval());
}

void BlockRadiation::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
    ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
    
    Chunk* pchunk = pworld->getChunk(blockpos);
    if (!pchunk) { return; }
    
    // 停止辐射效果
    WCoord effectPos = BlockCenterCoord(blockpos) + WCoord(0, -20, 0);
    StopRadiationEffect(pworld, effectPos);
    
    WCoord offset = blockpos - pchunk->m_Origin;
    pchunk->removeSearchBlock(offset.x, offset.y, offset.z, getBlockResID());
    
    UnregisterFromRadiationManager(pworld, blockpos);
}

void BlockRadiation::onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho)
{
    // 从辐射管理器注销
    UnregisterFromRadiationManager(pworld, blockpos);
    
    // 调用基类方法
    ModelBlockMaterial::onBlockDestroyedBy(pworld, blockpos, blockdata, destroytype, bywho);
}

bool BlockRadiation::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
    if (!pworld || !player) { return false; }
    if (pworld->isRemoteMode()) return true;
    
    // 切换辐射状态
    ToggleRadiation(pworld, blockpos);
    
    // 更新方块数据
    int iBlockData = pworld->getBlockData(blockpos);
    iBlockData = m_RadiationEnabled ? (iBlockData | 1) : (iBlockData & ~1);
    pworld->setBlockAll(blockpos, getBlockResID(), iBlockData);
    
    return true;
}

void BlockRadiation::onPlayRandEffect(World* pworld, const WCoord& blockpos)
{
    if (!pworld) { return; }
    
    EffectManager* pEffectManager = pworld->getEffectMgr();
    if (!pEffectManager) { return; }
    
    // 如果辐射已启用，显示粒子效果
    WCoord effectPos = BlockCenterCoord(blockpos) + WCoord(0, -20, 0);
    
    if (m_RadiationEnabled)
    {
        EffectParticle* pLastParticle = pEffectManager->getParticle(effectPos);
        
        // 辐射效果的名称
        Rainbow::FixedString effect_name = "prefab/particles/radiation_effect.prefab";
        
        if (pLastParticle == nullptr || pLastParticle->getName() != effect_name)
        {
            EffectParticle* pParticle = pEffectManager->playParticleEffectAsync(effect_name.c_str(), effectPos, 0, 0, 0, false);
            if (pParticle != nullptr)
            {
                pEffectManager->addBlockParticle(effectPos, pParticle);
            }
            
            if (pLastParticle != NULL) 
                pEffectManager->stopParticleEffect(pLastParticle->getName().c_str(), effectPos);
        }
        
        // 播放辐射声音效果
        if (GenRandomInt(5) == 0)
        {
            pworld->getEffectMgr()->playMusicGrid(CoordDivBlock(BlockCenterCoord(blockpos)), "misc.radiation", 1.0f + GenRandomFloat(), false, 0);
        }
    }
    else
    {
        // 停止辐射效果
        StopRadiationEffect(pworld, effectPos);
    }
}

bool BlockRadiation::canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)
{
    return pworld->doesBlockHaveSolidTopSurface(DownCoord(blockpos));
}

unsigned char BlockRadiation::getBlockLightSrc(int iBlockData)
{
    // 如果辐射已启用，提供光源
    if ((iBlockData & 1) > 0) 
    {
        return 8; // 默认光源亮度
    }
    
    return 0;
}

void BlockRadiation::UpdateRadiationEffect(World* pworld, const WCoord& blockpos)
{
    if (!pworld)
        return;
    
    RadiationManager* radMgr = static_cast<RadiationManager*>(pworld->GetWorldMgr()->getRadiationMgr());
    if (!radMgr)
        return;
    
    //// 如果辐射源ID无效且辐射已启用，重新注册
    //if (m_RadiationSourceId < 0 && m_RadiationEnabled)
    //{
    //    RegisterWithRadiationManager(pworld, blockpos);
    //}
}

void BlockRadiation::EnableRadiation(World* pworld, const WCoord& blockpos)
{
    if (!m_RadiationEnabled)
    {
        m_RadiationEnabled = true;
        BlockDef* blockdef = GetDefManagerProxy()->getBlockDef(getBlockResID());
        if (blockdef)
        {
            if (blockdef->TempSrc > 0)
            {
                RegisterWithRadiationManager(pworld, blockpos, blockdef->TempSrc, blockdef->TempAtten);
            }
        }
    }
}

void BlockRadiation::DisableRadiation(World* pworld, const WCoord& blockpos)
{
    if (m_RadiationEnabled)
    {
        m_RadiationEnabled = false;
        UnregisterFromRadiationManager(pworld, blockpos);
    }
}

void BlockRadiation::ToggleRadiation(World* pworld, const WCoord& blockpos)
{
    if (m_RadiationEnabled)
    {
        DisableRadiation(pworld, blockpos);
    }
    else
    {
        EnableRadiation(pworld, blockpos);
    }
}

bool BlockRadiation::CanRadiateInEnvironment(World* pworld, const WCoord& blockpos)
{
    if (!pworld) return false;
    
    //// 检查是否在水下 - 水可能会抑制辐射
    //if (pworld->isBlockWater(blockpos))
    //{
    //    // 水可能减弱辐射，但不会完全阻止
    //    return GenRandomInt(100) < 50; // 50%几率在水下继续辐射
    //}
    
    // 检查其他环境条件，例如是否有特定方块在附近
    // 例如，某些特殊方块可能会阻断辐射
    
    // 默认允许辐射
    return true;
}

void BlockRadiation::StopRadiation(World* pworld, const WCoord& blockpos)
{
    if (!pworld) { return; }
    
    BlockRadiation* radiation = dynamic_cast<BlockRadiation*>(g_BlockMtlMgr.getMaterial(pworld->getBlockID(blockpos)));
    if (!radiation) { return; }
    
    radiation->DisableRadiation(pworld, blockpos);
    
    // 更新方块数据
    int iBlockData = pworld->getBlockData(blockpos);
    iBlockData &= ~1; // 清除启用标志位
    pworld->setBlockAll(blockpos, radiation->getBlockResID(), iBlockData);
}

void BlockRadiation::StopRadiationEffect(World* pworld, const WCoord& effectpos)
{
    if (!pworld) return;
    
    EffectManager* pEffectManager = pworld->getEffectMgr();
    if (!pEffectManager) return;
    
    EffectParticle* pParticle = pEffectManager->getParticle(effectpos);
    if (pParticle != NULL)
    {
        pEffectManager->removeBlockParticle(effectpos, pParticle);
        pEffectManager->stopParticleEffect(pParticle->getName().c_str(), effectpos);
    }
}

void BlockRadiation::RegisterWithRadiationManager(World* pworld, const WCoord& blockpos, int radiation, float decay)
{
    if (!pworld)
        return;
    
    RadiationManager* radMgr = static_cast<RadiationManager*>(pworld->GetWorldMgr()->getRadiationMgr());
    if (!radMgr)
        return;

    long long objid =  (((long long)blockpos.x) << 40) | (((long long)blockpos.y) << 20) | ((long long)blockpos.z);
    m_RadiationSourceId = radMgr->AddMobileRadiationSource(pworld, objid, radiation, decay, blockpos);
}

void BlockRadiation::UnregisterFromRadiationManager(World* pworld, const WCoord& blockpos)
{
    if (!pworld || m_RadiationSourceId < 0)
        return;
    
    RadiationManager* radMgr = static_cast<RadiationManager*>(pworld->GetWorldMgr()->getRadiationMgr());
    if (!radMgr)
        return;
    
    radMgr->RemoveMobileRadiationSource(m_RadiationSourceId);
    m_RadiationSourceId = -1;
}

int BlockRadiation::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
    int iBlockData = sectionData->getBlock(blockpos).getData();
    bool isEnabled = (iBlockData & 1) > 0;
    
    dirbuf[0] = 0;
    idbuf[0] = isEnabled ? 1 : 0;
    
    return 1;
}

int BlockRadiation::getProtoBlockGeomID(int* idbuf, int* dirbuf)
{
    idbuf[0] = 0;
    dirbuf[0] = DIR_NEG_Z;
    return 1;
}

bool BlockRadiation::RadiationEnabledGet(bool& value) const
{
    value = m_RadiationEnabled;
    return true;
}

void BlockRadiation::RadiationEnabledSet(const bool& value)
{
    m_RadiationEnabled = value;
}

// Instance methods
float BlockRadiationInstance::GetRadiationValue() const
{
    return m_Radiation;
}

void BlockRadiationInstance::SetRadiationValue(const float& value)
{
    m_Radiation = value;
}

int BlockRadiationInstance::GetRadiationRadiusValue() const
{
   return m_RadiationRadius;
}

void BlockRadiationInstance::SetRadiationRadiusValue(const int& value)
{
    m_RadiationRadius = value;
}

bool BlockRadiationInstance::GetRadiationEnabledValue() const
{
    return m_RadiationEnabled;
}

void BlockRadiationInstance::SetRadiationEnabledValue(const bool& value)
{
    m_RadiationEnabled = value;
}
