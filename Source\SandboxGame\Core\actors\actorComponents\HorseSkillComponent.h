#pragma once
#include <string>
#include "Common/OgreShared.h"
#include "SandboxObject.h"
#include "Math/Vector3f.h"
#include "ActorBody.h"
#include "SandboxComponent.h"
class ActorHorse;
class ClientActor;

class HorseSkillComponent :public MNSandbox::Object //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(HorseSkillComponent)

	//tolua_begin
	HorseSkillComponent();
	virtual ~HorseSkillComponent();

	//初始化
	bool init(std::string path, ActorHorse* horse);

	bool update(float dTime);

	bool tick();

	//按下空格,空格按住期间开始充能
	bool startCharge();

	//放开空格
	bool endCharge();

	//idx,骨骼id
	bool setRiddenByActor(ClientActor* actor, int idx);

	//更新坐骑动作
	int updateHorseBodyAnim();

	//更新玩家动作
	int updateRiddenBodyAnim(ClientActor* ridden);

	//获取玩家绑点pos
	Rainbow::Vector3f getRiddenBindPos(ClientActor* ridden);

	// 上坐骑
	void OnHorseMounted();

	// 下坐骑
	void OnHorseDismounted();

	//使用技能
	bool useSkill();

	//检查绑定角色
	bool checkBindPlayer(bool isinit);

	//受击
	int attackedFrom(OneAttackData & atkdata, ClientActor * attacker);

	//locoMotionEntiy 检查
	bool moveLocoMotionEntityWithHeading(float strafing, float forward);

	//获取掉落伤害比例
	float getFallHurtRate();

	//播放在液体上行走时的特效
	bool playWalkOnLiquidEffect(bool iswater);

	//是否能在液体方块上行走 0不能 1能
	int canWalkOnLiquid(bool iswater);

	//tolua_end
protected:
	

private:

}; //tolua_exports