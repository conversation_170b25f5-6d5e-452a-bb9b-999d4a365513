#ifndef __RADIATION_MANAGER_H__
#define __RADIATION_MANAGER_H__

/*******************
*	辐射管理器
*	author:: cloud
*******************/

#include "OgreWCoord.h"
#include "OgreBlock.h"
#include "SandboxGame.h"
#include "RadiationManagerInterface.h"
class World;
class Section;
class ClientMob;

#define RADIATION_CHECK_RANGE 300.f

class EXPORT_SANDBOXGAME RadiationManager : public RadiationManagerInterface
{

public:

	// 移动辐射源
	struct MobileSourceRadiationData
	{
		float _val;        // 辐射值
		float _decay;      // 衰减速率
		long long _objid;  // 对象ID	
		WCoord _pos;       // 位置
		MobileSourceRadiationData() : _val(0.f), _decay(0.f), _objid(0), _pos(0, 0, 0) {}
		MobileSourceRadiationData(float val, float decay, long long objid, const WCoord& pos) : _val(val), _decay(decay), _objid(objid), _pos(pos) {}
	};

public:
	RadiationManager();
	virtual ~RadiationManager();
	// RadiationManagerInterface
	virtual void OnTick() override;

	// 获取环境辐射 = 地形 + 高度 + 天气
	float GetEnviromentRadiation(World* world, const WCoord& blockpos);

	// 获取地形辐射
	float GetBiomeRadiation(World* world, const WCoord& blockpos);

	// 获取高度辐射
	float GetHeightRadiation(World* world, const WCoord& blockpos);

	// 获取天气辐射
	float GetWeatherRadiation(World* world, const WCoord& blockpos);

	// 获取移动辐射源辐射
	float GetMobileSourceRadiation(World* world, const WCoord& blockpos);

	// 获取辐射在什么区间
	int GetRadiationLevel(float rad);
	// RadiationManagerInterface

	// 获取辐射区间和在区间最大最小值
	void GetRadiationLevelAndVal(float rad, int& level, float& minVal, float& maxVal);

	// 获取辐射系统活跃状态
	virtual bool GetRadiationActive() override { return m_Active; }

	// 添加区域辐射源(该range范围内固定变化 val 辐射值)
	int AddAreaRadiationSource(World* world, const WCoord& blockpos, float val, int tick = -1);

	// 移除区域辐射源
	bool RemoveAreaRadiationSource(int index);

	// 辐射是否在安全区间
	bool IsRadiationSafe(float rad);

	// 设置辐射系统活跃与否
	void SetRadiationActive(bool val) { m_Active = val; }
	
	// 清理位置辐射缓存
	void ClearPosRadiationCache(World* world, const WCoord& blockpos, int range);

	// 设置辐射源(特殊用法，一般添加使用 AddAreaSourceRadiation 接口）
	void SetAreaRadiationSource(int index, Section* psection);

	// 添加移动辐射源（跟随对象移动）
	int AddMobileRadiationSource(World* world, long long objid, float val, float decay, const WCoord& pos = WCoord(0, 0, 0));

	// 移除移动辐射源
	bool RemoveMobileRadiationSource(int index);

	// 获取某个位置周围半径范围内的所有移动辐射源，并返回累加的辐射值
	float GetRadiationInRange(World* world, const WCoord& blockpos, float radius = RADIATION_CHECK_RANGE);
	
private:

	void PlayerTick();
	void MobTick();
	void SourceTick();

public:
	bool m_ShowData = false;	// GM展示数据

private:
	bool m_Active = true;					// 功能是否开启
	unsigned int m_Tick = 0;

	// 辐射常量配置
	float m_ConstRadiationSafe = 0.f;       // 安全辐射值
	float m_ConstRadiationLow = 10.f;       // 低度辐射值
	float m_ConstRadiationMedium = 30.f;    // 中度辐射值
	float m_ConstRadiationHigh = 60.f;      // 高度辐射值
	float m_ConstRadiationLethal = 100.f;   // 致命辐射值
	float m_ConstWeatherEffect = 0.f;       // 天气对辐射的影响

	std::vector<long long> m_MobList;
	float m_MobListSizeRate = 0.f;
	std::vector<long long> m_PlayerList;
	float m_PlayerListSizeRate = 0.f;

	int m_AreaSourceInfoMapIdx = 0;
	std::unordered_map<char, std::unordered_map<int, WCoord>> m_AreaSourceInfoMap;
	int m_MobileSourceInfoMapIdx = 0;
	std::unordered_map<char, std::unordered_map<int, MobileSourceRadiationData>> m_MobileSourceInfoMap;  // 不会存档
};


#include "PluginManager.h"
#include "Plugin.h"
#include "IPluginBase.h"
#include "EventHandleEx.h"
#include "event/SandboxCallback.h"

class RadiationNetSys : public IPluginBase, public IEventExcuteEx
{
public:
	RadiationNetSys(PluginManager* p);
	virtual ~RadiationNetSys();

	virtual bool Awake();
	virtual bool Init();
	virtual bool Execute(float dtime);
	virtual bool Shut();
	bool CreateModuleEvent();
	void OnExecute(const char* eventname, void* context, int nLen, unsigned long long userdata);
	void OnExecute(unsigned short wEventID, unsigned char bSrcType, unsigned long dwSrcID, char* pszContext, int nLen) {}

private:
	std::map<std::string, MNSandbox::Callback>  m_eventCallbacks;
};

#endif 