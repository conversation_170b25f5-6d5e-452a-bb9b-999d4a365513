#include "ClientAquaticComponent.h"

#include "LivingLocoMotion.h"
#include "world.h"
#include "ActorBody.h"
#include "LuaInterfaceProxy.h"
#include "ClientActorHelper.h"
#include "AttackedComponent.h"
#include "RiddenComponent.h"
#include "ClientPlayer.h"
#include "SwarmComponent.h"
#include "LightningChainComponent.h"
#include "ThornBallComponent.h"
#include "ToAttackTargetComponent.h"
#include "AIFishHoveringAround.h"
#include "TemperatureComponent.h"
#include "VacantComponent.h"
#include "LivingAttrib.h"
#include "ActorVision.h"
using namespace MINIW;
IMPLEMENT_COMPONENTCLASS(ClientAquaticComponent)

ClientAquaticComponent::ClientAquaticComponent()
{
	
}

ClientAquaticComponent::~ClientAquaticComponent()
{

}

//use water when exposed in the air
int ClientAquaticComponent::getWaterUseInterval()
{
	return 20;
}

bool ClientAquaticComponent::init()
{
	if (!GetOwner()) return false;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (m_owner == NULL)
	{
		return false;
	}
	m_owner->setObjType(OBJ_TYPE_AQUATICMONSTER);
	m_DroughtTolerance = 2;
	m_owner->getBody()->setNeedUpdateAnim(false);
	m_owner->getBody()->setNeedUpdateSkinEffect(false);
	m_owner->getBody()->setNeedUpdateRenderYawOffset(false);
	m_owner->getBody()->setNeedUpdateLookUp(false);
	m_owner->getBody()->setControlRotation(false);
	m_owner->getBody()->playAnim(SEQ_SWIM);
	m_owner->m_Mass = 1000;

	return true;
}

void ClientAquaticComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
}

void ClientAquaticComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnLeaveOwner(owner);
}

//clientactor->ActorLiving->clientmob->
void ClientAquaticComponent::tick()
{
    OPTICK_EVENT();
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (m_owner == NULL)
	{
		return;
	}

	/*	ClientMob::tick();
	*/
	//-------------------------ClientActor tick begin-------------------------
	m_owner->UpdateOctree();
	m_owner->getLocoMotion()->tick();
	m_owner->getLocoMotion()->doBlockCollision();
	m_owner->getLocoMotion()->doPickThrough();
	if (m_owner->getAttrib()) m_owner->getAttrib()->tick();
	if (m_owner->getBody()) {
		m_owner->getBody()->tick();
		if (!m_owner->getBody()->hasAnimIdPlaying(m_owner->m_Body->getNowPlaySeqID()))
		{
			m_owner->getBody()->playAnim(SEQ_SWIM);
		}
	}

	auto* pLightningCom = m_owner->getLightningChainComponent();
	if (pLightningCom)
	{
		pLightningCom->OnTick();
	}
	auto* tempCom = m_owner->getTemperatureComponent();
	if (tempCom)
	{
		tempCom->OnTick();
	}

	auto* vacantCom = m_owner->getVacantComponent();
	if (vacantCom)
	{
		vacantCom->OnTick();
	}
	if (m_owner->getVision()) m_owner->getVision()->clearAICanSeeCache();
	//-------------------------ClientActor tick end-------------------------
	//-------------------------ClientMob tick end-------------------------
	m_owner->livingHPtick(); //fixby renjie 这两个tick需要放置在客机判断return前，否则不出血条
	m_owner->mobHPTick();
	if (m_owner->getWorld()->isRemoteMode())
	{
		return;
	}

	//-------------------------ActorLiving tick begin-------------------------

	m_owner->attackTick();

	if (!m_owner->isDead())
	{
		//水下生物的氧气处理
		bool subtractWater = false;
		if ((m_owner->m_LiveTicks % getWaterUseInterval()) == 0 && !m_owner->getLocoMotion()->m_InWater)
		{
			m_DroughtTolerance -= 1;
			subtractWater = true;
		}

		if ((m_owner->m_LiveTicks % 10) == 0)
		{
			//不在水中
			WCoord pos = m_owner->getPosition();
			int blockid = m_owner->getWorld()->getBlockID(pos / 100);
			if (!m_owner->getLocoMotion()->m_InWater && !(m_owner->m_Def->ID == 3630 && blockid == 23))//荧光棒藻鱼在沉积岩中时不进该判断
			{
				if (!subtractWater && m_DroughtTolerance <= 0)
				{
					auto component = m_owner->getAttackedComponent();
					if (component)
					{
						component->attackedFromType_Base(ATTACK_SUFFOCATE, 2.0f * GetLuaInterfaceProxy().get_lua_const()->default_shanghai_beilv); // modify by null, 伤害倍率
					}
					pos.x += GenRandomInt(BLOCK_SIZE) - GenRandomInt(BLOCK_SIZE);
					pos.y += GenRandomInt(BLOCK_SIZE) - GenRandomInt(BLOCK_SIZE);
					pos.z += GenRandomInt(BLOCK_SIZE) - GenRandomInt(BLOCK_SIZE);
					//m_pWorld->getEffectMgr()->playParticleEffect("particles/1025.ent", pos, 40);
				}

				auto RidComp = m_owner->getRiddenComponent();
				//骑着海马的话，直接就下马
				if (RidComp && RidComp->isRiding() && dynamic_cast<ActorLiving*>(RidComp->getRidingActor()) != NULL)
				{
					m_owner->mountActor_mob(NULL);
				}
			}
			else
			{
				m_DroughtTolerance = Rainbow::Clamp(0, 20, m_DroughtTolerance + 10);
			}
		}
		if (m_owner->m_Def->ID == 3628)
		{
			LivingAttrib* livingAttrib = dynamic_cast<LivingAttrib*>(m_owner->getAttrib());
			if (livingAttrib && livingAttrib->hasBuff(LOSS_OF_CONSCIOUSNESS_BUFF))
			{
				auto targetComponent = m_owner->getToAttackTargetComponent();
				if (targetComponent)
				{
					targetComponent->setTarget(NULL);
					m_owner->setData(sharkAtkStateMax);
				}
			}
		}
	}
	//-------------------------ActorLiving tick end-------------------------


	//-------------------------ClientMob tick begin-------------------------
	m_owner->MobTick();


	auto thornBall = m_owner->getThornBallComponent();
	if (thornBall)
	{
		thornBall->onTick();
	}
}

ActorLocoMotion* ClientAquaticComponent::newLocoMotion()
{
	if (!GetOwner()) return NULL;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return NULL;
	LivingLocoMotion* loco = m_owner->CreateComponent<LivingLocoMotion>("LivingLocoMotion");
	loco->initMoveAbility(MoveAbilityType::AquaticLoc);
	loco->m_SpeedInWater = getSwimSpeed();
	loco->setBehaviorOn(BehaviorType::ObstacleAvoidance);
	loco->setBehaviorOn(BehaviorType::WaterAvoidance);
	return loco;
}

int ClientAquaticComponent::saveToPB(PB_GeneralEnterAOIHC* pb)
{
	if (!GetOwner()) return -1;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return -1;

	PB_ActorAquaticMob* actorAquaticMob = pb->mutable_actoraquaticmob();
	PB_ActorMob* actorMob = actorAquaticMob->mutable_mobdata();
	if (m_owner->saveToMobPB(actorMob) != 0)
		return -1;

	LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	auto moveTarget = WCoordToCoord3(loco->m_MoveTarget);

	actorAquaticMob->set_droughttolerance(m_DroughtTolerance);
	PB_Vector3* movetarget = actorAquaticMob->mutable_movetarget();
	movetarget->set_x(moveTarget.x());
	movetarget->set_y(moveTarget.y());
	movetarget->set_z(moveTarget.z());
	return 0;

}
int ClientAquaticComponent::LoadFromPB(const PB_GeneralEnterAOIHC& pb)
{
	if (!GetOwner()) return -1;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return -1;

	const PB_ActorAquaticMob& actorAquaticMob = pb.actoraquaticmob();
	const PB_ActorMob& actorMob = actorAquaticMob.mobdata();
	if (m_owner->LoadFromMobPB(actorMob) != 0)
		return -1;
	m_DroughtTolerance = actorAquaticMob.droughttolerance();
	m_owner->getLocoMotion()->m_Motion = Rainbow::Vector3f::zero;
	LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	if (loco)
	{
		const PB_Vector3& movetarget = actorAquaticMob.movetarget();
		loco->m_MoveTarget.setElement(movetarget.x(), movetarget.y(), movetarget.z());
	}

	return 0;
}

flatbuffers::Offset<FBSave::SectionActor> ClientAquaticComponent::save(SAVE_BUFFER_BUILDER& builder, flatbuffers::Offset<FBSave::ActorMob>& mobData)
{
	if (!GetOwner()) return NULL;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return NULL;

	//auto mobdata = ClientMob::saveMob(builder);
	LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	auto moveTarget = WCoordToCoord3(loco->m_MoveTarget);

	auto mob = FBSave::CreateActorAquaticMob(builder, mobData, m_DroughtTolerance, &moveTarget);

	return m_owner->saveSectionActor(builder, FBSave::SectionActorUnion_ActorAquaticMob, mob.Union());
}

void ClientAquaticComponent::applyActorCollision(ClientActor* actor)
{
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return ;
	//if (actor->m_RidingActor != getObjId() && actor->m_RiddenByActor != getObjId())
	auto RidComp = actor->getRiddenComponent();
	bool checkRiding = false;
	bool checkRidden = false;
	if (RidComp)
	{
		checkRiding = RidComp->checkRidingByActorObjId(m_owner->getObjId());
		checkRidden = RidComp->checkRiddenByActorObjId(m_owner->getObjId());
	}
	if (!checkRiding && !checkRidden)
	{
		WCoord dpos = actor->getLocoMotion()->getPosition() - m_owner->getLocoMotion()->getPosition();
		float x = static_cast<float>(dpos.x) / BLOCK_FSIZE;
		float z = static_cast<float>(dpos.z) / BLOCK_FSIZE;
		float y = static_cast<float>(dpos.y) / BLOCK_FSIZE;

		float max_xz = Rainbow::Max(Abs(x), Abs(z));
		if (max_xz > 0)
		{
			float r = Sqrt(max_xz);
			x = x / r;
			y = y / r;
			z = z / r;
			float inv_r = 1.0f / r;

			if (inv_r > 1.0f) inv_r = 1.0f;

			x *= inv_r * 5.0f * (1.0f - ACTOR_COLLIDE_DEC);
			z *= inv_r * 5.0f * (1.0f - ACTOR_COLLIDE_DEC);
			y *= inv_r * 5.0f * (1.0f - ACTOR_COLLIDE_DEC);

			//给自己反弹
			LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
			if (loco != NULL)
			{
				loco->m_CollideMoveTicks = 3;
				loco->addMotion(-x, -y, -z);
				loco->m_ColliderMotion.x = -x;
				loco->m_ColliderMotion.y = -y;
				loco->m_ColliderMotion.z = -z;
			}

			if (m_owner->m_Def->Mass > 0.2f * actor->getMass())
			{
				//弹开别人
				LivingLocoMotion* loco2 = dynamic_cast<LivingLocoMotion*>(actor->getLocoMotion());
				if (loco2 != NULL)
				{
					loco2->m_CollideMoveTicks = 3;
					loco2->addMotion(x, y, z);
					loco2->m_ColliderMotion.x = x;
					loco2->m_ColliderMotion.y = y;
					loco2->m_ColliderMotion.z = z;
				}
				else
				{
					actor->getLocoMotion()->addMotion(x, y, z);
					actor->getLocoMotion()->m_CollideMoveTicks = 3;
				}
			}
			actorElasticCollision(actor);
		}
		else
		{
			LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
			if (loco != NULL)
			{
				loco->m_ColliderMotion.x = 0;
				loco->m_ColliderMotion.y = 0;
				loco->m_ColliderMotion.z = 0;
			}
		}
	}
	if ((actor->isPlayer() || actor->getObjType() == OBJ_TYPE_BOAT) && m_owner->getDefID() == 3628 && m_owner->m_Body->hasAnimPlaying(SEQ_SHARK_BITE_1))
	{
		m_owner->setData(sharkAtkPlayer);
	}
}

bool ClientAquaticComponent::load(const void* srcdata, int version)
{
	if (!GetOwner()) return false;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return false;

	auto src = reinterpret_cast<const FBSave::ActorAquaticMob*>(srcdata);

	if (!m_owner->todoLoad(src->mobdata(), version))
	{
		return false;
	}
	m_DroughtTolerance = src->droughtTolerance();
	m_owner->getLocoMotion()->m_Motion = Rainbow::Vector3f::zero;
	LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	if (loco)
	{
		loco->m_MoveTarget = Coord3ToWCoord(src->moveTarget());
	}

	return true;
}

void ClientAquaticComponent::onClear()
{

}

void ClientAquaticComponent::onDie()
{
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return ;
	emitSpawnSharkPoint();
	if (m_owner->m_Def->ID == 3628)//鲨鱼死亡埋点上报
	{
		int temp_shark_source = 2;
		if (shark_source == 1)
		{
			temp_shark_source = 1;
		}
		MINIW::ScriptVM::game()->callFunction("SharkDieReport", "i", temp_shark_source);
	}
	//m_owner->onDie();
	m_owner->m_Body->setCurAnim(SEQ_DIE, 0);
}

void ClientAquaticComponent::moveToPosition(const WCoord& pos, float yaw, float pitch, int interpol_ticks)
{
	if (!GetOwner()) return;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return;

	LivingLocoMotion* locmove = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	locmove->m_SyncSteps = 5;
	/*
		if (m_HostMotivate)
		{
			locmove->minecartPosRotationIncrements = interpol_ticks + 5;
		}
		else
		{
			WCoord dpos = pos - locmove->m_Position;
			if (dpos.lengthSquared() <= BLOCK_SIZE*BLOCK_SIZE)
			{
				return;
			}
			locmove->minecartPosRotationIncrements = 3;
		}*/

	locmove->m_SyncPos = pos;
	locmove->m_SyncYaw = yaw;
	locmove->m_SyncPitch = pitch;
}

bool ClientAquaticComponent::attackedFrom(OneAttackData& atkdata, ClientActor* attacker) {
	if (!GetOwner()) return false;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return false;

	if (attacker)
	{

		if (attacker->isPlayer())
		{
			ClientPlayer* cPlayer = dynamic_cast<ClientPlayer*>(attacker);
			m_fromAttackUin = cPlayer->getUin();
			if (m_owner->getDefID() == 3628 && (m_owner->getData() == 3 || m_owner->getData() == 0)) {
				m_owner->setData(sharkAtkBreakPreAround);
			}
		}
		else if (attacker->getObjType() == OBJ_TYPE_COBBLE || attacker->getObjType() == OBJ_TYPE_THROWABLE)
		{
			ClientActor* cActor = attacker->getShootingActor();
			if (cActor && cActor->isPlayer()) {
				ClientPlayer* cPlayer = dynamic_cast<ClientPlayer*>(cActor);
				if (cPlayer) m_fromAttackUin = cPlayer->getUin();
			}
		}
		else
		{
			m_fromAttackUin = 0;
		}
	}

	if (m_owner->isInWater())
	{
		std::vector<ClientActor*> cActors = m_owner->selectAllMobs();
		size_t size = cActors.size();
		for (size_t i = 0; i < size; i++)
		{
			ClientActor* cActor = cActors[i];
			if (cActor->getDefID() == 3627) // 惊吓飞鱼
			{
				dynamic_cast<LivingLocoMotion*>(cActor->getLocoMotion())->m_FearPos = m_owner->getPosition();
			}

			//呆呆鱼鱼群附近有伤害，打撒鱼群
			const std::vector<int> mobIds{ 3604,3605,3606,3607,3609,3610,3611,3612,3613,3614,3615,3616,3617,3618,3619 };
			int mobId = 0;
			if (cActor)
			{
				mobId = cActor->getDefID();
				size_t mobIdsSize = mobIds.size();
				for (size_t i = 0; i < mobIdsSize; i++)
				{
					if (mobIds[i] == mobId)
					{
						if (cActor->getSwarmComponent()) cActor->getSwarmComponent()->setSwarmId(0);

						dynamic_cast<LivingLocoMotion*>(cActor->getLocoMotion())->m_FearMobId = m_owner->getObjId();
					}
				}
			}
		}
	}
	return m_owner->todoAttackedFrom(atkdata, attacker);

}

float ClientAquaticComponent::getSwimSpeed()
{
	if (!GetOwner()) return 0.0f;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return 0.0f;

	return static_cast<float>(m_owner->m_Def->Speed);
}

ClientPlayer* ClientAquaticComponent::selectNearPlayer(int range, int height) {
	if (!GetOwner()) return NULL;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return NULL;
	ClientActorMgr* mgr = m_owner->getActorMgr();
	if (!mgr)
	{
		return NULL;
	}
	ClientPlayer* pTarget = NULL;
	float fDist = 99999999.0f;
	WCoord pos = m_owner->getPosition();
	for (size_t i = 0; i < mgr->getAllPlayer().size(); i++)
	{
		ClientPlayer* player = mgr->getAllPlayer()[i];

		if (player)
		{
			if (!player->isDead() && !player->needClear())
			{
				float disX = Abs(player->getLocoMotion()->getPosition().x - pos.x);
				float disZ = Abs(player->getLocoMotion()->getPosition().z - pos.z);
				float disY = Abs(player->getLocoMotion()->getPosition().y - pos.y);

				WCoord vec = player->getLocoMotion()->getPosition() - pos;

				float dist = vec.length();

				if (disX <= range && disZ <= range && disY <= height && dist < fDist)
				{
					fDist = dist;
					pTarget = player;
				}
			}
		}
	}

	return pTarget;
}

void ClientAquaticComponent::actorElasticCollision(ClientActor* actor) {
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return ;
	if (m_owner->m_Def->ID == 3626 && m_owner->getData() == 3 && actor->getObjType() != OBJ_TYPE_AQUATICMONSTER) //水母气体充盈
	{
		actor->getLocoMotion()->m_Motion.y = 60;
		m_owner->setData(4);
	}
}

void ClientAquaticComponent::emitSpawnSharkPoint() {
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return ;
	if (m_owner->IsInWorld() && m_fromAttackUin != 0) {
		WCoord pos = m_owner->getPosition() / BLOCK_SIZE;
		BIOME_TYPE biome_type = m_owner->getWorld()->getBiomeType(pos.x, pos.z);
		if (biome_type == BIOME_OCEAN || biome_type == BIOME_DEEPOCEAN)
		{
			ClientActorMgr* mgr = m_owner->getActorMgr();
			ClientPlayer* cPlayer = mgr->findPlayerByUin(m_fromAttackUin);
			int point = 20;
			if (cPlayer && cPlayer->getLivingAttrib()->hasBuff(1028))
			{
				point = 10;
			}
			MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("spawnSharkPoint", MNSandbox::SandboxContext(nullptr).SetData_Number("point", point));
		}
	}
}

bool ClientAquaticComponent::canDespawn()
{
	return false;
}
