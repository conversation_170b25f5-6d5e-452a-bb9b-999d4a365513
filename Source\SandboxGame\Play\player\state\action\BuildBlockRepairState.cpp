#include "BuildBlockRepairState.h"
#include "PlayerControl.h"
#include "InputInfo.h"
#include "PlayerStateController.h"
#include "DefManagerProxy.h"
#include "CameraModel.h"
#include "world.h"
#include "EffectManager.h"
#include "PlayerAnimation.h"
#include "OgreTimer.h"
#include "PlayerAttrib.h"
#include "SoundComponent.h"
#include "ClientActorFuncWrapper.h"
#include "SandboxIdDef.h"

BuildBlockRepairState::BuildBlockRepairState(PlayerControl* host) : PlayerState(host),
    m_RepairStartMark(0), m_RepairItemDuration(0), m_PlayRepairTicks(0),
    m_RepairComplete(false)// m_RepairMark(0),
{
    m_StateID = "BuildBlockRepair";
}

BuildBlockRepairState::~BuildBlockRepairState()
{
}

void BuildBlockRepairState::doBeforeEntering()
{
    m_OperateTicks = 0;
    //LOG_INFO("BuildBlockRepairState Enter");
    m_RepairStartMark = Rainbow::Timer::getSystemTick();
    createMarkMD5(m_RepairStartMark);
    m_CurToolID = m_Host->getCurToolID();
    m_CurShortcut = m_Host->getCurShortcut();
    
    // Get repair tool definition
    /*const ToolDef *toolDef = GetDefManagerProxy()->getToolDef(m_CurToolID);
    if (toolDef == NULL)
    {
        return;
    }*/

    // 转换时间为tick todo
    m_RepairItemDuration = 100; //toolDef->UseTime * 50;
    
    // 播放修理动画 todo
    //m_Host->m_PlayerAnimation->performRepair();
    
    // 播放修理声音
    if (!m_Host->getWorld()->isRemoteMode())
    {
        auto soundComp = m_Host->getSoundComponent();
        if (soundComp)
        {
            soundComp->playSound("misc.repair", 1.0f, 1.0f);
        }
    }

    m_PlayRepairTicks = 0;
    //m_RepairMark = m_RepairItemDuration - 300;
    m_RepairComplete = false;
    m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_BEGIN);
}

std::string BuildBlockRepairState::update(float dtime)
{
    auto functionWrapper = m_Host->getFuncWrapper();
    if (!(functionWrapper && functionWrapper->getKeepDigging()) && 
        (IsUseActionEnd() || m_Host->getCurToolID() != m_CurToolID || 
        m_CurShortcut != m_Host->getCurShortcut() || m_Host->isDead()))
    {
        m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
        m_Host->setOperate(PLAYEROP_NULL);
        return "ToActionIdle";
    }

    int curtick = Rainbow::Timer::getSystemTick();

    // 处理修理完成后的状态
    if (m_RepairComplete && curtick - m_RepairStartMark >= m_RepairItemDuration + 300)
    {
        m_RepairComplete = false;
        m_RepairStartMark = Rainbow::Timer::getSystemTick();
        createMarkMD5(m_RepairStartMark);
        //m_Host->m_PlayerAnimation->performRepair(); todo
        m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_BEGIN);
        return "";
    }

    if (!m_RepairComplete && curtick - m_RepairStartMark >= m_RepairItemDuration)
    {
        // 检查起始时间是否被篡改
        if (!checkMarkMD5(m_RepairStartMark))
        {
            m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
            m_Host->setOperate(PLAYEROP_NULL);
            return "ToActionIdle";
        }
        
        m_RepairComplete = true;


		bool ret = m_Host->interactBlock(m_Host->m_PickResult.block, m_Host->m_PickResult.face, m_Host->m_PickResult.facepoint);
		//if (ret)
		//{
		//	if (m_Host->getViewMode() == 0)
		//	{
		//		m_Host->m_PlayerAnimation->performDig();
		//	}

		//	m_UseBlock = 0.0f;
		//	return "";
		//}

        //m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_END);
        m_Host->m_PlayerAnimation->performIdle();
        m_OperateTicks = 0;
        return "";
    }

    // 周期性播放修理声音
    m_PlayRepairTicks += Rainbow::TimeToTick(dtime);
    if (m_PlayRepairTicks >= 500)
    {
        if (!m_Host->getWorld()->isRemoteMode())
        {
            auto soundComp = m_Host->getSoundComponent();
            if (soundComp)
            {
                soundComp->playSound("misc.repair", 1.0f, 1.0f);
            }
        }
        m_PlayRepairTicks = 0;
    }

    // 可以添加特定修理工具的处理
    // 例如:
    // if (m_CurToolID == ITEM_REPAIR_KIT) {
    //    m_Host->checkNewbieWorldProgress(20, "useRepairKit");
    // }

    return "";
}

void BuildBlockRepairState::doBeforeLeaving()
{
    //LOG_INFO("BuildBlockRepairState leaving");
    m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
    m_Host->m_PlayerAnimation->performIdle();
}

void BuildBlockRepairState::OnTick(float elapse)
{
    m_OperateTicks++;
} 