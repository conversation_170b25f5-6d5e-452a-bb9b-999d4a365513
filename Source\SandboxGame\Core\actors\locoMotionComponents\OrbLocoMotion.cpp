
#include "ActorExpOrb.h"
#include "OrbLocoMotion.h"
#include "world.h"
#include "BlockScene.h"
#include "ClientActorManager.h"
#include "ClientPlayer.h"
#include "WorldManager.h"
#include "PlayerAttrib.h"
#include "Entity/OgreEntity.h"

using namespace MINIW;

IMPLEMENT_COMPONENTCLASS(OrbLocoMotion)

OrbLocoMotion::OrbLocoMotion()
{
	m_BoundHeight = 25;
	m_BoundSize = 25;
	m_yOffset = m_BoundHeight/2;
}

void OrbLocoMotion::tick()
{
	ActorLocoMotion::tick();
	if(getOwnerActor()->needClear()) return;

	m_Motion.y -= m_pWorld->getGravity(GRAVITY_ITEM);
	setNoClip(pushOutOfBlocks(m_Position));

	WCoord prevpos = m_Position;
	Vector3f prevmotion = m_Motion;

	doMoveStep(m_Motion);

	m_Motion *= 0.8f;

	if(m_OnGround)
	{
		m_Motion.y = -prevmotion.y*0.5f;
	}
}