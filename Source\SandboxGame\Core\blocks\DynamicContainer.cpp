

#include "DynamicContainer.h"
#include "IClientActor.h"
#include "DefManagerProxy.h"
#include "PlayerControl.h"
#include "IPlayerControl.h"
#include "ActorCSProto.h"
#include "ClientItem.h"
#include "ClientActorManager.h"
#include "GameNetManager.h"
#include "LuaInterfaceProxy.h"
#include "BlockMaterialMgr.h"

#include "ObserverEvent.h" 
#include "ObserverEventManager.h"
//#include "GameEvent.h"
//#include "TaskSubSystem.h"
#include "SandboxIdDef.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "SandboxCoreDriver.h"
#include "world.h"
#include "Platforms/PlatformInterface.h"
static int ox = 0;
static int oy = 30;
static int oz = 0;

EXPORT_SANDBOXENGINE extern int CheckInsertItemIntoArray(BaseContainer* container, BackPackGrid* gridarray, int arraylen, const BackPackGrid& grid);

void DynamicContainer::Initialization()
{
	for (int i = 0; i < m_grid_num; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
}

DynamicContainer::DynamicContainer() : WorldContainer(STORAGE_START_INDEX)
{
	m_grid_num = 0;
	Initialization();
}

DynamicContainer::DynamicContainer(const WCoord &blockpos, int blockid, int grid_num) : WorldContainer(blockpos, STORAGE_START_INDEX)
{
	m_grid_num = grid_num;
	m_Grids.resize(m_grid_num);

	Initialization();
}

DynamicContainer::~DynamicContainer()
{
	
}

void DynamicContainer::SetGrid(int slotindex, const BackPackGrid &grid)
{
	assert(slotindex>=0 && slotindex<m_grid_num);

	m_Grids[slotindex] = grid;
}

BackPackGrid *DynamicContainer::index2Grid(int index)
{
	assert(index>=m_BaseIndex && index<m_BaseIndex + m_grid_num);

	return &m_Grids[index-m_BaseIndex];
}

void DynamicContainer::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);

	m_NeedSave = true;
}



void DynamicContainer::onAttachUI()
{
	m_AttachToUI = true;

	for (int i = 0; i < m_grid_num; i++)
	{
		// GameEventQue::GetInstance().postBackpackChange(FURNACE_START_INDEX + i);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", m_BaseIndex + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}
	// GetGameEventQue().postBackPackAttribChange();
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_ATTRIB_CHANGE", sandboxContext);
}

void DynamicContainer::onDetachUI()
{
	m_AttachToUI = false;
}

bool DynamicContainer::canPutItem(int index)
{
	return false;
}


void DynamicContainer::dropItems()
{
	
}



void DynamicContainer::clear()
{
	for (int i = 0; i < m_grid_num; i++) {
		BackPackGrid& grid = m_Grids[i];
		if (!grid.isEmpty()) {
			grid.clear();
			afterChangeGrid(grid.getIndex());
		}
	}
}

int DynamicContainer::addItemByCount(int itemid, int num)
{
	return 0;
}

void DynamicContainer::removeItemByCount(int itemid, int num)
{
}

flatbuffers::Offset<FBSave::ChunkContainer> DynamicContainer::save(SAVE_BUFFER_BUILDER &builder)
{
	auto actor = SaveTo(builder);
	return FBSave::CreateChunkContainer(builder, getUnionType(), actor.Union());
}

flatbuffers::Offset<FBSave::ContainerDynamicBox> DynamicContainer::SaveTo(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	flatbuffers::Offset<FBSave::ItemGrid>* items = new flatbuffers::Offset<FBSave::ItemGrid>[m_grid_num];
	unsigned char* indices = new unsigned char[m_grid_num];
	int count = 0;

	for (int i = 0; i < m_grid_num; i++)
	{
		if (!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			indices[count] = (unsigned char)i;
			count++;
		}
	}

	auto actor = FBSave::CreateContainerDynamicBox(builder, basedata, builder.CreateVector(items, count), builder.CreateVector(indices, count), m_grid_num);
	delete[] items;
	delete[] indices;
	return actor;
}

bool DynamicContainer::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerDynamicBox *>(srcdata);
	loadContainerCommon(src->basedata());

	auto items = src->items();
	auto indices = src->indices();

	m_grid_num = src->key();
	m_Grids.resize(m_grid_num);

	for (size_t i = 0; i < items->size(); i++)
	{
		int index = indices->Get(i);
		assert(index >= 0 && index < m_grid_num);

		m_Grids[index].load(items->Get(i));
	}
	
	return true;
}

// virtual float getAttrib(int i) 参考主机的
int DynamicContainer::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (int i = 0; i < m_grid_num; i++)
		{
			if (m_Grids[i].isEmpty()) continue;

			storeGridData(pItemInfos->Add(), &m_Grids[i]);
		}
	}

	return m_grid_num;
}

int DynamicContainer::onInsertItem(const BackPackGrid &grid, int num, int params)
{
	return -1;
}

// 传送带相关的
bool DynamicContainer::canInsertItem(const BackPackGrid& grid, int param)
{
	return false;
}

BackPackGrid *DynamicContainer::onExtractItem(int params)
{
	return NULL;
}

int DynamicContainer::calComparatorInputOverride()
{
	return 0;
}

void DynamicContainer::leaveWorld() 
{	
	WorldContainer::leaveWorld();
}


float DynamicContainer::getAttrib(int i)
{
	return 0;
}

bool DynamicContainer::doOpenContainer()
{
	BaseContainer::SetLuaOpenContainer(getContainerName(), this);
	BlockMaterial* mtl = getBlockMtl();
	int block_id = mtl ? mtl->m_BlockResID : 0;
	if (actor_storage)
		block_id = 10000748;//特殊处理-stringdef.csv 用存储箱子的blockdef.csv
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("baseindex", getBaseIndex()).
		SetData_Number("blockid", block_id).
		SetData_Number("posx", m_BlockPos.x).
		SetData_Number("posy", m_BlockPos.y).
		SetData_Number("posz", m_BlockPos.z).
		SetData_Bool("isRideContainer", m_isRideContainer);


	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	{
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_OPEN_CONTAINER", sandboxContext);
	}
	return true;
}
