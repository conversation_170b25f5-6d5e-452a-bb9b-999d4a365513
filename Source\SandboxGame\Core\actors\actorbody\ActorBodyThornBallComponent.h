#pragma once
#include "ActorBody.h"

class ModelItemMesh;
class ActorBody;

namespace Rainbow
{
	class Model;
	class Entity;
};

namespace MNSandbox
{
	struct ThronBallStruct
	{
		unsigned int AnchorId;
		Rainbow::Vector3f Pos;
		ModelItemMesh* _ItemMesh;
		ThronBallStruct()
		{
			_ItemMesh = nullptr;
			AnchorId = 0;
			Pos = Rainbow::Vector3f(0, 0, 0);
		}
	};
}


class ActorBodyThornBallComponent
{
	friend class ActorBody;
public:
	ActorBodyThornBallComponent(ActorBody* actorBody);
	~ActorBodyThornBallComponent();

	void createThornBallMeshModel(int anchorId, Rainbow::Vector3f pos);
	void removeThornBallMesh(bool isAll, int num = 1);
	void showThornBallMesh(bool is);
	std::vector<MNSandbox::ThronBallStruct> getThornBallModeInfo();
private:
	void checkModelThronBallLoad();

	ActorBody* m_ActorBody = nullptr;
	std::vector<MNSandbox::ThronBallStruct>		m_thornItemInfo;
};