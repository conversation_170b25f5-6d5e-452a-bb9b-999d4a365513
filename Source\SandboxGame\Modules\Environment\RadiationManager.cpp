#include "RadiationManager.h"
#include "world.h"
#include "section.h"
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "WeatherManager.h"
#include "SandBoxManager.h"
#include "WorldManager.h"
#include "PlayerControl.h"
#include "PlayerAttrib.h"

RadiationManager::RadiationManager()
{
	m_Tick = 0;
}

RadiationManager::~RadiationManager()
{
}

void RadiationManager::OnTick()
{
	if (!m_Active)
		return;

	m_Tick++;

	// 每10tick处理一次
	if (m_Tick % 10 == 0)
	{
		PlayerTick();
		MobTick();
	}

	// 每60tick处理一次
	if (m_Tick % 60 == 0)
	{
		SourceTick();
	}
}

float RadiationManager::GetEnviromentRadiation(World* world, const WCoord& blockpos)
{
	if (!world || !m_Active)
		return 0.f;

	// 环境辐射 = 地形辐射 + 高度辐射 + 天气辐射
	return GetBiomeRadiation(world, blockpos) + GetHeightRadiation(world, blockpos) + GetWeatherRadiation(world, blockpos);
}

float RadiationManager::GetBiomeRadiation(World* world, const WCoord& blockpos)
{
	if (!world || !m_Active) return 0.f;
	// auto biomeDef = world->getBiome(blockpos.x, blockpos.z);
	// short Temperature[] = { 0,0 };
	// if (biomeDef) std::copy(std::begin(biomeDef->Temperature), std::end(biomeDef->Temperature), std::begin(Temperature));
	// float hour = world->getHours();
	// if (hour >= 8.0f && hour < 20.0f) return Temperature[0];
	// else return Temperature[1];
	return 0.f;
}

float RadiationManager::GetHeightRadiation(World* world, const WCoord& blockpos)
{
	if (!world || !m_Active)
		return 0.f;

	return 0.f;
	// // 高度对辐射的影响，可能地下深处有更高的辐射
	// float heightEffect = 0.0f;
	
	// // 地下越深，辐射可能越高（矿石辐射）
	// if (blockpos.y < 40)
	// {
	// 	heightEffect = (40 - blockpos.y) * 0.1f;
	// }

	// return heightEffect;
}

float RadiationManager::GetWeatherRadiation(World* world, const WCoord& blockpos)
{
	if (!world || !world->getWeatherMgr() || !m_Active) return 0.0f;
	// int weather = world->getWeatherMgr()->getWeather(blockpos);
	//if (weather == GROUP_RAIN_WEATHER) return m_ConstWeatherRain;
	//else if (weather == GROUP_SNOW_WEATHER) return m_ConstWeatherSnow;
	//else if (weather == GROUP_TEMPEST_WEATHER)
	//{
	//	if (world->getWeatherMgr()->getTempestStage(blockpos) == PART_WEATHER_STAGE_STRENGTH) return m_ConstWeatherTempestUp;
	//	else return 0.0f;
	//}
	//else if (weather == GROUP_BLIZZARD_WEATHER)
	//{
	//	if (world->getWeatherMgr()->getWeatherStage(weather, blockpos) == PART_WEATHER_STAGE_STRENGTH) return m_ConstWeatherBlizzardUp;
	//	else return 0.0f;
	//}
	//else if (weather == GROUP_THUNDER_WEATHER)
	//	return m_ConstWeatherThunder;
	return 0.f;
}

int RadiationManager::GetRadiationLevel(float rad)
{
	// 根据辐射值返回辐射等级
	if (rad <= m_ConstRadiationSafe)
		return 0; // 安全
	else if (rad <= m_ConstRadiationLow)
		return 1; // 低度辐射
	else if (rad <= m_ConstRadiationMedium)
		return 2; // 中度辐射
	else if (rad <= m_ConstRadiationHigh)
		return 3; // 高度辐射
	else
		return 4; // 致命辐射
}

void RadiationManager::GetRadiationLevelAndVal(float rad, int& level, float& minVal, float& maxVal)
{
	level = GetRadiationLevel(rad);

	// 根据等级设置最小和最大值
	switch (level)
	{
	case 0: // 安全
		minVal = 0.f;
		maxVal = m_ConstRadiationSafe;
		break;
	case 1: // 低度辐射
		minVal = m_ConstRadiationSafe;
		maxVal = m_ConstRadiationLow;
		break;
	case 2: // 中度辐射
		minVal = m_ConstRadiationLow;
		maxVal = m_ConstRadiationMedium;
		break;
	case 3: // 高度辐射
		minVal = m_ConstRadiationMedium;
		maxVal = m_ConstRadiationHigh;
		break;
	case 4: // 致命辐射
		minVal = m_ConstRadiationHigh;
		maxVal = m_ConstRadiationLethal;
		break;
	default:
		minVal = 0.f;
		maxVal = 0.f;
		break;
	}
}

bool RadiationManager::IsRadiationSafe(float rad)
{
	return rad <= m_ConstRadiationSafe;
}

int RadiationManager::AddAreaRadiationSource(World* world, const WCoord& blockpos, float val, int tick)
{
	if (!world || !m_Active)
		return -1;

	// 添加区域辐射源
	int idx = m_AreaSourceInfoMapIdx++;
	//m_AreaSourceInfoMap[blockpos.getsectionxy()][idx] = blockpos;
	
	return idx;
}

bool RadiationManager::RemoveAreaRadiationSource(int index)
{
	if (!m_Active)
		return false;

	// 移除区域辐射源
	for (auto& it : m_AreaSourceInfoMap)
	{
		auto it1 = it.second.find(index);
		if (it1 != it.second.end())
		{
			it.second.erase(it1);
			return true;
		}
	}
	
	return false;
}

void RadiationManager::ClearPosRadiationCache(World* world, const WCoord& blockpos, int range)
{
	// 清理位置辐射缓存
	// 实际实现可能需要考虑缓存机制
}

void RadiationManager::SetAreaRadiationSource(int index, Section* psection)
{
	// 设置区域辐射源
	// 特殊用法，一般通过AddAreaRadiationSource接口添加
}

int RadiationManager::AddMobileRadiationSource(World* world, long long objid, float val, float decay, const WCoord& pos)
{
	if (!world || !m_Active)
		return -1;

	// 添加点（固定/移动)辐射源
	int idx = m_MobileSourceInfoMapIdx++;
	m_MobileSourceInfoMap[1][idx] = MobileSourceRadiationData(val, decay, objid, pos);
	
	return idx;
}

bool RadiationManager::RemoveMobileRadiationSource(int index)
{
	if (!m_Active)
		return false;

	// 移除移动辐射源
	for (auto& it : m_MobileSourceInfoMap)
	{
		auto it1 = it.second.find(index);
		if (it1 != it.second.end())
		{
			LOG_INFO("RadiationManager::RemoveMobileRadiationSource: index(%d), pos(%d, %d, %d)", index, it1->second._pos.x, it1->second._pos.y, it1->second._pos.z);
			it.second.erase(it1);
			return true;
		}
	}
	
	return false;
}

float RadiationManager::GetRadiationInRange(World* world, const WCoord& blockpos, float checkRange)
{
	if (!world || !m_Active)
		return 0.f;

	float radiation = 0.f;

	// 获取周围半径范围内的所有移动辐射源
	std::vector<MobileSourceRadiationData> mobList;
	auto mobMap = m_MobileSourceInfoMap[1];	
	for (auto& it : mobMap)
	{
		WCoord playerPos = blockpos;
		// 计算玩家位置到辐射源的距离
		playerPos.x = playerPos.x / 100;
		playerPos.y = playerPos.y / 100;
		playerPos.z = playerPos.z / 100;

		float dist = playerPos.distanceTo(it.second._pos);
		float radius = it.second._val / it.second._decay;
		// LOG_INFO("RadiationManager player pos(%d, %d, %d), mob pos(%d, %d, %d), dist = %.2f", playerPos.x, playerPos.y, playerPos.z, it.second._pos.x, it.second._pos.y, it.second._pos.z, dist);
		if (dist <= checkRange)
		{
			float ratio = clamp(dist / radius, 0.0f, 1.0f);
			float radiationReceived = it.second._val * (1.0f - ratio);
			radiation += radiationReceived;
		}
	}

	return radiation;
}

void RadiationManager::PlayerTick()
{
	// 处理玩家的辐射逻辑
	// 每个玩家获取当前位置的辐射值并应用效果
}

void RadiationManager::MobTick()
{
	// 处理生物的辐射逻辑
	// 生物可能受到辐射影响或者成为辐射源
}

void RadiationManager::SourceTick()
{
	// 处理辐射源的逻辑
	// 有些辐射源可能会随时间衰减或者扩散
	
	// 处理区域辐射源
	// for (auto it = m_AreaSourceInfoMap.begin(); it != m_AreaSourceInfoMap.end(); ++it)
	// {
	// 	for (auto it1 = it->second.begin(); it1 != it->second.end();)
	// 	{
	// 		// 处理有时间限制的辐射源
	// 		// 如果需要移除，则移除
	// 		++it1;
	// 	}
	// }
	
	// // 处理移动辐射源
	// for (auto it = m_MobileSourceInfoMap.begin(); it != m_MobileSourceInfoMap.end(); ++it)
	// {
	// 	for (auto it1 = it->second.begin(); it1 != it->second.end();)
	// 	{
	// 		if (it1->second._tick > 0)
	// 		{
	// 			it1->second._tick--;
	// 			if (it1->second._tick <= 0)
	// 			{
	// 				it->second.erase(it1++);
	// 				continue;
	// 			}
	// 		}
	// 		++it1;
	// 	}
	// }
}

// RadiationNetSys 实现

RadiationNetSys::RadiationNetSys(PluginManager* p)
{
}

RadiationNetSys::~RadiationNetSys()
{
}

bool RadiationNetSys::Awake()
{
	return true;
}

bool RadiationNetSys::Init()
{
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_RADIATION_HC");
	return true;
}

bool RadiationNetSys::Execute(float dtime)
{
	return true;
}

bool RadiationNetSys::Shut()
{
	SandBoxManager::getSingleton().unSubscibeEx((IEventExcuteEx*)this, "PB_RADIATION_HC");
	m_eventCallbacks.clear();
	return true;
}

bool RadiationNetSys::CreateModuleEvent()
{
	// 注册相关事件回调
	return true;
}

void RadiationNetSys::OnExecute(const char* eventname, void* context, int nLen, unsigned long long userdata)
{
	// 处理事件回调
	if (!GetWorldManagerPtr()) return;
	jsonxx::Object* obj = (jsonxx::Object*)context;
	if (0 == strcmp(eventname, "PB_RADIATION_HC"))
	{
		if (g_pPlayerCtrl && g_pPlayerCtrl->getPlayerAttrib())
		{
			float val = (float)obj->get<jsonxx::Number>("radiation");
			g_pPlayerCtrl->getPlayerAttrib()->setRadiation(val);
		}
	}	
} 