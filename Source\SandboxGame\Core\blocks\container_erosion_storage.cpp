#include "container_erosion_storage.h"
#include "ChunkSave_generated.h"
//#include "common.h"
#include "DefManagerProxy.h"
//#include "ItemDef.h"
#include "proto_common.pb.h"
#include "GameNetManager.h"
#include "ClientPlayer.h"
#include "world.h"
//#include "Math.h"
//#include "WorldContainerMgr.h"
#include "BlockMaterial.h"
//#include "WorldManagerProxy.h"
#include "container.h"
#include "SandboxActorSubsystem.h"
#include "WorldManager.h"
#include "IGameMode.h"

ErosionStorageBox::ErosionStorageBox() : ErosionContainer(STORAGE_START_INDEX), m_AppendBox(NULL), m_ParentBox(NULL), m_GridCount(0), m_isNeedDestroyWhenEmpty(false)
{
	for (int i = 0; i < EROSION_STORAGEBOX_CAPACITY; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
}

ErosionStorageBox::ErosionStorageBox(const WCoord& blockpos, const int blockId) : ErosionContainer(blockpos, blockId, STORAGE_START_INDEX),
m_AppendBox(NULL), m_ParentBox(NULL), m_GridCount(0), m_isNeedDestroyWhenEmpty(false)
{
	for (int i = 0; i < EROSION_STORAGEBOX_CAPACITY; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
}

ErosionStorageBox::~ErosionStorageBox()
{
}

void ErosionStorageBox::leaveWorld()
{
	//只使用主box的m_OpenUINs
	if (m_ParentBox)
	{
		m_ParentBox->resetOpenUINs();
		m_ParentBox->append(NULL);
		m_World = nullptr;
		return;
	}

	append(NULL);

	ErosionContainer::leaveWorld();
}

int ErosionStorageBox::getGridCount()
{
	if (m_GridCount > 0) return m_GridCount;

	if (m_ParentBox || m_AppendBox)
	{
		return EROSION_STORAGEBOX_CAPACITY * 2;
	}
	return EROSION_STORAGEBOX_CAPACITY;
}

int ErosionStorageBox::getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (int i = 0; i < EROSION_STORAGEBOX_CAPACITY * 2; i++)
		{
			BackPackGrid* tgrid = nullptr;
			if (i < EROSION_STORAGEBOX_CAPACITY)
			{
				tgrid = m_ParentBox ? &m_ParentBox->m_Grids[i] : &m_Grids[i];
			}
			else
			{
				if (m_ParentBox) tgrid = &m_Grids[i - EROSION_STORAGEBOX_CAPACITY];
				else if (m_AppendBox) tgrid = &m_AppendBox->m_Grids[i - EROSION_STORAGEBOX_CAPACITY];
			}

			if (tgrid && !tgrid->isEmpty())
			{
				GetISandboxActorSubsystem()->StoreGridData(pItemInfos->Add(), tgrid, i + STORAGE_START_INDEX);
			}
		}
	}

	if (m_GridCount > 0)
	{
		return m_GridCount;
	}
	else
	{
		return (m_ParentBox != nullptr || m_AppendBox != nullptr) ? EROSION_STORAGEBOX_CAPACITY * 2 : EROSION_STORAGEBOX_CAPACITY;
	}
}

void ErosionStorageBox::syncItemUserdata(IClientPlayer* player)
{
	assert(player);

	for (int i = 0; i < EROSION_STORAGEBOX_CAPACITY * 2; i++)
	{
		BackPackGrid* tgrid = nullptr;
		if (i < EROSION_STORAGEBOX_CAPACITY)
		{
			tgrid = m_ParentBox ? &m_ParentBox->m_Grids[i] : &m_Grids[i];
		}
		else
		{
			if (m_ParentBox) tgrid = &m_Grids[i - EROSION_STORAGEBOX_CAPACITY];
			else if (m_AppendBox) tgrid = &m_AppendBox->m_Grids[i - EROSION_STORAGEBOX_CAPACITY];
		}

		if (tgrid && !tgrid->isEmpty()
			&& !tgrid->userdata_str.empty())
		{
			PB_ItemGridUserData itemGridUserData;
			itemGridUserData.set_uin(player->getUin());
			itemGridUserData.set_gridindex(tgrid->getIndex());
			itemGridUserData.set_userdatastr(tgrid->userdata_str.c_str(), tgrid->userdata_str.size());

			GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_SYNC_GRIDUSERDATA_HC, itemGridUserData);
		}
	}
}

int ErosionStorageBox::onInsertItem(const BackPackGrid& grid, int num, int params)
{
	bool issame = false;
	GetISandboxActorSubsystem()->IsBlockType(issame, getBlockMtl(), BlockCheckType::BLOCK_CHEST_MATER);
	if (!issame) return 0;

	ErosionStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;

	GridCopyData gridcopydata(&grid);
	gridcopydata.num = num;
	return container->addItem_byGridCopyData(gridcopydata);
}

bool ErosionStorageBox::canInsertItem(const BackPackGrid& grid, int param)
{
	ErosionStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	int numgrid = getGridCount();
	if (numgrid > EROSION_STORAGEBOX_CAPACITY) numgrid = EROSION_STORAGEBOX_CAPACITY;
	BackPackGrid cgrid;
	cgrid.setItem(grid.getItemID(), grid.getNum());
	int num = CheckInsertItemIntoArray(container, &m_Grids[0], numgrid, cgrid);
	if (num > 0)
	{
		if (!m_AppendBox)
		{
			return false;
		}
		cgrid.setNum(num);
		return CheckInsertItemIntoArray(container, &m_AppendBox->m_Grids[0], EROSION_STORAGEBOX_CAPACITY, cgrid) <= 0;
	}
	return true;
}

bool ErosionStorageBox::checkPutItem(int itemid, int num)
{
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (!def) return false;

	int putnum = 0;
	for (int i = 0; i < sizeof(m_Grids) / sizeof(BackPackGrid); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (grid.isEmpty())
		{
			putnum += def->StackMax;
			if (putnum >= num) return true;
		}
		else if (grid.getItemID() == itemid)
		{
			putnum += (def->StackMax - grid.getNum());
			if (putnum >= num) return true;
		}
	}
	if (m_AppendBox != NULL)
	{
		for (int i = 0; i < sizeof(m_AppendBox->m_Grids) / sizeof(BackPackGrid); i++)
		{
			BackPackGrid& grid = m_AppendBox->m_Grids[i];
			if (grid.isEmpty())
			{
				putnum += def->StackMax;
				if (putnum >= num) return true;
			}
			else if (grid.getItemID() == itemid)
			{
				putnum += (def->StackMax - grid.getNum());
				if (putnum >= num) return true;
			}
		}
	}
	return false;
}

BackPackGrid* ErosionStorageBox::getGridByItemID(int itemid)
{
	for (int i = 0; i < sizeof(m_Grids) / sizeof(BackPackGrid); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (grid.getItemID() == itemid)
		{
			return &grid;
		}
	}
	if (m_AppendBox != NULL)
	{
		for (int i = 0; i < sizeof(m_AppendBox->m_Grids) / sizeof(BackPackGrid); i++)
		{
			BackPackGrid& grid = m_AppendBox->m_Grids[i];
			if (grid.getItemID() == itemid)
			{
				return &grid;
			}
		}
	}

	return NULL;
}

BackPackGrid* ErosionStorageBox::onExtractItem(int params)
{
	BlockMaterial* blkmtl = getBlockMtl();
	if (blkmtl == NULL) return NULL;
	bool issame = false;
	GetISandboxActorSubsystem()->IsBlockType(issame, blkmtl, BlockCheckType::BLOCK_CHEST_MATER);
	if (!issame) return nullptr;
	
	ErosionStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	if (container == NULL) return NULL;
	int maxgrids = container->getGridCount();

	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = container->index2Grid(i + STORAGE_START_INDEX);
		if (grid && !grid->isEmpty()) return grid;
	}
	setAppendBox(blkmtl->m_BlockResID);
	return NULL;
}

void ErosionStorageBox::setAppendBox(int blockid)
{
	if (m_World && (isHorizontalBigChest(blockid) || isVerticalBigChest(blockid)))
	{
		int blockdata = m_World->getBlockData(m_BlockPos);
		int placedir = blockdata & 3;
		int ismirror = (blockdata & 4) > 0;
		WCoord tempPos;
		bool ismajor = true;
		if (isHorizontalBigChest(blockid))
		{
			ismajor = !ismirror;
			DirectionType face = DIR_NOT_INIT;
			if (placedir == DIR_NEG_X) face = DIR_POS_Z;
			if (placedir == DIR_POS_X) face = DIR_NEG_Z;
			if (placedir == DIR_NEG_Z) face = DIR_NEG_X;
			if (placedir == DIR_POS_Z) face = DIR_POS_X;

			if (ismirror) //righter
				tempPos = NeighborCoord(m_BlockPos, face);
			else
				tempPos = NeighborCoord(m_BlockPos, ReverseDirection(face));
		}
		else if (isVerticalBigChest(blockid))
		{
			ismajor = ismirror;
			if (ismirror)
				tempPos = NeighborCoord(m_BlockPos, DIR_NEG_Y);
			else
				tempPos = NeighborCoord(m_BlockPos, DIR_POS_Y);
		}

		if (tempPos != m_BlockPos) //取相邻位置的方块
		{
			if (m_World->getBlockID(tempPos) == blockid)
			{
				WorldContainer* pContainer = m_World->getContainerMgr()->getContainer(tempPos);
				ErosionStorageBox* pStorageBox = dynamic_cast<ErosionStorageBox*>(pContainer);
				if (pStorageBox != NULL && pStorageBox->m_AppendBox == NULL)
				{
					bool isEmpty = true;
					int grids = pStorageBox->getGridCount();
					for (int i = 0; i < grids; i++)
					{
						BackPackGrid* grid = pStorageBox->index2Grid(i + STORAGE_START_INDEX);
						if (grid && !grid->isEmpty())
						{
							isEmpty = false;
						}
					}
					if (!isEmpty)
					{
						if (ismajor) //当前方块为主箱 左方块或下方块
						{
							ErosionStorageBox* pCurrBox = dynamic_cast<ErosionStorageBox*>(this);
							if (pCurrBox != NULL) pCurrBox->append(pStorageBox);
						}
						else
						{
							pStorageBox->append(dynamic_cast<ErosionStorageBox*>(this));
						}
					}
				}
			}
		}
	}
}

flatbuffers::Offset<FBSave::ContainerErosionStorage> ErosionStorageBox::saveContainerErosionStorage(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerErosion(builder);

	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
	flatbuffers::Offset<FBSave::ItemGrid> items[NUMGRIDS];
	unsigned char indices[NUMGRIDS];
	int count = 0;

	for (int i = 0; i < NUMGRIDS; i++)
	{
		if (!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			indices[count] = (unsigned char)i;
			count++;
		}
	}

	auto actor = FBSave::CreateContainerErosionStorage(builder, basedata, builder.CreateVector(items, count), builder.CreateVector(indices, count), m_GridCount, m_isNeedDestroyWhenEmpty);
	return actor;
}

void ErosionStorageBox::onSubtractItem(BackPackGrid* grid, int num)
{
	ErosionStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	int maxgrids = container->getGridCount();

	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* ptmp = container->index2Grid(i + STORAGE_START_INDEX);
		if (ptmp == grid)
		{
			assert(num <= grid->getNum());
			grid->addNum(-num);
			if (grid->getNum() == 0) grid->clear();

			container->afterChangeGrid(i + STORAGE_START_INDEX);
			break;
		}
	}
}

int ErosionStorageBox::calComparatorInputOverride()
{
	if (m_ParentBox) return m_ParentBox->calComparatorInputOverride();
	else
	{
		int count = m_AppendBox ? EROSION_STORAGEBOX_CAPACITY * 2 : EROSION_STORAGEBOX_CAPACITY;
		const BackPackGrid* grids[EROSION_STORAGEBOX_CAPACITY * 2];

		for (int i = 0; i < count; i++)
		{
			grids[i] = index2Grid(STORAGE_START_INDEX + i);
		}

		return CalculateItemsComparatorInput(grids, count);
	}
}

BackPackGrid* ErosionStorageBox::index2Grid(int index)
{
	assert(index >= STORAGE_START_INDEX);
	index -= STORAGE_START_INDEX;

	if (index >= getGridCount()) return NULL;

	if (index < EROSION_STORAGEBOX_CAPACITY) return &m_Grids[index];
	else if (m_AppendBox && index < EROSION_STORAGEBOX_CAPACITY * 2)
	{
		assert(index < 60 && m_AppendBox != NULL);
		return &m_AppendBox->m_Grids[index - EROSION_STORAGEBOX_CAPACITY];
	}
	return NULL;
}

void ErosionStorageBox::afterChangeGrid(int index)
{
	ErosionContainer::afterChangeGrid(index);

	if (GetWorldManagerPtr()->isGameMakerMode())
	{
		if (m_BlockMtl && m_BlockMtl->m_BlockResID == BLOCK_INITITEMBOX)
		{
			if (m_BlockPos == GetWorldManagerPtr()->m_RuleMgr->getInitItemBoxPos())
			{
				GetWorldManagerPtr()->m_RuleMgr->resetGameInitItems(true, m_Grids, EROSION_STORAGEBOX_CAPACITY);
			}
		}
		else if (m_BlockMtl && m_BlockMtl->m_BlockResID == BLOCK_REVIVEITEMBOX)
		{
			if (m_BlockPos == GetWorldManagerPtr()->m_RuleMgr->getReviveItemBoxPos())
			{
				GetWorldManagerPtr()->m_RuleMgr->resetGameInitItems(false, m_Grids, EROSION_STORAGEBOX_CAPACITY);
			}
		}
	}

	if (m_AttachToUI)
	{
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", index);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_UPDATE_STORAGEBOX_POINT", sandboxContext);
		}
	}
}

void ErosionStorageBox::dropItems()
{
	for (int i = 0; i < EROSION_STORAGEBOX_CAPACITY; i++)
	{
		dropOneItem(m_Grids[i]);
	}
}

void ErosionStorageBox::dropItems(WCoord BlockPos)
{
	for (int i = 0; i < EROSION_STORAGEBOX_CAPACITY; i++)
	{
		dropOneItem(m_Grids[i], BlockPos);
	}
}

void ErosionStorageBox::addOpenUIN(int uin)
{
	if (m_ParentBox)
	{
		m_ParentBox->addOpenUIN(uin);
	}
	else ErosionContainer::addOpenUIN(uin);
}

void ErosionStorageBox::removeOpenUIN(int uin)
{
	if (m_ParentBox)
	{
		m_ParentBox->removeOpenUIN(uin);
	}
	else ErosionContainer::removeOpenUIN(uin);
}

bool ErosionStorageBox::canPutItem(int index)
{
	return true;
}

static void SetErosionStorageBlockClosed(World* pworld, const WCoord& pos)
{
	int blockid = pworld->getBlockID(pos.x, pos.y, pos.z);
	int blockdata = pworld->getBlockData(pos.x, pos.y, pos.z);

	// 大储物箱data数据新增meshIndex 不能抹掉 code-by:lizb
	if (isHorizontalBigChest(blockid) || isVerticalBigChest(blockid))
	{
		blockdata = blockdata & 7;
	}
	else
	{
		blockdata = blockdata % 4;
	}

	pworld->setBlockData(pos.x, pos.y, pos.z, blockdata);
}

void ErosionStorageBox::onAttachUI()
{
	m_AttachToUI = true;

	for (int i = 0; i < EROSION_STORAGEBOX_CAPACITY; i++)
	{
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", STORAGE_START_INDEX + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}

	if (m_AppendBox)
	{
		m_AppendBox->m_AttachToUI = true;
		for (int i = 0; i < EROSION_STORAGEBOX_CAPACITY; i++)
		{
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("grid_index", STORAGE_START_INDEX + i + EROSION_STORAGEBOX_CAPACITY);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
			}
		}
	}
}

void ErosionStorageBox::onDetachUI()
{
	m_AttachToUI = false;

	SetErosionStorageBlockClosed(m_World, m_BlockPos);

	if (m_AppendBox)
	{
		m_AppendBox->m_AttachToUI = false;
		SetErosionStorageBlockClosed(m_World, m_AppendBox->m_BlockPos);
	}
}

void ErosionStorageBox::append(ErosionStorageBox* box)
{
	if (box && (box->m_ParentBox || box->m_AppendBox))
		return;
	if (m_AppendBox) m_AppendBox->m_ParentBox = NULL;
	m_AppendBox = box;
	if (m_AppendBox)
	{
		m_AppendBox->resetOpenUINs();
		m_AppendBox->m_ParentBox = this;
	}
}

BackPackGrid* ErosionStorageBox::genRandomGrid(bool clear_others)
{
	std::vector<int>indices;
	for (int i = 0; i < sizeof(m_Grids) / sizeof(BackPackGrid); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (!grid.isEmpty())
		{
			indices.push_back(i);
		}
	}

	if (indices.empty()) return NULL;
	else
	{
		int sel = GenRandomInt(indices.size());
		if (clear_others)
		{
			for (int i = 0; i < (int)indices.size(); i++)
			{
				if (i == sel) continue;
				m_Grids[indices[i]].clear();
			}
		}
		return &m_Grids[indices[sel]];
	}
}

bool ErosionStorageBox::checkEmptyGrid(int resid)
{
	for (int i = 0; i < (int)(sizeof(m_Grids) / sizeof(BackPackGrid)); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (grid.isEmpty())
		{
			return true;
		}
		if (grid.getItemID() == resid)
		{
			return true;
		}
	}
	if (m_AppendBox != NULL)
	{
		for (int i = 0; i < sizeof(m_AppendBox->m_Grids) / sizeof(BackPackGrid); i++)
		{
			BackPackGrid& grid = m_AppendBox->m_Grids[i];
			if (grid.isEmpty())
			{
				return true;
			}
			if (grid.getItemID() == resid)
			{
				return true;
			}
		}
	}
	return false;
}

bool ErosionStorageBox::isCompletelyEmpty()
{
	// 检查主储物箱是否为空
	for (int i = 0; i < (int)(sizeof(m_Grids) / sizeof(BackPackGrid)); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (!grid.isEmpty())
		{
			return false;
		}
	}
	
	// 检查附加储物箱是否为空（如果存在）
	if (m_AppendBox != NULL)
	{
		for (int i = 0; i < sizeof(m_AppendBox->m_Grids) / sizeof(BackPackGrid); i++)
		{
			BackPackGrid& grid = m_AppendBox->m_Grids[i];
			if (!grid.isEmpty())
			{
				return false;
			}
		}
	}
	
	return true;
}

bool ErosionStorageBox::isNeedDestroyWhenEmpty()
{
	return m_isNeedDestroyWhenEmpty;
}

void ErosionStorageBox::setItem(int offset, int resid, int num, const char* userdata)
{
	assert(offset >= 0 && offset < sizeof(m_Grids) / sizeof(BackPackGrid));

	BackPackGrid& grid = m_Grids[offset];
	SetBackPackGrid(grid, resid, num);
	grid.setUserdataStr(userdata);

	afterChangeGrid(grid.getIndex());
}

int ErosionStorageBox::addItem_byGridCopyData(const GridCopyData& gridcopydata)
{
	int num = gridcopydata.num;
	int numgrid = getGridCount();
	if (numgrid > EROSION_STORAGEBOX_CAPACITY) numgrid = EROSION_STORAGEBOX_CAPACITY;

	int sum = InsertItemToSameGrids(this, 0, &m_Grids[0], numgrid, gridcopydata.resid, num);
	if (sum < num && m_AppendBox != NULL)
	{
		sum += InsertItemToSameGrids(this, EROSION_STORAGEBOX_CAPACITY, &m_AppendBox->m_Grids[0], EROSION_STORAGEBOX_CAPACITY, gridcopydata.resid, num - sum);
	}

	if (sum < num)
	{
		GridCopyData tmpdata(gridcopydata);
		tmpdata.num = num - sum;
		sum += InsertItemToEmptyGrids_byGridCopyData(this, 0, &m_Grids[0], numgrid, tmpdata);
		if (sum < num && m_AppendBox != NULL)
		{
			GridCopyData tmpdata2(gridcopydata);
			tmpdata2.num = num - sum;
			sum += InsertItemToEmptyGrids_byGridCopyData(this, EROSION_STORAGEBOX_CAPACITY, &m_AppendBox->m_Grids[0], EROSION_STORAGEBOX_CAPACITY, tmpdata2);
		}
	}

	return sum;
}

void ErosionStorageBox::clear()
{
	ErosionStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	if (container == NULL) return;
	int maxgrids = container->getGridCount();

	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = container->index2Grid(i + STORAGE_START_INDEX);
		if (grid && !grid->isEmpty())
		{
			grid->clear();
			container->afterChangeGrid(grid->getIndex());
		}
	}
}

int ErosionStorageBox::addItemByCount(int itemid, int count)
{
	ErosionStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	if (container == NULL) return 0;

	GridCopyData copydata;
	copydata.resid = itemid;
	copydata.num = count;

	ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (def)
	{
		int result = GetISandboxActorSubsystem()->AddGridData(this, itemid, copydata);

		if (result >= 0)
		{
			return result;
		}
	}

	return addItem_byGridCopyData(copydata);
}

void ErosionStorageBox::removeItemByCount(int itemid, int count)
{
	ErosionStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	if (container == NULL) return;
	int maxgrids = container->getGridCount();

	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = container->index2Grid(i + STORAGE_START_INDEX);
		if (grid && grid->getItemID() == itemid)
		{
			if (count >= grid->getNum())
			{
				count -= grid->getNum();
				grid->addNum(-grid->getNum());
				grid->clear();
				container->afterChangeGrid(grid->getIndex());
			}
			else
			{
				grid->addNum(-count);
				container->afterChangeGrid(grid->getIndex());
				return;
			}
		}
	}
}

void ErosionStorageBox::removeItemByIndex(int index, int num)
{
	ErosionStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	if (container == NULL) return;

	BackPackGrid* pgrid = container->index2Grid(index);
	if (pgrid == NULL) return;

	if (num > pgrid->getNum()) num = pgrid->getNum();

	if (pgrid->addNum(-num) == 0) pgrid->clear();

	container->afterChangeGrid(index);
}

flatbuffers::Offset<FBSave::ChunkContainer> ErosionStorageBox::save(SAVE_BUFFER_BUILDER& builder)
{
	auto actor = saveContainerErosionStorage(builder);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerErosionStorage, actor.Union());
}

bool ErosionStorageBox::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerErosionStorage*>(srcdata);
	loadErosionContainer(src->basedata());

	auto items = src->items();
	auto indices = src->indices();

	for (size_t i = 0; i < items->size(); i++)
	{
		int index = indices->Get(i);
		assert(index >= 0 && index < sizeof(m_Grids) / sizeof(m_Grids[0]));

		m_Grids[index].load(items->Get(i));
	}

	m_GridCount = src->key();
	m_isNeedDestroyWhenEmpty = src->clearflag();

	return true;
} 