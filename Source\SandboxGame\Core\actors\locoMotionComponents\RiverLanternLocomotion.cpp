﻿
#include "RiverLanternLocomotion.h"
#include "BlockMaterialMgr.h"
#include "special_blockid.h"
#include "world.h"
#include "ClientActorLiving.h"
#include "LivingLocoMotion.h"
#include "ActorRiverLantern.h"
#include "ClientActor.h"
#include "OgreWCoord.h"
#include "FireBurnComponent.h"
#include "ClientActorFuncWrapper.h"
using namespace MINIW;
using namespace Rainbow;

static Vector3f s_MotionDecay(0.99f, 0.98f, 0.99f);
static float s_InWaterForce = 0.5f;
static float s_MaxSpeedInWater = 10;

int GetActorLanternDepthInLiquid(ActorLocoMotion *locomove, bool onlywater)
{
	BlockMaterial* inkWaterMtl = g_BlockMtlMgr.getMaterial(BLOCK_INK_STILL_WATER);
	BlockMaterial *watermtl = g_BlockMtlMgr.getMaterial(BLOCK_STILL_WATER);
	BlockMaterial *lavamtl = g_BlockMtlMgr.getMaterial(BLOCK_STILL_LAVA);
	CollideAABB box, tmpbox;
	locomove->getCollideBox(box);

	//把box分为SEGMENTS层，检测每一层是否在液体中，累加结果
	int SEGMENTS = 5;
	int inwater_h = 0;
	for (int i = 0; i<SEGMENTS; i++)
	{
		tmpbox.pos = WCoord(box.minX(), box.minY() + box.dim.y*i / SEGMENTS, box.minZ());
		tmpbox.dim = WCoord(box.dim.x, box.dim.y / SEGMENTS, box.dim.z);
		if (locomove->m_pWorld->isBoxInMaterial(tmpbox, watermtl) || locomove->m_pWorld->isBoxInMaterial(tmpbox, inkWaterMtl))
		{
			inwater_h += BLOCK_SIZE / SEGMENTS;
		}
		else if (!onlywater && locomove->m_pWorld->isBoxInMaterial(tmpbox, lavamtl))
		{
			inwater_h += BLOCK_SIZE / SEGMENTS;
		}
	}

	return inwater_h;
}

static float CalLiquidLanternFloatForce(ActorLocoMotion *locomove, bool onlywater, float curmy)
{
	int inwater_h = GetActorLanternDepthInLiquid(locomove, onlywater);

	float tmpY = 0;
	//在水中受向上浮力和重力
	if (inwater_h > 0)
	{
		tmpY =  curmy +   1.5f * (inwater_h + 18) / BLOCK_SIZE  -0.8f;
		tmpY = Clamp(tmpY, -1.0f, 1.0f);
	}
	else
	{
		//不在水中，只受重力
		tmpY = curmy - 2;
	}

	return tmpY;

}

IMPLEMENT_COMPONENTCLASS(RiverLanternLocomotion)

void RiverLanternLocomotion::initPosition(const WCoord& pos)
{
	m_Position = pos;
	boatPos = pos;
}

void RiverLanternLocomotion::tick()
{
	/******ActorLocoMotion::tick()  start**/
	if (getOwnerActor()->needClear()) return;

	WCoord contract = WCoord(1, 40, 1);
	if (contract.y > m_BoundHeight / 2 - 1) contract.y = m_BoundHeight / 2 - 1;

	WCoord minpos = m_Position - WCoord(m_BoundSize / 2, 0, m_BoundSize / 2) + contract;
	WCoord maxpos = m_Position + WCoord(m_BoundSize / 2, m_BoundHeight, m_BoundSize / 2) - contract;

	Vector3f flowmotion;
	if (m_pWorld->getFluidFlowMotion(minpos, maxpos, flowmotion))
	{
		m_Motion.x += flowmotion.x;
		m_Motion.z += flowmotion.z;

		m_InWater = true;

		auto functionWrapper = getOwnerActor()->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(0);
		}

		auto FireBurnComp = getOwnerActor()->getFireBurnComponent();
		if (FireBurnComp)
		{
			FireBurnComp->setFire(0, 0);
		}

	}
	else
	{
		m_InWater = false;
	}


	if (!m_pWorld->isRemoteMode() && m_Position.y < -64 * BLOCK_SIZE && !getOwnerActor()->isDead())
	{
		getOwnerActor()->kill();
	}
	/******ActorLocoMotion::tick()  end**/

	WCoord oldpos = m_Position;
	float motionlen = Rainbow::Sqrt(m_Motion.x*m_Motion.x + m_Motion.z*m_Motion.z);

	if (m_pWorld->isRemoteMode() && static_cast<ActorRiverLantern *>(getOwnerActor())->m_HostMotivate)
	{
		if (boatPosRotationIncrements > 0)
		{
			m_Position = m_Position + (boatPos - m_Position) / boatPosRotationIncrements;
			m_RotateYaw = m_RotateYaw + WrapAngleTo180(boatYaw - m_RotateYaw) / boatPosRotationIncrements;
			m_RotationPitch = m_RotationPitch + (boatPitch - m_RotationPitch) / boatPosRotationIncrements;

			boatPosRotationIncrements--;
		}
		else
		{
			m_Position = boatPos;
			//m_Position = m_Position + getIntegerMotion(m_Motion);
			//if (m_OnGround) m_Motion *= 0.5f;
			//m_Motion *= s_MotionDecay;
		}
	}
	else
	{
		m_Motion.y = CalLiquidLanternFloatForce(this, true, m_Motion.y);

		float newlen = Rainbow::Sqrt(m_Motion.x*m_Motion.x + m_Motion.z*m_Motion.z);

		const float MAX_MOTION_LEN = 52.0f;
		if (newlen > MAX_MOTION_LEN)
		{
			float t = MAX_MOTION_LEN / newlen;
			m_Motion.x *= t;
			m_Motion.z *= t;
			newlen = MAX_MOTION_LEN;
		}

		if (newlen>motionlen && speedMultiplier<MAX_MOTION_LEN)
		{
			speedMultiplier += (MAX_MOTION_LEN - speedMultiplier) / MAX_MOTION_LEN;

			if (speedMultiplier > MAX_MOTION_LEN)
			{
				speedMultiplier = MAX_MOTION_LEN;
			}
		}
		else
		{
			speedMultiplier -= (speedMultiplier - MAX_MOTION_LEN / 5.0f) / MAX_MOTION_LEN;

			if (speedMultiplier < MAX_MOTION_LEN / 5.0f)
			{
				speedMultiplier = MAX_MOTION_LEN / 5.0f;
			}
		}

		if (m_OnGround && !getOwnerActor()->isInWater())
		{
			m_Motion *= 0.5f;
		}


		if (motionlen > s_MaxSpeedInWater && m_Motion.NormalizeSafe() != 0.f)
			m_Motion *= s_MaxSpeedInWater;

		doMoveStep(m_Motion);


		m_RotationPitch = 0.0f;

		float targetyaw = m_RotateYaw;

		Vector3f dpos = m_Motion;
		dpos.y = 0;
		if (dpos.LengthSqr() > 10.0f)
		{
			Direction2PitchYaw(&targetyaw, NULL, dpos);
		}

		m_RotateYaw += Clamp(WrapAngleTo180(targetyaw - m_RotateYaw), -20.0f, 20.0f);
	}
}