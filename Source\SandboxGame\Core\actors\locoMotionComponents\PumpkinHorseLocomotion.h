#ifndef __PUMPKIN_HORSE_LOCOMOTION_H__
#define __PUMPKIN_HORSE_LOCOMOTION_H__
#include "HorseLocomotion.h"
/*	
author: qiwi
date:2021-04-28
desc: 南瓜车坐骑
*/

// 南瓜坐骑
class PumpkinHorseLocomotion : public HorseLocomotion //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(PumpkinHorseLocomotion)

	//tolua_begin
	PumpkinHorseLocomotion()
	{
		m_MaxSpeed = 0;
		speedMultiplier = 20.0f;
	}
	//tolua_end
	virtual ~PumpkinHorseLocomotion() {}

	virtual void tick() override;
	
public:
	//tolua_begin
	float m_MaxSpeed;
	float speedMultiplier;
	//tolua_end
}; //tolua_exports

#endif //__ACTORPUMPKINHORSE_H__

