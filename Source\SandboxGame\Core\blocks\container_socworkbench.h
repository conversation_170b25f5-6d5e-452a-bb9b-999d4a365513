#pragma once

#include "container_world.h"
#include "SandboxGame.h"
#include "container_erosion.h"
class  ContainerSocWorkbench : public ErosionContainer
{
public:
    ContainerSocWorkbench(int baseindex = 0);
    ContainerSocWorkbench(const WCoord& blockpos, const int blockId, int baseindex = 0);
    virtual ~ContainerSocWorkbench();

    virtual void enterWorld(World* pworld) override;
    virtual void leaveWorld() override;

    virtual FBSave::ContainerUnion getUnionType()
    {
        return FBSave::ContainerUnion_ContainerSocWorkbench;
    }
    virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
    virtual bool load(const void* srcdata);
	void setValueInt(int s)
	{
		m_Value = s;
	}
	int getValueInt()
	{
		return m_Value;
	}
public:
	int m_SubType;
	int m_Value;
};