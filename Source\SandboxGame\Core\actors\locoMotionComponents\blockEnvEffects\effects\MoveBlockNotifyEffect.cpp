#include "MoveBlockNotifyEffect.h"

#include "ClientPlayer.h"

using namespace MNSandbox;


void MoveBlockNotifyEffect::executeEffect(ClientActor* pActor)
{
	if (!pActor)
	{
		return;
	}
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(pActor);// context.GetData_Usertype<ClientPlayer>("target");
	if (player) {
		int moveBlockNum = GetMoveBlockNum();// context.GetData_Number("moveBlockNum");
		for (int i = 0; i < moveBlockNum; i++){
			player->moveOneBlockSizeOnTrigger();
		}
	}
}