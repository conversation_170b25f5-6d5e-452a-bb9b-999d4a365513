#include "PlayerSetterComponent.h"
//#include "IClientGameManagerInterface.h"
#include "WorldManager.h"
#include "LuaInterfaceProxy.h"
#include "ClientInfoProxy.h"

//---------------------------PlayerSetterCompoent---------------------------

IMPLEMENT_COMPONENTCLASS(PlayerSetterComponent)

PlayerSetterComponent::PlayerSetterComponent() 
	: m_BaseModelType(0), m_BaseModelScale(1.0f), m_StrengthUsed(false), m_StrengthFoodShowState(1)
	
{
	memset(m_BaseAttrs, 0, sizeof(m_BaseAttrs));

	m_BasePermitStates = ENABLE_NEWINITVALUE;
	m_CustomFuncStates = CUSTOM_FUNC_INITVALUE;

	InitAttrWithDefualt();
}

PlayerSetterComponent::~PlayerSetterComponent()
{
	auto iter = m_BaseItems.begin();
	for (; iter != m_BaseItems.end(); iter++)
	{
		OGRE_DELETE(iter->second);
	}
	m_BaseItems.clear();
}

void PlayerSetterComponent::InitAttrWithDefualt(int ctype)
{
	ConstAtLua* constAtLua = GetLuaInterfaceProxy().get_lua_const();
	m_BaseAttrs[PATTR_HP] = constAtLua->hpmax;
	m_BaseAttrs[PATTR_STRENGTH] = constAtLua->strengthmax;
	m_BaseAttrs[PATTR_HUNGER] = 100;
	m_BaseAttrs[PATTR_THIRST] = 100;  // 默认口渴值设为100
	m_BaseAttrs[PATTR_SPEED] = 10;
}

void PlayerSetterComponent::setBaseModel(const char* modelId, float scale)
{
	m_BaseModelId = modelId;
	m_BaseModelScale = scale;
}

void PlayerSetterComponent::setBaseModelData(const char* modelJsonData)
{
	m_BaseModelData = modelJsonData;
}

void PlayerSetterComponent::setBaseModelDataById(const std::string& modelId)
{
	//�Զ���װ������������� �����ͻ���ͨ�����û�ȡ��
	if (modelId.empty() || modelId[0] != 'a') return;

	int pos = modelId.find('_');
	if (pos == string::npos) return;

	std::string dataId = modelId.substr(pos + 1);

	const int cfgLen = 10240;
	char skinCfg[cfgLen] = ""; //������ȳ���Len��Χ���ؿ����ݷ�ֹ����
	MINIW::ScriptVM::game()->callFunction("PlayerModelGetAvatorSkinCfg", "si>s", dataId.c_str(), cfgLen, skinCfg);
	assert(skinCfg[0] != '\0');

	setBaseModelData(skinCfg);
}

bool PlayerSetterComponent::getPermitState(int pType)
{
	return (m_BasePermitStates & pType) > 0;
}

void PlayerSetterComponent::setPermitState(int pType, bool enable)
{
	if (enable)
		m_BasePermitStates |= pType;
	else
		m_BasePermitStates &= ~pType;
}

bool PlayerSetterComponent::getCustomFuncState(int fType)
{
	return (m_CustomFuncStates & fType) > 0;
}

void PlayerSetterComponent::setCustomFuncState(int fType, bool enable)
{
	if (enable)
		m_CustomFuncStates |= fType;
	else
		m_CustomFuncStates &= ~fType;
}

float PlayerSetterComponent::getBaseAttr(PRIME_ATTR_TYPE attrType)
{
	assert(attrType >= 0 && attrType < MAX_PATTR_TYPE);

	return m_BaseAttrs[attrType];
}

void PlayerSetterComponent::setBaseAttr(PRIME_ATTR_TYPE attrType, float attrValue)
{
	assert(attrType >= 0 && attrType < MAX_PATTR_TYPE);

	m_BaseAttrs[attrType] = (short)attrValue;
}

GameBaseItem* PlayerSetterComponent::getBaseItem(int index)
{
	//BACKPACK_START_INDEX/SHORTCUT_START_INDEX/EQUIP_START_INDEX

	auto iter = m_BaseItems.find(index);
	if (iter != m_BaseItems.end())
	{
		return iter->second;
	}

	return NULL;
}

void PlayerSetterComponent::setBaseItem(int index, int itemId, int num)
{
	auto iter = m_BaseItems.find(index);
	if (iter == m_BaseItems.end())
	{
		if (num == 0) return;
		GameBaseItem *item = ENG_NEW(GameBaseItem);
		item->ID = itemId;
		item->num = num;
		item->index = index;
		m_BaseItems[index] = item;
	}
	else
	{
		if (num == 0) //�Ƴ�
		{
			OGRE_DELETE(iter->second);
			m_BaseItems.erase(iter);
			return;
		}
		iter->second->num = num;
		iter->second->ID = itemId;
	}
}

void PlayerSetterComponent::load(const FBSave::PlayerSetterData* src, int version /* = 0 */)
{
	if (src->modelid() != NULL)
	{
		m_BaseModelId = src->modelid()->c_str();

		// ������Ҫ����ģ������
		if (g_WorldMgr && !g_WorldMgr->isRemote())
		{
			this->setBaseModelDataById(m_BaseModelId);
		}
	}
	m_BaseModelScale = src->modelscale();
	m_BaseModelType = src->modeltype();

	m_CustomFuncStates = src->funcstates();
	m_BasePermitStates = src->basepermits();

	// �����ݼ��ݣ��Զ���׼���¼ӵ��ĸ���־λ�ϵ�ͼĬ�϶���0����Ҫ�ĳ�1,��֤�ϵ�ͼ���������, 72960 = 1.29.0
	// 	ENABLE_STORAGE��ENABLE_BACKPACKCRAFT��ENABLE_SNEAK��ENABLE_RUN
	// code by 2023.8.22 chenshaobin
	int gameVersionInt = GetClientInfoProxy()->GetGameVersionIntForClientInfo();
#ifdef IWORLD_UNIVERSE_BUILD 
		if (gameVersionInt >= 66816 && version < 66822) //��������1.5.0
#else
		if (gameVersionInt >= 72960 && version < 72960)
#endif
	{
		m_BasePermitStates |= 0x1e0000;
	}

 	m_StrengthUsed = src->usestrength();
	m_StrengthFoodShowState = src->strengthfoodshowstate();

	if (src->baseattrs())
	{
		for (size_t i = 0; i < src->baseattrs()->size(); i++)
		{
			m_BaseAttrs[i] = src->baseattrs()->Get(i);
		}
	}

	//���ݾɵ�ͼ���ɵ�ͼת����ð��ģʽʱ�����õ�ǰ����Ϊ���ֵ��
	if (m_BaseAttrs[PATTR_STRENGTH] <= 0)
	{
		ConstAtLua* constAtLua = GetLuaInterfaceProxy().get_lua_const();
		m_BaseAttrs[PATTR_STRENGTH] = constAtLua->strengthmax;
	}

	if (src->baseitems())
	{
		for (size_t i = 0; i < src->baseitems()->size(); i++)
		{
			auto fbsItem = src->baseitems()->Get(i);
			if (fbsItem != NULL)
			{
				int index = fbsItem->index();
				GameBaseItem *baseItem = ENG_NEW(GameBaseItem);

				baseItem->index = index;
				baseItem->num = fbsItem->num();
				if (fbsItem->newitemid() > 0 ) // itemid�ᳬ�� �������ȶ�ȡnewitemid
				{
					baseItem->ID = fbsItem->newitemid();
				}
				else if (fbsItem->itemid() > 0)
				{
					baseItem->ID = fbsItem->itemid();
				}
				else
				{
					baseItem->ID = 0;
				}

				if (m_BaseItems.find(index) == m_BaseItems.end())
				{
					m_BaseItems[index] = baseItem;
				}
			}
		}
	}
}

flatbuffers::Offset<FBSave::PlayerSetterData> PlayerSetterComponent::save(flatbuffers::FlatBufferBuilder &builder)
{
	std::vector<int16_t> setterAttrs;
	for (size_t i = 0; i < MAX_PATTR_TYPE; i++)
	{
		setterAttrs.push_back(m_BaseAttrs[i]);
	}

	auto newIter = m_BaseItems.begin();
	std::vector<flatbuffers::Offset<FBSave::GameBaseItem>> setterItems;
	for (; newIter != m_BaseItems.end(); newIter++)
	{
		const GameBaseItem* baseItem = newIter->second;
		auto eq = FBSave::CreateGameBaseItem(builder,0, baseItem->index, baseItem->num, baseItem->getID());
		setterItems.push_back(eq);
	}

	auto baseModelId = builder.CreateString(m_BaseModelId);

	return FBSave::CreatePlayerSetterData(builder, baseModelId, m_BaseModelType, m_BaseModelScale,
		builder.CreateVector(setterAttrs), builder.CreateVector(setterItems), m_BasePermitStates, m_CustomFuncStates, m_StrengthUsed, m_StrengthFoodShowState);
}