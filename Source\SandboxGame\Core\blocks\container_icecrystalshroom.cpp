#include "container_icecrystalshroom.h"
#include "EffectModel.h"
#include"BlockScene.h"
#include "ClientActorDef.h"
#include "OgreEntity.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "SandboxIdDef.h"
#include "EffectManager.h"

ContainerIceCrystalShroom::ContainerIceCrystalShroom()
{
	m_curAnimId = -1;
	m_pEntity = nullptr; 
	m_NeedTick = true;
}

ContainerIceCrystalShroom::ContainerIceCrystalShroom(WCoord BlockPos)
	:WorldContainer(BlockPos, 0)
{
	m_curAnimId = -1;
	m_pEntity = nullptr;
	m_NeedTick = true;
}

ContainerIceCrystalShroom::~ContainerIceCrystalShroom()
{
	Rainbow::Entity::Destory(m_pEntity);
}

int ContainerIceCrystalShroom::getObjType() const
{
	return OBJ_TYPE_ICECRYSTALSHROOM;
}

void ContainerIceCrystalShroom::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateDisplay();

	if (!pworld) return;
	int blockid = pworld->getBlockID(m_BlockPos);
	if (blockid == BLOCK_ICRCRYSTALSHROOM_LIGHT)
	{
#ifndef IWORLD_SERVER_BUILD	

		int blockdata = pworld->getBlockData(m_BlockPos);
		int dir = blockdata & 3;

		if (m_pEntity)
		{
			m_pEntity->DetachFromScene();
			Rainbow::Entity::Destory(m_pEntity);
		}

		m_pEntity = Rainbow::Entity::Create();

#if ENTITY_MODIFY_MODEL_ASYNC
		m_pEntity->LoadAsync("entity/150009/body.omod", false);
#else
		Rainbow::Model* model = NULL;
		model = g_BlockMtlMgr.getModel("entity/150009/body.omod");
		if (!model) return;
		m_pEntity->Load(model);
#endif

		m_pEntity->ShowSkins(true);
		WCoord pos = m_BlockPos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2);

		WCoord step;
		WCoord center;
		Rainbow::Vector3f angle;
		angle = Rainbow::Vector3f::zero;

		if (dir == DIR_NEG_X)
		{
			angle.y = 90;
			step.z = BLOCK_SIZE;
		}
		else if (dir == DIR_POS_X)
		{
			angle.y = 270;
			step.x = BLOCK_SIZE;

		}
		else if (dir == DIR_NEG_Z)
		{

		}
		else if (dir == DIR_POS_Z)
		{
			angle.y = 180;
			step.x = BLOCK_SIZE;
			step.z = BLOCK_SIZE;
		}
			
		m_pEntity->SetRotation(angle.y, angle.x, angle.z);
		//pos += step;
		m_pEntity->SetPosition(pos.toWorldPos());

		if (m_World)
			m_pEntity->AttachToScene(pworld->getScene());

		// 覆雪还是非覆雪，喷发还是非喷发
		int animID = 0;
		int spayState = (blockdata & 4) >> 2;
		int snowState = (blockdata & 8) >> 3;
		// 覆雪喷发
		if (snowState == 1 && spayState == 1)
		{
			animID = 100981;
		}
		// 覆雪待机
		else if (snowState == 1)
		{
			animID = 100100;
		}
		// 普通喷发
		else if (spayState == 1)
		{
			animID = 100983;
		}
		// 普通待机
		else
		{
			animID = 100982;
		}

		playAnim(animID);

#endif
	}
}

void ContainerIceCrystalShroom::leaveWorld()
{
	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
	}
	WorldContainer::leaveWorld();
}

void ContainerIceCrystalShroom::updateTick()
{
}

void ContainerIceCrystalShroom::updateDisplay(float dtime)
{
	// 补光照
	if (m_pEntity)
	{
		Rainbow::Vector4f lightparam(0, 0, 0, 0);
		auto wp = m_pEntity->GetWorldPosition();
		if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(wp));
		lightparam.x += 0.2f;
		lightparam.y += 0.2f;
		m_pEntity->SetInstanceData(lightparam);
		unsigned int dtick = Rainbow::TimeToTick(dtime);
		m_pEntity->Tick(dtick);
	}
}

void ContainerIceCrystalShroom::playAnim(int animId, int times)
{
	if (m_pEntity)
	{
		if (m_curAnimId != animId)
		{
			m_curAnimId = animId;
			m_pEntity->StopAnim();
			m_pEntity->PlayAnim(animId, times);
		}
	}
}

void ContainerIceCrystalShroom::stopAnim()
{
	if (m_pEntity)
	{
		m_pEntity->StopAnim();
	}
}

bool ContainerIceCrystalShroom::hasAnimPlaying(int anim)
{
	if (m_pEntity)
	{
		return m_pEntity->HasAnimPlaying(anim);
	}
	return false;
}

void ContainerIceCrystalShroom::playSound(World* pworld, const char* name)
{
	pworld->getEffectMgr()->playPosSound(m_BlockPos, name, 1.0, 1.0, false, false);
}

bool ContainerIceCrystalShroom::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerIceCrystalShroom*>(srcdata);
	return loadContainerCommon(src->basedata());
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerIceCrystalShroom::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerShells(builder, basedata);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerIceCrystalShroom, actor.Union());
}

