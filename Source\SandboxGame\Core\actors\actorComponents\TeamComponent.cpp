#include "TeamComponent.h"
#include "ClientPlayer.h"
#include "ClientMob.h"
#include "GameMode.h"
#include "WorldManager.h"
#include "PlayerAttrib.h"
#include "ActionAttrStateComponent.h"
#include "AIFollowDirection.h"
IMPLEMENT_COMPONENTCLASS(TeamComponent)


TeamComponent::TeamComponent():m_TeamID(0)
{

}

void TeamComponent::setTeam(int id, bool ResetAttr)
{
	if (!GetOwner()) return ;
	ActorLiving* m_owner = dynamic_cast<ActorLiving*>(GetOwner());
	if (!m_owner) return ;
	m_TeamID = id;
	m_owner->setHPProgressDirty();
	ClientMob* pMob = dynamic_cast<ClientMob*>(m_owner);
	if (pMob)
	{
		auto pAITask = pMob->getAITask();
		if (pAITask)
		{
			AIFollowDirection* p = static_cast<AIFollowDirection*>(pAITask->getTaskAI<AIFollowDirection>());
			if (p)
			{
				p->updateTeamid();
			}
		}
	}
	

}

bool TeamComponent::isSameTeam(ActorLiving *target)
{
	if (target == NULL)
		return false;

	return m_TeamID == target->getTeam() && m_TeamID > 0;
}

void TeamComponent::addGameScoreByRule(int ruleid, int num)// = 1
{

}

IMPLEMENT_COMPONENTCLASS(MobTeamComponent)

MobTeamComponent::MobTeamComponent():TeamComponent()
{

}

bool MobTeamComponent::isSameTeam(ActorLiving *target)
{
	if (!GetOwner()) return NULL;
	m_ownerMob = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_ownerMob) return NULL;
	if(m_ownerMob->getTamed())
	{
		ClientPlayer *owner = m_ownerMob->getTamedOwner();
		if(target == owner)
		{
			return true;
		}

		if(owner) return owner->isSameTeam(target);
	}

	return TeamComponent::isSameTeam(target);
}

void MobTeamComponent::addGameScoreByRule(int ruleid, int num)// = 1
{
	GameMode *gmaker = static_cast<GameMode*>(GetWorldManagerPtr()->m_RuleMgr);
	if(gmaker)
	{
		int s = int(gmaker->getRuleOptionVal((GAMEMAKER_RULE)ruleid)*num);

		gmaker->addTeamScore(m_TeamID, s);
	}	
}

IMPLEMENT_COMPONENTCLASS(PlayerTeamComponent)

PlayerTeamComponent::PlayerTeamComponent():TeamComponent()
{

}

void PlayerTeamComponent::onChangeTeam(int oldteamid)
{
	if (!GetOwner()) return;
	m_ownerPlayer = dynamic_cast<ClientPlayer*>(GetOwner());
	int opWay = m_ownerPlayer->getOPWay();
	if ((opWay == PLAYEROP_WAY_FOOTBALLER || opWay == PLAYEROP_WAY_BASKETBALLER || opWay == PLAYEROP_WAY_PUSHSNOWBALL) && !m_ownerPlayer->getCatchBall())
	{
		ActorLiving* m_owner = dynamic_cast<ActorLiving*>(GetOwner());
		char effname[256];
		sprintf(effname, "%s%d", "ball_team_", oldteamid);

		ActorBody* body = m_owner->getBody();
		m_owner->stopMotion(effname);

		m_ownerPlayer->updateBallEffect();
	}
}

void PlayerTeamComponent::setTeam(int id, bool ResetAttr)
{
	if (getTeam() != id)
		onChangeTeam(getTeam());

	TeamComponent::setTeam(id, ResetAttr);

	if (!GetOwner()) return;
	m_ownerPlayer = dynamic_cast<ClientPlayer*>(GetOwner());

	m_ownerPlayer->applyDisplayName();

	if (GetWorldManagerPtr())
	{
		std::vector<IClientPlayer *> players;
		GetWorldManagerPtr()->getAllPlayers(players);
		for (auto it = players.begin(); it != players.end(); it++)  // 客机刷新其他玩家的昵称显示等
		{
			ClientPlayer* itTemp = (*it) ? (*it)->GetPlayer() : nullptr;
			if (itTemp && m_ownerPlayer != itTemp)
			{
				itTemp->applyDisplayName();
			}
		}
	}
	if (ResetAttr)
	{
		ResetPlayerAttr();
	}
	

}

void PlayerTeamComponent::addGameScoreByRule(int ruleid, int num)// = 1
{
	GameMode* gmaker = static_cast<GameMode*>(GetWorldManagerPtr()->m_RuleMgr);
	if (gmaker)
	{
		m_ownerPlayer = dynamic_cast<ClientPlayer*>(GetOwner());
		int s = int(gmaker->getRuleOptionVal((GAMEMAKER_RULE)ruleid) * num);
		
		int curGameScore = m_ownerPlayer->getGameScore();
		curGameScore += s;
		if (curGameScore < 0) curGameScore = 0;
		m_ownerPlayer->setGameScore(curGameScore);

		gmaker->addTeamScore(m_TeamID, s);
	}		
}

void PlayerTeamComponent::ResetPlayerAttr()
{
	//设置队伍数据
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode())
	{
		int id = getTeam();
		auto manager = GetWorldManagerPtr()->getBaseSettingManager();
		if (manager && manager->getTeamEnable(id))
		{
			if (!GetOwner()) return ;
			ActorLiving* m_owner = dynamic_cast<ActorLiving*>(GetOwner());
			if (!m_owner) return ;
			//初始权限
			unsigned int statesValue = manager->getPermitStatesValue(id);
			auto ActionAttrStateComp = m_owner->getActionAttrStateComponent();
			if (ActionAttrStateComp)
			{
				ActionAttrStateComp->setAllActionAttrState(statesValue & ENABLE_INITVALUE);
			}

			//基础属性
			PlayerAttrib* pAttrib = m_ownerPlayer->getPlayerAttrib();
			if (pAttrib) pAttrib->initPlayerBaseAttr(id);

			//基础模型
			m_ownerPlayer->setBaseModel(id);

			////初始物品
			//GameMode *makerMgr = GetWorldManagerPtr()->m_RuleMgr;
			//if (makerMgr == NULL) return;
			//makerMgr->initPlayerBaseItem(m_ownerPlayer, m_ownerPlayer->getShortcutStartIndex(), id);
			//makerMgr->initPlayerBaseItem(m_ownerPlayer, BACKPACK_START_INDEX, id);
		}
	}
}

