#ifndef __CONTAINER_SANDBOX_GAME_H__
#define __CONTAINER_SANDBOX_GAME_H__

#include "container_world.h"
#include "container_erosion.h"

const int CollectWaterTickToSecond = 20;
class ClientPlayer;
class containerWaterStorage : public ErosionContainer//tolua_exports
{//tolua_exports
public:
	containerWaterStorage();
	containerWaterStorage(const WCoord& blockpos, int blockId);
	virtual ~containerWaterStorage() {};

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerWaterStorage;
	}

	virtual bool load(const void* srcdata) override;
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld() override;
	virtual void updateTick() override;
	//tolua_begin
	void addWater(int value);
	int getCurWaterVolume() { return m_nCurWaterVolume; }

	//void updateEffect();
	//void stopEffect();
	//void stopEffectByBlockdata(int blockdata); //
	//tolua_end
	void initCollectData(World* pworld);
private:
	//WCoord getEffectPos(int blockdata);

public:
private:
	unsigned short m_nCurWaterVolume = 0;
	unsigned short m_nTickCounter = 0;
	unsigned short m_nOnceTickMax = 0;
	unsigned short m_nOnceVolume = 0; 
	unsigned int m_nWaterVolumeMax = 0;

};//tolua_exports


enum ArchitectureBlockLevel
{
	WoodStyle = 1,
	StoneStyle = 2,
	IronStyle = 3,
	SteelStyle = 4,
	LevelMax = SteelStyle,
};

class containerArchitecture : public ErosionContainer//tolua_exports
{//tolua_exports
public:
	containerArchitecture();
	containerArchitecture(const WCoord& blockpos, int blockId, int bptypeid, int bplevel);
	virtual ~containerArchitecture() {};

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerArchitecture;
	}

	virtual int getObjType() const override
	{
		return  OBJ_TYPE_ARCHITECTURE;
	}

	virtual bool load(const void* srcdata);
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld() override;
	virtual void updateTick() override;

	//void initBluePrintData();
	//tolua_begin
	int getBluePrintTypeId() { return m_nBluePrintTypeId; }
	int getBluePrintLevel() { return m_nBluePrintLevel; }
	bool getCanUnDo();

	int checkArchitectureResEnough(int type, int upgradeLevel, ClientPlayer* player, bool deal = false);//type 1为升级，2为修复, 3为撤销建筑回收。返回值大于0的itemid 为成功。-1为资源不足，-2为已经是最高级, -3数据错误
	int onUpgradeBlock(int upgradeNum, ClientPlayer* player, bool deal = true);
	//bool destoryRecycle(ClientPlayer* player);
	//tolua_end

private:

public:
private:
	unsigned char m_nBluePrintTypeId = 0;
	unsigned char m_nBluePrintLevel = 0;
	short m_nCanUnDoTick = 0;

};//tolua_exports
#endif 