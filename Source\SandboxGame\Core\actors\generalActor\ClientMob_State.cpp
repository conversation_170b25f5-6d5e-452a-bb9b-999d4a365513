
#include "ClientMob.h"

#include "ActorBody.h"
#include "LivingLocoMotion.h"
#include "ActorVision.h"
#include "ActorAttrib.h"

#include "ClientActorManager.h"
#include "OgreScriptLuaVM.h"
//#include "OgreFileSystem.h"
#include "world.h"
#include "ClientPlayer.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "container.h"
#include "OgreUtils.h"
#include "backpack.h"
#include "ActorCSProto.h"
#include "special_blockid.h"
#include "Environment.h"
#include "ClientItem.h"
#include "ActorEarthCoreMan.h"
#include "ActorTrader.h"
#include "ActorHorse.h"
#include "ActorDragonMount.h"
#include "ActorNpc.h"
#include "ClientAquaticMob.h"
#include "ClientFlyMob.h"
#include "ClientActorArrow.h"
#include "ActorBall.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "MpActorManager.h"
//#include "GameMod.h"
#include "ClientPlayer.h"
#include "BlockMaterialMgr.h"
#include "OgrePrerequisites.h"
#include "ActorGhost.h"
#include "GameNetManager.h"
//#include "GameEvent.h"
//#include "stringdef.h"
#include "ActorVillager.h"
#include "BlockBed.h"
#include "MpGameSurviveCdnResMgr.h"



//#include "IClientGameManagerInterface.h"
#include "AIPickupItemEx.h"
#include "GameModeDef.h"
#include "AIFishAttack.h"
#include "AITargetHurtee.h"
#include "AISwimming.h"
#include "AIFishBeg.h"
#include "AISit.h"
#include "AIFollowOwner.h"
#include "AIWander.h"
#include "AIPetWander.h"
#include "AITargetOwnerHurtee.h"
#include "AITargetOwnerHurter.h"
#include "AITargetNonTamed.h"
#include "AIBeg.h"
#include "AIWatchClosest.h"
#include "AILookIdle.h"
#include "AIRestrictSun.h"
#include "AIFleeSun.h"
#include "AITargetNearest.h"
#include "AIBreakDoor.h"
#include "AIMoveTowardsRestriction.h"
#include "AIPanic.h"
#include "AITempt.h"
#include "AILeapAtTarget.h"
#include "AIMate.h"
#include "AIFollowParent.h"
#include "AIBoom.h"
#include "AIArrowAttack.h"
#include "AIEatGrass.h"
#include "AIRandomSwim.h"
#include "AIFearPlayer.h"
#include "AIFollowDirection.h"
#include "AITargetSpecificEntity.h"
#include "AITransfiguration.h"
#include "ProjectileFactory.h"
#include "proto_common.h"
#include "AIProjectileAttack.h"
#include "AIClosestDance.h"
#include "AISleep.h"
#include "AILoggerHeads.h"
#include "AILayEggs.h"
#include "AILayEggInNest.h"
#include "AIHatch.h"
#include "AIEatFeedBlock.h"
#include "AIToppleOver.h"
#include "AISitbyItem.h"
#include "AIMilking.h"
#include "AIEatLeaf.h"
#include "AIEatFlower.h"
#include "AIRideHorse.h"
#include "AIKickAway.h"
#include "AIMakeTrouble.h"
#include "AIGetSpecialAttackattr.h"
#include "AISitBlock.h"
#include "AILoveBlock.h"
#include "AITargetFollowingPlayer.h"
#include "AIHoldMonster.h"
#include "AICeilingAtk.h"
#include "AIGoCeiling.h"
#include "AIPetDanceToPlayer.h"
#include "AISpecialAct.h"
#include "AIHungry.h"
#include "AIRandomFly.h"
#include "AIFlyAttack.h"
#include "AIFlyBeg.h"
#include "AIFlyLoveBlock.h"
#include "AIFearItem.h"
#include "AIDissolvedByItem.h"
#include "AIAttractBlock.h"
#include "AISavageSleep.h"
#include "AIWarning.h"
#include "AIItemPanic.h"
#include "AIPlayerPanic.h"
#include "AISeparate.h"
#include "AISeparatePanic.h"
#include "AICombine.h"
#include "AIWizardFly.h"
#include "AIWizardAttack.h"
#include "AIWizardProjectileAttack.h"
#include "AIBumpAttack.h"
#include "AIDigBlock.h"
#include "AIPickupItem.h"
#include "AIStoreItem.h"
#include "AITakeItem.h"
#include "AICraftItem.h"
#include "AINpcSleep.h"
#include "AIHunger.h"
#include "AIEatFood.h"
#include "AIEatThenMutate.h"
#include "AIMutateFly.h"
#include "AIPatrolOnBlock.h"
#include "AIMutateTarget.h"
#include "AIPlant.h"
#include "AITargetScream.h"
#include "AIPanicBuff.h"
#include "AIPetPlayToPlayer.h"
#include "Text3D/Text3D.h"
#include "AIGhostBombAttack.h"
#include "AIGhostIceAttack.h"
#include "AIGhostBumpAttack.h"
#include "AIPetFollowOwner.h"
#include "PlayerControl.h"
#include "ObserverEventManager.h"
#include "BehaviorTreeManager.h"
#include "MobEventListen.h"
#include "ActorMoonMount.h"

#include "ActorDouDuMount.h"
#include "ActorPumpkinHorse.h"

#include "AIThief.h"
#include "AIBananaFan.h"
#include "AIClimbTree.h"
#include "AIEvade.h"
#include "AILeopardAtk.h"
#include "ActorSavagePriest.h"
#include "AIBegEx.h"
#include "AIRanchWander.h"
#include "AIPosWander.h"
#include "AIFlyAttract.h"
#include "AIHungryStatus.h"
#include "AIHungryAtkTarget.h"
#include "AIHungryFollowPlayer.h"
#include "AiVacant.h"
#include "AILavaCrab.h"
#include "AIEarthCoreManLash.h"
#include "AIEarthCoreManRain.h"
#include "AIEarthCoreManSteal.h"
#include "AIFlyPanic.h"
#include "AIChangeBlock.h"
#include "AIFlyStayFlower.h"
#include "AISavageStandSleep.h"
#include "AIOriole.h"
#include "AIFlyFollow.h"
#include "AIStayBlock.h"
#include "AIAtkTiangou.h"
//#include "TerrgenStatistic.h"
#include <ctime>
#include "RuneDef.h"


#include "ClientTrixenieMob.h"
#include "Minitolua.h"
#include "OgreScriptLuaVM.h"


#include "AttackingTargetComponent.h"
#include "HPProgressComponent.h"
#include "TeamComponent.h"
#include "GrowComponent.h"
#include "BreedComponent.h"
#include "QuiteComponent.h"
#include "AngryComponent.h"
#include "TameComponent.h"
#include "PlotComponent.h"
//#include "GameMod.h"

#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"

#include "MobAttrib.h"
#include "PlayerAttrib.h"

#include "ClientActorFuncWrapper.h"
#include "RiddenComponent.h"
#include "ActionAttrStateComponent.h"

#define _UPDATE_BOUND_BY_SCALE_

using namespace MNSandbox;

ACTOR_MOVEMODE_T ClientMob::getMoveMode()
{
	if (m_Def->ID == 3414 || m_Def->ID == 3415 || m_Def->ID == 3244 || m_Def->ID == 3245) return ACTORMOVE_JUMP;
	else return ACTORMOVE_NORMAL;
}

int ClientMob::getMass()
{
	if (m_Def) {
		return m_Def->Mass;
	}
	else
		return 0;
}

bool ClientMob::getIsPet()
{
	if (OBJ_TYPE_ACTOR_PET == getObjType())
	{
		return true;
	}
	return false;
}

int ClientMob::getObjType() const
{
	return m_ObjType;
}

int ClientMob::GetJumpHighest()
{
	if (m_Def)
	{	//猴子、豹子能跳很高（现在写死id,后续看策划是否要在表格里加字段表示跳的高度）
		if (m_Def->ID == 3872 || m_Def->ID == 3871 || m_Def->ID == 3870)
			return 2;
	}

	return 1;
}

int ClientMob::GetJumpLongest()
{
	if (m_Def)
	{	//猴子、豹子能跳很远（现在写死id,后续看策划是否要在表格里加字段表示跳的距离）
		if (m_Def->ID == 3872 || m_Def->ID == 3871 || m_Def->ID == 3870)
			return 4;
	}

	return 1;
}

float ClientMob::getBlockPathWeight(World *pworld, const WCoord &pos)
{
	// 916冒险，灯谜鸟不是飞行类型 codeby:wudeshen 2021/09/02
	if (m_Def->ID == 3897)
	{
		int blockID = pworld->getBlockID(pos);
		if (blockID == 0)
		{
			return 5.0f;
		}
		else
		{
			return  -199999.0;
		}
	}
	else if (m_Def->Type == MOB_PASSIVE)
	{
		if (pworld->getBlockID(DownCoord(pos)) == BLOCK_GRASS) return 10.0f;
		else return pworld->getBlockBright(pos) - 0.5f;
	}
	else if (m_Def->Type == MOB_HOSTILE)
	{
		return 0.5f - pworld->getBlockBright(pos);
	}
	if (m_Def->Type == MOB_WATER)
	{
		if (IsWaterBlockID(pworld->getBlockID(pos)))
		{
			return 5.0f;
		}
		else
		{
			return  -199999.0;
		}
	}
	else if (m_Def->Type == MOB_FLY)
	{
		int blockID = pworld->getBlockID(pos);
		if (blockID == 0)
		{
			return 5.0f;
		}
		else
		{
			return  -199999.0;
		}
	}
	else return 0;
}

float ClientMob::getBlockPathWeight(const WCoord &pos)
{
	return getBlockPathWeight(m_pWorld, pos);
}

float ClientMob::getRunWalkFactor()
{
	// 20210926：眩晕buff时，不可移动  codeby： keguanqiang
	/*if (m_Def->Speed == 0 || (getLivingAttrib() && (getLivingAttrib()->hasBuff(SWIMMY_BUFF) || getLivingAttrib()->hasBuff(BUBBLE_SLEEP_BUFF))))
		return 0;*/
	auto funcWrapper = getFuncWrapper();
	float speed = funcWrapper ? funcWrapper->getAIMoveSpeed() : 200.0f;
	LivingAttrib* attrib = getLivingAttrib();
	if (m_Def->Speed == 0)
	{
		return 0;
	}
	if (attrib)
	{
		if (attrib->hasBuff(SWIMMY_BUFF) ||
			attrib->hasBuff(BUBBLE_SLEEP_BUFF) ||
			attrib->hasStatusEffect(STATUS_EFFECT_PERCIPIENCE) ||
			attrib->hasStatusEffect(STATUS_EFFECT_DROP))
		{
			return 0;
		}
	}

	return speed / MobAttrib::defSpeed2MoveSpeed(m_Def->Speed);
}

void ClientMob::setRiddenByActor(ClientActor *p, int i)
{
	auto RidComp = sureRiddenComponent();
	if (RidComp)
	{
		RidComp->setRiddenByActor_Base(p, i);
	}
	

	if (needUpdateAI())
	{
		if (getNavigator() == NULL)
		{
			CreateComponent<NavigationPath>("NavigationPath");
		}
	}
	else
	{
		if (m_isUseAILua)
		{
			GetCoreLuaDirector().CallFunctionM("AILuaManager", "clearAllRunningTasks", "i", m_AILuaKey);
		}
		else
		{
			if (m_AITask) m_AITask->clearAllRunningTasks();
			if (m_AITaskTarget) m_AITaskTarget->clearAllRunningTasks();
		}


		getLocoMotion()->gotoPosition(getLocoMotion()->getPosition());
		DestroyComponent(GetComponentByName("NavigationPath"));
		getBody()->resetPos();
	}
	setHPProgressDirty();
}

float ClientMob::getVerticalFaceSpeed()
{
	if (m_Def->ID == 3117 || m_Def->ID == 3118 || m_Def->ID == 3119)
	{
		return 0.0f;
	}
	else if ((m_Def->ID == 3408 || m_Def->ID == 3506) && getSitting())
	{
		return 20.0f;
	}
	else
	{
		return ActorLiving::getVerticalFaceSpeed();
	}
}

//HPProgressComponent* ClientMob::getHPProgressComponent()
//{
//	return GetComponent<MobHPProgressComponent>();
//}
void ClientMob::setScale(float scale)
{
	ActorLiving::setScale(scale);

#ifdef _UPDATE_BOUND_BY_SCALE_
	// 修改碰撞箱和攻击判定箱
	updateBound();
#endif
}

int ClientMob::getEquipItem(int slot)
{
	MobAttrib *attrib = static_cast<MobAttrib *>(getAttrib());
	return attrib->getEquipItem((EQUIP_SLOT_TYPE)slot);
}

void ClientMob::addInitEquip(int slot, int itemid, int enchantid, int userDataInt/* = 1*/)
{
	MobAttrib *attrib = static_cast<MobAttrib *>(getAttrib());

	attrib->equip((EQUIP_SLOT_TYPE)slot, itemid, -1, -1, -1);
	BackPackGrid *grid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)slot);
	grid->setUserDataInt(userDataInt); //标识掉落有一定概率
	if (enchantid >= 0 && grid->getRuneData().getRuneNum() == 0)// 有符文的装备不能加附魔
	{
		grid->setEnchants(1, &enchantid);
		grid->changeDurationOnRuneOrEnchantChange(0);
	}
}

void ClientMob::addInitEquip_byRune(int slot, int itemid, const GridRuneItemData& runeItem, int userDataInt)
{
	MobAttrib *attrib = static_cast<MobAttrib *>(getAttrib());

	attrib->equip((EQUIP_SLOT_TYPE)slot, itemid, -1, -1, -1);
	BackPackGrid *grid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)slot);
	grid->setUserDataInt(userDataInt); //标识掉落有一定概率

	if (runeItem.rune_id >= 0 && grid->getNumEnchant() == 0)//有附魔的装备不能加符文
	{
		grid->getRuneData().addOneGridRuneItem(runeItem);
		grid->changeDurationOnRuneOrEnchantChange(0);
	}
}

float ClientMob::getOxygenUseRate()
{
	if (m_NeedOxygen) return 1.0f;
	else return 0;
}

bool ClientMob::isInvulnerable(ClientActor *attacker)
{
	/*if (getAiInvulnerableProb() > 0)
	{
		if (hasAttChanged(2)) {
			if (getAiInvulnerableProb() == 100) {
				return true;
			}
			else {
				return (std::rand() % 100 <= getAiInvulnerableProb());
			}
		}
		else if (GenRandomInt(getAiInvulnerableProb()) == 0)
		{
			return true;
		}
	}*/
	return getInvulnerable();
}

bool ClientMob::checkIfSavageSleeping()
{
	if (m_Def && (m_Def->ID == 3101 || m_Def->ID == 3102 || m_Def->ID == 3105)) {
		if (getFlagBit(ACTORFLAG_AI_SLEEP)) {
			return true;
		}
	}

	return false;
}

bool ClientMob::checkIfActorCanAttack()
{
	auto ActionAttrStateComp = getActionAttrStateComponent();
	if (!(ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_ATTACK))) {
		return false;
	}
	LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getAttrib());
	if (pLivingAttrib && pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
	{
		return false;
	}
	return true;
}
void ClientMob::onBuffAppend(int buffid, int bufflvl)
{
	if (!m_pWorld)
		return;

	//LOG_INFO("BUFF for callback.[mob]: append! buffid=%d, bufflvl=%d", buffid, bufflvl);
	ObserverEvent_ActorBuff obevent(this->getObjId(), buffid, bufflvl);
	obevent.SetData_Actor(this->getDefID());
	GetObserverEventManager().OnTriggerEvent("Actor.AddBuff", &obevent);
}

void ClientMob::onBuffRemove(int buffid, int bufflvl)
{
	if (!m_pWorld)
		return;

	//LOG_INFO("BUFF for callback.[mob]: remove! buffid=%d, bufflvl=%d", buffid, bufflvl);
	ObserverEvent_ActorBuff obevent(this->getObjId(), buffid, bufflvl);
	obevent.SetData_Actor(this->getDefID());
	GetObserverEventManager().OnTriggerEvent("Actor.RemoveBuff", &obevent);

	// 韧性回满
	if (buffid == TOUGHNESSBREAK_MONSTER_BUFF)
	{
		getMobAttrib()->recoverAllToughness();
	}
}

void ClientMob::setHomeLandServerId(std::string serverid)
{
	m_ServerWid = serverid;
}

bool ClientMob::getIsHomeLandPet()
{
	if (m_Def)
	{
		return m_Def->ID >= 10001 && m_Def->ID <= 15000;
	}
	return false;
}

int/*BTInstanceID*/ ClientMob::GetBlackboardID() const
{
	return m_bb ? m_bb->GetInstanceID() : 0;
}


int ClientMob::getInteractNum()
{
	//return (int)m_InteractFuncs.size();
	//兼容旧lua调用函数，先不删除这两个函数
	return this->EXEC_USEMODULE(getInteractNum);
}

const InteractFuncDef *ClientMob::getInteractDef(int index)
{
	//assert(index >= 0 && index < getInteractNum());

	//return &m_InteractFuncs[index];
	return this->EXEC_USEMODULE(getInteractDef, index);
}

const MonsterDef *ClientMob::getMonsterDef()const
{
	return m_Def;
}

#pragma region Logo todo:delete
//--BreedComponent
bool ClientMob::isInLove()
{	
	auto breedComp = getBreedComponent();//by__Logo
	if (breedComp)
		return breedComp->isInLove();
	else
		return false;
}

bool ClientMob::isInHate()
{	
	auto breedComp = getBreedComponent();//by__Logo
	if (breedComp)
		return breedComp->isInHate();
	else
		return false;
}

void ClientMob::setInLove(int inLove)
{	
	auto breedComp = getBreedComponent();//by__Logo
	if (breedComp) breedComp->setInLove(inLove);
}

int ClientMob::getInLove()
{		
	auto breedComp = getBreedComponent();//by__Logo
	if (breedComp)
		return breedComp->getInLove();
	else
		return 0;
}

//return breed_item_type->BREED_ITEM_T
int ClientMob::isBreedItem(int itemID)
{	
	auto breedComp = getBreedComponent();//by__Logo
	if (breedComp)
		return breedComp->isBreedItem(itemID);
	else
		return 0;
}
#pragma endregion

#pragma region Logo todo:delete
//--QuiteComponent
bool ClientMob::isInQuite()
{
	auto quiteComp = getQuiteComponent();
	if (quiteComp) return quiteComp->isInQuite();
	else return false;
}

void ClientMob::setQuiteTick(int qTick)
{	
	auto quiteComp = getQuiteComponent();
	if (quiteComp) quiteComp->setQuiteTick(qTick);
}

void ClientMob::setReadyToQuit(int num)
{	
	auto quiteComp = getQuiteComponent();
	if (quiteComp) quiteComp->setReadyToQuit(num);
}

void ClientMob::setRealQuit(int num)
{	
	auto quiteComp = getQuiteComponent();
	if (quiteComp) quiteComp->setRealQuit(num);
}

int ClientMob::getInQuite()
{
	auto quiteComp = getQuiteComponent();
	if (quiteComp) return quiteComp->getInQuite();
	else return 0;
}

int ClientMob::getReadyToQuit()
{	
	auto quiteComp = getQuiteComponent();
	if (quiteComp) return quiteComp->getReadyToQuit();
	else return 0;
}

int ClientMob::getRealQuit()
{	
	auto quiteComp = getQuiteComponent();
	if (quiteComp) return quiteComp->getRealQuit();
	else return 0;
}

//-- AngryComponent
//AngryComponent* ClientMob::getAngryComponent()
//{
//	if (m_pAngryComponent)
//		return m_pAngryComponent;
//	m_pAngryComponent = new AngryComponent(this);
//	return m_pAngryComponent;
//}

void ClientMob::setAngry(bool angry)
{	
	auto angryComp = GetComponent<AngryComponent>();//by__Logo
	if (angryComp)
		angryComp->setAngry(angry);
}

bool ClientMob::getAngry()
{	
	auto angryComp = GetComponent<AngryComponent>();//by__Logo
	if (angryComp)
		return angryComp->getAngry();
	else
		return false;
}

void ClientMob::setAIAngry(bool angry)
{
	auto angryComp = GetComponent<AngryComponent>();//by__Logo
	if (angryComp)
		angryComp->setAIAngry(angry);
}

bool ClientMob::getAIAngry()
{
	auto angryComp = GetComponent<AngryComponent>();//by__Logo
	if (angryComp)
		return angryComp->getAIAngry();
	else
		return false;
}

#pragma endregion

#pragma region Logo todo:delete
//-- getTameComponent
int ClientMob::getTamedOwnerID()
{	
	auto tameComp = getTameComponent();
	if (tameComp) return tameComp->getTamedOwnerID();
	else return 0;
}

void ClientMob::setTamedOwnerID(int uin)
{	
	auto tameComp = sureTameComponent();
	if (tameComp) tameComp->setTamedOwnerID(uin);
}

bool ClientMob::getTamed()
{
	auto tameComp = getTameComponent();
	if (tameComp) return tameComp->getTamed();
	else return false;
}

bool ClientMob::getPlayClearFx()
{	
	auto tameComp = getTameComponent();
	if (tameComp) return tameComp->getPlayClearFx();
	else return true;
}

void ClientMob::SetPlayClearFx(bool clearFx)
{
	auto tameComp = sureTameComponent();
	if (tameComp) {
		tameComp->setPlayClearFx(clearFx);
	}
}

void ClientMob::mobTamed(int owneruin, int tamed_target)  //被驯养
{	
	auto tameComp = sureTameComponent();
	if (tameComp) tameComp->mobTamed(owneruin, tamed_target);
}

void ClientMob::mobToWild(int targetId)  //驯服转野生
{	
	auto tameComp = sureTameComponent();
	if (tameComp) tameComp->mobToWild(targetId);
}

void ClientMob::setTamedOwnerUin(int iUin)
{	
	auto tameComp = sureTameComponent();
	if (tameComp) tameComp->setTamedOwnerUin(iUin);
}

ClientPlayer *ClientMob::getTamedOwner()
{
	auto tameComp = getTameComponent();
	if (tameComp) return tameComp->getTamedOwner();
	else return NULL;
}

int ClientMob::getSpawnSkillID()
{
	auto tameComp = getTameComponent();
	if (tameComp) return tameComp->GetSpawnSkillID();
	else return 0;
}

bool ClientMob::pushToTamedOwnerTamedList()
{	
	auto tameComp = sureTameComponent();
	if (tameComp) return tameComp->pushToTamedOwnerTamedList();
	else return false;
}

//--GrowComponent
bool ClientMob::isAdult()
{	
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->isAdult();
	else return true;
}

int ClientMob::getGrowingAge()
{	
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->getGrowingAge();
	else return 0;
}


void ClientMob::setGrowingAge(int growingAge)
{	
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) growComp->setGrowingAge(growingAge);
}

int ClientMob::getGrowingTime()
{
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->getGrowingTime();
	else return 0;
}

void ClientMob::setGrowingTime(int growingTime)
{
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) growComp->setGrowingTime(growingTime);
}

int ClientMob::getGrowingDValue()
{
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->getGrowingDValue();
	else return 0;
}

void ClientMob::setGrowingDValue(int dValue)
{
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) growComp->setGrowingDValue(dValue);
}

bool ClientMob::isEaten()
{
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->isEaten();
	else return false;
}

void ClientMob::setEaten(bool eaten)
{	
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) growComp->setEaten(eaten);
}

void ClientMob::setCurNeedFeedNum(int num)
{	
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) growComp->setCurNeedFeedNum(num);
}

int ClientMob::getCurNeedFeedNum()
{	
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->getCurNeedFeedNum();
	else return 0;
}

bool ClientMob::isNeedFeed()
{	
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->isNeedFeed();
	else return false;
}

bool ClientMob::hadFeedFood()
{	
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->hadFeedFood();
	else return false;
}

bool ClientMob::canFeed()
{
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->canFeed();
	else return true;
}
int ClientMob::getMobGrowDValue(int mobID)
{
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->getMobGrowTimeDValue(mobID);
	else return 0;
}
void ClientMob::setLoveCD(int cd)
{
	auto growComp = getBreedComponent();//by__Logo
	if (growComp) growComp->setLoveCD(cd);
}
int ClientMob::getLoveCD(int mobID)
{
	auto growComp = getBreedComponent();//by__Logo
	if (growComp) return growComp->getMobLoveCD(mobID);
	else return 0;
}
int ClientMob::getMobGrowTime(int mobID)
{	
	auto growComp = getGrowComponent();//by__Logo
	if (growComp) return growComp->getMobGrowTime(mobID);
	else return 0;
}
#pragma endregion
