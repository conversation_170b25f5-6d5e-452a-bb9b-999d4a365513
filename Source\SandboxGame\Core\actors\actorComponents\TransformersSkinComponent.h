#ifndef __TRANSFORMERS_SKIN_COMPONENT_H__
#define __TRANSFORMERS_SKIN_COMPONENT_H__

#include <vector>
#include "OgreWCoord.h"

#include "ActorComponent_Base.h"

struct RoleSkinDef;


class TransformersSkinComponent : public ActorComponentBase
{ 
public:
	DECLARE_COMPONENTCLASS(TransformersSkinComponent)

	TransformersSkinComponent();
	~TransformersSkinComponent();

	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnUpdate(float elapse);
	void onPlayerRevive();

	void eraseSkinSubPlayer(long long objid);
	void restoreSkinByReason(int transformReason);
	bool onInteractByActorSkinNpc(long long mainPlayer, int changeModel);

	void onPlayerTransformSkinModel(int reason, long long mainplayerid, long long objid);

	virtual void resetActorBody();
	virtual void restoreSkin();
	void resetDeformation(int reason = 1);	//变形后还原模型
	void DeformationSkin(RoleSkinDef *skinDef);
	bool InTransform() { return m_bInTransform; }
	bool trySplitDisguise(short id); //准备分裂装扮	
	void InfoTips(int id, int num = 0);
protected:
	void findPlaceSplitDisguise(int &splitResult, bool ignoreOccupied  = false );
	friend class ClientPlayer;

	short m_TransformReason;
	bool m_bInTransform;
	//特殊皮肤用  变形金刚,变身效果分裂出装扮 可以提供给其他玩家穿
	long long m_MainSkinPlayerID;   //副装扮玩家--皮肤拥有者的objid
	std::vector<long long> m_vecSkinSubActors;        //副装扮npc---分裂出的装扮actor
	std::vector<long long> m_vecSkinSubPlayers;      //子装扮玩家
	std::vector<WCoord> m_SplitPos; //子装扮生成位置
	int m_iCheckDistanceTick;   //检测自己与生成子装扮的距离tick触发计数（距离过远将收回装扮），10个tick触发一次--计时 定时触发功能	
}; 

class MPTransformersSkinComponent : public TransformersSkinComponent
{
	DECLARE_COMPONENTCLASS(MPTransformersSkinComponent)
public:
	MPTransformersSkinComponent();

	virtual void resetActorBody() override;
	virtual void restoreSkin() override;
};

#endif