#include "AIUpdateFrequency.h"

#include "ClientActor.h"
#include "world.h"
#include "WorldManager.h"
#include <assert.h>
IMPLEMENT_COMPONENTCLASS(AIUpdateFrequency)
AIUpdateFrequency::AIUpdateFrequency():m_VisibleFrame(0),m_UpdateFrame(0), m_needUpdate(true)
{

}

bool AIUpdateFrequency::checkNeedUpdate(float dtime, float &retdt)
{
	m_needUpdate = false;
	flushVisibleFrame();
	if (GetWorldManagerPtr() == NULL) return m_needUpdate;
	WCoord& center = GetWorldManagerPtr()->m_RenderEyePos;
	m_owner = GetOwnerActor()->ToCast<ClientActor>();
	WCoord dp = m_owner->getPosition() - center;
	SInt64 lsq = dp.lengthSquared();
	if (lsq > 6400 * 6400)
	{
		if (m_UpdateFrame + 10 > m_VisibleFrame) return false;
	}else if (lsq > 3200 * 3200)
	{
		if (m_UpdateFrame + 2 > m_VisibleFrame) return false;
	}
	
	return true;
}

void AIUpdateFrequency::finishUpdate()
{
	m_UpdateFrame = m_VisibleFrame;
}

void AIUpdateFrequency::onEnterWorld(World *pworld)
{
	flushVisibleFrame();
	m_UpdateFrame  = m_VisibleFrame - 1;
}

void AIUpdateFrequency::flushVisibleFrame()
{
	m_VisibleFrame = ClientActor::m_CurActorFrame;
}