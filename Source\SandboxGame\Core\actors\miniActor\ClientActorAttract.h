#pragma once

#include "BlockMesh.h"
#include "ClientActorProjectile.h"

class EXPORT_SANDBOXGAME ClientActorAttract;
class ClientActorAttract :public ClientActorProjectile //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ClientActorAttract();
	flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	bool load(const void *srcdata, int version) override;
	void onImpactWithActor(ClientActor *actor, const std::string& partname);
	void onImpactWithBlock(const WCoord *blockpos, int face);
	virtual void onCollideWithPlayer(ClientActor *player) override;
	void tick();
	void update(float dtime);
	void init(int itemid, ClientActor *shooter = nullptr);
	void PreRender();
	//virtual void onSectionCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum) override{};
	ClientPlayer *getShootingPlayer();
	void onAttackActor(ClientA<PERSON> *actor);
	bool passActor() { return true;}
	void setState(int state) { m_nState = state;}
	void setBlockID (unsigned int blockID);
	void getViewBox(CollideAABB &box);
	virtual void enterWorld(World *pworld) override;
	virtual void leaveWorld(bool keep_inchunk) override;
	virtual int getObjType()const override
	{
		return OBJ_TYPE_HOOK;
	}
	virtual bool supportSaveToPB()
	{
		return false;
	}
	//tolua_end
protected:
	void callBack();
	void broadAttribChanges();
	~ClientActorAttract();
private:
	int m_nState;
	//WCoord m_blockpos;
	int m_nHandItem;
	int m_nLifeTick;

	int m_nBlockID;
	WORLD_ID m_nActorObj;
	int blockData;
	BlockMesh *m_BlockModel;
}; //tolua_exports

