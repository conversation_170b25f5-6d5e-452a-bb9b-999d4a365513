#include "container_worldselectmobspawner.h"
#include "ClientActorDef.h"
#include "EffectModel.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "EffectParticle.h"
#include "CustomModelMgr.h"
#include "FullyCustomModelMgr.h"
#include "BlockMaterialMgr.h"
#include "ActorBody.h"
#include "PlayerControl.h"

/**********************************************************************************************
类    名：WorldSelectMobSpawner
功    能："刷怪方块"容器
********************************************************************************************* */
WorldSelectMobSpawner::WorldSelectMobSpawner() : WorldContainer(WCoord(0, 0, 0), BRUSHMONSTER_START_INDEX),
m_GenerationSwitch(false), m_IsNumberDetection(false),
m_IsSpawnDelay(false), m_SpawnWide(10), m_SpawnHigh(10),
m_IsFirstSpawn(false), m_iCheckCustomModelBindTick(0),
m_bUsePackingFCM(false)
{
	m_NeedTick = true;
} 

WorldSelectMobSpawner::WorldSelectMobSpawner(const WCoord& blockpos) :
	WorldContainer(blockpos, BRUSHMONSTER_START_INDEX), m_GenerationSwitch(false), m_IsNumberDetection(false),
	m_IsSpawnDelay(false), m_SpawnWide(10), m_SpawnHigh(10), m_IsFirstSpawn(false), m_iCheckCustomModelBindTick(0),
	m_bUsePackingFCM(false)
{
	m_NeedTick = true;
}

WorldSelectMobSpawner::~WorldSelectMobSpawner()
{
}

int WorldSelectMobSpawner::getObjType() const
{
	return OBJ_TYPE_MOBSPAWNER;
}

void WorldSelectMobSpawner::init(int resid)
{
	m_SpawnDelay = -1;
	m_OriginSpawnDelay = 1;
	m_MinSpawnDelay = 20;
	m_MaxSpawnDelay = 20;
	m_SpawnCount = 1;
	m_MobResID = 4000;
	m_ForceSpawn = 1;
	m_MaxNearbyMobs = 10;
	m_MaxDuration = 2;
	if (m_BlockPos.y >= 1 && m_BlockPos.y <= 256)
	{
		m_SpawnHigh = m_BlockPos.y;
	}
	else
	{
		m_SpawnHigh = 10;
	}

}

bool WorldSelectMobSpawner::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerSelectMobSpawner*>(srcdata);
	loadContainerCommon(src->basedata());

	m_OriginSpawnDelay = m_SpawnDelay = src->spawndelay();
	m_MinSpawnDelay = src->mindelay();
	m_MaxSpawnDelay = src->maxdelay();
	m_SpawnCount = src->spawncount();
	m_ForceSpawn = src->forcespawn() > 0;
	m_MobResID = src->mobresid();
	m_MaxNearbyMobs = src->maxnearby();
	m_Duration = src->duration();
	m_MaxDuration = src->maxduration();
	m_GenerationSwitch = src->generationswitch();
	m_IsNumberDetection = src->numberdetection();
	m_IsSpawnDelay = src->isspawndelay();
	m_SpawnWide = src->spawnwide();
	m_SpawnHigh = src->Spawnhigh();
	m_IsFirstSpawn = src->firstspawn();
	return true;
}

void WorldSelectMobSpawner::updateTick()
{

	//没通电时模型停止旋转
	if (m_MobModel && !m_GenerationSwitch)
	{
		m_MobModel->setRotateSpeed(0.0f);
	}

	if (m_GenerationSwitch)
	{
		//关闭生成间隔开关且刷出了第一次怪
		if (m_MobModel && !m_IsSpawnDelay && m_IsFirstSpawn)
		{
			float s = Rainbow::Clamp(float(m_SpawnDelay) / m_OriginSpawnDelay, 0.0f, 1.0f);
			m_MobModel->setRotateSpeed(240.0f);
			return;
		}
		bool isMobCreated = updateSpawn(m_IsNumberDetection, m_SpawnWide, m_SpawnHigh);

		if (m_World->isRemoteMode()) return;

		int blockdata = m_World->getBlockData(m_BlockPos);
		int dir = blockdata & 3;
		int ontrigger = 4;
		//刷怪成功
		if (isMobCreated)
		{
			m_IsFirstSpawn = true;
			//输出信号
			m_World->setBlockData(m_BlockPos, dir | ontrigger);
		}

	}

}

void WorldSelectMobSpawner::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();

	//onEnterWorld(pworld);
	if (pworld->onClient())
	{
		m_bUsePackingFCM = false;
		WCoord pos = BlockBottomCenter(getSpawnCenter());
		const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(m_MobResID);
		if (def == NULL)
		{
			return;
		}
		m_FX = ENG_NEW(EffectParticle)(pworld, "particles/item_810.ent", pos, 0);
		char path[256] = { 0 };
		Rainbow::Model* model = NULL;
		Rainbow::Entity* entity = NULL;
		if (def->ModelType == MONSTER_CUSTOM_MODEL && CustomModelMgr::GetInstancePtr())
		{
			entity = CustomModelMgr::GetInstancePtr()->getActorEntity(def->Model.c_str());
			if (entity)
			{
				entity->SetScale(1.0f);
			}
		}
		else if (def->ModelType == MONSTER_FULLY_CUSTOM_MODEL && FullyCustomModelMgr::GetInstancePtr())
		{
			m_bUsePackingFCM = def->Icon == "fullycustompacking";
			entity = FullyCustomModelMgr::GetInstancePtr()->getEntity(def->Model.c_str(), true, m_bUsePackingFCM);
		}
		else
		{
			const char* pModeName = def->Model.c_str();
			if (pModeName && pModeName[0] != 0)
			{
				//玩家自定义模型
				if (pModeName[0] == '$')
				{
					const char* _dir = "";
					def->gamemod->Event2().EmitThreadSafe<const char*&>("Mod_getModDir", _dir);
					snprintf(path, sizeof(path), "%s/resource/model/models/entity/%s.omod", _dir, pModeName + 1);
				}
				//玩家自定义选择现有模型
				else if (pModeName[0] == '@')
				{
					snprintf(path, sizeof(path), "entity/%s/body.omod", pModeName + 1);
				}
				//角色模型
				else if (pModeName[0] == 'p')
				{
					snprintf(path, sizeof(path), "entity/player/player0%s/body.omod", pModeName + 1);
					const char* animpath = NULL/*"entity/player/body.oanim"*/;
					model = g_BlockMtlMgr.getModel(path, animpath);
				}
				//表格定义的模型
				else
				{
					snprintf(path, sizeof(path), "entity/%s/body.omod", pModeName);
				}
			}
		}

		if (model)
		{
			m_MobModel = ENG_NEW(EffectModel)(pworld, model, pos + WCoord(0, 120, 0), 0);
		}
		else if (entity)
		{
			m_MobModel = ENG_NEW(EffectModel)(pworld, entity, pos + WCoord(0, 120, 0), 0);
		}
		else
		{
			m_MobModel = ENG_NEW(EffectModel)(pworld, path, pos + WCoord(0, 120, 0), 0);
		}
		m_MobModel->setScale(0.4f);
		m_MobModel->playAnim(100101);
		ActorBody::clearEquipItems(m_MobModel->getModel());

		pworld->getEffectMgr()->addEffect(m_FX);
		pworld->getEffectMgr()->addEffect(m_MobModel);
	}
}

void WorldSelectMobSpawner::leaveWorld()
{
	onLeaveWorld();

	WorldContainer::leaveWorld();
}



void WorldSelectMobSpawner::onAttachUI()
{
	m_AttachToUI = true;

}

void WorldSelectMobSpawner::onDetachUI()
{
	m_AttachToUI = false;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldSelectMobSpawner::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerSelectMobSpawner(builder, basedata, m_SpawnDelay, m_MinSpawnDelay,
		m_MaxSpawnDelay, m_SpawnCount, m_ForceSpawn ? 1 : 0, m_MobResID, m_MaxNearbyMobs, m_Duration, m_MaxDuration,
		m_GenerationSwitch, m_IsNumberDetection, m_IsSpawnDelay, m_SpawnWide, m_SpawnHigh, m_IsFirstSpawn);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerSelectMobSpawner, actor.Union());
}


void WorldSelectMobSpawner::setBrushMonsterAttr(int MobResID, int everyNum, int maxNum, int spawnWide, int spawnHigh, int spawnDelay, bool numSwitch, bool DelaySwitch)
{
	LOG_INFO("setBrushMonsterAttr:");

	m_SpawnDelay = -1;
	m_OriginSpawnDelay = 1;

	m_SpawnCount = everyNum;               //一次刷怪数量
	m_MobResID = MobResID;			//怪物ID
	m_ForceSpawn = 1;
	m_MaxNearbyMobs = maxNum;			//范围内同种怪最大数量
	m_MaxDuration = 2;				//最大持续时间
	m_SpawnWide = spawnWide;
	m_SpawnHigh = spawnHigh;

	if (DelaySwitch)
	{
		m_MinSpawnDelay = spawnDelay * 20;   //刷怪时间/0.05s
		m_MaxSpawnDelay = spawnDelay * 20;
	}
	else
	{
		m_MinSpawnDelay = 20;   //刷怪时间/0.05s
		m_MaxSpawnDelay = 20;
	}

	m_IsNumberDetection = numSwitch;
	m_IsSpawnDelay = DelaySwitch;
	if (g_pPlayerCtrl)
	{
		g_pPlayerCtrl->getWorld()->markBlockForUpdate(m_BlockPos, true);
	}

	onLeaveWorld();

	enterWorld(getSpawnWorld());


}



BrushMonsterAttr WorldSelectMobSpawner::getBrushMonsterAttr()
{
	BrushMonsterAttr attr;

	attr.MobResID = m_MobResID;
	attr.everyNum = m_SpawnCount;
	attr.maxNum = m_MaxNearbyMobs;
	attr.spawnWide = m_SpawnWide;
	attr.spawnHigh = m_SpawnHigh;
	attr.spawnDelay = m_MinSpawnDelay;
	attr.numSwitch = m_IsNumberDetection;
	attr.DelaySwitch = m_IsSpawnDelay;

	return attr;
}

void WorldSelectMobSpawner::refreshMonster()
{

}

World* WorldSelectMobSpawner::getSpawnWorld()
{
	return m_World;
}

WCoord WorldSelectMobSpawner::getSpawnCenter()
{
	return m_BlockPos;
}

bool WorldSelectMobSpawner::checkDataValid()
{
	return true;
}
