﻿#include "ActorBody.h"

#include "BlockScene.h"
#include "WorldRender.h"
#include "ClientItem.h"
#include "SandBoxManager.h"

#include "ModelItemMesh.h"
#include "GameMode.h"
#include "OgreUtils.h"
#include "BlockMesh.h"
#include "special_blockid.h"
#include "PlayerControl.h"
#include "MpActorManager.h"
#include "GameCamera.h"
#include "CameraManager.h"

#include "RecordPkgManager.h"
#include "GameNetManager.h"
#include "CustomModelMgr.h"
#include "FullyCustomModelMgr.h"

#include "ActorVillager.h"
#include "ImportCustomModelMgr.h"
#include "Pkgs/PkgUtils.h"
#include "Entity/LegacySequenceMap.h"

#include "PlayerAttrib.h"
#include "Texture/LegacyTextureUtils.h"

#include "CarryComponent.h"
#include "RiddenComponent.h"
#include "ClientActorFuncWrapper.h"
#include "backpack.h"
#include "ActorGeniusMgr.h"
#include "ActorFishingVillager.h"
#include "BlockMaterialMgr.h"
#include "CustomModel.h"

#include "Optick/optick.h"
#include "Entity/ModelRenderer.h"
#include "Entity/ModelAnimationPlayer.h"
#include "PlayerLocoMotion.h"
#include "UGCEntity.h"
#include "Plugin.h"
#include "SandboxGameDef.h"
#include "UgcAssetMgr.h"

#if (defined(DEBUG) || defined(PROFILE_MODE)) && GIZMO_DRAW_ENGABLE
#include "Gizmo/DebugUtility.h"
#endif

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

namespace MNSandbox
{
	void alterAvatarPartColorAsyn(Rainbow::IActorBody* actorbody, int& modelId, int& partId, jsonxx::Object& aSkin, float& h, float& s, float& b, int& blockID)
	{
		if (actorbody == nullptr)
		{
			return;
		}

		if (aSkin.has<jsonxx::Object>("skin"))
		{
			const jsonxx::Object& childSkin = aSkin.get<jsonxx::Object>("skin");
			if (childSkin.has<jsonxx::Number>("ModelID") && childSkin.has<jsonxx::Number>("Part"))
			{
				if (childSkin.has<jsonxx::Object>("Data"))
				{
					const jsonxx::Object& childSkinData = childSkin.get<jsonxx::Object>("Data");
					if (childSkinData.has<jsonxx::Array>("DyeInfo")) {
						const jsonxx::Array& childSkinDataDyeInfo = childSkinData.get<jsonxx::Array>("DyeInfo");
						for (size_t j = 0; j < childSkinDataDyeInfo.size(); j++)
						{
							if (childSkinDataDyeInfo.has<jsonxx::Array>(j))
							{
								const jsonxx::Array& aDyeInfo = childSkinDataDyeInfo.get<jsonxx::Array>(j);
								if (aDyeInfo.size() == 4)
								{
									h = (float)aDyeInfo.get<jsonxx::Number>(1);
									s = (float)aDyeInfo.get<jsonxx::Number>(2);
									b = (float)aDyeInfo.get<jsonxx::Number>(3);
									blockID = (int)aDyeInfo.get<jsonxx::Number>(0);

									actorbody->alterAvatarPartColorAync(modelId, partId, h, s, b, blockID);
								}
							}
						}
					}
					else if (childSkinData.has<jsonxx::Object>("DyeInfo"))
					{
						const jsonxx::Object& childSkinDataDyeInfo = childSkinData.get<jsonxx::Object>("DyeInfo");
						const auto& temp = childSkinDataDyeInfo.kv_map();
						for (auto iter = temp.begin(); iter != temp.end(); iter++)
						{
							jsonxx::Value* value = iter->second;
							const jsonxx::Array& aDyeInfo = value->get<jsonxx::Array>();
							if (aDyeInfo.size() == 4)
							{
								//颜色
								h = (float)aDyeInfo.get<jsonxx::Number>(1);
								s = (float)aDyeInfo.get<jsonxx::Number>(2);
								b = (float)aDyeInfo.get<jsonxx::Number>(3);
								blockID = (int)aDyeInfo.get<jsonxx::Number>(0);

								actorbody->alterAvatarPartColorAync(modelId, partId, h, s, b, blockID);
							}
						}
					}
				}
			}
		}
	}
}

ActorBodyAvatarComponent* ActorBody::GetAvatarComponent()
{
	if (!m_AvatarComponent)
	{
		m_AvatarComponent = SANDBOX_NEW(ActorBodyAvatarComponent, this);
	}

	return m_AvatarComponent;
}

int ActorBody::getAvatarListModelId(int index) {
	return GetAvatarComponent()->getAvatarListModelId(index);
}

bool ActorBody::getAvatarListShowStatu(int index) {
	return GetAvatarComponent()->getAvatarListShowStatu(index);
}

bool ActorBody::GetAvatarPartModelShow(int type)
{
	return GetAvatarComponent()->GetAvatarPartModelShow(type);
}

bool ActorBody::hasCacheAvatar()
{
	if (m_AvatarComponent)
	{
		return GetAvatarComponent()->hasCacheAvatar();
	}
	return false;
}

void ActorBody::SetHideEquipAvatar(bool value)
{
	GetAvatarComponent()->SetHideEquipAvatar(value);
}


bool ActorBody::GetHideEquipAvatarState()
{
	return GetAvatarComponent()->GetHideEquipAvatarState();
}

bool ActorBody::canPlayerWearEquip()
{
	ActorGeniusMgr* geniusMgr = GET_SUB_SYSTEM(ActorGeniusMgr);
	return (isPlayer() && (getSkinID() == 0 || (geniusMgr && geniusMgr->isOldRoleSkin(getSkinID()))));
}


/*
	获取m_HelmetModel
*/
Rainbow::MovableObject* ActorBody::getHelmetModelModel() 
{ 
	if (m_EquipComponent)
	{
		return GetEquipComponent()->m_HelmetModel;
	}

	return NULL;
}

/*
	获取m_WeaponModel
*/
BaseItemMesh* ActorBody::getWeaponModel() 
{ 
	if (m_EquipComponent)
	{
		return GetEquipComponent()->m_WeaponModel;
	}

	return NULL;
}
/*
	获取m_WeaponModel_left
*/
BaseItemMesh* ActorBody::getLeftWeaponModel() 
{ 
	if (m_EquipComponent)
	{
		return GetEquipComponent()->m_WeaponModel_left;
	}

	return NULL;
}

Rainbow::Entity* ActorBody::getDorsumEntity() {
	if (m_EquipComponent)
	{
		return GetEquipComponent()->m_DorsumEntity;
	}

	return NULL;
}

void ActorBody::checkAvatarPartEffect(int avatarmodel, int index)
{
	GetAvatarComponent()->checkAvatarPartEffect(avatarmodel,index);
}

void ActorBody::addAvatarPartEffect(int avatarmodel, int index, int part)
{
	GetAvatarComponent()->addAvatarPartEffect(avatarmodel, index, part);
}

bool ActorBody::checkWholeBodyEffect()
{
	if (m_AvatarComponent)
	{
		return GetAvatarComponent()->checkWholeBodyEffect();
	}

	return false;
}

bool ActorBody::addAvatarPartModel(int avatarmodel, int index, bool isAsync, int pointId, bool isItem)
{
	return GetAvatarComponent()->addAvatarPartModel(avatarmodel, index, isAsync, pointId, isItem);
}

void ActorBody::EquipAavatarPlayer(EQUIP_SLOT_TYPE slot, int itemid)
{
	GetAvatarComponent()->EquipAavatarPlayer(slot, itemid);
}

void ActorBody::ClearEquipAvatar(EQUIP_SLOT_TYPE slot)
{
	GetAvatarComponent()->ClearEquipAvatar(slot);
}

void ActorBody::EquipAavatarPlayerByItemId(int itemid)
{
	GetAvatarComponent()->EquipAavatarPlayerByItemId(itemid);
}

void ActorBody::ClearAavatarPlayerByItemId(int itemid)
{
	GetAvatarComponent()->ClearAavatarPlayerByItemId(itemid);
}

bool ActorBody::alterAvatarPartColorAync(int modelID, int partID, float r, float g, float b, int block)
{
	return GetAvatarComponent()->alterAvatarPartColorAync(modelID,  partID,  r,  g,  b,  block);
}

bool ActorBody::apllyAvatarPartColor(int modelID, int partID)
{
	return GetAvatarComponent()->apllyAvatarPartColor(modelID, partID);
}

void ActorBody::resetAvatarPartShied(int index)
{
	GetAvatarComponent()->resetAvatarPartShied(index);
}

void ActorBody::addAvatarPartShied(int index, int shieldId)
{
	GetAvatarComponent()->addAvatarPartShied(index, shieldId);
}

bool ActorBody::clearAvatarPartShied(int index)
{
	return GetAvatarComponent()->clearAvatarPartShied(index);
}

void ActorBody::addDefaultAvatar(bool isAsync)
{
	// addAvatarPartModel(2, 1, isAsync);
	// addAvatarPartModel(3, 4, isAsync);
	// addAvatarPartModel(4, 6, isAsync);
}

void ActorBody::resetAvatarDefault()
{
	for (int j = AVATAR_PART_TYPE::HEAD; j <= AVATAR_PART_TYPE::SKIN; j++)
	{
		if (j == AVATAR_PART_TYPE::FACE)
		{
			exchangePartFace(0, j, false);
		}
		else
		{
			hideAvatarPartModel(j);
		}
	}

	addDefaultAvatar();
}

bool ActorBody::addAvatarPartModelByPath(const Rainbow::FixedString& path, int index, bool isAsync /* = false */)
{
	return GetAvatarComponent()->addAvatarPartModelByPath(path, index, isAsync);
}


void ActorBody::hideAvatarPartModel(int index, bool isItem )
{
	GetAvatarComponent()->hideAvatarPartModel(index,isItem);
}

void ActorBody::setAvatarPartModelShow(int index, bool bShow)
{
	GetAvatarComponent()->setAvatarPartModelShow(index,bShow);
}

int ActorBody::getEffectAvatarCount()
{
	int count = 0;
#ifndef IWORLD_SERVER_BUILD	
	for (int i = AVATAR_PART_TYPE::HEAD_EFFECT; i <= AVATAR_PART_TYPE::BG_EFFECT; i++)
	{
		if (GetAvatarPartModelShow(i))
		{
			count = count + 1;
		}
	}

	if (GetAvatarPartModelShow(AVATAR_PART_TYPE::FOOTPRINT))
	{
		count = count + 1;
	}
#endif
	return count;
}

void ActorBody::showEffectAvatar(bool show)
{
	GetAvatarComponent()->showEffectAvatar(show);
}

bool ActorBody::isEffectAvatar(int index)
{
	if (index >= AVATAR_PART_TYPE::HEAD_EFFECT && index <= AVATAR_PART_TYPE::BG_EFFECT)
	{
		return true;
	}
	return false;
}

bool ActorBody::alterAvatarPartColor(float r, float g, float b, int partID, int modelID, int block)
{
	ColourValue cololurValue(r, g, b);
	return alterAvatarPartColor(cololurValue, partID, modelID, block);
}

bool ActorBody::alterAvatarPartColor(ColourValue cololur, int partID, int modelID, int block)
{
	if (partID < 0 || partID > 10 || modelID < 0)
	{
		return false;
	}
	OPTICK_EVENT();
	if (GetRecordPkgManager().isRecordStarted() && m_OwnerActor)
	{
		OPTICK_EVENT("sendToClient");
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
		if (player && g_pPlayerCtrl)
		{
			PB_PlayerAvartarColorHC addAvartarColorHC;
			addAvartarColorHC.set_uin(player->getUin());
			addAvartarColorHC.set_r(cololur.r);
			addAvartarColorHC.set_g(cololur.g);
			addAvartarColorHC.set_b(cololur.b);
			addAvartarColorHC.set_partid((float)partID);
			addAvartarColorHC.set_modelid((float)modelID);
			addAvartarColorHC.set_block((float)block);
			GetGameNetManager().sendToClient(g_pPlayerCtrl->getUin(), PB_PLAYER_AVARTARCOLOR_HC, addAvartarColorHC);
		}
	}

	if (!m_Entity) return false;
	if (m_LoadModelData && m_LoadModelData->IsAsync() && m_Entity->GetMainModel() == nullptr)
	{
		SyncModelData();
	}

	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model)
	{
		int checkcode = 1;
		MINIW::ScriptVM::game()->callFunction("AvtCallTable", "ii>i", 1, modelID, &checkcode);
		if (checkcode != 0)
			return false;

		//char zippath[512] = { 0 };
		char basepath[512] = { 0 };
		char maskpath[512] = { 0 };

		if (modelID > 5)
		{
			snprintf(basepath, sizeof(basepath), "entity/avatar/1000_%d/%d.png_", modelID, modelID);
			snprintf(maskpath, sizeof(maskpath), "entity/avatar/1000_%d/mask_%d.png_", modelID, modelID);
		}
		else if (modelID == 0)
		{
			sprintf(basepath, "%s", m_ARTexPath.c_str());
		}
		else
		{
			sprintf(basepath, "entity/player/player12/%d.png_", modelID);
			sprintf(maskpath, "entity/player/player12/mask_%d.png_", modelID);
		}

		SharePtr<Texture2D> basetexture;
		if (modelID != 0)
		{
			if (!basetexture)
			{
				basetexture = PkgUtils::LoadTextureReadAble<Texture2D>(basepath);

				if (!basetexture)
				{
					MINIW::ScriptVM::game()->callFunction("AvtCallTable", "ii", 2, modelID);
					return false;
				}
			}
		}

		SharePtr<Texture2D> masktexture;
		if (modelID != 0)
		{
			if (!masktexture)
			{
				masktexture = PkgUtils::LoadTextureReadAble<Texture2D>(maskpath);

				if (!masktexture)
				{
					MINIW::ScriptVM::game()->callFunction("AvtCallTable", "ii", 2, modelID);
					return false;
				}
				//else
				//	m_mAvtMasktex.insert(make_pair(modelID, masktexture));
			}
		}

		Rainbow::Model::Avatar_Parts part;
		switch (partID)
		{
		case 0:
			part = Rainbow::Model::Avatar_Parts_Body;
			break;
		case 1:
			part = Rainbow::Model::Avatar_Parts_Head;
			break;
		case 2:
			part = Rainbow::Model::Avatar_Parts_Face;
			break;
		case 3:
			part = Rainbow::Model::Avatar_Parts_Face_Accessories;
			break;
		case 4:
			part = Rainbow::Model::Avatar_Parts_Jacket;
			break;
		case 5:
			part = Rainbow::Model::Avatar_Parts_Glove;
			break;
		case 6:
			part = Rainbow::Model::Avatar_Parts_Trousers;
			break;
		case 7:
			part = Rainbow::Model::Avatar_Parts_Shoe;
			break;
		case 8:
			part = Rainbow::Model::Avatar_Parts_Back_Accessories;
			break;
		case 9:
			part = Rainbow::Model::Avatar_Parts_VFX;
			break;
		case 10:
		{
			//预埋的位置 暂时没用到
			return false;
		}
		default:
		{
			return false;
		}
		}

		if (basetexture && masktexture)
			model->FillAvatarColor(part, basetexture, masktexture, block, cololur);

		return true;
	}
	return false;
}


bool ActorBody::alterBodyStyleInnel(float hue, float saturation, float illumination, float sharpness, bool isBlend)
{
#ifndef IWORLD_SERVER_BUILD		
	if (!m_Entity) return false;
	if (m_LoadModelData && m_LoadModelData->IsAsync() && m_Entity->GetMainModel() == nullptr)
	{
		SyncModelData();
	}

	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model)
	{
		if (!m_pNowBodyTex || !m_pSourceTex)
			return false;

		if (!m_pTransitionTex)
		{
			m_pTransitionTex = Rainbow::MakeSharePtr<Rainbow::Texture2D>(kMemTexture); //GetAssetManager().LoadAsset<Texture2D>("entity/player/player12/transition.png");
			m_pTransitionTex->InitTexture(m_pSourceTex->GetGPUWidth(), m_pSourceTex->GetGPUHeight(), kTexFormatRGBA32, Rainbow::TextureCreationFlags::kTextureInitNone);
		}

		if (hue >= -1 && hue <= 1 && saturation >= -1 && saturation <= 1 && illumination >= -1 && illumination <= 1)
			model->TextureHSV(m_pSourceTex, m_pTransitionTex, hue, saturation, illumination);

		if (sharpness >= 0 && sharpness <= 10)
			model->TextureSharpen(m_pTransitionTex, m_pNowBodyTex, sharpness, 1);

		if (isBlend)
		{
			model->SetTexture("g_DiffuseTex", m_pNowBodyTex, "1"/*, m_pOcclusionTex*/);
		}
		else
		{
			model->SetTexture("g_DiffuseTex", m_pNowBodyTex, "1");
		}
		return true;
	}
#endif	
	return false;
}

bool ActorBody::alterBodyStyle(float hue, float saturation, float illumination, float sharpness, bool isBlend)
{
	alterBodyStyleInnel(hue, saturation, illumination, sharpness, isBlend);
	return true;
}

void ActorBody::showAvatarPartModel(int index)
{
#ifndef IWORLD_SERVER_BUILD	
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model && !model->IsShowAvatar(index))
	{
		model->ShowAvatar(index, true);
	}
#endif	
}


bool ActorBody::exchangePartFace(int avatarmodel, int index, bool isShow, const char* texturePath /*=NULL*/, int skinModel /*=0*/)
{
	return GetAvatarComponent()->exchangePartFace(avatarmodel, index, isShow, texturePath, skinModel);
}

bool ActorBody::exchangePartFaceASync(const char* avatarInfo, std::function<void()> callback)
{
	return GetAvatarComponent()->exchangePartFaceASync(avatarInfo, callback);
}

bool ActorBody::exchangePartFaceByPath(const Rainbow::FixedString& avatarPath, const Rainbow::FixedString& skinPath /* = "" */)
{
	return GetAvatarComponent()->exchangePartFaceByPath(avatarPath, skinPath);
}

bool ActorBody::resetAcotorTexture(bool isBlend)
{
	if (!m_Entity) return false;
	if (m_LoadModelData && m_LoadModelData->IsAsync() && m_Entity->GetMainModel() == nullptr)
	{
		SyncModelData();
	}

	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model == NULL)
		return false;
	if (m_ARTexPath == "" || m_ARTexPath.length() <= 0)
		return false;

	if (!GetFileManager().IsFileExist(m_ARTexPath.c_str(), FileOpType::kFileOpAll))
		return false;

	SharePtr<Texture2D> tex = PkgUtils::LoadTextureReadAble<Texture2D>(m_ARTexPath.c_str(), kFileOpAll);//GetAssetManager().LoadAsset<Texture2D>(m_ARTexPath.c_str());
	if (!tex)
		return false;

	ColorRGBA32* srcColor = nullptr;
	ALLOC_TEMP_AUTO(srcColor, tex->GetGPUWidth() * tex->GetGPUHeight());
	tex->GetPixels32(0, srcColor, tex->GetGPUWidth() * tex->GetGPUHeight());

	TextureFormat format = kTexFormatRGBA32;
	int bytesPerPixel = GetBytesFromTextureFormat(format);
	ImageReference image(tex->GetGPUWidth(), tex->GetGPUHeight(), tex->GetGPUWidth() * bytesPerPixel, format, srcColor);

	m_pNowBodyTex = Rainbow::MakeSharePtr<Rainbow::Texture2D>(kMemTexture);
	m_pNowBodyTex->InitTexture(image.GetWidth(), image.GetHeight(), image.GetFormat(), Rainbow::TextureCreationFlags::kTextureInitNone);

	ImageReference dst1(image.GetWidth(), image.GetHeight(), bytesPerPixel * image.GetWidth(), format, m_pNowBodyTex->GetWritableImageData());
	if (image.GetImageData() != NULL)
		dst1.BlitImage(image);
	m_pNowBodyTex->Apply(false, false);


	m_pSourceTex = Rainbow::MakeSharePtr<Rainbow::Texture2D>(kMemTexture);
	m_pSourceTex->InitTexture(image.GetWidth(), image.GetHeight(), image.GetFormat(), Rainbow::TextureCreationFlags::kTextureInitNone);

	ImageReference dst2(image.GetWidth(), image.GetHeight(), bytesPerPixel * image.GetWidth(), format, m_pSourceTex->GetWritableImageData());
	if (image.GetImageData() != NULL)
		dst2.BlitImage(image);
	m_pSourceTex->Apply(false, false);


	char eyepath[256];
	sprintf(eyepath, "entity/player/player12/default_empty.png");
	SharePtr<Texture2D> eyePtex = GetAssetManager().LoadAssetAsync<Texture2D>(eyepath);
	if (!eyePtex)
		return false;

	model->SetTexture("g_AvatarFaceTex", eyePtex, "1");
	
	if (isBlend)
	{
		model->SetTexture("g_DiffuseTex", m_pNowBodyTex, "1");
	}
	else
	{
		model->SetTexture("g_DiffuseTex", m_pNowBodyTex, "1");
	}

	model->SetTextureAll("g_DiffuseTex", m_pNowBodyTex, "part1");

	return true;
}


void ActorBody::ParseAvatarInfo(jsonxx::Object& avatarInfoJson)
{
	if (avatarInfoJson.has<jsonxx::Array>("skin"))
	{
		jsonxx::Array skinArray = avatarInfoJson.get<jsonxx::Array>("skin");
		for (size_t i = 0; i < skinArray.size(); i++)
		{
			if (skinArray.values().at(i)->is<jsonxx::Object>())
			{
				float h, s, b = 0;
				int blockID = -1;

				jsonxx::Object aSkin = skinArray.get<jsonxx::Object>(i);
				if (aSkin.has<jsonxx::Object>("cfg"))
				{
					jsonxx::Object aSkinCfg = aSkin.get<jsonxx::Object>("cfg");
					if (aSkinCfg.has<jsonxx::Number>("ModelID") && aSkinCfg.has<jsonxx::Number>("Part"))
					{
						int modelId = (int)aSkinCfg.get<jsonxx::Number>("ModelID");
						int part = (int)aSkinCfg.get<jsonxx::Number>("Part");

						//异步装扮
						addAvatarPartModel(modelId, part, true);
						MNSandbox::alterAvatarPartColorAsyn(this, modelId, part, aSkin, h, s, b, blockID);
					}
				}
			}
		}
	}
}

bool ActorBody::IsAvatarPlayer()
{
	if (m_Entity == nullptr)
	{
		return false;
	}

	return getBodyType() == 3 && m_PlayerIndex > 0 && getSkinID() == 0;
}

bool ActorBody::IsShowAvatar(int partType)
{
	if (m_Entity == nullptr)
	{
		return false;
	}

	return m_Entity->GetMainModel() ? m_Entity->GetMainModel()->IsShowAvatar(partType) : false;
}

int ActorBody::GetAvatarParID(int partType)
{
	if (partType >= AVATAR_PART_TYPE::BODY && partType < AVATAR_PART_TYPE::MAX)
	{
		if (GetAvatarPartModelShow(partType))
		{
			return getAvatarListModelId(partType);
		}
	}

	return -1;
}


bool ActorBody::initCustomAvatar(std::string modelmark, int index)
{
	OPTICK_EVENT();
	core::string modelPath = "entity/player/avatar/body.prefab";
	Rainbow::Model* pModel = LoadModel(modelPath.c_str(), NULL);
	if (!pModel) return false;

	ReleaseMainEntity();
	UGCEntity* ugcEntity = UGCEntity::Create();
	m_Entity = ugcEntity;
	m_Entity->Load(pModel);
	ugcEntity->UpdateAvatarBodyOffset(index);
	UpdateVisiableDistance();
	m_HeadBoneID = pModel->GetBoneId("Head");

	ugcEntity->LoadPrefabAsync(modelmark.c_str(), [pModel, index, this](bool success, Rainbow::SharePtr<Rainbow::Asset> asset) {
		if (success)
		{
			if (asset->IsKindOf<Prefab>())
			{
				static_cast<ModelNew*>(pModel)->AddAvatar(index, asset.CastTo<Prefab>(), NULL, NULL);
				if (getIsInUI() || !isEffectAvatar(index))
				{
					pModel->ShowAvatar(index, true);
				}
			}
			else
			{
				pModel->AddAvatar(index, asset.CastTo<ModelData>(), NULL, NULL);
				pModel->ShowAvatar(index, true);
			}
		}
		});

	if (m_AttachScene) {
		m_AttachScene->AddGameObject(m_Entity->GetGameObject());
	}

	return true;
}



ActorBodyEquipComponent* ActorBody::GetEquipComponent()
{
	if (!m_EquipComponent)
	{
		m_EquipComponent = SANDBOX_NEW(ActorBodyEquipComponent, this);
	}

	return m_EquipComponent;
}

void ActorBody::getSkinPart(char* name, EQUIP_SLOT_TYPE slot, int itemid)
{
	GetEquipComponent()->getSkinPart(name, slot, itemid);
}

void ActorBody::getTexPath(char* texpath, EQUIP_SLOT_TYPE slot, int itemid)
{
	GetEquipComponent()->getTexPath(texpath, slot, itemid);
}

void ActorBody::clearEquipSlot(EQUIP_SLOT_TYPE slot)
{
	if (m_EquipComponent)
		GetEquipComponent()->clearEquipSlot(slot);
}

void ActorBody::getEquitMesh(int itemid, int& equipmesh)
{
	GetEquipComponent()->getEquitMesh(itemid, equipmesh);
}

//装备武器皮肤
bool ActorBody::equipSkinWeapon(int itemid, int skinid)
{
	return GetEquipComponent()->equipSkinWeapon(itemid, skinid);
}

void ActorBody::equipWeaponItem(EQUIP_SLOT_TYPE slot, int itemid)
{
	return GetEquipComponent()->equipWeaponItem(slot, itemid);
}

void ActorBody::equipPifengItem(EQUIP_SLOT_TYPE slot, int itemid)
{
	return GetEquipComponent()->equipPifengItem(slot, itemid);
}

void ActorBody::equipPifengSpecial(EQUIP_SLOT_TYPE slot, int itemid)
{
	return GetEquipComponent()->equipPifengSpecial(slot, itemid);
}

void ActorBody::equipShoeItem(EQUIP_SLOT_TYPE slot, int itemid, char* skinname)
{
	return GetEquipComponent()->equipShoeItem(slot, itemid, skinname);
}

void ActorBody::setEquipItem(EQUIP_SLOT_TYPE slot, int itemid)
{
	return GetEquipComponent()->setEquipItem(slot, itemid);
}

//是否为自定义装备
bool ActorBody::isCustomEquip(int itemId)
{
	return GetEquipComponent()->isCustomEquip(itemId);
}

void ActorBody::setCustomEquip(EQUIP_SLOT_TYPE slot, int itemid)
{
	return GetEquipComponent()->setCustomEquip(slot, itemid);
}

//脱掉自定义装备
void ActorBody::takeoffCustomEquip(int slot)
{
	return GetEquipComponent()->takeoffCustomEquip(slot);
}

//穿上常规装备
void ActorBody::putonNormalEquip(int itemid)
{
	return GetEquipComponent()->putonNormalEquip(itemid);
}

//判断是否为普通的装备(即头盔、胸甲这种)
bool ActorBody::isNormalEquip(int itemid)
{
	return GetEquipComponent()->isNormalEquip(itemid);
}

/**************************************穿上自定义装备部件**************************************
函数名: putonCustomEquipPart
参  数: pPartDef:部件定义
		slot	:装备格子位置(头部、披风...)
		nPartIndex:部件索引, [0, 3)
**********************************************************************************************/
void ActorBody::putonCustomEquipPart(EquipmentPartDef* pPartDef, int slot, int nPartIndex)
{
	return GetEquipComponent()->putonCustomEquipPart(pPartDef, slot, nPartIndex);
}

EquipmentPartDef* ActorBody::GetUIEquipmentPartDef()
{
	return GetEquipComponent()->GetUIEquipmentPartDef();
}

void ActorBody::ReleaseUIEquipmentPartDef()
{
	if (m_EquipComponent)
	{
		GetEquipComponent()->ReleaseUIEquipmentPartDef();
	}
}

void ActorBody::releaseCustomEquip()
{
	if (m_EquipComponent)
	{
		GetEquipComponent()->releaseCustomEquip();
	}
}

int ActorBody::getCurShowEquipItemId(EQUIP_SLOT_TYPE slot)
{
	return GetEquipComponent()->getCurShowEquipItemId(slot);
}

bool ActorBody::GetWorldPosFromWeapon(int anchorId, Rainbow::Vector3f& boneWorldPos)
{
	return GetEquipComponent()->GetWorldPosFromWeapon(anchorId, boneWorldPos);
}


void ActorBody::updateToolModelTexture(int texureIndex /*= 0*/)
{
	if (m_EquipComponent && m_EquipComponent->m_WeaponModel)
	{
		auto model = dynamic_cast<ModelItemMesh*>(m_EquipComponent->m_WeaponModel);
		if (model)
		{
			model->updateToolModelTexture(texureIndex);
		}
	}
}