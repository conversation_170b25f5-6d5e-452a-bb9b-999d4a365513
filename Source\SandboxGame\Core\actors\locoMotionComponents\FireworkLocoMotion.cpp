
#include "ActorFirework.h"
#include "ActorLocoMotion.h"
#include "world.h"
#include "ClientItem.h"
#include "special_blockid.h"
#include "OgreUtils.h"
//#include "OgreMath.h"
//#include "OgreRenderableObject.h"
#include "BlockScene.h"
#include "BlockMesh.h"
#include "BlockFirework.h"
#include "BlockMaterialMgr.h"
#include "ClientActorManager.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "ActorExpOrb.h"
#include "WorldManager.h"
#include "Environment.h"
#include "BaseItemMesh.h"
#include "PlayerControl.h"
#include "FireworkLocoMotion.h"

using namespace MINIW;
using namespace Rainbow;

const int BLOCK_FIREWORK = 850;
inline bool IsSmallExpFirework(int d)
{
	return d<2000;
}
IMPLEMENT_COMPONENTCLASS(FireworkLocoMotion)
FireworkLocoMotion::FireworkLocoMotion() 
{
	m_BoundHeight = 25;
	m_BoundSize = 25;
	m_yOffset = m_BoundHeight/2;
}

void FireworkLocoMotion::setMovement(const Vector3f& initMotion, const Vector3f& accelerate)
{
	m_Motion = initMotion;
	m_Accelerate = accelerate;
}

void FireworkLocoMotion::tick()
{
	ActorLocoMotion::tick();
	m_Motion += m_Accelerate;
	doMoveStep(m_Motion);
}

