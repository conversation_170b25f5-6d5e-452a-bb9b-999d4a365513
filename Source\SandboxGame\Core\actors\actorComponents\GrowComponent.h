#ifndef __GROW_COMPONENT_H__
#define __GROW_COMPONENT_H__
#include "coreMisc.h"
#include "ActorComponent_Base.h"

class ClientMob;

class GrowComponent : public ActorComponentBase//tolua_exports
{//tolua_exports
public:
	DECLARE_COMPONENTCLASS(GrowComponent)

	GrowComponent();
	~GrowComponent();

	bool isAdult()
	{
		return m_GrowingAge >= 0 && !m_bWaitToAdult;
	}
	int getGrowingAge()
	{
		return m_GrowingAge;
	}
	int getGrowingTime()
	{
		return m_GrowingTime;
	}
	int getGrowingDValue()
	{
		return m_GrowingDValue;
	}
	void setGrowingTime(int dtime)
	{
		m_GrowingTime = dtime;
	}
	void setGrowingAge(int growingAge);
		void setGrowingDValue(int dValue)
	{
		int DV = GenRandomInt(dValue,0);
		m_GrowingDValue = DV;
	}
	void mobAdult();

	bool isEaten() { 
		return m_bEaten; 
	}
	void setEaten(bool eaten)
	{
		m_bEaten = eaten;
	}
	void setCurNeedFeedNum(int num)
	{
		m_iCurNeedFeedNum = num;
	}
	int getCurNeedFeedNum()
	{
		return m_iCurNeedFeedNum;
	}
	bool isNeedFeed() { 
		return m_iCurNeedFeedNum > 0; 
	}
	bool hadFeedFood() { 
		return m_iCurNeedFeedNum <= 0 || m_bEaten; 
	}

	//void growTick();
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

	bool canFeed()
	{
		return m_GrowingAge == 0;
	}

	//void initMobGrowTime();
	int getMobGrowTime(int mobID); 
	int getMobGrowTimeDValue(int mobID);
protected:
	ClientMob* m_owner;

	int m_GrowingAge;
	bool m_bWaitToAdult;	// 待成年状态 时间上已满足成长需求
	bool m_bEaten;  //是否已进食（原有自动成长改为进食后再成长）
	int m_iCurNeedFeedNum;	// 触发成长和繁殖还需要进食的次数（目前只有饲料槽需要）
	int m_GrowingDValue;//生长差值
	int m_GrowingTime;//生长时间戳

};//tolua_exports

 //tolua_begin
extern void MobAddGrowTime(int mobID, int time, int dValue);
//tolua_end
#endif