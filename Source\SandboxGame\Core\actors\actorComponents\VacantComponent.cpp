#include "VacantComponent.h"
#include "EffectManager.h"
#include "LuaInterfaceProxy.h"
#include "ActorAttrib.h"
#include "WorldManager.h"
#include "DangerNightManager.h"
#include "PlayerControl.h"
#include "SandBoxManager.h"
#include "SandboxIdDef.h"
#include "EffectComponent.h"
#include "MpActorManager.h"
#include "MpActorTrackerEntry.h"
#include "VacantVortexComponent.h"
#include "ActorHorse.h"

IMPLEMENT_COMPONENTCLASS(VacantComponent)

using namespace game::common;

//普通生物转虚空
const static std::unordered_map<int, std::vector<int>> static_Normal2VacantMap = {
	{3414, {3244}},					//团子				-->	虚空团子
	{3415, {3245}},					//驯服的团子		-->	驯服的虚空团子
	{3403, {3246}},					//普通角鹿			-->	虚空角鹿
	{3407, {3248, 3250, 3252}},		//狐狸				-->	虚空1尾狐狸 虚空2尾狐狸 虚空3尾狐狸
	{3408, {3249, 3251, 3253}},		//驯服的狐狸（灵狐）--> 驯服的虚空1尾狐狸 2尾 3尾
	{3910, {3254}},					//雪兔				--> 虚空雪兔
	{3401, {3261}},					//沃沃兽			-->	虚空沃沃兽
	{3891, {3262}},					//驯服的沃沃兽		-->	驯服的虚空沃沃兽
	{3885, {3255}},					//红薇蝶			--> 虚空蝴蝶
	{3886, {3255}},					//兰青蝶			--> 虚空蝴蝶
	{3887, {3255}},					//香粉蝶			--> 虚空蝴蝶
	{3888, {3255}},					//向阳蝶			--> 虚空蝴蝶
	{3889, {3255}},					//龙信蝶			--> 虚空蝴蝶
	{3890, {3255}},					//舌钟蝶			--> 虚空蝴蝶
	{3626, {3263}},					//水母				--> 虚空水母
	{3828, {3264}},					//沙漠狼			--> 虚空沙漠狼
	//{3413, {3265}},					//迅猛龙			--> 虚空迅猛龙
	{3628, {3266}},					//鯊魚				--> 虚空鯊魚
	{3107, {3267}},					//蝙蝠				--> 虚空蝙蝠
	{3872, {3268}},					//豹子				--> 虚空豹子
	{3225, {3269}},					//小型海盗船		--> 虚空小型海盗船
	{3226, {3272}},					//中型海盗船		--> 虚空中型海盗船
	{3227, {3273}},					//大型海盗船		--> 虚空大型海盗船
	{3419, {3270}},					//萤火虫			--> 虚空萤火虫
};

//虚空生物转普通
const static std::unordered_map<int, std::vector<int>> static_Vacant2NormalMap = {
	{3244, {3414}},					//虚空团子			-->	团子
	{3245, {3415}},					//驯服的虚空团子	--> 驯服的团子
	{3246, {3403}},					//虚空角鹿			--> 角鹿
	{3247, {3403}},					//驯服的虚空角鹿	--> 角鹿
	{3248, {3407}},					//虚空1尾狐狸		--> 狐狸
	{3250, {3407}},					//虚空2尾狐狸		--> 狐狸
	{3252, {3407}},					//虚空3尾狐狸		--> 狐狸
	{3249, {3408}},					//驯服的虚空1尾狐狸	--> 驯服的狐狸
	{3251, {3408}},					//驯服的虚空2尾狐狸	--> 驯服的狐狸
	{3253, {3408}},					//驯服的虚空3尾狐狸	--> 驯服的狐狸
	{3254, {3910}},					//虚空雪兔			--> 雪兔
	{3261, {3401}},					//虚空沃沃兽		-->	沃沃兽
	{3262, {3891}},					//驯服的虚空沃沃兽  -->	驯服的沃沃兽
	{3255, {3885, 3886, 3887, 3888, 3889, 3890}},					//虚空蝴蝶			--> 红薇蝶 兰青蝶 香粉蝶 向阳蝶 龙信蝶 舌钟蝶
	{3263, {3626}},					//虚空水母			-->	水母
	{3264, {3828}},					//虚空沙漠狼		-->	沙漠狼
	//{3265, {3413}},					//虚空迅猛龙		--> 迅猛龙
	{3266, {3628}},					//虚空鯊魚			--> 鯊魚
	{3267, {3107}},					//虚空蝙蝠			--> 蝙蝠 
	{3268, {3872}},					//虚空豹子			--> 豹子
	{3269, {3225}},					//虚空小型海盗船	--> 小型海盗船
	{3272, {3226}},					//虚空中型海盗船	--> 中型海盗船
	{3273, {3227}},					//虚空大型海盗船	--> 大型海盗船
	{3270, {3419}},					//虚空萤火虫		--> 萤火虫
};

bool VacantComponent::IsVacantId(int monsterid)
{
	if (static_Normal2VacantMap.find(monsterid) != static_Normal2VacantMap.end())
		return true;

	if (static_Vacant2NormalMap.find(monsterid) != static_Vacant2NormalMap.end())
		return true;

	return false;
}

VacantComponent::VacantComponent()
{
	
}

void VacantComponent::init()
{
	if (!GetOwner()) return;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (m_owner == NULL)
	{
		return;
	}

	m_nMaxVacantEnergy = 100;
	m_nCurVacantEnergy = m_nMaxVacantEnergy;
	m_nChangeId = 0;
	m_nLackEnergyTipTick = 0;
	
	m_bIsNormalType = false;
	m_bIsVacantType = false;
	m_bNeedRefreshChangeHit = true;
	m_bIsNatureChangeHit = false;

	m_nNatureChangeHitRate = 1;
	jsonxx::Object vacantConfig = GetLuaInterfaceProxy().get_lua_const()->vacantConfig;
	if (vacantConfig.has<jsonxx::Number>("natureChangeHitRate"))
	{
		m_nNatureChangeHitRate = vacantConfig.get<jsonxx::Number>("natureChangeHitRate");
	}

	m_bIsUseItemToChange = false;

	m_pChangeMobObjid = 0;

	m_curMonsterId = m_owner->getDefID();
	m_nChangeFrom = 0;

	auto iter = static_Normal2VacantMap.find(m_curMonsterId);
	if (iter != static_Normal2VacantMap.end())
	{
		m_nChangeId = GetChangeId(m_curMonsterId, iter->second);
		m_bIsNormalType = true;
		m_bIsVacantType = false;
	}

	if (m_nChangeId <= 0)
	{
		iter = static_Vacant2NormalMap.find(m_curMonsterId);
		if (iter != static_Vacant2NormalMap.end())
		{
			m_nChangeId = GetChangeId(m_curMonsterId, iter->second);
			m_bIsNormalType = false;
			m_bIsVacantType = true;
		}
	}
}

VacantComponent::~VacantComponent()
{

}

void VacantComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	init();
	BindOnTick();
}

void VacantComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void VacantComponent::OnEndPlay()
{
	Super::OnEndPlay();
	clearVacantVortex();
}

int VacantComponent::GetChangeId(int monsterId, const std::vector<int>& vecData)
{
	if (vecData.size() > 1)
	{
		if ((monsterId == 3407 || monsterId == 3408) && vecData.size() >= 3) //狐狸 概率转变
		{
			int randNum = GenRandomInt(100);
			if (randNum <= 60)
				return vecData.at(0);
			else if (randNum <= 90)
				return vecData.at(1);

			return vecData.back();
		}
		else if (monsterId == 3255)
		{
			if (m_nChangeFrom == 0)
				return vecData.front();

			return m_nChangeFrom;
		}
	}

	return vecData.front();
}

void VacantComponent::updateLackEnergyTip()
{
	if (!GetOwner()) return;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (m_owner == NULL)
	{
		return;
	}

	if (m_owner->getWorld() && !m_owner->getTamedOwner())
	{
		return;
	}

	auto pEffectComponent = m_owner->getEffectComponent();
	if (!pEffectComponent)
	{
		return;
	}

	char effectStr1[32] = "xukongqipao01";
	char effectStr2[32] = "xukongqipao02";

	//显示虚空饥饿气泡逻辑
	if (m_nCurVacantEnergy > 50)
	{
		pEffectComponent->stopBodyEffect(effectStr1);
		pEffectComponent->stopBodyEffect(effectStr2);
		m_nLackEnergyTipTick = 0;
	}
	else if (m_nCurVacantEnergy > 20 && m_nCurVacantEnergy <= 50)
	{
		m_nLackEnergyTipTick++;
		int nTime = 5 * 20; //持续时间
		if (m_nLackEnergyTipTick <= nTime)
		{
			pEffectComponent->playBodyEffect(effectStr1);
			pEffectComponent->stopBodyEffect(effectStr2);
		}
		else
		{
			pEffectComponent->stopBodyEffect(effectStr1);
			pEffectComponent->stopBodyEffect(effectStr2);
		}
	}
	else if (m_nCurVacantEnergy <= 20)
	{
		pEffectComponent->stopBodyEffect(effectStr1);
		pEffectComponent->playBodyEffect(effectStr2);
		m_nLackEnergyTipTick = 0;
	}
}

void VacantComponent::OnTick()
{
	if (!GetOwner()) return;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (m_owner == NULL)
	{
		return;
	}

	bool bVoidNight = isVoidNight();

	if (m_bIsNormalType)
	{
		if (!bVoidNight && !m_bNeedRefreshChangeHit)
		{
			m_bNeedRefreshChangeHit = true;
		}

		if (bVoidNight && m_bNeedRefreshChangeHit)
		{
			m_bIsNatureChangeHit = GenRandomInt(100) < m_nNatureChangeHitRate ? true : false;//有概率命中
			m_bNeedRefreshChangeHit = false;
		}

		if (bVoidNight && m_bIsNatureChangeHit)
		{
			toChange();
		}
	}
	else if(m_bIsVacantType)
	{
		if (m_owner->getTamedOwner() && m_nCurVacantEnergy <= 0) //被驯服且能量消耗完
		{
			toChange();
		}
		else if ((!bVoidNight && !m_owner->getTamedOwner() && !IsUseItemToChange())) //非虚空之夜且不是被驯服跟不是使用道具变身的
		{
			toChange();
		}

		updateLackEnergyTip();
	}
}

void VacantComponent::setMaxVacantEnergy(int nVacantEnergy)
{
	m_nMaxVacantEnergy = nVacantEnergy;
}

int VacantComponent::getMaxVacantEnergy()
{
	return m_nMaxVacantEnergy;
}

void VacantComponent::setCurVacantEnergy(int nVacantEnergy)
{
	if (!GetOwner()) return;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	ClientActor* m_ownerActor = GetOwner()->ToCast<ClientActor>();
	if (m_owner == NULL || m_ownerActor == NULL)
	{
		return;
	}

	if (m_owner->getWorld() && !m_owner->getTamedOwner())
	{
		return;
	}

	int old = m_nCurVacantEnergy;

	if (nVacantEnergy < 0)
	{
		nVacantEnergy = 0;
	}
	else if (nVacantEnergy > m_nMaxVacantEnergy)
	{
		nVacantEnergy = m_nMaxVacantEnergy;
	}
	m_nCurVacantEnergy = nVacantEnergy;

	syncAttr(ATTRT_VACANT_ENERGY, m_nCurVacantEnergy);
	if (m_owner->getWorld() && m_owner->getTamedOwner()->hasUIControl() && int(m_nCurVacantEnergy) != old)
	{
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	}
}

void VacantComponent::syncAttr(int attrType, float val)
{
	if (!GetOwner()) return;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (m_owner == NULL)
	{
		return;
	}

	if (m_owner->getWorld() && !m_owner->getWorld()->isRemoteMode() && attrType == ATTRT_VACANT_ENERGY)
	{
		jsonxx::Object object;
		object << "objid" << m_owner->getObjId();
		object << "attrtype" << attrType;
		object << "val" << val;
		//相关逻辑在LuaMsgHandle.lua中处理
		GetSandBoxManager().sendBroadCast("ACTOR_SET_ATTR_TOTRACKINGPLAYERS", object.bin(), object.binLen());
	}
}

int VacantComponent::getCurVacantEnergy()
{
	return m_nCurVacantEnergy;
}

void VacantComponent::setChangeFrom(int from)
{
	m_nChangeFrom = from;
}

int VacantComponent::getChangeFrom()
{
	return m_nChangeFrom;
}

void VacantComponent::toChange()
{
	if (!GetOwner()) return;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	World* pWorld = m_owner->getWorld();
	if (!(m_owner && pWorld && pWorld->getActorMgr() && pWorld->getMpActorMgr() && pWorld->getEffectMgr()))
		return;
	ClientActorMgr* actorMgr = pWorld->getActorMgr()->ToCastMgr();
	if (!actorMgr) return;

	if (pWorld->isRemoteMode())
		return;

	if (m_nChangeId <= 0)
		return;

	if (m_owner && !m_owner->alive())
	{
		return;
	}

	int changeId = m_nChangeId;
	if (m_nChangeFrom != 0)
		changeId = m_nChangeFrom;

	ClientMob* newmob = ClientMob::createFromDef(changeId);
	if (newmob)
	{
		newmob->getLocoMotion()->gotoPosition(m_owner->getLocoMotion()->m_Position);
		actorMgr->spawnActor(newmob);

		//特殊操作 被驯服的虚空角鹿 转换 不用判断是否被驯服 因为没有对应的驯服的普通怪物
		bool bNeedRestoreTamed = true;
		if (m_curMonsterId == 3247)
		{
			bNeedRestoreTamed = false;
		}

		if (bNeedRestoreTamed)
		{
			ClientPlayer* pTamedOwner = m_owner->getTamedOwner();
			if (pTamedOwner) // 如果已经被驯服变回驯服状态
			{
				newmob->setTamedOwnerUin(pTamedOwner->getUin());
				newmob->pushToTamedOwnerTamedList();
			}
		}

		auto pVacantComponent = newmob->getVacantComponent();
		if (pVacantComponent)
		{
			pVacantComponent->setIsUseItemToChange(IsUseItemToChange());

			if (newmob->getDefID() == 3255)
				pVacantComponent->setChangeFrom(m_curMonsterId);
		}

		//需要先一步确定tracker,才能成功同步effect
		MpActorTrackerEntry* entry = pWorld->getMpActorMgr()->getTrackerEntry(newmob->getObjId());
		if (entry)
		{
			entry->updateTracker();//先初始化
			entry->updateTracker();//执行tracker
		}
		m_pChangeMobObjid = newmob->getObjId();

		if (m_bIsNatureChangeHit)
		{
			auto effectComponent = newmob->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("void_spread_entity", true, 2.0f);
			}
		}

		// 驯服沃沃兽放耙后虚空化要继承对应的道具
		ActorHorse* pActorHorse = dynamic_cast<ActorHorse*>(m_owner);
		if (pActorHorse != nullptr)
		{
			ActorHorse* pNewHorse = dynamic_cast<ActorHorse*>(newmob);
			BackPackGrid* pTempGrid = pActorHorse->index2Grid(HORSE_EQUIP_INDEX + 2);
			if (pNewHorse != nullptr && pTempGrid != nullptr)
			{
				pNewHorse->equipRake(*pTempGrid);
				bool isRakeActor = pActorHorse->isRakeToolLiving();
				pNewHorse->setRakeToolLiving(isRakeActor);
			}
		}
	}

	m_owner->SetPlayClearFx(false); // 不播放消除动画
	m_owner->setNeedClear();
}

bool VacantComponent::isVoidNight()
{
	bool bVoidNight = false;
	DangerNightManager* pDNMgr = static_cast<DangerNightManager*>(g_WorldMgr->getDangerNightManager());
	if (pDNMgr)
	{
		bVoidNight = pDNMgr->isVoidNight();
	}
	return bVoidNight;
}

void VacantComponent::setIsUseItemToChange(bool isUseItemChange)
{
	m_bIsUseItemToChange = isUseItemChange;
}

bool VacantComponent::IsUseItemToChange()
{
	return m_bIsUseItemToChange;
}

bool VacantComponent::useItemToChange()
{
	bool isChangeHit = GenRandomInt(100) < 20 ? true : false;//20%概率命中
	if (m_bIsNormalType && m_nChangeId > 0 && isChangeHit)
	{
		setIsUseItemToChange(true);
		toChange();
		return true;
	}
	return false;
}

int VacantComponent::GetInteractVacantEnergy(ClientMob* m_owner, int currItemID, bool &bCanUse)
{
	if (m_owner == NULL)
	{
		return 0;
	}

	jsonxx::Object vacantConfig = GetLuaInterfaceProxy().get_lua_const()->vacantConfig;
	jsonxx::Object commonData; // 通用道具
	jsonxx::Object veganData; // 素食道具
	jsonxx::Object nonVeganData; // 肉食道具
	
	if (vacantConfig.has<jsonxx::Object>("common"))
	{
		commonData = vacantConfig.get<jsonxx::Object>("common");
	}
	if (vacantConfig.has<jsonxx::Object>("vegan"))
	{
		veganData = vacantConfig.get<jsonxx::Object>("vegan");
	}
	if (vacantConfig.has<jsonxx::Object>("nonVegan"))
	{
		nonVeganData = vacantConfig.get<jsonxx::Object>("nonVegan");
	}

	int nAddEnergy = 0;
	int nFodderType = m_owner->m_Def->Fodder[0]; // 饲养类型
	bCanUse = false;

	std::string currItemIDStr = std::to_string(currItemID);
	if (commonData.has<jsonxx::Number>(currItemIDStr)) // 通用道具
	{
		nAddEnergy = commonData.get<jsonxx::Number>(currItemIDStr);
		bCanUse = true;
	}
	else if (veganData.has<jsonxx::Number>(currItemIDStr)) // 草食生物或杂食生物
	{
		nAddEnergy = veganData.get<jsonxx::Number>(currItemIDStr);
		if (nFodderType == 1 || nFodderType == 3)
		{
			bCanUse = true;
		}
	}
	else if (nonVeganData.has<jsonxx::Number>(currItemIDStr)) // 肉食生物或杂食生物
	{
		nAddEnergy = nonVeganData.get<jsonxx::Number>(currItemIDStr);
		if (nFodderType == 2 || nFodderType == 3)
		{
			bCanUse = true;
		}
	}


	return nAddEnergy;
}

bool VacantComponent::interact(ClientActor* player, int currItemID)
{
	if (!GetOwner()) return false;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return false;
	World* pworld = m_owner->getWorld();
	if (!pworld) return false;
	ClientActorMgr* actorMgr = pworld->getActorMgr() ? pworld->getActorMgr()->ToCastMgr() : nullptr;
	if (!actorMgr) return false;

	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (pTempPlayer == NULL)
	{
		return false;
	}
	
	bool bInteract = false; // 是否跟道具交互
	bool ret = false;
	bool bPlayEffect = false;
	if (m_bIsNormalType)
	{
		if (currItemID == ITEM_VOID_CRYSTAL) //虚空晶体
		{
			pTempPlayer->shortcutItemUsed();
			ret = useItemToChange();
			bPlayEffect = true;
			bInteract = true;
		}
	}
	else if (m_bIsVacantType)
	{
		if (m_owner->getTamedOwner()) // 被驯服的生物
		{
			//const FoodDef* fooddef = GetDefManagerProxy()->getFoodDef(currItemID); //fooddef->Type
			bool bCanUse = false;
			int nAddEnergy = VacantComponent::GetInteractVacantEnergy(m_owner, currItemID, bCanUse);
			if (nAddEnergy > 0 && bCanUse && (getCurVacantEnergy() < getMaxVacantEnergy()))
			{
				pTempPlayer->shortcutItemUsed();
				setCurVacantEnergy(getCurVacantEnergy() + nAddEnergy);
				ret = true;
			}
			if (nAddEnergy > 0) // 是添加虚空能量的道具
			{
				bPlayEffect = true;
				bInteract = true;
			}
		}
	}

	if (bPlayEffect)
	{
		ClientMob* playEffectMob;
		if (m_pChangeMobObjid != 0)
		{
			playEffectMob = dynamic_cast<ClientMob*>(actorMgr->findActorByWID(m_pChangeMobObjid));
		}
		else
		{
			playEffectMob = m_owner;
		}

		if (!(playEffectMob && playEffectMob->getWorld() && playEffectMob->getWorld()->getEffectMgr()))
		{
			return ret;
		}

		if (ret)
		{
			auto effectComponent = playEffectMob->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(BODYFX_TAME_SUCCEED);
			}
			playEffectMob->getWorld()->getEffectMgr()->playSoundAtActor(playEffectMob, "ui.info.tame_success", 1.0f, 1.0f);
		}
		else
		{
			auto effectComponent = playEffectMob->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(BODYFX_TAME_FAILED);
			}
			playEffectMob->getWorld()->getEffectMgr()->playSoundAtActor(playEffectMob, "ui.info.tame_failed", 1.0f, 1.0f);
		}
	}

	return bInteract;
}

ClientMob* VacantComponent::createVacantVortex(long long nEnterObjid)
{
	if (!GetOwner()) return NULL;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return NULL;
	World* pworld = m_owner->getWorld();
	if (!pworld) return NULL;
	ClientActorMgr* actorMgr = pworld->getActorMgr() ? pworld->getActorMgr()->ToCastMgr() : nullptr;
	if (!actorMgr) return NULL;

	if (nEnterObjid == 0)
	{
		clearVacantVortex();
	}

	ClientMob* newmob = ClientMob::createFromDef(3271);
	if (newmob)
	{
		actorMgr->spawnActor(newmob);
		auto pNewMobVacantVortexComp = newmob->getVacantVortexComponent();
		if (pNewMobVacantVortexComp)
		{
			pNewMobVacantVortexComp->setVortexOwnerOjbid(m_owner->getObjId());
		}
		if (nEnterObjid == 0) //入口
		{
			if (pNewMobVacantVortexComp)
			{
				pNewMobVacantVortexComp->setEnterObjid(newmob->getObjId());
			}
		}
		else //出口
		{
			ClientMob* pEnterActor = dynamic_cast<ClientMob*>(actorMgr->findActorByWID(nEnterObjid));
			if (!pEnterActor)
			{
				clearVacantVortex();
				return NULL;
			}
			pNewMobVacantVortexComp->setEnterObjid(pEnterActor->getObjId());
			pNewMobVacantVortexComp->setExitObjid(newmob->getObjId());

			auto pEnterVacantVortexComp = pEnterActor->getVacantVortexComponent();
			if (pEnterVacantVortexComp)
			{
				pEnterVacantVortexComp->setExitObjid(newmob->getObjId());
			}
		}
		return newmob;
	}
	return NULL;
}

void VacantComponent::clearVacantVortex()
{
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return ;
	World* pworld = m_owner->getWorld();
	if (!pworld) return ;
	ClientActorMgr* actorMgr = pworld->getActorMgr() ? pworld->getActorMgr()->ToCastMgr() : nullptr;
	if (!actorMgr) return ;

	auto selectFunc = [this,&m_owner](ClientActor* c)
	{
		ClientMob* pVortexMob = dynamic_cast<ClientMob*>(c);
		if (pVortexMob)
		{
			auto pVacantVortexComponent = pVortexMob->getVacantVortexComponent();
			return (pVacantVortexComponent && (pVacantVortexComponent->getVortexOwnerOjbid() == m_owner->getObjId()));
		}
		return false;
	};

	std::vector<ClientActor*>pVector;
	actorMgr->FindActors(pVector, selectFunc);

	for (size_t i = 0; i < pVector.size(); i++)
	{
		pVector[i]->setNeedClear();
	}
}

void VacantComponent::CreateComponentData(jsonxx::Object& componentData)
{
	jsonxx::Object userData;
	userData.import("maxVacantEnergy", jsonxx::Number(getMaxVacantEnergy()));
	userData.import("curVacantEnergy", jsonxx::Number(getCurVacantEnergy()));
	userData.import("changeFrom", jsonxx::Number(getChangeFrom()));
	userData.import("isUseItemToChange", jsonxx::Boolean(IsUseItemToChange()));
	componentData.import("VacantComponent", jsonxx::Object(userData));
}

void VacantComponent::LoadComponentData(const jsonxx::Object& componentData, bool fromArchive)
{
	if (componentData.has<jsonxx::Object>("VacantComponent"))
	{
		jsonxx::Object userData = componentData.get<jsonxx::Object>("VacantComponent");
		if (userData.has<jsonxx::Number>("maxVacantEnergy"))
		{
			setMaxVacantEnergy(userData.get<jsonxx::Number>("maxVacantEnergy"));
		}
		if (userData.has<jsonxx::Number>("curVacantEnergy"))
		{
			setCurVacantEnergy(userData.get<jsonxx::Number>("curVacantEnergy"));
		}
		if (userData.has<jsonxx::Number>("changeFrom"))
		{
			setChangeFrom(userData.get<jsonxx::Number>("changeFrom"));
		}
		if (userData.has<jsonxx::Boolean>("isUseItemToChange"))
		{
			setIsUseItemToChange(userData.get<jsonxx::Boolean>("isUseItemToChange"));
		}
	}
}