#ifndef __BIND_ACTOR_COMPONENT_H__
#define __BIND_ACTOR_COMPONENT_H__

//#include "ActorTypes.h"
#include "BaseTargetComponent.h"
#include <vector>
#include "SandboxGame.h"

class ClientActor;
class ClientPlayer;
class ActorBasketBall;
class BaseItemMesh;

class EXPORT_SANDBOXGAME BindActorComponent;
class BindActorComponent : public BaseTargetComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(BindActorComponent)
	//tolua_begin
	BindActorComponent(/*ClientActor* owner*/);
	virtual ~BindActorComponent();
	void CreateEvent2();
	void DestroyEvent2();

	virtual void setBindInfo(WORLD_ID objIdBind, const WCoord &bindPos, bool bindoperate =false); //bindoperate 标记客机执行绑定的逻辑
	virtual void leaveWorld(bool keep_inchunk){};
	void unbind();
	bool hasBindChildren();
	bool unbindWithCheckPhys();
	void updatePosByBindActor();
	void sendBindMsg(int uin);//PB_ACTOR_BIND_HC
	void setBindChildren(WORLD_ID objId, bool unbind);	

	ClientActor *getCatchBall();
	ClientActor *getCatchGravityActor();
	//tolua_end
protected:	
	virtual void setUnbindPos() {}

	WCoord	 m_OffetBind;
	std::vector<WORLD_ID> m_BindChildren;

	// 往Actor身上挂Item
public:
	int bindItemToAnchor(int itemId, int anchorId);
	void unbindItem(int bindId);

	void unbindAllItems();

	void bindItemFromHost(int bindId, int itemId, int anchorId);
	void unbindItemFromHost(int bindId);
	BaseItemMesh* getBindItem(int bindId);
private:
	std::unordered_map<int, BaseItemMesh*> m_BindItems;
	int m_bindUId;

	MNSandbox::AutoRef<MNSandbox::Listener<int, int*>> m_listenerBindActor1;
}; //tolua_exports

class ActorBallComponent : public BindActorComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(ActorBallComponent)
	//tolua_begin
	ActorBallComponent(/*ClientActor* owner*/);
	virtual void setBindInfo(WORLD_ID objIdBind, const WCoord &bindPos, bool bindoperate =false) override;
	//tolua_end
}; //tolua_exports

class ActorBasketBallComponent : public BindActorComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(ActorBasketBallComponent)
	//tolua_begin
	ActorBasketBallComponent(/*ActorBasketBall* owner*/);
	virtual void setBindInfo(WORLD_ID objIdBind, const WCoord &bindPos, bool bindoperate =false) override;
	virtual void leaveWorld(bool keep_inchunk) override;
	//tolua_end
protected:	
	virtual void setUnbindPos()override;

private:
	ActorBasketBall* m_ownerBasketBall;
}; //tolua_exports


class ActorPushSnowBallComponent : public BindActorComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(ActorPushSnowBallComponent)
	//tolua_begin
	ActorPushSnowBallComponent(/*ClientActor* owner*/);
	virtual void setBindInfo(WORLD_ID objIdBind, const WCoord& bindPos, bool bindoperate = false) override;
	//tolua_end
}; //tolua_exports

class ClientSouvenirComponent : public BindActorComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(ClientSouvenirComponent)
	//tolua_begin
	ClientSouvenirComponent(/*ClientActor* owner*/);
	virtual void leaveWorld(bool keep_inchunk) override;
	//tolua_end
}; //tolua_exports
#endif