
#ifndef __WORLD_TYPES_H__
#define __WORLD_TYPES_H__

#include <vector>
#include <assert.h>
#include "OgreWorldPos.h"
#include "OgreVector3.h"
#include "OgreMath.h"
#include "OgreWCoord.h"
const int TICKS_ONEDAY = 20*60*20; //20分钟

#define SECTION_BLOCK_DIM 16  //每个section在x, y, z方向的块数
#define SECTION_TOTAL_BLOCKS (SECTION_BLOCK_DIM*SECTION_BLOCK_DIM*SECTION_BLOCK_DIM)
#define CHUNK_SECTION_DIM 16  //每个chunk有多少个section
#define CHUNK_BLOCK_X SECTION_BLOCK_DIM  //chunk在x方向有多少块
#define CHUNK_BLOCK_Z SECTION_BLOCK_DIM
#define CHUNK_BLOCK_Y (SECTION_BLOCK_DIM*CHUNK_SECTION_DIM)
#define CHUNK_TOTAL_BLOCKS (CHUNK_BLOCK_X*CHUNK_BLOCK_Z*CHUNK_BLOCK_Y)

#define SECTION_SIZE (SECTION_BLOCK_DIM*BLOCK_SIZE)
#define CHUNK_SIZE_X (SECTION_BLOCK_DIM*BLOCK_SIZE)
#define CHUNK_SIZE_Y (SECTION_BLOCK_DIM*CHUNK_SECTION_DIM*BLOCK_SIZE)
#define CHUNK_SIZE_Z (SECTION_BLOCK_DIM*BLOCK_SIZE)

const float JUMP_HEIGHT = 130.0f;//跳起高度
const float JUMP_TIME = 0.35f; //跳起上升到最高点时间
const float VELOCITY_JUMP = JUMP_HEIGHT*2/JUMP_TIME;
const float GAME_TICK_TIME = 0.05f;
const unsigned int GAME_TICK_MSEC = 50;

const int MAX_TERRGEN_Y = CHUNK_BLOCK_Y/2;

#define SUN_LIGHT 15

const int WATER_LEVEL = 62;
const int ODDS_TOTAL = 10000;  //概率都用1/10000
const unsigned short NULL_MAPID = 65535;

//tolua_begin
typedef MINIW::int64 WORLD_ID;
typedef MINIW::int64 WORLD_SEED;
//tolua_end
class Block;
class BlockLight;
class BaseSection;
class Section;
class Chunk;
class World;
class ClientActor;
class BlockTemplate;



class TickPosition //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	void beginTick(const WCoord &curpos)
	{
		m_LastTickPos = curpos;
		m_TickOffsetTime = 0;
	}
	MINIW::WorldPos getPos(const WCoord &curpos)
	{
		return Lerp(m_LastTickPos.toWorldPos(), curpos.toWorldPos(), m_TickOffsetTime/GAME_TICK_TIME);
	}
	void update(float dtime)
	{
		m_TickOffsetTime += dtime;
		if(m_TickOffsetTime > GAME_TICK_TIME) m_TickOffsetTime = GAME_TICK_TIME;
	}
	//tolua_end
public:
	//tolua_begin
	WCoord m_LastTickPos;
	float m_TickOffsetTime;
	//tolua_end
}; //tolua_exports

inline int BlockDivSection(int x)
{
	int n = x/SECTION_BLOCK_DIM;
	return (x - n*SECTION_BLOCK_DIM < 0) ? n-1 : n;
}

inline WCoord BlockDivSection(const WCoord &pos)
{
	return WCoord(BlockDivSection(pos.x), BlockDivSection(pos.y), BlockDivSection(pos.z));
}

inline int CoordDivSection(int x)
{
	int n = x/CHUNK_SIZE_X;
	return (x - n*CHUNK_SIZE_X) < 0 ? n-1 : n;
}

inline int CoordDivBlock(int x)
{
	int n = x/BLOCK_SIZE;
	return (x - n*BLOCK_SIZE) < 0 ? n-1 : n;
}

inline WCoord CoordDivBlock(const WCoord &pos)
{
	return WCoord(CoordDivBlock(pos.x), CoordDivBlock(pos.y), CoordDivBlock(pos.z));
}

inline WCoord CoordDivSection(const WCoord &pos)
{
	return WCoord(CoordDivSection(pos.x), CoordDivSection(pos.y), CoordDivSection(pos.z));
}

class CollideAABB{ //tolua_exports
public:
	//tolua_begin
	CollideAABB() {}
	int minX() const
	{
		return pos.x;
	}
	int minY() const
	{
		return pos.y;
	}
	int minZ() const
	{
		return pos.z;
	}
	int maxX() const
	{
		return pos.x+dim.x;
	}
	int maxY() const
	{
		return pos.y+dim.y;
	}
	int maxZ() const
	{
		return pos.z+dim.z;
	}
	int centerX() const
	{
		return pos.x + dim.x/2;
	}
	int centerY() const
	{
		return pos.y + dim.y/2;
	}
	int centerZ() const
	{
		return pos.z + dim.z/2;
	}
	WCoord minPos() const
	{
		return pos;
	}
	WCoord maxPos() const
	{
		return pos+dim;
	}

	void setPoints(const WCoord &minpos, const WCoord &maxpos)
	{
		pos = minpos;
		dim = maxpos - minpos;
	}

	void setPosDim(const WCoord &p, const WCoord &d)
	{
		pos = p;
		dim = d;
	}

	void expand(int x, int y, int z)
	{
		pos.x -= x;
		pos.y -= y;
		pos.z -= z;
		dim.x += x*2;
		dim.y += y*2;
		dim.z += z*2;
	}

	void expand(const CollideAABB &rhs)
	{
		pos.x = MINIW::Min(pos.x, rhs.pos.x);
		pos.y = MINIW::Min(pos.y, rhs.pos.y);
		pos.z = MINIW::Min(pos.z, rhs.pos.z);
		dim.x = MINIW::Max(maxX(), rhs.maxX()) - pos.x;
		dim.y = MINIW::Max(maxY(), rhs.maxY()) - pos.y;
		dim.z = MINIW::Max(maxZ(), rhs.maxZ()) - pos.z;
	}

	bool intersect(const CollideAABB &box) const
	{
		if(minX()>=box.maxX() || minY()>=box.maxY() || minZ()>=box.maxZ()
			|| maxX()<=box.minX() || maxY()<=box.minY() || maxZ()<=box.minZ()) return false;
		return true;
	}

	void reset()
	{
		pos = WCoord(MINIW::MAX_INT, MINIW::MAX_INT, MINIW::MAX_INT);
		dim = WCoord(MINIW::MAX_INT, MINIW::MAX_INT, MINIW::MAX_INT);
	}

	CollideAABB getOffsetBox(int x, int y, int z)const
	{
		CollideAABB box2;
		box2.pos = pos + WCoord(x,y,z);
		box2.dim = dim;
		return box2;
	}

	CollideAABB getOffsetBox(const WCoord dp)const
	{
		CollideAABB box2;
		box2.pos = pos + dp;
		box2.dim = dim;
		return box2;
	}
	inline std::string __tostring()
	{
		char log[128];
		sprintf(log, "pos(%d, %d, %d), dim(%d, %d, %d)", pos.x, pos.y, pos.z, dim.x, dim.y, dim.z);
		return std::string(log);
	}
	//lua运算符函数
	//tolua_end
public:
	//tolua_begin
	WCoord pos;
	WCoord dim;
	//tolua_end
}; //tolua_exports
//tolua_begin
enum DirectionType
{
	DIR_NOT_INIT = -1,
	DIR_NEG_X = 0,
	DIR_POS_X,
	DIR_NEG_Z,
	DIR_POS_Z,
	DIR_NEG_Y,
	DIR_POS_Y
};
//tolua_end

inline DirectionType ReverseDirection(int dir)
{
	if(dir%2) return (DirectionType)(dir-1);
	else return (DirectionType)(dir+1);
}

inline DirectionType RotateDir90(int dir)
{
	if (dir == DIR_NEG_X)
		return DIR_NEG_Z;
	else if (dir == DIR_POS_X)
		return DIR_POS_Z;
	else if (dir == DIR_NEG_Z)
		return DIR_POS_X;
	else if (dir == DIR_POS_Z)
		return DIR_NEG_X;

	return (DirectionType)dir;
}


//上面的旋转90难道指的是逆时针?这里加个顺时针的旋转
inline DirectionType RotateDirPos90(int dir)
{
	if (dir == DIR_NEG_X)
		return DIR_POS_Z;
	else if (dir == DIR_POS_X)
		return DIR_NEG_Z;
	else if (dir == DIR_NEG_Z)
		return DIR_NEG_X;
	else if (dir == DIR_POS_Z)
		return DIR_POS_X;

	return (DirectionType)dir;
}

inline int xyz2Index(int x, int y, int z)
{
	assert(x>=0 && x<CHUNK_BLOCK_X);
	assert(y>=0 && y<CHUNK_BLOCK_Y);
	assert(z>=0 && z<CHUNK_BLOCK_Z);

	return (y<<8)|(z<<4)|x;
}


inline int xz2Index(int x, int z)
{
	assert(x>=0 && x<CHUNK_BLOCK_X);
	assert(z>=0 && z<CHUNK_BLOCK_Z);
	return (z<<4)|x;
}

inline void index2XYZ(int &x, int &y, int &z, int index)
{
	x = index&0xf;
	z = (index>>4)&0xf;
	y = (index>>8)&0xff;
}
//tolua_begin
struct ChunkIndex
{
	ChunkIndex(): x(0), z(0)
	{
	}

	ChunkIndex(int x1, int z1) : x(x1), z(z1)
	{
	}

	int x;
	int z;
};
//tolua_end

inline bool operator==(const ChunkIndex& index1, const ChunkIndex& index2)
{
	return index1.x==index2.x && index1.z==index2.z;
}

inline bool operator<(const ChunkIndex& index1, const ChunkIndex& index2)
{
	if(index1.x < index2.x) return true;
	else if(index1.x > index2.x) return false;
	else return index1.z < index2.z;
}

typedef ChunkIndex CHUNK_INDEX; //tolua_exports


/*
inline CHUNK_INDEX ChunkCoord2Index(int x, int z)
{
	ChunkIndex ci;
	ci.coord.x = (short)x;
	ci.coord.z = (short)z;

	return ci.index;
}

inline void ChunkIndex2Coord(int &x, int &z, CHUNK_INDEX index)
{
	ChunkIndex ci;
	ci.index = index;
	x = ci.coord.x;
	z = ci.coord.z;
}*/

// 指针hash
template<typename T>
class PtrHashCoder
{
public:
	MINIW::uint operator()(T* ptr) const
	{
		return (unsigned int)ptr << 2;
	}
	bool operator()(T* i, T* j)const
	{
		return i == j;
	}
};

class ChunkIndexHashCoder //tolua_exports
{ //tolua_exports
public:
	MINIW::uint operator()(const CHUNK_INDEX& i) const
	{
		return (31+(unsigned int)i.x)*31 + (unsigned int)i.z;
	}
	bool operator()(const CHUNK_INDEX& i, const CHUNK_INDEX& j)const
	{ 
		return i.x==j.x && i.z==j.z;
	}
}; //tolua_exports

struct WCoordHashCoder //tolua_exports
{ //tolua_exports
	unsigned int operator()(const WCoord &pos) const
	{
		unsigned int x = (unsigned int)pos.x;
		unsigned int y = (unsigned int)pos.y;
		unsigned int z = (unsigned int)pos.z;
		//return (x*2654435761ul + z)*2654435761ul + y;
		return ((x+31)*31 + z)*31 + y;
	}
	bool operator()(const WCoord &pos1, const WCoord &pos2) const
	{
		return pos1==pos2;
	}
}; //tolua_exports

//tolua_begin
extern WCoord g_DirectionCoord[6];

inline WCoord NeighborCoord(const WCoord &grid, int dir)
{
	return grid + g_DirectionCoord[dir];
}

inline WCoord DownCoord(const WCoord &grid)
{
	return NeighborCoord(grid, DIR_NEG_Y);
}

inline WCoord TopCoord(const WCoord &grid)
{
	return NeighborCoord(grid, DIR_POS_Y);
}

inline WCoord BlockCenterCoord(const WCoord &grid)
{
	return grid*BLOCK_SIZE + WCoord(BLOCK_SIZE/2, BLOCK_SIZE/2, BLOCK_SIZE/2);
}

inline WCoord BlockBottomCenter(const WCoord &grid)
{
	return grid*BLOCK_SIZE + WCoord(BLOCK_SIZE/2, 0, BLOCK_SIZE/2);
}
//tolua_end
//tolua_begin
enum BLOCK_MINE_TYPE
{
	BLOCK_MINE_NONE = 0, //不掉落
	BLOCK_MINE_NOTOOL,  //工具不符合  
	BLOCK_MINE_TOOLFIT,  //空手或者工具符合 coe by : keguanqiang
	BLOCK_MINE_PRECISE, //精准掉落
};
//tolua_end
//tolua_begin
enum TERRAIN_TYPE
{
	TERRAIN_FLAT = 0,
	TERRAIN_NORMAL,
	TERRAIN_LIMITSIZE,
	TERRAIN_LARGE_BIOME,
	TERRAIN_AIRISLAND,
	TERRAIN_PLANETSPACE,
	TERRAIN_PLANETSPACE_AIRLAND,
	TERRAIN_HARDSAND_FLAT = 10,
	TERRAIN_WATER_FLAT = 11,
	TERRAIN_EMPTY_FLAT = 12, //空白地形
	HOMEGARDEN_ZONE = 13,//家园地图
};
//tolua_end

class ChunkGenData //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ChunkGenData()
	{
		m_Data = new unsigned short[CHUNK_BLOCK_X*CHUNK_BLOCK_Z*MAX_TERRGEN_Y];
	}
	//tolua_end
	ChunkGenData(unsigned short *src) : m_Data(src)
	{
	}
	//tolua_begin
	~ChunkGenData()
	{
		OGRE_DELETE_ARRAY(m_Data);
	}
	//tolua_end

	unsigned short &operator[](int i)
	{
		assert(i>=0 && i<CHUNK_BLOCK_X*CHUNK_BLOCK_Z*MAX_TERRGEN_Y);
		return m_Data[i];
	}

	unsigned short *getData()
	{
		return m_Data;
	}

	unsigned short *requireData()
	{
		unsigned short *ptmp = m_Data;
		m_Data = NULL;
		return ptmp;
	}
	
private:
	unsigned short *m_Data;
}; //tolua_exports

inline void NumTiles2StartEnd(int &s, int &e, int ntiles)
{
	s = -ntiles / 2;
	e = (ntiles - 1) / 2;
}

extern int GetGameVersionInt();
extern const char *GetGameVersionStr();
// 同步briefinfo
enum BRIEF_INFO_SYNC_TYPE {
	BIS_MAP			= 1,
	BIS_POSITION	= 1 << 1,
	BIS_HP			= 1 << 2,
	BIS_STRENGTH	= 1 << 3,
	BIS_TEAM_ID		= 1 << 4,
	BIS_VIP			= 1 << 5,
	BIS_CGVARS		= 1 << 6,
	BIS_INSPECTATOR	= 1 << 7,
	BIS_SKIN_ID		= 1 << 8,
	BIS_NICK_NAME	= 1 << 9,
	BIS_FRAME_ID	= 1 << 10,
	BIS_PLAYER_INDEX= 1 << 11,
	BIS_CUSTOM_JSON	= 1 << 12,
	BIS_HP_MAX		= 1 << 13,
	BIS_HP_OVERFLOW	= 1 << 14,
	BIS_STRENGTH_MAX= 1 << 15,
	BIS_STRENGTH_OF	= 1 << 16,
	BIS_ALL = 0xffffffff
};

#endif