#include "WaterEffect.h"

#include "ClientActor.h"
#include "ActorLocoMotion.h"

#include "PhysicsLocoMotion.h"
#include "OgrePhysXManager.h"
#include "ClientActorFuncWrapper.h"
#include "FireBurnComponent.h"

using namespace MNSandbox;


void WaterEffect::executeEffect(ClientActor* pActor)
{
	if (!pActor)
	{
		return;
	}

	ActorLocoMotion* locomotion = pActor->getLocoMotion();
	assert(locomotion != nullptr);
	if (!locomotion)
	{
		return;
	}

	Rainbow::Vector3f flowmotion = GetFlowmotion();// context.GetData_UserObject<Vector3f>("flowmotion");
	bool status = GetStatus();//;context.GetData_Bool("status", false);
	if (status) {
		locomotion->m_Motion += flowmotion;
		auto functionWrapper = pActor->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(0);
		}
		auto FireBurnComp = pActor->getFireBurnComponent();
		if (FireBurnComp)
		{
			FireBurnComp->setFire(0, 0);
		}

	}
}

void PhysicsWaterEffect::executeEffect(ClientActor* pActor)
{
	if (!pActor)
	{
		return;
	}
	WaterEffect::executeEffect(pActor);

	bool status = GetStatus();//context.GetData_Bool("status", false);
	if (status) {
		ActorLocoMotion* locomotion = pActor->getLocoMotion();
		
		auto phylocomotion = dynamic_cast<PhysicsLocoMotion*>(locomotion);
		if (phylocomotion && phylocomotion->m_PhysActor){
			Rainbow::Vector3f flowmotion = GetFlowmotion();// context.GetData_UserObject<Vector3f>("flowmotion");
			Rainbow::Vector3f motion = flowmotion * 200.0f;
			phylocomotion->m_PhysActor->SetLinearVelocity(motion);
		}
	}
}