
#ifndef __ACTORVISION_H__
#define __ACTORVISION_H__

#include "vector_nofree.h"
#include "SandboxGame.h"
#include "ActorComponent_Base.h"
class ClientActor;
class EXPORT_SANDBOXGAME ActorVision;
class ActorVision : public ActorComponentBase //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(ActorVision)
public:

	//tolua_begin
	ActorVision();
	~ActorVision();

	//void tick();
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	void setTargetPos(const WCoord &pos)
	{
		m_TargetPos = pos;
	}
	const WCoord &getTargetPos()
	{
		return m_TargetPos;
	}

	bool canSee(ClientActor *pActor);
	bool canSeeInAICache(ClientActor *pActor);
	void clearAICanSeeCache()
	{
		m_AISeenCache.clear();
		m_AIUnSeenCache.clear();
	}
	//tolua_end
private:
	ClientActor *m_Owner;
	WCoord m_TargetPos;

	VectorNoFree<ClientActor*> m_AISeenCache;
	VectorNoFree<ClientActor*> m_AIUnSeenCache;
}; //tolua_exports
#endif
