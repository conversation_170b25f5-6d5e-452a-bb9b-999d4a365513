
#include "ClientMob.h"
#include "ClientActorManager.h"
#include "world.h"
#include "container_dragoncup.h"
#include "DefManagerProxy.h"
#include "EffectParticle.h"
#include "EffectManager.h"
#include "special_blockid.h"
#include "WorldManager.h"

using namespace MNSandbox;

//半径
const static int DISTANCE_RADIUS = 32;

ContainerDragonCup::ContainerDragonCup() : m_CheckTicks(0), m_FX(NULL), m_CurActive(false)
{
	m_NeedTick = true;
}

ContainerDragonCup::ContainerDragonCup(const WCoord &blockpos) : WorldContainer(blockpos, 0), m_CheckTicks(0), m_FX(NULL), m_CurActive(false)
{
	m_NeedTick = true;
}

ContainerDragonCup::~ContainerDragonCup()
{
	m_Callback = NULL;
}

int ContainerDragonCup::getObjType() const
{
	return OBJ_TYPE_VALUE;
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerDragonCup::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerDragonCup(builder, basedata, m_CurActive?1:0);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerDragonCup, actor.Union());
}

bool ContainerDragonCup::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerDragonCup *>(srcdata);
	loadContainerCommon(src->basedata());

	m_CurActive = src->active()!=0;

	return true;
}

void ContainerDragonCup::updateTick()
{
	if(m_CheckTicks == 0)
	{
		bool isactive = checkActive();
		if(isactive != m_CurActive)
		{
			if(isactive)
			{
				GetWorldManagerPtr()->m_DragonStatuePoint = m_BlockPos;
				if(m_FX == NULL) createFX(m_World);
			}
			else
			{
				GetWorldManagerPtr()->m_DragonStatuePoint = WCoord(0,-1,0);
				if(m_FX)
				{
					m_FX->setNeedClear();
					m_FX = NULL;
				}
			}

			m_CurActive = isactive;
		}
	}

	m_CheckTicks++;
	if(m_CheckTicks >= 20) m_CheckTicks = 0;
}

bool ContainerDragonCup::checkActive()
{
	const int W = 2;
	int count = 0;
	for(int y=m_BlockPos.y-W; y<=m_BlockPos.y+W; y++)
	{
		for(int x=m_BlockPos.x-W; x<=m_BlockPos.x+W; x++)
		{
			for(int z=m_BlockPos.z-W; z<=m_BlockPos.z+W; z++)
			{
				WCoord pos(x,y,z);
				if(pos!=m_BlockPos && m_World->getBlockID(pos)==BLOCK_EMERALDBLOCK)
				{
					count++;
				}
			}
		}
	}

	return count>=9;
}

void ContainerDragonCup::onAttachUI()
{

}

void ContainerDragonCup::onDetachUI()
{

}

void ContainerDragonCup::createFX(World *pworld)
{
	if(pworld->onClient())
	{
		WCoord pos = BlockBottomCenter(m_BlockPos);
		m_FX = ENG_NEW(EffectParticle)(pworld, "particles/chaosstatue.ent", pos, 0);

		pworld->getEffectMgr()->addEffect(m_FX);
	}
}

void ContainerDragonCup::enterWorld(World *pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();

	if(m_CurActive) createFX(pworld);

	m_Callback = pworld->Event().SubscribeEventWithCreateEvent("IsNearDragonCup", this, (SandboxClassCallback)&ContainerDragonCup::isNearRevivalStatue);
}

void ContainerDragonCup::leaveWorld()
{
	if (m_World != NULL && m_Callback.IsValid())
	{
		m_World->Event().Unsubscribe("IsNearDragonCup", m_Callback);
	}
	
	if(m_FX) m_FX->setNeedClear();

	WorldContainer::leaveWorld();
}

SandboxResult SANDBOXAPI ContainerDragonCup::isNearRevivalStatue(SandboxContext context)
{
	if (m_World == NULL)
		return SandboxResult(this, false);

	//是否要判断黑龙还是混乱雕像
	//int blockID = m_World->getBlockID(m_BlockPos);

	WCoord revivalStatuePoint = context.GetData_UserObject<WCoord>("revival_statue_point");
	int xDistance = m_BlockPos.x - revivalStatuePoint.x;
	//先不管y
	//int yDistance = m_BlockPos.y - revivalStatuePoint.y;
	int zDistance = m_BlockPos.z - revivalStatuePoint.z;
	if (xDistance < DISTANCE_RADIUS && zDistance < DISTANCE_RADIUS)
	{
		SandboxResult ret(this, true);
		//如果半径内存在就把位置传递过去，只要存在即可，不管存在几个
		ret.SetData_UserObject("dragoncup_point", m_BlockPos);
		return ret;
	}
	return SandboxResult(this, false);
}
