#pragma warning( disable : 4482 )
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "AttribTypes.h"
#include "ActorAttrib.h"
//#include "GameEvent.h"
#include "ClientActorLiving.h"

#include "defdata.h"
#include "VillagerAttrib.h"
#include "ActorHorse.h"
#include "container_backpack.h"

#include "ActorVillager.h"
#include "ObserverEvent.h"
#include "ObserverEventManager.h"
#include "LuaInterfaceProxy.h"
#include "ActionAttrStateComponent.h"
#include "CarryComponent.h"
#include "DieComponent.h"
#include "TriggerComponent.h"
#include "SoundComponent.h"
#include "AttackedComponent.h"
#include "TemperatureComponent.h"
#include "SandboxIdDef.h"

IMPLEMENT_COMPONENTCLASS(VillagerAttrib)

VillagerAttrib::VillagerAttrib() : m_iMaxExtremisVal(0), m_iMaxFavor(0)
{
	m_iHunger = 0;
	m_iHungerstatus = 0;
	m_iDisposition = 0;
	m_iFavor = 0;
	m_speedOfBreakingBlock = 0;
	m_iDecayExtremisTick = 0;
	m_iWakeupTime = 6 * TICKS_ONEDAY / 24;
	m_iSleepTime = 18 * TICKS_ONEDAY / 24;
	m_iStayupTime = 0;
	m_iFatigue = 0;
	m_bStayingUp = false;
	m_iExtremisVal = -1;
}

void VillagerAttrib::init(const MonsterDef *def)
{
	MobAttrib::init(def);

	m_iFavor = GetLuaInterfaceProxy().get_lua_const()->actor_init_favor;
	m_iMaxFavor = GetLuaInterfaceProxy().get_lua_const()->actor_max_favor;
	m_iMaxExtremisVal = GetLuaInterfaceProxy().get_lua_const()->actor_max_extremisval;
}

void VillagerAttrib::tick()
{
	MobAttrib::tick();

	auto comp = m_OwnerActor->getTemperatureComponent();
	if (comp)
	{
		LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(m_OwnerActor->getAttrib());
		if (attrib->hasBuff(1034))
			return;
	}

	int num = 0;
	if (GetLuaInterfaceProxy().get_lua_const()->actor_max_hugger != 0)
	{
		num = m_iHunger / GetLuaInterfaceProxy().get_lua_const()->actor_max_hugger;
	}
	if (num > 0)
	{
		m_Food -= num * GetLuaInterfaceProxy().get_lua_const()->consume_food_by_hugger;
		m_iHunger -= (num * GetLuaInterfaceProxy().get_lua_const()->actor_max_hugger);
		if (m_Food < 0)
			m_Food = 0;
	}

	auto CarryComp = m_OwnerActor->getCarryComponent();
	if (isExtremis() && !(CarryComp && CarryComp->isCarried()))
	{
		m_iDecayExtremisTick++;
		if (m_iDecayExtremisTick == 20)
		{
			m_iDecayExtremisTick = 0;
			addExtremisVal(0 - GetLuaInterfaceProxy().get_lua_const()->decay_extremis);
			if (m_iExtremisVal <= 0)
			{
				auto DieComp = m_OwnerActor->SureDieComponent();
				if (DieComp) DieComp->onDie();
				ActorVillager *villager = dynamic_cast<ActorVillager *>(m_OwnerActor);
				if (villager)
				{
					villager->generateKeepsakePackage(false);
				}
			}
		}
	}
}

void VillagerAttrib::addHP(float hp, bool overflowable)
{
	if (isExtremis() && hp > 0) //进入濒死状态 不能加血
		return;

	bool islive = m_Life > 0 || m_iExtremisVal > 0;
	float limitHp = overflowable ? getLimitHP() : getMaxHP();

	if (m_Life <= 0 && hp < 0 && m_iExtremisVal > 0)  //没有血条后，才会扣濒死值
	{
		addExtremisVal((short)hp);
	}
	else
	{
		m_Life += hp;

		auto ActionAttrStateComp = m_OwnerActor->getActionAttrStateComponent();
		if (islive && m_Life <= 0 && !(ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_BEKILLED)))
			m_Life = 1;

		if (m_Life > limitHp) m_Life = limitHp;
		if (m_Life < 0)
			m_Life = 0;

		if (m_Life == 0 && m_iExtremisVal > 0)
			addExtremisVal(-1);  //进入濒死状态
	}

	if (islive && m_Life == 0 && m_iExtremisVal <= 0 && m_OwnerActor != NULL)
	{
		auto attackedComponent = m_OwnerActor->getAttackedComponent();
		if (attackedComponent)
		{
			attackedComponent->recordAttacker(2);
		}
		auto DieComp = m_OwnerActor->SureDieComponent();
		if (DieComp) DieComp->onDie();
		ActorVillager *villager = dynamic_cast<ActorVillager *>(m_OwnerActor);
		if (villager)
		{
			villager->generateKeepsakePackage(false);
		}
	}

	if (hp < 0 && m_OwnerActor)
	{
		int val = 0;
		MINIW::ScriptVM::game()->callFunction("AIFunctionDefs_GetHungryReduceByType", "s>i", "beHurt", &val);
		addHugger(val);
		auto triggerComponent = m_OwnerActor->getTriggerComponent();
		if (triggerComponent)
		{
			triggerComponent->beHurtOnTrigger(hp);
		}
		auto attackedComponent = m_OwnerActor->getAttackedComponent();
		if (attackedComponent)
		{
			attackedComponent->recordAttacker(1);
		}
	}

	if (m_OwnerActor)
	{
		m_OwnerActor->addHPEffect(hp);
	}
}

void VillagerAttrib::addHugger(short val)
{
	m_iHunger += val;
}

void VillagerAttrib::setHugger(short val)
{
	m_iHunger = val;
}

void VillagerAttrib::addExtremisVal(short val)
{
	int oldVal = m_iExtremisVal;
	int oldStage = getExtremisStage();

	m_iExtremisVal += val;
	if (m_iExtremisVal < 0)
		m_iExtremisVal = 0;
	if (m_iExtremisVal > m_iMaxExtremisVal)
		m_iExtremisVal = m_iMaxExtremisVal;

	if (oldVal == -1)
		return;

	if (oldVal < m_iMaxExtremisVal && m_iExtremisVal >= m_iMaxExtremisVal)
	{
		onChangeExtremisState(false);
	}
	else if (isExtremis() && oldStage != getExtremisStage())
	{
		onChangeExtremisState(true, oldStage);

	}
	/*else if (oldVal >= m_iMaxExtremisVal && m_iExtremisVal < m_iMaxExtremisVal)
	{
		onChangeExtremisState(true);
	}*/
}

void VillagerAttrib::setExtremisVal(short val)
{
	int oldVal = m_iExtremisVal;
	int oldStage = getExtremisStage();

	m_iExtremisVal = val;
	if (m_iExtremisVal < 0)
		m_iExtremisVal = 0;
	if (m_iExtremisVal > m_iMaxExtremisVal)
		m_iExtremisVal = m_iMaxExtremisVal;

	if (oldVal == -1)
		return;

	if (oldVal < m_iMaxExtremisVal && m_iExtremisVal >= m_iMaxExtremisVal)
	{
		onChangeExtremisState(false);
	}
	else if (isExtremis() && oldStage != getExtremisStage())
	{
		onChangeExtremisState(true, oldStage);

	}
	/*else if (oldVal >= m_iMaxExtremisVal && m_iExtremisVal < m_iMaxExtremisVal)
	{
		onChangeExtremisState(true);
	}*/
}

bool VillagerAttrib::isDead()
{
	if (m_Life < 0.0f)  //ClientActor::kill() getAttrib()->setHpForTrigger(-1.0f); -1.0是kill掉Mob的  村民没血是0
	{
		return true;
	}
	bool a = m_Life <= 0 && m_iExtremisVal <= 0;
	return a;
}

void VillagerAttrib::onChangeExtremisState(bool isextremis, int oldstage/* =0 */)
{
	int faceId = 1;

	ActorVillager *villager = dynamic_cast<ActorVillager *>(m_OwnerActor);
	if (isextremis)
	{
		m_OwnerActor->getBody()->setHeadDisplayIcon(ITEM_BANDAGE);
		faceId = 5;
		if (villager)
		{
			if (villager->m_Def)
			{
				char sound[20] = { 0 };
				sprintf(sound, "ent.%d.dying%d", villager->m_Def->ID, getExtremisStage());
				auto soundComp = m_OwnerActor->getSoundComponent();
				if (soundComp)
				{
					soundComp->playSound(sound, 1.0f, 1.0f);
				}
			}

			if (oldstage == 0)
			{
				villager->ResetBTree();
				if (g_pPlayerCtrl)
					villager->statisticEvent(31003, g_pPlayerCtrl->getUin());
			}
		}
	}
	else
	{
		m_OwnerActor->getBody()->setHeadDisplayIcon(0);
		faceId = 1;
	}

	if (villager)
	{
		villager->setFaceId(faceId);
		villager->checkExtremisForBed(isextremis);
	}
}

int VillagerAttrib::getExtremisStage()
{
	int stageVal = m_iMaxExtremisVal / 3;
	if (m_iExtremisVal <= stageVal)
	{
		return 3;
	}
	else if (m_iExtremisVal <= 2 * stageVal)
	{
		return 2;
	}
	else if (m_iExtremisVal < m_iMaxExtremisVal)
	{
		return 1;
	}

	return 0;
}

void VillagerAttrib::damageArmor(float points, ClientActor *attacker)
{
	for (int i = 0; i < EQUIP_WEAPON; i++)
	{
		int id = damageEquipItem((EQUIP_SLOT_TYPE)i, (int)points);
		if (id > 0 && attacker)
		{
			// 道具被破坏
			ObserverEvent_ActorItem obevent(attacker->getObjId(), id, 1);
			GetObserverEventManager().OnTriggerEvent("Item.Destroy", &obevent);
		}
	}
}