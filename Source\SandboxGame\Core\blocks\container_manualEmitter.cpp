#include "container_manualEmitter.h"
#include "DefManagerProxy.h"
#include "world.h"
#include "OgreEntity.h"
#include "BlockScene.h"
#include "BlockMaterialMgr.h"
#include "BlockManualEmitter.h"
#include "IClientPlayer.h"
#include "ActorBody.h"
#include "OgreEntity.h"
#include "ClientItem.h"
#include "ActorLocoMotion.h"
#include "LuaInterfaceProxy.h"
#include "IPlayerControl.h"
#include "ClientInfoProxy.h"
#include "SandboxCoreDriver.h"
#include "ActorCSProto.h"
#include "BlockBed.h"
#include "GameCamera.h"
#include "EffectManager.h"
#include "SandboxIdDef.h"
#include "WorldManager.h"
#include "special_blockid.h"
#include "LuaInterfaceProxy.h"
#include "ConstAtLua.h"
#include "SandboxResult.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "ActorManager.h"
 
using namespace MNSandbox;

ContainerManualEmitter::ContainerManualEmitter():WorldContainer(WCoord(0, 0, 0), MANUAL_EMITTER_START_INDEX), m_rotateAngle(0, 0, 0)
{
	m_pEntity = nullptr;
	m_curAnimId = -1;
	//m_player = nullptr;
	auto lua = GetLuaInterfaceProxy().get_lua_const();
	if (lua)
	{
		m_totalTick = lua->manualEmitterInterval;
	}
	else
	{
		m_totalTick = 200;
	}
	m_useTick = MIN_INTERVAL;
	MANUAL_EMITTER_START_INDEX;
	for (int i = 0; i < MANUALEMITTER_MAX; i++)
	{
		m_Grids[i].setIndex(MANUAL_EMITTER_START_INDEX + i);
	}
	CreateEvent2();
	m_NeedTick = true;
}

ContainerManualEmitter::ContainerManualEmitter(WCoord BlockPos, int dir)
	:WorldContainer(BlockPos, MANUAL_EMITTER_START_INDEX), m_dir(dir), m_rotateAngle(0, 0, 0)
{
	m_pEntity = nullptr;
	m_curAnimId = -1;
	//m_player = nullptr;
	int angle = 0;
	if (dir == DIR_NEG_X)
	{
		angle = 90;
	}
	else if (dir == DIR_POS_X)
	{
		angle = 270;
	}
	else if (dir == DIR_NEG_Z)
	{

	}
	else if (dir == DIR_POS_Z)
	{
		angle = 180;
	}
	m_rotateAngle.y = angle;
	m_rotate = Rainbow::EulerToQuaternionf(m_rotateAngle);
	auto lua = GetLuaInterfaceProxy().get_lua_const();
	if (lua)
	{
		m_totalTick = lua->manualEmitterInterval;
	}
	else
	{
		m_totalTick = 200;
	}
	m_useTick = MIN_INTERVAL;
	for (int i = 0; i < MANUALEMITTER_MAX; i++)
	{
		m_Grids[i].setIndex(MANUAL_EMITTER_START_INDEX + i);
	}
	CreateEvent2();
	m_NeedTick = true;
}

void ContainerManualEmitter::CreateEvent2()
{
	typedef ListenerFunctionRef<IClientPlayer*> Listener1;
	m_listenerEmitterWorld1 = SANDBOX_NEW(Listener1, [&](IClientPlayer* player) -> void
		{
			static_cast<ClientPlayer*>(player)->setPosition(this->getCurEmitterGetDownPos());
		});
	Event2().Subscribe("EmitterWorld_setPosition", m_listenerEmitterWorld1);

}
void ContainerManualEmitter::DestroyEvent2()
{
	SANDBOX_RELEASE(m_listenerEmitterWorld1);
}

void ContainerManualEmitter::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	registerUpdateDisplay();

	WCoord pos = m_BlockPos * BLOCK_SIZE;
	{
		int addx = 0;
		int addz = 0;
		BlockManualEmitter::getStepXZ(m_dir, addx, addz);
		addx += 1;
		addz += 1;
		pos += WCoord(BLOCK_SIZE * 0.5 * addx, 0.0f, BLOCK_SIZE * 0.5 * addz);
	}
#ifndef IWORLD_SERVER_BUILD	
	//int blockdata = pworld->getBlockData(m_BlockPos);
	int dir = m_dir;
	/*const ItemDef* def = GetDefManagerProxy()->getItemDef(ITEM_BELLS);
	if (def == NULL)
	{
		return;
	}*/
	char path[100] = { 0 };
	//直接给模型
	sprintf(path, "itemmods/%d/body.omod", 2002);

	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
		Rainbow::Entity::Destory(m_pEntity);
	}

	m_pEntity = Rainbow::Entity::Create();

#if ENTITY_MODIFY_MODEL_ASYNC
	m_pEntity->LoadAsync(path, true);
#else
	Rainbow::Model* model = NULL;
	model = g_BlockMtlMgr.getModel(path);
	if (!model) return;
	m_pEntity->Load(model);
#endif

	m_pEntity->ShowSkins(true);
	auto& angle = m_rotateAngle;
	m_pEntity->SetRotation(angle.y, angle.x, angle.z);
	m_pEntity->SetPosition(pos.toWorldPos());

	if (m_World)
		m_pEntity->AttachToScene(pworld->getScene());

#endif
	m_centerPos = pos;
	//if (m_player == NULL && m_uin > 0 && m_World->getActorMgr())
	//{
	//	IClientPlayer* player = m_World->getActorMgr()->findPlayerByUin(m_uin);
	//	if (player)
	//	{
	//		attachPlayer(player);
	//	}
	//}
	playAnim(100100, -1);
}
void ContainerManualEmitter::leaveWorld()
{
	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
	}
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(getPlayer());
	if (player && player->getUsingEmitter())
	{
		player->disMountEmitter(m_BlockPos);
	}
	WorldContainer::leaveWorld();
}

ContainerManualEmitter::~ContainerManualEmitter()
{
	Rainbow::Entity::Destory(m_pEntity);
	DestroyEvent2();
}

void ContainerManualEmitter::updateTick()
{
	auto player = dynamic_cast<ClientPlayer*>(getPlayer());
	//m_World;
	if (player)
	{
		m_rotateAngle.x = player->getFacePitch();
		m_rotateAngle.y = player->getFaceYaw();
		//m_rotateAngle = Rainbow::QuaternionToEulerAngle(m_rotate);
		/*if (player->getBody()->isLerpRotation())
		{
			m_rotateAngle.y = player->getBody()->getTargetYaw();
		}
		else*/
		/*{
			m_rotateAngle.y = player->getBody()->getRenderYawOffset();
		}*/
		m_rotateAngle.z = 0;
		if (abs(m_rotateAngle.x) > 15)
		{
			m_rotateAngle.x = 15 * (m_rotateAngle.x < 0 ? -1 : 1);
		}
		m_rotateAngle.x = -m_rotateAngle.x;
		m_rotate = Rainbow::AngleEulerToQuaternionf(m_rotateAngle);
		if (m_pEntity)
		{
			m_pEntity->SetRotation(m_rotateAngle.y, m_rotateAngle.x, m_rotateAngle.z);
		}
	}
	if (m_useTick > MIN_INTERVAL)
	{
		m_useTick -= MNSandbox::g_TickElapse * 1000;
		if (m_useTick <= MIN_INTERVAL)
		{
			m_useTick = 0;
			playAnim(100, -1);
		}
		if (player && player->hasUIControl())
		{
			//GetGameEventQue().postManualEmitterUpdateUI(2, m_useTick, m_totalTick);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("type", 2).
				SetData_Number("time", m_useTick).
				SetData_Number("totalTime", m_totalTick);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_MANUAL_EMITTER_UPDATE", sandboxContext);
		}
	}
}

bool ContainerManualEmitter::getCurEmitterShootPos(IClientPlayer* player, WCoord& retPos, Rainbow::Vector3f& motion) const
{
	//if (m_player && m_pEntity && m_player->getBody() && m_player->getBody()->getEntity())
	//{
		auto qu = m_rotate;
		Rainbow::Matrix3x3f matrix;
		QuaternionfToMatrix(m_rotate, matrix);
		Rainbow::Vector3f vec(0, 0, -1);
		vec = matrix.MultiplyVector3(vec);
		retPos = vec * (BLOCK_SIZE * 1.9f) + m_centerPos;
		retPos.y += BLOCK_SIZE * 1.5;
		motion = vec;
		//return true;
	//}
	//return false;
	//if (!player)
	//{
	//	return false;
	//}
	//WCoord pos = WCoord(player->getEyePosition());
	////pos += offset;
	//// 彩蛋枪子弹
	////if (itemId == 12249)
	////{
	////	pos = WCoord(shootactor->getChestPosition());
	////}
	////IClientPlayer* player = dynamic_cast<IClientPlayer*>(shootactor);
	//IPlayerControl* player_control = dynamic_cast<IPlayerControl*>(player);
	////ClientMob* mob = dynamic_cast<ClientMob*>(shootactor);
	//int usetarget = 0;
	//ItemDef* itemdef = NULL;
	////if (player)
	////{
	////	itemdef = GetDefManagerProxy()->getItemDef(player->getCurToolID());
	////	if (itemdef)
	////	{
	////		usetarget = itemdef->UseTarget;
	////	}
	////}

	//ActorLocoMotion* shootloc = player->getLocoMotion();
	//float yaw;
	//float pitch;
	//yaw = shootloc->m_RotateYaw;
	//pitch = shootloc->m_RotationPitch;

	//// 玩家射击/投射
	//if (player)
	//{
	//	// 客机
	//	if ((player_control != g_pPlayerCtrl) || (player_control == NULL))
	//	{
	//		// 如果是客机，从GunLogic获取客机同步过来的精确位置和朝向
	//		Rainbow::Vector3f tmp;
	//		if (player->getGunLogical()) player->getGunLogical()->getDir(yaw, pitch, tmp);
	//		pos = tmp;
	//	}
	//	// 主机
	//	else if (g_pPlayerCtrl)
	//	{
	//		// 枪械外的射击武器
	//		//if (usetarget != ITEM_USE_GUN)
	//		{
	//			pos = g_pPlayerCtrl->getCamera()->getEyePos();
	//			Rainbow::Vector3f camlookdir = g_pPlayerCtrl->getCamera()->getLookDir();
	//			auto PlayerRidComp = g_pPlayerCtrl->getRiddenComponent();
	//			//IClientActor* riding = NULL;
	//			/*if (PlayerRidComp)
	//			{
	//				riding = PlayerRidComp->getRidingActor();
	//			}*/
	//			//Rainbow::Quaternionf quat;
	//			//if (riding)
	//			//{
	//			//	auto ridingComp = riding->getRiddenComponent();
	//			//	if (ridingComp && ridingComp->getRiddenBindRot(g_pPlayerCtrl, quat))
	//			//	{
	//			//		//quat.rotate(camlookdir, camlookdir);
	//			//		camlookdir = RotateVectorByQuat(quat, camlookdir);
	//			//	}
	//			//}

	//			CameraControlMode viewmode = g_pPlayerCtrl->getCamera()->getMode();
	//			if (viewmode == CAMERA_TPS_BACK || viewmode == CAMERA_TPS_BACK_2)
	//			{
	//				Vector2f deltaXZ(
	//					(float)pos.x - g_pPlayerCtrl->getPosition().x,
	//					(float)pos.z - g_pPlayerCtrl->getPosition().z);
	//				if (fabs(camlookdir.y) > fabs(camlookdir.x) && fabs(camlookdir.y) > fabs(camlookdir.z))
	//				{
	//					Rainbow::Vector3f sDir = g_pPlayerCtrl->getLookDir();
	//					WCoord ssDir = WCoord(sDir.x * BLOCK_SIZE, BLOCK_SIZE, sDir.z * BLOCK_SIZE);
	//					pos = player->getPosition() + ssDir;
	//				}
	//				else
	//				{
	//					pos += camlookdir * deltaXZ.Length() * 1.1f;
	//				}
	//			}
	//			else if (viewmode == CAMERA_FPS)
	//			{
	//				pos += BLOCK_SIZE * camlookdir;
	//			}
	//			else if (viewmode == CAMERA_TPS_OVERLOOK)
	//			{
	//				pos = player->getPosition() + WCoord(0, BLOCK_SIZE / 2, 0);
	//			}
	//			else if (viewmode == CAMERA_CUSTOM_VIEW)
	//			{
	//				pos = WCoord(player->getEyePosition());
	//			}
	//		}

	//		// 自定义视角，以屏幕中心的位置发射（因为主机是不知客机的当前视角设置的，所以暂时以主机玩家的视角作为参考判断）
	//		// 背视角2，都是以屏幕中心发射。若要特殊处理在此加特例判断
	//		if ((g_pPlayerCtrl->getCamera()->getMode() == CAMERA_CUSTOM_VIEW
	//			&& g_pPlayerCtrl->getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_SCREEN_CENTER) ||
	//			g_pPlayerCtrl->getCamera()->getMode() == CAMERA_TPS_BACK_2)
	//		{
	//			// 主机直接使用摄像机位置发射
	//			//pos = player_control->m_pCamera->getPosition();
	//			pitch = player_control->m_pCamera->m_RotatePitch;
	//			yaw = player_control->m_pCamera->m_RotateYaw - 180.f;
	//		}
	//	}

	//	if (m_World->IsUGCEditMode() && !g_pPlayerCtrl->isSightMode())
	//	{
	//		//新编辑场景的鼠标模式, 朝鼠标位置射击
	//		MINIW::WorldRay ray;
	//		ray.m_Origin = Rainbow::WorldPos(0, 0, 0);
	//		ray.m_Range = 0;

	//		g_pPlayerCtrl->m_pCamera->getViewRayByScreenPt(&ray, g_pPlayerCtrl->m_CurMouseX, g_pPlayerCtrl->m_CurMouseY);
	//		ray.m_Range = 1;

	//		auto tmpOrigin = g_pPlayerCtrl->getEyePosition();
	//		ray.m_Origin = tmpOrigin.toWorldPos();
	//		Direction2PitchYaw(&yaw, &pitch, ray.m_Dir);
	//	}

	//	// 枪械射击的随机偏移
	///*	if (usetarget == ITEM_USE_GUN
	//		&& !player->getGunLogical()->isFirstShoot())
	//	{
	//		float ratio = 0.15f;
	//		Vector2f randomVec2 = RandomUnitVector2() * ratio * player->getGunLogical()->getGunSpread() * GenRandomFloat();
	//		yaw += randomVec2.x;
	//		pitch += randomVec2.y;
	//	}*/

	//	//player->attackOnTrigger();
	//}
	//Rainbow::Matrix3x3f matrix;
	//matrix.SetAxisAngle(Rainbow::Vector3f::yAxis, Rainbow::Radians(yaw));
	//matrix *= Rainbow::Matrix3x3f().SetAxisAngle(Rainbow::Vector3f::xAxis, Rainbow::Radians(-pitch));
	//Rainbow::Vector3f vec(0, 0, -1);
	//vec = matrix.MultiplyVector3(vec);
	//motion = vec;
	//retPos = pos;
	return true;
}

WCoord ContainerManualEmitter::getCurEmitterGetDownPos() const
{
	WCoord retPos;
	/*Rainbow::Matrix3x3f matrix;
	QuaternionfToMatrix(m_rotate, matrix);
	Rainbow::Vector3f vec(0, 0, 1);
	vec = matrix.MultiplyVector3(vec);
	retPos = vec * (BLOCK_SIZE * 1.9f) + m_centerPos;
	retPos.y = m_BlockPos.y * BLOCK_SIZE;*/
	bool succeed = BedLogicHandle::getNearestEmptyChunkCoordinates(retPos, m_World, m_BlockPos, 0);

	if (!succeed)
	{
		retPos = m_BlockPos;
		retPos.y += 2;
	}
	retPos = BlockBottomCenter(retPos);
	return retPos;
}

void ContainerManualEmitter::updateDisplay(float dtime)
{
	// 补光照
	if (m_pEntity)
	{
		Rainbow::Vector4f lightparam(0, 0, 0, 0);
		auto wp = m_pEntity->GetWorldPosition();
		if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(wp));
		lightparam.x += 0.2f;
		lightparam.y += 0.2f;
		m_pEntity->SetInstanceData(lightparam);
		unsigned int dtick = Rainbow::TimeToTick(dtime);
		m_pEntity->Tick(dtick);
	}
}

void ContainerManualEmitter::playAnim(int animId, int times)
{
	if (m_pEntity)
	{
		if (m_curAnimId != animId)
		{
			m_curAnimId = animId;
			m_pEntity->StopAnim();
			m_pEntity->PlayAnim(animId, times);
		}
	}
}

void ContainerManualEmitter::stopAnim()
{
	if (m_pEntity)
	{
		m_pEntity->StopAnim();
	}
}

bool ContainerManualEmitter::hasAnimPlaying(int anim)
{
	if (m_pEntity)
	{
		return m_pEntity->HasAnimPlaying(anim);
	}
	return false;
}

void ContainerManualEmitter::playSound(World* pworld, const char* name)
{
	//pworld->getEffectMgr()->playPosSound(m_BlockPos, name, 1.0, 1.0, false, false);
}

bool ContainerManualEmitter::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerManualEmitter*>(srcdata);
	if (!src->basedata() || !loadContainerCommon(src->basedata()))
	{
		return false;
	}
	if (src->items())
	{
		int size = src->items()->size();
		if (size > 0 && size <= MANUALEMITTER_MAX)
		{
			for (size_t i = 0; i < size; i++)
			{
				m_Grids[i].load(src->items()->Get(i));
			}
		}
	}
	if (src->rotate())
	{
		auto rotate = src->rotate();
		m_rotate.x = rotate->x();
		m_rotate.y = rotate->y();
		m_rotate.z = rotate->z();
		m_rotate.w = rotate->w();
		m_rotateAngle = Rainbow::QuaternionToEulerAngle(m_rotate);
	}
	//m_uin = src->playerUin();
	m_dir = src->dir();
	//m_useTick = src->curTick();
	if (m_World && (g_pPlayerCtrl != NULL && g_pPlayerCtrl->hasUIControl()))
	{
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("type", 1).
			SetData_Number("time", 0).
			SetData_Number("totalTime", 0);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_MANUAL_EMITTER_UPDATE", sandboxContext);
	}
	//if (m_World && m_uin > 0 && m_World->getActorMgr())
	//{
	//	m_player = m_World->getActorMgr()->findPlayerByUin(m_uin);
	//}
	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerManualEmitter::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);
	flatbuffers::Offset<FBSave::ItemGrid> items[MANUALEMITTER_MAX];
	for (int i = 0; i < MANUALEMITTER_MAX; i++)
	{
		items[i] = m_Grids[i].save(builder);
	}
	auto actor = FBSave::CreateContainerManualEmitter(builder, basedata, m_uin, builder.CreateVector(items, MANUALEMITTER_MAX), m_dir,
		FBSave::CreateContainerQuation(builder, m_rotate.x, m_rotate.y, m_rotate.z, m_rotate.w), m_useTick);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerManualEmitter, actor.Union());
}


BackPackGrid* ContainerManualEmitter::index2Grid(int index)
{
	assert(index >= m_BaseIndex && index < (int)(m_BaseIndex + MANUALEMITTER_MAX));

	return &m_Grids[index - m_BaseIndex];
}

void ContainerManualEmitter::onAttachUI()
{
	m_AttachToUI = true;
	for (size_t i = 0; i < MANUALEMITTER_MAX; i++)
	{
		//GetGameEventQue().postBackpackChange(MANUAL_EMITTER_START_INDEX + i);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", MANUAL_EMITTER_START_INDEX + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}
}

void ContainerManualEmitter::onDetachUI()
{
	m_AttachToUI = false;
}

bool ContainerManualEmitter::canPutItem(int index)
{
	return true;
}

bool ContainerManualEmitter::attachPlayer(IClientPlayer* player)
{
	if (m_uin >= 0)
	{
		assert(0);
		return false;
	}
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return false;
	WCoord pos = m_BlockPos * BLOCK_SIZE;
	{
		int addx = 0;
		int addz = 0;
		BlockManualEmitter::getStepXZ(m_dir, addx, addz);
		addx += 1;
		addz += 1;
		pos += WCoord(BLOCK_SIZE * 0.5 * addx, BLOCK_SIZE * 0.5f + 70, BLOCK_SIZE * 0.5 * addz);
	}
	//设置坐下.
	int ret = playerTmp->sitInEmitter(pos, m_rotateAngle);
	if (ret != 0)
	{
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, ret);
		return false;
	}
	//player->setMoveState("ToUseEmitterMove");
	playerTmp->setActionState("ToUseEmitter");
	playerTmp->setUsingEmitter(true);
	playerTmp->setEmitterBlockPos(m_BlockPos);
	m_uin = player->getUin();
	if (player->hasUIControl())
	{
		PlayerControl* pcontrol = dynamic_cast<PlayerControl*>(player);
		if (pcontrol)
		{
			pcontrol->setCurShortcut(player->getCurShortcut());
		}
		MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", -1, 0);
	}
	else
	{
		player->applyEquips(EQUIP_WEAPON);
	}
	return true;
}

void ContainerManualEmitter::dettachPlayer()
{
	auto player = dynamic_cast<ClientPlayer*>(getPlayer());
	if (player)
	{
		player->setUsingEmitter(false);
		player->setEmitterBlockPos(WCoord(0, 0, 0));
		//恢复玩家的包围盒
		player->updateBound(180, 60);
		if (player->hasUIControl())
		{
			PlayerControl* pcontrol = dynamic_cast<PlayerControl*>(player);
			if (pcontrol)
			{
				pcontrol->setCurShortcut(player->getCurShortcut());
			}
		}
		else
		{
			player->applyEquips(EQUIP_WEAPON);
		}
		player = nullptr;
		m_uin = -1;
	}
}

bool ContainerManualEmitter::checkPutItem(int itemid, int num)
{
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (!def) return false;

	int putnum = 0;

	for (int i = 0; i < MANUALEMITTER_MAX; i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (grid.isEmpty())
		{
			putnum += def->StackMax;
			if (putnum >= num) return true;
		}
		else if (grid.getItemID() == itemid)
		{
			putnum += (def->StackMax - grid.getNum());
			if (putnum >= num) return true;
		}
	}
	return false;
}

void ContainerManualEmitter::dropItems()
{
	if (!m_World || !m_World->getActorMgr())
	{
		return;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_World->getActorMgr());
	if (!actorMgr) return;
	for (size_t i = 0; i < MANUALEMITTER_MAX; i++)
	{

		BackPackGrid& itemgrid = m_Grids[i];

		if (!itemgrid.isEmpty())
		{
			WCoord pos;
			ClientItem* item = NULL;
			{

				pos = m_BlockPos * BLOCK_SIZE + WCoord(GenRandomInt(10, 90), GenRandomInt(10, 90), GenRandomInt(10, 90));
				item = actorMgr->spawnItem(pos, itemgrid);
			}

			if (item)
			{
				ActorLocoMotion* locmove = item->getLocoMotion();
				if (!locmove)
				{
					return;
				}
				float scale = 0.05f;
				locmove->m_Motion.x = GenGaussian() * scale;
				locmove->m_Motion.y = GenGaussian() * scale + 0.2f;
				locmove->m_Motion.z = GenGaussian() * scale;
			}
		}
		itemgrid.clear();
	}
}

BackPackGrid* ContainerManualEmitter::getCurGrid()
{
	for (int i = 0; i < MANUALEMITTER_MAX; i++)
	{
		auto& grid = m_Grids[i];
		if (grid.getItemID() > 0 && grid.getNum() > 0)
		{
			return &grid;
		}
	}
	return nullptr;
}

static void ThowItemMotion(ClientItem* item, const Rainbow::Vector3f& dir, float speed)
{
	Rainbow::Vector3f motion = dir * 225.f * speed;
	motion.y += 20.0f;

	/*motion.x += GenGaussian() * 0.75f * 6.0f;
	motion.z += GenGaussian() * 0.75f * 6.0f;
	motion.y += GenGaussian() * 0.75f * 6.0f;*/

	item->getLocoMotion()->m_Motion = motion;
}

int ContainerManualEmitter::doEmit()
{
	if (isCooling())
	{
		return -1;
	}
	if (!m_World)
	{
		return -1;
	}

	//if (m_World->isRemoteMode())
	//{
	//	////需要照常播动画
	//	m_useTick = m_totalTick;
	//	//GetGameEventQue().postManualEmitterUpdateUI(3, m_useTick, m_useTick);
	//	int ret = emitItem();
	//	return 0;
	//}
	int ret = 0;
	if (!m_World->isRemoteMode())
	{
		ret = emitItem();
		if (ret <= 0)
		{
			return -1;
		}
	}
	else
	{
		ret = 1;
	}
	m_useTick = m_totalTick;
	auto player = getPlayer();
	if (player && player->hasUIControl())
	{
		//GetGameEventQue().postManualEmitterUpdateUI(3, m_useTick, m_useTick);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("type", 3).
			SetData_Number("time", m_useTick).
			SetData_Number("totalTime", m_useTick);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_MANUAL_EMITTER_UPDATE", sandboxContext);
	}
	playAnim(101, -1);
	/*if (!m_World->isRemoteMode())
	{
		m_World->markBlockForUpdate(m_BlockPos, true);
	}*/
	return ret;
}

int ContainerManualEmitter::emitItem()
{
	if (!m_World)
	{
		return 0;
	}
	auto grid = getCurGrid();
	if (!grid)
	{
		return 0;
	}
	if (!m_World)
	{
		return 0;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_World->getActorMgr());
	if (!actorMgr) return 0;
	int ret = 0;
	WCoord shootPos;
	if (!m_World->isRemoteMode())
	{
		if (!m_World->getActorMgr())
		{
			return 0;
		}
		Rainbow::Vector3f shootMotion;
		if (!getCurEmitterShootPos(getPlayer(), shootPos, shootMotion))
		{
			return 0;
		}
		int itemid = grid->getItemID();
		auto def = GetDefManagerProxy()->getItemDef(itemid);
		float speed = 1.3f;
		if (GetLuaInterfaceProxy().get_lua_const())
		{
			speed = GetLuaInterfaceProxy().get_lua_const()->emitter_speedRate;
		}
		if (def && !def->EmitScript.empty())
		{
			// 		int high = shooterObjId >> 32;
			// 		int low = shooterObjId;
			int high = m_uin >> 32;
			int low = m_uin & 0xffffffffffffffff;
			MINIW::ScriptVM::game()->callFunction(def->EmitScript.c_str(), "u[World]u[BackPackGrid]iiifffiiiifb>i", m_World, grid, shootPos.x, shootPos.y, shootPos.z, shootMotion.x, shootMotion.y, shootMotion.z, DIR_POS_Y, high, low, -1, speed, false, &ret);
			//MINIW::ScriptVM::game()->callFunction(def->EmitScript.c_str(), "u[World]u[BackPackGrid]iiidddi>i", pworld, grid, pos.x, pos.y, pos.z, dir.x, dir.y, dir.z, DIR_POS_Y, &ret);
		}
		else
		{
			//auto pos = CoordDivBlock(shootPos);
			BackPackGrid spawngrid(*grid);
			spawngrid.setNum(1);
			ClientItem* item = actorMgr->spawnItem(shootPos, spawngrid);
			if (item)
			{
				//item->setMasterObjId(shooterObjId);
				ThowItemMotion(item, shootMotion, speed);
			}
			ret = 1;
		}
	}
	else
	{
		ret = 1;
	}
	if (ret > 0)
	{
		onSubtractItem(grid, ret);
		if (m_World->getEffectMgr())
		{
			m_World->getEffectMgr()->playSound(shootPos, "misc.emitter", 1.0f, 1.0f);
		}
	}
	return ret;
}

ClientPlayer* ContainerManualEmitter::getPlayer()
{
	if (!m_World)
	{
		return nullptr;
	}
	if (!m_World->getActorMgr())
	{
		return nullptr;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_World->getActorMgr());
	if (!actorMgr) return nullptr;
	if (m_uin > 0)
	{
		return actorMgr->findPlayerByUin(m_uin);
	}
	return nullptr;
}

void ContainerManualEmitter::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);
	/*if (!m_World->isRemoteMode())
	{
		m_World->markBlockForUpdate(m_BlockPos, true);
	}*/
}

int ContainerManualEmitter::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (int i = 0; i < MANUALEMITTER_MAX; i++)
		{
			storeGridData(pItemInfos->Add(), &m_Grids[i], i + MANUAL_EMITTER_START_INDEX);
		}
	}
	return MANUALEMITTER_MAX;
}

void ContainerManualEmitter::onSubtractItem(BackPackGrid* grid, int num)
{
	for (int i = 0; i < MANUALEMITTER_MAX; i++)
	{
		BackPackGrid* ptmp = &m_Grids[i];
		if (ptmp == grid)
		{
			assert(num <= grid->getNum());
			grid->addNum(-num);
			if (grid->getNum() == 0) grid->clear();

			afterChangeGrid(i + MANUAL_EMITTER_START_INDEX);
			break;
		}
	}
}

int ContainerManualEmitter::addItem_byGridCopyData(const GridCopyData& gridcopydata)
{
	int num = gridcopydata.num;
	int numgrid = MANUALEMITTER_MAX;

	int sum = InsertItemToSameGrids(this, 0, &m_Grids[0], numgrid, gridcopydata.resid, num);

	if (sum < num)
	{
		GridCopyData tmpdata(gridcopydata);
		tmpdata.num = num - sum;
		sum += InsertItemToEmptyGrids_byGridCopyData(this, 0, &m_Grids[0], numgrid, tmpdata);
	}

	return sum;
}

//void ContainerManualEmitter::clear()
//{
//	for (int i = 0; i < MANUALEMITTER_MAX; i++)
//	{
//		m_Grids[i].clear();
//	}
//}

bool ContainerManualEmitter::checkPermit(ClientPlayer* player)
{
	if (!player)
	{
		return false;
	}
	auto grid = getCurGrid();
	if (!grid)
	{
		return false;
	}
	if (grid->getNum() <= 0)
	{
		return false;
	}
	int itemid = grid->getItemID();
	if (itemid <= 0)
	{
		return false;
	}
	MNSandbox::SandboxResult resultCanUseItem = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canUseItem",
		MNSandbox::SandboxContext(nullptr).SetData_Number("uin", player->getUin()).SetData_Number("itemid", itemid));
	bool canUseItemFlag = false;
	if (resultCanUseItem.IsExecSuccessed())
	{
		canUseItemFlag = resultCanUseItem.GetData_Bool();
	}
	if (!canUseItemFlag)
	{
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 451);
	}
	return canUseItemFlag;
}