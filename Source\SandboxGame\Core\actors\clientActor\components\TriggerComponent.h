#ifndef __TRIGGER_COMPONENT_H__
#define __TRIGGER_COMPONENT_H__

#include "ActorComponent_Base.h"
//#include "ClientActor.h"
#include "SandboxGame.h"

class ClientActor;
class EXPORT_SANDBOXGAME TriggerComponent;
class TriggerComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(TriggerComponent)

	TriggerComponent();
	~TriggerComponent();
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	//virtual
	void beHurtOnTrigger_Base(float hp);
	void moveOneBlockSizeOnTrigger_Base();
	void checkCollideOnTrigger_Base(ClientActor *actor);

	// tag  player
	void moveOneBlockSizeOnTrigger();
	void beHurtOnTrigger(float hp);
	void checkCollideOnTrigger(ClientA<PERSON> *actor);

	void ActorCreateOnTrigger(int id, int type);
	void ActorDamageOnTrigger(ClientActor* attacker, int hp, int attacktype,bool bhit,bool bhithead);
	std::map<long long, int>& getCollideActors();
protected:
	std::map<long long, int> m_CollideActors;
}; //tolua_exports
#endif