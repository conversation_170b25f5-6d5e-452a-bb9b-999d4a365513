#ifndef BLOCK_ENV_DEF_H_
#define BLOCK_ENV_DEF_H_

#include <string>

class BlockRangeCheckBase;
class BlockEnvEffectBase;
class ClientActor;

struct BlockEnvST
{
	BlockRangeCheckBase*  pRange;
	BlockEnvEffectBase*   pEffect;
	std::string           Name;

	BlockEnvST():pRange(NULL),pEffect(NULL),Name(""){

	}

	BlockEnvST(const std::string& name):pRange(NULL),pEffect(NULL),Name(name){

	}

	bool execute(ClientActor* owner);
};

struct ActiveBlockEnvST
{
	BlockEnvST blockEnvSt;
	bool bActive;
	bool operator()(const ActiveBlockEnvST& l, const ActiveBlockEnvST& r) const{
		return l.bActive;
	}
};



#endif