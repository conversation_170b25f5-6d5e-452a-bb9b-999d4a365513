#include "ActorRocket.h"
#include "world.h"
#include "WorldRender.h"
#include "ClientActorManager.h"
#include "ClientActor.h"
#include "RocketLocoMotion.h"
#include "LivingLocoMotion.h"
#include "WorldManager.h"
#include "ParticlesComponent.h"
#include "SoundComponent.h"
#include "RiddenComponent.h"
#include "ClientActorLiving.h"
#include "coreMisc.h"
using namespace MINIW;
using namespace Rainbow;
IMPLEMENT_COMPONENTCLASS(RocketLocoMotion)

RocketLocoMotion::RocketLocoMotion() 
{
	m_PrevRotateRoll = 0;
	m_RotationRoll = 0;
	m_PosRotationIncrements = 0;
	m_AddSpeed = 0;
}

void RocketLocoMotion::tick()
{
	ActorLocoMotion::tick();
	if (!m_pWorld->isRemoteMode())
	{
		ActorRocket *rocket = dynamic_cast<ActorRocket *>(getOwnerActor());
		if (rocket == nullptr) return;

		auto RidComp = getOwnerActor()->getRiddenComponent();
		ActorLiving *riddenby = NULL;
		if (RidComp)
		{
			riddenby =dynamic_cast<ActorLiving *>(RidComp->getRiddenByActor());
		}
		//旋转方向
		if (riddenby && rocket->getState() == BLAST_OFF)
		{
			LivingLocoMotion *locomove = static_cast<LivingLocoMotion *>(riddenby->getLocoMotion());
			if (locomove->m_MoveForward != 0)
			{
				Quaternionf quat;
				//quat.setEulerAngle(0, locomove->m_MoveForward * g_WorldMgr->m_SurviveGameConfig->rocketconfig.rotate, 0);
				quat = AngleEulerToQuaternionf(Vector3f(locomove->m_MoveForward * g_WorldMgr->m_SurviveGameConfig->rocketconfig.rotate, 0, 0));
				m_RotateQuat = quat*m_RotateQuat;
			}
			if (locomove->m_MoveStrafing != 0)
			{
				Quaternionf quat;
				//quat.setEulerAngle(0, 0, -locomove->m_MoveStrafing * g_WorldMgr->m_SurviveGameConfig->rocketconfig.rotate);
				quat = AngleEulerToQuaternionf(Vector3f(0, 0, -locomove->m_MoveStrafing * g_WorldMgr->m_SurviveGameConfig->rocketconfig.rotate));
				m_RotateQuat = quat*m_RotateQuat;
			}
		}
		else if (rocket->getState() == LANDING || (!riddenby && rocket->getState() == BLAST_OFF))
		{
			if (m_RotationPitch > 0)
			{
				m_RotationPitch = m_RotationPitch - 10;
				if (m_RotationPitch < 0) m_RotationPitch = 0;
			}
			else if (m_RotationPitch < 0)
			{
				m_RotationPitch = m_RotationPitch + 10;
				if (m_RotationPitch > 0) m_RotationPitch = 0;
			}

			if (m_RotationRoll > 0)
			{
				m_RotationRoll = m_RotationRoll - 10;
				if (m_RotationRoll < 0) m_RotationRoll = 0;
			}
			else if (m_RotationRoll < 0)
			{
				m_RotationRoll = m_RotationRoll + 10;
				if (m_RotationRoll > 0) m_RotationRoll = 0;
			}

			//m_RotateQuat.setEulerAngle(m_RotateYaw, -m_RotationPitch, m_RotationRoll);
			m_RotateQuat = AngleEulerToQuaternionf(Vector3f(-m_RotationPitch, m_RotateYaw, m_RotationRoll));
		}
		else if (rocket->getState() == DROP)
		{
			float tmp = WrapAngleTo180(180 - m_RotationPitch);
			if (Abs(tmp) < 10)
				m_RotationPitch = 180;
			else
				m_RotationPitch -= (tmp / tmp) * 10;


			if (m_RotationRoll > 0)
			{
				m_RotationRoll = m_RotationRoll - 10;
				if (m_RotationRoll < 0) m_RotationRoll = 0;
			}
			else if (m_RotationRoll < 0)
			{
				m_RotationRoll = m_RotationRoll + 10;
				if (m_RotationRoll > 0) m_RotationRoll = 0;
			}

			//m_RotateQuat.setEulerAngle(m_RotateYaw, -m_RotationPitch, m_RotationRoll);
			m_RotateQuat = AngleEulerToQuaternionf(Vector3f(-m_RotationPitch, m_RotateYaw, m_RotationRoll));
		}

		//motion
		if (rocket->getState() == BLAST_OFF)
		{
			int curY = CoordDivBlock(getPosition()).y;
			if (curY >= SPACE_Y)
			{
				m_Motion = Vector3f(0, 0, 0);
			}
			else
			{
				Vector3f dir;
				//m_RotateQuat.rotate(dir, Vector3f(0, 1, 0));
				dir = RotateVectorByQuat(m_RotateQuat, Vector3f(0, 1, 0));
				Rainbow::Normalize(dir);
				m_Motion = dir * (g_WorldMgr->m_SurviveGameConfig->rocketconfig.flying_v + getAddSpeed());

				checkBlockCollision();
				doMoveStep(m_Motion);
			}
		}
		else if (rocket->getState() == LANDING)
		{
			m_Motion = Vector3f(0, g_WorldMgr->m_SurviveGameConfig->rocketconfig.landing_v, 0);
			/*if (riddenby)
			{
				LivingLocoMotion *locomove = static_cast<LivingLocoMotion *>(riddenby->getLocoMotion());
				moveFlying(locomove->m_MoveStrafing, locomove->m_MoveForward, 15);
			}*/

			checkBlockCollision();
			doMoveStep(m_Motion);
		}
		else if (rocket->getState() == DROP)
		{
			m_Motion = Vector3f(0, g_WorldMgr->m_SurviveGameConfig->rocketconfig.drop_v, 0);
			checkBlockCollision();
			doMoveStep(m_Motion);
		}
	}
	else
	{
		if (m_PosRotationIncrements > 0)
		{
			m_Position = m_Position + (m_ServerPos - m_Position) / m_PosRotationIncrements;
			m_RotateQuat = Slerp(m_RotateQuat, m_ServerRot, 1.0f / m_PosRotationIncrements);

			m_PosRotationIncrements--;
		}

		return;
	}
}

void RocketLocoMotion::prepareTick()
{
	ActorLocoMotion::tick();
	m_PrevRotateQuat = m_RotateQuat;
}

void RocketLocoMotion::update(float dtime)
{
	ActorLocoMotion::update(dtime);

	m_UpdateRot = Slerp(m_PrevRotateQuat, m_RotateQuat, m_TickPosition.m_TickOffsetTime / GAME_TICK_TIME);
	m_UpdatePos = getFramePosition();
}

void RocketLocoMotion::getRotation(Rainbow::Quaternionf &quat)
{
	quat = m_RotateQuat;
}

void RocketLocoMotion::checkBlockCollision()
{
	if (m_pWorld->isRemoteMode()) return;
	if (getOwnerActor()->isDead()) return;

	bool isCollidedWithBlock = false;

	CollideAABB box;
	getCollideBox(box);
	
	if (m_pWorld->moveBox(box, WCoord(0, -30, 0)).y > -30 || m_pWorld->moveBox(box, WCoord(0, 30, 0)).y < 30)
		isCollidedWithBlock = true;
	else if (m_pWorld->moveBox(box, WCoord(-30, 0, 0)).x > -30 || m_pWorld->moveBox(box, WCoord(30, 0, 0)).x < 30)
		isCollidedWithBlock = true;
	else if (m_pWorld->moveBox(box, WCoord(0, 0, -30)).z > -30 || m_pWorld->moveBox(box, WCoord(0, 0, 30)).z < 30)
		isCollidedWithBlock = true;

	if (isCollidedWithBlock)
	{
		ActorRocket *rocket = dynamic_cast<ActorRocket *>(getOwnerActor());
		if (rocket)
		{
			rocket->collisionByBlock();
		}
	}
}

int RocketLocoMotion::getAddSpeed()
{
	auto RidComp = getOwnerActor()->getRiddenComponent();
	ActorLiving *riddenby = NULL;
	if (RidComp)
	{
		riddenby = dynamic_cast<ActorLiving *>(RidComp->getRiddenByActor());
	}
	//旋转方向
	if (riddenby)
	{
		LivingLocoMotion *locomove = static_cast<LivingLocoMotion *>(riddenby->getLocoMotion());
		if (locomove->m_isJumping)
		{
			if (m_AddSpeed == 0)
			{
				ParticlesComponent::playParticles(getOwnerActor(), "rocket_speed.ent");
								
				auto sound = getOwnerActor()->getSoundComponent();
				if (sound)
				{
					sound->playSound("ent.3803.launching", 1.0, 1.0);
				}
			}
				
			if (m_AddSpeed < g_WorldMgr->m_SurviveGameConfig->rocketconfig.add_max_v) m_AddSpeed += g_WorldMgr->m_SurviveGameConfig->rocketconfig.add_v;

			return m_AddSpeed;
		}
	}

	m_AddSpeed = 0;
	return m_AddSpeed;
}
