﻿#include "ActorBodySubsystem.h"
#include "DefManagerProxy.h"
#include "AssetPipeline/AssetManager.h"
#include "ActorBody.h"
#include "ClientMob.h"
#include "OgreModel.h"
#include "Entity/OgreEntity.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;
ActorBodySubsystem::ActorBodySubsystem(PluginManager* p)
{
	m_pPluginManager = p;
}

ActorBodySubsystem::~ActorBodySubsystem()
{

}


bool ActorBodySubsystem::Awake()
{
	CreateModuleEvent();
	return true;
}

bool ActorBodySubsystem::Init()
{
	return true;
}

bool ActorBodySubsystem::Execute(float dtime)
{
	return true;
}

bool ActorBodySubsystem::FixedTick()
{
	return true;
}
bool ActorBodySubsystem::Shut()
{
	return true;
}
void ActorBodySubsystem::CreateModuleEvent()
{
	
}

bool ActorBodySubsystem::AddUpdateActorBody(ActorBody* actorbody)
{
	/*if (nullptr == actorbody)
	{
		return false;
	}
	auto iter = m_UpdateActorBody.find(actorbody->GetBodyID());
	if (iter != m_UpdateActorBody.end())
	{
		m_UpdateActorBody.emplace(std::make_pair(actorbody->GetBodyID(), actorbody));
		return true;
	}*/
	return false;
}

bool ActorBodySubsystem::DeleteUpdateActorBody(ActorBody* actorbody)
{
	/*if (nullptr == actorbody)
	{
		return false;
	}
	auto iter = m_UpdateActorBody.find(actorbody->GetBodyID());
	if (iter != m_UpdateActorBody.end())
	{
		m_UpdateActorBody.erase(iter);
		return true;
	}*/
	return false;
}


void ActorBodySubsystem::applyBodyColor(ActorBody* actorbody, unsigned int color, bool sheared)
{
	char texdir[256];
	sprintf(texdir, "%s", "entity/110029");
	if (actorbody->getOwnerActor())
	{
		ClientMob *mob = dynamic_cast<ClientMob *>(actorbody->getOwnerActor());
		if (mob && mob->getDef())
		{
			//只有某些模型才能变色
			if (mob->getDef()->Model == "110029" || mob->getDef()->Model == "110060")
				sprintf(texdir, "entity/%s", mob->getDef()->Model.c_str());
			else
				return;
		}
		else if (actorbody->getSkinID() > 0)
		{
			const RoleSkinDef *skindef = GetDefManagerProxy()->getRoleSkinDef(actorbody->getSkinID());
			if (skindef)
			{
				sprintf(texdir, "entity/%d", skindef->Model);
			}
		}
	}
	char path[256];
	//int loadparam = MINIW::ResourceManager::getSingleton().saveMemory() ? (RLF_CONVERT_BIT16 | RLF_DONT_KEEP_MEMORY_BAK) : (RLF_CONVERT_BIT16);

	if (color >= 0)
	{
		ColourValue colorval;
		colorval.setAsARGB(color);

		if (sheared) sprintf(path, "%s/yanse1.png", texdir);
		else sprintf(path, "%s/yanse.png", texdir);

		//MINIW::Texture *tex = static_cast<MINIW::Texture *>(ResourceManager::getSingleton().blockLoad(path, loadparam));  //memopt
		const Rainbow::SharePtr<Rainbow::Texture2D> tex = GetAssetManager().LoadAsset<Texture2D>(path);
		actorbody->getEntity()->GetMainModel()->SetOverlayMask(tex, &colorval);
		/*if (tex)
			tex->release();*/
	}
	else
		actorbody->getEntity()->GetMainModel()->SetOverlayMask();

	if (sheared) 
		sprintf(path, "%s/male1.png", texdir);
	else 
		sprintf(path, "%s/male.png", texdir);

	//MINIW::Texture *tex = static_cast<MINIW::Texture *>(ResourceManager::getSingleton().blockLoad(path, loadparam));  //memopt
	const Rainbow::SharePtr<Rainbow::Texture2D> tex = GetAssetManager().LoadAsset<Texture2D>(path);
	actorbody->getEntity()->GetMainModel()->SetTexture("g_DiffuseTex", tex);
	/*if (tex)
	{
		tex->release();
	}	*/
}