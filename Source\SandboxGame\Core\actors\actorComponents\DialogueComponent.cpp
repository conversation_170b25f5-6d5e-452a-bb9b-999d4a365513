#include "DialogueComponent.h"

#include "ClientMob.h"
#include "world.h"
#include "ClientPlayer.h"
#include "GameNetManager.h"
#include "EffectComponent.h"

using namespace MNSandbox;

IMPLEMENT_COMPONENTCLASS(DialogueComponent)

DialogueComponent::DialogueComponent()
{
	m_OpenDialogueUINs.clear();
}

bool DialogueComponent::Init()
{
	return Super::Init();
}

void DialogueComponent::Release()
{
	Super::Release();
}

void DialogueComponent::addOpenDialogueUIN(int uin)
{
	if (std::find(m_OpenDialogueUINs.begin(), m_OpenDialogueUINs.end(), uin) == m_OpenDialogueUINs.end())
	{
		m_OpenDialogueUINs.push_back(uin);
		if (!GetOwner()) return ;
		ClientMob* mob = dynamic_cast<ClientMob*>(GetOwner());
		if (!mob) return ;
		if (m_OpenDialogueUINs.size() == 1)
		{
			auto effectComponent = mob->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("NPC_talk");
			}
		}
		if (mob->getDef())
		{
			int plotNum = GetDefManagerProxy()->getNpcPlotDefNum();
			std::vector<int> &npcPlotIds = GetDefManagerProxy()->getNpcPlotIds();
			for (int i = 0; i < plotNum; i++)
			{
				auto def = GetDefManagerProxy()->getNpcPlotDef(npcPlotIds[i]);
				if (def && def->InteractID == mob->getDefID())
				{
					ObserverEvent obevent;
					obevent.SetData_EventObj(uin);
					obevent.SetData_ToObj(mob->getObjId());
					obevent.SetData_Plot(def->ID);
					obevent.SetData_TargetActorID(mob->getDefID());
					GetObserverEventManager().OnTriggerEvent("Plot.begin", &obevent);
					break;
				}
			}
		}
	}
}

void DialogueComponent::closeDialogue()
{
	if (m_OpenDialogueUINs.empty()) return;

	auto tuins = m_OpenDialogueUINs;
	if (!GetOwner()) return;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return;
	World* pWorld = m_owner->getWorld();
	if (pWorld && !pWorld->isRemoteMode() && pWorld->getActorMgr())
	{
		auto am = pWorld->getActorMgr()->ToCastMgr();
		for (auto it = tuins.begin(); it != tuins.end(); ++it)
		{
			ClientPlayer *player = am->findPlayerByUin(*it);
			if (player == NULL) continue;

			player->closeContainer();
			if (player->hasUIControl())
			{
				//ge GetGameEventQue().postClosePlotDialogue();
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				   MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_CLOSE_DIALOGUE", sandboxContext);
			}
				
			else
			{
				PB_CloseDialogueHC closeDialogueHC;

				GetGameNetManagerPtr()->sendToClient(*it, PB_CLOSEDIALOGUE_HC, closeDialogueHC);
			}
		}
	}
	auto effectComponent = m_owner->getEffectComponent();
	if (effectComponent)
	{
		effectComponent->stopBodyEffect("NPC_talk");
	}
	m_OpenDialogueUINs.clear();
}

void DialogueComponent::removeOpenDialogueUIN(int uin)
{
	m_OpenDialogueUINs.erase(std::remove(m_OpenDialogueUINs.begin(), m_OpenDialogueUINs.end(), uin), m_OpenDialogueUINs.end());
	if (!GetOwner()) return;
	ClientMob* mob = dynamic_cast<ClientMob*>(GetOwner());
	if (!mob) return;
	if (m_OpenDialogueUINs.size() <= 0)
	{
		auto effectComponent = mob->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("NPC_talk");
		}
	}
	if (mob->getDef())
	{
		int plotNum = GetDefManagerProxy()->getNpcPlotDefNum();
		std::vector<int> &npcPlotIds = GetDefManagerProxy()->getNpcPlotIds();
		for (int i = 0; i < plotNum; i++)
		{
			auto def = GetDefManagerProxy()->getNpcPlotDef(npcPlotIds[i]);
			if (def && def->InteractID == mob->getDefID())
			{
				ObserverEvent obevent;
				obevent.SetData_EventObj(uin);
				obevent.SetData_ToObj(mob->getObjId());
				obevent.SetData_Plot(def->ID);
				obevent.SetData_TargetActorID(mob->getDefID());
				GetObserverEventManager().OnTriggerEvent("Plot.end", &obevent);
				break;
			}
		}
	}
}

bool DialogueComponent::isInteracting()
{
	return !m_OpenDialogueUINs.empty();
}

void DialogueComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	ClientMob* mob = dynamic_cast<ClientMob*>(owner);
	if (mob)
	{
		mob->SET_USEMODULE(isInteracting, [this]() -> bool {
			return this->isInteracting();
		});
		mob->SET_USEMODULE(addOpenDialogueUIN, [this](int uin) -> int {
			this->addOpenDialogueUIN(uin);
			return 0;
		});
		mob->SET_USEMODULE(removeOpenDialogueUIN, [this](int uin) -> int {
			this->removeOpenDialogueUIN(uin);
			return 0;
		});
		mob->SET_USEMODULE(closeDialogue, [this]() -> int {
			this->closeDialogue();
			return 0;
		});
	}
	else
	{
		assert(false);
	}

	m_OpenDialogueUINs.clear();
	Super::OnEnterOwner(owner);
}

void DialogueComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	ClientMob* mob = dynamic_cast<ClientMob*>(owner);
	if (mob)
	{
		mob->CLEAR_USEMODULE(isInteracting);
		mob->CLEAR_USEMODULE(addOpenDialogueUIN);
		mob->CLEAR_USEMODULE(removeOpenDialogueUIN);
		mob->CLEAR_USEMODULE(closeDialogue);
	}

	Super::OnLeaveOwner(owner);
}

/////////////////////////////////////////////////////////////////////

