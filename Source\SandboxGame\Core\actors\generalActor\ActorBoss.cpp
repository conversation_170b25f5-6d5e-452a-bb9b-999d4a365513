
#include "ActorBoss.h"
#include "WorldManager.h"
//#include "GameEvent.h"
#include "ActorUpdateFrequency.h"
#include "SandboxListener.h"
//#include "TaskSubSystem.h"
#include "TaskData.h"
#include "worldMesh/MiniCraftRenderer.h"
#include "Entity/OgreEntity.h"
#include "LivingAttrib.h"

using namespace MNSandbox;

const int BOSS_VIEWCHUNKS = 1;
IMPLEMENT_SCENEOBJECTCLASS(ActorBoss)
void ActorBoss::enterWorld(World *pworld)
{
	ActorLiving::enterWorld(pworld);

	m_ChunkViewer.enterWorld(pworld, getPosition(), BOSS_VIEWCHUNKS);
}

void ActorBoss::leaveWorld(bool keep_inchunk)
{
	m_ChunkViewer.leaveWorld(m_pWorld);

	ActorLiving::leaveWorld(keep_inchunk);
}

void ActorBoss::updateChunkView()
{
	m_ChunkViewer.updateChunkView(m_pWorld, getPosition(), BOSS_VIEWCHUNKS);
}

void ActorBoss::addMissionFlags(int bit)
{
	m_MissionFlags |= bit;

	GetWorldManagerPtr()->saveGlobalAndPlayers();
}

void ActorBoss::onDie()
{
	/*if (TaskSubSystem::GetTaskSubSystem())
	{
		TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_KILL_MOB, getPosition(), m_Def->ID);
	}*/
	WCoord pos = getPosition();
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("type", TASKSYS_KILL_MOB).
			SetData_Userdata("WCoord", "trackPos", &pos).
			SetData_Number("target1", m_Def->ID).
			SetData_Number("target2", 0).
			SetData_Number("goalnum", 1);
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
	}

	//GetGameEventQue().postBossDie(getBeHurtTargetID(), m_Def->ID);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("beurtTarget", getBeHurtTargetID()).
		SetData_Number("bossId", m_Def->ID);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr() == nullptr) return;
	MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BOSS_DIE", sandboxContext);

	getAttrib()->onDie();
}

void ActorBoss::tick() {
	ActorLiving::tick();
	LivingAttrib* attrib = getLivingAttrib();
	if (attrib == NULL) return;
	if (attrib->hasBuff(SUDDEN_ILLNESS_BUFF))
	{
		attrib->removeBuff(SUDDEN_ILLNESS_BUFF);
	}
}

ActorBody *ActorBoss::newActorBody()
{
	return NULL;
}

ActorUpdateFrequency* ActorBoss::getUpdateFrequencyCom()
{
	if (!m_pActorUpdateFrequency) {
		m_pActorUpdateFrequency = CreateComponent<EmptyUpdateFrequency>("EmptyUpdateFrequency");
	}
	if (m_pActorUpdateFrequency == nullptr) {
		return nullptr;
	}
	return m_pActorUpdateFrequency;
}

//事件注册
void ActorBoss::createEvent()
{

}

int ActorBoss::getDefID()
{
	if (m_Def)
		return m_Def->ID;

	return 0;
}

void ActorBoss::setBodyCull()
{
	//虚空幻影类型排除
	if (!m_bSectionIsDisplay)
	{
		//actor所在的section已经不显示了,所以actor应该也要隐藏
		ClientActor::setBodyCull();
	}
	else
	{
		/*float distance = SetMobVisibleDistance();
		if (getBody() && getBody()->getEntity())
		{
			LOG_INFO("ActorBoss::setBodyCull() %f", distance);
			getBody()->getEntity()->SetVisibleDistance(distance, Rainbow::CullPolicy::kCullPolicyDistance);
		}*/
		if (getEntity())
			getEntity()->SetVisibleDistance(1.0f, Rainbow::CullPolicy::kCullPolicyNone);
	}
}
float ActorBoss::SetMobVisibleDistance()
{
	//默认值设置为8
	int viewRange = 8; 
	if (!m_pWorld->isRemoteMode())
	{
		viewRange = Rainbow::GetMiniCraftRenderer().GetClientSetting().m_ViewSectionRange;
	}
	int range = viewRange * SECTION_BLOCK_DIM;
	return range * BLOCK_SIZE / 2;
}