#ifndef __CONTAINER_EROSION_STORAGE_H__
#define __CONTAINER_EROSION_STORAGE_H__

#include "container_erosion.h"
#include "container_world.h"

#define EROSION_STORAGEBOX_CAPACITY 30

class  ErosionStorageBox : public ErosionContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	ErosionStorageBox();
	ErosionStorageBox(const WCoord& blockpos, const int blockId);
	virtual ~ErosionStorageBox();

	virtual int getObjType() const override
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType() override
	{
		return FBSave::ContainerUnion_ContainerErosionStorage;
	}

	virtual void leaveWorld() override;

	virtual void afterChangeGrid(int index) override;
	virtual bool canPutItem(int index) override;
	virtual void onAttachUI() override;
	virtual void onDetachUI() override;

	virtual int getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos) override;
	virtual void syncItemUserdata(IClientPlayer* player) override;
	virtual int onInsertItem(const BackPackGrid& grid, int num, int params) override;
	virtual bool canInsertItem(const BackPackGrid& grid, int param) override;
	virtual BackPackGrid* onExtractItem(int params) override;
	virtual void onSubtractItem(BackPackGrid* grid, int num) override; //从属于container的grid里面减去多少个物品
	virtual int calComparatorInputOverride() override;

	virtual void dropItems() override;
	virtual void dropItems(WCoord BlockPos);

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata) override;

	virtual int addItem_byGridCopyData(const GridCopyData& grid) override;

	void append(ErosionStorageBox* box);
	virtual void addOpenUIN(int uin) override;
	virtual void removeOpenUIN(int uin) override;
	virtual bool checkPutItem(int itemid, int num) override;
	virtual BackPackGrid* getGridByItemID(int itemid) override;

	BackPackGrid* genRandomGrid(bool clear_others); //得到一个有物品的随机格子

	bool checkEmptyGrid(int resid);
	bool isCompletelyEmpty(); // 检查储物箱是否完全为空的新方法
	
	bool isNeedDestroyWhenEmpty();
	void setIsNeedDestroyWhenEmpty(bool b) { m_isNeedDestroyWhenEmpty = b; }

	ErosionStorageBox* getAppend()
	{
		return m_AppendBox;
	}
	void setItem(int offset, int resid, int num, const char* userdata = NULL);
	virtual int getGridNum() override
	{
		return EROSION_STORAGEBOX_CAPACITY;
	}
	virtual int getGridCount();
	virtual BackPackGrid* index2Grid(int index) override;

	void clear(); //清空储物箱
	int addItemByCount(int itemid, int num);
	void removeItemByCount(int itemid, int num);
	void removeItemByIndex(int index, int num);

	//类名
	virtual const char* getContainerName() const override {
		return "ErosionStorageBox";
	}
	//tolua_end

	void setAppendBox(int blockid);//主箱Grid空时，副箱Grid不空时，合并箱子

protected:
	virtual flatbuffers::Offset<FBSave::ContainerErosionStorage> saveContainerErosionStorage(SAVE_BUFFER_BUILDER& builder);

public:
	//tolua_begin
	BackPackGrid m_Grids[EROSION_STORAGEBOX_CAPACITY];
	ErosionStorageBox* m_AppendBox;
	ErosionStorageBox* m_ParentBox;
	int m_GridCount;
	bool m_isNeedDestroyWhenEmpty;
	//tolua_end
};//tolua_exports

#endif 