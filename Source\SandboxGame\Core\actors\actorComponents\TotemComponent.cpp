#include "TotemComponent.h"

#include "ClientPlayer.h"
#include "world.h"
#include "ActorLocoMotion.h"
#include "ActorRocket.h"
#include "LuaInterfaceProxy.h"
#include "special_blockid.h"
#include "container_alientotem.h"
#include "container_world_lua.h"
#include "RiddenComponent.h"
#include "LivingAttrib.h"
#include "CustomModelData.h"
#include "ClientActorManager.h"

using namespace MNSandbox;

/*
depend on owner's interfaces:
bool isDead();
LivingAttrib* getLivingAttrib();
ActorLocoMotion* getLocoMotion();
unsigned short getCurMapID();
float getOxygenUseRate();
ClientActor* getRidingActor();
bool isApplyOxyPack();
World* getWorld();
*/
IMPLEMENT_COMPONENTCLASS(TotemComponent)

TotemComponent::TotemComponent() :m_TotemTicks(0), m_ReportLock(false)
{

}

void TotemComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void TotemComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}
void TotemComponent::OnTick()
{

	if (!GetOwner()) return;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if(m_owner && !m_owner->isDead())
	{	
		m_TotemTicks++;
		if (m_TotemTicks >= 20)
		{
			m_TotemTicks = 0;
			//if(getObjType() == OBJ_TYPE_ROLE)
			//{
			bool hasRocketOxygenBuf = false;
			/*ActorRocket *rocket = dynamic_cast<ActorRocket *>(getRidingActor());
			if (rocket && rocket->getState() != DROP && rocket->getState() != HOLD_STILL)
				hasRocketOxygenBuf = true;*/
			hasRocketOxygenBuf = isNeedAddOxygenBuff();

			LivingAttrib* livingAttr = m_owner->getLivingAttrib();
			ActorLocoMotion* locomotion = m_owner->getLocoMotion();

			const int buffSize = 4;
			int buffIds[buffSize] = { 63, 64, 65, 74 };
			if (m_owner->getCurMapID() == MAPID_MENGYANSTAR)
			{
				int buffid = 0;
				if (locomotion->isInsideNoOxygenBlock())
				{
					if (m_owner->getOxygenUseRate() > 0 && !hasRocketOxygenBuf)
					{
						//if (getLivingAttrib()->getOxygen() == 0) buffid = 64;
						//else buffid = 63;

						//毒气Buff改为氧气稀少Buff code-by:lizb
						buffid = 74;

						//氧气果支持60s的氧气供给
						if (livingAttr->hasBuff(66)) buffid = 0;
					}
					else if (hasRocketOxygenBuf)
						buffid = 65;
				}
				else
				{
					if (hasRocketOxygenBuf || hasActivatedFurnaceOxy())
						buffid = 65;
				}

				for (int i = 0; i < buffSize; i++)
				{
					int iterId = buffIds[i];
					if (iterId == buffid)
					{
						if (!livingAttr->hasBuff(iterId))
							livingAttr->addBuff(iterId, iterId == 65 ? 2 : 1);
					}
					else
					{
						if (livingAttr->hasBuff(iterId))
							livingAttr->removeBuff(iterId);
					}
				}
			}
			else
			{
				for (int i = 0; i < buffSize; i++)
				{
					int iterId = buffIds[i];
					if ((iterId != 65 || !hasRocketOxygenBuf) && livingAttr->hasBuff(iterId))
						livingAttr->removeBuff(iterId);
				}
			}

			//{//6月子地形 粉蝶子地形buff圈 
			//	bool remoteNeedReport = false;
			//	if (m_owner && m_owner->getWorld())
			//	{
			//		if (!m_owner->getWorld()->isRemoteMode())
			//		{
			//			int hour = m_owner->getWorld()->getHours();
			//			auto tmpWorld = m_owner->getWorld();
			//			auto actormgrinterface = tmpWorld->getActorMgr();
			//			ClientActorMgr* actMgr = actormgrinterface ? actormgrinterface->ToCastMgr() : nullptr;
			//			if (actMgr)
			//			{
			//				int tempbuffId = 2020;//发光蝴蝶buff

			//				auto players = actMgr->getAllPlayer();
			//				std::vector<ClientPlayer*> playerInZoom;
			//				for (int i = 0; i < players.size(); i++)
			//				{
			//					if (!players[i]) continue;
			//					WCoord tmpPos = players[i]->getPosition();
			//					int biomeId = tmpWorld->getBiomeId(tmpPos.x / BLOCK_SIZE, tmpPos.z / BLOCK_SIZE);
			//					if (biomeId == BIOME_AIRISLAND_SHINE && (hour >= 20 || hour <= 4))//晚上8点到凌晨4点生效
			//					{
			//						playerInZoom.push_back(players[i]);
			//					}
			//					else
			//					{
			//						auto tempLivingAttre = players[i]->getLivingAttrib();
			//						if (tempLivingAttre)
			//						{
			//							if (tempLivingAttre->hasBuff(tempbuffId))
			//							{
			//								tempLivingAttre->removeBuff(tempbuffId);
			//							}
			//						}
			//					}
			//				}
			//				for (int i = 0; i < playerInZoom.size(); i++)
			//				{
			//					int tempbuffLevel = GenRandomInt(1, 3);
			//					if (!playerInZoom[i]) continue;
			//					auto tempLivingAttre = playerInZoom[i]->getLivingAttrib();
			//					if (tempLivingAttre)
			//					{
			//						if (!tempLivingAttre->hasBuff(tempbuffId) && playerInZoom.size() >= 2)
			//						{

			//							tempLivingAttre->addBuff(tempbuffId, tempbuffLevel);
			//							if (m_ReportLock == false)
			//							{
			//								m_ReportLock = true;
			//								//上报遇到蝴蝶
			//								MINIW::ScriptVM::game()->callFunction("FlowersActivity_GameEventReport", "sis", "meet_butterfly", 1201, "");
			//								//埋点上报
			//								MINIW::ScriptVM::game()->callFunction("GodStatueReport", "");
			//							}
			//							//上报遇到蝴蝶
			//							MINIW::ScriptVM::game()->callFunction("FlowersActivity_GameEventReport", "sis", "meet_butterfly", 1201, "");
			//							//埋点上报
			//							MINIW::ScriptVM::game()->callFunction("GodStatueReport", "");

			//						}
			//						else if (tempLivingAttre->hasBuff(tempbuffId) && playerInZoom.size() < 2)
			//						{
			//							tempLivingAttre->removeBuff(tempbuffId);
			//						}
			//					}
			//				}
			//			}
			//		}
			//		else//客机上报
			//		{
			//			if (m_ReportLock == false && livingAttr->hasBuff(2020))
			//			{
			//				m_ReportLock = true;
			//				//上报遇到蝴蝶
			//				MINIW::ScriptVM::game()->callFunction("FlowersActivity_GameEventReport", "sis", "meet_butterfly", 1201, "");
			//				//埋点上报
			//				MINIW::ScriptVM::game()->callFunction("GodStatueReport", "");
			//			}
			//		}
			//	}

			//}
			//图腾剩余时间
			isInsideTotem();
			//}
		}
	}
}


bool TotemComponent::isNeedAddOxygenBuff()
{
	if (!GetOwner()) return false;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	bool bNeedAddOxyBuff = false;

	//if(getObjType() == OBJ_TYPE_ROLE)
	//{
	if (m_owner->getCurMapID() >= MAPID_MENGYANSTAR)
	{
		auto PlayerRidComp = m_owner->getRiddenComponent();
		//火箭
		ActorRocket* rocket = NULL;
		if (PlayerRidComp)
		{
			rocket = dynamic_cast<ActorRocket*>(PlayerRidComp->getRidingActor());
		}
		if (rocket && rocket->getState() != DROP && rocket->getState() != HOLD_STILL)
			bNeedAddOxyBuff = true;

		//氧气背包
		if (!bNeedAddOxyBuff)
			bNeedAddOxyBuff = m_owner->isApplyOxyPack();
	}
	//}

	return bNeedAddOxyBuff;
}

bool TotemComponent::hasActivatedFurnaceOxy()
{
	if (!GetOwner()) return false;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	ActorLocoMotion* locomotion = m_owner->getLocoMotion();
	std::vector<WCoord> pos;
	int radius = GetLuaInterfaceProxy().get_lua_const()->planet_safearea_radius;
	WCoord center = CoordDivBlock(locomotion->getPosition());
	WCoord minpos = center - WCoord(radius, 0, radius);
	WCoord maxpos = center + WCoord(radius, 0, radius);
	World* pWorld = m_owner->getWorld();
	if (pWorld->findAllBlock(pos, center, minpos, maxpos, 1045, radius))
	{
		int nSize = pos.size();
		for (int i = 0; i < nSize; i++)
		{
			WCoord blockpos = pos[i];
			//WorldContainer *container = m_pWorld->getContainerMgr()->getContainer(blockpos);
			int blockdata = pWorld->getBlockData(blockpos);

			if (blockdata & 4)
			{
				//已激�?
				return true;
			}
		}
	}

	return false;
}


bool TotemComponent::isInsideTotem()
{
	if (!GetOwner()) return false;
	ClientPlayer* m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_owner) return false;
	//m_TotemTicks++;
	if (true)
	{
		LivingAttrib* livingAttr = m_owner->getLivingAttrib();
		ActorLocoMotion* locomotion = m_owner->getLocoMotion();
		World* pWorld = m_owner->getWorld();
		if (pWorld)
		{
			if (pWorld->isRemoteMode())
			{
				return true;
			}
		}
		//m_TotemTicks = 0;
		int buffId = 67;
		int lv = 1;
		const BuffDef* def = NULL;
#if 0
		if (livingAttr->isNewStatus())
		{
			int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffId, 1);
			def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
		}
		else
		{
			def = GetDefManagerProxy()->getBuffDef(buffId, 1);
		}
#else
		int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffId, 1);
		def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
#endif

		if (def)
		{
			int nMaxTick = def->EffectTicks; //GetLuaInterfaceProxy().get_lua_const()->planet_totem_activeage * 20;
			int nMintick = nMaxTick;		//剩余时间最长的图腾的使用时�?

			std::vector<WCoord> pos;
			int radius = GetLuaInterfaceProxy().get_lua_const()->planet_safearea_radius;
			WCoord center = CoordDivBlock(locomotion->getPosition());
			WCoord minpos = center - WCoord(radius, 0, radius);
			WCoord maxpos = center + WCoord(radius, 0, radius);

			if (pWorld->findAllBlock(pos, center, minpos, maxpos, 1042, radius) || pWorld->findAllBlock(pos, center, minpos, maxpos, 1058, radius))
			{
				int nSize = pos.size();
				for (int i = 0; i < nSize; i++)
				{
					WCoord blockpos = pos[i];
					WorldContainer* container = pWorld->getContainerMgr()->getContainer(blockpos);
					int blockdata = pWorld->getBlockData(blockpos);

					if (container && container->getObjType() == OBJ_TYPE_EFFECT)
					{
						WorldAlienTotemContainer* relContainer = static_cast<WorldAlienTotemContainer*>(container);
						if (relContainer)
						{
							if (pWorld && pWorld->getBlockID(blockpos) == BLOCK_STONE_MONUMENT)
							{
								lv = 2;
#if 0
								if (livingAttr->isNewStatus())
								{
									int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffId, lv);
									def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
								}
								else
								{
									def = GetDefManagerProxy()->getBuffDef(buffId, 1);
								}
#else
								int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffId, lv);
								def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
#endif
								nMaxTick = def->EffectTicks;
								nMintick = nMaxTick;
							}

							if (blockdata & 4)
							{
								//已激�?
								int oxygenTick = relContainer->getOxygenTick();
								if (oxygenTick >= 0 && oxygenTick < nMintick)
									nMintick = oxygenTick;
							}
						}

						//lua 实现
						WorldContainerLua* containerLua = static_cast<WorldContainerLua*>(container);
						if (containerLua && containerLua->getSubTypeLua() == 1)
						{
							if (pWorld && pWorld->getBlockID(blockpos) == BLOCK_STONE_MONUMENT)
							{
								lv = 2;
#if 0
								if (livingAttr->isNewStatus())
								{
									int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffId, lv);
									def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
								}
								else
								{
									def = GetDefManagerProxy()->getBuffDef(buffId, 1);
								}
#else
								int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffId, lv);
								def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
#endif
								nMaxTick = def->EffectTicks;
								nMintick = nMaxTick;
							}

							if (blockdata & 4)
							{
								//已激�?
								SandboxResult result = containerLua->Event().Emit("getOxygenTick", SandboxContext(containerLua));
								int oxygenTick = 0;
								if (result.IsSuccessed())
								{
									oxygenTick = int(result.GetData_Number());
								}
								if (oxygenTick >= 0 && oxygenTick < nMintick)
									nMintick = oxygenTick;
							}
						}
					}
				}
			}

			if (nMintick >= 0 && nMintick < nMaxTick)
			{
				//0 < nMintick < 6000
				//加buff, 显示剩余时间
				int remainTick = (nMaxTick - nMintick);	//剩余tick

				if (remainTick > 0)
				{
					if (!livingAttr->hasBuff(buffId))
						livingAttr->addBuff(buffId, lv, remainTick);

					//剩余时间
					if (livingAttr->hasBuff(buffId))
						livingAttr->SetBuffTick(buffId, lv, remainTick);
				}
			}
			else
			{
				//去buff
				if (livingAttr->hasBuff(buffId))
					livingAttr->removeBuff(buffId);
			}
		}
	}

	return true;
}