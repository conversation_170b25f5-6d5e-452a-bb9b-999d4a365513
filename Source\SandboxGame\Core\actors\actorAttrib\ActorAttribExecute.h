#ifndef __ACTORATTRIBEXECUTE_H__
#define __ACTORATTRIBEXECUTE_H__

#include <iostream>
#include "ActorAttrib.h"

class ActorAttrib;

class ActorAttribExecute
{
public:
	ActorAttribExecute() {};
	virtual ~ActorAttribExecute() = default;
	virtual bool ExecuteAdd(ActorAttrib* attrib, float& val,  bool overflowable, ActorAttribType type) = 0;
	virtual bool ExecuteSet(ActorAttrib* attrib, float val, bool overflowable, ActorAttribType type) = 0;
};

class ActorHpExecute : public ActorAttribExecute
{
public:
	ActorHpExecute() {};
	bool ExecuteAdd(ActorAttrib* attrib, float& hp, bool overflowable, ActorAttribType type) override;
	bool ExecuteSet(ActorAttrib* attrib, float hp, bool overflowable, ActorAttribType type) override;
};

class ActorArmorExecute : public ActorAttribExecute
{
public:
	ActorArmorExecute(float value = 0, float minValue = 0, float maxValue = 100) :m_minValue(minValue), m_maxValue(maxValue) { setValue(value); };
	bool ExecuteAdd(ActorAttrib* attrib, float& val, bool overflowable, ActorAttribType type) override;
	bool ExecuteSet(ActorAttrib* attrib, float val, bool overflowable, ActorAttribType type) override;
	void setValue(float val);
	float getValue() { return m_value; }
	void setMaxValue(float v) { m_maxValue = v; }

private:
	float m_value;
	float m_maxValue;
	float m_minValue;
};

class ActorStrengthExecute : public ActorAttribExecute
{
public:
	ActorStrengthExecute() {};
	bool ExecuteAdd(ActorAttrib* attrib, float& strength, bool overflowable, ActorAttribType type) override;
	bool ExecuteSet(ActorAttrib* attrib, float strength, bool overflowable, ActorAttribType type) override;
};

class ActorThirstExecute : public ActorAttribExecute
{
public:
	ActorThirstExecute() {};
	bool ExecuteAdd(ActorAttrib* attrib, float& thirst, bool overflowable, ActorAttribType type) override;
	bool ExecuteSet(ActorAttrib* attrib, float thirst, bool overflowable, ActorAttribType type) override;
};

class ActirPerseveranceExecute : public ActorAttribExecute
{
public:
	ActirPerseveranceExecute(float value = 0, float minValue = 0, float maxValue = 100) :m_minValue(minValue), m_maxValue(maxValue) { setValue(value); };
	bool ExecuteAdd(ActorAttrib* attrib, float& val, bool overflowable, ActorAttribType type) override;
	bool ExecuteSet(ActorAttrib* attrib, float val, bool overflowable, ActorAttribType type) override;
	void setValue(float val);
	float getValue() { return m_value; }
private:
	float m_value;
	float m_maxValue;
	float m_minValue;
};

#endif//__ACTORATTRIBEXECUTE_H__