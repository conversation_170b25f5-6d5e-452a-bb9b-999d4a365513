#pragma once

class ClientActor;
class ClientMob;
#include <limits>
#include "AttributeValue.h"
class EXPORT_SANDBOXGAME FoodValue;

class FoodValue : public AttributeValue //tolua_export
{//tolua_export
	DECLARE_REFCLASS(FoodValue)
public:
	FoodValue();
	virtual ~FoodValue();

	//设置饱食度
	void SetFoodValue(float fValue)
	{
		SetValue(fValue);
	}
	
	float GetFoodValue() const
	{
		return GetValue();
	}

	//设置基础最大饱食度
	void SetBasicMaxFood(float fValue);
	float GetBasicMaxFood() const;

	//设置最大饱食度
	void SetMaxFood(float fValue);
	float GetMaxFood() const;

	//设置基础溢出值
	void SetBasicOverflowFood(float fValue);
	float GetBasicOverflowFood()const;

	//设置溢出值
	void SetOverflowFood(float fValue);
	float GetOverflowFood() const;

	//增加饱食度
	void IncreaseFood(float fValue);

	//减少饱食度
	void DecreaseFood(float fValue);

private:
	//tolua_begin
	//数值变化前
	virtual void OnValueChangeBefore();
	//数值变化后
	virtual void OnValueChangeAfter();

	//tolua_end

private:
	float m_fBasicMaxFood = 0;
	float m_fMaxFood = 0;
	float m_fBasicOverflowFood = 0;
	float m_fOverflowFood = 0;
	
}; 