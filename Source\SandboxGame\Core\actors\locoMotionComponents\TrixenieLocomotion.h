#pragma once
// #include "FlyLocomotion.h"
// #include "LivingLocoMotion.h"
// #include "AquaticLocomotion.h"
#include "ActorLocoMotion.h"
#include "LivingLocoMotion.h"
#include "Smoother.h"
#include "OgrePhysXManager.h"
#include "SandboxGame.h"
//tolua_begin
enum LocomotionType
{
	Land_Loc = 0,
	Air_Loc,
	Aquatic_Loc,
};
//tolua_end

//三栖移动类  1使用菱形继承，爷类数据有多份，且调用是需要指定 2分别创建三栖三个对象，同样基类数据3份占内存。（1,2都需要同步基类数据）3三栖对象代码合并，只会存在一份基类数据且不需要进行拷贝操作，但是不能与三栖对象代码同步更新。
//现使用第三种，以内存效率
//暂时没有 同时水中移动的需求，所以没有测试功能
class EXPORT_SANDBOXGAME TrixenieLocomotion;
class TrixenieLocomotion : public ActorLocoMotion//public FlyLocomotion, public LivingLocoMotion, public AquaticLocomotion //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(TrixenieLocomotion)

	//tolua_begin
	TrixenieLocomotion();
	virtual ~TrixenieLocomotion();
	//tolua_end
	/* ����owner */
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner)override;
	virtual void tick() override;
	virtual void gotoPosition(const WCoord &pos, float yaw, float pitch) override;

protected:
	bool updateMoveTarget();//到达目标: return true
	void collideWithNearbyActors();
	float getGravityFactor(bool up); //up ? 上升: 下降

//LivingLocoMotion
public:
	//tolua_begin
	void doJump();
	void doJumpTarget();

	void setMoveForward(float vel);
	void setTarget(const WCoord &target, float speed); //如果设置了目标点,  到达目标点会发消息,  NULL会清除目标
	void clearTarget();
	float calGravityMotionY(float my);
	void setBoundJump(bool bound) { m_bBoundJump = bound; }
	bool canDoJump(); //玩家是否可跳跃
	const WCoord& GetMoveTarget() { return m_MoveTarget; }

	void setJumpToTarget(const WCoord &target);
	//tolua_end
protected:
	bool prepareJump(int &cooldown);
	void updateJetpackMove();
	void updateJumping();
	void UpdateClimbTree();

	void autoStep();
	void moveEntityWithHeading(float strafing, float forward);
	bool checkSeatInWaterSkill(int iSkillId) { return false; }//1 == 浮水, 2 == 潜泳, 4 == 水中突进
	bool checkCanUpAndDown(float fRotationPitch) { return false; }
	bool isDoingRush() { return false; }
//LivingLocoMotion end
//FlyLocomotion AquaticLocomotion 
public:
	//tolua_begin
	void UpdateRotation();

	bool isBehaviorOn(BehaviorType type)
	{
		return (m_BehaviorFlag & type) == type;
	}

	void setBehaviorOn(BehaviorType type)
	{
		m_BehaviorFlag |= type;
	}

	void setBehaviorOff(BehaviorType type)
	{
		if (isBehaviorOn(type))
			m_BehaviorFlag ^= type;
	}

	void SetBehaviorTypeWeight(BehaviorType type, float weight);

	void SetSmoothRotation(bool b);
	bool IsFlyBeStop()
	{
		if (Air_Loc == m_nMoveType)
		{
			return m_bIsFlyBeStop;
		}
		return false;
	}
	//tolua_end
protected:
	void moveEntityWithDirection();
	void calculateSteering();
	Rainbow::Vector3f seek(const WCoord& target);
	Rainbow::Vector3f flee(const WCoord& target);

	Rainbow::Vector3f pursuit(ClientActor& target);
	Rainbow::Vector3f evade(ClientActor& evader);

	bool AccumulateForce(Rainbow::Vector3f& SteeringForce, const Rainbow::Vector3f& addTo);

//FlyLocomotion AquaticLocomotion end

//FlyLocomotion
public:
	//tolua_begin
	float GetSpeedInAir();
//FlyLocomotion end

//AquaticLocomotion
	//tolua_end
public:
	//tolua_begin
	float GetSpeedInWater();
	//tolua_end
protected:
	Rainbow::Vector3f obstacleAvoidance();
	Rainbow::Vector3f surfaceAvoidance();
//AquaticLocomotion end

public:
	//tolua_begin
	WCoord m_MoveTarget;
	//tolua_end
protected:
	float m_MoveTargetSpeed; //<0 表示没有目标


//LivingLocoMotion
public:
	//tolua_begin
	float m_MoveForward;
	float m_MoveStrafing;
	bool m_bBoundJump;
	//tolua_end
protected:
	WCoord m_JumpTarget;
//LivingLocoMotion	end

//FlyLocomotion AquaticLocomotion
public:
	//tolua_begin
	Rainbow::Vector3f m_Velocity;
	Rainbow::Vector3f m_ColliderMotion;
	Rainbow::Vector3f m_SteeringForce;
	WORLD_ID m_FearPlayerId;
	float m_StopDist;
	bool m_HasTarget;
	float m_SpeedMultiple;
	int m_SyncSteps;
	WCoord m_SyncPos;
	float m_SyncYaw;
	float m_SyncPitch;
	//binary flags to indicate whether or not a behavior should be active
	int m_BehaviorFlag;
	//tolua_end
protected:
	float m_MaxSteeringForce;
	bool m_SmoothRotationOn;
	float obstacleAvoidanceWeight;
	float surfaceAvoidanceWeight;
	float fleeWeight;
	float wanderWeight;
	float pursuitWeight;

	Smoother<Rainbow::Vector3f>* m_smoother;

	bool m_bIsFlyBeStop;
//FlyLocomotion AquaticLocomotion end
//FlyLocomotion
public:
	//tolua_begin
	float m_SpeedInAir;

//FlyLocomotion  end

//AquaticLocomotion
	//tolua_end
public:
	//tolua_begin
	float m_SpeedInWater;

//AquaticLocomotion end
	//tolua_end
public:
	//tolua_begin
	int getCurMoveType() { return m_nMoveType; }
	void setCurMoveType(int type);
	//tolua_end
protected:
	int m_nMoveType;  //三栖状态，联机的时候没有进行同步。 把陆地移动的同步方式都改成和飞行游泳一样。原移动是只同步位置和转向。不同步状态效果一样
}; //tolua_exports