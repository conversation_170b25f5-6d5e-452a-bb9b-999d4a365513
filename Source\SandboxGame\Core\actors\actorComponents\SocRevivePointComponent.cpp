#include "SocRevivePointComponent.h"

#include "special_blockid.h"
#include "BlockBed.h"
#include "ZmqProxy.h"
#include "ClientActorHelper.h"
#include "GameNetManager.h"
#include "EffectManager.h"
#include "WorldManager.h"
#include "ClientPlayer.h"
#include "DefManagerProxy.h"
#include "Play/gameplay/SocBedMgr.h"
#include "SandboxEventDispatcherManager.h"

#define NetType 1
#define CheckPosition 1
#define AddPosition 2
#define DelPosition 3
#define CheckPositionRet 4
#define ReqAllPoints 5
#define ReqAllPointsRet 6
#define GivePoint 7

IMPLEMENT_COMPONENTCLASS(SocRevivePointComponent)

SocRevivePointComponent::SocRevivePointComponent() :m_isselect(false){

}

SocRevivePointComponent::~SocRevivePointComponent() {

}

void SocRevivePointComponent::OnNetMessage(const std::string& data)
{
	jsonxx::Object obj;
	obj.parse(data);
	//校验复活点位置
	if (obj.get<jsonxx::Number>("type") == CheckPosition)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");
		OnCheckPosition(x, y, z);
		return;
	}

	if (obj.get<jsonxx::Number>("type") == AddPosition)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");
		int time = obj.get<jsonxx::Number>("time");
		int itemid = obj.get<jsonxx::Number>("itemid");
		OnAddPosition(x, y, z, time, itemid);
		return;
	}

	if (obj.get<jsonxx::Number>("type") == DelPosition)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");
		OnDelPosition(x, y, z);
		return;
	}

	if (obj.get<jsonxx::Number>("type") == CheckPositionRet)
	{
		int ret = obj.get<jsonxx::Number>("ret");
		std::string str = obj.get<jsonxx::String>("msg");
		OnCheckPositionRet(ret, str);
		return;
	}

	if (obj.get<jsonxx::Number>("type") == ReqAllPoints)
	{
		OnReqAllPosits();
		return;
	}

	if (obj.get<jsonxx::Number>("type") == ReqAllPointsRet)
	{
		OnReqAllPointsRet(obj.get<jsonxx::String>("posints"));
		return;
	}

	if (obj.get<jsonxx::Number>("type") == GivePoint)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");
		OnGivePosint(x,y,z);
		return;
	}
}

void SocRevivePointComponent::OnPlayerEnter()
{
#ifdef IWORLD_SERVER_BUILD
	LOG_WARNING("SocRevivePointComponent::OnPlayerEnter");
	ClientPlayer* clientplayer = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!clientplayer)
	{
		LOG_WARNING("get bedmgr error");
		return;
	}

	World* pWorld = clientplayer->getWorld();
	if (!pWorld)
	{
		LOG_WARNING("get pWorld error");
		return;
	}

	SocBedMgr* bedmgr = pWorld->GetBedMgr();
	if (!bedmgr)
	{
		LOG_WARNING("get bedmgr error");
		return;
	}

	const std::unordered_map<int, std::vector<WCoord>>& PlayerBed = bedmgr->GetPlayerBed();

	auto itbedpos = PlayerBed.find(clientplayer->getUin());
	if (itbedpos == PlayerBed.end()) return;
	m_positions.clear();
	for (auto pos : itbedpos->second)
	{
		LOG_WARNING("OnPlayerEnter pos %d %d %d", pos.x, pos.y, pos.z);
		m_positions.push_back(pos);
	}

	//OnReqAllPosits();
#endif
}

void SocRevivePointComponent::WritePoint(const WCoord& pos)
{
	m_positions.push_back(pos);
#ifdef IWORLD_SERVER_BUILD
	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)
		return;

	World* pWorld = owner->getWorld();
	if (!pWorld)
	{
		LOG_WARNING("check not pWorld");
		return;
	}

	SocBedMgr* bedmgr = pWorld->GetBedMgr();
	if (!bedmgr)
	{
		LOG_WARNING("check not bedmgr");
		return;
	}

	SocBedData* beddata = bedmgr->GetSocBedDataByPos(pos);
	if (!beddata)
	{
		LOG_WARNING("check not beddata");
		return;
	}

	PB_PlayerCustomHC protoHC;
	protoHC.set_type(NetType);

	jsonxx::Object obj;
	obj << "type" << AddPosition;
	obj << "x" << pos.x;
	obj << "y" << pos.y;
	obj << "z" << pos.z;

	obj << "time" << bedmgr->GetBedLeftTime(pos);
	obj << "itemid" << beddata->itemid;

	protoHC.set_data(obj.json());

	GetGameNetManagerPtr()->sendToClient(owner->getUin(), PB_PLAYER_CUSTOM_HC, protoHC);
#endif
}

void SocRevivePointComponent::RemovePoint(const WCoord& pos)
{
	auto item = std::find_if(m_positions.begin(), m_positions.end(), [pos](WCoord c1) {
		return c1 == pos;
		});

	if (m_positions.end() == item)
		return;

	m_positions.erase(item);

#ifdef IWORLD_SERVER_BUILD
	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)
		return;

	PB_PlayerCustomHC protoHC;
	protoHC.set_type(NetType);

	jsonxx::Object obj;
	obj << "type" << DelPosition;
	obj << "x" << pos.x;
	obj << "y" << pos.y;
	obj << "z" << pos.z;
	protoHC.set_data(obj.json());

	GetGameNetManagerPtr()->sendToClient(owner->getUin(), PB_PLAYER_CUSTOM_HC, protoHC);
#endif
}

void SocRevivePointComponent::SelectPosint(int x, int y, int z)
{
#ifndef IWORLD_SERVER_BUILD
	//前端先校验，然后发给gs确认，没问题会直接复活
	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)
		return;

	World* pWorld = owner->getWorld();
	if (!pWorld) 
		return;

	//WorldBed* socbed = dynamic_cast<WorldBed*>(pWorld->getContainerMgr()->getContainer(WCoord(x,y,z)));
	//if (!socbed)
	//	return;
	//
	//std::chrono::duration<double> time_diff = std::chrono::system_clock::now() - socbed->GetLastUseTime();
	////先测试写死
	//if (time_diff.count() < 20)
	//	return;

	//发给gs校验
	jsonxx::Object obj;
	obj << "type" << CheckPosition;
	obj << "x" << x;
	obj << "y" << y;
	obj << "z" << z;

	PB_PlayerCustomCH protoCH;
	protoCH.set_type(NetType);
	protoCH.set_data(obj.json());

	GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);
#endif
}

void SocRevivePointComponent::GivePosint(int x, int y, int z)
{
#ifndef IWORLD_SERVER_BUILD
	jsonxx::Object obj;
	obj << "type" << GivePoint;
	obj << "x" << x;
	obj << "y" << y;
	obj << "z" << z;

	PB_PlayerCustomCH protoCH;
	protoCH.set_type(NetType);
	protoCH.set_data(obj.json());

	GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);
#endif
}

std::string SocRevivePointComponent::GetPoints()
{
	jsonxx::Array arr;

	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)
	{
		LOG_WARNING("check not owner");
		return "[]";
	}

	World* pWorld = owner->getWorld();
	if (!pWorld)
	{
		LOG_WARNING("check not pWorld");
		return "[]";
	}

	SocBedMgr* bedmgr = pWorld->GetBedMgr();
	if (!bedmgr)
	{
		LOG_WARNING("check not bedmgr");
		return "[]";
	}

	const std::unordered_map<int, std::vector<WCoord>>& PlayerBed = bedmgr->GetPlayerBed();

	auto itbedpos = PlayerBed.find(owner->getUin());
	if (itbedpos == PlayerBed.end()) return "[]";
	m_positions.clear();
	for (auto pos : itbedpos->second)
	{
		LOG_WARNING("OnPlayerEnter pos %d %d %d", pos.x, pos.y, pos.z);
		m_positions.push_back(pos);
	}

	for (const auto& it : m_positions)
	{
		jsonxx::Object item;

		SocBedData* itembeddata = bedmgr->GetSocBedDataByPos(it);
		if (!itembeddata) continue;

		item << "x" << it.x;
		item << "y" << it.y;
		item << "z" << it.z;

		item << "time" << bedmgr->GetBedLeftTime(it);
		item << "itemid" << itembeddata->itemid;

		arr.import(item);
	}

	return arr.json();
}

void SocRevivePointComponent::ReqAllPosints()
{
	jsonxx::Object obj;
	obj << "type" << ReqAllPoints;

	PB_PlayerCustomCH protoCH;
	protoCH.set_type(NetType);
	protoCH.set_data(obj.json());

	GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);
}

void SocRevivePointComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);

	//owner->Event().SubscribeEventWithCreateEvent("interactBlockEnd", this, (MNSandbox::SandboxClassCallback)&SocRevivePointComponent::interactBlockEnd, "SocRevivePointComp_1");

	BindOnTick();
}

void SocRevivePointComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);

}

void SocRevivePointComponent::OnTick() {
	Super::OnTick();
}

void SocRevivePointComponent::OnCheckPosition(int x, int y, int z)
{
	PB_PlayerCustomHC CustomHC;
	CustomHC.set_type(NetType);
	jsonxx::Object obj;
	obj << "type" << CheckPositionRet;

	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)
	{ 
		LOG_WARNING("check not owner");
		return;
	}

	World* pWorld = owner->getWorld();
	if (!pWorld)
	{
		obj << "ret" << 1;
		obj << "msg" << "check not pWorld";
		CustomHC.set_data(obj.json());
		
		GetGameNetManagerPtr()->sendToClient(owner->getUin(), PB_PLAYER_CUSTOM_HC, CustomHC);
		return;
	}

	SocBedMgr* bedmgr = pWorld->GetBedMgr();
	if (!bedmgr)
	{
		obj << "ret" << 2;
		obj << "msg" << "check not bedmgr";
		CustomHC.set_data(obj.json());

		GetGameNetManagerPtr()->sendToClient(owner->getUin(), PB_PLAYER_CUSTOM_HC, CustomHC);
		return;
	}

	WCoord pos(x, y, z);
	if (!bedmgr->IsUseBed(pos))
	{
		obj << "ret" << 3;
		obj << "msg" << std::to_string(bedmgr->GetBedLeftTime(pos));
		CustomHC.set_data(obj.json());

		GetGameNetManagerPtr()->sendToClient(owner->getUin(), PB_PLAYER_CUSTOM_HC, CustomHC);
		return;
	}

	if (bedmgr->GetSocBedDataByPos(pos)->owner != owner->getUin())
	{
		obj << "ret" << 4;
		obj << "msg" << std::to_string(bedmgr->GetBedLeftTime(pos));
		CustomHC.set_data(obj.json());

		GetGameNetManagerPtr()->sendToClient(owner->getUin(), PB_PLAYER_CUSTOM_HC, CustomHC);
		return;
	}

	m_selectPos = pos;
	m_isselect = true;

	bedmgr->GetSocBedDataByPos(pos)->now = std::chrono::system_clock::now();

	obj << "ret" << 0;
	obj << "msg" << "ok";
	CustomHC.set_data(obj.json());
	GetGameNetManagerPtr()->sendToClient(owner->getUin(), PB_PLAYER_CUSTOM_HC, CustomHC);
}

void SocRevivePointComponent::OnReqAllPosits()
{
	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)
	{
		LOG_WARNING("check not owner");
		return;
	}

	PB_PlayerCustomHC CustomHC;
	CustomHC.set_type(NetType);
	jsonxx::Object obj;
	obj << "type" << ReqAllPointsRet;
	obj << "posints" << GetPoints();
	CustomHC.set_data(obj.json());

	GetGameNetManagerPtr()->sendToClient(owner->getUin(), PB_PLAYER_CUSTOM_HC, CustomHC);
}

void SocRevivePointComponent::OnGivePosint(int x, int y, int z)
{
	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)
	{
		LOG_WARNING("check not owner");
		return;
	}

	World* pWorld = owner->getWorld();
	if (!pWorld)
	{
		LOG_WARNING("check not pWorld");
		return;
	}

	SocBedMgr* bedmgr = pWorld->GetBedMgr();
	if (!bedmgr)
	{
		LOG_WARNING("check not bedmgr");
		return;
	}

	SocBedData* beddata = bedmgr->GetSocBedDataByPos(WCoord(x,y,z));
	if (!beddata)
	{
		LOG_WARNING("check not pos");
		return;
	}

	bedmgr->SetSocBedDataOwner(WCoord(x, y, z),0);
	RemovePoint(WCoord(x, y, z));

#ifdef IWORLD_SERVER_BUILD
	int uin = 1000;// 不是存储到玩家 所以填1000
	WorldManager* worldMgr = GetWorldManagerPtr();
	long long owid = worldMgr->getWorldId();
	miniw::bed_data data;
	data.set_x(x);
	data.set_y(y);
	data.set_z(z);
	data.set_itemid(beddata->itemid);
	data.set_owner(-1); //-1是删除

	std::string savedata;
	data.SerializeToString(&savedata);
	g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveBedData, savedata.c_str(), savedata.length(), owid, uin);
#endif
}

void SocRevivePointComponent::OnAddPosition(int x, int y, int z, int time, int itemid)
{
	m_positions.push_back(WCoord(x, y, z));

	jsonxx::Object obj;
	obj << "x" << x;
	obj << "y" << y;
	obj << "z" << z;
	obj << "time" << time;
	obj << "itemid" << itemid;

	//发事件通知,目前只有小地图使用
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("SocRevivePoint_AddPosition",
		MNSandbox::SandboxContext(nullptr)
		.SetData_Number("ret", 1)
		.SetData_String("pos", obj.json())
	);
}

void SocRevivePointComponent::OnDelPosition(int x, int y, int z)
{
	WCoord pos(x, y, z);
	auto item = std::find_if(m_positions.begin(), m_positions.end(), [pos](WCoord c1) {
		return c1 == pos;
		});

	if (m_positions.end() == item)
		return;

	m_positions.erase(item);

	jsonxx::Object obj;
	obj << "x" << x;
	obj << "y" << y;
	obj << "z" << z;

	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("SocRevivePoint_DelPosition",
		MNSandbox::SandboxContext(nullptr)
		.SetData_Number("ret", 1)
		.SetData_String("pos", obj.json())
	);
}

void SocRevivePointComponent::OnCheckPositionRet(int ret, const std::string& msg)
{
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("SocRevivePoint_CheckPositionRet",
		MNSandbox::SandboxContext(nullptr)
		.SetData_Number("ret", ret)
		.SetData_String("msg", msg)
	);
}

void SocRevivePointComponent::OnReqAllPointsRet(const std::string& posints)
{
	jsonxx::Array arr;
	arr.parse(posints);
	m_positions.clear();
	for (int i = 0; i < arr.size(); i++)
	{
		jsonxx::Object item = arr.get<jsonxx::Object>(i);
		int x = item.get<jsonxx::Number>("x");
		int y = item.get<jsonxx::Number>("y");
		int z = item.get<jsonxx::Number>("z");

		m_positions.push_back(WCoord(x,y,z));
	}

	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("SocRevivePoint_ReqAllPosints",
		MNSandbox::SandboxContext(nullptr)
		.SetData_Number("ret", 1)
		.SetData_String("posints", posints)
	);
}

MNSandbox::SandboxResult SANDBOXAPI SocRevivePointComponent::interactBlockEnd(MNSandbox::SandboxContext context)
{
	//必须是bed 类型才记录
	int blockid = (int)context.GetData_Number("blockid");
	BlockDef* blockdef = GetDefManagerProxy()->getBlockDef(blockid);
	if (!blockdef)
		return MNSandbox::SandboxResult(this, true);

	if (blockdef->Type != "bed")
		return MNSandbox::SandboxResult(this, true);

	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)
		return MNSandbox::SandboxResult(this, true);

	World* pWorld = owner->getWorld();
	if (!pWorld) 
		return MNSandbox::SandboxResult(this, true);
	
	WCoord blockpos = context.GetData_UserObject<WCoord>("blockpos");

	//添加 bed信息
	if (SocBedMgr *bedmgr = pWorld->GetBedMgr())
		bedmgr->AddBed(blockpos, blockid);

	return MNSandbox::SandboxResult(this, true);
}
