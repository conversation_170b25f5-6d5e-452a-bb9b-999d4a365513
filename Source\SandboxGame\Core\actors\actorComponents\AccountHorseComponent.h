#ifndef __ACCOUNT_HORSE_COMPONENT_H__
#define __ACCOUNT_HORSE_COMPONENT_H__

#include "world_types.h"
#include <vector>

#include "WorldRole_generated.h"
#include "ActorComponent_Base.h"

class ClientPlayer;
class ClientActor;

//namespace game {
//	namespace ch {
//		class PB_CloseFullyCustomModelUICH;
//		class PB_CloseEditActorModelCH;
//	}
//}

class AccountHorseComponent : public ActorComponentBase //tolua_exports
{//tolua_exports
public:
	DECLARE_COMPONENTCLASS(AccountHorseComponent)

	AccountHorseComponent();

	//void onTick();
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

	virtual void summonAccountHorse(int horseid);
	virtual void accountHorseEgg();

	ClientActor* summonShapeShiftHorse(int horseid);
	void updateAccountHorse(int horseid, float hp, int addlivetick, int shieldcoolingticks);
	void resetAccountHorseLiveTick(int horseid, int t);
	void setAccountHorseEquip(int horseid, int index, int itemid);
	int getAccountHorseLiveAge(int horseid);
	void clearAccountHorseLiveAge(int horseid);
	//void onCloseFullyCustomModelUI(const game::ch::PB_CloseFullyCustomModelUICH &closeFullyCustomModelUICH);
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::AccountHorseData>>> save(flatbuffers::FlatBufferBuilder &builder);
	void load(const FBSave::ActorPlayer* wrole);

	bool isMyAccountHorse(long long objid){ return getCurAccountHorse() == objid;};
	void setCurAccountHorse(WORLD_ID objid){ m_CurAccountHorse = objid;};
	WORLD_ID getCurAccountHorse()const { return m_CurAccountHorse;};
	int getAccountHorseCount()const { return m_AccountHorses.size();};

protected:
	void notifyAccountHorse2Self(int horseid);
	void sendUIDisplayHorseData();

	struct AccountHorseInfo
	{
		int horseid;
		float hp;
		int liveticks; //生存时间
		int shieldcoolingticks; //护盾冷却时间
		int equips[3];
	};
	
	WORLD_ID m_CurAccountHorse;
	std::vector<AccountHorseInfo>m_AccountHorses;
};//tolua_exports


class MPAccountHorseComponent : public AccountHorseComponent
{
	DECLARE_COMPONENTCLASS(MPAccountHorseComponent)
public:

	MPAccountHorseComponent();

	virtual void summonAccountHorse(int horseid) override;
	virtual void accountHorseEgg() override;
};
#endif