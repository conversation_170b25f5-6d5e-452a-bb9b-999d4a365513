#include "ActorLocoMotion.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "OgreUtils.h"
#include "Entity/OgreModel.h"
#include "Entity/OgreEntity.h"
#include "BlockScene.h"
#include "ClientActorManager.h"
#include "ClientPlayer.h"
#include "backpack.h"
#include "ActorAttrib.h"
#include "ClientMob.h"
#include "special_blockid.h"
#include "EffectManager.h"
#include "ActorCSProto.h"
#include "ClientItem.h"
#include "BaseItemMesh.h"
#include "ProjectileLocoMotion.h"
#include "DefManagerProxy.h"
#include "OgreRay.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "ClientActorImpulse.h"
#include "Graphics/LegacyOgreRenderTypes.h"
#include "OgrePrerequisites.h"
#include "OgreBezierCurve.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "GameCamera.h"
#include "PlayerControl.h"
#include "CameraModel.h"
//#include "MpGameSurvive.h"

#include "Math/Matrix4x4f.h"

#include "IClientGameManagerInterface.h"
#include "SandboxCoreDriver.h"

#include "ClientActorHelper.h"
#include "LivingAttrib.h"
#include "LuaInterfaceProxy.h"

using namespace Rainbow;

using namespace MNSandbox;

ClientActorImpulse::ClientActorImpulse() : m_nLifeTick(6*20), m_nAttackCount(0)
{

}

ClientActorImpulse::~ClientActorImpulse()
{

}

flatbuffers::Offset<FBSave::SectionActor> ClientActorImpulse::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	auto startpos = WCoordToCoord3(m_StartPos);
	auto obj = FBSave::CreateActorImpulse(builder, basedata, m_ShootingActorID, m_ItemID, &startpos, m_nAttackCount, m_KnockbackStrength, m_AttackPoints, m_BuffAttackAdd, m_AttachedEffect, m_FireLv);
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorImpulse, obj.Union());
}


bool ClientActorImpulse::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorImpulse*>(srcdata);
	loadActorCommon(src->basedata());
	init(src->itemid(), NULL);
	m_ShootingActorID = src->shooter();
	m_StartPos = Coord3ToWCoord(src->startpos());
	m_nAttackCount = src->attackCount();
	m_KnockbackStrength = src->knockback();
	m_AttackPoints = src->atkpoint();
	m_BuffAttackAdd = src->buffatk();
	m_AttachedEffect = src->infire();
	m_FireLv = src->firelv();
	playMotion(m_ProjectileDef->TailEffect.c_str());
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (loco)
	loco->syncPos = getLocoMotion()->getPosition();
	return true;
}

void ClientActorImpulse::onImpactWithActor(ClientActor *actor, const std::string& partname)
{
	onAttackActor(actor);
}

void ClientActorImpulse::onAttackActor(ClientActor *actor)
{
	if(actor && (actor->getObjId() != m_ShootingActorID || m_nAttackCount > 0) && this->getMasterObjId() != actor->getObjId())
	{
		//道具技能释放
		useItemSkill(actor);
		doTrigger();
		ClientActorProjectile::onImpactWithActor(actor, "");		
		setNeedClear();

		ActorLiving* actorliving = dynamic_cast<ActorLiving*>(actor);
		if (actorliving != NULL && actorliving->getLivingAttrib() != NULL)
		{
			actorliving->getLivingAttrib()->addArrowById(m_ItemID);
		}
	} 
}

ClientPlayer *ClientActorImpulse::getShootingPlayer()
{
	ClientActor *actor = getShootingActor(); 
	if(!actor)
	{
		return NULL;
	}
	ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
	return player;
}

void ClientActorImpulse::onImpactWithBlock(const WCoord *blockpos, int face)
{ 
	int blockid = getWorld()->getBlockID(*blockpos);
	if(blockid == BLOCK_UNLOAD)
	{
		setNeedClear();  
		return;
	}
	doTrigger();
	// 投射物击碎方块判断
	const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);
	auto vel = getLocoMotion()->m_Motion / (getLocoMotion()->getLookDir().Normalize());
	ClientActor *shooter = getShootingActor();
	ClientPlayer *player = dynamic_cast<ClientPlayer*>(shooter);
	auto game = GetIClientGameManagerInterface()->getICurGame(GameType::MpGameSurvive);
	bool gamerule_forbid = GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_BLOCKDESTROY) == 0;
	if (player != NULL) gamerule_forbid = !player->checkActionAttrState(ENABLE_DESTROYBLOCK); //将设置界面的权限赋值到玩家身上

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getGuestMode",
		SandboxContext(nullptr));
	bool GuestModeFlag = false;
	if (result.IsExecSuccessed())
	{
		GuestModeFlag = result.GetData_Bool();
	}
	if (!gamerule_forbid
		&& (!GuestModeFlag || GuestModeFlag && player && game && game->isHost((int)player->getObjId()))
		&& blockdef && blockdef->Breakable && m_ProjectileDef && m_ProjectileDef->Break)
		//&& blockdef->Hardness < vel.x / g_projectile_factor + g_projectile_ratio)
	{
		m_pWorld->getEffectMgr()->playBlockDestroyEffect(0, *blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2), DIR_NEG_X, 40);
		if (!blockdef->DigSound.empty()) m_pWorld->getEffectMgr()->playSound(*blockpos*BLOCK_SIZE, blockdef->DigSound.c_str(), GSOUND_DESTROY);
		getWorld()->destroyBlock(*blockpos, BLOCK_MINE_NOTOOL);
		if (m_ItemID == 12002 || m_ItemID == 12063)
		{
			const ToolDef *toolDef = GetDefManagerProxy()->getToolDef(m_ItemID);
			if (toolDef)
			{
				m_Durable -= 2 * toolDef->AtkDuration;
				m_Durable = m_Durable < 0 ? 0 : m_Durable;
			}
			if (m_Durable == 0) setNeedClear();
		}
		else setNeedClear();
		return;
	}
	else
	{
		m_nAttackCount++;
		if (m_nAttackCount > 6)
		{
			setNeedClear(); 
			return;
		}
		else
		{
			ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
			if (loc)
			{
				loc->m_InGround = false;
			}
		}
		Rainbow::Quaternionf quat = Rainbow::Quaternionf::identity;
		getLocoMotion()->getRotation(quat);
		if (face == DIR_NEG_X || face == DIR_POS_X)
		{
			//quat.rotate(Rainbow::Vector3f(1.0f, 0.0f, 0.0f), 180);
			quat = AxisAngleToQuaternionf(Rainbow::Vector3f(1.0f, 0.0f, 0.0f), Deg2Rad(180)) * quat;
		}
		else if (face == DIR_NEG_Z || face == DIR_POS_Z)
		{
			//quat.rotate(Rainbow::Vector3f(0.0f, 0.0f, 1.0f), 180);
			quat = AxisAngleToQuaternionf(Rainbow::Vector3f(0.0f, 0.0f, 1.0f), Deg2Rad(180)) * quat;
		}
		else if (face == DIR_NEG_Y || face == DIR_POS_Y)
		{
			//quat.rotate(Rainbow::Vector3f(0.0f, 1.0f, 0.0f), 180);
			quat = AxisAngleToQuaternionf(Rainbow::Vector3f(0.0f, 1.0f, 0.0f), Deg2Rad(180)) * quat;
		}
		// todo_yanxiongjian
		Direction2PitchYaw(&getLocoMotion()->m_RotateYaw, &getLocoMotion()->m_RotationPitch, quat.GetAxisZ());
		//移动发射物
		Rainbow::Vector3f &motion = getLocoMotion()->m_Motion;
		// todo_yanxiongjian
		motion = quat.GetAxisZ();
		const ProjectileDef *projectileDef = GetDefManagerProxy()->getProjectileDef(m_ItemID, true);
		if (projectileDef)
		{
		   motion *= projectileDef->InitSpeed;
		}
		else
		{
		   motion *= 300.0f;	
		}
		
		Rainbow::Vector3f dir = motion;
		dir  = MINIW::Normalize(dir);
		dir *= BLOCK_SIZE/2;
		for (int i = 0; i<10; i++)
		{
			WCoord position = getLocoMotion()->getPosition();
			position.x += (int)dir.x;
			position.y += (int)dir.y;
			position.z += (int)dir.z;
			getLocoMotion()->setPosition(position.x, position.y, position.z);
			int blockid = getWorld()->getBlockID(CoordDivBlock(position));
			if (IsAirBlockID(blockid))
			{
				break;
			}
		}
	}
}

void ClientActorImpulse::onCollideWithPlayer(ClientActor *player)
{
	if (!player)
	{
		return;
	}
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		if (pTempPlayer->getObjId() != m_ShootingActorID || m_nAttackCount > 1)
		{
			onAttackActor(pTempPlayer);
		}
	}
}

void ClientActorImpulse::update(float dtime)
{
	ClientActor::update(dtime);

	if (!m_EntityModel)
		return;

	ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
#ifndef IWORLD_SERVER_BUILD
	m_EntityModel->SetPosition(loc->getFramePosition());
#endif
	float angle = 180;
#ifndef IWORLD_SERVER_BUILD
	m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch+angle, 0);
	m_EntityModel->UpdateTick(Rainbow::TimeToTick(dtime));
#endif
}

void ClientActorImpulse::tick()
{
	ClientActorProjectile::tick();

	if (m_pWorld->isRemoteMode())
		return;
	ClientPlayer *player = getShootingPlayer();

	if(m_nLifeTick-- == 0)
	{
		setNeedClear(); 
	}
}

void ClientActorImpulse::init(int itemid, ClientActor *shooter)
{
	m_ProjectileDef = GetDefManagerProxy()->getProjectileDef(itemid, true);
	m_ItemID = m_ProjectileDef->ID;
	if(itemid != m_ItemID)
	{
		auto tooDef = GetDefManagerProxy()->getToolDef(m_ItemID);
		if(tooDef)
			m_Durable = tooDef->Duration;
		else
			m_Durable = -1;
	}
#ifndef IWORLD_SERVER_BUILD
	char modelPath[128];
	sprintf(modelPath, "itemmods/%s/body.omod", m_ProjectileDef->Model.c_str());
	m_EntityModel = g_BlockMtlMgr.getEntity(modelPath);
#endif

	// todo_yanxiongjian
	if(m_EntityModel && m_EntityModel->GetMainModel())
		m_EntityModel->GetMainModel()->SetInstanceData(Rainbow::Vector4f(1.0f, 1.0f, 0, 0)); 

	ProjectileLocoMotion *locmove = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	locmove->setBound((int)m_ProjectileDef->Bounds, (int)m_ProjectileDef->Bounds);
	locmove->m_yOffset = locmove->m_BoundHeight/2;
	locmove->m_Gravity = m_ProjectileDef->Gravity;
	locmove->m_SpeedDecay = m_ProjectileDef->SpeedDecay;
	locmove->m_TriggerCondition = (TRIGGER_CONDITION)m_ProjectileDef->TriggerCondition;

	m_ShootingActorID = 0;
	m_ImpactTimeMark = -1;
	m_AttachedEffect = 0;

	m_StartPos = WCoord(0,0,0);
	m_AttackPoints = 0;
	m_BuffAttackAdd = 0;
	m_KnockbackStrength = 0;
	m_BuffId = 0;

	// 投掷物发射前，处理技能相关
	processSkillOnInit(shooter);
}

//void ClientActorImpulse::onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum)
//{
//	if (m_EntityModel) 
//	{
//		presult->addRenderable(m_EntityModel, MINIW::RL_SCENE, NULL);
//	}
//	else
//		return;
//}

ClientActorImpulse *ClientActorImpulse::shootImpulseAuto(int itemid, World *pworld, const WCoord &pos, const Rainbow::Vector3f &dir, float speed, float deviation, long long shooterObjId)
{
	if (pworld == nullptr) return nullptr;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return nullptr;

	ClientActorImpulse *projectile = SANDBOX_NEW(ClientActorImpulse);
	projectile->init(itemid);

	actorMgr->spawnActor(projectile, pos, 0, 0);
	projectile->m_StartPos = projectile->getPosition();

	ProjectileLocoMotion *locomove = static_cast<ProjectileLocoMotion *>(projectile->getLocoMotion());
	locomove->setThrowableHeading(dir, speed, deviation);

	projectile->playMotion(projectile->m_ProjectileDef->TailEffect.c_str());
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
	{
		// 近战、远程、元素伤害
		int damageType = projectile->m_ProjectileDef->DamageType;
		if (projectile->m_ProjectileDef->AttackType == 0)
		{
			if (damageType == 0 || damageType == 2 || damageType == 3)
			{
				assert(false && "Projectiledef damageType error!");
			}
			projectile->m_AttackPointsNew[damageType] = projectile->m_ProjectileDef->AttackValue;
			projectile->m_atkType = (1 << damageType);
		}
		// 独立爆炸伤害
		else
		{
			projectile->m_atkType = (1 << ATTACK_EXPLODE);
			if (damageType < 4)
			{
				projectile->m_ExplodePoints[0] = projectile->m_ProjectileDef->AttackValue;
			}
			else
			{
				projectile->m_ExplodePoints[damageType - 3] = projectile->m_ProjectileDef->AttackValue;
			}
		}

		projectile->m_strength = 1.0f;
	}
	else
	{
		projectile->m_AttackPoints = projectile->m_ProjectileDef->AttackValue;
	}
	if (projectile->m_ProjectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}
	// TriggerCondition==5 不触发
	else if (projectile->m_ProjectileDef->TriggerCondition == 5)
	{
		projectile->m_ImpactTimeMark = -1;
	}
	projectile->setMasterObjId(shooterObjId);
	return projectile;
}
