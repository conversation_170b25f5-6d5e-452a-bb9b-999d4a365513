#include "PumpkinHorseLocomotion.h"
#include "world.h"
#include "ActorPumpkinHorse.h"
using namespace MINIW;
static Vector3f s_MotionDecay(0.9f, 0.98f, 0.9f);
IMPLEMENT_COMPONENTCLASS(PumpkinHorseLocomotion)
void PumpkinHorseLocomotion::tick()
{
	HorseLocomotion::tick();
	WCoord oldpos = m_Position;
	float motionlen = Rainbow::Sqrt(m_Motion.x*m_Motion.x + m_Motion.z*m_Motion.z);

	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		m_Motion *= s_MotionDecay;
	}
	else
	{
		if (!getOwnerActor())
			return;

		if (m_MaxSpeed == 0)
		{
			ActorPumpkinHorse *horse = static_cast<ActorPumpkinHorse *>(getOwnerActor());
			if (horse)
			{
				m_MaxSpeed = (float)(horse->getRiddenLandSpeed() / 10);  //每tick的速度
				speedMultiplier = m_MaxSpeed;
			}
		}

		float MAX_MOTION_LEN = m_MaxSpeed > 0 ? m_MaxSpeed : 50.0f;

		m_Motion.y = calGravityMotionY(m_Motion.y);



		float newlen = Rainbow::Sqrt(m_Motion.x*m_Motion.x + m_Motion.z*m_Motion.z);

		if (newlen > MAX_MOTION_LEN)
		{
			float t = MAX_MOTION_LEN / newlen;
			m_Motion.x *= t;
			m_Motion.z *= t;
			newlen = MAX_MOTION_LEN;
		}

		if (newlen > motionlen && speedMultiplier < MAX_MOTION_LEN)
		{
			speedMultiplier += (MAX_MOTION_LEN - speedMultiplier) / MAX_MOTION_LEN;

			if (speedMultiplier > MAX_MOTION_LEN)
			{
				speedMultiplier = MAX_MOTION_LEN;
			}
		}
		else
		{
			speedMultiplier -= (speedMultiplier - MAX_MOTION_LEN / 5.0f) / MAX_MOTION_LEN;

			if (speedMultiplier < MAX_MOTION_LEN / 5.0f)
			{
				speedMultiplier = MAX_MOTION_LEN / 5.0f;
			}
		}

		doMoveStep(m_Motion);
		m_Motion *= s_MotionDecay;
	}
}
