#include "SandboxEventDispatcherManager.h"
#include "BlockPolaroidPhotoFrame.h"
#include "container_polaroid.h"
#include "ClientActorHelper.h"
#include "ClientActorDef.h"
#include "SandBoxManager.h"
#include "PlayerControl.h"
#include "SandboxIdDef.h"
#include "GameCamera.h"
#include "section.h"
#include "world.h"
#include "chunk.h"

//MINI_CLUB_INDEX这个id用跳舞机的，暂时不会有问题 
ContainerPolaroidFrame::ContainerPolaroidFrame() : WorldContainer(MINI_CLUB_INDEX)
{
	resetPolaroidParam();
	listenPhotoStatusChange();
}

ContainerPolaroidFrame::ContainerPolaroidFrame(const WCoord& blockpos) : WorldContainer(blockpos, MINI_CLUB_INDEX)
{
	resetPolaroidParam();
	listenPhotoStatusChange();
}

void ContainerPolaroidFrame::listenPhotoStatusChange()
{
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("polaroidPictureStateChange");
	m_PicStateChangeCB = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("polaroidPictureStateChange", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {
		std::string resourceId = context.GetData_String("resourceid");
		int openSvr = context.GetData_Number("opensvr");
		bool ret = false;
		if (resourceId != m_PhotoURL)
			return MNSandbox::SandboxResult(nullptr, false);

		updatePhoto3DTexture();

		return MNSandbox::SandboxResult(nullptr, true);
	});
}

ContainerPolaroidFrame::~ContainerPolaroidFrame()
{
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe("polaroidPictureStateChange", m_PicStateChangeCB);
}

void ContainerPolaroidFrame::resetPolaroidParam()
{
	m_Dev3DTextureID = 0;
	m_PhotoURL = "";
	m_PhotoStretchMode = 0;
	//默认样式
	m_FrameStyle = "";
	m_FrameWidth = 0;
	m_FrameHeight = 0;
	m_IsRecreate = false;
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerPolaroidFrame::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	std::vector<flatbuffers::Offset<FBSave::PolaroidPartFrameInfo>> framePartsInfo;
	for (auto it = m_FramePartsInfo.begin(); it != m_FramePartsInfo.end(); it++) {
		const FBSave::Coord3 partBlockPos = WCoordToCoord3(it->blockPos);
		auto partInfo = FBSave::CreatePolaroidPartFrameInfo(builder, &partBlockPos, it->edgeType);
		framePartsInfo.push_back(partInfo);
	}

	auto actor = FBSave::CreateContainerPolaroidFrame(builder, basedata, builder.CreateString(m_PhotoURL), m_PhotoStretchMode, builder.CreateString(m_FrameStyle), m_FrameWidth, m_FrameHeight, builder.CreateVector(framePartsInfo));
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerPolaroidFrame, actor.Union());
}

bool ContainerPolaroidFrame::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerPolaroidFrame*>(srcdata);
	loadContainerCommon(src->basedata());

	m_PhotoURL = src->photourl()->c_str();
	m_PhotoStretchMode = src->photostretchmode();
	m_FrameStyle = src->frameurl()->c_str();
	m_FrameWidth = src->framewidth();
	m_FrameHeight = src->frameheight();

	m_FramePartsInfo.clear();
	auto framePartsInfo = src->framepartsinfo();
	if (framePartsInfo->size() > 0)
	{
		for (int index = 0; index < framePartsInfo->size(); index++) {
			auto partInfo = framePartsInfo->Get(index);
			if (!partInfo)
				continue;
			if (!partInfo->blockpos())
				continue;

			WCoord blockPos = Coord3ToWCoord(partInfo->blockpos());
			FrameEdgeType edgeType = (FrameEdgeType)partInfo->edgetype();
			PolaroidPartFrameInfo partFrameInfo(blockPos, edgeType);
			m_FramePartsInfo.push_back(partFrameInfo);
		}
	}
	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerPolaroidFrame::ConvertModResourceId(const void* srcdata, SAVE_BUFFER_BUILDER& builder)
{
	bool loadret = load(srcdata);

	char ret[250] = { 0 };
	MINIW::ScriptVM::game()->callFunction("modEditorToolReplaceVbpResId", "s>s", m_PhotoURL.c_str(), ret);
	m_PhotoURL = ret;

	return save(builder);
}

int ContainerPolaroidFrame::getObjType() const
{
	return OBJ_TYPE_STRING;
}

void ContainerPolaroidFrame::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);

	if (pworld->onClient())
	{
	}

	if (m_IsRecreate)
		recreatePolaroidFrame();

	updatePhoto3DTexture();
	//主机同步到客机
	if (!pworld->isRemoteMode())
	{
		syncToOtherPlayerFrameChange();
	}

	registerUpdateTick();
}

void ContainerPolaroidFrame::leaveWorld()
{
	if (!m_World)
		return;

	m_FramePartsInfo.clear();
	m_FrameWidth = 0;
	m_FrameHeight = 0;
	m_FrameStyle = "";
	m_PhotoURL = "";

	WorldContainer::leaveWorld();
}

bool ContainerPolaroidFrame::doOpenContainer()
{
	if (!m_World)
		return false;

	BaseContainer::SetLuaOpenContainer("WorldContainer", this);

	Block block = m_World->getBlock(m_BlockPos);

	std::string photoUrl = getPhotoURL();
	int photoStretchMode = getPhotoStretchMode();
	std::string frameStyle = getFrameURL();
	unsigned int width, height;
	getFrameSize(width, height);
	MINIW::ScriptVM::game()->callFunction("OpenPhotoFrameContainerWithInfo", "isisiiiii", 
		block.getResID(), photoUrl.c_str(), photoStretchMode, frameStyle.c_str(), width, height,
		m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
	return true;
}


bool ContainerPolaroidFrame::isEnoughSpacePlaceFrame(int width, int height, int blockDir)
{
	if (m_World == NULL)
		return false;

	if (width == 0 || height == 0)
		return false;

	if (width == 1 && height == 1)
	{
		return true;
	}
	else if (width == 2 && height == 1)
	{
		WCoord partFramePos(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
		if (blockDir == DirectionType::DIR_NEG_X)
		{
			partFramePos.z = partFramePos.z - 1;
		}
		else if (blockDir == DirectionType::DIR_POS_X)
		{
			partFramePos.z = partFramePos.z + 1;
		}
		else if (blockDir == DirectionType::DIR_NEG_Z)
		{
			partFramePos.x = partFramePos.x + 1;
		}
		else if (blockDir == DirectionType::DIR_POS_Z)
		{
			partFramePos.x = partFramePos.x - 1;
		}
		int blockId = m_World->getBlock(partFramePos).getResID();
		if ((blockId == ITEM_POLAROID_FRAME || blockId == ITEM_POLAROID_RARE_FRAME || blockId == ITEM_POLAROID_INNER_FRAME) && m_FramePartsInfo.size() > 0)
			return isBlockBelongToMe(partFramePos);
		return blockId == BLOCK_AIR;
	}
	else if (width == 1 && height == 2)
	{
		WCoord partFramePos(m_BlockPos.x, m_BlockPos.y + 1, m_BlockPos.z);
		int blockId = m_World->getBlock(partFramePos).getResID();
		if ((blockId == ITEM_POLAROID_FRAME || blockId == ITEM_POLAROID_RARE_FRAME || blockId == ITEM_POLAROID_INNER_FRAME) && m_FramePartsInfo.size() > 0)
			return isBlockBelongToMe(partFramePos);
		return blockId == BLOCK_AIR;
	}
	else
	{
		int tempWidth = width - 1;
		int upWidthLimit = math::ceil(tempWidth / 2.0);
		int downWidthLimit = -(tempWidth / 2);
		int upHeightLimit = height;
		int downHeightLimit = 0;

		if (blockDir == DirectionType::DIR_NEG_X || blockDir == DirectionType::DIR_POS_X)
		{
			for (int indexZ = downWidthLimit; indexZ <= upWidthLimit; indexZ++)
			{
				for (int indexY = downHeightLimit; indexY < upHeightLimit; indexY++)
				{
					WCoord framePartPos;
					if (blockDir == DirectionType::DIR_NEG_X)
						framePartPos = WCoord(m_BlockPos.x, m_BlockPos.y + indexY, m_BlockPos.z - indexZ);
					else
						framePartPos = WCoord(m_BlockPos.x, m_BlockPos.y + indexY, m_BlockPos.z + indexZ);

					if (framePartPos == m_BlockPos)
						continue;

					int blockId = m_World->getBlock(framePartPos).getResID();
					//如果是相框，并且是自己的一部分就继续往下查
					if ((blockId == ITEM_POLAROID_FRAME || blockId == ITEM_POLAROID_RARE_FRAME || blockId == ITEM_POLAROID_INNER_FRAME) && m_FramePartsInfo.size() > 0)
					{
						if (isBlockBelongToMe(framePartPos))
							continue;
					}

					if (blockId != BLOCK_AIR)
						return false;
				}
			}
		}
		else if (blockDir == DirectionType::DIR_NEG_Z || blockDir == DirectionType::DIR_POS_Z)
		{
			for (int indexX = downWidthLimit; indexX <= upWidthLimit; indexX++)
			{
				for (int indexY = downHeightLimit; indexY < upHeightLimit; indexY++)
				{
					WCoord framePartPos;
					if (blockDir == DirectionType::DIR_NEG_Z)
						framePartPos = WCoord(m_BlockPos.x - indexX, m_BlockPos.y + indexY, m_BlockPos.z);
					else
						framePartPos = WCoord(m_BlockPos.x + indexX, m_BlockPos.y + indexY, m_BlockPos.z);

					if (framePartPos == m_BlockPos)
						continue;
					
					int blockId = m_World->getBlock(framePartPos).getResID();
					//如果是相框，并且是自己的一部分就继续往下查
					if ((blockId == ITEM_POLAROID_FRAME || blockId == ITEM_POLAROID_RARE_FRAME || blockId == ITEM_POLAROID_INNER_FRAME) && m_FramePartsInfo.size() > 0)
					{
						if (isBlockBelongToMe(framePartPos))
							continue;
					}

					if (blockId != BLOCK_AIR)
						return false;
				}
			}
		}
	}
	return true;
}

bool ContainerPolaroidFrame::changeFrameSize(int width, int height)
{
	if (m_World == NULL || g_pPlayerCtrl == NULL)
		return false;

	if (width == m_FrameWidth && height == m_FrameHeight)
		return false;

	Block blockInfo = m_World->getBlock(m_BlockPos);
	int blockId = blockInfo.getResID();
	int blockData = blockInfo.getData();
	if ((blockData & 8) == 0)
		return false;
	int blockDir = blockData & 7;
	if (blockDir < DirectionType::DIR_NEG_X || blockDir > DirectionType::DIR_POS_Y)
		return false;
	
	if (blockDir == DirectionType::DIR_NEG_Y || blockDir == DirectionType::DIR_POS_Y)
	{
		int rotateYaw = g_pPlayerCtrl->getCamera()->getRotateYaw();
		if (45 <= rotateYaw && rotateYaw < 135)
		{
			blockDir = DirectionType::DIR_NEG_X;
		}
		else if (135 <= rotateYaw && rotateYaw < 225)
		{
			blockDir = DirectionType::DIR_POS_Z;
		}
		else if (225 <= rotateYaw && rotateYaw < 315)
		{
			blockDir = DirectionType::DIR_POS_X;
		}
		else if (rotateYaw < 45 || rotateYaw >= 315)
		{
			blockDir = DirectionType::DIR_NEG_Z;
		}
	}

	if (!isEnoughSpacePlaceFrame(width, height, blockDir))
		return false;

	destroyFrameParts();

	m_FrameWidth = width;
	m_FrameHeight = height;

	syncToOtherPlayerFrameChange((DirectionType)blockDir);

	//客机就同步到主机去
	if (m_World->isRemoteMode())
	{
		return false;
	}

	//宽或者高为1的情况特殊处理下(因为至允许放置1*1，2*1，1*2这三种，而且没有其他种为一情况的模型)，其他是通用的
	if (width == 1 || height == 1)
	{
		createSpecialFrame(m_World, blockId, blockData, blockDir);
	}
	else
	{
		createNormalFrame(m_World, blockId, blockData, blockDir);
	}

	Chunk* pChunk = m_World->getChunk(m_BlockPos);
	if (pChunk)
		pChunk->m_Dirty = true;

	//刷新本section，因为有可能核心和其他部分不在一个section
	for (const PolaroidPartFrameInfo& info : m_FramePartsInfo)
	{
		Section* pSection = m_World->getSection(info.blockPos);
		if (pSection)
			pSection->setMeshInvalid(false);
	}

	updatePhoto3DTexture();

	return true;
}

void ContainerPolaroidFrame::createSpecialFrame(World* pWorld, int blockId, int blockData, int blockDir)
{
	if (m_FrameWidth != 1 && m_FrameHeight != 1)
		return;

	if (pWorld->isRemoteMode())
		return;

	if (m_FrameWidth == 1 && m_FrameHeight == 1)
	{
		FrameEdgeType singleEdgeType = FrameEdgeType::NONE_EDGE;
		if (blockDir == DirectionType::DIR_NEG_X)
		{
			singleEdgeType = FrameEdgeType::SINGLE_NEG_X;
		}
		else if (blockDir == DirectionType::DIR_POS_X)
		{
			singleEdgeType = FrameEdgeType::SINGLE_POS_X;
		}
		else if (blockDir == DirectionType::DIR_NEG_Z)
		{
			singleEdgeType = FrameEdgeType::SINGLE_NEG_Z;
		}
		else if (blockDir == DirectionType::DIR_POS_Z)
		{
			singleEdgeType = FrameEdgeType::SINGLE_POS_Z;
		}
		PolaroidPartFrameInfo partFrameInfo(m_BlockPos, singleEdgeType);
		m_FramePartsInfo.push_back(partFrameInfo);
	}
	else if (m_FrameWidth == 2 && m_FrameHeight == 1)
	{
		WCoord partFramePos(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
		
		FrameEdgeType coreEdgeType = FrameEdgeType::NONE_EDGE;
		if (blockDir == DirectionType::DIR_NEG_X)
		{
			partFramePos.z = partFramePos.z - 1;
			coreEdgeType = FrameEdgeType::LEFT_CLOSURE_NEG_X;
		}
		else if (blockDir == DirectionType::DIR_POS_X)
		{
			partFramePos.z = partFramePos.z + 1;
			coreEdgeType = FrameEdgeType::LEFT_CLOSURE_POS_X;
		}
		else if (blockDir == DirectionType::DIR_NEG_Z)
		{
			partFramePos.x = partFramePos.x + 1;
			coreEdgeType = FrameEdgeType::LEFT_CLOSURE_NEG_Z;
		}
		else if (blockDir == DirectionType::DIR_POS_Z)
		{
			partFramePos.x = partFramePos.x - 1;
			coreEdgeType = FrameEdgeType::LEFT_CLOSURE_POS_Z;
		}
		
		FrameEdgeType edgeType = FrameEdgeType::RIGHT_CLOSURE;
		
		pWorld->setBlockAll(partFramePos, blockId, blockDir);
		PolaroidPartFrameInfo partFrameInfo(partFramePos, edgeType);
		m_FramePartsInfo.push_back(partFrameInfo);

		PolaroidPartFrameInfo corePartFrameInfo(m_BlockPos, coreEdgeType);
		m_FramePartsInfo.push_back(corePartFrameInfo);
	}
	else if (m_FrameWidth == 1 && m_FrameHeight == 2)
	{
		WCoord partFramePos(m_BlockPos.x, m_BlockPos.y + 1, m_BlockPos.z);
		FrameEdgeType edgeType = FrameEdgeType::TOP_CLOSURE;
		FrameEdgeType coreEdgeType = FrameEdgeType::NONE_EDGE;
		if (blockDir == DirectionType::DIR_NEG_X)
		{
			coreEdgeType = FrameEdgeType::BOTTOM_CLOSURE_NEG_X;
		}
		else if (blockDir == DirectionType::DIR_POS_X)
		{
			coreEdgeType = FrameEdgeType::BOTTOM_CLOSURE_POS_X;
		}
		else if (blockDir == DirectionType::DIR_NEG_Z)
		{
			coreEdgeType = FrameEdgeType::BOTTOM_CLOSURE_NEG_Z;
		}
		else if (blockDir == DirectionType::DIR_POS_Z)
		{
			coreEdgeType = FrameEdgeType::BOTTOM_CLOSURE_POS_Z;
		}

		pWorld->setBlockAll(partFramePos, blockId, blockDir);
		PolaroidPartFrameInfo partFrameInfo(partFramePos, edgeType);
		m_FramePartsInfo.push_back(partFrameInfo);

		PolaroidPartFrameInfo corePartFrameInfo(m_BlockPos, coreEdgeType);
		m_FramePartsInfo.push_back(corePartFrameInfo);
	}
}

void ContainerPolaroidFrame::createNormalFrame(World* pWorld, int blockId, int blockData, int blockDir)
{
	if (pWorld == NULL)
		return;
	if (pWorld->isRemoteMode())
		return;

	auto GetFrameEdgeType = [blockDir](int downWidthLimit, int upWidthLimit, int downHeightLimit, int upHeightLimit, int indexI, int indexJ) -> FrameEdgeType
	{
		FrameEdgeType edgeType = FrameEdgeType::NONE_EDGE;
		if (indexI == downWidthLimit && indexJ == downHeightLimit)
		{
			if (blockDir == DirectionType::DIR_NEG_X)
			{
				edgeType = FrameEdgeType::LEFT_BOTTOM_NEG_X;
			}
			else if (blockDir == DirectionType::DIR_POS_X)
			{
				edgeType = FrameEdgeType::LEFT_BOTTOM_POS_X;
			}
			else if (blockDir == DirectionType::DIR_NEG_Z)
			{
				edgeType = FrameEdgeType::LEFT_BOTTOM_NEG_Z;
			}
			else if (blockDir == DirectionType::DIR_POS_Z)
			{
				edgeType = FrameEdgeType::LEFT_BOTTOM_POS_Z;
			}
		}
		else if (indexI == upWidthLimit && indexJ == downHeightLimit)
			edgeType = FrameEdgeType::RIGHT_BOTTOM;
		else if (indexI == downWidthLimit && indexJ == upHeightLimit)
			edgeType = FrameEdgeType::LEFT_TOP;
		else if (indexI == upWidthLimit && indexJ == upHeightLimit)
			edgeType = FrameEdgeType::RIGHT_TOP;
		else if (indexI == downWidthLimit && downHeightLimit < indexJ && indexJ < upHeightLimit)
			edgeType = FrameEdgeType::LEFT_MIDDLE;
		else if (downWidthLimit < indexI && indexI < upWidthLimit && indexJ == upHeightLimit)
			edgeType = FrameEdgeType::MIDDLE_TOP;
		else if (downWidthLimit < indexI && indexI < upWidthLimit && indexJ == downHeightLimit)
		{
			if (blockDir == DirectionType::DIR_NEG_X)
			{
				edgeType = FrameEdgeType::MIDDLE_BOTTOM_NEG_X;
			}
			else if (blockDir == DirectionType::DIR_POS_X)
			{
				edgeType = FrameEdgeType::MIDDLE_BOTTOM_POS_X;
			}
			else if (blockDir == DirectionType::DIR_NEG_Z)
			{
				edgeType = FrameEdgeType::MIDDLE_BOTTOM_NEG_Z;
			}
			else if (blockDir == DirectionType::DIR_POS_Z)
			{
				edgeType = FrameEdgeType::MIDDLE_BOTTOM_POS_Z;
			}
		}
		else if (indexI == upWidthLimit && downHeightLimit < indexJ && indexJ < upHeightLimit)
			edgeType = FrameEdgeType::RIGHT_MIDDLE;
		else
			edgeType = FrameEdgeType::MIDDLE_MIDDLE;
		return edgeType;
	};

	int tempWidth = m_FrameWidth - 1;
	int upWidthLimit = math::ceil(tempWidth / 2.0);
	int downWidthLimit = -(tempWidth / 2);
	int upHeightLimit = m_FrameHeight;
	int downHeightLimit = 0;

	if (blockDir == DirectionType::DIR_NEG_X || blockDir == DirectionType::DIR_POS_X)
	{
		for (int indexZ = downWidthLimit; indexZ <= upWidthLimit; indexZ++)
		{
			for (int indexY = downHeightLimit; indexY < upHeightLimit; indexY++)
			{
				WCoord framePartPos;
				if (blockDir == DirectionType::DIR_NEG_X)
					framePartPos = WCoord(m_BlockPos.x, m_BlockPos.y + indexY, m_BlockPos.z - indexZ);
				else
					framePartPos = WCoord(m_BlockPos.x, m_BlockPos.y + indexY, m_BlockPos.z + indexZ);

				if (framePartPos != m_BlockPos)
					pWorld->setBlockAll(framePartPos, blockId, blockDir);

				FrameEdgeType edgeType = GetFrameEdgeType(downWidthLimit, upWidthLimit, downHeightLimit, upHeightLimit - 1, indexZ, indexY);
				PolaroidPartFrameInfo partFrameInfo(framePartPos, edgeType);
				m_FramePartsInfo.push_back(partFrameInfo);
			}
		}
	}
	else if (blockDir == DirectionType::DIR_NEG_Z || blockDir == DirectionType::DIR_POS_Z)
	{
		for (int indexX = downWidthLimit; indexX <= upWidthLimit; indexX++)
		{
			for (int indexY = downHeightLimit; indexY < upHeightLimit; indexY++)
			{
				WCoord framePartPos;
				if (blockDir == DirectionType::DIR_NEG_Z)
					framePartPos = WCoord(m_BlockPos.x + indexX, m_BlockPos.y + indexY, m_BlockPos.z);
				else
					framePartPos = WCoord(m_BlockPos.x - indexX, m_BlockPos.y + indexY, m_BlockPos.z);

				if (framePartPos != m_BlockPos)
					pWorld->setBlockAll(framePartPos, blockId, blockDir);

				FrameEdgeType edgeType = GetFrameEdgeType(downWidthLimit, upWidthLimit, downHeightLimit, upHeightLimit - 1, indexX, indexY);
				PolaroidPartFrameInfo partFrameInfo(framePartPos, edgeType);
				m_FramePartsInfo.push_back(partFrameInfo);
			}
		}
	}
}

PolaroidPartFrameInfo* ContainerPolaroidFrame::getPartFrameInfo(const WCoord& blockPos)
{
	auto isPosEqual = [blockPos](PolaroidPartFrameInfo& info) -> bool
	{
		return blockPos.x == info.blockPos.x && blockPos.y == info.blockPos.y && blockPos.z == info.blockPos.z;
	};

	auto iter = std::find_if(m_FramePartsInfo.begin(), m_FramePartsInfo.end(), isPosEqual);
	if (iter == m_FramePartsInfo.cend())
		return NULL;

	return &(*iter);
}

bool ContainerPolaroidFrame::isBlockBelongToMe(const WCoord& blockPos)
{
	return getPartFrameInfo(blockPos) != NULL;
}

FrameEdgeType ContainerPolaroidFrame::getBlockFrameType(const WCoord& blockPos)
{
	PolaroidPartFrameInfo* info = getPartFrameInfo(blockPos);
	if (!info)
		return FrameEdgeType::NONE_EDGE;
	return info->edgeType;
}

void ContainerPolaroidFrame::destroyFrameParts()
{
	if (m_World == NULL || m_FramePartsInfo.size() == 0)
		return;

	if (m_World->isRemoteMode())
		return;

	//这里删除不需要通知回调onBlockRemoved，因为onBlockRemoved是为了用户敲碎其中一个的时候，删除整个相框的
	int blockFlag = kBlockUpdateFlagDefault | kBlockUpdateFlagIgnoreRemoveCallBack;
	for (auto iter = m_FramePartsInfo.begin(); iter != m_FramePartsInfo.end(); iter++)
	{
		Block tempBlock  = m_World->getBlock(iter->blockPos);
		if (tempBlock.getResID() != ITEM_POLAROID_RARE_FRAME && tempBlock.getResID() != ITEM_POLAROID_FRAME && tempBlock.getResID() != ITEM_POLAROID_INNER_FRAME)
			continue;
		if (iter->blockPos == m_BlockPos)
			continue;
		m_World->setBlockAir(iter->blockPos, blockFlag);
	}
	m_FramePartsInfo.clear();

	//判断核心方块是否存在，如果存在大小就是1*1，否则就是0了
	Block coreBlock = m_World->getBlock(m_BlockPos);
	if (coreBlock.getResID() == ITEM_POLAROID_RARE_FRAME || coreBlock.getResID() == ITEM_POLAROID_FRAME || coreBlock.getResID() == ITEM_POLAROID_INNER_FRAME)
	{
		m_FrameWidth = 1;
		m_FrameHeight = 1;
	}
	else
	{
		m_FrameWidth = 0;
		m_FrameHeight = 0;
	}
}

void ContainerPolaroidFrame::updateFrameStyle(std::string frameStyle)
{
	if (!m_World)
		return;

	if (frameStyle.compare(m_FrameStyle) == 0)
		return;

	m_FrameStyle = frameStyle;

	Chunk* pChunk = m_World->getChunk(m_BlockPos);
	if (pChunk)
		pChunk->m_Dirty = true;

	//刷新本section，因为有可能核心和其他部分不在一个section
	for (const PolaroidPartFrameInfo& info : m_FramePartsInfo)
	{
		Section* pSection = m_World->getSection(info.blockPos);
		if (pSection)
			pSection->setMeshInvalid(false);
	}

	syncToOtherPlayerFrameChange();
}

void ContainerPolaroidFrame::updatePhotoFitMode(int fitModel)
{
	if (fitModel == m_PhotoStretchMode)
		return;

	//这个枚举定义在DEV3DUIFILLTYPE中
	if (fitModel < 1 || fitModel > 4)
		return;
	m_PhotoStretchMode = fitModel;

	Chunk* pChunk = m_World->getChunk(m_BlockPos);
	if (pChunk)
		pChunk->m_Dirty = true;

	syncToOtherPlayerFrameChange();
	//主机才去直接刷
	if (m_World && !m_World->isRemoteMode())
		updatePhoto3DTexture();
}

void ContainerPolaroidFrame::updatePhotoURL(std::string photoUrl)
{
	if (m_PhotoURL.compare(photoUrl) == 0)
		return;

	m_PhotoURL = photoUrl;
	
	Chunk* pChunk = m_World->getChunk(m_BlockPos);
	if (pChunk)
		pChunk->m_Dirty = true;

	syncToOtherPlayerFrameChange();
	
	//主机才去直接刷
	if (m_World && !m_World->isRemoteMode())
		updatePhoto3DTexture();
}

void ContainerPolaroidFrame::updatePhoto3DTexture()
{
#ifndef DEDICATED_SERVER
	if (!m_World)
		return;
	//还未设置图片
	if (m_PhotoURL.size() == 0)
		return;

	Block block = m_World->getBlock(m_BlockPos);
	int blockData = block.getData();
	bool isCoreBlock = blockData & 8;
	if (!isCoreBlock)
		return;

	//更新2，创建3
	int operateType = 2;
	if (m_Dev3DTextureID == 0)
		operateType = 3;

	MNSandbox::SandboxContext context = MNSandbox::SandboxContext(nullptr);

	WCoord worldBlockPos(m_BlockPos.x * BLOCK_SIZE, m_BlockPos.y * BLOCK_SIZE, m_BlockPos.z * BLOCK_SIZE);
	worldBlockPos.y += (m_FrameHeight * BLOCK_SIZE) / 2;

	//是否偶数
	bool isEvenNumber = m_FrameWidth % 2 == 0;
	int posOffset = isEvenNumber ? BLOCK_SIZE : BLOCK_SIZE / 2;

	FrameEdgeType blockDir = getBlockFrameType(m_BlockPos);

	Vector3f dir(0,0,0);
	Vector3f rotation(0,0,0);
	const int FRAME_DEPTH = 6;
	if (blockDir == FrameEdgeType::SINGLE_NEG_X || blockDir == FrameEdgeType::BOTTOM_CLOSURE_NEG_X || 
		blockDir == FrameEdgeType::LEFT_CLOSURE_NEG_X || blockDir == FrameEdgeType::MIDDLE_BOTTOM_NEG_X || 
		blockDir == FrameEdgeType::LEFT_BOTTOM_NEG_X)//DIR_NEG_X
	{
		dir = Vector3f(-1, 0, 0);
		rotation.y = 90;
		if (isEvenNumber)
			worldBlockPos += WCoord(BLOCK_SIZE - FRAME_DEPTH, 0, 0);
		else
			worldBlockPos += WCoord(BLOCK_SIZE - FRAME_DEPTH, 0, posOffset);
	}
	else if (blockDir == FrameEdgeType::SINGLE_NEG_Z || blockDir == FrameEdgeType::BOTTOM_CLOSURE_NEG_Z ||
		blockDir == FrameEdgeType::LEFT_CLOSURE_NEG_Z || blockDir == FrameEdgeType::MIDDLE_BOTTOM_NEG_Z || 
		blockDir == FrameEdgeType::LEFT_BOTTOM_NEG_Z)//DIR_NEG_Z
	{
			dir = Vector3f(0, 0, -1);
			rotation.y = 0;
			worldBlockPos += WCoord(posOffset, 0, BLOCK_SIZE - FRAME_DEPTH);
	}
	else if (blockDir == FrameEdgeType::SINGLE_POS_X || blockDir == FrameEdgeType::BOTTOM_CLOSURE_POS_X ||
		blockDir == FrameEdgeType::LEFT_CLOSURE_POS_X || blockDir == FrameEdgeType::MIDDLE_BOTTOM_POS_X || 
		blockDir == FrameEdgeType::LEFT_BOTTOM_POS_X)//DIR_POS_X
	{
		dir = Vector3f(1, 0, 0);
		rotation.y = 270;
		worldBlockPos += WCoord(FRAME_DEPTH, 0, posOffset);
	}
	else if (blockDir == FrameEdgeType::SINGLE_POS_Z || blockDir == FrameEdgeType::BOTTOM_CLOSURE_POS_Z ||
		blockDir == FrameEdgeType::LEFT_CLOSURE_POS_Z || blockDir == FrameEdgeType::MIDDLE_BOTTOM_POS_Z || 
		blockDir == FrameEdgeType::LEFT_BOTTOM_POS_Z)//DIR_POS_Z
	{
		dir = Vector3f(0, 0, 1);
		rotation.y = 180;
		if (isEvenNumber)
			worldBlockPos += WCoord(0, 0, FRAME_DEPTH);
		else
			worldBlockPos += WCoord(posOffset, 0, FRAME_DEPTH);
	}

	int width = (m_FrameWidth * BLOCK_SIZE) - 40;
	int height = (m_FrameHeight * BLOCK_SIZE) - 40;
	
	context.SetData_Number("operateType", operateType).
		SetData_UserObject<Vector3f>("dir", dir).
		SetData_UserObject<Vector3f>("rotation", rotation).
		SetData_UserObject<WCoord>("pos", worldBlockPos).
		SetData_Number("textureId", m_Dev3DTextureID).
		SetData_String("photoUrl", m_PhotoURL).
		SetData_Number("stretchMode", m_PhotoStretchMode).
		SetData_Number("width", width).
		SetData_Number("height", height);

	LOG_INFO("ContainerPolaroidFrame::updatePhoto3DTexture %f,%f,%f", rotation.x, rotation.y, rotation.z);
	
	MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerObjLibManager_polaroidFrameTexture",context);

	if (!result.IsSuccessed())
	{
		LOG_INFO("ContainerPolaroidFrame::updatePhoto3DTexture create texture fail %s", m_PhotoURL.c_str());
		return;
	}

	m_Dev3DTextureID = result.GetData_Number("textureId");
#endif
}

void ContainerPolaroidFrame::destroyPhoto3DTexture()
{
	//主机通知客机去销毁显示板
	if (m_World && !m_World->isRemoteMode())
	{
		syncToOtherPlayerFrameChange(DirectionType::DIR_NOT_INIT, true);
	}

	if (m_Dev3DTextureID != 0)
	{
		MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerObjLibManager_polaroidFrameTexture",
			MNSandbox::SandboxContext(nullptr).
			SetData_Number("operateType", 1).
			SetData_Number("textureId", m_Dev3DTextureID)
		);
		if (!result.IsSuccessed())
		{
			LOG_INFO("ContainerPolaroidFrame::destroyPhoto3DTexture delete texture fail resid = %d", m_Dev3DTextureID);
		}
		else
		{
			m_Dev3DTextureID = 0;
			m_PhotoURL = "";
		}
	}
	else
	{
		m_PhotoURL = "";
	}
}

void ContainerPolaroidFrame::updateFrameChange(std::string jsonstr, bool isHC)
{
	if (!m_World)
		return;

	jsonxx::Object frameInfoObj;
	if (!frameInfoObj.parse(jsonstr))
		return;

	//如果是主机那么执行修改大小
	if (!m_World->isRemoteMode() && frameInfoObj.has<jsonxx::Number>("blockdir"))
		destroyFrameParts();

	if (frameInfoObj.has<jsonxx::Number>("width"))
		m_FrameWidth = frameInfoObj.get<jsonxx::Number>("width");
	if (frameInfoObj.has<jsonxx::Number>("height"))
		m_FrameHeight = frameInfoObj.get<jsonxx::Number>("height");
	if (frameInfoObj.has<jsonxx::String>("url"))
		m_PhotoURL = frameInfoObj.get<jsonxx::String>("url");
	if (frameInfoObj.has<jsonxx::Number>("stretchmode"))
		m_PhotoStretchMode = frameInfoObj.get<jsonxx::Number>("stretchmode");
	if (frameInfoObj.has<jsonxx::String>("framestyle"))
		m_FrameStyle = frameInfoObj.get<jsonxx::String>("framestyle");

	if (frameInfoObj.has<jsonxx::Array>("parts"))
	{
		m_FramePartsInfo.clear();
		jsonxx::Array partsInfo = frameInfoObj.get<jsonxx::Array>("parts");
		for (int index = 0; index < partsInfo.size(); ++index)
		{
			jsonxx::Object partObj = partsInfo.get<jsonxx::Object>(index);
			if (partObj.has<jsonxx::Number>("posx") && partObj.has<jsonxx::Number>("posy") && 
				partObj.has<jsonxx::Number>("posz") && partObj.has<jsonxx::Number>("edgetype"))
			{
				int posx = partObj.get<jsonxx::Number>("posx");
				int posy = partObj.get<jsonxx::Number>("posy");
				int posz = partObj.get<jsonxx::Number>("posz");
				WCoord blockPos(posx, posy, posz);
				FrameEdgeType edgetype = (FrameEdgeType)partObj.get<jsonxx::Number>("edgetype");
				PolaroidPartFrameInfo partFrameInfo(blockPos, edgetype);
				m_FramePartsInfo.push_back(partFrameInfo);
			}
		}
	}

	//如果是主机那么执行修改大小
	if (!m_World->isRemoteMode() && frameInfoObj.has<jsonxx::Number>("blockdir"))
	{
		Block blockInfo = m_World->getBlock(m_BlockPos);
		int blockId = blockInfo.getResID();
		int blockData = blockInfo.getData();
		DirectionType dir = (DirectionType)frameInfoObj.get<jsonxx::Number>("blockdir");
		if (m_FrameWidth == 1 || m_FrameHeight == 1)
		{
			createSpecialFrame(m_World, blockId, blockData, dir);
		}
		else
		{
			createNormalFrame(m_World, blockId, blockData, dir);
		}
	}

	//因为可能会在不同的section中
	if (m_FramePartsInfo.size() == 0)
	{
		Section* pSection = m_World->getSection(m_BlockPos);
		if (pSection)
			pSection->setMeshInvalid(false);
	}
	else
	{
		for (const PolaroidPartFrameInfo& info : m_FramePartsInfo)
		{
			Section* pSection = m_World->getSection(info.blockPos);
			if (pSection)
				pSection->setMeshInvalid(false);
		}
	}

	if (frameInfoObj.has<jsonxx::Boolean>("isdestorytex") && frameInfoObj.get<jsonxx::Boolean>("isdestorytex"))
	{
		destroyPhoto3DTexture();
	}
	else
	{
		updatePhoto3DTexture();
	}

	//广播给所有客机 isHC true标识主机到客机
	if(!m_World->isRemoteMode() && !isHC)
		syncToOtherPlayerFrameChange();
}

void ContainerPolaroidFrame::syncToOtherPlayerFrameChange(DirectionType blockDir, bool isDestoryTex)
{
	if (!m_World)
		return;

	jsonxx::Object frameInfo;
	frameInfo << "containerx" << m_BlockPos.x;
	frameInfo << "containery" << m_BlockPos.y;
	frameInfo << "containerz" << m_BlockPos.z;
	frameInfo << "url" << m_PhotoURL;
	frameInfo << "stretchmode" << m_PhotoStretchMode;
	frameInfo << "framestyle" << m_FrameStyle;
	frameInfo << "width" << m_FrameWidth;
	frameInfo << "height" << m_FrameHeight;
	frameInfo << "isdestorytex" << isDestoryTex;
	if (blockDir != DirectionType::DIR_NOT_INIT)
		frameInfo << "blockdir" << (int)blockDir;
	//客机不用传这个给主机
	if (!m_World->isRemoteMode())
	{
		jsonxx::Array partsFrameInfo;
		for (auto iter = m_FramePartsInfo.begin(); iter != m_FramePartsInfo.end(); iter++)
		{
			jsonxx::Object partFrameInfo;
			partFrameInfo << "posx" << iter->blockPos.x;
			partFrameInfo << "posy" << iter->blockPos.y;
			partFrameInfo << "posz" << iter->blockPos.z;
			partFrameInfo << "edgetype" << (int)iter->edgeType;
			partsFrameInfo << partFrameInfo;
		}
		frameInfo << "parts" << partsFrameInfo;
	}

	if (m_World->isRemoteMode())
		GetSandBoxManagerPtr()->sendToHost("PB_POLAROID_FRAMECHANGE_CH", frameInfo.bin(), frameInfo.binLen());
	else
		GetSandBoxManagerPtr()->sendBroadCast("PB_POLAROID_FRAMECHANGE_HC", frameInfo.bin(), frameInfo.binLen());
}

void ContainerPolaroidFrame::recreatePolaroidFrame()
{
	//蓝图那里是先放容器后放方块
	if (!g_pPlayerCtrl)
		return;
	World* pWorld = g_pPlayerCtrl->getWorld();
	if (!pWorld)
		return;
	if (m_FramePartsInfo.size() == 0)
		return;
	PolaroidPartFrameInfo* info = getPartFrameInfo(m_BlockPos);
	//有两种情况，一种位置变了，一种位置没变，位置变了那么获取到info就是NULL，没变才能获取到
	//if (info != NULL && info->blockPos == m_BlockPos)
	//	return;
	int length = m_FramePartsInfo.size();
	const PolaroidPartFrameInfo& frameInfo = m_FramePartsInfo[length - 1];
	if (frameInfo.blockPos.x != 0 || frameInfo.blockPos.y != Rainbow::MAX_INT || frameInfo.blockPos.z != 0)
		return;
	DirectionType dir = DirectionType::DIR_NOT_INIT;
	if (FrameEdgeType::SINGLE_NEG_X == frameInfo.edgeType)
		dir = DirectionType::DIR_NEG_X;
	else if (FrameEdgeType::SINGLE_POS_X == frameInfo.edgeType)
		dir = DirectionType::DIR_POS_X;
	else if (FrameEdgeType::SINGLE_NEG_Z == frameInfo.edgeType)
		dir = DirectionType::DIR_NEG_Z;
	else if (FrameEdgeType::SINGLE_POS_Z == frameInfo.edgeType)
		dir = DirectionType::DIR_POS_Z;
	if (dir == DirectionType::DIR_NOT_INIT)
		return;
	m_FramePartsInfo.clear();
	
	Block blockInfo = pWorld->getBlock(m_BlockPos);
	int blockId = blockInfo.getResID();
	int blockData = blockInfo.getData();
	
	if (m_FrameWidth == 1 || m_FrameHeight == 1)
	{
		createSpecialFrame(pWorld, blockId, blockData, dir);
	}
	else
	{
		createNormalFrame(pWorld, blockId, blockData, dir);
	}
	m_IsRecreate = false;
}

bool ContainerPolaroidFrame::rotatePolaroidFrame(DirectionType direc)
{
	if (DirectionType::DIR_POS_Y == direc || DirectionType::DIR_NEG_Y == direc)
		return false;
	FrameEdgeType edgeType = FrameEdgeType::NONE_EDGE;
	if (direc == DirectionType::DIR_NEG_X)
		edgeType = FrameEdgeType::SINGLE_NEG_X;
	else if (direc == DirectionType::DIR_POS_X)
		edgeType = FrameEdgeType::SINGLE_POS_X;
	else if (direc == DirectionType::DIR_NEG_Z)
		edgeType = FrameEdgeType::SINGLE_NEG_Z;
	else if (direc == DirectionType::DIR_POS_Z)
		edgeType = FrameEdgeType::SINGLE_POS_Z;
	PolaroidPartFrameInfo frameInfo(WCoord(0, Rainbow::MAX_INT, 0), edgeType);
	m_FramePartsInfo.push_back(frameInfo);
	return true;
}
