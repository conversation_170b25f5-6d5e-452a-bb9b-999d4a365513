

#ifndef __LIVINGATTRIB_H__
#define __LIVINGATTRIB_H__

#include "ActorTypes.h"
#include <vector>
#include <map>
#include <functional>
#include "world_types.h"
#include "AttribTypes.h"
#include "ActorAttrib.h"
#include "SandboxGame.h"
struct EnchantDef;
class ClientActor;
class ActorBody;
struct BuffDef;
struct FoodDef;
class BackPackGrid;
class PackContainer;
class BackPack;
struct EffectInfo;
struct CustomScriptEffectArr;
struct BuffEffectDef;
class GridRuneItemData;
class MonsterDef;
struct RuneDef;
class ClientPlayer;

//tolua_begin
struct ActorBuff
{
	int buffid;
	int bufflv;
	int ticks;
	int buffidx;
	const BuffDef *def;
	int count;
	WORLD_ID fromObjid;	//buff施加者
	float damageHPToRemoveBuff; //累计一定伤害解除 BUFFATTRT_DAMAGE_REMOVE_BUFF
	bool effplayertype;
	ActorBuff() : buffid(0), bufflv(0), ticks(0), buffidx(0), def(NULL), fromObjid(0), damageHPToRemoveBuff(0), effplayertype(true)
	{
		count = 0;
	}
};

struct AttribModified
{
	float value;
	AttribModified() :value(0.0f)
	{

	}
};

struct StatusValue
{
	int iParaType;
	int iType;//数值类型 1：百分比； 非1：数值
	float value;
};

struct StatusAttInfo
{
	int iStatusId;
	int iStatusLv;
	int iEffectId;//效果id
	int iStatusIdx;
	std::vector<StatusValue> vValue;
	StatusAttInfo()
	{
		iStatusId = 0;
		iStatusLv = 0;
		iEffectId = 0;
		iStatusIdx = 0;
		vValue.clear();
	}
};
//tolua_end

struct EquipEntryStatus
{
	int equipID;
	std::vector<ActorBuff> entryBuffs;
};
class EXPORT_SANDBOXGAME LivingAttrib;
class LivingAttrib : public ActorAttrib {//tolua_exports
public:
	//DECLARE_COMPONENTCLASS(LivingAttrib)


	LivingAttrib();
	virtual ~LivingAttrib();

	virtual void CreateEvent2();
	virtual void DestroyEvent2();

	virtual void revive();
	virtual void clearrevive(int cleartype);
	virtual void tick() override;
	virtual bool attackedFrom(OneAttackData &atkdata, ClientActor *attacker = NULL);

	// 旧伤害计算
	virtual float calculateHpdecOld(OneAttackData& atkdata, ClientActor* attacker = NULL);

	/*
	* 新伤害系统计算接口
	* code-by:liya designer:huzhe
	* 处理近战物理，远程物理，近战元素、远程元素、爆炸伤害
	*/
	virtual float calculateHpdecNew(OneAttackData& atkdata, ClientActor* attacker = NULL);

	//tolua_begin
	virtual float getSpeedInAir();
	virtual float getFlySpeed();
	virtual bool isEquipOxygenpack() { return false; }
	virtual bool damageOxygenpack(int value) { return false; }

	float getOxygen()
	{
		return m_Oxygen;
	}
	float getMaxOxygen()
	{
		return START_OXYGEN;
	}
	void setOxygen(float v)
	{
		if (START_OXYGEN < v)
			m_Oxygen = START_OXYGEN;
		else
			m_Oxygen = v;
	}
	void addOxygen(float v);
	void addBuffTimeExtended(int buffid, int bufflv, float add_ratio);

	// added by dongjianan 因为目前没有类似GameplayEffect那种完善的免疫机制，buff的免疫目前通过简单的重载addBuff实现。
	// src: void addBuff(int buffid, int bufflv, int customticks = -1, int buffInstanceId = 0, long long objid = 0);
	// dst:
	virtual void addBuff(int buffid, int bufflv, int customticks = -1, int buffInstanceId = 0, long long objid = 0);

	void addBuffOnLoad(int buffid, int bufflv, int ticks);
	void removeBuff(int buffid, bool bRemoveSingleOne = true, int buffInstanceId = 0);
	void removeBuffByIndex(int idx);
	void removeBuffByNature(int nature);
	bool hasBuff(int buffid, int bufflv = -1);
	bool HasBadBuff();
	int  getBuffIndex(int buffid);
	bool hasBuffByNature(int nature);
	void clearBuffAndStatus();
	void clearBuff(bool bUseClearFlag = false);
	void clearRandomBuff();
	void clearRandomBadBuff();
    void clearBuffByFoodDef(const FoodDef* def);
	void clearAllBadBuff();
	int getBuffNum();
	int getBuffNumById(int buffid);
	ActorBuff getBuffInfo(int index);
	void RefreshAllBuffTick();//刷新当前对象身上的所有buff对应的ticks
	void SetBuffTick(int buffid, int bufflv, int ticks);
	//buff 添加tick时间
	void AddBuffTick(int buffId, int ticks);
	bool isActionDisabled();
	void addModAttrib(int attrtype, float v);
	float getModAttrib(int attrtype);
	bool setModAttrib(int attrtype, float v);
	bool addEnchant(int slot, int enchantId, int enchantLevel);
	bool removeEnchant(int slot, int enchantId);
	bool addRune(int slot, const GridRuneItemData &one);//添加符文
	bool removeRune(int slot, int runeid);//删除符文
	void setFirstSearch(bool firstSearch);//设置优先查找
	bool getFirstSearch();//设置优先查找
	void attackedByBuff(int atktype, float atkpoints, long long fromObjid = 0,int buffid=0,int bufflv=0);
	bool hasNeedSwitchTPSBuff();
	float getArmorPointLua(int atktype);

	bool hasHarmfulBuff();	// 是否有有害的buff，使用TriggerType判断
	void damageRemoveBuff(float hp, int atktype, int attackTargetType); //受到一定伤害解除buff

	virtual void staminaUsed(float v) {}
	// 改为虚函数，可被继承重载（monster需要重载）code_by:liya
	virtual float getAttackBaseLua(int attacktype);
	void setAttackBaseLua(int attacktype, float v);
	// 改为虚函数，可被继承重载（monster需要重载）code_by:liya
	virtual float getArmorBaseLua(int attacktype);
	void setArmorBaseLua(int attacktype, float v);

	virtual void applyEquips(ActorBody *body, EQUIP_SLOT_TYPE t = MAX_EQUIP_SLOTS, bool takeoffAble = true);//装备模型挂件（参数就是给类型）by charles xie
	virtual void equip(EQUIP_SLOT_TYPE t, int itemid, int durable, int toughness, int maxdurable) = 0;//这里使用的参数t是装备栏的idx by charles xie
	virtual void equip(EQUIP_SLOT_TYPE t, BackPackGrid* itemgrid) = 0;
	virtual int getEquipItem(EQUIP_SLOT_TYPE t) = 0;//这个是获取装备栏 t 位置上的装备id
	virtual int getEquipItemWithType(EQUIP_SLOT_TYPE t) { return getEquipItem(t); };//这个是获得t类型装备的id， 在mob中是没有装备位与装备类型绑定的，所以mob不用重写 by charles xie
	// 装备顺坏  t->装备位置 damage->损耗的耐久值  耐久值<=0时返回 装备ID
	virtual int damageEquipItem(EQUIP_SLOT_TYPE t, int damage) = 0; //参数t为装备放置的位置
	virtual int damageEquipItemWithType(EQUIP_SLOT_TYPE t, int damage) { return damageEquipItem(t, damage); };//参数t为装备的类型
	virtual void dropEquipItems() = 0;
	virtual void dropEquipItemsToChest(int itemid) {}
	virtual void dropEquipItemsToJar(int itemid) {}
	virtual BackPackGrid *getEquipGrid(EQUIP_SLOT_TYPE t) = 0;
	virtual BackPackGrid* getEquipGridWithType(EQUIP_SLOT_TYPE t) { return getEquipGrid(t); };
	virtual void ApplyWeaponEnchantEffect(ActorBody *body) {}
	virtual void SyncWeaponEnchantEffect() {}

	float getEnchantAttackPoint(ATTACK_TYPE atktype, ATTACK_TARGET_TYPE targettype); //附魔伤害点数: 近战, 远程, 爆炸,  targettype: 普通, 玩家, 亡灵, 动物
	float getEnchantAttackPercent(ATTACK_TYPE atktype); //附魔伤害加成: 远程-弓
	//附魔盔甲保护点数: //近战, 远程, 爆炸, 火焰, 毒素, 凋零,  掉落 @param hasAllArmor 是否包含所有防御
	float getEnchantArmorPoint(ATTACK_TYPE atktype, bool hasAllArmor = true);
	//所有伤害减免附魔盔甲 保护百分比: //近战, 远程, 爆炸, 火焰, 毒素, 凋零,  掉落 @param hasAllArmor 是否包含所有防御
	float getEnchantDamageDecPer(ENCHANT_TYPE enchanttype, ATTACK_TYPE atktype, bool hasAllArmor = true);
	float getEnchantIncPer(ENCHANT_TYPE enchanttype, ATTACK_TYPE atktype = ATTACK_ALL, ATTACK_TARGET_TYPE targettype = ATTACK_TARGET_ALL,bool hasAllArmor = true);
	float getEnchantJumpHeightInc();
	float getEnchantExpInc();
	float getEnchantWeponSkillDec();
	bool checkEnchant(ENCHANT_TYPE enchanttype, ATTACK_TYPE atktype = ATTACK_ALL, ATTACK_TARGET_TYPE targettype = ATTACK_TARGET_ALL);
	float getEquipRuneValue(EQUIP_SLOT_TYPE slot, ENCHANT_TYPE enchanttype, ATTACK_TYPE attacktype, ATTACK_TARGET_TYPE targettype, float *value2, bool hasAllArmor = true);
	float getEquipEnchantValueOld(EQUIP_SLOT_TYPE slot, ENCHANT_TYPE enchanttype, ATTACK_TYPE attacktype, ATTACK_TARGET_TYPE targettype, float *value2, bool hasAllArmor = true);
	float getEquipEnchantValue(EQUIP_SLOT_TYPE slot, ENCHANT_TYPE enchanttype, ATTACK_TYPE attacktype = ATTACK_ALL, ATTACK_TARGET_TYPE targettype = ATTACK_TARGET_ALL, float *value2 = NULL, bool hasAllArmor = true);
	const EnchantDef *getEquipEnchant(EQUIP_SLOT_TYPE slot, ENCHANT_TYPE enchanttype, ATTACK_TYPE attacktype = ATTACK_ALL, ATTACK_TARGET_TYPE targettype = ATTACK_TARGET_ALL);
	const RuneDef *getEquipRuneEnchant(EQUIP_SLOT_TYPE slot, ENCHANT_TYPE enchanttype, ATTACK_TYPE attacktype = ATTACK_ALL, ATTACK_TARGET_TYPE targettype = ATTACK_TARGET_ALL);
	int getEquipMineBlockEfficiency(int baseEfficiency);
	float antiInjuryEnchant(ATTACK_TYPE atktype);
	int getDigProbEnchant(); //得到时运附魔等级

	virtual float getKnockback(ATTACK_TYPE atktype, ATTACK_TARGET_TYPE targettype); //击退
	float getKnockUp(ATTACK_TYPE atktype, ATTACK_TARGET_TYPE targettype);//击飞
	int getFireAspect(int &bufflv);
	int getBleedAspect(int& bufflv);
	int getIceAspect(int& bufflv);
	float getAttackAndDefenseBase(int type, bool bAttack);

	bool isNewStatus();
	bool hasStatusEffect(int iEffectId);
	void checkAllStatusClearFlags(int clearType, int exceptId = 0);//1 死亡是否清除（只有玩家才会有）；2 主动攻击是否清除； 3受击是否清除
	/*
	* @param fromType：0：近战 1：远程 2：爆炸
	*/
	float getAttackPoint(ATTACK_TYPE atktype, int fromType = 0); //近战, 远程, 爆炸, 火焰, 毒素, 凋零
	float getArmorPoint(ATTACK_TYPE atktype); //近战, 远程, 爆炸
	float getArmorPointByPart(ATTACK_TYPE atktype, ATTACK_BODY_TYPE bodytype); //近战, 远程, 爆炸
	//给lua使用上面那个ATTACK_BODY_TYPE tolua没转成int
	float getArmorPointByPartLua(int atktype, int bodytype); //近战, 远程, 爆炸

	// 获取单类型防御力（比如近战物理防御力不包含物理防御，火元素防御力不包含魔法防御）
	float getArmorPointSingle(ATTACK_TYPE atktype);
	float getHurtPointWithStatus(ATTACK_TYPE atktype, float baseHurtValue, ClientActor* attacker = NULL);	//效果影响伤害
	// 获取针对特定生物伤害加成（@param isPer false固定点 true百分比）
	float getTargetDamageIns(ClientActor* attacker, bool isPer);
	// 获取针对特定生物伤害减免（@param isPer false固定点 true百分比）
	float getTargetDamageDec(ClientActor* attacker, bool isPer);
	float getAttackPointWithStatus(ATTACK_TYPE atktype, float baseAttackPoint);								//效果影响攻击力
	float getAromrPointWithStatus(ATTACK_TYPE atktype, float baseArmorPoint);								//效果影响防御力
	// 效果影响攻击力（单元素，不包含所有攻击力加成）
	float getAttackPointWithStatusSingle(ATTACK_TYPE atktype, float baseAttackPoint);
	// 效果影响防御力（单元素，不包含所有防御力加成）
	float getAromrPointWithStatusSingle(ATTACK_TYPE atktype, float baseArmorPoint);
	bool getBuffEffectBankInfo(int iAttType);//查找是否存在buff库

	/**
	@brief buff免疫相关
	*/
	void AddImmueBuff(int buffId);
	void RemoveImmueBuff(int buffId);

	//tolua_end
	float getActorAttValueWithStatus(int iAttType, float baseValue);
	// @param isPer true 获取百分比 false 获取固定值
	float getActorAttValueWithStatusByType(int iAttType, bool isPer);
	float getRandomAttValueWithStatus(int iAttType);
	void getStatusAddAttInfo(int iAttType, std::vector<StatusAttInfo>& vValue);
	void addStatusEffects(ActorBuff* status);//添加一个状态下的所有效果
	void addOneEffect(ActorBuff* status, const EffectInfo& effect, CustomScriptEffectArr* pCustEffArr = nullptr);//添加一条效果
	void addNormalEffect(ActorBuff* status, const EffectInfo& effect, const BuffEffectDef* def);
	void addSpecialEffect(ActorBuff* status, const EffectInfo& effect, const BuffEffectDef* def);
	void addCustomEffect(ActorBuff* status, const CustomScriptEffectArr& custEffectArr, const BuffEffectDef* def);
	void removeStatusEffects(ActorBuff* status);//移除一个状态
	void removeOneEffect(ActorBuff* status, const EffectInfo& effect);//移除一条效果
	void removeNormalEffect(ActorBuff* status, const EffectInfo& effect, const BuffEffectDef* def);
	void removeSpecialEffect(ActorBuff* status, const EffectInfo& effect, const BuffEffectDef* def);
	void removeCustomEffect(ActorBuff* status, const EffectInfo& effect, const BuffEffectDef* def);
	void execStatusEffect(ActorBuff* status, int buffindex);
	float manageShieldLife(float damage);
	BuffAttrType getEffectType(const EffectInfo& effect, const BuffEffectDef* def);
	virtual void execEffect(BuffAttrType type, std::vector<StatusAttInfo>& vSAInfo);
	void execNormalEffect(BuffAttrType type);
	void execSpecialEffect(BuffAttrType type);
	void againTakeBuffEffect();
	void againTakeBuffSound();

	// 武器熟练度系统 - 皮肤加成 codeby:lizb
	float getWeaponSkinAddition(float damage, ClientPlayer* atkplayer);

	// 防御系统受伤处理 code_by:liya
	bool handleDefanceStateDamaged(float& damage, ClientActor* attacker, OneAttackData& atkdata);

	// 韧性扣除处理 code_by:liya
	void handleToughnessDeduction(ClientActor* attacker, OneAttackData& atkdata);

	// 击退抗性处理 code_by:liya
	void handleRepelResist(float& knockback);

	void addEquipBuff(int iEquipId);
	void removeEquipBuff(int iEquipId);
	bool hasEquipBuff(int iEquipId);
	/* 冒险 20220623 codeby:zhangyusong*/
	void addToolBuff(int iToolId);
	void removeToolBuff(int iToolId);
	bool hasToolBuff(int iToolId);
    void AttackedByScorpionSpineShield(OneAttackData &atkdata);
	//armType 0 枪, 1 装备
	void addEquipEntryBuff(int iEquipId, int armType);
	void removeEquipEntryBuff(int iEquipId);
    /**
    @brief buff免疫相关
    */
    bool CheckImmueBuff(int buffId);

	/* 骆驼坐骑在沙尘暴debuff中减伤 20220712 codeby:wangyu*/
	int  getHurtValueOnLuotuo(int hurtValue);

	/*
@brief	获取第xx个天赋数据
*/
	float getGeniusValue(PLAYER_GENIUS_TYPE geniustype, int idx);

	// 皮肤加成 - 水下呼吸时间增加@1秒 codeby:lizb
	float getWeaponSkinAddOxgen();

	/**
	@brief 属性变化时，立即更新最大值
	*/
	virtual void updateStrength() {}
	virtual void updateOverflowStrength() {}

	void setDelegateBuffAppend(std::function<void(int, int)> func) {
		m_delegateBuffAppend = func;
	}

	void setDelegateBuffRemove(std::function<void(int, int)> func) {
		m_delegateBuffRemove = func;
	}

	void setWaterPressure(int val)
	{
		m_iWaterPressure = val;
	}
	//tolua_begin
	int getWaterPressure();
	virtual int getCalculatedWaterPressure() { return m_iWaterPressure;} // 获得计算各项处理后的水压影响值

	/*
	*	获取是否属性变身状态
	*/
	virtual bool getAttrShapeShift() { return false; }

	// 当前位置温度
	virtual float getPosTemperature() { return m_PosTemperature; }
	virtual void setPosTemperature(float val) { m_PosTemperature = val; }
	
	// 自身温度
	virtual void setTemperature(float val) { m_Temperature = val; }
	virtual void addTemperature(float val, bool suit = true);
	virtual float getTemperature() { return m_Temperature; }
	
	// 温度buff抗性，不会突破适宜温度
	virtual void addTemperatureBuffDefend(float val) { m_TemperatureBuffDefend += val; }
	virtual void setTemperatureBuffDefend(float val) { m_TemperatureBuffDefend = val; }
	virtual float getTemperatureBuffDefend() { return m_TemperatureBuffDefend; }

	// 温度附魔抗性，不会突破适宜温度
	virtual void addTemperatureEnchantDefend(float val) { m_TemperatureEnchantDefend += val; }
	virtual void setTemperatureEnchantDefend(float val) { m_TemperatureEnchantDefend = val; }
	virtual float getTemperatureEnchantDefend() { return m_TemperatureEnchantDefend; }
	
	// 温度装备抗性，不会突破适宜温度
	virtual void addTemperatureEquipDefend(float val) { m_TemperatureEquipDefend += val; }
	virtual void setTemperatureEquipDefend(float val) { m_TemperatureEquipDefend = val; }
	virtual float getTemperatureEquipDefend() { return m_TemperatureEquipDefend; }
	
	// 温度buff效果改变，可以突破适宜温度
	virtual void addTemperatureBuffEffect(float val) { m_TemperatureBuffEffect += val; }
	virtual void setTemperatureBuffEffect(float val) { m_TemperatureBuffEffect = val; }
	virtual float getTemperatureBuffEffect() { return m_TemperatureBuffEffect; }
	
	// 最终位置温度
	virtual float getFinalPosTemperature() { return m_FinalPosTemperature; }
	virtual void setFinalPosTemperature(float val) { m_FinalPosTemperature = val; }

	// 辐射
	virtual void setRadiation(float val) { m_Radiation = val; }
	virtual void addRadiation(float val) { m_Radiation += val; }
	virtual float getRadiation() { return m_Radiation; }	

	virtual void applyEquipToughness();
	virtual void resetToughness() { m_ToughnessTotal = 0; }
	virtual int getToughnessTotal() { return m_ToughnessTotal; }
	virtual void addToughnessTotal(int tou);
	virtual int getToughnessTotalMax() {return m_ToughnessTotalMax;}
	
	virtual int getToughnessBase() { return m_ToughnessBase; }
	virtual void setToughnessBase(int toughness);

	//tolua_end
	void getLastAttackBuff(int& buffId, int& buffLv) {
		buffId = m_lastAttackBuffId;
		buffLv = m_lastAttackBuffLv;
	};

	// 新增中剑次数-用于中箭返还
	void addArrowById(int itemId, int count = 1);
	void returnArrows(); // 死亡时根据公式返中箭 
	EQUIP_BACK_INDEX getEquipIdxByType(EQUIP_SLOT_TYPE type);

protected:
	void execBuff(ActorBuff *buff, int mode);
	void callBuffScript(ActorBuff *buff, int mode); //mode:0进入,  1退出,   2tick
	void setBuffAttrs(ActorBuff *buff, int mode);
	virtual void onDie();
	virtual void damageArmor(float points)
	{
		damageArmor(points, NULL);
	}
	virtual void damageArmor(float points, ClientActor *attacker)
	{
	}
	virtual float getBasicAttackPoint(ATTACK_TYPE atktype)
	{
		return 0;
	}
	virtual float getBasicArmorPoint(ATTACK_TYPE atktype)
	{
		return 0;
	}
	float getKnockbackResistance();

	/*
*	是否拥有属性变身 buff
*/
	virtual bool isHasAttrShapeShiftBuff(int buffid, int bufflv) { return false; }
	/*
	*	设置属性变身状态，属性来自 mobid 得生物属性
	*/
	virtual void setAttrShapeShift(bool change, int mobid = 0) {};

	void removeBuffEffect(int buffid);//移除1017001特效

	virtual void temperatureTick();
	void checkPosTemperatureSuit(float& posTemp, float defend);

	virtual void radiationTick();

	void randomCreateBlockTick();
	void changeAroundLiquidTick();

	void randomAddStateToAttacker(ClientActor* attacker);
	void randomAddStateToAttack(ClientActor* attacker);

private:
	//尝试修复tick循环的野指针
	bool removeBuffFix2(ActorBuff* buff); //void removeBuffByIndex
	bool removeBuffFix(int buffid, bool bRemoveSingleOne = true, int buffInstanceId = 0); //void removeBuff
	bool m_hasClearBuffs;
public:
	std::vector<ActorBuff> m_Buffs;
	std::vector<int> m_RemoveBuffIds;
	std::vector<AttribModified> m_Attribs;
	std::map<int, int> m_tempBuffsMap;	// 缓冲buff区，存入循环当前buff添加buff效果的时候，需要添加的buff，下帧加入buff队列

	std::vector<EquipEntryStatus> m_EquipBuffs;
	std::vector<BuffDef> m_EquipBuffDefs;
    /**
    @brief	buff状态免疫
    */
    std::set<int> m_immuneBuff;
	MonsterDef* m_AttrShapeShiftDef;// 属性变身的怪物表
protected:

	unsigned int m_Tick;
	float m_Oxygen;
	float m_fBaseAttack[2];
	float m_fBaseArmor[2];

	float m_fBodyTypeArmor[ATTACK_BODY_MAX];

	int m_iStatusIdx;
	bool m_hasBadBuff;
	int m_iWaterPressure;// 水压
	float m_TemperatureBuffDefend;// 温度buff提供得抗性，不会突破适宜温度
	float m_TemperatureEnchantDefend;// 温度附魔抗性，不会突破适宜温度
	float m_TemperatureEquipDefend;// 温度装备抗性，不会突破适宜温度
	float m_TemperatureBuffEffect;// 温度buff效果改变，可以突破适宜温度
	/**
	@brief 自身温度
	*/
	float m_SelfTemperature;
	/**
	@brief 当前位置受到得温度
	*/
	float m_PosTemperature;
	/**
	@brief 自身温抗属性，不会突破适宜温度
	*/
	float m_TemperatureDefend;
	/**
	@brief 最后表现得温度
	*/
	float m_Temperature;
	/**
	@brief 温度tick变化大小
	*/
	float m_TickTemperatureChangeVal;
	float m_BaseTemperatureChangeVal;
	float m_FinalPosTemperature; // 修正后的位置温度
	float m_TickTemperatureChangeRate;

	// radiation
	float m_Radiation;
	float m_MaxRadiation;

	std::map<int, std::vector<StatusAttInfo> > m_StatusAddAttInfo;//[BuffAttrType类型, 数值数组]

	std::function<void(int, int)> m_delegateBuffAppend; // 委托：效果附加(加载时不触发)
	std::function<void(int, int)> m_delegateBuffRemove; // 委托：效果移除(释放时不触发)

	int m_lastAttackBuffId;
	int m_lastAttackBuffLv;
	int m_AreaSourceIndex;
	bool m_CreateBlockEnable;
	bool m_AreaSourceWait;
	bool m_LiquidBlockEnable;

	int m_ToughnessTotalMax;	// 总韧性最大值
	int m_ToughnessTotal;		// 总韧性属性
	int m_ToughnessBase;		// 生物基础韧性属性
	int m_ToughnessEquip;		// 手持道具韧性属性
	bool m_FirstSearch;// 优先搜索用于ai中搜寻目标
	std::map<int, int> m_arrows; //中剑返还记录
	std::map<long long, int> m_attackedNumMap;	// 受击次数统计（攻击者objid - 攻击次数）
	struct EquipPosTypeChangeData
	{
		int _equipData = 0;
		void reset()
		{
			_equipData = 0;
		}
		
		int getGroupType()
		{
			return _equipData >> 30;
		}
		
		int getSourceTypeORIdx()
		{
			if (_equipData == 0) return -1;
			return (_equipData & 0x3FFFFFFF) >> 26;
		}
		
		int getSourceItemId()
		{
			return _equipData & 0x03FFFFFF;
		}
		
		void setGroupData(int groupType, int sourceIdx, int itemId)
		{
			_equipData = (groupType << 30) | (sourceIdx << 26) | itemId;
		}
	};
	// 装备类型到数据位的映射
	//std::map<EQUIP_SLOT_TYPE, EquipPosTypeChangeData> m_EquipType2Pos;
	EquipPosTypeChangeData m_EquipType2PosList[MAX_EQUIP_SLOTS];
	// 数据位到装备类型的映射
	//std::map<EQUIP_BACK_INDEX, EquipPosTypeChangeData> m_EquipPos2Type;
	EquipPosTypeChangeData m_EquipPos2TypeList[EQUIP_BACK_INDEXMAX];
private:
	// 通知监听
	MNSandbox::AutoRef<MNSandbox::Listener<bool&>> m_listenerBlockClipTrap;
	MNSandbox::AutoRef<MNSandbox::Listener<int, int, int, int, long long>> m_listenerLiving1;
	MNSandbox::AutoRef<MNSandbox::Listener<int, bool, int>> m_listenerLiving2;
	MNSandbox::AutoRef<MNSandbox::Listener<int *, std::string& >> m_listenerLiving3;
	MNSandbox::AutoRef<MNSandbox::Listener<BackPackGrid* &, int, int&>> m_listenerLiving4;
};//tolua_exports


#endif
