#pragma once


#include "ClientActorProjectile.h"

class ClientActorHook :public ClientActorProjectile //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ClientActorHook();
	flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	bool load(const void *srcdata, int version) override;
	void onImpactWithActor(ClientActor *actor, const std::string& partname);
	void onImpactWithBlock(const WCoord *blockpos, int face);
	virtual void onCollideWithPlayer(ClientActor *player)override;
	void tick();
	void update(float dtime);
	void init(int itemid, ClientActor *shooter = nullptr);
	//void onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum);
	void RenderCurve();
	//virtual void onSectionCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum) override{};
	ClientPlayer *getShootingPlayer();
	void onAttackActor(ClientActor *actor);
	void getViewBox(CollideAABB &box);
	virtual void enterWorld(World *pworld) override;
	virtual void leaveWorld(bool keep_inchunk) override;
	virtual int getObjType()const override
	{
		return OBJ_TYPE_HOOK;
	}
	virtual bool supportSaveToPB()
	{
		return false;
	}
	int getState() {return m_nState;}
	//tolua_end
protected:
	~ClientActorHook();
private:
	int m_nState;
	WCoord m_blockpos;
	int m_nHandItem;
	int m_nLifeTick;
}; //tolua_exports

