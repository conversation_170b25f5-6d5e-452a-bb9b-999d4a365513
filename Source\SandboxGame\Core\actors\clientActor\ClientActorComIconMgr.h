#pragma once
#include "SandboxGame.h"
#include "BaseClass/SharePtr.h"
#include "Common/GameStatic.h"
#include "Common/SingletonDefinition.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Graphics/Texture2D.h"
#include "OgreWCoord.h"
#include "Text3D/Image3D.h"
#include <string>
#include <vector>
#include <unordered_map>

class ClientActor;

class EXPORT_SANDBOXGAME ClientActorComIconMgr
{
public:
	struct IconInfo
	{
		std::string strIcon;
		int nOrder;
		Rainbow::PPtr <Rainbow::Image3D> pptrImg3D;
		IconInfo(const char* szIcon, int nOrderArg, Rainbow::Image3D* pImg3DArg)
		:pptrImg3D(pImg3DArg)
		{
			strIcon = szIcon;
			nOrder = nOrderArg;
		}
	};

	struct RegInfo
	{
		bool bShow;
		std::vector<IconInfo> vecIcons;
		RegInfo() {
			bShow = true;
		}
	};

	ClientActorComIconMgr();
	~ClientActorComIconMgr();
	void RegComIcon(long long llObjId, const char* szIcon, int nOrder);
	void UnregComIcon(long long llObjId, const char* szIcon);
	void SetActorComIconVisible(long long llObjId, bool bVisible);
	void Update(bool bIsEditing, float dtime);

protected:
	Rainbow::Vector3f WorldToScreenPoint(const Rainbow::Vector3f& blockSpacePoint);
	bool FloatEqual(float lValue, float rValue);
	bool IsPointInBox(const WCoord& iTargetCoord, const WCoord& iBoxStartCoord, const WCoord& iBoxEndCoord);
	void GetActorCollideBox(ClientActor* actor, Rainbow::AABB& box);
private:
	std::string				   m_XmlPackResPath = "ui/mobile/texture0/sceneeditor";
	std::unordered_map<long long, RegInfo> m_unoRegedInfo;
};

EXPORT_SANDBOXGAME \
ClientActorComIconMgr& GetClientActorComIconMgr();
EXPORT_SANDBOXGAME \
ClientActorComIconMgr* GetClientActorComIconMgrPtr();