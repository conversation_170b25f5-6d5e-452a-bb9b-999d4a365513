#pragma once
#include "ClientMob.h"


class ClientAquaticMob:public ClientMob //tolua_exports
{ //tolua_exports
	DECLARE_SCENEOBJECTCLASS(ClientAquaticMob)
public:
	//tolua_begin
	int getWaterUseInterval();
	virtual bool init(int monsterid);
	ClientAquaticMob();
//	~ClientAquaticMob();
	virtual void tick();
	virtual void update(float dtime);
	virtual ActorLocoMotion *newLocoMotion();
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual int getObjType()const override
	{
		return OBJ_TYPE_AQUATICMONSTER;
	}
	virtual bool supportSaveToPB()
	{
		return true;
	}
	virtual int saveToPB(PB_GeneralEnterAOIHC* pb);
	virtual int LoadFromPB(const PB_GeneralEnterAOIHC& pb);

	virtual void applyActorCollision(ClientActor *actor);
	virtual bool load(const void *srcdata, int version) override;
	virtual bool canDespawn()
	{
		return false;
	}
	virtual void onClear();
	virtual void onDie();
	virtual void moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks);
	virtual bool attackedFrom(OneAttackData& atkdata, ClientActor* attacker);
	float getSwimSpeed();
	//tolua_end

	ClientPlayer* selectNearPlayer(int range, int height);
	void actorElasticCollision(ClientActor* actor);
	void emitSpawnSharkPoint();
	virtual void onFeedCallback(int breedType, int feed_ret, ClientPlayer* player = NULL); //breedItemType:食物类型
	virtual int onFeed(int itemid, int feedType = 0);
public:
	int shark_source;//鲨鱼来源
private:
	int m_DroughtTolerance;
	int m_fromAttackUin;
}; //tolua_exports