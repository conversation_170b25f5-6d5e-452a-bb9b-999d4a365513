#pragma once

#include "WorldManager.h"
#include "SoundComponent.h"
#include "DieComponent.h"
#include <random>

class PlayerAttrib;
class ClientPlayer;

// 前向声明
class PlayerDownedState;
class DownedStateNormal;
class DownedStateBleedOut;
class DownedStateBeingRevived;

// 计时常量
const int TICKS_PER_SECOND = 20;  // 1秒 = 20tick

// 倒地状态配置结构体
struct DownedStateConfig {
	float downed_health_max;                // 倒地血量上限
	float max_revive_health_percent;        // 被救援后恢复的血量百分比
	float downed_health_decay_rate;         // 倒地血量每秒衰减率（可保留但不使用）
	int total_downed_ticks;                 // 倒地状态的总持续tick数
	float downed_health_decay_per_tick;     // 每tick的血量衰减率（由total_downed_ticks计算得出）
	float revive_speed;                     // 救援速度
	float revive_speed_per_tick;            // 每tick的救援进度
	float revive_max_distance;              // 最大救援距离
	float fatal_damage_threshold;           // 致命伤害阈值(超过此值直接死亡)
	float revive_immunity_duration;         // 救援后的无敌时间
	float downed_damage_multiplier;         // 倒地状态下受到伤害的倍率

	// 自救相关配置
	float self_revive_success_chance;       // 自救成功概率 (0.0-1.0)
	float self_revive_health_amount;        // 自救成功恢复的固定血量值(10点)
	int self_revive_immunity_ticks;         // 自救后的免疫期tick数（在此期间再次HP为0直接死亡）

	// 被其他玩家救援后的免疫期相关配置
	int revive_immunity_ticks;              // 被其他玩家救援后的免疫期tick数（在此期间再次HP为0直接死亡）
};

// 倒地状态属性类
class PlayerDownedStateAttrib;

// 定义完整的基类 PlayerDownedState
class PlayerDownedState {
public:
	virtual ~PlayerDownedState() {}
	
	// 进入状态
	virtual void enter(PlayerDownedStateAttrib* context) = 0;

	// 退出状态
	virtual void exit(PlayerDownedStateAttrib* context) = 0;

	// 状态更新
	virtual void tick(PlayerDownedStateAttrib* context, int ticks) = 0;

	// 受到伤害处理
	virtual void handleDamage(PlayerDownedStateAttrib* context, float damage) = 0;

	// 调试用状态名称
	virtual const char* getStateName() const = 0;
};

// 倒地状态属性类
class EXPORT_SANDBOXGAME PlayerDownedStateAttrib {
public:
	PlayerDownedStateAttrib(PlayerAttrib* owner, ClientPlayer* player);
	~PlayerDownedStateAttrib();

	// 初始化和重置
	void initialize();
	void reset();

	// 每帧更新
	void tick();

	// 外部接口
	void enterDownedState();
	void exitDownedState();
	bool startRevive(long long reviverId);
	bool cancelRevive();
	bool completeRevive(float healthPercentage);
	void onPlayerDamaged(float damageAmount);
	void addDownedHealth(float amount);
	bool canMove() const;

	// 自救相关接口
	bool attemptSelfRevive();                           // 尝试自救
	bool cancelSelfRevive();                            // 取消自救尝试
	bool hasSelfReviveAttempted() const { return m_bSelfReviveAttempted; }
	void setSelfReviveAttempted(bool attempted) { m_bSelfReviveAttempted = attempted; }
	bool isSelfReviving() const { return m_bIsSelfReviving; }
	void setSelfReviving(bool selfReviving) { m_bIsSelfReviving = selfReviving; }

	// 状态查询
	bool isInNormalState() const;
	bool isInDownedState() const;
	bool isBeingRevived() const;

	// 数据查询
	float getDownedHealth() const { return m_fDownedHealth; }
	float getMaxDownedHealth() const { return m_fMaxDownedHealth; }
	long long getReviverActorId() const { return m_reviverActorId; }
	int getReviveProgress() const { return m_nReviveProgress; }
	const DownedStateConfig& getConfig() const { return m_cfg; }

	// 内部方法，供状态类使用
	PlayerAttrib* getOwner() const { return m_pOwner; }
	ClientPlayer* getPlayer() const { return m_pPlayer; }
	PlayerDownedState* getPreviousState() const { return m_previousState; }  // 获取上一个状态
	DownedStateNormal* getNormalState() const { return m_normalState; }     // 获取正常状态
	void setDownedHealth(float health) { m_fDownedHealth = health; }
	void setReviveProgress(int progress) { m_nReviveProgress = progress; }
	void setReviverActorId(long long id) { m_reviverActorId = id; }
	void setReviveInterrupted(bool interrupted) { m_bReviveInterrupted = interrupted; }
	void setPreDownedHealth(float health) { m_fPreDownedHealth = health; }

	void setReviveSuccessChance(float value) { m_cfg.self_revive_success_chance = value; }
	void setTotalDownedTicks(float value) { m_cfg.total_downed_ticks = value; };
	void setDownedHealthDecayPerTick(float value) { m_cfg.downed_health_decay_per_tick = value; };

	// 自救结果判定
	bool rollForSelfReviveSuccess();

	// Add this method to the public section:
	bool checkDirectDeathConditions();
	void handlePlayerDeath();

	// 状态切换快捷方法
	void changeToNormalState();
	void changeToDownedState();
	void changeToBeingRevivedState();

	// 直接处理倒地状态下的伤害
	void handleDamage(float damage) {
		if (m_currentState) {
			m_currentState->handleDamage(this, damage);
		}
	}

	// 自救免疫期相关方法
	bool isRecentlySelfRevived() const { return m_selfReviveImmunityTicks > 0; }
	int getSelfReviveImmunityTicks() const { return m_selfReviveImmunityTicks; }
	void setSelfReviveImmunityTicks(int ticks) { m_selfReviveImmunityTicks = ticks; }

	// 被救援后免疫期相关方法
	bool isRecentlyRevived() const { return m_reviveImmunityTicks > 0; }
	int getReviveImmunityTicks() const { return m_reviveImmunityTicks; }
	void setReviveImmunityTicks(int ticks) { m_reviveImmunityTicks = ticks; }

	// 添加获取UI更新计时器的方法
	int getUIUpdateTick() const { return m_UIUpdateTick; }
	// 添加网络同步倒地血量变化的方法
	void notifyDownedHealthChange();

	// 辅助方法：检查是否应该执行本地逻辑（非远程模式）
	bool shouldExecuteLocalLogic() const;
private:
	// 状态相关方法
	void changeState(PlayerDownedState* newState);
	void updateUI();
	void playStateEffects(PlayerDownedState* oldState, PlayerDownedState* newState);
	void handleSelfReviveSuccess();
	void sendStateChangeNetwork(PlayerDownedState* oldState, PlayerDownedState* newState);

private:
	PlayerAttrib* m_pOwner;
	ClientPlayer* m_pPlayer;

	// 状态机实现
	PlayerDownedState* m_currentState;
	PlayerDownedState* m_previousState;  // 添加上一个状态的跟踪

	// 状态对象 - 每个实例拥有自己的状态对象
	DownedStateNormal* m_normalState;
	DownedStateBleedOut* m_downedState;
	DownedStateBeingRevived* m_beingRevivedState;

	// 状态数据
	float m_fDownedHealth;           // 当前倒地状态下的血量
	float m_fMaxDownedHealth;        // 倒地状态下的最大血量
	float m_fPreDownedHealth;        // 进入倒地状态前的血量值
	int m_nReviveProgress;           // 复活进度（0-100）
	bool m_bReviveInterrupted;       // 复活是否被中断
	long long m_reviverActorId;      // 正在救援玩家的ID
	int m_UIUpdateTick;              // UI更新计时器
	float m_lastDamageAmount;        // 最近一次受到的伤害值

	// 自救相关数据
	bool m_bSelfReviveAttempted;     // 是否已尝试过自救
	bool m_bIsSelfReviving;          // 是否正在进行自救尝试
	std::mt19937 m_randomEngine;

	// 配置
	DownedStateConfig m_cfg;

	// 濒死免疫期相关数据
	int m_selfReviveImmunityTicks;    // 濒死免疫期剩余tick数
	int m_reviveImmunityTicks;        // 被救援后免疫期剩余tick数
};

// 正常状态
class DownedStateNormal : public PlayerDownedState {
public:
	void enter(PlayerDownedStateAttrib* context) override;
	void exit(PlayerDownedStateAttrib* context) override;
	void tick(PlayerDownedStateAttrib* context, int ticks) override;
	void handleDamage(PlayerDownedStateAttrib* context, float damage) override;
	const char* getStateName() const override { return "Normal"; }
};

// 倒地状态
class DownedStateBleedOut : public PlayerDownedState {
public:
	void enter(PlayerDownedStateAttrib* context) override;
	void exit(PlayerDownedStateAttrib* context) override;
	void tick(PlayerDownedStateAttrib* context, int ticks) override;
	void handleDamage(PlayerDownedStateAttrib* context, float damage) override;
	const char* getStateName() const override { return "BleedOut"; }

	// 特有方法
	void updateDownedHealth(PlayerDownedStateAttrib* context, int ticks);
};

// 被救援状态
class DownedStateBeingRevived : public PlayerDownedState {
public:
	void enter(PlayerDownedStateAttrib* context) override;
	void exit(PlayerDownedStateAttrib* context) override;
	void tick(PlayerDownedStateAttrib* context, int ticks) override;
	void handleDamage(PlayerDownedStateAttrib* context, float damage) override;
	const char* getStateName() const override { return "BeingRevived"; }
};