# 武装直升机事件系统

## 概述
武装直升机事件是一个新的世界事件系统，参照AirDrop空投事件实现，提供了完整的武装直升机AI行为逻辑。

## 文件结构
```
GunShip/
├── GunShipEvent.h          # 主事件类头文件
├── GunShipEvent.cpp        # 主事件类实现
├── GunShipHelicopter.h     # 直升机实体头文件
├── GunShipHelicopter.cpp   # 直升机实体实现
└── README.md               # 本文档
```

## 状态机设计
武装直升机事件包含以下状态：

1. **PREPARING** (准备中) - 事件初始化，准备生成直升机
2. **PATROLLING** (巡逻中) - 在指定区域内巡逻，寻找目标
3. **COMBAT** (战斗中) - 发现目标，进入战斗模式
4. **ESCAPING** (逃跑中) - 血量过低或其他条件下逃离战场
5. **DISENGAGING** (脱战中) - 暂时脱离战斗，准备重新巡逻
6. **COMPLETED** (结束) - 事件结束

## 主要特性

### 事件系统 (GunShipEvent)
- 完整的状态机管理
- 目标检测和管理
- 战斗逻辑控制
- 事件生命周期管理

### 直升机实体 (GunShipHelicopter)
- 多种移动模式：巡逻、战斗、逃跑
- 武器系统：机枪、火箭弹、机炮
- 物理碰撞检测
- 音效和视觉效果
- 生命值系统

## 集成方式

### WorldEventManager集成
已在WorldEventManager中添加对GunShip事件的支持：

```cpp
// 事件类型识别
if (eventType == "sys_gunship" || eventType == "player_gunship") {
    event_instance = std::make_shared<GunShipEvent>(*eventdef);
}

// 外部触发接口
bool TriggerGunShipEvent(int eventid, int pos_x, int pos_y, int pos_z);
```

### 配置参数
事件通过WorldEventDef配置，支持以下参数：
- `pos_x, pos_z`: 巡逻中心位置
- `pos_y`: 直升机高度
- `drop_pos2.x`: 巡逻半径
- `drop_pos2.z`: 检测范围
- `dropType`: 事件类型标识(2=武装直升机)

## 使用方法

### 触发事件
```cpp
WorldEventManager* manager = GetWorldEventManager();
manager->TriggerGunShipEvent(eventId, centerX, height, centerZ);
```

### 事件配置
在CSV配置文件中添加武装直升机事件：
```
ID,EventType,MaxHeight,drop_pos1,drop_pos2,dropType
1001,sys_gunship,100,"0,80,0","200,150,0",2
```

## 待完善功能
以下功能留作TODO，需要后续实现：

1. **位置生成逻辑** - 根据地图生成合适的生成点和巡逻中心
2. **玩家检测** - 实现范围内玩家检测和目标管理
3. **武器发射** - 具体的武器攻击逻辑和伤害计算
4. **碰撞检测** - 与地形和建筑物的碰撞检测
5. **游戏世界集成** - 在游戏世界中创建和管理实体
6. **音效和特效** - 各种音频和视觉效果的播放
7. **网络同步** - 多人游戏中的状态同步

## 扩展建议
- 可以添加更多直升机类型（运输、侦察等）
- 支持编队飞行
- 添加更复杂的AI行为（如掩护、撤退等）
- 支持可定制的攻击模式