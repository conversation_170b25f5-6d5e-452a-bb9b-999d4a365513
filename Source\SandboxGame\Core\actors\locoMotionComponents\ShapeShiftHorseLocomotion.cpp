
#include "ShapeShiftHorseLocomotion.h"
#include "ActorLocoMotion.h"
#include "world.h"
#include "ActorHorse.h"
#include "ActorShapeShiftHorse.h"
#include "SoundComponent.h"
#include "EffectComponent.h"
#include "RiddenComponent.h"
#include "defdata.h"
static Vector3f s_MotionDecay(0.9f, 0.98f, 0.9f);
static float s_InWaterForce = 1.0f;
IMPLEMENT_COMPONENTCLASS(ShapeShiftHorseLocomotion)

void ShapeShiftHorseLocomotion::tick()
{
	ActorLocoMotion::tick();
	//LivingLocoMotion::tick();

	WCoord oldpos = m_Position;
	float motionlen = Rainbow::Sqrt(m_Motion.x*m_Motion.x + m_Motion.z*m_Motion.z);

	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		if (m_PosRotationIncrements > 0)
		{
			m_Position = m_Position + (m_ShapeShiftHorsePos - m_Position) / m_PosRotationIncrements;
			m_RotateYaw = m_RotateYaw + WrapAngleTo180(m_ShapeShiftYaw - m_RotateYaw) / m_PosRotationIncrements;
			m_RotationPitch = m_RotationPitch + (m_ShapeShiftPitch - m_RotationPitch) / m_PosRotationIncrements;
			m_PosRotationIncrements--;
		}
		else
		{
			m_Position = m_Position + getIntegerMotion(m_Motion);
			m_Motion *= s_MotionDecay;
		}
		syncPhysActorPosByLocPos();
	}
	else
	{
		if (!getOwnerActor())
			return;

		if (m_MaxSpeed == 0)
		{
			ActorShapeShiftHorse *horse = static_cast<ActorShapeShiftHorse *>(getOwnerActor());
			if (horse)
			{
				m_MaxSpeed = (float)(horse->getRiddenLandSpeed() / 20);  //每tick的速度
				speedMultiplier = m_MaxSpeed;
			}
		}

		bool hasPower = false;
		float MAX_MOTION_LEN = m_MaxSpeed > 0 ? m_MaxSpeed : 50.0f;

		m_Motion.y = calGravityMotionY(m_Motion.y);
		auto RidComp = getOwnerActor()->getRiddenComponent();
		ActorLiving *riddenby = NULL;
		if (RidComp)
		{
			riddenby = dynamic_cast<ActorLiving *>(RidComp->getRiddenByActor());
		}
		if (riddenby)
		{
			LivingLocoMotion *locomove = static_cast<LivingLocoMotion *>(riddenby->getLocoMotion());
			if (locomove)
			{
				ActorShapeShiftHorse *horse = static_cast<ActorShapeShiftHorse *>(getOwnerActor());
				if (horse && !horse->isFloatageing())
				{
					//注意这里有个深坑  坐骑类的m_Motion有个清空处理(满足条件locomove->m_MoveStrafing == 0 && locomove->m_MoveForward == 0)下
					//如果不这样处理,会出现 停止运动后 动作反复的情况(seq_walk 与 seq_stand切换播放)//|| horse->getHorseDef()->ID == 3494
					//if(horse&&horse->getHorseDef()->ID == 3494){
					//	if (locomove->m_MoveStrafing <= 0.00001 && locomove->m_MoveStrafing >= -0.00001  && locomove->m_MoveForward <= 0.00001 && locomove->m_MoveForward >= -0.00001) {
					//		m_Motion = Vector3f(0, m_Motion.y, 0);
					//	}
					//}

					// 解决 部分坐骑 从 seq_walk （行走状态）切换到 seq_stand（idle状态）时动作抽搐的问题
					if (horse && (horse->getHorseDef()->ID == 4622 || 
						horse->getHorseDef()->ID == 4642 ||
						horse->getHorseDef()->ID == 4676 ||
						horse->getHorseDef()->ID == 4640)) 
					{
						if (locomove->m_MoveStrafing <= 0.00001 && locomove->m_MoveStrafing >= -0.00001
							&& locomove->m_MoveForward <= 0.00001 && locomove->m_MoveForward >= -0.00001) 
						{
							m_Motion = Vector3f(0, m_Motion.y, 0);
						}
					}

					const StoreHorseDef* storeHorseDef = GetDefManagerProxy()->getStoreHorseByID(horse->getHorseDef()->ID);

					//指定动物坐骑可以横着走
					if (horse->getHorseDef()->ID == 3477 || 
						horse->getHorseDef()->ID == 3473 || 
						horse->getHorseDef()->ID == 3482 || 
						horse->getHorseDef()->ID == 4568 ||
						horse->getHorseDef()->ID == 4570 ||
						horse->getHorseDef()->ID == 3494 ||
						horse->getHorseDef()->ID == 4622 ||
						horse->getHorseDef()->ID == 4642 || 
						horse->getHorseDef()->ID == 4640 ||
						horse->getHorseDef()->ID == 4658 || (storeHorseDef && storeHorseDef->Move == 1)) {

						if (horse->getHorseDef()->ID != 3482)//兔子需要处理一下
						{
							m_fSpeedUpTime = 0;
						}
						if (locomove->m_MoveStrafing != 0 && locomove->m_MoveForward != 0) {
							Vector3f dir;
							float yaw = locomove->m_RotateYaw;
							MAX_MOTION_LEN = m_MaxSpeed / 2;
							if (locomove->m_MoveForward > 0 && locomove->m_MoveStrafing > 0) {
								yaw += 45.0f;
							}
							else if (locomove->m_MoveForward > 0 && locomove->m_MoveStrafing < 0) {
								yaw += 315.0f;
							}
							else if (locomove->m_MoveForward < 0 && locomove->m_MoveStrafing < 0) {
								yaw += 225.0f;
							}
							else if (locomove->m_MoveForward < 0 && locomove->m_MoveStrafing > 0) {
								yaw += 135.0f;
							}
							PitchYaw2Direction(dir, yaw, 0);
							m_Motion += dir * (m_MaxSpeed*0.05f);
							hasPower = false;
						}
						else if (locomove->m_MoveStrafing != 0 || locomove->m_MoveForward != 0) {
							Vector3f dir;
							float yaw = locomove->m_RotateYaw;
							MAX_MOTION_LEN = m_MaxSpeed / 2;
							float speed = m_MaxSpeed * 0.9f;
							if (locomove->m_MoveStrafing < 0)
							{
								yaw += 270.0f;
							}
							else if (locomove->m_MoveStrafing > 0) {
								yaw += 90.0f;
							}

							if (locomove->m_MoveForward < 0)  //后退
							{
								yaw += 180.0f;
							}
							else if (locomove->m_MoveForward > 0) {
								speed = m_MaxSpeed;
							}
							PitchYaw2Direction(dir, yaw, 0);

							m_Motion += dir * (speed*0.05f);
							hasPower = false;
						}
						if (locomove->m_MoveStrafing == 0 && locomove->m_MoveForward == 0) {
							m_Motion = Vector3f(0, m_Motion.y, 0);
						}
					}
					else {
						if (locomove->m_MoveForward != 0)
						{
							Vector3f dir;
							float yaw = locomove->m_RotateYaw;
							static bool isBraking = false;
							if (locomove->m_MoveForward < 0)  //后退
							{
								yaw += 180.0f;
								MAX_MOTION_LEN = m_MaxSpeed / 2;
							}

							PitchYaw2Direction(dir, yaw, 0);
							Vector3f tempMotion = m_Motion;
							tempMotion.y = 0;
							if (tempMotion.Length() > 10.0f && DotProduct(tempMotion, dir) < 0 && !isBraking)
							{
								char soundName[64] = { 0 };
								sprintf(soundName, "ent.%d.brake", getOwnerActor()->getDefID());
								auto sound = riddenby->getSoundComponent();
								if (sound)
								{
									sound->playSound(soundName, 1.0f, 1.0f);
								}
								isBraking = true;
							}
							else
								isBraking = false;

							m_Motion += dir * (speedMultiplier*0.05f);

							hasPower = true;
						}
					}
				}
			}

		}

		float newlen = Rainbow::Sqrt(m_Motion.x*m_Motion.x + m_Motion.z*m_Motion.z);

		if (newlen > MAX_MOTION_LEN)
		{
			float t = MAX_MOTION_LEN / newlen;
			m_Motion.x *= t;
			m_Motion.z *= t;
			newlen = MAX_MOTION_LEN;
		}

		if (newlen > motionlen && speedMultiplier < MAX_MOTION_LEN)
		{
			speedMultiplier += (MAX_MOTION_LEN - speedMultiplier) / MAX_MOTION_LEN;

			if (speedMultiplier > MAX_MOTION_LEN)
			{
				speedMultiplier = MAX_MOTION_LEN;
			}
		}
		else
		{
			speedMultiplier -= (speedMultiplier - MAX_MOTION_LEN / 5.0f) / MAX_MOTION_LEN;

			if (speedMultiplier < MAX_MOTION_LEN / 5.0f)
			{
				speedMultiplier = MAX_MOTION_LEN / 5.0f;
			}
		}

		bool canTurn = false;
		Vector3f speedUpMotion(0, 0, 0);
		Vector3f turnMotion(0, 0, 0);
		if (m_fSpeedUpTime > 0 && riddenby)
		{
			LivingLocoMotion *locomove = static_cast<LivingLocoMotion *>(riddenby->getLocoMotion());
			if (locomove)
			{
				Vector3f dir;
				PitchYaw2Direction(dir, m_RotateYaw, 0);
				speedUpMotion = dir * m_fSpeedUpVal;

				PitchYaw2Direction(dir, locomove->m_RotateYaw, 0);
				turnMotion = dir * m_fSpeedUpVal;
				canTurn = true;
			}
		}
		doMoveStep(m_Motion + speedUpMotion + m_ExtraMotion);
		m_ExtraMotion = Vector3f(0, 0, 0);
		m_Motion *= s_MotionDecay;
		m_RotationPitch = 0.0f;

		float targetyaw = m_RotateYaw;
		float targetPitch = m_RotationPitch;
		Vector3f dpos = m_Motion;
		dpos.y = 0;
		float curSpeedSqr = dpos.LengthSqr();
		if (!hasPower && canTurn) //松开了按键但使用加速技能的时候，给与转弯
		{
			dpos += turnMotion;
			dpos.y = 0;
			curSpeedSqr = dpos.LengthSqr();
			hasPower = true;
		}

		if (curSpeedSqr > 10.0f && hasPower)
		{
			if (riddenby)
			{
				LivingLocoMotion *locomove = static_cast<LivingLocoMotion *>(riddenby->getLocoMotion());
				if (locomove)
				{
					Vector3f dir;
					PitchYaw2Direction(dir, locomove->m_RotateYaw, 0);

					if (DotProduct(dpos, dir) < 0)  //后退不用转向
					{
						dpos.x = 0 - dpos.x;
						dpos.z = 0 - dpos.z;
					}
				}
			}

			Direction2PitchYaw(&targetyaw, NULL, dpos);
		}


		if (riddenby) {
			LivingLocoMotion *locomove = static_cast<LivingLocoMotion *>(riddenby->getLocoMotion());
			ActorShapeShiftHorse *horse = static_cast<ActorShapeShiftHorse *>(getOwnerActor());
			int horseResID = horse != NULL ? horse->getHorseDef()->ID : 0;
			const StoreHorseDef* storeHorseDef = GetDefManagerProxy()->getStoreHorseByID(horseResID);

			if (horseResID == 3477 || horseResID == 3473 || horseResID == 3482 ||
				horseResID == 3494 || horseResID == 4568 || horseResID == 4570 || 
				horseResID == 4622 || horseResID == 4642 || horseResID == 4640 ||
				horseResID == 4658 || (storeHorseDef && storeHorseDef->Move == 1))//变形坐骑是否可以左右移动（0=不可以，1=可以）
			{
				if (m_RotateYaw != locomove->m_RotateYaw&&locomove->m_MoveForward > 0) {
					m_Motion = Vector3f(0, m_Motion.y, 0);
					Vector3f dir;
					PitchYaw2Direction(dir, m_RotateYaw, 0);
					m_Motion += dir * speedMultiplier;
				}
				m_RotateYaw = m_PrevRotateYaw = locomove->m_RotateYaw;
				//m_RotationPitch = m_PrevRotatePitch = locomove->m_RotationPitch * 0.5f;

			}
			else {
				if (locomove->m_MoveForward != 0) {
					m_RotateYaw += Rainbow::Clamp(WrapAngleTo180(targetyaw - m_RotateYaw), -20.0f, 20.0f);
				}
			}
		}

		if (m_fSpeedUpTime > 0)
		{
			m_fSpeedUpTime -= 0.05f;
		}
		else if (m_bWaitStopEffect)
		{
			m_bRabbitRun = false;
			ActorShapeShiftHorse *horse = static_cast<ActorShapeShiftHorse *>(getOwnerActor());
			m_bWaitStopEffect = false;
			auto effectComponent = getOwnerActor()->getEffectComponent();
			if (effectComponent)
			{
				if (horse&&horse->hasHorseSkill(HORSE_SKILL_RED_LINGHTNING_SPRINT))
					effectComponent->stopBodyEffect("horsechange_3466_1");
				else if (horse&&horse->hasHorseSkill(HORSE_SKILL_RABBIT_SPRINT))
				{
					effectComponent->stopBodyEffect("horse_3481_speedup");
				}
				else
					effectComponent->stopBodyEffect("horsechange_3453_1");
			}
		}
	}
}
