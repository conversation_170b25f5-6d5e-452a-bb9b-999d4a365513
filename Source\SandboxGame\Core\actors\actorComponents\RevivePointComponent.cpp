#include "RevivePointComponent.h"

#include "special_blockid.h"
#include "PlayerControl.h"
#include "BlockBed.h"
#include "ActorLocoMotion.h"
#include "ClientActorHelper.h"
#include "SoundComponent.h"
#include "GameNetManager.h"
#include "EffectManager.h"
#include "WorldManager.h"

using namespace MNSandbox;


IMPLEMENT_COMPONENTCLASS(RevivePointComponent)

RevivePointComponent::RevivePointComponent()
:m_RevivePoint(WCoord(0, -1, 0))
,m_TriggerRevivePoint(WCoord(0, -1, 0))
,m_SpawnPoint(WCoord(0, -1, 0))
,m_SpawnForced(false)
//,m_LastInteractSpBlock(500)
,m_bFirstPlayReviveEffect(false)
{
	m_AccountWorldPoint.resize(0);
	m_InteractSpBlockList.resize(0);
}

void RevivePointComponent::syncRevivePoint(int uin)
{
	World* pWorld = GetOwnerActor()->getWorld();

	if (!pWorld || pWorld->isRemoteMode())
	{
		return;
	}

	if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->getPlayerByUin(uin)))
	{
		return;
	}

	std::map<int, std::vector<AccountWorldPointInfo>>& ClientWorldPointMap = GetWorldManagerPtr()->getClientAccountWorldPointMap();

	IT_CLIENTWPMAP itMap = ClientWorldPointMap.find(uin);
	ClientPlayer *player = dynamic_cast<ClientPlayer*>(GetWorldManagerPtr()->getPlayerByUin(uin));

	if (itMap == ClientWorldPointMap.end())
	{
		if (player->getWorld())
		{
			unsigned short CurMapID = player->getWorld()->getCurMapID();
			WCoord spawnpoint = WCoord(0, -1, 0);
			WCoord revivepoint = WCoord(0, -1, 0);

			if (getAccountWorldPoint(CurMapID, spawnpoint, revivepoint))
			{
				
				sendRevivePoint(uin, CurMapID, spawnpoint, revivepoint);
				//player->setAccountWorldPoint(CurMapID, spawnpoint, revivepoint);
				SandboxContext context;
				context.SetData_UserObject("spawn", spawnpoint);
				context.SetData_UserObject("revive", revivepoint);
				context.SetData_Number("mapid", CurMapID);
				player->Event().Emit("revive_setAccountWorldPoint", context);
			}
		}

		return;
	}

	for (size_t i = 0; i < (itMap->second).size(); i++)
	{
		AccountWorldPointInfo& src = (itMap->second)[i];
		sendRevivePoint(uin, src.mapid, src.spawnpoint, src.revivepoint);
		//player->setAccountWorldPoint(src.mapid, src.spawnpoint, src.revivepoint);
		SandboxContext context;
		context.SetData_UserObject("spawn", src.spawnpoint);
		context.SetData_UserObject("revive", src.revivepoint);
		context.SetData_Number("mapid", src.mapid);
		player->Event().Emit("revive_setAccountWorldPoint", context);
	}
}

void RevivePointComponent::sendRevivePoint(int uin, int mapid, WCoord& spawnpoint, WCoord& revivepoint)
{
	if (GetOwnerActor()->getWorld()->isRemoteMode())
	{
		return;
	}

	PB_PlayerRevivePointHC playerRevivePointHC;
	playerRevivePointHC.set_uin(uin);
	playerRevivePointHC.set_mapid(mapid);

	PB_Vector3* revivePos = playerRevivePointHC.mutable_revivepoint();
	revivePos->set_x(revivepoint.x);
	revivePos->set_y(revivepoint.y);
	revivePos->set_z(revivepoint.z);

	PB_Vector3* spawnPos = playerRevivePointHC.mutable_spawnpoint();
	spawnPos->set_x(spawnpoint.x);
	spawnPos->set_y(spawnpoint.y);
	spawnPos->set_z(spawnpoint.z);

	GetGameNetManagerPtr()->sendToClient(uin, PB_PLAYER_REVIVEPOINT_HC, playerRevivePointHC);
}


void RevivePointComponent::checkChangeSpawnPoint(const WCoord& blockpos, int mapid)
{
	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	World* pWorld = GetOwnerActor()->getWorld();
	int uin = owner->getUin();

	if (!pWorld->isRemoteMode())
	{
		PB_PlayerSpawnPointHC addSpawnPointHC;
		addSpawnPointHC.set_uin(uin);
		addSpawnPointHC.set_x(blockpos.x);
		addSpawnPointHC.set_y(blockpos.y);
		addSpawnPointHC.set_z(blockpos.z);
		addSpawnPointHC.set_mapid(mapid);
		GetGameNetManagerPtr()->sendToClient(uin, PB_PLAYER_SPAWN_POINT_HC, addSpawnPointHC);
	}
}

void RevivePointComponent::setSpawnPoint(const WCoord &blockpos, bool bSetWorldMgr/* = true*/)
{
	m_SpawnPoint = blockpos;
	World* pWorld = GetOwnerActor()->getWorld();
	
	if (GetWorldManagerPtr() && pWorld)
	{
		int curmapid = pWorld->getCurMapID();
		if (bSetWorldMgr)
		{
			GetWorldManagerPtr()->setSpawnPointEx(m_SpawnPoint, pWorld);
		}

		bool bExist = false;
		for (size_t i = 0; i < m_AccountWorldPoint.size(); i++)
		{
			AccountWorldPointInfo& src = m_AccountWorldPoint[i];
			if (src.mapid == curmapid)
			{
				src.spawnpoint = m_SpawnPoint;
				bExist = true;
				break;
			}
		}

		if (!bExist)
		{
			AccountWorldPointInfo src;
			src.mapid = curmapid;
			src.revivepoint = WCoord(0, -1, 0);
			src.spawnpoint = m_SpawnPoint;

			m_AccountWorldPoint.push_back(src);
		}
	}
}

WCoord RevivePointComponent::getSpawnPoint()
{
	World* pWorld = GetOwnerActor()->getWorld();
	if (GetWorldManagerPtr() && m_SpawnPoint != WCoord(0, -1, 0) && pWorld && pWorld->getBlock(m_SpawnPoint).getResID() == BLOCK_PERSONALSPAWN) // ¸öÈË¸´»îÖØÉúµã
	{
		return GetWorldManagerPtr()->getSpawnPointEx(pWorld);
	}

	return WCoord(0, -1, 0);
}


//ÕâÀïÔÝÊ±Ö»¸ø´¥·¢Æ÷ÖØÉúµãÓÃ£¬ÏÈ¼æÈÝÏÂ
void RevivePointComponent::setRevivePoint(int x, int y, int z)
{
	//WCoord pos = WCoord(x, y, z);
	//setRevivePoint(&pos, true);
	setTriggerRevivePoint(x, y, z);
}

void RevivePointComponent::setTriggerRevivePoint(int x, int y, int z)
{
	m_TriggerRevivePoint = WCoord(x, y, z);
}

bool RevivePointComponent::IsInteractSpBlockValid()
{
	int nBlockID = m_LastInteractSpBlock.nBlockID;
	WCoord point = m_LastInteractSpBlock.interactpoint;

	if (m_RevivePoint == point)
	{
		return false;
	}

	//ç‰¹æ®Šé›•åƒæ ¡éªŒ
	if (!IsSpBlockRevive(nBlockID))
	{
		return false;
	}

	for (size_t i = 0; i < m_InteractSpBlockList.size(); i++)
	{
		const InteractSpBlockInfo& src = m_InteractSpBlockList[i];
		if ((src.nBlockID == nBlockID) && (src.interactpoint == point))
		{
			return false;
		}
	}

	return true;
}

void RevivePointComponent::AddInteractSpBlock(int mapid, int nBlockID, WCoord point)
{
	m_InteractSpBlockList.clear();

	InteractSpBlockInfo src;
	src.nMapID = mapid;
	src.nBlockID = nBlockID;
	src.interactpoint = point;

	m_InteractSpBlockList.push_back(src);
}

void RevivePointComponent::ClearInteractSpBlock()
{
	m_InteractSpBlockList.clear(); 
}

void RevivePointComponent::ResetRevivePoint()
{
	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)	return;

	int uin = owner->getUin();
	int mapid = owner->getCurMapID();
	World* pWorld = owner->getWorld();

	if (m_LastInteractSpBlock.interactpoint != WCoord(0, -1, 0) && IsSpBlockRevive(m_LastInteractSpBlock.nBlockID))
	{
		setRevivePoint(&(m_LastInteractSpBlock.interactpoint), true, true);	
		AddInteractSpBlock(mapid, m_LastInteractSpBlock.nBlockID, m_LastInteractSpBlock.interactpoint);

		if (pWorld->isRemoteMode())
		{
			PB_PlayerRevivePointCH playerRevivePointCH;
			playerRevivePointCH.set_uin(uin);
			playerRevivePointCH.set_mapid(mapid);

			PB_Vector3* revivePos = playerRevivePointCH.mutable_revivepoint();
			revivePos->set_x(m_RevivePoint.x);
			revivePos->set_y(m_RevivePoint.y);
			revivePos->set_z(m_RevivePoint.z);

			PB_Vector3* spawnPos = playerRevivePointCH.mutable_spawnpoint();
			spawnPos->set_x(m_SpawnPoint.x);
			spawnPos->set_y(m_SpawnPoint.y);
			spawnPos->set_z(m_SpawnPoint.z);

			GetGameNetManagerPtr()->sendToHost(PB_PLAYER_REVIVEPOINT_CH, playerRevivePointCH);
		}

		if (pWorld->getBlockID(m_SpawnPoint) == BLOCK_PERSONALSPAWN)
		{
			setSpawnPoint(m_LastInteractSpBlock.interactpoint);
		}

		if (GetWorldManagerPtr() && GetWorldManagerPtr()->isAdventureMode())
		{
			owner->ReviveCostExp();
			auto sound = owner->getSoundComponent();
			if (sound)
			{
				sound->playSound("misc.starblock", 1.0f, 1.0f);
			}
		}
	}	
}



void RevivePointComponent::setAccountWorldPoint(int mapid, WCoord spawnpoint/* = WCoord(0, -1, 0)*/, WCoord revivepoint/* = WCoord(0, -1, 0)*/)
{
	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)	return;
	int curmapid = owner->getCurMapID();

	bool bExist = false;
	for (size_t i = 0; i < m_AccountWorldPoint.size(); i++)
	{
		AccountWorldPointInfo& src = m_AccountWorldPoint[i];
		if (src.mapid == mapid)
		{
			bExist = true;
			if (spawnpoint != WCoord(0, -1, 0))
			{
				src.spawnpoint = spawnpoint;
				if (curmapid == mapid)
				{
					m_SpawnPoint = spawnpoint;
				}
			}
			if (revivepoint != WCoord(0, -1, 0))
			{
				src.revivepoint = revivepoint;
				if (curmapid == mapid)
				{
					m_RevivePoint = revivepoint;
				}
			}

			break;
		}
	}

	if (!bExist)
	{
		AccountWorldPointInfo src;
		src.mapid = mapid;
		src.revivepoint = revivepoint;
		src.spawnpoint = spawnpoint;

		m_AccountWorldPoint.push_back(src);

		if (curmapid == mapid)
		{
			m_SpawnPoint = spawnpoint;
			m_RevivePoint = revivepoint;
		}
	}
}



void RevivePointComponent::checkPlayReviveEffect()
{
	//Ã‘Â­Â»Â·Â²Â¥Â·Ã…ÂµÃ±ÃÃ±ÃŒÃ˜ÃÂ§
	if (!m_bFirstPlayReviveEffect)
	{
		if (!GetOwner()) return ;
		ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
		if (!m_owner) return ;
		int mapid = m_owner->getCurMapID();

		WCoord spawnpoint = WCoord(0, -1, 0);
		WCoord revivepoint = WCoord(0, -1, 0);

		if (getAccountWorldPoint(mapid, spawnpoint, revivepoint))
		{
			playClientReviveEffect(mapid, revivepoint);
		}
	}

}

WCoord RevivePointComponent::verifyRespawnCoordinates(World *pworld, const WCoord &pt, bool spawnforced)
{
	pworld->syncLoadChunk(pt, 3);

	if (IsBedBlock(pworld->getBlockID(pt)))
	{
		WCoord retpos;
		if (BedLogicHandle::getNearestEmptyChunkCoordinates(retpos, pworld, pt, 0)) return retpos;
		else return WCoord(0, -1, 0);
	}
	else
	{
		int tempPointOffset[8][2] = { { -1,0 },{ 1,0 },{ 0,-1 },{ 0,1 },{ -1,-1 },{ -1,1 },{ 1,-1 },{ 1,1 } };
		int startindex = GenRandomInt(8);
		for (int i = 0; i < 8; i++)
		{
			int index = (startindex + i) % 8;
			WCoord pt2 = pt + WCoord(tempPointOffset[index][0], 0, tempPointOffset[index][1]);

			if (pworld->doesBlockHaveSolidTopSurface(pt2) && pworld->isAirBlock(pt2 + WCoord(0, 1, 0))
				&& pworld->isAirBlock(pt2 + WCoord(0, 2, 0)))
			{
				return TopCoord(pt2);
			}
			else if (pworld->doesBlockHaveSolidTopSurface(DownCoord(pt2)) && pworld->isAirBlock(DownCoord(pt2) + WCoord(0, 1, 0))
				&& pworld->isAirBlock(DownCoord(pt2) + WCoord(0, 2, 0)))
			{
				return pt2;
			}
		}

		//Ã•Ã’Â²Â»ÂµÂ½Ã‚ÃºÃ—Ã£ÂµÃ„Â¸Â½Â½Ã¼Â¸Â´Â»Ã®ÂµÃ£,Ã”Â­ÂµÃ˜Â¸Â´Â»Ã®
		return pt;
	}
}



bool RevivePointComponent::replacePlayer(bool bSetWorldMgr, bool bGameMakerRunMode, int curToolID)
{
	ClientPlayer* owner = GetOwnerActor()->ToCast<ClientPlayer>();
	World* pWorld = owner->getWorld();
	ActorLocoMotion*  locomotion = owner->getLocoMotion();

	WCoord revivept(0, -1, 0);
	bool succeed_revivept = false;

	if (bGameMakerRunMode)
	{
		revivept = GetWorldManagerPtr()->getTeamSpawnPoint(owner);
		locomotion->gotoPosition(BlockCenterCoord(revivept), 0, 0);

		if (m_RevivePoint == WCoord(0, -1, 0)){
			setRevivePoint(&revivept, true, bSetWorldMgr);
		}
		succeed_revivept = true;
	}
	else
	{
		bool hasWorldPoint = true;
		if (curToolID == ITEM_TRANSFERSCROLL && pWorld)
		{
			WCoord spawnPoint;
			hasWorldPoint = getAccountWorldPoint(pWorld->getCurMapID(), spawnPoint, revivept);
		}

		if (!hasWorldPoint)
			revivept = getRevivePoint();
		else
		{
			revivept = m_RevivePoint;
		}

		bool spawnforced = m_SpawnForced;

		//setRevivePoint(NULL, false);
		if (revivept.y > 0)
		{
			WCoord verifiedpos = verifyRespawnCoordinates(pWorld, revivept, spawnforced);
			if (verifiedpos.y > 0)
			{
				locomotion->gotoPosition(BlockCenterCoord(verifiedpos), 0, 0);
				setRevivePoint(&revivept, spawnforced, bSetWorldMgr);
				succeed_revivept = true;
			}
		}
	}
	return succeed_revivept;
}

WCoord RevivePointComponent::getRevivePoint()
{
	if (m_RevivePoint.y > 0)
		return m_RevivePoint;
	if (GetWorldManagerPtr())
	{
		return GetWorldManagerPtr()->getRevivePointEx(GetOwnerActor()->getWorld());
	}

	return m_RevivePoint;
}
void RevivePointComponent::setRevivePoint(const WCoord *pos, bool forced, bool bSetWorldMgr)
{
	World* pWorld = GetOwnerActor()->getWorld();

	//Ã‰ÃÂ¸Ã¶Â¸Â´Â»Ã®ÂµÃ£Ã‹Ã¹Ã”ÃšÃŽÂ»Ã–ÃƒÂµÃ±ÃÃ±
	int nPrevBlockID = pWorld ? pWorld->getBlockID(m_RevivePoint) : BLOCK_UNLOAD;
	if (pWorld && pWorld->getEffectMgr() 
		&& IsSpBlockRevive(nPrevBlockID) 
		&& (m_RevivePoint != *pos) 
		&& (nPrevBlockID != BLOCK_UNLOAD)
		/*&& (nPrevBlockID != BLOCK_PERSONALSPAWN)*/)
	{
		WCoord keypos = BlockCenterCoord(m_RevivePoint);
		pWorld->getEffectMgr()->stopParticleEffect("particles/item_xfuhuo.ent", keypos, false);
	}

	if (pos)
	{
		m_RevivePoint = *pos;
		m_SpawnForced = forced;
	}
	else
	{
		m_RevivePoint = WCoord(0, -1, 0);
		m_SpawnForced = false;
	}

	if (GetWorldManagerPtr() && pWorld)
	{
		if (bSetWorldMgr)
		{
			GetWorldManagerPtr()->setRevivePointEx(m_RevivePoint, pWorld);
		}

		bool bExist = false;
		for (size_t i = 0; i < m_AccountWorldPoint.size(); i++)
		{
			AccountWorldPointInfo& src = m_AccountWorldPoint[i];
			if (src.mapid == pWorld->getCurMapID())
			{
				src.revivepoint = m_RevivePoint;
				bExist = true;
				break;
			}
		}

		if (!bExist)
		{
			AccountWorldPointInfo src;
			src.mapid = pWorld->getCurMapID();
			src.revivepoint = m_RevivePoint;
			src.spawnpoint = WCoord(0, -1, 0);

			m_AccountWorldPoint.push_back(src);
		}

		//Ã‘Â­Â»Â·Â²Â¥Â·Ã…ÂµÃ±ÃÃ±ÃŒÃ˜ÃÂ§
		if (g_pPlayerCtrl && (g_pPlayerCtrl == GetOwnerActor()))
		{
			playClientReviveEffect(pWorld->getCurMapID(), m_RevivePoint);
			g_pPlayerCtrl->updateTaskSysProcess(TASKSYS_SET_POINT, pWorld->getBlockID(m_RevivePoint));
		}
	}

	ClientPlayer* player = GetOwnerActor()->ToCast<ClientPlayer>();
	if(player)
	{
		syncRevivePoint(player->getUin());
	}
	
}


bool RevivePointComponent::GetLastInteractSpBlockList(WCoord & pos, int mapid)
{
	if (m_InteractSpBlockList.size() == 0)
	{
		pos = WCoord(0, -1, 0);
		return false;
	}

	assert(m_InteractSpBlockList.size() == 1);
	InteractSpBlockInfo blockInfo = m_InteractSpBlockList.back();

	if (mapid == -1 || blockInfo.nMapID == mapid)
	{
		pos = blockInfo.interactpoint;
	}
	else
	{
		pos = WCoord(0, -1, 0);
		return false;
	}
	

	return true;
}

World* RevivePointComponent::teleportHome(int curToolID, WCoord& pos)
{
	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)
		return NULL;

	World* pWorld = owner->getWorld();
	World* newworld = NULL;
	//Â´Â«Ã‹ÃÂ¾Ã­Ã–Ã¡Ã–Â»Ã„ÃœÂ´Â«Â»Ã˜ÃˆÂ¥ÂµÃ˜Ã‡Ã²
	if (curToolID == 12964 && (pWorld->getCurMapID() != 0))
	{
		bool createworld = (GetWorldManagerPtr()->getWorld(0) == NULL);
		if (createworld)
		{
			newworld = GetWorldManagerPtr()->getOrCreateWorld(0, owner);
		}
		else
		{
			newworld = GetWorldManagerPtr()->GoToWorld(pWorld, 0, owner);
		}

		pos = WCoord(0, -1, 0);
		WCoord spawnpoint = WCoord(0, -1, 0);
		WCoord revivepoint = WCoord(0, -1, 0);

		bool ret = getAccountWorldPoint(0, spawnpoint, revivepoint);
		if (createworld && newworld)
		{
			if (!ret)//
			{
				//Â´Â´Â½Â¨Â³Ã¶Ã‰ÃºÂµÃ£
				WCoord spt = newworld->createSpawnPoint();
				m_SpawnPoint = spt;
				m_RevivePoint = WCoord(0, -1, 0);
			}
			else
			{
				m_SpawnPoint = spawnpoint;
				m_RevivePoint = revivepoint;
			}

			GetWorldManagerPtr()->setSpawnPointEx(m_SpawnPoint, newworld);
			GetWorldManagerPtr()->setRevivePointEx(m_RevivePoint, newworld);

			spawnpoint = m_SpawnPoint;
			revivepoint = m_RevivePoint;
		}

		pos = revivepoint;
		if (pos.y < 0) {
			pos = spawnpoint;
		}
		//newworld
		//pos
	}
	else
	{
		pos = WCoord(0, -1, 0);
		if (!GetLastInteractSpBlockList(pos)) {
			pos = m_RevivePoint;
		}

		if (pos.y < 0) {
			pos = m_SpawnPoint;
		}
		//pos
	}
	return newworld;
}

bool RevivePointComponent::getAccountWorldPoint(int mapid, WCoord& spawnpoint, WCoord& revivepoint)
{
	for (size_t i = 0; i < m_AccountWorldPoint.size(); i++)
	{
		AccountWorldPointInfo& src = m_AccountWorldPoint[i];
		if (src.mapid == mapid)
		{
			spawnpoint = src.spawnpoint;
			revivepoint = src.revivepoint;

			bool bValid = (spawnpoint != WCoord(0, -1, 0)) || (revivepoint != WCoord(0, -1, 0));
			return bValid;
		}
	}
	return false;	
}

void RevivePointComponent::playClientReviveEffect(int mapid, WCoord revivepoint)
{
	World* pworld = GetOwnerActor()->getWorld();

	if (pworld && pworld->getEffectMgr() 
		&& (revivepoint.y > 0) 
		&& (mapid == pworld->getCurMapID()) 
		&& IsSpBlockRevive(pworld->getBlockID(revivepoint))
		&& (pworld->getBlockID(revivepoint) != BLOCK_PERSONALSPAWN))
	{
		pworld->getEffectMgr()->stopParticleEffect("particles/item_xfuhuo.ent", BlockCenterCoord(revivepoint), false);
		pworld->getEffectMgr()->playParticleEffectAsync("particles/item_xfuhuo.ent", BlockCenterCoord(revivepoint), -1, 0, 0, false);
		m_bFirstPlayReviveEffect = true;
	}
}

bool RevivePointComponent::getValidAccountWorldPoint(int mapid,  World* pworld, WCoord& spawnpoint, WCoord& revivepoint)
{
	if (getAccountWorldPoint(mapid, spawnpoint, revivepoint))
	{
		m_SpawnPoint = spawnpoint;
		m_RevivePoint = revivepoint;
		playClientReviveEffect(mapid, revivepoint);
		return true;
	}
	return false;	
}

void RevivePointComponent::OnBeginPlay()
{
	m_bFirstPlayReviveEffect = false;
	Super::OnBeginPlay();
}

void RevivePointComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	//add
	owner->Event().SubscribeEventWithCreateEvent("revive_getValidAccountWorldPoint", this, (SandboxClassCallback)&RevivePointComponent::OnGetValidAccountWorldPoint, "RevivePointComp_1");
	owner->Event().SubscribeEventWithCreateEvent("teleportHome", this, (SandboxClassCallback)&RevivePointComponent::OnTeleportHome, "RevivePointComp_2");
	owner->Event().SubscribeEventWithCreateEvent("replacePlayer", this, (SandboxClassCallback)&RevivePointComponent::OnReplacePlayer, "RevivePointComp_3");

	typedef ListenerFunctionRef<> ListenerCheckPlayReviveEffect;
	ListenerCheckPlayReviveEffect* listenerCheckPlayReviveEffect = SANDBOX_NEW(ListenerCheckPlayReviveEffect, [&]() -> void {
		this->checkPlayReviveEffect();
	});
	owner->Event2().Subscribe("revive_checkPlayReviveEffect", listenerCheckPlayReviveEffect);

	owner->Event().SubscribeEventWithCreateEvent("revive_setRevivePoint", this, (SandboxClassCallback)&RevivePointComponent::OnSetRevivePoint, "RevivePointComp_5");
	owner->Event().SubscribeEventWithCreateEvent("revive_getRevivePoint", this, (SandboxClassCallback)&RevivePointComponent::OnGetRevivePoint, "RevivePointComp_6");

	owner->Event().SubscribeEventWithCreateEvent("loadFromFile", this, (SandboxClassCallback)&RevivePointComponent::OnLoadFromFile, "RevivePointComp_7");
	owner->Event().SubscribeEventWithCreateEvent("saveToFile", this, (SandboxClassCallback)&RevivePointComponent::OnSaveToFile, "RevivePointComp_8");
	owner->Event().SubscribeEventWithCreateEvent("loadFileByToggleGameMakerMode", this, (SandboxClassCallback)&RevivePointComponent::OnLoadFromFileByToggleGameMakerMode, "RevivePointComp_9");

	owner->Event().SubscribeEventWithCreateEvent("interactBlock", this, (SandboxClassCallback)&RevivePointComponent::OnInteractBlock, "RevivePointComp_10");
	//old
	typedef ListenerFunctionRef<MNSandbox::SandboxResult &, MNSandbox::SandboxContext> ListenerRevive_GetRevivePointEx;
	ListenerRevive_GetRevivePointEx* listenerRevive_GetRevivePointEx = SANDBOX_NEW(ListenerRevive_GetRevivePointEx, [&](MNSandbox::SandboxResult &ret, MNSandbox::SandboxContext context) -> void {
		ret = this->OnGetRevivePointEx(context);
	});
	owner->Event2().Subscribe("revive_getRevivePointEx", listenerRevive_GetRevivePointEx);

	typedef ListenerFunctionRef<MNSandbox::SandboxResult &, MNSandbox::SandboxContext> ListenerRevive_GetSpawnPoint;
	ListenerRevive_GetSpawnPoint* listenerRevive_GetSpawnPoint = SANDBOX_NEW(ListenerRevive_GetSpawnPoint, [&](MNSandbox::SandboxResult &ret, MNSandbox::SandboxContext context) -> void {
		ret = this->OnGetSpawnPoint(context);
	});
	owner->Event2().Subscribe("revive_getSpawnPoint", listenerRevive_GetSpawnPoint);

	
	owner->Event().SubscribeEventWithCreateEvent("revive_setSpawnPoint", this, (SandboxClassCallback)&RevivePointComponent::OnSetSpawnPoint, "RevivePointComp_13");
	owner->Event().SubscribeEventWithCreateEvent("revive_getSpawnPointByPriority", this, (SandboxClassCallback)&RevivePointComponent::OnGetSpawnPointByPriority, "RevivePointComp_14");
	owner->Event().SubscribeEventWithCreateEvent("revive_setTriggerRevivePoint", this, (SandboxClassCallback)&RevivePointComponent::OnSetTriggerRevivePoint, "RevivePointComp_15");

	owner->Event().SubscribeEventWithCreateEvent("revive_setRevivePointEx", this, (SandboxClassCallback)&RevivePointComponent::OnSetRevivePointEx, "RevivePointComp_16");
	owner->Event().SubscribeEventWithCreateEvent("revive_triggerChangeSpawnPoint", this, (SandboxClassCallback)&RevivePointComponent::OnTriggerChangeSpawnPoint, "RevivePointComp_17");

	owner->Event().SubscribeEventWithCreateEvent("revive_isInteractSpBlockValid", this, (SandboxClassCallback)&RevivePointComponent::OnIsInteractSpBlockValid, "RevivePointComp_18");
	owner->Event().SubscribeEventWithCreateEvent("revive_resetRevivePoint", this, (SandboxClassCallback)&RevivePointComponent::OnResetRevivePoint, "RevivePointComp_19");
	owner->Event().SubscribeEventWithCreateEvent("revive_getLastInteractSpBlockList", this, (SandboxClassCallback)&RevivePointComponent::OnGetLastInteractSpBlockList, "RevivePointComp_20");
	owner->Event().SubscribeEventWithCreateEvent("revive_checkChangeSpawnPoint", this, (SandboxClassCallback)&RevivePointComponent::OnCheckChangeSpawnPoint, "RevivePointComp_21");

	owner->Event().SubscribeEventWithCreateEvent("revive_getAccountWorldPoint", this, (SandboxClassCallback)&RevivePointComponent::OnGetAccountWorldPoint, "RevivePointComp_22");
	owner->Event().SubscribeEventWithCreateEvent("revive_setAccountWorldPoint", this, (SandboxClassCallback)&RevivePointComponent::OnSetAccountWorldPoint, "RevivePointComp_23");
	owner->Event().SubscribeEventWithCreateEvent("revive_revivePoint2Client", this, (SandboxClassCallback)&RevivePointComponent::OnRevivePoint2Client, "RevivePointComp_24");
	//revive.
	owner->Event().SubscribeEventWithCreateEvent("revive_syncRevivePoint", this, (SandboxClassCallback)&RevivePointComponent::OnSyncRevivePoint, "RevivePointComp_25");

	//checkChangeSpawnPoint 
	//memset(m_AttratTargets,0,sizeof(m_AttratTargets));
	//memset(m_AttratFollowTimOuts,0,sizeof(m_AttratFollowTimOuts));
	//m_AttratTimeOut = 15;
	//m_AttratDist = 500;

	Super::OnEnterOwner(owner);
}

void RevivePointComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	char buf[32];
	memset(buf, 0, sizeof(buf));
	for (int i = 1; i <= 25; i++)
	{
		sprintf(buf, "RevivePointComp_%d", i);
		SchedulerMgr().DestroySchedulerByName(buf);
	}
	Super::OnLeaveOwner(owner);
}

/////////////////////////////////////////////////////////////////////////

SandboxResult SANDBOXAPI RevivePointComponent::OnGetValidAccountWorldPoint(SandboxContext context)
{
	WCoord spawnpoint  = WCoord(0, -1, 0);
	WCoord revivepoint = WCoord(0, -1, 0);

	bool ret = getValidAccountWorldPoint((int)context.GetData_Number("mapid")
		, context.GetData_RefEx<World>("pworld")
		, spawnpoint
		, revivepoint);

	SandboxResult result(this, ret);
	result.SetData_UserObject("spawn", spawnpoint);
	result.SetData_UserObject("revive", revivepoint);
	return result;
}

SandboxResult SANDBOXAPI RevivePointComponent::OnTeleportHome(SandboxContext context)
{
	WCoord  retPos;
	World*  retNewWorld = teleportHome((int)context.GetData_Number("curToolId"), retPos);

	SandboxResult ret(this, true);
	ret.SetData_RefEx("world", retNewWorld);
	ret.SetData_UserObject("pos", retPos);
	return ret;
}

SandboxResult SANDBOXAPI RevivePointComponent::OnReplacePlayer(SandboxContext context)
{
	bool ret = replacePlayer( context.GetData_Bool("bSetWorldMgr"),
								context.GetData_Bool("bGameMakerRunMode"),
								(int) context.GetData_Number("curToolId"));

	return SandboxResult(this, ret);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnCheckPlayReviveEffect(SandboxContext context)
{
	checkPlayReviveEffect();
	return SandboxResult(this, true);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnGetSpawnPoint(SandboxContext context)
{
	SandboxResult ret(this, true);
	ret.SetData_UserObject("point", getSpawnPoint());
	return ret;
}


SandboxResult SANDBOXAPI RevivePointComponent::OnGetSpawnPointByPriority(SandboxContext context)
{
	WCoord ret(0,-1,0);
	if (getTriggerRevivePoint().y >= 0)// 优先级：触发器修改的重生点 > 个人重生点 > 通电的复活重生点（定点） > 未通电的复活重生点
	{
		ret = getTriggerRevivePoint();
	}
	else if ( getSpawnPoint().y >= 0 ) 
	{
		ret = getSpawnPoint();
	}
	SandboxResult result(this, true);
	result.SetData_UserObject("point", ret);
	return result;
}

SandboxResult SANDBOXAPI RevivePointComponent::OnSetRevivePoint(SandboxContext context)
{
	m_RevivePoint = context.GetData_UserObject<WCoord>("point");
	return SandboxResult(this, true);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnGetRevivePoint(SandboxContext context)
{
	SandboxResult ret(this, true);
	ret.SetData_UserObject("point", m_RevivePoint);
	return ret;
}

SandboxResult SANDBOXAPI RevivePointComponent::OnGetRevivePointEx(SandboxContext context)
{
	SandboxResult ret(this, true);
	ret.SetData_UserObject("point", getRevivePoint());
	return ret;
}


SandboxResult SANDBOXAPI RevivePointComponent::OnLoadFromFileByToggleGameMakerMode(SandboxContext context)
{
	const FBSave::ActorPlayer* wrole = (const FBSave::ActorPlayer*)(context.GetData_Userdata("userdata", "wrole"));
	//const FBSave::ActorPlayer* wrole = context.GetData_Usertype<const FBSave::ActorPlayer>("wrole");
	if (wrole) {
		if (wrole->revivepoint())
		{
			WCoord tmpRevivePoint = Coord3ToWCoord(wrole->revivepoint());
			setRevivePoint(&tmpRevivePoint, true, true);
		}
		if (wrole->spawnpoint())
		{
			WCoord tmpSpawnPoint = Coord3ToWCoord(wrole->spawnpoint());
			setSpawnPoint(tmpSpawnPoint);
		}

		if (wrole->triggerrevivepoint())
		{
			m_TriggerRevivePoint = Coord3ToWCoord(wrole->triggerrevivepoint());
		}
	}
	return SandboxResult(this, true);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnLoadFromFile(SandboxContext context)
{
	const FBSave::ActorPlayer* wrole = (const FBSave::ActorPlayer*)( context.GetData_Userdata("userdata","wrole") );
	if (wrole) {
		m_RevivePoint = Coord3ToWCoord(wrole->revivepoint());
		if (wrole->spawnpoint())
		{
			m_SpawnPoint = Coord3ToWCoord(wrole->spawnpoint());
		}

		if (wrole->triggerrevivepoint())
		{
			m_TriggerRevivePoint = Coord3ToWCoord(wrole->triggerrevivepoint());
		}

		if (wrole->worldpoint())
		{
			m_AccountWorldPoint.resize(wrole->worldpoint()->size());
			for (size_t i = 0; i < wrole->worldpoint()->size(); i++)
			{
				auto src = wrole->worldpoint()->Get(i);
				AccountWorldPointInfo& dest = m_AccountWorldPoint[i];
				dest.mapid = src->mapid();
				dest.spawnpoint = Coord3ToWCoord(src->spawnpoint());
				dest.revivepoint = Coord3ToWCoord(src->revivepoint());
			}
		}
	}
	return SandboxResult(this, true);
}
SandboxResult SANDBOXAPI RevivePointComponent::OnSaveToFile(SandboxContext context)
{
	flatbuffers::FlatBufferBuilder* builder = (flatbuffers::FlatBufferBuilder*)(context.GetData_Userdata("userdata", "builder"));
	//flatbuffers::FlatBufferBuilder* builder        = context.GetData_Usertype<flatbuffers::FlatBufferBuilder>("builder");
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::AccountWorldPoint>>> worldpointoffset = 0;
	if (!m_AccountWorldPoint.empty())
	{
		std::vector<flatbuffers::Offset<FBSave::AccountWorldPoint>> worldpoint;
		for (size_t i = 0; i < m_AccountWorldPoint.size(); i++)
		{
			const AccountWorldPointInfo& src = m_AccountWorldPoint[i];
			FBSave::Coord3 spawncoord = WCoordToCoord3(src.spawnpoint);
			FBSave::Coord3 revivecoord = WCoordToCoord3(src.revivepoint);
			worldpoint.push_back(FBSave::CreateAccountWorldPoint(*builder, src.mapid, &spawncoord, &revivecoord));
		}
		worldpointoffset = builder->CreateVector(worldpoint);
	}

	SandboxResult result(this, true);
	result.SetParamGroupPtr(context.GetParamGroupPtr());
	result.SetData_UserObject("revive", WCoordToCoord3(m_RevivePoint));
	result.SetData_UserObject("spawn",  WCoordToCoord3(m_SpawnPoint));
	result.SetData_UserObject("trigger", WCoordToCoord3(m_TriggerRevivePoint));
	result.SetData_UserObject("worldpointoffset", worldpointoffset);
	return result;
}


SandboxResult SANDBOXAPI RevivePointComponent::OnInteractBlock(SandboxContext context)
{
	int blockid = (int) context.GetData_Number("blockid");
	//ÌØÊâµñÏñ(±£´æ¸´»îµã)
	if (IsSpBlockRevive(blockid))
	{
		m_LastInteractSpBlock.nBlockID      = blockid;
		m_LastInteractSpBlock.interactpoint = context.GetData_UserObject<WCoord>("blockpos");
	}

	return SandboxResult(this, true);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnInitNewWorldPoint(SandboxContext context)
{
	World* pworld     = context.GetData_RefEx<World>("world");
	bool bSetWorldMgr = context.GetData_Bool("bSetWorldMgr");

	WCoord spawnpoint  = WCoord(0, -1, 0);
	WCoord revivepoint = WCoord(0, -1, 0);

	bool ret = false;
	if (!getAccountWorldPoint(pworld->getCurMapID(), spawnpoint, revivepoint))
	{
		if (spawnpoint.y < 0)
		{
			spawnpoint = pworld->createSpawnPoint();
		}
		WCoord tmpHostRevivePoint = WCoord(0, -1, 0);
		setSpawnPoint(spawnpoint, bSetWorldMgr);
		setRevivePoint(&tmpHostRevivePoint, true, bSetWorldMgr);
		ret = true;
	}
	else
	{
		setSpawnPoint(spawnpoint, bSetWorldMgr);
		setRevivePoint(&revivepoint, true, bSetWorldMgr);
	}

	if (getRevivePoint() != WCoord(0, -1, 0))
	{
		WCoord tmpRevivePoint = getRevivePoint();
		AddInteractSpBlock(pworld->getCurMapID(), pworld->getBlockID(tmpRevivePoint), tmpRevivePoint);
	}
	else
	{
		ClearInteractSpBlock();
	}

	SandboxResult result(this, ret);
	result.SetData_UserObject("spawn", spawnpoint);
	result.SetData_UserObject("revive", revivepoint);
	return result;
}

SandboxResult SANDBOXAPI RevivePointComponent::OnSetTriggerRevivePoint(SandboxContext context)
{
	m_TriggerRevivePoint = context.GetData_UserObject<WCoord>("point");
	return SandboxResult(this, true);	
}

SandboxResult SANDBOXAPI RevivePointComponent::OnSetSpawnPoint(SandboxContext context)
{
	bool bSetWorldMgr = context.GetData_Bool("bSetWorldMgr", true);
	WCoord blockpos   = context.GetData_UserObject<WCoord>("point");
	setSpawnPoint(blockpos, bSetWorldMgr);//void setSpawnPoint(const WCoord &blockpos, bool bSetWorldMgr = true);
	return SandboxResult(this, true);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnSetRevivePointEx(SandboxContext context)
{
	WCoord pos        = context.GetData_UserObject<WCoord>("point");
	setRevivePoint(&pos, context.GetData_Bool("forced"), context.GetData_Bool("bSetWorldMgr", true));
	return SandboxResult(this, true);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnTriggerChangeSpawnPoint(SandboxContext context)
{

	int mapid       = (int) context.GetData_Number("mapid");
	WCoord blockpos = context.GetData_UserObject<WCoord>("blockpos");
	int blockid     = (int) context.GetData_Number("blockid");

	if(getSpawnPoint() != blockpos){// 右键个人复活重生点（个人点不参与切换）
		checkChangeSpawnPoint(blockpos, mapid);
		//插入交互方块列表
		AddInteractSpBlock(mapid, blockid, blockpos);
		setRevivePoint(&blockpos, true, true);	
		return SandboxResult(this, true);		
	}

	return SandboxResult(this, false);	
}

SandboxResult SANDBOXAPI RevivePointComponent::OnIsInteractSpBlockValid(SandboxContext context)
{
	return SandboxResult(this, IsInteractSpBlockValid());	
}
SandboxResult SANDBOXAPI RevivePointComponent::OnResetRevivePoint(SandboxContext context)
{
	ResetRevivePoint();
	return SandboxResult(this, true);	
}

SandboxResult SANDBOXAPI RevivePointComponent::OnGetLastInteractSpBlockList(SandboxContext context)
{
	ClientPlayer* owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!owner)	
		return SandboxResult(this, false);

	WCoord out;
	if(GetLastInteractSpBlockList(out, context.GetData_Number("mapid"))){
		SandboxResult ret(this, true);
		ret.SetData_UserObject("point", out);
		return ret;
	}
	return SandboxResult(this, false);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnCheckChangeSpawnPoint(SandboxContext context)
{
	checkChangeSpawnPoint(context.GetData_UserObject<WCoord>("point"),
		context.GetData_Number("mapid"));

	return SandboxResult(this, true);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnGetAccountWorldPoint(SandboxContext context)
{
	SandboxResult result(this, true);
	WCoord spawn = context.GetData_UserObject<WCoord>("spawn");
	WCoord revive = context.GetData_UserObject<WCoord>("revive");
	result.SetSuccess(getAccountWorldPoint((int)context.GetData_Number("mapid"), spawn, revive));
	result.SetData_UserObject("spawn", spawn);
	result.SetData_UserObject("revive", revive);
	return result;

}
SandboxResult SANDBOXAPI RevivePointComponent::OnSetAccountWorldPoint(SandboxContext context)
{
	setAccountWorldPoint((int)context.GetData_Number("mapid"),
		context.GetData_UserObject<WCoord>("spawn", WCoord(0, -1, 0)),
		context.GetData_UserObject<WCoord>("revive", WCoord(0, -1, 0)));
	return SandboxResult(this, true);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnRevivePoint2Client(SandboxContext context)
{
	WCoord revivepoint = context.GetData_UserObject<WCoord>("revive");
	int mapid = (int)context.GetData_Number("mapid");
	setAccountWorldPoint(mapid, context.GetData_UserObject<WCoord>("spawn"), revivepoint);
	playClientReviveEffect(mapid, revivepoint);
	return SandboxResult(this, true);
}

SandboxResult SANDBOXAPI RevivePointComponent::OnSyncRevivePoint(SandboxContext context)
{
	syncRevivePoint(context.GetData_Number("uin"));
	return SandboxResult(this, true);
}