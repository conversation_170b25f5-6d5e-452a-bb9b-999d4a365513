
#include "TrixenieLocomotion.h"
#include "ClientActor.h"
#include "world.h"

#include "WorldManager.h"
#include "LivingAttrib.h"
#include "ActorBody.h"
#include "ActorAttrib.h"
#include "PlayerControl.h"
//#include "DebugRenderer.h"
#include "Entity/OgreModel.h"
#include "ClientActorManager.h"
#include "ClientPlayer.h"
#include "DebugDataMgr.h"
#include "LuaInterfaceProxy.h"
#include "world.h"
#include "InputInfo.h"
#include "ActorHorse.h"
#include "SandboxCoreDriver.h"
#include "BlockEnv_Constants.h"
#include "AttackedComponent.h"
#include "ClientActorFuncWrapper.h"
#include "CameraManager.h"
#include "FireBurnComponent.h"
#include "RiddenComponent.h"
#include "ActionAttrStateComponent.h"
#include "SoundComponent.h"
#include "BlockEnvEffectsComponent.h"
#include "special_blockid.h"
#include "SandboxIdDef.h"
#include "navigationpath.h"
#include "OgreModel.h"
#include "MobAttrib.h"
#include "Entity/OgreEntity.h"
#ifdef TestScriptCamera
int m_nCameraId = 0;
#endif // TestScriptCamera


using namespace MINIW;
using namespace Rainbow;

extern int GetActorDepthInLiquid(ActorLocoMotion *locomove, bool onlywater);
extern float CalMoveDecay(World *pworld, const WCoord &pos, ClientPlayer *player, bool onground);

static float MOB_JUMPVEL = 50.0f;
static int JUMP_BEGINDIST = 60;
static float vel_limit_xz = 5.0f;
static float vel_limit_y = 15.0f;
const float DEFAULT_MOVEDCAY = 0.91f;

static float CalWalkOnLiquidForce(ActorLocoMotion *locomove, float my)
{
	int inwater_h = GetActorDepthInLiquid(locomove, false);
	float minh = BLOCK_FSIZE / 2.0f;
	if (inwater_h < minh)
	{
		return my + 1.0f * (inwater_h*2.0f / minh - 1.0f);
	}
	else
	{
		if (my < 0) my /= 2.0f;

		return my + 30.0f;
	}
}

IMPLEMENT_COMPONENTCLASS(TrixenieLocomotion)

TrixenieLocomotion::TrixenieLocomotion() : m_nMoveType(Land_Loc), m_bIsFlyBeStop(false)
{
	m_MoveTargetSpeed = -1.0f;
	m_MoveForward = 0;
	m_MoveStrafing = 0;
	m_Motion = Vector3f::zero;
	m_bBoundJump = false;
	m_bIsAIJumping = false;
	m_JumpTarget = WCoord(0, -1, 0);


	m_Velocity = Vector3f::zero;
	m_ColliderMotion = Vector3f::zero;
	m_StopDist = 20;
	m_HasTarget = false;
	m_FearPlayerId = -1;
	m_SpeedMultiple = 1;
	m_MaxSteeringForce = 2000;
	obstacleAvoidanceWeight = 1.0f;
	surfaceAvoidanceWeight = 1.0f;
	fleeWeight = 1.0f;
	wanderWeight = 1.0f;
	pursuitWeight = 1.0f;
	m_smoother = ENG_NEW(Smoother<Vector3f>)(8, Vector3f::zero);
	m_BehaviorFlag = 0;

	m_SpeedInAir = 1;
	m_SpeedInWater = 1;

	SetSmoothRotation(true);
}

TrixenieLocomotion::~TrixenieLocomotion()
{
	ENG_DELETE(m_smoother);
}

void TrixenieLocomotion::UpdateClimbTree()
{
	if (Land_Loc == m_nMoveType)
	{
		if (!getOwnerActor()->getNavigator()->noPath())
		{
			WCoord dir = m_MoveTarget - m_Position;
			m_Motion.y = dir.y > 0 ? 10.0f : -10.0f;
			m_Motion.x = dir.x > 0 ? 10.0f : -10.0f;
			m_Motion.z = dir.z > 0 ? 10.0f : -10.0f;
			if (Abs(dir.y) < 1.0f)
			{
				dir.y = 0;
				m_Motion.y = 0;
			}
			if (Abs(dir.y) < 10.0f)
			{
				m_Motion.y = dir.y;
			}
			if (Abs(dir.x) < 10.0f)
			{
				m_Motion.x = dir.x;
			}
			if (Abs(dir.z) < 10.0f)
			{
				m_Motion.z = dir.z;
			}
			if (dir.y != 0 || dir.x == 0) m_Motion.x = 0;
			if (dir.y != 0 || dir.z == 0) m_Motion.z = 0;
			CollideAABB box;
			getCollideBox(box);
			WCoord mvec = getIntegerMotion(m_Motion);
			//mvec = m_pActor->getWorld()->moveBoxWalk(box, mvec, 50);
			addRealMove(box, mvec);
			if (!getOwnerActor()->getBody()->hasAnimPlaying(SEQ_RUN))
				getOwnerActor()->playAnim(SEQ_RUN);
			WCoord pos;
			pos = getOwnerActor()->getPosition();
			int x = CoordDivBlock(pos.x);
			int y = CoordDivBlock(pos.y);
			int z = CoordDivBlock(pos.z);
			int blockID = getOwnerActor()->getWorld()->getBlockID(x, y, z);
			if (IsAirBlockID(blockID) && IsLeavesBlockID(getOwnerActor()->getWorld()->getBlockID(x, y + 1, z)))
				getOwnerActor()->setReverse(true);
			else
				getOwnerActor()->setReverse(false);

			for (int i = DIR_NEG_X; i < DIR_NEG_Y; i++)
			{
				WCoord dir = NeighborCoord(CoordDivBlock(pos), i);
				if (IsWoodBlockID(getOwnerActor()->getWorld()->getBlockID(dir)))
				{
					WCoord totarget = BlockCenterCoord(dir) - getOwnerActor()->getPosition();
					float yaw, pitch;
					Direction2PitchYaw(&yaw, &pitch, (totarget).toVector3());
					getOwnerActor()->SyncPosition(pos, yaw, pitch);
				}
			}
		}
	}
}

void TrixenieLocomotion::tick()
{
	if (Land_Loc == m_nMoveType)
	{
		ActorLocoMotion::tick();

		if (m_pWorld->isRemoteMode())
		{
			if (m_SyncSteps > 0)
			{
				m_Position += (m_SyncPos - m_Position) / m_SyncSteps;
				m_RotateYaw += WrapAngleTo180(m_SyncYaw - m_RotateYaw) / m_SyncSteps;
				m_RotationPitch += WrapAngleTo180(m_SyncPitch - m_RotationPitch) / m_SyncSteps;

				m_SyncSteps--;
			}

			auto pEntity = getOwnerActor()->getBody()->getEntity();
			if (pEntity && pEntity->GetMainModel())
			{
				pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
			}
			return;
		}
		m_Motion *= 0.98f;
		if (Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
		//if(Abs(m_Motion.y) < 0.5f) m_Motion.y = 0;
		if (Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;

		if (isMovementBlocked())
		{
			clearTarget();
			m_MoveStrafing = 0;
			m_MoveForward = 0;
			m_isJumping = false;
		}
		else
		{
			if (m_MoveTargetSpeed >= 0) updateMoveTarget();
		}

		if (!getOwnerActor()->getClimbing())
		{
			updateJumping();
			m_MoveStrafing *= 0.98f;
			m_MoveForward *= 0.98f;
			moveEntityWithHeading(m_MoveStrafing, m_MoveForward);
			auto functionWrapper = getOwnerActor()->getFuncWrapper();
			bool isCanFly = functionWrapper ? functionWrapper->getCanFly() : false;
			if (isCanFly)
			{
				if (m_Motion.y < 0) m_Motion.y *= 0.6f;
			}
		}
		else
		{
			UpdateClimbTree();
		}

		collideWithNearbyActors();
	}
	else if (Air_Loc == m_nMoveType)
	{
		if (m_pWorld->isRemoteMode())
		{
			if (m_SyncSteps > 0)
			{
				m_Position += (m_SyncPos - m_Position) / m_SyncSteps;
				m_RotateYaw += WrapAngleTo180(m_SyncYaw - m_RotateYaw) / m_SyncSteps;
				m_RotationPitch += WrapAngleTo180(m_SyncPitch - m_RotationPitch) / m_SyncSteps;

				m_SyncSteps--;
			}
			else
			{
				m_Position = m_SyncPos;
			}

			auto pEntity = getOwnerActor()->getBody()->getEntity();
			if (pEntity && pEntity->GetMainModel())
			{
				pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
			}
			return;
		}

		if (getOwnerActor()->needClear()) return;

		updateRidden();
		updateBindActor();

		handleWaterMovement();

		//when actor in lava
		if (handleLavaMovement())
		{
			ActorAttrib *attrib = getOwnerActor()->getAttrib();
			if (attrib)
			{
				if (attrib->immuneToFire() <= 0)
				{
					auto FireBurnComp = getOwnerActor()->sureFireBurnComponent();
					if (FireBurnComp)
					{
						FireBurnComp->setFire(100, 1);
					}

				}
				if (attrib->immuneToFire() <= 1)
				{
					auto component = getOwnerActor()->getAttackedComponent();
					if (component)
					{
						component->attackedFromType_Base(ATTACK_FIRE, 4.0f * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv); // modify by null, �ҽ��˺�����
					}
				}
			}
		}

		auto functionWrapper = getOwnerActor()->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(0);
		}
		//when actor in lava
		if (handleLavaMovement())
		{
			ActorAttrib *attrib = getOwnerActor()->getAttrib();
			if (attrib)
			{
				if (attrib->immuneToFire() <= 0)
				{
					auto FireBurnComp = getOwnerActor()->sureFireBurnComponent();
					if (FireBurnComp)
					{
						FireBurnComp->setFire(100, 1);
					}

				}
				if (attrib->immuneToFire() <= 1)
				{
					auto component = getOwnerActor()->getAttackedComponent();
					if (component)
					{
						component->attackedFromType_Base(ATTACK_FIRE, 4.0f * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv); // modify by null, �ҽ��˺�����
					}
				}
			}
			if (functionWrapper)
			{
				functionWrapper->setFallDistance(functionWrapper->getFallDistance() * 0.5f);
			}
			//getOwnerActor()->m_FallDistance *= 0.5f;
		}

		if (!m_pWorld->isRemoteMode() && m_Position.y < -64 * BLOCK_SIZE && !getOwnerActor()->isDead())
		{
			getOwnerActor()->kill();
		}

		//-------------ActorLocoMotion tick end------------------


		//-------------LivingLocoMotion tick start------------------
		m_Motion *= 0.98f;
		if (Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
		if (Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;

		if (isMovementBlocked())
		{
			//clearTarget();
			m_isJumping = false;
		}
		else
		{
			if (m_MoveTargetSpeed >= 0) updateMoveTarget();
		}
		moveEntityWithDirection();

		collideWithNearbyActors();
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		if (m_pWorld->isRemoteMode())
		{
			if (m_SyncSteps > 0)
			{
				m_Position += (m_SyncPos - m_Position) / m_SyncSteps;
				m_RotateYaw += WrapAngleTo180(m_SyncYaw - m_RotateYaw) / m_SyncSteps;
				m_RotationPitch += WrapAngleTo180(m_SyncPitch - m_RotationPitch) / m_SyncSteps;

				m_SyncSteps--;
			}
			else
			{
				m_Position = m_SyncPos;
			}

			auto pEntity = getOwnerActor()->getBody()->getEntity();
			if (pEntity && pEntity->GetMainModel())
			{
				pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
			}
			return;
		}

		if (getOwnerActor()->needClear()) return;

		updateRidden();
		updateBindActor();

		//Check actor in water
		WCoord contract = WCoord(1, 40, 1);
		if (contract.y > m_BoundHeight / 2 - 1) contract.y = m_BoundHeight / 2 - 1;

		WCoord minpos = m_Position - WCoord(m_BoundSize / 2, 0, m_BoundSize / 2) + contract;
		WCoord maxpos = m_Position + WCoord(m_BoundSize / 2, m_BoundHeight, m_BoundSize / 2) - contract;

		Vector3f flowmotion;
		if (m_pWorld->getFluidFlowMotion(minpos, maxpos, flowmotion))
		{
			m_Motion += flowmotion;
			m_InWater = true;

			auto functionWrapper = getOwnerActor()->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setFallDistance(0);
			}

			auto FireBurnComp = getOwnerActor()->getFireBurnComponent();
			if (FireBurnComp)
			{
				FireBurnComp->setFire(0, 0);
			}

		}
		else
		{
			m_InWater = false;
		}

		//when actor in lava
		if (handleLavaMovement())
		{
			ActorAttrib *attrib = getOwnerActor()->getAttrib();
			if (attrib)
			{
				if (attrib->immuneToFire() <= 0)
				{
					auto FireBurnComp = getOwnerActor()->sureFireBurnComponent();
					if (FireBurnComp)
					{
						FireBurnComp->setFire(100, 1);
					}

				}
				if (attrib->immuneToFire() <= 1)
				{
					auto component = getOwnerActor()->getAttackedComponent();
					if (component)
					{
						component->attackedFromType_Base(ATTACK_FIRE, 4.0f * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv); // modify by null, �ҽ��˺�����
					}
				}
			}
			auto functionWrapper = getOwnerActor()->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setFallDistance(functionWrapper->getFallDistance() * 0.5f);
			}

			//getOwnerActor()->m_FallDistance *= 0.5f;
		}

		if (!m_pWorld->isRemoteMode() && m_Position.y < -64 * BLOCK_SIZE && !getOwnerActor()->isDead())
		{
			getOwnerActor()->kill();
		}

		//-------------ActorLocoMotion tick end------------------


		//-------------LivingLocoMotion tick start------------------
		m_Motion *= 0.98f;
		if (Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
		//if(Abs(m_Motion.y) < 0.5f) m_Motion.y = 0;
		if (Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;

		if (isMovementBlocked())
		{
			//clearTarget();
			m_isJumping = false;
		}
		else
		{
			if (m_MoveTargetSpeed >= 0) updateMoveTarget();
		}
		moveEntityWithDirection();

		collideWithNearbyActors();
		//-------------LivingLocoMotion tick end------------------
	}

#ifdef TestScriptCamera
	char name[128];
	sprintf(name, "testcamera%d", m_nCameraId);
	WorldPos pos = getPosition().toWorldPos() + WorldPos(0, 1000, 0);

	float yaw = m_RotateYaw + 180;
	if (yaw > 360.0f) yaw -= 360.0f;
	if (yaw < 0) yaw += 360.0f;

	float pitch = m_RotationPitch;
	float anglex = 88.0f;
	if (pitch < -anglex) pitch = -anglex;
	if (pitch > anglex) pitch = anglex;

	Quaternion quat;
	//quat.setEulerAngle(yaw, pitch, 0);
	quat = AngleEulerToQuaternionf(Vector3f(pitch, yaw, 0));
	CameraManager::GetInstance().setSpCameraPosRot(name, pos, quat);
#endif // TestScriptCamera
}

void TrixenieLocomotion::collideWithNearbyActors()
{
	if (Land_Loc == m_nMoveType)
	{
		CollideAABB box;
		getOwnerActor()->getCollideBox(box);
		ClientPlayer * p = getOwnerActor()->ToCast<ClientPlayer>();
		if (p && p->getOPWay() == PLAYEROP_WAY_BASKETBALLER && p->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
		{
			box.expand(BLOCK_SIZE, 0, BLOCK_SIZE);
		}
		else
		{
			box.expand(20, 0, 20);
		}

		std::vector<IClientActor *>actors;
		m_pWorld->getActorsInBoxExclude(actors, box, getOwnerActor());

		for (size_t i = 0; i < actors.size(); i++)
		{
			if (actors[i]->GetActor()->canBePushed())
			{
				getOwnerActor()->collideWithActor(actors[i]->GetActor());
			}
		}
	}
	else if (Air_Loc == m_nMoveType)
	{
		CollideAABB box;
		getOwnerActor()->getCollideBox(box);
		box.expand(20, 0, 20);

		std::vector<IClientActor *>actors;
		m_pWorld->getActorsInBoxExclude(actors, box, getOwnerActor());

		for (size_t i = 0; i < actors.size(); i++)
		{
			if (actors[i]->GetActor()->canBePushed())
			{
				getOwnerActor()->collideWithActor(actors[i]->GetActor());
			}
		}

		if (actors.size() == 0)
		{
			m_ColliderMotion = Vector3f::zero;
		}
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		CollideAABB box;
		getOwnerActor()->getCollideBox(box);
		box.expand(20, 0, 20);

		std::vector<IClientActor *>actors;
		m_pWorld->getActorsInBoxExclude(actors, box, getOwnerActor());

		for (size_t i = 0; i < actors.size(); i++)
		{
			if (actors[i]->GetActor()->canBePushed())
			{
				getOwnerActor()->collideWithActor(actors[i]->GetActor());
			}
		}

		if (actors.size() == 0)
		{
			m_ColliderMotion = Vector3f::zero;
		}
	}
}

void TrixenieLocomotion::gotoPosition(const WCoord &pos, float yaw, float pitch)
{
	if (Land_Loc == m_nMoveType)
	{
//		LOG_WARNING("!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!Land_Loc yaw = %f, pitch = %f", yaw, pitch);
		ActorLocoMotion::gotoPosition(pos, yaw, pitch);
		m_MoveForward = m_MoveStrafing = 0;
		m_isJumping = false;
		clearTarget();
	}
	else if (Air_Loc == m_nMoveType)
	{
//		LOG_WARNING("!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!Air_Loc yaw = %f, pitch = %f", yaw, pitch);
		ActorLocoMotion::gotoPosition(pos, yaw, pitch);
	}
}

bool TrixenieLocomotion::prepareJump(int &cooldown)
{
	if (Land_Loc == m_nMoveType)
	{
		static_cast<ActorLiving *>(getOwnerActor())->playAnim(SEQ_JUMP);
		cooldown = 10;
	}
	return m_OnGround;
}

void TrixenieLocomotion::setMoveForward(float vel)
{
	if (Land_Loc == m_nMoveType)
	{
		m_MoveForward = vel;
	}
}

void TrixenieLocomotion::doJumpTarget()
{
	if (Land_Loc == m_nMoveType)
	{
		if (!m_pWorld)
			return;

		WCoord &from = getPosition();
		WCoord &to = m_JumpTarget;

		WCoord totarget = to - from;
		// ת��
		float yaw, pitch;
		Direction2PitchYaw(&yaw, &pitch, (totarget).toVector3());
		getOwnerActor()->SyncPosition(m_MoveTarget, (int)yaw, (int)pitch, 10.0f);
		m_NavigationRotateYaw = yaw;
		m_NavigationRotationPitch = pitch;

		Vector3f vec = totarget.toVector3();
		setMoveDir(vec);

		float maxPosY = to.y + BLOCK_SIZE;
		if (maxPosY <= from.y)
			maxPosY = from.y + BLOCK_SIZE;

		float g = m_pWorld->getGravity();
		float velocityY = Rainbow::Sqrt(2 * g * (maxPosY - from.y));

		float t1 = velocityY / g;
		float t2 = Rainbow::Sqrt(2 * (maxPosY - to.y) / g);

		Vector3f dir = Vector3f(to.x - from.x, 0, to.z - from.z);
		float offset = -40.0f;
		float dist = dir.Length();
		if (dist > 150)
			offset = 30.0f;

		float movedecay = CalMoveDecay(m_pWorld, m_Position, NULL, true);
		float v = 2 * (dist + offset) / (t1 + t2) + Pow(movedecay, (t1 + t2));
		//float v = 2 * (dist + offset) / (t1 + t2);
		/*float movedecay = CalMoveDecay(m_pWorld, m_Position, NULL, true);
		v /= movedecay;*/

		Rainbow::Normalize(dir);

		m_Motion.x = dir.x * v;
		m_Motion.z = dir.z * v;
		m_Motion.y = velocityY;
		//m_Motion /= 0.98;

		m_JumpTarget = WCoord(0, -1, 0);
		setJumping(false);
	}
}

void TrixenieLocomotion::doJump()
{
	if (Land_Loc == m_nMoveType)
	{
		if (m_JumpTarget.y > 0)
		{
			doJumpTarget();
		}
		else
		{
			float base = MOB_JUMPVEL;
			if (getOwnerActor() && getOwnerActor()->getAttrib()) {
				base = getOwnerActor()->getAttrib()->getSpeedAtt(Actor_Jump_Speed);
				if (base < 0.0f) { base = MOB_JUMPVEL; }

				auto pLivingAtt = dynamic_cast<LivingAttrib*>(getOwnerActor()->getAttrib());
				if (pLivingAtt && pLivingAtt->isNewStatus()) {
					base += pLivingAtt->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_JUMP_SPEED, base);
				}
			}

			if (getOwnerActor()->getReverse())
				m_Motion.y = -base;
			else
				m_Motion.y = base;



		}
	}
}

float TrixenieLocomotion::getGravityFactor(bool up)
{
	if (Land_Loc == m_nMoveType)
	{
		return 1.0f;
	}
	else if (Air_Loc == m_nMoveType)
	{
		return 1.0f;
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		return 1.0f;
	}
	return 1.0f;
}

void TrixenieLocomotion::updateJetpackMove()
{
	if (Land_Loc == m_nMoveType)
	{
		auto RidComp = getOwnerActor()->getRiddenComponent();
		if (m_isJumping && !(RidComp && RidComp->isRiding()))
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(getOwnerActor());
			if (player)
			{
				m_Motion.y += 13;
			}
		}
	}
}


void TrixenieLocomotion::updateJumping()
{
	if (Land_Loc == m_nMoveType)
	{
		if (m_JumpingTicks > 0) m_JumpingTicks--;

		auto RidComp = getOwnerActor()->getRiddenComponent();
		if ((m_isJumping || m_bIsAIJumping) && !(RidComp && RidComp->isRiding()))
		{
			if (m_InWater || m_InLava || m_InHoney || isOnLadder()) {
				if (getOwnerActor()->getReverse())
					m_Motion.y -= 4.0f;
				else
					m_Motion.y += 4.0f;
			}
			else {
				int cooldown;
				if (m_JumpingTicks == 0 && prepareJump(cooldown))
				{
					doJump();
					m_JumpingTicks = cooldown;
				}
			}
		}
		else
		{
			m_JumpingTicks = 0;
		}
	}
}

void TrixenieLocomotion::autoStep()
{
	if (Land_Loc == m_nMoveType)
	{
		PlayerControl *player = dynamic_cast<PlayerControl *>(getOwnerActor());

		if (player && player->m_InputInfo && player->m_InputInfo->jump)
		{
			m_Motion.y = 40.0f * (1.0f + player->getGeniusValue(GENIUS_TWOJUMP));
		}
		else
		{
			m_Motion.y = 40.0f * (1.0f);
		}


	}
}

bool TrixenieLocomotion::updateMoveTarget()
{
	if (Land_Loc == m_nMoveType)
	{
		setMoveForward(0);
		setJumping(false);

		WCoord curpos = m_Position;
		auto RidComp = getOwnerActor()->getRiddenComponent();
		if (RidComp && RidComp->isRiding())
		{
			ClientActor *riding = RidComp->getRidingActor();
			if (riding)
			{
				curpos = riding->getPosition();
			}
		}

		curpos.y = CoordDivBlock(curpos.y + BLOCK_SIZE / 2)*BLOCK_SIZE;

		WCoord totarget = m_MoveTarget - curpos;
		if (totarget.isZero())
		{
			clearTarget();
			return true; //����Ŀ��
		}

		// ת��
		float yaw, pitch;
		Direction2PitchYaw(&yaw, &pitch, (totarget).toVector3());
// 		getOwnerActor()->syncPosition(m_MoveTarget, (int)yaw, (int)pitch, 10.0f);

		m_RotateYaw = yaw;
		m_RotationPitch = pitch;

		getOwnerActor()->setHeadLookAt(yaw, pitch, 10, 30);

		m_NavigationRotateYaw = yaw;
		m_NavigationRotationPitch = pitch;
		auto pEntity = getOwnerActor()->getBody()->getEntity();
		if (pEntity && pEntity->GetMainModel())
		{
			pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
		}
// 		LOG_WARNING("!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!updataMoveTarget yaw = %f, pitch = %f", m_NavigationRotateYaw, m_NavigationRotationPitch);

		/*PlayerControl* pPlayerCtrl = dynamic_cast<PlayerControl*>(getOwnerActor());
		if (pPlayerCtrl)
		{
		pPlayerCtrl->applyCurCameraConfig();
		}*/

		ClientPlayer *player = dynamic_cast<ClientPlayer *>(getOwnerActor());
		bool playerflying = (player && getOwnerActor()->getFlying());
		bool bInWater = ((m_InWater || m_InLava || m_InHoney) && !playerflying);//��ˮ�л����ҽ��У��Ƿ���ģʽ

		Vector3f vec = totarget.toVector3();
		setMoveDir(vec);
		float speed = getOwnerActor()->getAttrib()->getMoveSpeed(bInWater ? Actor_Swim_Speed : Actor_Walk_Speed) * m_MoveTargetSpeed;
		auto functionWrapper = getOwnerActor()->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setAIMoveSpeed(speed);
		}
		setMoveForward(speed);
		/*if (getOwnerActor()->getObjType() == OBJ_TYPE_VILLAGER)
		{
		ActorVillager *village = dynamic_cast<ActorVillager *>(getOwnerActor());
		if (village)
		{
		int val = 0;
		if (bInWater)
		village->addHuggerByType("swim", speed);
		else
		village->addHuggerByType("walk", speed);
		}
		}*/
		if (getOwnerActor()->getMoveMode() == ACTORMOVE_JUMP)
		{
			setJumping(true);
		}
		else
		{
			int r = JUMP_BEGINDIST + m_BoundSize / 2;
			//int r = BLOCK_SIZE;
			if (((!getOwnerActor()->getReverse() && totarget.y>0) || (getOwnerActor()->getReverse() && totarget.y<0))
				&& totarget.x*totarget.x + totarget.z*totarget.z<r*r)
			{
				setJumping(true);
				m_JumpTarget = WCoord(0, -1, 0);
			}
		}

		clearTarget();
	}
	else if (Air_Loc == m_nMoveType)
	{
		return true;
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		return true;
	}
	return false;
}


void TrixenieLocomotion::moveEntityWithHeading(float strafing, float forward)
{
	if (Land_Loc == m_nMoveType)
	{
		ClientActor* pActor = getOwnerActor();

		auto pBlockEnvEffectsComponent = pActor->getBlockEnvEffectsComponent();
		auto RidComp = pActor->getRiddenComponent();
		if (RidComp && RidComp->isRiding())
		{
			return;
		}

		auto functionWrapper = pActor->getFuncWrapper();
		auto ActionAttrStateComp = pActor->getActionAttrStateComponent();
		if (!(functionWrapper && functionWrapper->getCanMove()) || !(ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_MOVE)))
		{
			return;
		}

		bool bNeedFloat = false;
		bool bCanDiving = false;
		bool bDoingRush = false;
		Vector3f speedOffset(0.0f, 0.0f, 0.0f);
		if (m_InWater) {
			bNeedFloat = checkSeatInWaterSkill(1);
			bCanDiving = checkSeatInWaterSkill(2);
			bDoingRush = isDoingRush();
			bool bCanRush = checkSeatInWaterSkill(4);
			if (bNeedFloat) {
				ActorLiving *riddenby = NULL; 
				if (RidComp)
				{
					riddenby = dynamic_cast<ActorLiving *>(RidComp->getRiddenByActor());
				}
				if (!riddenby) {
					int inwater_h = GetActorDepthInLiquid(this, true);
					if (inwater_h < BLOCK_SIZE) {
						if (m_Motion.y < -4.0f) { m_Motion.y = -4.0f; }
						m_Motion.y += 6.0f * (inwater_h*2.0f / BLOCK_FSIZE - 1.0f) + 1.0f;
					}
					else {
						if (m_Motion.y < 0) m_Motion.y /= 2.0f;
						m_Motion.y += 4.0f;
					}

					doMoveStep(m_Motion);
					m_Motion.x *= 0.8f;
					m_Motion.z *= 0.8f;
					return;
				}
			}
			else if ((bCanDiving || bCanRush) && !bDoingRush) {
				ActorLiving *riddenby = NULL;
				if (RidComp)
				{
					riddenby = dynamic_cast<ActorLiving *>(RidComp->getRiddenByActor());
				}
				if (riddenby) {
					TrixenieLocomotion *locomove = static_cast<TrixenieLocomotion *>(riddenby->getLocoMotion());
					if (locomove && checkCanUpAndDown(locomove->m_RotationPitch) && (locomove->m_MoveForward > 0.000001f ||
						locomove->m_MoveForward < -0.000001f || locomove->m_MoveStrafing > 0.000001f || locomove->m_MoveStrafing < -0.000001f))
					{
						Vector3f dir;
						PitchYaw2Direction(dir, 0, locomove->m_RotationPitch);
						dir.x = 0.0f;
						dir.z = 0.0f;
						m_Motion += 3 * dir;
					}
				}
			}

			if (bNeedFloat || bCanDiving || bDoingRush) {
				ActorLiving *riddenby = NULL;
				if (RidComp)
				{
					riddenby = dynamic_cast<ActorLiving *>(RidComp->getRiddenByActor());
				}
				if (riddenby) {
					float fSpeedFact = (functionWrapper ? functionWrapper->getAIMoveSpeed() : 200 )* (bDoingRush ? 0.4f : 0.07f);
					speedOffset.x = m_Motion.x*fSpeedFact;
					speedOffset.y = bDoingRush ? m_Motion.y*fSpeedFact : 0;
					speedOffset.z = m_Motion.z*fSpeedFact;
				}
			}
		}

		ClientPlayer *player = dynamic_cast<ClientPlayer *>(pActor);
		bool playerflying = pActor->getFlying() && player != NULL;

		//��ˮ�л����ҽ��У��Ƿ���ģʽ
		if ((m_InWater || m_InLava || m_InHoney) && !playerflying)
		{
			bool walkonliquid = (m_InWater&&canWalkOnLiquid(true)) || (m_InLava&&canWalkOnLiquid(false));
			float speed = (functionWrapper ? functionWrapper->getAIMoveSpeed() : 200);
			if (!walkonliquid)
			{
				if (!m_InHoney)
					speed *= GetLuaInterfaceProxy().get_lua_const()->swimming_yidong_beilv;// 2.5f;
				else
					speed *= GetLuaInterfaceProxy().get_lua_const()->swimming_honey_yidong_beilv;
			}
			int oldy = m_Position.y;
			moveFlying(strafing, forward, speed);
			doMoveStep((bNeedFloat || bCanDiving || bDoingRush) ? m_Motion + speedOffset : m_Motion);

			m_Motion *= (bDoingRush ? 0.9f : 0.8f);
			if (walkonliquid || bNeedFloat)
			{
				m_Motion.y = CalWalkOnLiquidForce(this, m_Motion.y);
				if (bNeedFloat) {
					m_Motion.y -= 0.25f;
				}
			}
			else if (!bCanDiving && !bDoingRush) {
				m_Motion.y -= 2.0f;
			}

			if (m_CollidedHorizontally && isOffsetPositionInLiquid(m_Motion.x, m_Motion.y + 60.0f - m_Position.y + oldy, m_Motion.z))
			{
				m_Motion.y = 30.0f;
			}

			if (findBottomBlock(BLOCK_HOTCRYSTAL))
			{
				m_Motion.y = 40;
				ActorLiving *living = dynamic_cast<ActorLiving *>(getOwnerActor());
				if (living != nullptr && living->getLivingAttrib())
				{
					living->getLivingAttrib()->addBuff(BOUND_BUFF, 1);
				}
			}

		}
		else if (playerflying)
		{
			float speed = 1.3f * (functionWrapper ? functionWrapper->getAIMoveSpeed() : 200);
			moveFlying(strafing, forward, speed);
			float movedecay = 0.71f;
			doMoveStep(m_Motion);

			m_Motion.y *= 0.98f;
			m_Motion.x *= movedecay;
			m_Motion.z *= movedecay;
		}
		else
		{
			float movedecay = CalMoveDecay(m_pWorld, m_Position, player, m_OnGround);
			if(movedecay == 0) return;
			float slipper_slow = 0.16277136f / (movedecay * movedecay * movedecay);
			float speed;

			if (movedecay >= 0)
			{
				speed = (functionWrapper ? functionWrapper->getAIMoveSpeed() : 200) * slipper_slow;
			}
			else
			{
				speed = m_JumpMovementFactor;
			}

			if (player && forward<0) speed *= GetLuaInterfaceProxy().get_lua_const()->houtui_yidong_beilv;

			if (pBlockEnvEffectsComponent && pBlockEnvEffectsComponent->GetStatus(ENV_THICKET))//��������50%
				speed *= 0.5f;

			moveFlying(strafing, forward, speed);

			if (isOnLadder())
			{
				m_Motion.x = Rainbow::Clamp(m_Motion.x, -vel_limit_xz, vel_limit_xz);
				m_Motion.z = Rainbow::Clamp(m_Motion.z, -vel_limit_xz, vel_limit_xz);

				auto functionWrapper = getOwnerActor()->getFuncWrapper();
				if (functionWrapper)
				{
					functionWrapper->setFallDistance(functionWrapper->getFallDistance() * 0.5f);
				}
				if (m_Motion.y < -vel_limit_y) m_Motion.y = -vel_limit_y;

				if (getOwnerActor()->getSneaking() && m_Motion.y<0)
				{
					m_Motion.y = 0;
				}
			}

			doMoveStep(m_Motion);

			movedecay = CalMoveDecay(m_pWorld, m_Position, player, m_OnGround);
			if (movedecay < 0) movedecay = DEFAULT_MOVEDCAY;

			bool isclimbwall = false;
			ActorLiving *living = dynamic_cast<ActorLiving *>(getOwnerActor());
			if (living && living->getLivingAttrib())
				isclimbwall = living->getLivingAttrib()->hasStatusEffect(STATUS_EFFECT_CLIMBWALL);
			//m_CollidedHorizontally
			if (((m_CollidedHorizontally || m_isJumping) && isOnLadder()) || ((m_CollidedHorizontally || m_isJumping) && isclimbwall))
			{
				m_Motion.y = 20.0f;
			}

			m_Motion.y = calGravityMotionY(m_Motion.y);

			m_Motion.y *= 0.98f;
			m_Motion.x *= movedecay;
			m_Motion.z *= movedecay;
			/*if (player || !m_isJumping)
			{
			m_Motion.x *= movedecay;
			m_Motion.z *= movedecay;
			}*/

			//���İ��;�
			ClientMob *pMob = dynamic_cast<ClientMob *>(getOwnerActor());
			if (pMob && pMob->getLivingAttrib())
			{
				MobAttrib *attr = static_cast<MobAttrib *>(pMob->getLivingAttrib());
				assert(attr != NULL);
				BackPackGrid *pPackGrid = attr->getEquipGridWithType(EQUIP_LEGGING);

				if (pPackGrid && pPackGrid->def && (pMob->m_Def && ActorHorse::mobCanRake(pMob->m_Def->ID)))
				{
					WCoord pos = getOwnerActor()->getPosition();
					Vector3f vecPos(pos.x, pos.y, pos.z);
					Vector3f dir =Vector3f(0.0f,0.0f,0.0f);
					PitchYaw2Direction(dir, m_RotateYaw + 180.0f, 0);
					vecPos += dir * 100.0f;

					int x = CoordDivBlock(vecPos.x);
					int y = CoordDivBlock(vecPos.y);
					int z = CoordDivBlock(vecPos.z);

					int blockID = getOwnerActor()->getWorld()->getBlockID(x, y - 1, z);

					//���ұ�ɸ���
					if (blockID == BLOCK_GRASS/* || blockID == BLOCK_DIRT*/)
					{
						WCoord blockpos(x, y - 1, z);
						m_pWorld->setBlockAll(blockpos, BLOCK_FARMLAND, 0);

						//��������;�
						int ndamageDuration = GenRandomInt(3, 5);
						attr->damageEquipItemWithType(EQUIP_LEGGING, ndamageDuration);

						ActorHorse *pHorse = dynamic_cast<ActorHorse *>(pMob);
						if (pHorse)
						{
							//�����;�
							BackPackGrid * PackGrid = pHorse->index2Grid(HORSE_EQUIP_INDEX + 2);
							assert(PackGrid);
							if (PackGrid)
							{
								PackGrid->addDuration(-ndamageDuration);
							}
						}

						//�����Ч
						if (getOwnerActor())
						{
							auto sound = getOwnerActor()->getSoundComponent();
							if (sound)
							{
								sound->playSound("ent.3401.plough", 1.0f, 1.0f);
							}
						}
					}
				}
			}
		}

		CheckMotionValid(m_Motion);
	}
}

float TrixenieLocomotion::calGravityMotionY(float my)
{
	if (Land_Loc == m_nMoveType)
	{
		float gravity = m_pWorld->getGravity(GRAVITY_LIVING);
		if (getOwnerActor()->getReverse())
		{
			if (my >= 0) my += gravity*getGravityFactor(false);
			else
			{
				my += gravity*getGravityFactor(true);
				if (my > 0 && getGravityFactor(false) < 1.0f) my = 0;
			}
		}
		else
		{
			if (my <= 0) my -= gravity*getGravityFactor(false);
			else
			{
				my -= gravity*getGravityFactor(true);
				if (my < 0 && getGravityFactor(false) < 1.0f) my = 0;
			}
		}

		if (onTheBlock(BLOCK_HOTCRYSTAL))
		{
			my = 40;
			ActorLiving *living = dynamic_cast<ActorLiving *>(getOwnerActor());
			if (living != nullptr && living->getLivingAttrib())
			{
				living->getLivingAttrib()->addBuff(BOUND_BUFF, 1);
			}
		}
		else
		{
			ActorLiving *living = dynamic_cast<ActorLiving *>(getOwnerActor());
			if (living != nullptr && living->getLivingAttrib()->hasBuff(BOUND_BUFF))
			{
				my += 8.0f;
			}
		}

		return my;
	}

	return 0;
}

void TrixenieLocomotion::setTarget(const WCoord &target, float speed)
{
	if (Land_Loc == m_nMoveType)
	{
		m_MoveTarget = target;
		m_MoveTargetSpeed = speed;
	}
}

void TrixenieLocomotion::clearTarget()
{
	if (Land_Loc == m_nMoveType)
	{
		m_MoveTargetSpeed = -1.0f;
		m_NavigationRotateYaw = -1.0f;
		m_NavigationRotationPitch = -1.0f;
	}
}

bool TrixenieLocomotion::canDoJump()
{
	bool result = false;
	if (Land_Loc == m_nMoveType)
	{
		ClientActor* pActor = getOwnerActor();

		auto pBlockEnvEffectsComponent = pActor->getBlockEnvEffectsComponent();
		auto RidComp = getOwnerActor()->getRiddenComponent();
		if (!(RidComp && RidComp->isRiding()))
		{
			if (!m_InWater && !m_InLava && !m_InHoney && !((pBlockEnvEffectsComponent && pBlockEnvEffectsComponent->GetStatus(ENV_THICKET))))
			{
				if (m_OnGround && m_JumpingTicks == 0)
				{
					result = true;
				}
			}
		}
	}
	return result;
}

void TrixenieLocomotion::setJumpToTarget(const WCoord &target)
{
	if (Land_Loc == m_nMoveType)
	{
		setJumping(true);
		m_JumpTarget = target;
	}
}




float TrixenieLocomotion::GetSpeedInAir()
{
	if (Air_Loc == m_nMoveType)
	{
		return m_SpeedInAir * m_SpeedMultiple;
	}
	return 1;
}

float TrixenieLocomotion::GetSpeedInWater()
{
	if (Aquatic_Loc == m_nMoveType)
	{
		return m_SpeedInWater * m_SpeedMultiple;
	}
	return 1;
}

void TrixenieLocomotion::UpdateRotation()
{
	auto pEntity = getOwnerActor()->getBody()->getEntity();
	if (Air_Loc == m_nMoveType)
	{
		float yaw = 0;
		float pitch = 0;

		if (m_SmoothRotationOn)
		{
			Vector3f tmpHead = m_smoother->Update(m_Velocity.GetNormalizedSafe());
			Direction2PitchYaw(&yaw, &pitch, tmpHead);
		}
		else
		{
			Direction2PitchYaw(&yaw, &pitch, m_Velocity);
		}

		m_RotateYaw = yaw;
		m_RotationPitch = Clamp(pitch, -15.0f, 15.0f);

// 		LOG_WARNING("!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!~!UpdateRotation yaw = %f, pitch = %f", m_RotateYaw, m_RotationPitch);
		if (pEntity && pEntity->GetMainModel())
		{
			pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
		}
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		if (pEntity == NULL)
			return;

		float yaw = 0;
		float pitch = 0;

		if (m_SmoothRotationOn)
		{
			Vector3f tmpHead = m_smoother->Update(m_Velocity.GetNormalized());
			Direction2PitchYaw(&yaw, &pitch, tmpHead);
		}
		else
		{
			Direction2PitchYaw(&yaw, &pitch, m_Velocity);
		}

		m_RotateYaw = yaw;
		m_RotationPitch = Clamp(pitch, -15.0f, 15.0f);
		auto pEntity = getOwnerActor()->getBody()->getEntity();
		if (pEntity && pEntity->GetMainModel())
		{
			pEntity->GetMainModel()->SetRotation(m_RotateYaw, -m_RotationPitch, 0);
		}
	}
}

void TrixenieLocomotion::SetBehaviorTypeWeight(BehaviorType type, float weight)
{
	if (Air_Loc == m_nMoveType)
	{
		switch (type)
		{
		case None:
			break;
		case Seek:
			break;
		case Flee:
			fleeWeight = weight;
			break;
		case Arrive:
			break;
		case Wander:
			break;
		case Cohesion:
			break;
		case Separation:
			break;
		case Allignment:
			break;
		case ObstacleAvoidance:
			obstacleAvoidanceWeight = weight;
			break;
		case WaterAvoidance:
			surfaceAvoidanceWeight = weight;
			break;
		case FollowPath:
			break;
		case Pursuit:
			pursuitWeight = weight;
			break;
		case Evade:
			break;
		case Interpose:
			break;
		case Hide:
			break;
		case Flock:
			break;
		case OffsetPursuit:
			break;
		default:
			break;
		}
	}
}

void TrixenieLocomotion::SetSmoothRotation(bool b)
{
	if (Air_Loc == m_nMoveType)
	{
		m_SmoothRotationOn = b;
	}
}

void TrixenieLocomotion::moveEntityWithDirection()
{
	if (Air_Loc == m_nMoveType)
	{
		Vector3f motion;
		if (isBehaviorOn(BehaviorType::LashTagert))
		{
			Vector3f desiredDirection = (m_MoveTarget - m_Position).toVector3();
			if (desiredDirection.Length() < (GetSpeedInAir()*0.05))
			{
				m_Velocity = desiredDirection / 0.05f;
			}
			else
			{
				desiredDirection.NormalizeSafe();
				m_Velocity = desiredDirection*GetSpeedInAir();
			}
		}
		else
		{
			if (m_InWater || m_InLava)
			{
				m_SteeringForce = Vector3f::yAxis * m_MaxSteeringForce;
			}
			else
			{
				calculateSteering();
			}
			Vector3f acceleration = m_SteeringForce / (float)(getOwnerActor()->m_Mass / 1000);

			m_Velocity = m_Velocity + acceleration * 0.05f;

			//Truncate(m_Velocity, GetSpeedInAir());
			m_Velocity.Truncate(GetSpeedInAir());
		}
		motion = m_Velocity * 0.05f;

		if (m_Velocity.LengthSqr() > 10.0f)
		{
			UpdateRotation();
		}

		WCoord mvec = getIntegerMotion(motion + m_ColliderMotion);
		if (mvec.x == 0 && mvec.y == 0 && mvec.z == 0)
		{
			return;
		}

		if (mvec.lengthSquared() < 10)
		{
			return;
		}


		CollideAABB box;
		getCollideBox(box);
		//m_OnGround = (m_pWorld->moveBox(box, WCoord(0, -1, 0)).y == 0);
		WCoord realMov = m_pWorld->moveBox(box, mvec);
		if (realMov.x == 0 && realMov.y == 0 && realMov.z == 0)
		{
			m_HasTarget = false;
		}
		m_bIsFlyBeStop = false;
		m_Position = box.pos + realMov + WCoord(m_BoundSize / 2, m_yOffset, m_BoundSize / 2);
		int flags = 0;
		m_CollidedHorizontally = false;
		m_CollidedVertically = false;
		if (realMov.y != mvec.y)
		{
			if (mvec.y < 0)
			{
				setOnGround(true);
				m_Motion.y = 0;
				m_AccumLeftMotion.y = 0;
			}
			m_CollidedVertically = true;
			flags |= 2;
		}
		if (mvec.x != 0 && realMov.x == 0)
		{
			m_AccumLeftMotion.x = 0;
			m_CollidedHorizontally = true;
			flags |= 1;
		}
		if (mvec.z != 0 && realMov.z == 0)
		{
			m_AccumLeftMotion.z = 0;
			m_CollidedHorizontally = true;
			flags |= 1;
		}
		if (flags & 1 /*|| flags & 2*/)
		{
			m_bIsFlyBeStop = true;
		}
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		if (m_InWater || m_InLava)
		{
			calculateSteering();
		}
		else {
			m_SteeringForce = Vector3f::neg_yAxis * m_MaxSteeringForce;
		}
		Vector3f acceleration = m_SteeringForce / (float)(getOwnerActor()->m_Mass / 1000);

		m_Velocity = m_Velocity + acceleration * 0.05f;

		//Truncate(m_Velocity, GetSpeedInWater());
		m_Velocity.Truncate(GetSpeedInWater());

		Vector3f m_Motion = m_Velocity * 0.05f;

		if (m_Velocity.LengthSqr() > 10.0f)
		{
			UpdateRotation();
		}

		WCoord mvec = getIntegerMotion(m_Motion + m_ColliderMotion);
		if (mvec.x == 0 && mvec.y == 0 && mvec.z == 0)
		{
			return;
		}

		if (mvec.lengthSquared() < 10)
		{
			return;
		}

		CollideAABB box;
		getCollideBox(box);

		//m_OnGround = (m_pWorld->moveBox(box, WCoord(0, -1, 0)).y == 0);
		WCoord realMov = m_pWorld->moveBox(box, mvec);

		m_Position = box.pos + realMov + WCoord(m_BoundSize / 2, m_yOffset, m_BoundSize / 2);
		int flags = 0;
		m_CollidedHorizontally = false;
		m_CollidedVertically = false;
		if (realMov.y != mvec.y)
		{
			if (mvec.y < 0)
			{
				setOnGround(true);
				m_Motion.y = 0;
				m_AccumLeftMotion.y = 0;
			}
			m_CollidedVertically = true;
			flags |= 2;
		}
		if (mvec.x != 0 && realMov.x == 0)
		{
			m_AccumLeftMotion.x = 0;
			m_CollidedHorizontally = true;
			flags |= 1;
		}
		if (mvec.z != 0 && realMov.z == 0)
		{
			m_AccumLeftMotion.z = 0;
			m_CollidedHorizontally = true;
			flags |= 1;
		}
	}
}

void TrixenieLocomotion::calculateSteering()
{
	if (Air_Loc == m_nMoveType)
	{
		m_SteeringForce = Vector3f::zero;
		//�ӱ����
		if (m_FearPlayerId != -1)
		{
			ClientPlayer *pTarget = getOwnerActor()->getActorMgr()->findPlayerByUin((unsigned int)m_FearPlayerId);

			if (pTarget != NULL)
			{
				AccumulateForce(m_SteeringForce, flee(pTarget->getPosition() * fleeWeight));
			}
		}

		//Wander����һ��Ŀ���
		if (isBehaviorOn(BehaviorType::Wander))
		{
			if (!AccumulateForce(m_SteeringForce, seek(m_MoveTarget) * wanderWeight))
			{
				return;
			}
		}

		//Pursute
		if (isBehaviorOn(BehaviorType::Pursuit))
		{
			if (!AccumulateForce(m_SteeringForce, seek(m_MoveTarget) * pursuitWeight))
			{
				return;
			}
		}
		//Truncate(m_SteeringForce, m_MaxSteeringForce);
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		m_SteeringForce = Vector3f::zero;

		//���ˮ��
		if (isBehaviorOn(BehaviorType::WaterAvoidance))
		{
			if (!AccumulateForce(m_SteeringForce, surfaceAvoidance() * surfaceAvoidanceWeight))
			{
				return;
			}
		}

		//��ܹ����
		if (isBehaviorOn(BehaviorType::ObstacleAvoidance))
		{
			if (!AccumulateForce(m_SteeringForce, obstacleAvoidance() * obstacleAvoidanceWeight))
			{
				return;
			}
		}

		//�ӱ����
		if (m_FearPlayerId != -1)
		{
			ClientPlayer *pTarget = getOwnerActor()->getActorMgr()->findPlayerByUin((unsigned int)m_FearPlayerId);

			if (pTarget != NULL)
			{
				AccumulateForce(m_SteeringForce, flee(pTarget->getPosition() * fleeWeight));
			}
		}

		//Wander����һ��Ŀ���
		if (isBehaviorOn(BehaviorType::Wander))
		{
			if (!AccumulateForce(m_SteeringForce, seek(m_MoveTarget) * wanderWeight))
			{
				return;
			}
		}

		//Pursute
		if (isBehaviorOn(BehaviorType::Pursuit))
		{
			if (!AccumulateForce(m_SteeringForce, seek(m_MoveTarget) * pursuitWeight))
			{
				return;
			}
		}
	}
}

Vector3f TrixenieLocomotion::seek(const WCoord& target)
{
	Vector3f desiredDirection = (target - m_Position).toVector3();
	desiredDirection.NormalizeSafe();
	if (Air_Loc == m_nMoveType)
	{
		return GetSpeedInAir() * desiredDirection - m_Velocity;
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		return GetSpeedInWater() * desiredDirection - m_Velocity;
	}
	return desiredDirection - m_Velocity;
}

Rainbow::Vector3f TrixenieLocomotion::flee(const WCoord& target)
{
	Vector3f desiredDirection = (m_Position - target).toVector3();
	desiredDirection.NormalizeSafe();
	if (Air_Loc == m_nMoveType)
	{
		return GetSpeedInAir() * desiredDirection - m_Velocity;
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		return GetSpeedInWater() * desiredDirection - m_Velocity;
	}
	return desiredDirection - m_Velocity;
}

Rainbow::Vector3f TrixenieLocomotion::pursuit(ClientActor& evader)
{
	Vector3f toEvader = (evader.getLocoMotion()->m_Position - m_Position).toVector3();

	float relativeHeading = DotProduct(m_Velocity, evader.getLocoMotion()->m_Motion);
	if (DotProduct(toEvader, m_Velocity) > 0 && relativeHeading < -0.95f)
	{
		return seek(evader.getLocoMotion()->m_Position);
	}
	auto funcWrapper = evader.getFuncWrapper();
	float speed = funcWrapper ? funcWrapper->getAIMoveSpeed() : 200.0f;
	float predictionTime = toEvader.Length() / (speed);
	if (Air_Loc == m_nMoveType)
	{
		
		predictionTime = toEvader.Length() / (GetSpeedInAir() + speed);
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		predictionTime = toEvader.Length() / (GetSpeedInWater() + speed);
	}
	return seek(evader.getLocoMotion()->m_Position + predictionTime * speed);
}

Rainbow::Vector3f TrixenieLocomotion::evade(ClientActor& pursuer)
{
	Vector3f toPursuer = (pursuer.getLocoMotion()->m_Position - m_Position).toVector3();
	auto funcWrapper = pursuer.getFuncWrapper();
	float speed = funcWrapper ? funcWrapper->getAIMoveSpeed() : 200.0f;
	float predictionTime = toPursuer.Length() / (speed);

	if (Air_Loc == m_nMoveType)
	{
		predictionTime = toPursuer.Length() / (GetSpeedInAir() + speed);
	}
	else if (Aquatic_Loc == m_nMoveType)
	{
		predictionTime = toPursuer.Length() / (GetSpeedInWater() + speed);
	}
	return flee(pursuer.getLocoMotion()->m_Position + predictionTime * speed);
}

bool TrixenieLocomotion::AccumulateForce(Rainbow::Vector3f& steeringForce, const Rainbow::Vector3f &forceToAdd)
{
	float currentForceSize = steeringForce.Length();

	float forceRemaing = m_MaxSteeringForce - currentForceSize;
	if (forceRemaing <= 0)
	{
		return false;
	}

	float forceToAddSize = forceToAdd.Length();

	if (forceToAddSize < forceRemaing)
	{
		steeringForce += forceToAdd;
	}
	else
	{
		Vector3f forceToAddNorlized = forceToAdd.GetNormalized();
		steeringForce += forceToAddNorlized * forceRemaing;
	}
	return true;
}

void TrixenieLocomotion::setCurMoveType(int type)
{ 
	if (m_nMoveType != type)
	{
		if (Land_Loc == m_nMoveType)
		{
			m_MoveTarget = m_Position;
		}
		else if (Air_Loc == m_nMoveType)
		{
			m_HasTarget = false;
			m_Velocity = Rainbow::Vector3f(0.0f, 0.0f, 0.0f);
			m_SpeedMultiple = 1.0f;
			m_MoveTarget = m_Position;
		}
		clearTarget();
		m_nMoveType = type;
	}
}

Rainbow::Vector3f TrixenieLocomotion::obstacleAvoidance()
{
	return Vector3f::zero;
}

//��ֹ�ܵ�ˮ����
Rainbow::Vector3f TrixenieLocomotion::surfaceAvoidance()
{
	if (isOffsetPositionInLiquid(m_Motion.x, m_Motion.y + 50, m_Motion.z))
	{
		return Vector3f::neg_yAxis * m_MaxSteeringForce;

	}
	else if (isOffsetPositionInLiquid(m_Motion.x, m_Motion.y + 70, m_Motion.z))
	{
		if (m_Velocity.y > 0.25 * GetSpeedInWater())
			return 0.25f *Vector3f::neg_yAxis * m_MaxSteeringForce;
		else
			return Vector3f::zero;
	}
	else if (isOffsetPositionInLiquid(m_Motion.x, m_Motion.y + 100, m_Motion.z))
	{
		if (m_Velocity.y > 0.5 * GetSpeedInWater())
			return 0.5f *Vector3f::neg_yAxis * m_MaxSteeringForce;
		else
			return Vector3f::zero;
	}

	return Vector3f::zero;
}
/* ����owner */
void TrixenieLocomotion::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	ActorLocoMotion::OnEnterOwner(owner);
#ifdef TestScriptCamera
	m_nCameraId = CameraManager::GetInstance().getSpCameraCount();
	char name[128];
	sprintf(name, "testcamera%d", m_nCameraId);
	CameraManager::GetInstance().addScriptCamera(name);
	if (getOwnerActor() && getOwnerActor()->getBody())
	{
		Quaternion quat = getOwnerActor()->getBody()->getRotation();
		WorldPos pos = getOwnerActor()->getBody()->getPosition();
		CameraManager::GetInstance().setSpCameraPosRot(name, pos, quat);
	}
#endif // TestScriptCamera
}