#include "ClientActorThornBall.h"
#include "ClientPlayer.h"
#include "ProjectileLocoMotion.h"
#include "ProjectileFactory.h"
#include "ThornBallComponent.h"
#include "ClientActorHelper.h"
#include "ActorBody.h"
#include "SandBoxManager.h"
#include "Entity/OgreEntity.h"
#include "world.h"
#include "ActorManager.h"
using namespace MINIW;

static int getThornBallAnchorId()
{
	int anchorId[5] = { 100,101,105,106,107 };
	int random = GenRandomInt(0, 4);
	return anchorId[random];
}

static Rainbow::Vector3f getPlayerThornPos(ClientActor* actor)
{
	if (dynamic_cast<ClientPlayer*>(actor) != nullptr)
	{
		bool minusX = GenRandomInt(10) >= 5;
		bool minusY = GenRandomInt(10) >= 5;
		bool minusZ = GenRandomInt(10) >= 5;

		int rx = GenRandomInt(0, 30);
		int ry = GenRandomInt(0, 30);
		int rz = GenRandomInt(15, 27);
		int ox = minusX ? rx : 0 - rx;
		int oy = 0 - ry;
		int oz = minusZ ? rz : 0 - rz;
		return Rainbow::Vector3f(ox, oz, oy);
	}
	//����̶��� 108.106 ����Ҫ�Լ����λ��
	return Rainbow::Vector3f(0, 0, 0);
}

class ThornBallLocoMotion : public ProjectileLocoMotion
{
	DECLARE_COMPONENTCLASS(ThornBallLocoMotion)
public:
	ThornBallLocoMotion();
	virtual void tick();
	int	m_TailEffectTick;
};

IMPLEMENT_COMPONENTCLASS(ThornBallLocoMotion)

ThornBallLocoMotion::ThornBallLocoMotion() : ProjectileLocoMotion()
{
	m_TailEffectTick = 0;
}

void ThornBallLocoMotion::tick()
{
	ClientActorThornBall* projectile = dynamic_cast<ClientActorThornBall*>(GetOwnerActor());
	if (projectile == nullptr)
	{
		return;
	}
	if (GetOwnerActor()->needClear()) return;
	if (m_pWorld == NULL) return;
	if (!projectile->isDrop())
	{
		ProjectileLocoMotion::tick();
	}
	else
	{
		//����
		ActorLocoMotion::tick();
		if (m_pWorld->isRemoteMode())
		{
			m_RotateYaw = syncYaw;
			m_RotationPitch = syncPitch;

			if (m_InterplateStep > 0)
			{
				m_Position = m_Position + (syncPos - m_Position) / m_InterplateStep;
				m_InterplateStep--;
			}
			else
			{
				m_Position = syncPos;
			}
		}
		else
		{
			m_Motion.x = 0;
			m_Motion.z = 0;
			m_Motion.y -= m_pWorld->getGravity(GRAVITY_LIVING);

			WCoord prevpos = m_Position;
			doMoveStep(m_Motion);
			m_Motion.y *= 0.98f;
			if (m_OnGround)
			{
				m_Motion.y *= -0.5f;
			}
		}
	}
	if (!m_pWorld->isRemoteMode())
	{
		CollideAABB box;
		GetOwnerActor()->getCollideBox(box);
		box.expand(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
		std::vector<IClientActor*>actors;
		m_pWorld->getActorsInBox(actors, box);
		for (size_t i = 0; i < actors.size(); i++)
		{
			ClientActor* actor = actors[i]->GetActor();
			if (actor->getObjType() == OBJ_TYPE_ROLE) continue;
			if (actor->getObjType() == OBJ_TYPE_THORNBALL) continue;
			if (!actor->needClear() && !projectile->needClear())
			{
				projectile->onImpactWithActor(actor, "");
			}
		}
	}
}


ClientActorThornBall::ClientActorThornBall() :
	m_ThornBallModel(nullptr),
	m_MaxActorCount(5),
	m_ImpactActor(nullptr),
	m_IsInMaxState(false),
	m_IsDrop(false), m_IsTailEffect(false)
{
	//OGRE_DELETE(m_LocoMotion);
	//m_LocoMotion = new ThornBallLocoMotion(this);

	CreateComponent<ThornBallLocoMotion>("ThornBallLocoMotion");
	
	getLocoMotion()->setBound(100, 100);
	getLocoMotion()->setNoClip(false);
	getLocoMotion()->m_yOffset = getLocoMotion()->m_BoundHeight / 2;
}
ClientActorThornBall::~ClientActorThornBall()
{

}

flatbuffers::Offset<FBSave::SectionActor> ClientActorThornBall::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveActorCommon(builder);
	ProjectileLocoMotion* loco = static_cast<ProjectileLocoMotion*>(getLocoMotion());
	auto quat = QuaternionToSave(loco->m_RotateQuat);
	auto obj = FBSave::CreateActorThornBall(builder, basedata, m_ShootingActorID, m_ItemID, &quat, m_IsDrop);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorThornBall, obj.Union());
}

bool ClientActorThornBall::load(const void* srcdata, int version)
{
	m_LiveTicks = 0;
	auto src = reinterpret_cast<const FBSave::ActorThornBall*>(srcdata);
	init(src->itemid());
	loadActorCommon(src->basedata());
	m_ShootingActorID = src->shooter();
	m_IsDrop = src->isDrop();
	
	ThornBallLocoMotion* loco = dynamic_cast<ThornBallLocoMotion*>(getLocoMotion());
	if (loco)
	{
		loco->syncPos = getLocoMotion()->getPosition();
		if (src->rotatequat())
			loco->m_RotateQuat = QuaternionFromSave(src->rotatequat());
		else
		{
			//loco->m_RotateQuat.setEulerAngle(loco->m_RotateYaw, loco->m_RotationPitch, 0);
			loco->m_RotateQuat = Rainbow::AngleEulerToQuaternionf(Rainbow::Vector3f(loco->m_RotationPitch, loco->m_RotateYaw, 0));
		}

		loco->m_CanTrigger = false;
		if (loco->needFullRotation())
		{
			//m_ServerYawCmp = loco->m_RotateQuat.compressToInt();
			m_ServerYawCmp = loco->m_RotateQuat.ToUInt32();
		}
		else
		{
			loco->syncYaw = loco->m_RotateYaw;
			loco->syncPitch = loco->m_RotationPitch;
		}
		//ֱ�ӻ��ʵ��λ��
		if (m_EntityModel)
		{
			m_EntityModel->SetPosition(getLocoMotion()->getPosition().toVector3());
			playMotion(m_ProjectileDef->TailEffect);
		}
	}
	setNeedTailEffect(true);
	return true;
}

void ClientActorThornBall::init(int itemid, ClientActor* shooter)
{
	ClientActorProjectile::init(itemid, shooter);
}

void ClientActorThornBall::setNeedClearEx(int delay_ticks)
{
	//����ʱ����ʧ
	if (!m_IsInMaxState)
	{
		if (!m_ImpactActor)
		{
			ClientActorProjectile::setNeedClearEx(delay_ticks);
		}
		else
		{
			auto thornBall = m_ImpactActor->sureThornBallComponent();
			if (thornBall)
			{
				int count = getBallLostCount();
				if (count <= thornBall->getThronBallNum())
				{
					ClientActorProjectile::setNeedClearEx(delay_ticks);
				}
			}
			
		}
	}
	m_ImpactActor = nullptr;
}

//void ClientActorThornBall::doTrigger()
//{
//
//}

void ClientActorThornBall::onImpactWithActor(ClientActor* actor, const std::string& partname)
{
	m_ImpactActor = actor;
	doImpactActor();
}

void ClientActorThornBall::onImpactWithBlock(const WCoord* blockpos, int face)
{
	m_ImpactTimeMark = 0;
}
int ClientActorThornBall::saveToPB(PB_GeneralEnterAOIHC* pb)
{
	PB_ActorThornBall* actorThornBall = pb->mutable_actorthornball();
	PB_ActorProjectile* actorMob = actorThornBall->mutable_actorprojectile();
	saveToProjectilePB(actorMob);
	ThornBallLocoMotion* loco = dynamic_cast<ThornBallLocoMotion*>(getLocoMotion());
	actorThornBall->set_isdrop(m_IsDrop);
	return 0;
}
int ClientActorThornBall::LoadFromPB(const PB_GeneralEnterAOIHC& pb)
{
	const PB_ActorThornBall& actorThornBall = pb.actorthornball();
	const PB_ActorProjectile& actorMob = actorThornBall.actorprojectile();
	const PB_ActorCommon& actorCommon = actorMob.basedata();
	int ret = loadPBActorCommon(actorCommon);
	if (ret != 0)
		return ret;
	LoadFromProjectilePB(actorMob);
	m_IsDrop = actorThornBall.isdrop();
	return 0;
}

void ClientActorThornBall::onCollideWithPlayer(ClientActor* player)
{
	m_ImpactActor = player;
	doImpactActor();
}

bool ClientActorThornBall::isMaxActorThornBall()
{
	return getBallLostCount() >= m_MaxActorCount;
}
void	ClientActorThornBall::setIsDrop(bool is)
{
	m_IsDrop = is;
}

bool 	ClientActorThornBall::isDrop()
{
	return m_IsDrop;
}
bool	ClientActorThornBall::isNeedTailEffect()
{
	return m_IsTailEffect;
}
void	ClientActorThornBall::setNeedTailEffect(bool is)
{
	m_IsTailEffect = is;
}
void	ClientActorThornBall::doImpactActor()
{
	m_IsInMaxState = false;
	m_ImpactTimeMark = 0;
	auto thornBall = m_ImpactActor->sureThornBallComponent();
	if (thornBall == nullptr)
	{
		return;
	}
	if (getBallLostCount() < thornBall->getThronBallNum())
	{
		createThornEntity(m_ImpactActor);
		setNeedClear();
	}
	else
	{
		m_IsInMaxState = true;
	}
}

int ClientActorThornBall::getBallLostCount()
{
	int count = 0;
	if (m_ImpactActor != nullptr)
	{
		auto thornBall = m_ImpactActor->getThornBallComponent();
		if (thornBall == nullptr)
		{
			return 0;
		}
		count = thornBall->getThornAnchorNum();
	}
	return count;
}
//### todo..
void ClientActorThornBall::createThornEntity2(ClientActor* actor, int num)
{
	auto pBody = actor->getBody();
	if (actor->needClear() || pBody == nullptr)
	{
		return;
	}
	Rainbow::Entity* pEntity = pBody->getEntity();
	if (pEntity == nullptr)
	{
		return;
	}
}



void ClientActorThornBall::createThornEntity(ClientActor* actor)
{
	auto pBody = actor->getBody();
	if (actor->needClear() || pBody == nullptr)
	{
		return;
	}
#ifndef DEDICATED_SERVER
	Rainbow::Entity* pEntity = pBody->getEntity();
	if (pEntity == nullptr || pEntity->GetMainModel() == nullptr)
	{
		return;
	}
#endif
	auto thornBall = actor->sureThornBallComponent();
	if (thornBall == nullptr)
	{
		return;
	}
	int anchorId = getThornBallAnchorId();
	Rainbow::Vector3f pos = getPlayerThornPos(actor);
	thornBall->setThornAnchorId(anchorId, pos);
	pBody->createThornBallMeshModel(anchorId, pos);

	jsonxx::Object context;
	char objid_str[128];
	sprintf(objid_str, "%lld", actor->getObjId());
	context << "objid" << objid_str;
	context << "anchorId" << anchorId;
	context << "pos" << (Vector3ToVec3Json(Rainbow::Vector3f(pos.x, pos.y, pos.z)));
	SandBoxManager::getSingletonPtr()->sendBroadCast("PB_ACTOR_CREATE_THORNBALL", context.bin(), context.binLen());
}

void   ClientActorThornBall::createSawtooth(World* pworld, const WCoord& blockpos, ClientActor* actor)
{
	if (pworld == nullptr) return ;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return ;
	auto thornComponent = actor->sureThornBallComponent();
	if (thornComponent == nullptr)
	{
		return;
	}
	if (thornComponent->getThornAnchorNum() < thornComponent->getThronBallNum())
	{
		//ֱ�Ӵ���
		ClientActorThornBall::createThornEntity(actor);
		return;
	}
	ActorLocoMotion* shootloc = actor->getLocoMotion();
	Rainbow::Vector3f dir;
	PitchYaw2Direction(dir, actor->getLocoMotion()->m_RotateYaw, actor->getLocoMotion()->m_RotationPitch);

	WCoord start = BlockCenterCoord(blockpos);
	start.y += GenRandomInt(30, 100);
	float s = GenRandomInt(70, 120);
	start.x += -dir.x * s;
	start.z += -dir.z * s;

	//const ProjectileDef* projectileDef = ProjectileDefCsv::getInstance()->get(12619, true);

	ClientActorThornBall* projectile = dynamic_cast<ClientActorThornBall*>(ProjectileFactory::createProjectile(12619, nullptr));
	projectile->setIsDrop(true);
	projectile->setShootingActor(actor); // ��������Ҫ��spawnActor֮ǰ֪��shooter
	actorMgr->spawnActor(projectile, start, actor->getLocoMotion()->m_RotateYaw, actor->getLocoMotion()->m_RotationPitch);
	projectile->m_StartPos = projectile->getPosition();

	ProjectileLocoMotion* projectileLoco = static_cast<ProjectileLocoMotion*>(projectile->getLocoMotion());

	projectileLoco->m_TicksInGround = 0;
	projectileLoco->m_Motion.x = 0;
	projectileLoco->m_Motion.y = 0;
	projectileLoco->m_Motion.z = 0;
	Direction2PitchYaw(&projectileLoco->m_RotateYaw, &projectileLoco->m_RotationPitch, projectileLoco->m_Motion);

	projectile->m_AttachedEffect |= 4;
	//projectile->playMotion(projectileDef->TailEffect);
	projectile->playMotion(projectile->m_ProjectileDef->TailEffect);
}
