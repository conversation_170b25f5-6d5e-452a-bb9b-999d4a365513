﻿#pragma once
#include "PlayerState.h"

class PCControl;
class InputInfo;
class PlayerControl;
class PlayerDownedStateAttrib;
class ClientPlayer;

class RevivePlayerState :public PlayerState//tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	RevivePlayerState(PlayerControl* host);
	virtual ~RevivePlayerState();
	virtual void doBeforeEntering() override;
	virtual std::string update(float dtime) override;
	virtual void doBeforeLeaving() override;
	virtual void OnTick(float elapse) final;
	//tolua_end

    // 救援相关方法
    void reset();
    bool canRevivePlayer() const { return m_canRevivePlayer; }
    bool isReviving() const { return m_isReviving; }
    int getReviveTicksProgress() const { return m_reviveTicksProgress; }
    int getRevivePercentage() const;
    long long getRevivingPlayerId() const { return m_revivingPlayerId; }
    // 获取归一化的救援进度 (0.0-1.0)，如果没在救援返回-1.0
    float getReviveProgressNormalized(long long targetId) const;
    // 检查是否有可救援的玩家
    void checkForDownedPlayers(PlayerControl* playerCtrl);

    // 尝试开始救援
    bool startRevivePlayer(PlayerControl* playerCtrl, long long playerId);

    // 停止救援
    void stopRevivePlayer(PlayerControl* playerCtrl);

    // 更新救援UI
    void updateReviveUI(PlayerControl* playerCtrl, bool forceUpdate = false);

private:
    // 更新InputInfo中的E键状态
    void updateInputInfo(InputInfo* inputInfo);

    // 更新救援进度
    void updateReviveProgress(int ticksElapsed, PlayerControl* playerCtrl);

    // 获取附近倒地玩家
    long long findNearestDownedPlayer(PlayerControl* playerCtrl) const;

    // 获取玩家之间的距离
    float getDistanceBetweenPlayers(ClientPlayer* currentPlayer, ClientPlayer* targetPlayer) const;

    // 判断玩家是否处于倒地状态
    bool isPlayerDowned(ClientPlayer* player) const;

    // 获取玩家的倒地状态组件
    PlayerDownedStateAttrib* getPlayerDownedStateAttrib(ClientPlayer* player) const;

    // 向服务器发送救援请求
    void sendReviveRequestToServer(PlayerControl* playerCtrl, long long targetId);

    // 向服务器发送取消救援请求
    void sendCancelReviveToServer(PlayerControl* playerCtrl);

    // 向服务器发送救援进度更新
    void sendReviveProgressToServer(PlayerControl* playerCtrl, int progress);

	// 救援相关属性
	bool m_canRevivePlayer;   // 是否有可救援的玩家
	bool m_isReviving;        // 是否正在救援
	int m_reviveTicksProgress; // 救援进度 (ticks)
	long long m_revivingPlayerId; // 正在被救援的玩家ID
	float m_maxReviveDistance; // 最大救援距离
	int m_uiUpdateTick;        // UI更新计时器
}; //tolua_exports