#include "InWaterSpecialCheck.h"

#include <cstdarg>
#include <algorithm>

#include "world.h"
#include "ClientActor.h"

#include "SandboxContext.h"
#include "ActorLocoMotion.h"
#include "Math/Vector3f.h"

using namespace MNSandbox;


bool InWaterSpecialCheck::isInRange(ClientActor* pActor, BlockEnvEffectBase* pEffect)
{
	if (!pActor)
	{
		return false;
	}
	ClientActor* owner = pActor;//context.GetData_Usertype<ClientActor>("owner");

	World* pWorld     = owner->getWorld();
	assert(pWorld != nullptr);
	ActorLocoMotion* locomotion = owner->getLocoMotion();
	assert(locomotion != nullptr);
	const WCoord& pos = owner->getPosition();

	int boundWidth  = locomotion->m_BoundSize;
	int boundHeight = locomotion->m_BoundHeight;

	bool canMoveInWater = locomotion->m_MovementAbility.CheckFlag(ActorLocoMotion::Movement_Water);
	///////////

	WCoord contract = WCoord(1, 40, 1);
	contract.y = std::min(contract.y, boundHeight/2 - 1);
	int halfSize = boundWidth >> 1;
	WCoord minpos = pos - WCoord(halfSize, 0, halfSize) + contract;
	WCoord maxpos = pos + WCoord(halfSize, boundHeight, halfSize) - contract;

	Rainbow::Vector3f flowmotion;
	if (canMoveInWater && pWorld->getFluidFlowMotion(minpos, maxpos, flowmotion)) {
		if (pEffect)
		{
			pEffect->SetFlowmotion(flowmotion);//context.SetData_UserObject<Vector3f>("flowmotion", flowmotion);
			pEffect->SetStatus(true);//context.SetData_Bool("status", true);
		}
		

		return true;
	}
	if (pEffect)
	{
		pEffect->SetStatus(false);//context.SetData_Bool("status", false);
	}
	
	if (pWorld->isAnyMoveCollideLiquid(minpos, maxpos))
		return true;
	
	return false;
}