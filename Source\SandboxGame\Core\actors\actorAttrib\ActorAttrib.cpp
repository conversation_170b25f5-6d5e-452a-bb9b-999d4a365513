#pragma warning( disable : 4482 )
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "AttribTypes.h"
#include "ActorAttrib.h"
#include "ClientActorLiving.h"
#include "LuaInterfaceProxy.h"
#include "ActorAttribExecute.h"
#include "DieComponent.h"
#include "ActionAttrStateComponent.h"
#include "TriggerComponent.h"
#include "AttackedComponent.h"
#include "FireBurnComponent.h"
#include "SandboxCfg.h"
#include "WorldManager.h"
#include "ClientMob.h"
#include "ClientActorLiving.h"
#include "SandBoxManager.h"
#include "HPValue.h"

using namespace MNSandbox;

bool ActorAttrib::m_DisplayHurtNumber = false;
IMPLEMENT_COMPONENTCLASS(ActorAttrib)

ActorAttrib::ActorAttrib() 
	: m_OwnerActor(NULL)
	, m_iAttackType(-1)
	, m_iImmuneType(0)
	, m_iImmuneAttackType(0)
#ifdef OLD_ATTRIBUTES
	, m_fBasicMaxHP(100.f)
	, m_fMaxHP(100.f)
#endif
	, m_Life(100.f)
	, m_fBasicOverflowHP(0.f)
	, m_fOverflowHP(0.f)
	, m_fRecover(-1.f)
	, m_immuneToFire(0)
	, m_MaxHurtInResistant(0)
	, m_HurtResistantTime(0)
	, m_maxArmor(0.f)
	, m_extraLife(0.f)
{
#ifndef OLD_ATTRIBUTES
	mHPValue = ENG_NEW(HPValue);
	mHPValue->SetMaxLimitValue(100);
#endif
	m_unsighted = 0;
	for (int i = 0; i < 5; i++) {
		m_fSpeed[i] = -1.0f;
	}

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		setImmuneType(ALL_Immune, true);
	}
	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
	if (lua_const)
	{
		m_maxArmor = lua_const->actor_armor_max;
	}
	m_hpExecute.push_back(ActorAttribType_Armor);
	m_hpExecute.push_back(ActorAttribType_Hp);
	if (GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		m_attribExecute[ActorAttribType_Hp] = SANDBOX_NEW(ActorHpExecute);
	}
	CreateEvent2();
}

ActorAttrib::~ActorAttrib()
{
#ifndef OLD_ATTRIBUTES
	mHPValue = nullptr;
#endif
	m_OwnerActor = NULL;
	m_hpExecute.clear();
	for (auto p = m_attribExecute.begin(); p != m_attribExecute.end(); p++)
	{
		SANDBOX_DELETE(p->second);
	}
	m_attribExecute.clear();
	DestroyEvent2();
}
void ActorAttrib::CreateEvent2()
{
	typedef ListenerFunctionRef<> ListenerBlockCoagulation;
	m_listenerBlockCoagulation = SANDBOX_NEW(ListenerBlockCoagulation, [&]() -> void {
		auto actor = this->getOwnerActor();
		if (this->immuneToFire() <= 0)
		{
			auto FireBurnComp = actor->sureFireBurnComponent();
			if (FireBurnComp)
			{
				FireBurnComp->setFire(100, 1);
			}
		}
		if (this->immuneToFire() <= 1)
		{
			auto component = actor->getAttackedComponent();
			if (component)
			{
				component->attackedFromType_Base(ATTACK_FIRE, 2.0f * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv);
			}
		}
		});
	Event2().Subscribe("BlockCoagulation_ActorWalk", m_listenerBlockCoagulation);
}
void ActorAttrib::DestroyEvent2()
{
	SANDBOX_RELEASE(m_listenerBlockCoagulation);
}
void ActorAttrib::revive()
{
#ifdef OLD_ATTRIBUTES
	m_Life = m_fMaxHP;
#else
	m_Life = mHPValue->GetMaxLimitValue();
#endif
	if (m_OwnerActor) m_OwnerActor->Event2().Emit<int>("changeHPOnTrigger", 0);
}

void ActorAttrib::onDie()
{
#ifdef BUILD_MINI_EDITOR_APP
	//工具编辑模式下，屏蔽死亡逻辑
	auto editorRunMode = MNSandbox::SandBoxCfg::GetInstancePtr()->getRun();
	if (editorRunMode == MNSandbox::RunMode::editor_edit)
	{
		return;
	}
#endif

	m_Life = -1;
	m_HurtResistantTime = 0;
	m_MaxHurtInResistant = 0;
}

void ActorAttrib::resetMaxLife(float v)
{
#ifdef OLD_ATTRIBUTES
	m_fMaxHP = v;
	m_Life = m_fMaxHP;
#else
	mHPValue->SetMaxLimitValue(v);
	m_Life = mHPValue->GetMaxLimitValue();
#endif
}

bool ActorAttrib::isDead()
{
	if (m_Life < 0.0f)  //ClientActor::kill() getAttrib()->setHpForTrigger(-1.0f); -1.0是kill掉Mob的  村民没血是0
	{
		return true;
	}
	return m_Life <= 0 && m_extraLife <= 0;
}

/*=========================================================*/
void ActorAttrib::addHP(float hp, bool overflowable)
{
	//熊猫无敌
	ClientMob* mob = dynamic_cast<ClientMob*>(m_OwnerActor);
	if (mob && (mob->getMonsterId() == 3416 || mob->getMonsterId() == 3417)) {
		return;
	}
#ifdef BUILD_MINI_EDITOR_APP
	//工具编辑模式下，屏蔽血量操作
	auto editorRunMode = MNSandbox::SandBoxCfg::GetInstancePtr()->getRun();
	if (editorRunMode == MNSandbox::RunMode::editor_edit)
	{
		return;
	}
#endif //
	bool isHomeActor = false;
	MNSandbox::GetGlobalEvent().Emit<bool&,ClientActor*>("HOME_ADDHP", isHomeActor,m_OwnerActor);
	if (isHomeActor)
	{
		return;
	}
	bool islive = m_Life > 0;
	for (auto p : m_hpExecute)
	{
		auto ex = getExtraExecute(p);
		if (ex && ex->ExecuteAdd(this, hp, overflowable, ActorAttribType_Hp))
		{
			return;
		}
	}
	float maxHP = getMaxHP();
	float limitHP;
	if (overflowable)
	{
		limitHP = getLimitHP();
	}
	else
	{
		//溢出则保留
		limitHP = m_Life > maxHP ? m_Life : maxHP;
	}

	if (m_OwnerActor)
	{
		ActorLiving *targetLiving = dynamic_cast<ActorLiving *>(m_OwnerActor);
		if (targetLiving && 0 != hp)
		{
			targetLiving->setHPProgressDirty();
		}

		if (!(hp >= 0 && m_Life >= limitHP)) //血量是满的  不显示加血效果
		{
			m_OwnerActor->addHPEffect(hp);
		}
	}
#ifdef OLD_ATTRIBUTES
	if (!overflowable && m_Life > m_fMaxHP && hp > 0) 
	{
		hp = 0;
	}
#else
	if (!overflowable && m_Life > mHPValue->GetMaxLimitValue() && hp > 0)
	{
		hp = 0;
	}
#endif
	const int old = int(m_Life);
	m_Life += hp;
	GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_ACTOR_ATTRIB, SandBoxMgrTypeID::ACTOR_CHANGE_HP, m_OwnerActor->getObjId() & 0xffffffff, (char*)&hp, sizeof(hp));

	auto ActionAttrStateComp = m_OwnerActor->getActionAttrStateComponent();
	if (islive && m_Life <= 0 && !(ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_BEKILLED)))
		m_Life = 1;

	if (m_Life > limitHP) m_Life = limitHP;
	if (m_Life < 0) m_Life = 0;

#ifdef OLD_ATTRIBUTES
	if (m_Life > m_fMaxHP)
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_OVERFLOWABLE_HP, m_Life);
	}
	else
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_HP, m_Life);
	}
#else
	if (m_Life > mHPValue->GetMaxLimitValue())
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_OVERFLOWABLE_HP, m_Life);
	}
	else
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_HP, m_Life);
	}
#endif

	// 受到伤害在死亡之前执行，否则伤害事件将获取不到攻击者信息
	if (hp < 0 && m_OwnerActor)
	{
		auto triggerComponent = m_OwnerActor->getTriggerComponent();
		if (triggerComponent)
		{
			triggerComponent->beHurtOnTrigger(hp);
		}
		auto attackedComponent = m_OwnerActor->getAttackedComponent();
		if (attackedComponent)
		{
			attackedComponent->recordAttacker(1);	// 1受伤
		}
	}

	if (islive && m_Life == 0 && m_OwnerActor != NULL)
	{
		auto attackedComponent = m_OwnerActor->getAttackedComponent();
		if (attackedComponent)
		{
			attackedComponent->recordAttacker(2);	// 2死亡
		}
		auto DieComp = m_OwnerActor->SureDieComponent();
		if (DieComp) DieComp->onDie();
	}



	//仅整数部分变化时下发通知
	if (int(m_Life) != old)
	{
		if (m_OwnerActor == g_pPlayerCtrl)
		{
			//ge GetGameEventQue().postPlayerAttrChange();
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
		}
			
		
		if (g_WorldMgr && m_OwnerActor && m_OwnerActor->isPlayer())
			g_WorldMgr->signChangedToSync(m_OwnerActor->getObjId(), BIS_HP);

		if (m_OwnerActor) m_OwnerActor->Event2().Emit<int>("changeHPOnTrigger", old);
	}
}

void ActorAttrib::setHP(float hp, bool overflowable)
{
	auto islive = m_Life > 0 || m_extraLife > 0;
	for (auto p : m_hpExecute)
	{
		auto ex = getExtraExecute(p);
		if (ex && ex->ExecuteSet(this, hp, overflowable, ActorAttribType_Hp))
		{
			// 血量小于0 触发死亡
			if (islive && (m_Life <= 0 && m_extraLife == 0) && m_OwnerActor) {
				auto DieComp = m_OwnerActor->SureDieComponent();
				if (DieComp) DieComp->onDie();
			}
			return;
		}
	}
	if (m_OwnerActor)
	{
		ActorLiving *targetLiving = dynamic_cast<ActorLiving *>(m_OwnerActor);
		if (targetLiving && m_Life != hp)
		{
			targetLiving->setHPProgressDirty();
		}
	}

	const float maxHP = getMaxHP();
	if (overflowable)
	{
		if (hp > getLimitHP())
		{
			hp = getLimitHP();
		}
	}
	else
	{
		if (hp > maxHP)
		{
			hp = maxHP;
		}
	}
	const int old = int(m_Life);
	m_Life = hp;

	if (hp > maxHP)
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_OVERFLOWABLE_HP, hp);
	}
	else
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_HP, hp);
	}

	if (islive && m_Life <= 0 && m_OwnerActor ) {
		auto DieComp = m_OwnerActor->SureDieComponent();
		if (DieComp) DieComp->onDie();
	}

	//仅整数部分变化时下发通知
	if (int(hp) != old)
	{
		if (m_OwnerActor == g_pPlayerCtrl)
		{
			//ge GetGameEventQue().postPlayerAttrChange();
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
		}
			
		
		if (g_WorldMgr && m_OwnerActor && m_OwnerActor->isPlayer())
			g_WorldMgr->signChangedToSync(m_OwnerActor->getObjId(), BIS_HP);
	}
}

float ActorAttrib::getHP()
{
	return m_Life;
}

void ActorAttrib::setHpForTrigger(float hp, bool overflowable)
{
	setHP(hp, overflowable);
}

void ActorAttrib::setBasicMaxHP(float max)
{
	if (max < 0)
	{
		return;
	}
	
#ifdef OLD_ATTRIBUTES
	m_fBasicMaxHP = max;
#else
	mHPValue->SetBasicMaxHP(max);
#endif
	//m_fMaxHP = max;
	setMaxHP(max);
}

float ActorAttrib::getBasicMaxHP()
{

#ifdef OLD_ATTRIBUTES
	return m_fBasicMaxHP;
#else
	return mHPValue->GetBasicMaxHP();
#endif
}

void ActorAttrib::setMaxHP(float max)
{
	if (max < 0 || !m_OwnerActor)
		return;

	if (max == 0)
	{
		m_OwnerActor->kill();
		return;
	}

	ActorLiving *targetLiving = dynamic_cast<ActorLiving *>(m_OwnerActor);
#ifdef OLD_ATTRIBUTES
	bool bNeedNotify = (targetLiving && abs(m_fMaxHP - max) > 0.000001f);
	m_fMaxHP = max;
#else
	bool bNeedNotify = (targetLiving && abs(mHPValue->GetMaxLimitValue() - max) > 0.000001f);
	mHPValue->SetMaxLimitValue(max);
#endif
	max = getMaxHP();
	const float overflow = getOverflowHP();
	if (overflow > max)
	{
		setOverflowHP(max);
	}

	m_OwnerActor->addAttChangeFlag(1);
	m_OwnerActor->syncAttr(ATTRT_MAX_HP, max);
	if (bNeedNotify)
	{
		targetLiving->setHPProgressDirty();
		//ge GetGameEventQue().postPlayerAttrChange();
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	}
	if (g_WorldMgr && m_OwnerActor && m_OwnerActor->isPlayer())
		g_WorldMgr->signChangedToSync(m_OwnerActor->getObjId(), BIS_HP_MAX);
}

void ActorAttrib::initMaxHP(float maxHP)
{
#ifdef OLD_ATTRIBUTES
		m_fMaxHP = maxHP;
#else
		mHPValue->SetMaxLimitValue(maxHP);
#endif
}

float ActorAttrib::getMaxHP()
{
#ifdef OLD_ATTRIBUTES
	return m_fMaxHP;
#else
	return mHPValue->GetMaxLimitValue();
#endif
}

void ActorAttrib::setHPRecover(float recover)
{
#ifdef OLD_ATTRIBUTES
	if (m_fMaxHP < recover)
		m_fRecover = m_fMaxHP;
	else
		m_fRecover = recover;
#else
	if (mHPValue->GetMaxLimitValue() < recover)
		m_fRecover = mHPValue->GetMaxLimitValue();
	else
		m_fRecover = recover;

#endif
}

float ActorAttrib::getHPRecover()
{
	return m_fRecover;
}

void ActorAttrib::setBasicOverflowHP(float overflow)
{
	m_fBasicOverflowHP = overflow;
	//m_fOverflowHP = overflow;
	setOverflowHP(overflow);
}

float ActorAttrib::getBasicOverflowHP()
{
	return m_fBasicOverflowHP;
}

void ActorAttrib::setOverflowHP(float overflow)
{
	if (overflow < 0)overflow = 0;
	if (overflow > getMaxHP())
	{
		if (m_OwnerActor->getWorld() && !m_OwnerActor->getWorld()->isRemoteMode())
		{
			overflow = getMaxHP();
		}
	}
	m_fOverflowHP = overflow;
	m_OwnerActor->syncAttr(ATTRT_OVERFLOW_HP, overflow);
	if (getHP() > getLimitHP())
	{
		setHP(getLimitHP(), overflow > 0);
	}
		
	if (g_WorldMgr && m_OwnerActor && m_OwnerActor->isPlayer())
		g_WorldMgr->signChangedToSync(m_OwnerActor->getObjId(), BIS_HP_OVERFLOW);
}

float ActorAttrib::getOverflowHP()
{
	World* world = m_OwnerActor->getWorld();
	if (!world)
	{
		return m_fOverflowHP;
	}
	if (world->isRemoteMode())
	{
		return m_fOverflowHP;
	}
	m_fOverflowHP = m_fBasicOverflowHP;
	return m_fOverflowHP;
}

float ActorAttrib::getLimitHP()
{
	return getMaxHP() + getOverflowHP();
}

/*=========================================================*/

void ActorAttrib::setExtraHP(float hp)
{
	if (hp < 0)
		hp = 0;
	float oldExtraLife = m_extraLife;
	m_extraLife = hp;
	// 受到伤害在死亡之前执行，否则伤害事件将获取不到攻击者信息
	if (m_extraLife < oldExtraLife && m_OwnerActor)
	{
		auto attackComp = m_OwnerActor->getAttackedComponent();
		auto triggerComp = m_OwnerActor->getTriggerComponent();
		if (triggerComp)
		{
			triggerComp->beHurtOnTrigger(hp);
		}
		if (attackComp)
		{
			attackComp->recordAttacker(1);	// 1受伤
		}
	}

	if (oldExtraLife > 0 && m_OwnerActor != NULL && m_extraLife == 0 && m_Life <= 0)
	{
		auto attackComp = m_OwnerActor->getAttackedComponent();
		if (attackComp)
		{
			attackComp->recordAttacker(2);	// 2死亡
		}
		auto dieComp = m_OwnerActor->SureDieComponent();
		if (dieComp)
		{
			dieComp->onDie();
		}
	}
}

float ActorAttrib::getExtraHP()
{
	return m_extraLife;
}

/*=========================================================*/

float ActorAttrib::getMoveSpeed(int type /* = 0 */)
{
	return 0.0f;
}

void ActorAttrib::setSpeedAtt(int type, float v)
{
	if (!isValidSpeedType(type)) { return; }

	m_fSpeed[type] = v;

	if (m_OwnerActor)
		m_OwnerActor->syncAttr(ATTRT_WALK_SPEED + type, v);
}

float ActorAttrib::getSpeedAtt(int type)
{
	if (!isValidSpeedType(type)) { return -1.0f; }

	return m_fSpeed[type];
}

int ActorAttrib::getImmuneTypeByAttackType(int iImmuneType)
{
	switch (iImmuneType)
	{
	case ATTACK_TYPE::ATTACK_PUNCH:
		return ImmuneAttackType::Punch_Immune;
	case ATTACK_TYPE::ATTACK_RANGE:
		return ImmuneAttackType::Range_Immune;
	case ATTACK_TYPE::ATTACK_EXPLODE:
		return ImmuneAttackType::Explode_Immune;
	case ATTACK_TYPE::ATTACK_FIRE:
		return ImmuneAttackType::Fire_Immune;
	case ATTACK_TYPE::ATTACK_POISON:
		return ImmuneAttackType::Poison_Immune;
	case ATTACK_TYPE::ATTACK_WITHER:
		return ImmuneAttackType::Wither_Immune;
	case ATTACK_TYPE::ATTACK_FALLING:
		return ImmuneAttackType::Falling_Immune;
	case ATTACK_TYPE::ATTACK_WALL:
		return ImmuneAttackType::Wall_Immune;
	case ATTACK_TYPE::PHYSICS_ATTACK:
		return ImmuneAttackType::Physics_Immune;
	case ATTACK_TYPE::MAX_MAGIC_ATTACK:
		return ImmuneAttackType::Magic_Immune;
	case ATTACK_TYPE::ATTACK_ANVIL:
		return ImmuneAttackType::Anvil_Immune;
	case ATTACK_TYPE::ATTACK_CACTUS:
		return ImmuneAttackType::Cactus_Immune;
	case ATTACK_TYPE::ATTACK_ANTIINJURY:
		return ImmuneAttackType::Antiinjury_Immune;
	case ATTACK_TYPE::ATTACK_DROWN:
		return ImmuneAttackType::Drown_Immune;
	case ATTACK_TYPE::ATTACK_SUFFOCATE:
		return ImmuneAttackType::Suffocate_Immune;
	case ATTACK_TYPE::ATTACK_BLOCK_LASER:
		return ImmuneAttackType::Laser_Immune;
	case ATTACK_TYPE::ATTACK_FIXED:
		return ImmuneAttackType::Fixed_Immune;
	case ATTACK_TYPE::ATTACK_ALL:
		return ImmuneAttackType::ALL_Immune;
	case ATTACK_TYPE::TRUE_DAMAGE:
		return ImmuneAttackType::True_Immune;
	case ATTACK_TYPE::ATTACK_THORNBALL:
		return ImmuneAttackType::Thron_Immune;
	case ATTACK_TYPE::ATTACK_ICE:
		return ImmuneAttackType::Ice_Immune;
	}

	return 0;
}

void ActorAttrib::setImmuneType(int iImmuneType, bool bAdd)
{
	iImmuneType = getImmuneTypeByAttackType(iImmuneType);

	if (bAdd)
		m_iImmuneType |= iImmuneType;
	else
		m_iImmuneType &= ~iImmuneType;
}

void ActorAttrib::setImmuneAttackType(int iImmuneType, bool bAdd)
{
	iImmuneType = getImmuneTypeByAttackType(iImmuneType);

	if (bAdd)
		m_iImmuneAttackType |= iImmuneType;
	else
		m_iImmuneAttackType &= ~iImmuneType;
}
#ifdef IWORLD_DEV_BUILD
std::string ActorAttrib::toString()
{
	ostringstream oss;
#ifdef OLD_ATTRIBUTES
	oss << "hp = " << m_Life << '/' << m_fMaxHP << '(' << m_fOverflowHP << ')';
#else
	oss << "hp = " << m_Life << '/' << mHPValue->GetMaxLimitValue() << '(' << m_fOverflowHP << ')';
#endif
	return oss.str();
}
#endif

void ActorAttrib::tick()
{
	if (m_OwnerActor->isDead())
		return;
	if (m_HurtResistantTime > 0) m_HurtResistantTime--;
}

bool ActorAttrib::attackedFrom(OneAttackData &atkdata, ClientActor *attacker/* =NULL */)
{
	if (!m_OwnerActor)
		return false;
	auto ActionAttrStateComp = m_OwnerActor->getActionAttrStateComponent();
	if (!(ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_BEATTACKED))) {
		//LOG_INFO("ActorAttrib::attackedFrom(): false 1");
		return false;
	}

	float ap = 0.f;
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
	{
		for (int i = 0; i < 10; i++)
		{
			ap += atkdata.atkPointsNew[i];
		}
		for (int i = 0; i < 7; i++)
		{
			ap += atkdata.explodePoints[i];
		}
	}
	else
		ap = atkdata.atkpoints;
	if (ap == 0) ap = 1.0f * GetLuaInterfaceProxy().get_lua_const()->kongshou_shanghai_beilv; //modify by null  乘以空手伤害倍率

	if (atkdata.atktype != ATTACK_RANGE)
	{
		if (m_HurtResistantTime > MAX_HURTRESISTANT_TIME / 2)
		{
			if (ap <= m_MaxHurtInResistant)
			{
				//LOG_INFO("ActorAttrib::attackedFrom(): false 2");
				return false;
			}

			m_MaxHurtInResistant = ap;
		}
		else
		{
			m_MaxHurtInResistant = ap;
			m_HurtResistantTime = MAX_HURTRESISTANT_TIME;
		}
	}

	int iAttackType = ((attacker && attacker->getAttrib()) ? attacker->getAttrib()->getAttackType(atkdata.atktype) : atkdata.atktype);
	if (hasImmuneAttackType(getImmuneTypeByAttackType(iAttackType)))
	{
		return false;
	}
	if (!hasImmuneType(getImmuneTypeByAttackType(iAttackType)))
	{
		if (iAttackType == TRUE_DAMAGE)
		{
			addHPByTrueDamage(-ap);
		}
		else
		{
			addHP(-ap);
		}
	}
	atkdata.knockback = atkdata.knockup = 0;

	if (m_OwnerActor)
	{
		auto triggerComponent = m_OwnerActor->getTriggerComponent();
		if (triggerComponent)
		{
			triggerComponent->ActorDamageOnTrigger(attacker, (int)(-ap), atkdata.atktype, atkdata.triggerhit, atkdata.isAttackHead);
		}
	}
	if (ap > 0 && attacker)
	{
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(attacker);
		if (player)
		{
			player->damageActorToTrigger(m_OwnerActor->getObjId(), m_OwnerActor->getDefID(), (int)ap, atkdata.isAttackHead, atkdata.ignoreTriggerEvent);
		}
	}
	return true;
}
void ActorAttrib::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	m_OwnerActor = dynamic_cast<ClientActor*>(owner);
	if (m_OwnerActor)
	{
		m_OwnerActor->BindAttrib(this);
	}
	Super::OnEnterOwner(owner);
}

void ActorAttrib::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	if (m_OwnerActor)
	{
		m_OwnerActor->BindAttrib(NULL);
	}
	m_OwnerActor = nullptr;
	Super::OnLeaveOwner(owner);
}

void ActorAttrib::addHPByTrueDamage(float hp, bool overflowable)
{
	bool islive = m_Life > 0;
	for (auto p : m_hpExecute)
	{
		//真实伤害跳过护甲
		if (p == ActorAttribType_Armor)
		{
			continue;
		}
		auto ex = getExtraExecute(p);
		if (ex && ex->ExecuteAdd(this, hp, overflowable, ActorAttribType_Hp))
		{
			return;
		}
	}

	float maxHP = getMaxHP();
	float limitHP;
	if (overflowable)
	{
		limitHP = getLimitHP();
	}
	else
	{
		//溢出则保留
		limitHP = m_Life > maxHP ? m_Life : maxHP;
	}

	if (m_OwnerActor)
	{
		ActorLiving *targetLiving = dynamic_cast<ActorLiving *>(m_OwnerActor);
		if (targetLiving && 0 != hp)
		{
			targetLiving->setHPProgressDirty();
		}

		if (!(hp >= 0 && m_Life >= limitHP)) //血量是满的  不显示加血效果
		{
			m_OwnerActor->addHPEffect(hp);
		}
	}
#ifdef OLD_ATTRIBUTES
	if (!overflowable && m_Life > m_fMaxHP && hp > 0) {
		hp = 0;
	}
#else
	if (!overflowable && m_Life > mHPValue->GetMaxLimitValue() && hp > 0) 
	{
		hp = 0;
	}
#endif
	const int old = int(m_Life);
	m_Life += hp;
	GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_ACTOR_ATTRIB, SandBoxMgrTypeID::ACTOR_CHANGE_HP, m_OwnerActor->getObjId() & 0xffffffff, (char*)&hp, sizeof(hp));

	auto ActionAttrStateComp = m_OwnerActor->getActionAttrStateComponent();
	if (islive && m_Life <= 0 && !(ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_BEKILLED)))
		m_Life = 1;

	if (m_Life > limitHP) m_Life = limitHP;
	if (m_Life < 0) m_Life = 0;
#ifdef OLD_ATTRIBUTES
	if (m_Life > m_fMaxHP)
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_OVERFLOWABLE_HP, m_Life);
	}
	else
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_HP, m_Life);
	}
#else
	if (m_Life > mHPValue->GetMaxLimitValue())
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_OVERFLOWABLE_HP, m_Life);
	}
	else
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_HP, m_Life);
	}
#endif
	// 受到伤害在死亡之前执行，否则伤害事件将获取不到攻击者信息
	if (hp < 0 && m_OwnerActor)
	{
		auto triggerComponent = m_OwnerActor->getTriggerComponent();
		if (triggerComponent)
		{
			triggerComponent->beHurtOnTrigger(hp);
		}
		auto attackedComponent = m_OwnerActor->getAttackedComponent();
		if (attackedComponent)
		{
			attackedComponent->recordAttacker(1);	// 1受伤
		}
	}

	if (islive && m_Life <= 0 && m_OwnerActor != NULL)
	{
		auto attackedComponent = m_OwnerActor->getAttackedComponent();
		if (attackedComponent)
		{
			attackedComponent->recordAttacker(2);	// 2死亡
		}
		auto DieComp = m_OwnerActor->SureDieComponent();
		if (DieComp) DieComp->onDie();
	}



	//仅整数部分变化时下发通知
	if (int(m_Life) != old)
	{
		if (m_OwnerActor == g_pPlayerCtrl)
		{
			//ge GetGameEventQue().postPlayerAttrChange();
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
		}
			

		if (g_WorldMgr && m_OwnerActor && m_OwnerActor->isPlayer())
			g_WorldMgr->signChangedToSync(m_OwnerActor->getObjId(), BIS_HP);

		if (m_OwnerActor) m_OwnerActor->Event2().Emit<int>("changeHPOnTrigger", old);
	}
}

ActorAttribExecute* ActorAttrib::getExtraExecute(ActorAttribType type)
{
	if (m_attribExecute.find(type) != m_attribExecute.end())
	{
		return m_attribExecute[type];
	}
	return nullptr;
}

void ActorAttrib::addArmor(float val)
{
	if (!GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		return;
	}
	ActorAttribExecute* ex = getExtraExecute(ActorAttribType_Armor);
	if (!ex)
	{
		ex = SANDBOX_NEW(ActorArmorExecute, 0, 0, m_maxArmor);
		m_attribExecute[ActorAttribType_Armor] = ex;
	}
	ex->ExecuteAdd(this, val, false, ActorAttribType_Armor);
}

float ActorAttrib::getArmor()
{
	ActorAttribExecute* ex = getExtraExecute(ActorAttribType_Armor);
	if (!ex)
	{
		return 0.f;
	}
	return ((ActorArmorExecute*)ex)->getValue();
}

void ActorAttrib::setArmor(float v, bool force)
{
	if (!GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		return;
	}
	if (!force)
	{
		float oldval = getArmor();
		//护甲设置规则, 高的覆盖低的
		if (v <= oldval)
		{
			return;
		}

	}
	ActorAttribExecute* ex = getExtraExecute(ActorAttribType_Armor);
	if (!ex)
	{
		ex = SANDBOX_NEW(ActorArmorExecute, 0, 0, m_maxArmor);
		m_attribExecute[ActorAttribType_Armor] = ex;
	}
	ex->ExecuteSet(this, v, false, ActorAttribType_Armor);
}

void ActorAttrib::setMaxArmor(float v)
{
	if (!GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		return;
	}

	m_maxArmor = v;
	ActorArmorExecute* ex = static_cast<ActorArmorExecute*>(getExtraExecute(ActorAttribType_Armor));
	if (ex)
	{
		ex->setMaxValue(v);
	}
}

bool ActorAttrib::isUnsighted()
{
	return m_unsighted > 0;
}
void ActorAttrib::setUnsighted(bool isUnsighted)
{
	if (isUnsighted)
	{
		isUnsighted = 1;
	}
	else
	{
		isUnsighted = 0;
	}
}