
#include "container_arm_prismatic.h"
#include "ActorMechaUnit.h"
#include "world.h"
#include "ClientActorManager.h"
#include "VehicleJoint.h"
#include "VehicleJointArmPrismatic.h"
#include "BlockMaterialMgr.h"
#include "ActorVehicleAssemble.h"
#include "SandboxEventDispatcherManager.h"
#include "SandboxIdDef.h"
#include "VehicleWorld.h"

int g_nIntervalTime = 3 * 20; //1��20֡

ContainerArmPrismatic::ContainerArmPrismatic() 
	: m_BindUnitID(0)
	, m_bHaveCreateActor(false)
	, m_nExtendSize(0)
	, m_nToExtendSize(0)
//	, m_nNextStepSize(0)
	, m_nState(0)
	, m_bIsAction(false)
	, m_nPower(0)
	, m_BindJoint(NULL)
	, m_nIntervalTick(0)
	, m_nTicker(0)
	, m_bIsArrive(false)
	, m_nOrgSize(0)
	, m_nExtendStepSize(1)
{
	m_ActionMode = NORMAL_MODE;
	m_ActionerSignal = false;
	m_SlideTick = 0;
	m_ActionerPos = WCoord(-1, -1, -1);
	m_LastActionerPos = WCoord(-1, -1, -1);
	m_NeedTick = true;
}

ContainerArmPrismatic::ContainerArmPrismatic(const WCoord &blockpos) 
	: WorldContainer(blockpos, 0)
	, m_BindUnitID(0)
	, m_bHaveCreateActor(false)
	, m_nExtendSize(0)
	, m_nToExtendSize(0)
//	, m_nNextStepSize(0)
	, m_nState(0)
	, m_bIsAction(false)
	, m_nPower(0)
	, m_BindJoint(NULL)
	, m_nIntervalTick(0)
	, m_nTicker(0)
	, m_bIsArrive(false)
	, m_nOrgSize(0)
	, m_nExtendStepSize(1)
{
	m_ActionMode = NORMAL_MODE;
	m_ActionerSignal = false;
	m_SlideTick = 0;
	m_ActionerPos = WCoord(-1, -1, -1);
	m_LastActionerPos = WCoord(-1, -1, -1);
	m_NeedTick = true;
}

ContainerArmPrismatic::~ContainerArmPrismatic()
{
}

void ContainerArmPrismatic::SetActionMode(int mode)
{
	if (ACTIONER_MODE_INIT == mode)
	{
		m_nOrgSize = m_nExtendSize;
	}
	else if (NORMAL_MODE == mode && m_ActionMode != mode)
	{
		m_nExtendSize = 0;
		m_nState = 0;
	}
	m_nTicker = 0;
	m_nExtendStepSize = 1;
	m_ActionMode = mode;
}

void ContainerArmPrismatic::setBindJoint(VehicleJoint* joint)
{
	m_BindJoint = joint;
}

VehicleJoint* ContainerArmPrismatic::getBindJoint()
{
	return m_BindJoint;
}

int ContainerArmPrismatic::getObjType() const
{
	return OBJ_TYPE_ARM_PRISMATIC;
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerArmPrismatic::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerArmPrismatic(builder, basedata, m_BindUnitID, m_ActionMode, m_nExtendSize, m_nState, m_bIsAction, m_nPower);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerArmPrismatic, actor.Union());
}

bool ContainerArmPrismatic::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerArmPrismatic *>(srcdata);
	if (!src)
	{
		return false;
	}
	loadContainerCommon(src->basedata());

	m_BindUnitID = src->bindunit();
	m_nExtendSize = src->extendsize();
	m_nState = src->state();
	m_bIsAction = src->isaction();
	m_nPower = src->power();
	if (m_nPower)
	{
		m_nIntervalTick = g_nIntervalTime / m_nPower > 0 ? g_nIntervalTime / m_nPower : 1;
	}
	if (m_vehicleWorld)
	{
		auto vehicleworld = static_cast<VehicleWorld*>(m_vehicleWorld);
		Assert(vehicleworld != nullptr);
		int facedir = vehicleworld->getBlockData(m_BlockPos) & 7;
		ActorVehicleAssemble * vehicle = vehicleworld->getActorVehicleAssemble();
		if (vehicle)
		{
			vehicle->changeStraightFlexibleShape(m_BlockPos, facedir, (float)m_nExtendSize);
		}
	}
	if (src->actionmode())
		m_ActionMode = src->actionmode();

	return true;
}

void ContainerArmPrismatic::setBindUnit(ActorMechaUnit *unit)
{
	if(unit) m_BindUnitID = unit->getObjId();
	else m_BindUnitID = 0;
}

ActorMechaUnit *ContainerArmPrismatic::getBindUnit()
{
	if(m_BindUnitID == 0) return NULL;
	else
	{
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_World->getActorMgr());
		if (!actorMgr) return NULL;
		return dynamic_cast<ActorMechaUnit *>(actorMgr->findActorByWID(m_BindUnitID));
	}
}

void ContainerArmPrismatic::setPower(int power) 
{
	m_nPower = power;
	if (m_ActionMode == NORMAL_MODE)
	{
		m_nExtendStepSize = 1;
		if (m_nPower)
		{
			m_nIntervalTick = g_nIntervalTime / m_nPower > 0 ? g_nIntervalTime / m_nPower : 1;
		}
		else
		{
			m_nTicker = 0;
		}
	}
}

void ContainerArmPrismatic::startVehicle()
{
	if (!m_vehicleWorld)
		return;
	int blockid = m_vehicleWorld->getBlock(m_BlockPos).getResID();
	BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
	if (pmtl)
	{
		pmtl->DoOnNotify(m_vehicleWorld, m_BlockPos, blockid);
	}
}

void ContainerArmPrismatic::leaveWorld()
{
	if (m_BindJoint)
		m_BindJoint->setContainer(NULL);
	m_BindJoint = NULL;

	WorldContainer::leaveWorld();
}

void ContainerArmPrismatic::setActionerStep(int nextSize, int tick)
{
	if (m_ActionMode != NORMAL_MODE && m_ActionMode != ACTIONER_MODE_STOP && m_nExtendSize != nextSize)
	{
		m_nToExtendSize = nextSize;
		int realSize = m_nToExtendSize > m_nExtendSize ? m_nToExtendSize - m_nExtendSize : m_nExtendSize - m_nToExtendSize;
		m_nIntervalTick = tick / realSize;
		if (m_nIntervalTick <= 0)
		{
			m_nIntervalTick = 1;
			m_nExtendStepSize = 1;
		}
		m_nTicker = 0;
	}
}

void ContainerArmPrismatic::actionerStopOlder()
{
	m_nToExtendSize = m_nExtendSize;
}

int ContainerArmPrismatic::getActionerCount()
{
	if (!m_vehicleWorld)
	{
		return 0;
	}
	auto vehicleworld = static_cast<VehicleWorld*>(m_vehicleWorld);
	Assert(vehicleworld != nullptr);
	if (!vehicleworld->getActorVehicleAssemble())
		return 0;
	int count = 0;
	vector<WCoord> parts;
	ActorVehicleAssemble* vehicle = vehicleworld->getActorVehicleAssemble();
	vehicle->GetToPartBindLineInfo(m_BlockPos, parts);
	for (auto iter = parts.begin(); iter != parts.end(); iter++)
	{
		if (vehicleworld->getBlockID(*iter) == BLOCK_ACTIONER)
			count++;
	}
	return count;

}

void ContainerArmPrismatic::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);

	registerUpdateTick();
	registerUpdateDisplay();
}

void ContainerArmPrismatic::updateTick()
{
	if (m_World->isRemoteMode() || !m_vehicleWorld)
	{
		return;
	}
	if (m_ActionMode != NORMAL_MODE && m_ActionMode != ACTIONER_MODE_STOP)
	{
		if (m_nToExtendSize != m_nExtendSize)
		{
			m_bIsArrive = false;
			m_nTicker++;
			m_nState = m_nToExtendSize > m_nExtendSize ? 0 : 1;
			if (m_nTicker >= m_nIntervalTick)
			{
				bool notify = false;
				m_nTicker = 0;
				if (0 == m_nState)
				{
					if (15 > m_nExtendSize)
					{
						m_nExtendSize += m_nExtendStepSize;
						notify = true;
					}
				}
				else if (1 == m_nState)
				{
					if (0 < m_nExtendSize)
					{
						m_nExtendSize -= m_nExtendStepSize;
						notify = true;
					}
				}
				m_bIsAction = notify;
				if (notify)
				{
// 					int blockid = m_vehicleWorld->getBlock(m_BlockPos).getResID();
					m_vehicleWorld->notifyBlock(m_BlockPos, m_vehicleWorld->getBlock(m_BlockPos).getResID());

					auto vehicleworld = static_cast<VehicleWorld*>(m_vehicleWorld);
					Assert(vehicleworld != nullptr);
					ActorVehicleAssemble * vehicle = vehicleworld->getActorVehicleAssemble();
					int blockdata = vehicleworld->getBlockData(m_BlockPos);
					int facedir = blockdata & 7;
					WCoord dirPos = NeighborCoord(m_BlockPos, facedir);
					if (vehicleworld->getBlock(dirPos).getResID())
					{
						WCoord dirRealPos = vehicle->getRealWorldPosWithPos(dirPos);
						WCoord realp = vehicle->getRealWorldPosWithPos(m_BlockPos);
						WCoord cPos = dirRealPos - realp;
						int offsetPos = (int)((cPos.length() - (BLOCK_SIZE / 2) * m_nState) / BLOCK_SIZE); //��ֹҺѹ��ͷ������ķ������
						if (abs(offsetPos - m_nExtendSize) > 1/* && m_nIntervalTick > 1*/)
						{
							m_nExtendSize = offsetPos;
						}
					}
				}
			}
		}
		else
		{
			m_bIsArrive = true;
		}
	}
	else if (m_ActionMode == NORMAL_MODE)
	{
		if (m_nPower > 0)
		{
			m_nTicker++;
			if (m_nTicker >= m_nIntervalTick)
			{
				bool notify = false;
				m_nTicker = 0;
				if (0 == m_nState)
				{
					if (15 > m_nExtendSize)
					{
						m_nExtendSize += m_nExtendStepSize;
						notify = true;
					}
				}
				else if (1 == m_nState)
				{
					if (0 < m_nExtendSize)
					{
						m_nExtendSize -= m_nExtendStepSize;
						notify = true;
					}
				}
				m_bIsAction = notify;
				if (m_vehicleWorld && notify)
				{
					int blockid = m_vehicleWorld->getBlock(m_BlockPos).getResID();
					m_vehicleWorld->notifyBlock(m_BlockPos, blockid);

					auto vehicleworld = static_cast<VehicleWorld*>(m_vehicleWorld);
					Assert(vehicleworld != nullptr);

					ActorVehicleAssemble * vehicle = vehicleworld->getActorVehicleAssemble();
					int blockdata = vehicleworld->getBlockData(m_BlockPos);
					int facedir = blockdata & 7;
					WCoord dirPos = NeighborCoord(m_BlockPos, facedir);
					if (vehicleworld->getBlock(dirPos).getResID())
					{
						WCoord dirRealPos = vehicle->getRealWorldPosWithPos(dirPos);
						WCoord realp = vehicle->getRealWorldPosWithPos(m_BlockPos);
						WCoord cPos = dirRealPos - realp;
						int ee = (int)((cPos.length() - (BLOCK_SIZE / 2) * m_nState) / BLOCK_SIZE);
						if (abs(ee - m_nExtendSize) > 1)
						{
							m_nExtendSize = ee;
						}
					}
				}
			}
		}
		else
		{
			m_bIsAction = false;
		}
	}
}

void ContainerArmPrismatic::updateDisplay(float dtime)
{
// 	if (m_World->isRemoteMode())
// 	{
// 		return;
// 	}
// 	if (m_vehicleWorld)
// 	{
// 		if (m_ActionMode == NORMAL_MODE)
// 		{
// 			if (m_nPower > 0/* && m_nState < 2*/)
// 			{
// 				m_nTicker++;
// 				if (m_nTicker >= m_nIntervalTick)
// 				{
// 					bool notify = false;
// 					m_nTicker = 0;
// 					if (0 == m_nState)
// 					{
// 						if (15 > m_nExtendSize)
// 						{
// 							m_nExtendSize++;
// 							notify = true;
// 						}
// 					}
// 					else if (1 == m_nState)
// 					{
// 						if (0 < m_nExtendSize)
// 						{
// 							m_nExtendSize--;
// 							notify = true;
// 						}
// 					}
// 					m_bIsAction = notify;
// 					if (m_vehicleWorld && notify)
// 					{
// 						int blockid = m_vehicleWorld->getBlock(m_BlockPos).getResID();
// 						m_vehicleWorld->notifyBlock(m_BlockPos, blockid);
// 
// 						ActorVehicleAssemble * vehicle = m_vehicleWorld->getActorVehicleAssemble();
// 						int blockdata = m_vehicleWorld->getBlockData(m_BlockPos);
// 						int facedir = blockdata & 7;
// 						WCoord dirPos = NeighborCoord(m_BlockPos, facedir);
// 						if (m_vehicleWorld->getBlock(dirPos).getResID())
// 						{
// 							WCoord dirRealPos = vehicle->getRealWorldPosWithPos(dirPos);
// 							WCoord realp = vehicle->getRealWorldPosWithPos(m_BlockPos);
// 							WCoord cPos = dirRealPos - realp;
// 							int ee = (cPos.length() - BLOCK_SIZE / 2) / BLOCK_SIZE;
// 							if (abs(ee - m_nExtendSize) > 1)
// 							{
// 								m_nExtendSize = ee;
// 							}
// 						}
// 					}
// 				}
// 			}
// 			else
// 			{
// 				m_bIsAction = false;
// 			}
// 		}
// 	}
// 	else
// 	{
// 
// 	}
}
void ContainerArmPrismatic::SetLastActionerPos(WCoord pos)
{
	m_LastActionerPos = pos;
}

void ContainerArmPrismatic::SetActionerPos(WCoord pos)
{
	SetLastActionerPos(m_ActionerPos);
	m_ActionerPos = pos;
}
