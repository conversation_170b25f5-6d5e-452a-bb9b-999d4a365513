#include "ActorYak.h"
#include "ClientPlayer.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "world.h"
#include "navigationpath.h"
#include "SandboxIdDef.h"

ActorYak::ActorYak()
{
	m_foodID = 0;
	m_curMilkCD = 0;
	m_maxMilkCD = 0;
	m_hornCount = 1;
	m_otherYakObjID = 0;
	m_hornGrowCD = 0;
	m_hormGrowMaxCD = 20*60*5; //5分钟
	m_battleStatus = 0;
}

ActorYak::~ActorYak()
{
}

bool ActorYak::interact(ClientActor* player, bool onshift, bool isMobile)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		if (m_BindUIN > 0 && pTempPlayer->getUin() != m_BindUIN)
		{
			return false;
		}
		int toolid = pTempPlayer->getCurToolID();
		if (toolid == BLOCK_THICK_WITHERED)
		{
			auto soundComp = getSoundComponent();
			if (soundComp)
			{
				soundComp->playSound("ent.3912.smooth", 1, 1, 6);
			}

			if (m_curMilkCD <= 0){
				milkStart();
				pTempPlayer->shortcutItemUsed();
			}
			else {
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->clearBodyEffect();
					effectComponent->playBodyEffect(BODYFX_FORBIDDEN);
				}
				auto soundComp = getSoundComponent();
				if (soundComp)
				{
					soundComp->playSound("ent.3912.jet", 1, 1, 6);
				}
			}
			return true;
		}
	}
	return ActorHorse::interact(pTempPlayer);
}

void ActorYak::tick()
{
	if (m_pWorld && !m_pWorld->isRemoteMode()) {
		if (m_curMilkCD > 0) {
			--m_curMilkCD;
		}
		if (m_hornGrowCD > 0) {
			--m_hornGrowCD;
			if (m_hornGrowCD == 0) {
				SetHornCount(1);//长角了
			}
		}
		milkCheckStop();
	}
	
	ActorHorse::tick();
}

bool ActorYak::needUpdateAI()
{
	return true;
}

void ActorYak::SetFoodID(int id)
{
	m_foodID = id;
}

int ActorYak::GetFoodID()
{
	return m_foodID;
}

void ActorYak::SetMaxMilkCD(int id)
{
	m_maxMilkCD = id;
}

int ActorYak::GetMaxMilkCD()
{
	return m_maxMilkCD;
}

void ActorYak::SetHornCount(int count)
{
	m_hornCount = count;
	if (count == 0) {//断角了
		m_hornGrowCD = m_hormGrowMaxCD;
		showSkin("part1", false,true);
	}
	else {
		showSkin("part1", true, true);
	}
}

int ActorYak::GetHornCount()
{
	return m_hornCount;
}

void ActorYak::SetOtherYakID(long long objID)
{
	m_otherYakObjID = objID;
}

long long ActorYak::GetOtherYakID()
{
	return m_otherYakObjID;
}

void ActorYak::SetMaxHornGrowCD(int id)
{
	m_hormGrowMaxCD = id;
}

int ActorYak::GetMaxHornGrowCD()
{
	return m_hormGrowMaxCD;
}

void ActorYak::SetBattleStatu(int pStatu)
{
	m_battleStatus = pStatu;
}

int ActorYak::GetBattleStatu()
{
	return m_battleStatus;
}

flatbuffers::Offset<FBSave::SectionActor> ActorYak::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = ClientMob::saveMob(builder);
	auto mob = FBSave::CreateActorYak(builder, basedata);
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorYak, mob.Union());
}

bool ActorYak::load(const void* srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorYak*>(srcdata);
	if (!ClientMob::load(src->mobdata(), version))
	{
		return false;
	}
	return true;
}

void ActorYak::milkStart()
{
	m_curMilkCD = m_maxMilkCD;
	getNavigator()->clearPathEntity();
	setFlagBit(ACTORFLAG_AI_MILKING, true);
	setQuiteTick(1800);
	setMilkingTimes(3);
	auto effectComponent = getEffectComponent();
	if (effectComponent)
	{
		effectComponent->clearBodyEffect();
		effectComponent->playBodyEffect(BODYFX_MILKING);
	}
}

void ActorYak::milkCheckStop()
{
	if (m_curMilkCD<=0) {
		setFlagBit(ACTORFLAG_AI_MILKING, false);
		setMilkingTimes(0);
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect(BODYFX_MILKING);
		}
	}
}
