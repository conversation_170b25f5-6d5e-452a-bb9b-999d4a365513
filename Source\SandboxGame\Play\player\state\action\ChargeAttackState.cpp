#include "ChargeAttackState.h"
#include "PlayerControl.h"
#include "PlayerStateController.h"
#include "InputInfo.h"
#include "DefManagerProxy.h"
#include "CameraModel.h"
#include "world.h"
#include "EffectManager.h"
#include "PlayerAnimation.h"
#include "backpack.h"
#include "GameCamera.h"
#include "DefManagerProxy.h"
#include "OgreTimer.h"
#include "TouchControl.h"
#include "PCControl.h"
#include "PlayerAttrib.h"
#include "EffectComponent.h"

#include "LuaInterfaceProxy.h"
#include "WeaponSkinMgr.h"
#include "WorldManager.h"

ChargeAttackState::ChargeAttackState(PlayerControl* host) : PlayerState(host), m_EnterStateMark(0),
                                                            m_HasEnterCharge(false)
{
	m_StateID = "ChargeAttack";
	m_ChargeDuration = 1000;
}

ChargeAttackState::~ChargeAttackState()
{

}

void ChargeAttackState::doBeforeEntering()
{
	PlayerAttrib* playerAttrib = m_Host->getPlayerAttrib();
	if (!playerAttrib->isStrengthEnough(GetLuaInterfaceProxy().get_lua_const()->strength_consumption_of_charging_per_second))
	{
		GetLuaInterfaceProxy().showGameTips(1571);
		return;
	}
	if (playerAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
	{
		return;
	}
	m_CurToolID = 0;
	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(m_Host->getCurToolID());
	if(tooldef == NULL) return;
	if (tooldef->Duration > 0 && m_Host->getEquipItemDuration(EQUIP_WEAPON) <= 0)
	{
		GetLuaInterfaceProxy().showGameTips(700007);
		return;
	}
	if(!m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_BEGIN)) return;

	m_CurToolID = m_Host->getCurToolID();
	m_CurShortcut = m_Host->getCurShortcut();

	m_Host->m_pCamera->setBobbing(false);
	m_Host->m_PlayerAnimation->performArrowAttackStart();
	m_EnterStateMark = Rainbow::Timer::getSystemTick();
	createMarkMD5(m_EnterStateMark);
	m_Host->setAtkingTarget(NULL);
	m_HasEnterCharge = false;
 
	//defalut 6 tick to recover
	if (tooldef)
	{
		float accumtime = tooldef->AccumulatorTime;

		if (!g_WorldMgr->isGodMode()) //皮肤加成 - // 蓄力时间缩短X%
		{
			WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
			if (weaponSkinMgr != NULL)
			{
				const char* attrType = "WeaponSkin_System_SkillTime";
				float fpercent = weaponSkinMgr->GetSkinAdditionPercent(attrType, m_Host);
				if (fpercent > 0.0f) accumtime = accumtime * (1 - fpercent);
			}
		}

		m_ChargeDuration = int(accumtime * 1000);
		int accumticks = int(accumtime * 20);
		if(accumticks == 0) accumticks = 10;
		m_Host->getCamera()->setZoomInOut(m_Host->getCamera()->getFov() - 15,  accumticks, 3);
		//m_Host->getCamera()->setZoom(true, tooldef->AccumulatorTime * 20, 6);
	}

	if (m_Host->getRun())
	{
		m_Host->setRun(false);
	}

	m_Host->setAccumulatorState(0);

	processProjectileSkillChargeStart();
}

std::string ChargeAttackState::update(float dtime)
{
	if(m_CurToolID == 0 || m_CurToolID != m_Host->getCurToolID() || m_CurShortcut != m_Host->getCurShortcut() || m_Host->isDead())
	{
		m_Host->setOperate(PLAYEROP_NULL);
		return "ToActionIdle";
	}

	PlayerAttrib* playerAttrib = m_Host->getPlayerAttrib();
	if (playerAttrib && playerAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
	{
		m_Host->setOperate(PLAYEROP_NULL);
		return "ToActionIdle";
	}


	float chargeTime = (float)(getCurTick() - m_EnterStateMark);
	if (IsUseActionEnd()/*m_Host->m_InputInfo->useActionEnd*/)
	{
		const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(m_CurToolID);
		if(!(tooldef->AccumulatorType==1 && chargeTime<m_ChargeDuration))
		{
			//2022318 能量剑类型蓄力反作弊，校验m_EnterStateMark是否被篡改 codeby:liushuxin
			if (!checkMarkMD5(m_EnterStateMark))
			{
				m_Host->setOperate(PLAYEROP_NULL);
				return "ToActionIdle";
			}
			processProjectileSkillChargeEnd();
			m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_END);
			m_Host->m_PlayerAnimation->performArrowAttackShoot();
			m_CurToolID = 0; //不需要cancel了
		}
		else
		{
			m_Host->setOperate(PLAYEROP_NULL);
		}
		return "ToActionIdle";
	}

	if (chargeTime < m_ChargeDuration)
	{
		m_Host->setAccumulatorState(chargeTime/m_ChargeDuration);
	}

	if (!m_HasEnterCharge && chargeTime > m_ChargeDuration)
	{
		m_Host->setAccumulatorState(-1);
		m_HasEnterCharge = true;
		m_Host->m_PlayerAnimation->performArrowAttackReady();
	}

	return "";
}

void ChargeAttackState::doBeforeLeaving()
{
	if(m_CurToolID > 0)
	{
		m_Host->useItem(m_CurToolID, PLAYEROP_STATUS_CANCEL);
		m_Host->m_PlayerAnimation->performChargeCancel(m_CurToolID);
	}

	m_Host->getCamera()->disableZoom();
	m_Host->setAccumulatorState(-1);

	if (m_Host->m_StateController->getMovementState() == "Walk")
	{
		m_Host->m_pCamera->setBobbing(true);
	}

	if (m_Host->m_StateController->getMovementState() == "Idle")
	{
		m_Host->m_PlayerAnimation->performIdle();
	}
	else if (m_Host->m_StateController->getMovementState() == "Walk")
	{
		m_Host->m_PlayerAnimation->performIdle();
	}

	//m_Host->setOperate(PLAYEROP_NULL);
	m_Host->setAccumulatorState(-1);
}

void ChargeAttackState::processProjectileSkillChargeStart()
{
	if (!m_Host)
	{
		return;
	}

	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(m_CurToolID);
	if (tooldef == NULL) return;
	int consumeid = tooldef->ConsumeID == -1 ? tooldef->ID : tooldef->ConsumeID;
	const ItemDef* def = GetDefManagerProxy()->getItemDef(consumeid);
	if (!def)
	{
		return;
	}

	for (int i = 0; i < (int)def->SkillID.size(); i++)
	{
		const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
		if (skilldef && skilldef->SkillType == 2)
		{
			if (skilldef->ChargeEffect.size())
			{
				World* pworld = m_Host->getWorld();
				auto effectComponent = m_Host->getEffectComponent();
				if (effectComponent && pworld)
				{
					if (!pworld->isRemoteMode())
					{
						effectComponent->playBodyEffect((char*)skilldef->ChargeEffect.c_str(), true, skilldef->ChargeEffectTime);
						effectComponent->setBodyEffectScale((char*)skilldef->ChargeEffect.c_str(), skilldef->ChargeEffectSize);
					}
					else
					{
						effectComponent->playBodyEffectClient((char*)skilldef->ChargeEffect.c_str(), skilldef->ChargeEffectSize, skilldef->ChargeEffectTime);
					}
				}
			}
		}
	}
}

void ChargeAttackState::processProjectileSkillChargeEnd()
{
	if (!m_Host)
	{
		return;
	}

	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(m_Host->getCurToolID());
	if (tooldef == NULL) return;
	int consumeid = tooldef->ConsumeID == -1 ? tooldef->ID : tooldef->ConsumeID;
	const ItemDef* def = GetDefManagerProxy()->getItemDef(consumeid);
	if (!def)
	{
		return;
	}

	for (int i = 0; i < (int)def->SkillID.size(); i++)
	{
		const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
		if (skilldef && skilldef->SkillType == 2)
		{
			if (skilldef->ChargeEffect.size())
			{
				World* pworld = m_Host->getWorld();
				auto effectComponent = m_Host->getEffectComponent();
				if (effectComponent && pworld)
				{
					if (!pworld->isRemoteMode())
					{
						effectComponent->stopBodyEffect((char*)skilldef->ChargeEffect.c_str());
					}
					else
					{
						effectComponent->stopBodyEffectClient((char*)skilldef->ChargeEffect.c_str());
					}
				}
			}
		}
	}
}
