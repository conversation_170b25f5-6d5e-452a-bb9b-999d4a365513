#pragma warning( disable : 4482 )
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "AttribTypes.h"
#include "ActorAttrib.h"
//#include "GameEvent.h"
#include "ClientActorLiving.h"
#include "DefManagerProxy.h"
#include "defdata.h"
#include "MobAttrib.h"
#include "ActorHorse.h"
#include "container_backpack.h"
#include "ObserverEvent.h"
#include "ObserverEventManager.h"
#include "ActionAttrStateComponent.h"
#include "DropItemComponent.h"
#include "SoundComponent.h"
#include "ThornBallComponent.h"
#include "SandboxIdDef.h"
#include "WorldManager.h"
#include "ScriptComponent.h"
#include "HPValue.h"
#include "GridContainer.h"
#include "EquipGridContainer.h"
IMPLEMENT_COMPONENTCLASS(MobAttrib)

MobAttrib::MobAttrib() : m_Def(NULL)
{
	// code_by：liya 怪物属性扩充
	//m_Attribs.resize(MAX_MOB_MODATTR);
	m_Attribs.resize(MAX_MOD_ATTRIB);
	//memset(&m_Attribs[0], 0, m_Attribs.size()*sizeof(AttribModified));

	m_Equips = NULL;
	m_Bags = NULL;
	m_Food = 0;
	m_fFoodLmt = -1;
	m_fFoodLmtMax = -1;
	m_bHungryStatus = false;
	m_iDecayExtremisTick = 0;
	m_dropRates.clear();

	m_fAttackPhysical = 0.0f;
	m_fAttackElem = 0.0f;
	m_fDefPhysical = 0.0f;
	m_fDefElem = 0.0f;
}

MobAttrib::~MobAttrib()
{
	SANDBOX_DELETE(m_Equips);
	SANDBOX_DELETE(m_Bags);
	m_dropRates.clear();
}

void MobAttrib::init(const MonsterDef* def)
{
	if (def)
	{
		m_Def = def;

		m_Life = float(def->Life);
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->isEasyMode() && def->LifeIncrease > 0)
			m_Life = float(def->Life * def->LifeIncrease);
#ifdef OLD_ATTRIBUTES
		m_fMaxHP = m_Life;
#else
		mHPValue->SetMaxLimitValue(m_Life);
#endif
		m_Equips = SANDBOX_NEW(PackContainer, MAX_EQUIP_SLOTS, 0);
		if (def->BagNum > 0)
			m_Bags = SANDBOX_NEW(PackContainer, def->BagNum, 0);

		m_Food = (float)(m_Def->Food);

		m_TemperatureDefend = m_Def->TemperatureDefense;

		m_ToughnessBase = def->Toughness;

		applyEquipToughness();
	}
	else
	{
		LOG_SEVERE("def NULL");
	}
}

float MobAttrib::defSpeed2MoveSpeed(int speed)
{
	return float(speed) * VELOCITY_FB / 440.0f;
}

float MobAttrib::getMoveSpeed(int type /* = 0 */)
{
	if (!m_Def)
	{
		LOG_SEVERE("m_Def NULL: type = %d", type);
		return 0.f;
	}
	float speed = defSpeed2MoveSpeed(m_Def->Speed);
#if 0
	if (isNewStatus()) {
		if (type >= Actor_Walk_Speed && type <= Actor_Jump_Speed) {
			if (m_fSpeed[type] < 0.0f) {}
			else {
				speed = m_fSpeed[type] * VELOCITY_FB / 440.0f;
			}
		}
		float tmp = speed;
		speed += getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_WALK_SPEED, tmp);

		if (type >= Actor_Run_Speed && type <= Actor_Swim_Speed) {
			int iAttType = BuffAttrType::BUFFATTRT_RUN_SPEED;
			if (type == Actor_Sneak_Speed)
				iAttType = BuffAttrType::BUFFATTRT_SNEAK_SPEED;
			else if (type == Actor_Swim_Speed)
				iAttType = BuffAttrType::BUFFATTRT_SWIM_SPEED;

			tmp = speed;
			speed += getActorAttValueWithStatus(iAttType, tmp);
		}
	}
	else {
		if (type >= Actor_Walk_Speed && type <= Actor_Jump_Speed) {
			if (m_fSpeed[type] < 0.0f) {}
			else {
				speed = m_fSpeed[type] * VELOCITY_FB / 440.0f;
			}
			speed *= (1.0f + getModAttrib(MODATTR_MOVE_SPEED));
		}
	}
#else
	if (type >= Actor_Walk_Speed && type <= Actor_Jump_Speed) {
		if (m_fSpeed[type] < 0.0f) {}
		else {
			speed = m_fSpeed[type] * VELOCITY_FB / 440.0f;
		}
	}
	float tmp = speed;
	speed += getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_WALK_SPEED, tmp);

	if (type >= Actor_Run_Speed && type <= Actor_Swim_Speed) {
		int iAttType = BuffAttrType::BUFFATTRT_RUN_SPEED;
		if (type == Actor_Sneak_Speed)
			iAttType = BuffAttrType::BUFFATTRT_SNEAK_SPEED;
		else if (type == Actor_Swim_Speed)
			iAttType = BuffAttrType::BUFFATTRT_SWIM_SPEED;

		tmp = speed;
		speed += getActorAttValueWithStatus(iAttType, tmp);
	}
#endif

	if (speed < 0.0f) { speed = 0.0f; }

	return speed;
	//return defSpeed2MoveSpeed(m_Def->Speed) * (1.0f + getModAttrib(MODATTR_MOVE_SPEED));
}

float MobAttrib::getMoveBaseSpeed(int type /* = 0 */)
{
	if (!m_Def)
	{
		LOG_SEVERE("m_Def NULL: type = %d", type);
		return 0.f;
	}
	float speed = (float)(m_Def->Speed);
	if (type >= Actor_Walk_Speed && type <= Actor_Jump_Speed) {
		if (m_fSpeed[type] < 0.0f) {}
		else {
			speed = m_fSpeed[type];
		}
	}
	return speed;
}

void MobAttrib::setMoveBaseSpeed(float speed, int type /* = 0 */)
{
	if (type >= Actor_Walk_Speed && type <= Actor_Jump_Speed)
	{
		m_fSpeed[type] = speed;
	}
}

void MobAttrib::setFoodLmtAndMax(float fFoodLmt, float fFoodLmtMax)
{
	m_fFoodLmt = fFoodLmt;
	m_fFoodLmtMax = fFoodLmtMax;

	calculateHungryStatus();
}

float MobAttrib::getMaxFood()
{
	if (!m_Def)
	{
		LOG_SEVERE("m_Def NULL");
		return 0.f;
	}

	return (float)m_Def->Food;
}

void MobAttrib::setAttackPhysical(float val)
{
	m_fAttackPhysical = val;
}

void MobAttrib::setAttackElem(float val)
{
	m_fAttackElem = val;
}

void MobAttrib::setDefPhysical(float val)
{
	m_fDefPhysical = val;
}

void MobAttrib::setDefElem(float val)
{
	m_fDefElem = val;
}

float MobAttrib::getAttackPhysical() const
{
	return m_fAttackPhysical;
}

float MobAttrib::getAttackElem() const
{
	return m_fAttackElem;
}

float MobAttrib::getDefPhysical() const
{
	return m_fDefPhysical;
}

float MobAttrib::getDefElem() const
{
	return m_fDefElem;
}

void MobAttrib::onDie()
{
	auto ActionAttrStateComp = m_OwnerActor->getActionAttrStateComponent();
	if (m_OwnerActor->getWorld() && !m_OwnerActor->getWorld()->isRemoteMode() && ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_DEATHDROPITEM) && m_OwnerActor->getObjType() != OBJ_TYPE_VILLAGER)
	{
		dropItem(m_OwnerActor->isBurning());
		if (!canDropItem()) {
			dropEquipItems();
			dropBagsItems();
		}

		dropThornBallItems();
		/*	if (GetClientInfoProxy()->getFcmRate() != 0)
		{
		dropItem(m_OwnerActor->isBurning());
		dropEquipItems();
		}
		else
		{
		GetGameEventQue().postInfoTips(3692);
		}*/
	}
	LivingAttrib::onDie();
}

int MobAttrib::getEquipItem(EQUIP_SLOT_TYPE t)
{
	BackPackGrid* grid = getEquipGrid(t);
	if (grid && grid->def)
	{
		return grid->def->ID;
	}
	else return 0;
}

int MobAttrib::damageEquipItem(EQUIP_SLOT_TYPE t, int damage)
{
	BackPackGrid* grid = getEquipGrid(t);
	if (grid && grid->def)
	{
		if (grid->addDuration(-damage) <= 0)
		{
			int itemid = grid->def->ID;
			equip(t, nullptr);

			//判断耙耐久
			ActorHorse* pHorse = dynamic_cast<ActorHorse*>(m_OwnerActor);
			if (pHorse && pHorse->m_Def && (pHorse->m_Def->ID == 3891) && (EQUIP_LEGGING == t))
			{
				pHorse->setRakeToolLiving(false);

				//销毁耙装备
				BackPackGrid* PackGrid = pHorse->index2Grid(HORSE_EQUIP_INDEX + 2);
				assert(PackGrid);
				if (PackGrid)
				{
					PackGrid->reset(HORSE_EQUIP_INDEX + 2);
				}
			}

			// 道具被消耗
			ObserverEvent_ActorItem obevent(m_OwnerActor->getObjId(), itemid, 1);
			GetObserverEventManager().OnTriggerEvent("Item.Damage", &obevent);
			return itemid;
		}
	}
	return 0;
}

void MobAttrib::dropOneEquipItem(EQUIP_SLOT_TYPE slot)
{
	BackPackGrid* itemgrid = getEquipGrid(slot);
	if (itemgrid && !itemgrid->isEmpty())
	{
		if (itemgrid->getUserDataInt() == 0 || itemgrid->getUserDataInt() == 1 && GenRandomInt(100) < 10)
		{
			auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();
			if (dropComponent)
			{
				dropComponent->dropItem(itemgrid);
			}
			SetBackPackGrid(*itemgrid, 0, 0);
		}
	}
}

void MobAttrib::dropEquipItems()
{
	if (m_Equips || m_OwnerActor->getEquipGridContainer())
	{
		for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
		{
			int itemID = getEquipItem((EQUIP_SLOT_TYPE)i);
			if (11810 == itemID || 12069 == itemID || 12068 == itemID)continue;
			dropOneEquipItem((EQUIP_SLOT_TYPE)i);

			auto horse = dynamic_cast<ActorHorse*>(m_OwnerActor);
			if (horse) {
				horse->clearEquip(i);
			}
		}
	}
}

void MobAttrib::dropBagsItems()
{
	GridContainer* grid = m_OwnerActor->getGridContainer();
	if (grid)
	{
		grid->dropItems();
	}
	else
	{
		if (m_Bags)
		{
			auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();
			for (int i = 0; i < (int)m_Bags->m_Grids.size(); i++)
			{
				if (!m_Bags->m_Grids[i].isEmpty())
				{
					if (dropComponent)
					{
						dropComponent->dropItem(&m_Bags->m_Grids[i]);
					}
					SetBackPackGrid(m_Bags->m_Grids[i], 0, 0);
				}
			}
		}
	}
}

void MobAttrib::dropSkinningsItems()
{
	if (!m_Def)
	{
		LOG_SEVERE("m_Def NULL in dropSkinningsItems");
		return;
	}

	GetDefManagerProxy()->checkCrcCode(CRCCODE_MONSTER);

	auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();
	if (!dropComponent)
	{
		LOG_SEVERE("DropItemComponent is NULL in dropSkinningsItems");
		return;
	}

	if (m_Def->SkinningDropItems.empty())
	{
		LOG_INFO("No skinning drop items configured for monster %d", m_Def->ID);
		return;
	}

	dropComponent->GenDropPos(m_OwnerActor->getPosition(), static_cast<int>(m_Def->SkinningDropItems.size()));

	for (const auto& skinningDrop : m_Def->SkinningDropItems)
	{
		if (skinningDrop.id <= 0)
			continue;

		if (GenRandomInt(10000) < skinningDrop.odds)
		{
			dropComponent->dropItem(skinningDrop.id, skinningDrop.num);
			LOG_INFO("Skinning drop: itemid=%d, num=%d, odds=%d", skinningDrop.id, skinningDrop.num, skinningDrop.odds);
		}
	}
}

void MobAttrib::dropThornBallItems()
{
	auto thornComponent = m_OwnerActor->getThornBallComponent();
	if (thornComponent)
	{
		int num = thornComponent->getThornAnchorNum();
		thornComponent->dropThornBall(num);
	}
}

BackPackGrid* MobAttrib::getEquipGrid(EQUIP_SLOT_TYPE t)
{
	EquipGridContainer* equipContainer = m_OwnerActor->getEquipGridContainer();
	if (equipContainer)
	{
		return equipContainer->index2Grid(t);
	}
	else
	{
		if (m_Equips == NULL) return NULL;

		return m_Equips->index2Grid(t);
	}
}

void MobAttrib::equip(EQUIP_SLOT_TYPE t, int itemid, int durable, int toughness, int maxdurable)
{
	EquipGridContainer* equipContainer = m_OwnerActor->getEquipGridContainer();
	if (equipContainer)
	{
		BackPackGrid* grid = equipContainer->index2Grid(t);
		if (grid == NULL) return;

		SetBackPackGrid(*grid, itemid, 1, durable, toughness);
		grid->setMaxDuration(maxdurable);
		equipContainer->equip(t);
		applyEquipToughness();
	}
	else
	{
		if (m_Equips == NULL) m_Equips = SANDBOX_NEW(PackContainer, MAX_EQUIP_SLOTS, 0);
		BackPackGrid* grid = m_Equips->index2Grid(t);
		if (grid == NULL) return;
		//const ToolDef *tool = GetDefManagerProxy()->getToolDef(itemid);
		//if (tool)
		//{
		//	if (durable < 0) durable = tool->Duration;
		//}
		SetBackPackGrid(*grid, itemid, 1, durable, toughness);
		grid->setMaxDuration(maxdurable);

		if (m_OwnerActor != NULL) {
			applyEquips(m_OwnerActor->getBody(), t);
		}

		applyEquipToughness();
	}
}

void MobAttrib::equip(EQUIP_SLOT_TYPE t, BackPackGrid* itemgrid)
{
	if (itemgrid == nullptr || itemgrid->def == nullptr)
	{
		equip(t, 0, -1, 0, 0);
		return;
	}

	equip(t, itemgrid->def->ID, itemgrid->getDuration(), itemgrid->getToughness(), itemgrid->getMaxDuration());

	BackPackGrid* dest = getEquipGrid(t);
	if (dest == NULL) return;
	dest->loadRunesAndEnchants(itemgrid);//复制符文/附魔信息 code by:tanzhenyu
	dest->loadDataComponent(itemgrid);
	applyEquipToughness();
}

void MobAttrib::setBagItem(int index, BackPackGrid* itemgrid)
{
	if (!itemgrid)
	{
		return;
	}
	GridContainer* grids = m_OwnerActor->getGridContainer();
	if (grids)
	{
		BackPackGrid* grid = grids->index2Grid(index);
		if (grid)
		{
			SetBackPackGrid(*grid, itemgrid->getItemID(), itemgrid->getNum(), itemgrid->getDuration(), itemgrid->getToughness(), itemgrid->userdata, 1, 0, itemgrid->userdata_str.c_str());
			grid->loadRunesAndEnchants(itemgrid);//复制符文/附魔信息 code by:tanzhenyu
			grid->loadDataComponent(itemgrid);
		}
	}
	else
	{
		if (!m_Bags)
		{
			return;
		}
		BackPackGrid* grid = m_Bags->index2Grid(index);
		if (grid)
		{
			SetBackPackGrid(*grid, itemgrid->getItemID(), itemgrid->getNum(), itemgrid->getDuration(), itemgrid->getToughness(), itemgrid->userdata, 1, 0, itemgrid->userdata_str.c_str());
			grid->loadRunesAndEnchants(itemgrid);//复制符文/附魔信息 code by:tanzhenyu
			grid->loadDataComponent(itemgrid);
		}
	}
}

int MobAttrib::getFoodReduce()
{
	if (!m_Def)
	{
		LOG_SEVERE("m_Def NULL");
		return -1;
	}

	return m_Def->FoodReduce;
}

void MobAttrib::applyEquipToughness()
{
	int itemid = getEquipItem(EQUIP_WEAPON);
	applyEquipToughness(itemid);
}
void  MobAttrib::applyEquipToughness(int itemid)
{

	int toughnessMax = m_ToughnessBase;
	const ToolDef* def = GetDefManagerProxy()->getToolDef(itemid);
	if (def)
	{
		toughnessMax = m_ToughnessBase + def->Toughness;
	}
	if (m_ToughnessTotal == m_ToughnessTotalMax)
	{
		m_ToughnessTotal = toughnessMax;
	}
	else
	{
		if (m_ToughnessTotal > m_ToughnessTotalMax)
		{
			m_ToughnessTotal = m_ToughnessTotalMax;
		}
	}
	m_ToughnessTotalMax = toughnessMax;
}


void MobAttrib::recoverToughness()
{
	if (m_ToughnessTotal < m_ToughnessTotalMax)
	{
		if (m_Def)
			addToughnessTotal(m_Def->ToughnessRecover);
	}
}

void MobAttrib::recoverAllToughness()
{
	m_ToughnessTotal = m_ToughnessTotalMax;
}

void MobAttrib::addFood(float val)
{
	m_Food += val;

	if ((val > 0) && (m_fFoodLmtMax != -1) && m_bHungryStatus && (m_Food > m_fFoodLmtMax))
	{
		m_bHungryStatus = false;
	}
	else if ((val < 0) && (m_fFoodLmt != -1) && !m_bHungryStatus && (m_Food <= m_fFoodLmt))
	{
		m_bHungryStatus = true;
	}
}

static int GetBurnDropItem(int itemid)
{
	int bakeid = GetDefManagerProxy()->getItemBakeTo(itemid);
	if (bakeid > 0)
	{
		if (GenRandomInt(1, 100) <= 10)
		{
			return 12497;
		}
		return bakeid;
	}
	return itemid;
}

void MobAttrib::dropItem(bool burn)
{
	if (!m_Def)
	{
		LOG_SEVERE("m_Def NULL");
		return;
	}

	GetDefManagerProxy()->checkCrcCode(CRCCODE_MONSTER);

	/*if (burn)
	{
		if (GenRandomInt(1, 10000) <= m_Def->BurnDropItemOdds)
		{
			auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();
			if (dropComponent)
			{
				dropComponent->dropItem(m_Def->BurnDropItem, 1);
			}
		}
	}*/
	auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();

	if (nullptr != dropComponent)
	{
		if (!m_Def->DropItemsOddsNew.empty())
		{
			// 检查掉落数量
			int dropnum = 0;
			int randval = GenRandomInt(ODDS_TOTAL);
			// v1为掉落数量， v2为概率  基数为10000  ODDS_TOTAL
			for (auto& conf : m_Def->DropItemsOddsNew)
			{
				if (randval < conf.v2)
				{
					dropnum = conf.v1;
					break;
				}
				else
				{
					randval -= conf.v2;
				}
			}
			if (dropnum > 0)
			{
				int totalWeight = 0;
				// v1 为道具id  v2为weight权重
				for (auto& conf : m_Def->DropItemsNew)
				{
					totalWeight += conf.v2;
				}
				dropComponent->GenDropPos(m_OwnerActor->getPosition(), dropnum);
				for (int i = 0; i < dropnum; ++i)
				{
					randval = GenRandomInt(totalWeight);
					int itemid = 0;
					for (auto& conf : m_Def->DropItemsNew)
					{
						if (randval <= conf.v2)
						{
							itemid = conf.v1;
							break;
						}
						else
						{
							randval -= conf.v2;
						}
					}
					if (itemid > 0)
					{
						dropComponent->dropItem(itemid, 1);
					}
				}
			}
		}
		// for (int idrop = 0; idrop < ClientMob::m_DropItemCallCount; idrop++)
		// {
		// 	for (int i = 0; i < MAX_MONSTER_DROPITEM; i++)
		// 	{
		// 		int itemid = m_Def->DropItem[i];
		// 		if (itemid == 0) continue;
		// 		int odds = m_Def->DropItemOdds[i];
		// 		int itemnum = max(m_Def->DropItemNum[i], 1);
		//
		// 		while (odds > 0)
		// 		{
		// 			if (GenRandomInt(ODDS_TOTAL) < odds)
		// 			{
		// 				if (itemid == BLOCK_WOOL)
		// 				{
		// 					ClientMob* mob = dynamic_cast<ClientMob*>(m_OwnerActor);
		// 					int blockid, userdata;
		//
		// 					MINIW::ScriptVM::game()->callFunction("Color2BlockInfo", "ii>ii", mob->getColor(), BLOCK_WOOL, &blockid, &userdata);
		//
		// 					itemid = blockid;
		// 					if (userdata)
		// 					{
		// 						MINIW::ScriptVM::game()->callFunction("GetDefaultBlockId", "i>i", blockid, &itemid);
		// 					}
		// 				}
		//
		// 				if (burn)
		// 				{
		// 					itemid = GetBurnDropItem(itemid);
		// 				}
		// 				dropComponent->dropItem(itemid, itemnum);
		// 			}
		// 			odds -= ODDS_TOTAL;
		// 		}
		// 	}
		// }
	}

	bool dropexp = m_Def->DropExp > 0 && GenRandomInt(10000) < m_Def->DropExpOdds;
	if (!m_OwnerActor->OnHurtByActor())
	{
		dropexp = false;
	}

	if (dropexp)
	{
		//CollideAABB box;
		//m_OwnerActor->getCollideBox(box);
		//ActorExpOrb::SpawnExpOrb(m_OwnerActor->getWorld(), m_Def->DropExp, box.pos, box.dim);

		//怪物被击杀, 直接给击杀者加经验
		if (m_OwnerActor)
		{
			ClientActor* pTarget = m_OwnerActor->getBeHurtTarget();
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(pTarget);
			if (player)
			{
				int nLastAttakedTime = m_OwnerActor->getBeHurtTimer();
				int nCurTime = m_OwnerActor->m_LiveTicks;
				int nMaxInterval = 5 * 20;

				if (nLastAttakedTime > 0 && nCurTime > 0 && nCurTime - nLastAttakedTime <= nMaxInterval)
				{
					//OBJ_TYPE_DRAGON 和 OBJ_TYPE_GIANT 类型的召唤怪 不加经验
					int dropext = m_Def->DropExp;
					if ((m_OwnerActor->getObjType() == OBJ_TYPE_DRAGON || m_OwnerActor->getObjType() == OBJ_TYPE_GIANT) &&
						m_OwnerActor->getMasterObjId() > 0)
					{
						dropext = 0;
					}
					player->OnGainedExp(dropext);
				}
			}
		}
	}
}

float MobAttrib::getBasicAttackPoint(ATTACK_TYPE atktype)
{
	if (!m_Def)
	{
		LOG_SEVERE("m_Def NULL: atktype = %d", atktype);
		return 0.f;
	}

	float coefficient = 1.0f;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isEasyMode() && m_Def->AttackIncrease > 0)
		coefficient = m_Def->AttackIncrease;

	if (atktype <= MAX_MAGIC_ATTACK || atktype == PHYSICS_ATTACK)
	{
		float atkpoints = 0.f;
		const int index = AtkType2ArmorIndex(atktype);
		// 物理攻击
		float physicsAtkpoints = m_fAttackPhysical > 0.f ? m_fAttackPhysical : m_Def->Attacks[MAX_PHYSICS_ATTACK];
		if (atktype == PHYSICS_ATTACK)
		{
			atkpoints = physicsAtkpoints;
		}
		else if (atktype < MAX_PHYSICS_ATTACK)
		{
			// 近战、远程
			if (atktype == ATTACK_TYPE::ATTACK_PUNCH || atktype == ATTACK_TYPE::ATTACK_RANGE)
			{
				float fBaseAttack = m_fBaseAttack[atktype == ATTACK_TYPE::ATTACK_PUNCH ? 0 : 1];
				atkpoints = (fBaseAttack < 0.0f ? (physicsAtkpoints + m_Def->Attacks[index]) : (fBaseAttack + physicsAtkpoints));
			}
			// 爆炸
			else
			{
				atkpoints = physicsAtkpoints + m_Def->Attacks[index];
			}
		}
		else
		{
			// 元素攻击
			if (atktype == MAX_MAGIC_ATTACK)
				atkpoints = (m_fAttackElem > 0.f ? m_fAttackElem : m_Def->Attacks[index]);
			else if (atktype < MAX_MAGIC_ATTACK)
			{
				atkpoints = m_Def->Attacks[index];	//燃烧、毒素、混乱
			}
		}

		return atkpoints * coefficient;
	}

	return 0.f;
}

float MobAttrib::getBasicArmorPoint(ATTACK_TYPE atktype)
{
	if (!m_Def)
	{
		LOG_SEVERE("m_Def NULL: atktype = %d", atktype);
		return 0.f;
	}

	float coefficient = 1.0f;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isEasyMode() && m_Def->ArmorIncrease > 0)
		coefficient = m_Def->ArmorIncrease;

	if (atktype <= MAX_MAGIC_ATTACK || atktype == PHYSICS_ATTACK)
	{
		float armor = 0.f;
		const int index = AtkType2ArmorIndex(atktype);
		// 物理防御
		float physicsArmor = m_fDefPhysical > 0.f ? m_fDefPhysical : m_Def->Armors[MAX_PHYSICS_ATTACK];
		if (atktype == PHYSICS_ATTACK)
		{
			armor = physicsArmor;
		}
		else if (atktype < MAX_PHYSICS_ATTACK)
		{
			// 近战、远程
			if (atktype == ATTACK_TYPE::ATTACK_PUNCH || atktype == ATTACK_TYPE::ATTACK_RANGE)
			{
				float fBaseArmor = m_fBaseArmor[atktype == ATTACK_TYPE::ATTACK_PUNCH ? 0 : 1];
				armor = (fBaseArmor < 0.0f ? (physicsArmor + m_Def->Armors[index]) : (fBaseArmor + physicsArmor));
			}
			// 爆炸
			else
			{
				armor = physicsArmor + m_Def->Armors[index];
			}
		}
		else
		{
			// 魔法防御
			float magicArmor = m_fDefElem > 0.f ? m_fDefElem : m_Def->Armors[MAX_MAGIC_ATTACK + 1];
			if (atktype == MAX_MAGIC_ATTACK)
				armor = magicArmor;
			else if (atktype < MAX_MAGIC_ATTACK)
			{
				armor = magicArmor + m_Def->Armors[index];	//燃烧、毒素、混乱
			}
		}

		return armor * coefficient;
	}

	return 0.f;
}

void MobAttrib::calculateHungryStatus()
{
	if ((m_fFoodLmtMax != -1) && (m_Food > m_fFoodLmtMax))
	{
		m_bHungryStatus = false;
	}
	else if ((m_fFoodLmt != -1) && (m_Food <= m_fFoodLmt))
	{
		m_bHungryStatus = true;
	}
}

PackContainer* MobAttrib::getBags()
{
	return m_Bags;
}

void MobAttrib::onCurToolUsed(int num)
{
	BackPackGrid* grid = nullptr;
	EquipGridContainer* equipContainer = m_OwnerActor->getEquipGridContainer();
	if (equipContainer)
	{
		grid = equipContainer->index2Grid(EQUIP_WEAPON);
	}
	else
	{
		grid = m_Equips->index2Grid(EQUIP_WEAPON);
	}

	if (grid->def == NULL) return;

	int itemId = grid->def->ID;

	//对于不可堆叠的的工具类，消耗耐久
	if (grid->def->StackMax <= 1 && grid->getDuration() > 0)
	{
		if (GetWorldManagerPtr() && !GetWorldManagerPtr()->isGodMode() && grid->addDuration(num, true) <= 0)
		{
			auto sound = m_OwnerActor->getSoundComponent();
			if (sound)
			{
				sound->playSound("misc.break", 1.0f, 1.0f);
			}

			grid->clear();

			if (m_OwnerActor->getBody())
			{
				applyEquips(m_OwnerActor->getBody(), EQUIP_WEAPON);
			}

			applyEquipToughness();

			// 道具被消耗
			ObserverEvent_ActorItem obevent(m_OwnerActor->getObjId(), itemId, 1);
			GetObserverEventManager().OnTriggerEvent("Item.Damage", &obevent);

			//冰怪冰棒消失
			ClientMob* mob = dynamic_cast<ClientMob*>(m_OwnerActor);
			if (mob && mob->getMonsterId() == 3915 && itemId == 12316) {
				int dp = 40;
				auto att = mob->getAttrib();
				att->setMaxHP(att->getMaxHP() - dp);
				int hp = att->getHP() > att->getMaxHP() ? att->getMaxHP() : att->getHP();
				att->setHP(hp);
				mob->setCustomScale(1.5f);
			}
		}
	}
	else
	{
		grid->addNum(num);
		if (grid->getNum() <= 0)
		{
			grid->clear();

			if (m_OwnerActor->getBody())
			{
				applyEquips(m_OwnerActor->getBody(), EQUIP_WEAPON);
			}
		}
	}
}

BackPackGrid* MobAttrib::findItem(int itemid)
{
	GridContainer* grid = m_OwnerActor->getGridContainer();
	if (grid)
	{
		return grid->getGridByItemID(itemid);
	}
	else
	{
		return m_Bags ? m_Bags->getGridByItemID(itemid) : nullptr;
	}
	return nullptr;
}

void MobAttrib::resetDiffModeAttr()
{
	if (!m_Def)
		return;

	auto def = m_Def;
#ifdef OLD_ATTRIBUTES
	m_fMaxHP = float(def->Life);
	//switch diff mode
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isEasyMode())
	{
		if (def->LifeIncrease > 0)
		{
			m_fMaxHP *= def->LifeIncrease;
		}
		m_Life = Rainbow::Clamp(float(m_Life), 0.f, m_fMaxHP);
	}
	else
	{
		m_Life = m_fMaxHP; //recover all hp
	}
#else
	mHPValue->SetMaxLimitValue(float(def->Life));
	//switch diff mode
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isEasyMode())
	{
		if (def->LifeIncrease > 0)
		{
			mHPValue->SetMaxLimitValue(mHPValue->GetMaxLimitValue() * def->LifeIncrease);
		}
		m_Life = Rainbow::Clamp(float(m_Life), 0.f, mHPValue->GetMaxLimitValue());
	}
	else
	{
		m_Life = mHPValue->GetMaxLimitValue(); //recover all hp
	}
#endif
	//其他
}

void MobAttrib::setDropItem(int rate, int itemId, int num)
{
	if (num < 0) num = 0;
	if (num > 100) num = 100;
	if (rate < 0) rate = 0;
	if (rate > 100) rate = 100;

	DropItem item;
	item.itemId = itemId;
	item.itemNum = num;
	item.dropRate = rate;
	m_dropRates[itemId] = item;
}

float MobAttrib::getAttackBaseLua(int attacktype)
{
	return getBasicAttackPoint((ATTACK_TYPE)attacktype);
}

float MobAttrib::getArmorBaseLua(int attacktype)
{
	return getBasicArmorPoint((ATTACK_TYPE)attacktype);
}

bool MobAttrib::canDropItem()
{
	ClientMob* mob = dynamic_cast<ClientMob*>(m_OwnerActor);
	if (mob == nullptr)
	{
		return false;
	}
	if (mob->isLoadAiEditNode())
	{
		if (m_dropRates.size() > 0)
		{
			auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();
			if (dropComponent)
			{
				for (auto it = m_dropRates.begin(); it != m_dropRates.end(); it++)
				{
					int itemId = it->second.itemId;
					int itemNum = it->second.itemNum;
					int rate = it->second.dropRate;
					int randNum = GenRandomInt(100) + 1;
					if (itemNum > 0 && randNum <= rate)
					{
						dropComponent->dropItem(itemId, itemNum);
					}
				}
			}
		}

		return true;
	}

	return false;
}