
#ifndef __VILLAGERATTRIB_H__
#define __VILLAGERATTRIB_H__

#include "ActorTypes.h"
#include <vector>
#include <map>
#include <functional>
#include "world_types.h"
#include "MobAttrib.h"
#include "LivingAttrib.h"
#include "ActorAttrib.h"

struct MonsterDef;

class VillagerAttrib : public MobAttrib {//tolua_exports
public:
	DECLARE_COMPONENTCLASS(VillagerAttrib)

	//tolua_begin
	VillagerAttrib();

	virtual void init(const MonsterDef *def);
	virtual void tick() override; //not_auto_export_to_lua
	virtual void addHP(float hp, bool overflowable = false) override;
	void addHugger(short val);
	/*{
		m_iHunger += val;
	}*/

	void setHugger(short val);
	/*{
		m_iHunger = val;
	}*/

	short getHugger()
	{
		return m_iHunger;
	}

	void addHuggerStatus(short val)
	{
		m_iHungerstatus += val;
		if (m_iHungerstatus >= HUNGER_MAX_LEVEL)
			m_iHungerstatus = HUNGER_MAX_LEVEL - 1;
	}

	void setHuggerStatus(short val)
	{
		m_iHungerstatus = val;
		if (m_iHungerstatus >= HUNGER_MAX_LEVEL)
			m_iHungerstatus = HUNGER_MAX_LEVEL - 1;
	}

	short getHuggerStatus()
	{
		return m_iHungerstatus;
	}

	void addFatigue(short val)
	{
		m_iFatigue += val;
		if (m_iFatigue >= FATIGUE_MAX_LEVEL)
			m_iFatigue = FATIGUE_MAX_LEVEL - 1;
	}

	void setFatigue(short val)
	{
		m_iFatigue = val;
		if (m_iFatigue >= FATIGUE_MAX_LEVEL)
			m_iFatigue = FATIGUE_MAX_LEVEL - 1;
	}

	short getFatigue()
	{
		return m_iFatigue;
	}

	void addExtremisVal(short val);

	bool isExtremis()
	{
		return m_iExtremisVal > 0 && m_iExtremisVal < m_iMaxExtremisVal;
	}

	void setExtremisVal(short val);

	short getExtremisVal()
	{
		return m_iExtremisVal;
	}

	void setDisposition(short disposition)
	{
		m_iDisposition = disposition;
	}

	short getDisposition()
	{
		return m_iDisposition;
	}

	void addFavor(short val)
	{
		m_iFavor += val;
		if (m_iFavor > m_iMaxFavor)
			m_iFavor = m_iMaxFavor;
		else if (m_iFavor < 0)
			m_iFavor = 0;
	}

	void setFavor(short val)
	{
		m_iFavor = val;
		if (m_iFavor > m_iMaxFavor)
			m_iFavor = m_iMaxFavor;
		else if (m_iFavor < 0)
			m_iFavor = 0;
	}

	short getFavor()
	{
		return m_iFavor;
	}

	void setSpeedOfBreakingBlock(float speed)
	{
		m_speedOfBreakingBlock = speed;
	}

	float getSpeadOfBreakingBlock()
	{
		return m_speedOfBreakingBlock;
	}

	void setWakeupTime(float hours)
	{
		m_iWakeupTime = (short)(hours * TICKS_ONEDAY / 24.0f);
		if (m_iWakeupTime >= TICKS_ONEDAY)
			m_iWakeupTime = 0;
	}

	float getWakeupTime()
	{
		return m_iWakeupTime * 24.0f / TICKS_ONEDAY;
	}

	void setSleepTime(float hours)
	{
		m_iSleepTime = (short)(hours * TICKS_ONEDAY / 24.0f);
		if (m_iSleepTime >= TICKS_ONEDAY)
			m_iSleepTime = 0;
	}

	float getSleepTime()
	{
		return m_iSleepTime * 24.0f / TICKS_ONEDAY;
	}

	void setStayupTime(float hours)
	{
		m_iStayupTime = (short)(hours * TICKS_ONEDAY / 24.0f);
		if (m_iStayupTime >= TICKS_ONEDAY)
			m_iStayupTime = 0;
	}

	float getStayupTime()
	{
		return m_iStayupTime * 24.0f / TICKS_ONEDAY;
	}

	void setStayingUp(bool b)
	{
		m_bStayingUp = b;
	}

	bool isStayingUp()
	{
		return m_bStayingUp;
	}

	virtual bool isDead(); //not_auto_export_to_lua
	void onChangeExtremisState(bool isextremis, int oldstage=0);
	int getExtremisStage();
	void damageArmor(float points, ClientActor *attacker);
	//tolua_begin
public:
	//tolua_begin
	short m_iMaxExtremisVal;
	short m_iMaxFavor;
	//tolua_end
private:
	short m_iFatigue; //疲劳状态
	short m_iExtremisVal; //濒死值
	short m_iHunger;	//饥饿值
	short m_iHungerstatus; //饥饿状态
	short m_iDisposition; //性格
	short m_iFavor;		//好感值
	float m_speedOfBreakingBlock;	//破坏方块速度，小数，1是百分百
	short m_iWakeupTime;	//起床时间
	short m_iSleepTime;		//睡觉时间
	short m_iStayupTime;	//熬夜时间
	bool m_bStayingUp;		//是否在熬夜
	short m_iDecayExtremisTick;	//濒死值衰减时间间隔

};//tolua_exports


#endif
