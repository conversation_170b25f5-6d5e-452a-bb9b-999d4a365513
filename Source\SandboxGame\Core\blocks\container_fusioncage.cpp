
#include "ClientItem.h"
#include "container_fusioncage.h"
#include "world.h"
#include "BaseItemMesh.h"
#include "BlockScene.h"
#include "special_blockid.h"
#include "DefManagerProxy.h"
#include "ActorVehicleAssemble.h"
#include "block_tickmgr.h"
#include "BlockMaterialMgr.h"
#include "BlockVisualizer.h"
#include "ClientPipelineActor.h"
#include "EffectManager.h"
#include "VehicleWorld.h"
#include "LuaInterfaceProxy.h"
#include "EffectParticle.h"
#include "Text3D/StoveProgressBar3D.h"
#include "Text3D/ItemImage3D.h"
#include "ItemIconManager.h"
#include "chunk.h"
using namespace MINIW;
using namespace Rainbow;

static float scale = 2.0f;
static const WCoord effPlaceOne[4] = {
	WCoord(10, 50, 18),
	<PERSON>oord(-10, 50, -18),
	<PERSON>oord(-18, 50, 10),
	<PERSON>oord(18, 50, -10)
};
static const WCoord effPlaceTwo[4] = {
	WCoord(10, 50, -118),
	<PERSON>oord(-10, 50, 118),
	<PERSON>oord(118, 50, 10),
	WCoord(-118, 50, -10)
};
static const WCoord effPlaceThird[4] = {
	WCoord(-30, 0, -40),
	WCoord(20, 0, 60),
	WCoord(40, 0, -20),
	WCoord(-60, 0, 30)
};

static const WorldPos firstIconPos[4] = {
	WorldPos(120,-700,180),
	WorldPos(-120,-700,-180),
	WorldPos(-180,-700,120),
	WorldPos(180,-700,-120)
};

static const WorldPos secondIconPos[4] = {
	WorldPos(120,-700,-1170),
	WorldPos(-120,-700,1170),
	WorldPos(1170,-700,120),
	WorldPos(-1170,-700,-120)
};

static const WorldPos thirdIconPos[4] = {
	WorldPos(-200,-500,-500),
	WorldPos(200,-500,500),
	WorldPos(500,-500,-200),
	WorldPos(-500,-500,200)
};
ContainerFusionCage::ContainerFusionCage() : WorldContainer(WCoord(0,0,0), 0), m_ItemMesh_1(NULL), m_ItemMesh_2(NULL), m_ItemMesh_3(NULL)
{
	m_ItemData_1.clear();
	m_ItemData_2.clear();
	m_ItemData_3.clear();
	FusionCage_value = 0;
	m_doneTime = 0;
	m_doneValue = 0;
	m_MaxValue = 50;
	RotationVal = 0;
	m_interactiveuin = 0;
	NeedValPer= GetLuaInterfaceProxy().get_lua_const()->doubleWeaponNeedValPer;
	durPer=GetLuaInterfaceProxy().get_lua_const()->doubleWeaponDurPer;
	donTime = GetLuaInterfaceProxy().get_lua_const()->fusionCageTime;
	m_Progress = NULL;
	m_sound = NULL;
	for (int i = 0; i < EFF_MAX;i++)
	{
		m_hEffect[i] = NULL;
	}
#ifndef IWORLD_SERVER_BUILD
	m_Progress = Rainbow::StoveProgressBar3D::Create("img_board_bar_2.png", "img_board_bar_green.png");
	m_Progress->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
#endif // !IWORLD_SERVER_BUILD
	m_NeedTick = true;
}

ContainerFusionCage::ContainerFusionCage(const WCoord &blockpos) : WorldContainer(blockpos, 0), m_ItemMesh_1(NULL), m_ItemMesh_2(NULL), m_ItemMesh_3(NULL)
{
	m_ItemData_1.clear();
	m_ItemData_2.clear();
	m_ItemData_3.clear();
	FusionCage_value = 0;
	m_doneTime = 0;
	m_doneValue = 0;
	m_MaxValue = 50;
	RotationVal = 0;
	m_interactiveuin = 0;
	m_Progress = NULL;
	m_sound = NULL;
	for (int i = 0; i < EFF_MAX; i++)
	{
		m_hEffect[i] = NULL;
	}
	NeedValPer = GetLuaInterfaceProxy().get_lua_const()->doubleWeaponNeedValPer;
	durPer = GetLuaInterfaceProxy().get_lua_const()->doubleWeaponDurPer;
	donTime=GetLuaInterfaceProxy().get_lua_const()->fusionCageTime;
#ifndef IWORLD_SERVER_BUILD
	m_Progress = Rainbow::StoveProgressBar3D::Create("img_board_bar_2.png", "img_board_bar_green.png");
	m_Progress->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
#endif // !IWORLD_SERVER_BUILD
	m_NeedTick = true;
}

void ContainerFusionCage::setItem(BackPackGrid *itemdata,int interactiveuin, bool isOutPut)
{
	int blockData = m_World->getBlockData(m_BlockPos);
	int dir = blockData & 3;
	if (getItemFst().isEmpty())
	{
		if (m_ItemMesh_1)
		{
			m_ItemMesh_1->DestroyGameObject();
			m_ItemMesh_1 = NULL;
		}
		if (itemdata)
		{
			int userdata = 0;
			int itemid = 0;
			m_ItemData_1 = *itemdata;
			m_ItemData_1.setNum(1);
			itemid = m_ItemData_1.getItemID();
			if (itemid > 0 && interactiveuin > 0 && m_interactiveuin == 0)
			{
				m_interactiveuin = interactiveuin;
			}
			//云服主机调用这里会宕机，云服没有必要执行
			adjustItemMesh();
			m_World->getEffectMgr()->playSound(BlockBottomCenter(m_BlockPos) + effPlaceOne[dir], "item.150029.fusion_in", 1.0f, 1.0f);
		}
		else m_ItemData_1.clear();
	}
	else if (getItemSnd().isEmpty())
	{
		if (m_ItemMesh_2)
		{
			m_ItemMesh_2->DestroyGameObject();
			m_ItemMesh_2 = NULL;
		}
		if (itemdata)
		{
			int userdata = 0;
			int itemid = 0;
			m_ItemData_2 = *itemdata;
			m_ItemData_2.setNum(1);
			itemid = m_ItemData_2.getItemID();
			if (itemid > 0 && interactiveuin > 0 && m_interactiveuin == 0)
			{
				m_interactiveuin = interactiveuin;
			}
			//云服主机调用这里会宕机，云服没有必要执行
			adjustItemMesh();	
			m_World->getEffectMgr()->playSound(BlockBottomCenter(m_BlockPos) + effPlaceTwo[dir], "item.150029.fusion_in", 1.0f, 1.0f);
		}
		else m_ItemData_2.clear();
	}
	else if (getItemTrd().isEmpty())
	{
		if (m_ItemMesh_3)
		{
			m_ItemMesh_3->DestroyGameObject();
			m_ItemMesh_3 = NULL;
		}
		if (itemdata)
		{
			int userdata = 0;
			int itemid = 0;
			m_ItemData_3 = *itemdata;
			m_ItemData_3.setNum(1);
			itemid = m_ItemData_3.getItemID();
			//云服主机调用这里会宕机，云服没有必要执行
			adjustItemMesh();
			m_World->getEffectMgr()->playSound(BlockBottomCenter(m_BlockPos) + effPlaceThird[dir], "item.150029.fusion_success", 1.0f, 1.0f);
		}
		else m_ItemData_3.clear();
	}
	else
	{
		return;
	}
	//保存chunk数据
	if (m_World)
	{
		Chunk* pchunk = m_World->getChunk(m_BlockPos);
		if (pchunk)
		{
			pchunk->m_Dirty = true;
		}
	}
	m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
}

ContainerFusionCage::~ContainerFusionCage()
{
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh_1);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh_2);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh_3);

	if (m_sound)//释放音效
	{
		m_sound->stop();
		m_sound->Release();
		m_sound = NULL;
	}
}

int ContainerFusionCage::getObjType() const
{
	return OBJ_TYPE_FUSIONCAGE;
}

void ContainerFusionCage::resetEntityBySyncData(std::string skey)
{
	if (!getItemFst().isEmpty())
	{
		int itemid = m_ItemData_1.getItemID();
		auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
		if (!itemDef)
			return;
		if (m_ItemMesh_1)
		{
			m_ItemMesh_1->DestroyGameObject();
			m_ItemMesh_1 = NULL;
		}
	}
	if (!getItemSnd().isEmpty())
	{
		int itemid = m_ItemData_2.getItemID();
		auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
		if (!itemDef)
			return;
		if (m_ItemMesh_2)
		{
			m_ItemMesh_2->DestroyGameObject();
			m_ItemMesh_2 = NULL;
		}
	}
	if (!getItemTrd().isEmpty())
	{
		int itemid = m_ItemData_3.getItemID();
		auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
		if (!itemDef)
			return;
		if (m_ItemMesh_3)
		{
			m_ItemMesh_3->DestroyGameObject();
			m_ItemMesh_3 = NULL;
		}
	}
	adjustItemMesh();
}

void ContainerFusionCage::adjustItemMesh()
{
	if (m_World && m_World->onClient())
	{
		if (getItemFst().isEmpty() && m_ItemMesh_1)
		{
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh_1);
		}
		if (getItemSnd().isEmpty() && m_ItemMesh_2)
		{
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh_2);
		}
		if (getItemTrd().isEmpty() && m_ItemMesh_3)
		{
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh_3);
		}
		int dir = checkItemPos();
		if (m_ItemMesh_1)
		{
			m_ItemMesh_1->SetVisibleDistance(16 * BLOCK_FSIZE);
			m_ItemMesh_1->UpdateTick(0);
			m_ItemMesh_1->AttachToScene(m_World->getScene());
			if (!m_hEffect[0])
			{
				auto eff = m_World->getEffectMgr()->playParticleEffectAsync("particles/fusion_cage_zhoushen_one.ent", BlockBottomCenter(m_BlockPos) + effPlaceOne[dir], 0, 0, 0, false);//展台特效)
				if (eff) {
					m_hEffect[0] = eff->_ID;
				}
			}
		}
		if (m_ItemMesh_2)
		{
			m_ItemMesh_2->SetVisibleDistance(16 * BLOCK_FSIZE);
			m_ItemMesh_2->UpdateTick(0);
			m_ItemMesh_2->AttachToScene(m_World->getScene());
			if (!m_hEffect[1])
			{
				auto eff = m_World->getEffectMgr()->playParticleEffectAsync("particles/fusion_cage_zhoushen_one.ent", BlockBottomCenter(m_BlockPos) + effPlaceTwo[dir], 0, 0, 0, false);//展台特效)
				if (eff) {
					m_hEffect[1] = eff->_ID;
				}
			}
		}
		if (m_ItemMesh_3)
		{
			m_ItemMesh_3->SetVisibleDistance(16 * BLOCK_FSIZE);
			m_ItemMesh_3->UpdateTick(0);
			m_ItemMesh_3->AttachToScene(m_World->getScene());
			if (!m_hEffect[2])
			{
				auto eff = m_World->getEffectMgr()->playParticleEffectAsync("particles/fusion_cage_ronghe.ent", BlockBottomCenter(m_BlockPos) + effPlaceThird[dir], 0, 0, 0, false);//展台特效)
				if (eff) {
					m_hEffect[2] = eff->_ID;
				}
			}
		}
		if (m_ItemMesh_1 == NULL && m_hEffect[0] != 0)
		{
			m_World->getEffectMgr()->stopParticleEffectByID(m_hEffect[0]);
			m_hEffect[0] = NULL;
		}
		if (m_ItemMesh_2 == NULL && m_hEffect[1] != 0)
		{
			m_World->getEffectMgr()->stopParticleEffectByID(m_hEffect[1]);
			m_hEffect[1] = NULL;
		}
		if (m_ItemMesh_3 == NULL && m_hEffect[2] != 0)
		{
			m_World->getEffectMgr()->stopParticleEffectByID(m_hEffect[2]);
			m_hEffect[2] = NULL;
		}
	}
}

void ContainerFusionCage::clearForVal(bool clearVal)
{
	m_ItemData_1.clear();
	m_ItemData_2.clear();
	m_ItemData_3.clear();
	m_interactiveuin = 0;
	adjustItemMesh();
	if(clearVal)
		FusionCage_value = 0;
}
void ContainerFusionCage::clear()
{
	m_ItemData_1.clear();
	m_ItemData_2.clear();
	m_ItemData_3.clear();
	m_interactiveuin = 0;
	adjustItemMesh();
	FusionCage_value = 0;
}

int ContainerFusionCage::checkItemPos()
{
	int blockData = m_World->getBlockData(m_BlockPos);
	int dir = blockData & 3;
	if (m_World && m_World->onClient())
	{
		WorldPos pos = BlockCenterCoord(TopCoord(m_BlockPos)).toWorldPos();
		if (!getItemFst().isEmpty())
		{
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh_1);
			
			int itemid;
			itemid = m_ItemData_1.getItemID();
			int userdata = 0;
			if (itemid == ITEM_COLORED_EGG || itemid == ITEM_COLORED_EGG_SMALL || itemid == ITEM_COLORED_EGGBULLET)
			{
				userdata = (itemid == ITEM_COLORED_EGG) ? 1 : 2;
			}
			//云服主机调用这里会宕机，云服没有必要执行
#ifndef IWORLD_SERVER_BUILD
			m_ItemMesh_1= ClientItem::createItemModel(m_ItemData_1.getItemID(), ITEM_MODELDISP_DROP, 1.0f, userdata, EXPO_MESH);
#endif
			if (m_ItemMesh_1)
			{
				checkPosForSingleItem(m_ItemMesh_1, m_ItemData_1);
				m_ItemMesh_1->SetScale(m_ItemMesh_1->GetScale() * scale);
				m_ItemMesh_1->SetPosition(pos + firstIconPos[dir]);
			}
		}
		if (!getItemSnd().isEmpty())
		{
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh_2);
			int itemid;
			itemid = m_ItemData_2.getItemID();
			int userdata = 0;
			if (itemid == ITEM_COLORED_EGG || itemid == ITEM_COLORED_EGG_SMALL || itemid == ITEM_COLORED_EGGBULLET)
			{
				userdata = (itemid == ITEM_COLORED_EGG) ? 1 : 2;
			}
			//云服主机调用这里会宕机，云服没有必要执行
#ifndef IWORLD_SERVER_BUILD
			m_ItemMesh_2= ClientItem::createItemModel(m_ItemData_2.getItemID(), ITEM_MODELDISP_DROP, 1.0f, userdata, EXPO_MESH);
#endif
			if (m_ItemMesh_2)
			{
				checkPosForSingleItem(m_ItemMesh_2, m_ItemData_2);
				m_ItemMesh_2->SetScale(m_ItemMesh_2->GetScale() * scale);
				m_ItemMesh_2->SetPosition(pos + secondIconPos[dir]);
			}
		}
		if (!getItemTrd().isEmpty())
		{
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh_3);
			int itemid;
			itemid = m_ItemData_3.getItemID();
			int userdata = 0;
			if (itemid == ITEM_COLORED_EGG || itemid == ITEM_COLORED_EGG_SMALL || itemid == ITEM_COLORED_EGGBULLET)
			{
				userdata = (itemid == ITEM_COLORED_EGG) ? 1 : 2;
			}
			//云服主机调用这里会宕机，云服没有必要执行
#ifndef IWORLD_SERVER_BUILD
			m_ItemMesh_3 = ClientItem::createItemModel(m_ItemData_3.getItemID(), ITEM_MODELDISP_DROP, 1.0f, userdata, EXPO_MESH);
#endif
			if (m_ItemMesh_3)
			{
				checkPosForSingleItem(m_ItemMesh_3, m_ItemData_3);
				m_ItemMesh_3->SetScale(m_ItemMesh_3->GetScale() * scale);
				m_ItemMesh_3->SetPosition(pos + thirdIconPos[dir]);
			}
		}
	}
	

	return dir;
}

void ContainerFusionCage::checkPosForSingleItem(BaseItemMesh* ItemMesh, BackPackGrid ItemData)
{
	int dir = -1;
	if (m_World && m_World->onClient() && ItemMesh)
	{
		if (m_vehicleWorld)
		{
			int itemid = ItemData.getItemID();
			auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
			dir = m_vehicleWorld->getBlockData(m_BlockPos) & 7;
			Rainbow::Quaternionf quatTarget = Quaternionf::zero;
			WCoord realPos;
			Rainbow::Quaternionf realRotate;
			WCoord offsetPos = WCoord(0, 0, 0);
			auto pVehicle = static_cast<VehicleWorld*>(m_vehicleWorld)->getActorVehicleAssemble();
			int nJointType = pVehicle->getJointTypeWithPos(m_BlockPos);
			if (4 > dir)
			{
				float yaws[4] = { 90.0f, -90.0f, 0, 180.0f };
				float yaw = yaws[dir];
				float pitch = 0;
				float roll = 0;
				float forward_offset = 4.0f;
				float up_offset = -1.0f;
				if (ItemMesh->getModelType() == ITEM_MODEL_OMOD)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						offsetPos.y += -5;
						if (1150 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.y += -20;
							offsetPos.x -= 35;
							offsetPos.z += 40;
						}
						else if (12828 == itemid)
						{
							offsetPos.y += -20;
							ItemMesh->SetScale(0.3f);
						}
						else if (12253 == itemid || 12281 == itemid)
						{
							offsetPos.x -= 35;
							offsetPos.z -= 35;
						}
						else if (12283 == itemid)
						{
							offsetPos.y -= 85;
							offsetPos.x -= 45;
							offsetPos.z += 50;
						}
						else if (15506 == itemid)
						{
							offsetPos.y -= 20;
						}
						else if (12830 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.y -= 15;
						}
						else if (10500 == itemid)
						{
							offsetPos.z -= 20;
							offsetPos.y += 10;
						}
						else if (15529 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.z -= 10;
						}
						else if (12583 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.y -= 15;
						}
						else if (12822 == itemid)
						{
							ItemMesh->SetScale(0.2f);
							offsetPos.x -= 7;
							offsetPos.y -= 7;
							offsetPos.z -= 7;
						}
						else if (12828 == itemid)
						{
							ItemMesh->SetScale(0.1f);
						}
						else if (12827 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.y -= 20;
						}
						else if (15005 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.z -= 15;
							offsetPos.y += 5;
						}
						else if (15004 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.z -= 13;
						}
						else if (12002 == itemid || 12063 == itemid)
						{
							ItemMesh->SetScale(0.2f);
							offsetPos.z -= 5;
							offsetPos.y += 5;
						}
						else if (12001 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.y -= 5;
						}
						else if (15508 == itemid || 15519 == itemid || 15520 == itemid || 15507 == itemid)
						{
							offsetPos.y -= 15;
						}
						else if (11315 == itemid)
						{
							ItemMesh->SetRotation(0, 0, 90.f);
						}
						else if (11016 == itemid)
						{
							offsetPos.z -= 10;
							offsetPos.y -= 10;
							ItemMesh->SetScale(0.3f);
						}
					}
					else
					{
						offsetPos.y += -5;
						if (12828 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.y += -20;
							offsetPos.x += 40;
						}
						else if (12253 == itemid)
						{
							offsetPos.z -= 35;
						}
						else if (12280 == itemid)
						{
							offsetPos.x += 45;
						}
						else if (1150 == itemid)
						{
							offsetPos.y += -20;
							offsetPos.z += 35;
						}
						else if (12281 == itemid)
						{
							offsetPos.x -= 35;
							offsetPos.z -= 35;
							offsetPos.x += 30;
						}
						else if (12283 == itemid)
						{
							offsetPos.y -= 85;
							offsetPos.z += 50;
						}
						else if (15506 == itemid)
						{
							offsetPos.y -= 20;
							offsetPos.x += 45;
						}
						else if (12822 == itemid)
						{
							ItemMesh->SetScale(0.2f);
							offsetPos.z -= 7;
							offsetPos.y -= 7;
							offsetPos.x += 35;
						}
						else if (12827 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.x += 40;
							offsetPos.y -= 20;
						}
						else if (15529 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.x += 40;
							offsetPos.z -= 10;
						}
						else if (12583 == itemid)
						{
							offsetPos.x += 40;
							offsetPos.y -= 15;
						}
						else if (1150 == itemid)
						{
							ItemMesh->SetScale(0.3f);

						}
						else if (12830 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.y -= 15;
							offsetPos.x += 40;
						}
						else if (15005 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.z -= 15;
							offsetPos.y += 5;
							offsetPos.x += 40;
						}
						else if (15004 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.z -= 13;
							offsetPos.x += 40;
						}
						else if (12002 == itemid || 12063 == itemid)
						{
							ItemMesh->SetScale(0.2f);
							offsetPos.x += 40;
							offsetPos.z -= 5;
							offsetPos.y += 5;
						}
						else if (12001 == itemid)
						{
							ItemMesh->SetScale(0.3f);
							offsetPos.x += 40;
							offsetPos.y -= 5;
						}
						else if (15508 == itemid || 15519 == itemid || 15520 == itemid || 15507 == itemid)
						{
							offsetPos.y -= 15;
							offsetPos.x += 40;
						}
						else if (11315 == itemid)
						{
						ItemMesh->SetRotation(0, 0, 90);
							offsetPos.x += 40;
						}
						else if (11016 == itemid)
						{
							offsetPos.x += 40;
							offsetPos.z -= 10;
							offsetPos.y -= 10;
							ItemMesh->SetScale(0.3f);
						}
						else if (12293 == itemid || 12294 == itemid || 12295 == itemid || 12296 == itemid || 12298 == itemid ||
							12587 == itemid || 12588 == itemid || 12003 == itemid || 12004 == itemid || 11035 == itemid ||
							12526 == itemid || 12053 == itemid || 12058 == itemid || 11058 == itemid || 12285 == itemid ||
							12282 == itemid || 15003 == itemid || 15008 == itemid || 15007 == itemid || 15002 == itemid ||
							11101 == itemid || 12056 == itemid || 12291 == itemid || 12240 == itemid || 11806 == itemid || 12591 == itemid ||
							817 == itemid || 11025 == itemid || 11005 == itemid || 15000 == itemid || 12289 == itemid || 12008 == itemid || 12051 == itemid || 12050 == itemid ||
							11015 == itemid || 11001 == itemid || 11002 == itemid || 11003 == itemid || 11004 == itemid || 11011 == itemid || 11012 == itemid || 11013 == itemid ||
							11014 == itemid || 11021 == itemid || 11022 == itemid || 11023 == itemid || 11024 == itemid || 11031 == itemid || 11032 == itemid || 11033 == itemid ||
							11034 == itemid || 12248 == itemid)
						{
							offsetPos.x += 40;
						}
					}
					// 					offsetPos.z -= 20;
				}
				else if (ItemMesh->getModelType() == ITEM_MODEL_IMAGE)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						offsetPos.x -= 40;
						offsetPos.z -= 40;
					}
					else
					{
						// 						offsetPos.x -= 40;
						offsetPos.z -= 40;
					}
				}
				else if (ItemMesh->getModelType() == ITEM_MODEL_BLOCK)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						offsetPos.x += 10;
					}
					else
					{
						offsetPos.x += 50;
					}
				}

				if (itemDef && itemDef->MeshType == CUSTOM_GEN_MESH)
				{
					if (itemDef->Icon == "customegg")
					{
						ItemMesh->SetScale(0.4f);
						offsetPos.y -= 15;
					}
					else
					{
						ItemMesh->SetScale(0.4f);

						forward_offset = 12.5f;
						offsetPos.y -= 8;

						float side_offset = 10.0f;
						offsetPos.x += (int)(side_offset * Rainbow::Cos(yaw + 180.0f));
						offsetPos.z += (int)(side_offset * Rainbow::Sin(yaw));
					}
				}
				else if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					ItemMesh->SetScale(0.25f);
					offsetPos.y -= 25;
				}
				else if (itemDef && itemDef->MeshType == IMPORT_MODEL_GEN_MESH)
				{
					ItemMesh->SetScale(0.5f);
					offsetPos.y -= 25;
				}
				// 				offsetPos.x += /*WorldPos::Flt2Fix*/(forward_offset * Rainbow::Sin(-yaw));
				// 				offsetPos.z += /*WorldPos::Flt2Fix*/(forward_offset * Cos(yaw + 180.0f));

				if (itemid == 12283) quatTarget = AngleEulerToQuaternionf(Vector3f(-90.0f, yaw + 90.0f, 90.0f));
				else if (itemid == 12253 || itemid == 12281) quatTarget = AngleEulerToQuaternionf(Vector3f(0, yaw, 0));
				else if (ItemMesh->getModelType() == ITEM_MODEL_IMAGE) quatTarget = AngleEulerToQuaternionf(Vector3f(0, yaw, 0));
				else if (1150 == itemid) quatTarget = AngleEulerToQuaternionf(Vector3f(0, yaw + 180, 0));
				else quatTarget = AngleEulerToQuaternionf(Vector3f(0, yaw + 90.0f, 0));

			}
			else
			{
				float zaws[3] = { 0, 180.0f, 0 };
				float zaw = zaws[dir - 4];
				auto pVehicle = static_cast<VehicleWorld*>(m_vehicleWorld)->getActorVehicleAssemble();

				float forward_offset = 4.0f;
				// 				if (dir == DIR_NEG_Y)
				// 				{
				// 					offsetPos.y -= /*WorldPos::Flt2Fix*/(forward_offset);
				// 				}
				// 				else
				// 				{
				// 					offsetPos.y += /*WorldPos::Flt2Fix*/(forward_offset);
				// 				}
				float up_offset = 0.0f;

				int itemid = ItemData.getItemID();
				auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
				if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					offsetPos.z -= 22;
					ItemMesh->SetScale(0.25);
				}

				if (ItemMesh->getModelType() == ITEM_MODEL_OMOD)
				{
					offsetPos.y += (int)up_offset;
					if (dir == DIR_NEG_Y)
					{
						if (1150 == itemid)
						{
							offsetPos.y -= 20;
						}
						else if (12828 == itemid)
						{
							offsetPos.y -= 50;
						}
						else if (15506 == itemid)
						{
							offsetPos.x += 10;
							offsetPos.y -= 20;
						}
						else if (12253 == itemid || 12281 == itemid)
						{
							offsetPos.z -= 10;
						}
						else if (12283 == itemid)
						{
							offsetPos.y += 10;
							// 							offsetPos.x -= 45;
							offsetPos.z -= 10;
						}
						else if (12822 == itemid)
						{
							ItemMesh->SetScale(0.2f);
							offsetPos.y -= 10;
							offsetPos.x += 20;
							offsetPos.z -= 10;
						}
					}
					else
					{
						if (1150 == itemid)
						{
							offsetPos.y -= 20;
							offsetPos.z += 100;
						}
						else if (12828 == itemid)
						{
							offsetPos.x -= 80;
							offsetPos.y -= 30;
						}
						else if (15506 == itemid)
						{
							offsetPos.x -= 90;
							offsetPos.y -= 30;
						}
						else if (12253 == itemid)
						{
							offsetPos.z += 100;
						}
						else if (12281 == itemid)
						{
							// 							offsetPos.x -= 10;
							// 							offsetPos.y -= 10;
							offsetPos.z += 90;
						}
						else if (12283 == itemid)
						{
							offsetPos.y -= 90;
							offsetPos.z -= 10;
						}
						else if (12822 == itemid)
						{
							ItemMesh->SetScale(0.2f);
							offsetPos.x -= 100;
							offsetPos.z -= 10;
						}
					}
				}
				else if (ItemMesh->getModelType() == ITEM_MODEL_IMAGE)
				{
					if (nJointType == 3)
					{
						if (dir == DIR_NEG_Y)
						{
							// 							offsetPos.x -= 40;
							offsetPos.z += 40;
							offsetPos.y += 50;
						}
						else
						{
							// 							offsetPos.x -= 40;
							offsetPos.z += 40;
							offsetPos.y -= 50;
						}
					}
					else
					{
						if (dir == DIR_NEG_Y)
						{
							offsetPos.z -= 10;
						}
						else
						{
							offsetPos.z += 90;
						}
					}
				}
				else if (ItemMesh->getModelType() == ITEM_MODEL_BLOCK)
				{
					if (dir == DIR_NEG_Y)
					{
					}
					else
					{
						offsetPos.y -= 100;
					}
				}

				if (itemid == 12253 || itemid == 12281 || ItemMesh->getModelType() == ITEM_MODEL_IMAGE)
				{
					quatTarget = AngleEulerToQuaternionf(Vector3f(zaw + 90, 0, 0));
				}
				else if (ItemMesh->getModelType() == ITEM_MODEL_BLOCK || itemid == 12283)
				{
					quatTarget = AngleEulerToQuaternionf(Vector3f(zaw, 0, 0));
				}
				else if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					quatTarget = AngleEulerToQuaternionf(Vector3f(zaw, -90, -90));
				}
				else if (1150 == itemid)
				{
					quatTarget = AngleEulerToQuaternionf(Vector3f(zaw + 90, 90, 90));
				}
				else
				{
					quatTarget = AngleEulerToQuaternionf(Vector3f(zaw, 90, 90));
				}
			}
			if (pVehicle->getRealWorldPosAndRotate(dir, m_BlockPos, realPos, quatTarget, realRotate, offsetPos))
			{
				WorldPos pos = realPos.toWorldPos();
				ItemMesh->SetPosition(pos);
				ItemMesh->SetRotation(realRotate);
			}
		}
		else
		{
			dir = m_World->getBlockData(m_BlockPos) & 7;
			int Block_ID = m_World->getBlockID(m_BlockPos);
			if (dir < 4)
			{
				static float yaws[4] = { 90.0f, -90.0f, 0, 180.0f };
				float yaw = yaws[dir];
				WorldPos pos = BlockCenterCoord(m_BlockPos).toWorldPos();

				float forward_offset = 20.0f;//40.0f;

				
				/*
				static float side_offset = 10.0f;
				pos.x += side_offset * Cos(yaw+180.0f);
				pos.z += side_offset * Sin(yaw);
				*/

				static float up_offset = -10.0f;
				if (ItemMesh->getModelType() == ITEM_MODEL_OMOD) pos.y += (Rainbow::WPOS_T)up_offset;

				int itemid = ItemData.getItemID();
				auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
				if (itemDef && itemDef->MeshType == CUSTOM_GEN_MESH)
				{
					if (itemDef->Icon == "customegg")
					{
						ItemMesh->SetScale(0.4f);
						pos.y -= 150;
					}
					else
					{
						ItemMesh->SetScale(0.4f);

						forward_offset = 125.0f;
						pos.y -= 80;

						float side_offset = 100.0f;
						pos.x += (Rainbow::WPOS_T)(side_offset * Cos(yaw + 180.0f));
						pos.z += (Rainbow::WPOS_T)(side_offset * Sin(yaw));
					}

				}
				else if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					ItemMesh->SetScale(0.25);
					pos.y -= 250;
				}
				else if (itemDef && itemDef->MeshType == IMPORT_MODEL_GEN_MESH)
				{
					ItemMesh->SetScale(0.3f);
					pos.y -= 300;
				}

				pos.x += WorldPos::Flt2Fix(forward_offset * Sin(-yaw));
				pos.z += WorldPos::Flt2Fix(forward_offset * Cos(yaw + 180.0f));
				ItemMesh->SetPosition(pos);


				if (itemid == 12283) ItemMesh->SetRotation(yaw + 90.0f, -90.0f, 90.0f);
				else if (itemid == 12253) ItemMesh->SetRotation(yaw, 0, 0);
				else if (itemid == 12281) ItemMesh->SetRotation(yaw, 0, 0);
				else if (ItemMesh->getModelType() == ITEM_MODEL_IMAGE) ItemMesh->SetRotation(yaw, 0, 0);
				else ItemMesh->SetRotation(yaw + 90.0f, 0, 0);
			}
			else
			{
				static float zaws[3] = { 0, 180.0f, 0 };
				float zaw = zaws[dir - 4];


				WorldPos pos = BlockCenterCoord(m_BlockPos).toWorldPos();

				static float forward_offset = 20.0f; // 40.0f;
				if (dir == 4)
				{
					pos.y -= WorldPos::Flt2Fix(forward_offset);
				}
				else
				{
					pos.y += WorldPos::Flt2Fix(forward_offset);
				}
				float up_offset = 0.0f;

				int itemid = ItemData.getItemID();
				auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
				if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					pos.z -= 220;
					ItemMesh->SetScale(0.25);
				}
				else if (itemDef && itemDef->MeshType == IMPORT_MODEL_GEN_MESH)
				{
					ItemMesh->SetScale(0.3f);
					pos.z -= 250;
				}

				if (ItemMesh->getModelType() == ITEM_MODEL_OMOD) pos.y += (Rainbow::WPOS_T)up_offset;
				ItemMesh->SetPosition(pos);
				//修改悬浮物品使用setRotation函数
				if (itemid == 12283) ItemMesh->SetRotation(0, zaw, 0);
				else if (itemid == 12253) ItemMesh->SetRotation(0, zaw + 90, 0);
				else if (itemid == 12281) ItemMesh->SetRotation(0, zaw + 90, 0);
				else if (ItemMesh->getModelType() == ITEM_MODEL_IMAGE) ItemMesh->SetRotation(0, zaw + 90, 0);
				else if (ItemMesh->getModelType() == ITEM_MODEL_BLOCK)
				{
					ItemMesh->SetRotation(0, zaw, 0);
				}
				else if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					ItemMesh->SetRotation(-90, zaw, -90);
				}
				else if (itemDef && itemDef->MeshType == IMPORT_MODEL_GEN_MESH)
				{
					ItemMesh->SetRotation(-90, zaw, -90);
				}
				else
				{
					/*int x, y, z = 0;
					float x1, x2, x3, x4, x5, x6, x7, x8, x9,x10 = 0;
					MINIW::ScriptVM::game()->callFunction("getcactus_force", ">iiiffffffffff", &x, &y, &z,&x1, &x2, &x3, &x4, &x5, &x6, &x7, &x8, &x9,&x10);*/


					/*if (x != 0)
					{
						m_ItemMesh->SetRotation(x,y, z);*/
					if (GetDefManagerProxy()->isFishNeedUp(itemDef->ID))
					{
						ItemMesh->SetRotation(0, 0, 0);
						float scaleNum = 1.0f;
						if (itemDef->ID == ITEM_CRAB)
						{
							scaleNum = 0.5f;
						}
						else if (itemDef->ID == 13622)
						{
							scaleNum = 0.5f;
						}
						else if (itemDef->ID == ITEM_HIPPOCAMPUS)
						{
							scaleNum = 0.12f;
						}
						else if (itemDef->ID == ITEM_SMALL_HIPPOCAMPUS)
						{
							scaleNum = 0.4f;
						}
						else if (itemDef->ID == ITEM_TAME_HIPPOCAMPUS)
						{
							//scaleNum = x5;
						}
						else if (itemDef->ID == ITEM_JELLYFISH)
						{
							scaleNum = 0.2f;
						}
						else if (itemDef->ID == 13627)
						{
							scaleNum = 0.5f;
						}
						else if (itemDef->ID == 13628)
						{
							scaleNum = 0.1f;
						}
						else if (itemDef->ID == 13629)
						{
							scaleNum = 0.14f;
						}
						else if (itemDef->ID == 13630)
						{
							scaleNum = 0.15f;
						}
						ItemMesh->SetScale(scaleNum);
					}
					else
					{
						ItemMesh->SetRotation(90, zaw, 90);
					}
					//}

				}
			}
		}

	}
}

void ContainerFusionCage::enterWorld(World *pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	registerUpdateDisplay();

	if (m_World && m_World->onClient())
	{
		// 进度条可视距离
		float dist = 16 * BLOCK_FSIZE;
		WCoord pos = BlockCenterCoord(m_BlockPos) + WCoord(0, int(1.2 * BLOCK_SIZE), 0);
		if (m_Progress)
		{
			m_Progress->SetVisibleDistance(dist);
			m_Progress->AttachToScene(m_World->getScene());
			m_Progress->setVisible(false);
			WCoord pos = BlockCenterCoord(m_BlockPos) + WCoord(0, int(0.4 * BLOCK_SIZE), 0);
			m_Progress->SetPosition(pos.toWorldPos());
			m_Progress->setWidth(60);
			m_Progress->setHight(6);
			m_Progress->SetNineSquare(true);
			m_Progress->setBarByBoardOffset(2, 2, 1);
			m_Progress->SetBarNinesquareParam(4, 4, 4, 4);
			m_Progress->SetBackNinesquareParam(4, 4, 4, 4);
		}
		setProgressBarPos();
	}
	adjustItemMesh();
}

void ContainerFusionCage::leaveWorld()
{
	if(m_World->onClient() )
	{
		if(m_ItemMesh_1)
			m_ItemMesh_1->DetachFromScene();
		if (m_ItemMesh_2)
			m_ItemMesh_2->DetachFromScene();
		if (m_ItemMesh_3)
			m_ItemMesh_3->DetachFromScene();
	}
	if (m_Progress)
	{
		m_Progress->DetachFromScene();
	}
	for (int i = 0; i < EFF_MAX; i++)
	{
		if (m_hEffect[i] != 0) {
			m_World->getEffectMgr()->stopParticleEffectByID(m_hEffect[i]);
			m_hEffect[i] = NULL;
		}
	}
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_Progress);
	WorldContainer::leaveWorld();
}

void ContainerFusionCage::dropItems()
{
	dropOneItem(m_ItemData_1);
	dropOneItem(m_ItemData_2);
	dropTrdItem();
	if (FusionCage_value > 0)
	{
		int countV = FusionCage_value / GetLuaInterfaceProxy().get_lua_const()->fusionCageValPer;
		m_World->spawnItem(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z, CRAFT_UPDATE_ITEM, countV);//丢弃剩余创造晶体
		FusionCage_value = 0;
	}
	if (m_sound)//释放音效
	{
		m_sound->stop();
		m_sound->Release();
		m_sound = NULL;
	}
}
void ContainerFusionCage::dropTrdItem()
{
	dropOneItem(m_ItemData_3);
	m_ItemData_3.clear();
}
void ContainerFusionCage::dropItemMesh()
{
	//保存chunk数据
	if (m_World)
	{
		Chunk* pchunk = m_World->getChunk(m_BlockPos);
		if (pchunk)
		{
			pchunk->m_Dirty = true;
		}
	}
	clear();
	m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
}
void ContainerFusionCage::dropItemMeshNoClear()//不清除融合台上的数值
{
	//保存chunk数据
	if (m_World)
	{
		Chunk* pchunk = m_World->getChunk(m_BlockPos);
		if (pchunk)
		{
			pchunk->m_Dirty = true;
		}
	}
	clearForVal(false);
	m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
}
flatbuffers::Offset<FBSave::ChunkContainer> ContainerFusionCage::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerFusionCage(builder, basedata, m_ItemData_1.save(builder), m_ItemData_2.save(builder), m_ItemData_3.save(builder), FusionCage_value, m_doneValue, m_MaxValue,m_interactiveuin);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerFusionCage, actor.Union());
}

bool ContainerFusionCage::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerFusionCage *>(srcdata);
	loadContainerCommon(src->basedata());
	m_ItemData_1.load(src->itemfst()); 
	m_ItemData_2.load(src->itemSnd());
	m_ItemData_3.load(src->itemTrd());
	FusionCage_value=src->cageValue();
	m_doneValue = src->doneValue();
	m_MaxValue = src->maxValue();
	m_interactiveuin = src->interactiveuin();
#ifndef IWORLD_SERVER_BUILD
	if (!m_ItemData_1.isEmpty()) m_ItemMesh_1 = ClientItem::createImageMesh(m_ItemData_1.getItemID());
	if (!m_ItemData_2.isEmpty()) m_ItemMesh_2 = ClientItem::createImageMesh(m_ItemData_2.getItemID());
	if (!m_ItemData_3.isEmpty()) m_ItemMesh_3 = ClientItem::createImageMesh(m_ItemData_3.getItemID());
#endif
	return true;
}

const WCoord dropPlaceOne[4] = {
	WCoord(50, 100, 120),
	WCoord(50, 100, -50),
	WCoord(-30, 100, 50),
	WCoord(120, 100, 50)
};
const WCoord dropPlaceTwo[4] = {
	WCoord(50, 100, -120),
	WCoord(50, 100, 250),
	WCoord(220, 100, 50),
	WCoord(-120, 100, 50)
};
const WCoord dropPlaceThird[4] = {
	WCoord(-25, 100, 0),
	WCoord(125, 100, 100),
	WCoord(100, 100, -25),
	WCoord(0, 100, 125)
};

void ContainerFusionCage::updateTick()
{
	int blockData = m_World->getBlockData(m_BlockPos);
	int dir = blockData & 3;
	WorldPos pos = BlockCenterCoord(TopCoord(m_BlockPos)).toWorldPos();
	if (!m_ItemData_1.isEmpty() && !m_ItemData_2.isEmpty() && getFusionCageValue() > 0)
	{
		if (m_World->GetWorldMgr() && m_doneTime < m_World->GetWorldMgr()->getWorldTime())
		{
			setFusionCageValue(getFusionCageValue() - 1);
			DefDataTable<ToolDef>& ToolTable = GetDefManagerProxy()->getToolTable();
			ToolDef* tooldef1 = ToolTable.GetRecord(m_ItemData_1.getItemID());
			ToolDef* tooldef2 = ToolTable.GetRecord(m_ItemData_2.getItemID());
			if (tooldef1 && tooldef2)//设置融合机最大进度
			{
				int Value1 = tooldef1->SubLevel;
				int Value2 = tooldef2->SubLevel;
				m_MaxValue = (Value1 + Value2) * 10 * NeedValPer;//NeedValPer双持武器消耗创造晶体比例
				if (m_MaxValue > 100)//大于10则设置为10
				{
					m_MaxValue = 100;
				}
				else if (m_MaxValue <= 20)//最低为20
				{
					m_MaxValue = 20;
				}
			}
			else
			{
				m_MaxValue = 20;
			}
#ifndef IWORLD_SERVER_BUILD			
			int ItemId1 = m_ItemData_1.getItemID();
			int ItemId2 = m_ItemData_2.getItemID();
			const CraftingDef* craftDef = GetDefManagerProxy()->findCraftingByDoubleWeapon(ItemId1, ItemId2);
			if (craftDef == NULL)//对调左右手武器尝试判断
			{
				craftDef = GetDefManagerProxy()->findCraftingByDoubleWeapon(ItemId2, ItemId1);
			}
			if (craftDef != NULL)//尝试生成icon
			{
				ItemIconManager::GetInstance().genOneDoubleWeaponIconTexture(craftDef->ResultID);		
			}
			m_World->markBlockForUpdate(m_BlockPos);
			setProgressBarPos();
#endif
			m_doneTime = m_World->GetWorldMgr()->getWorldTime() + donTime;//40tick为2秒
		}
	}
	if (m_World->GetWorldMgr() && m_doneTime >= m_World->GetWorldMgr()->getWorldTime())
	{
		int dis = m_doneTime - m_World->GetWorldMgr()->getWorldTime();
		if (dis % (donTime/10) == 0 && dis != donTime)
		{
			m_doneValue++;
		}
		if (!m_hEffect[3])
		{
			auto eff = m_World->getEffectMgr()->playParticleEffectAsync("particles/fusion_cage_ronghezhong.ent", BlockBottomCenter(m_BlockPos) + effPlaceThird[dir], 0, 0, 0, false);
			if (eff) {
				m_hEffect[3] = eff->_ID;
			}
		}
		if (!m_sound)
		{
			auto snd=m_World->getEffectMgr()->playLoopSound(BlockBottomCenter(m_BlockPos) + effPlaceThird[dir], "item.150029.fusion_loading", 1.0f, 1.0f);//融合中音效
			if (snd) {
				m_sound = snd;
			}
		}
	}
	else if (m_World->GetWorldMgr() && m_doneTime < m_World->GetWorldMgr()->getWorldTime())
	{
		if (m_hEffect[3])
		{
			m_World->getEffectMgr()->stopParticleEffectByID(m_hEffect[3]);
			m_hEffect[3] = NULL;
		}
		if (m_sound)//释放音效
		{
			m_sound->stop();
			m_sound->Release();
			m_sound = NULL;
		}
	}
	if (m_ItemMesh_3)
	{
		m_ItemMesh_3->SetRotation(RotationVal,0,0);
		RotationVal += 1;
		if (RotationVal == 360)
		{
			RotationVal = 0;
		}
	}
	if (m_doneValue >= m_MaxValue && m_MaxValue!=0)
	{
		//判断是否能成功融合

		int ItemId1 = m_ItemData_1.getItemID();
		int ItemId2 = m_ItemData_2.getItemID();
		const CraftingDef* craftDef = GetDefManagerProxy()->findCraftingByDoubleWeapon(ItemId1, ItemId2);
		if (craftDef == NULL)//对调左右手武器尝试判断
		{
			craftDef = GetDefManagerProxy()->findCraftingByDoubleWeapon(ItemId2, ItemId1);
		}
		if (craftDef != NULL && craftDef->CraftingItemID==BLOCK_FUSION_CAGE)
		{
			//融合成功
			int runeNum1 = m_ItemData_1.getRuneNum();
			for (int i = 0; i < runeNum1; i++)
			{
				GridRuneItemData runeData1 = m_ItemData_1.getRuneData().getItemByIndex(i);
				BackPackGrid runeStone;
				runeStone.setItem(RUNE_AUTHED_ACCURATE, 1);
				runeStone.addRune(runeData1);
				dropOneItem(runeStone, dropPlaceThird[dir]);
				runeStone.clear();
			}
			int runeNum2 = m_ItemData_2.getRuneNum();
			for (int i = 0; i < runeNum2; i++)
			{
				GridRuneItemData runeData2 = m_ItemData_2.getRuneData().getItemByIndex(i);
				BackPackGrid runeStone;
				runeStone.setItem(RUNE_AUTHED_ACCURATE, 1);
				runeStone.addRune(runeData2);
				dropOneItem(runeStone, dropPlaceThird[dir]);
				runeStone.clear();
			}
			BackPackGrid outPutWeapon;

			
			outPutWeapon.setItem(craftDef->ResultID, 1);
			//设置双持武器的耐久度
			DefDataTable<ToolDef>& ToolTable = GetDefManagerProxy()->getToolTable();
			ToolDef* tooldef = ToolTable.GetRecord(craftDef->ResultID);
			if (tooldef)
			{
				int Duration1 = m_ItemData_1.getDuration();
				int Duration2 = m_ItemData_2.getDuration();
				float newDur = (Duration1 + Duration2) * durPer;
				if (newDur > tooldef->Duration)//耐久度附魔的情况下会有大于原本耐久度的情况，大于则拿回本身的耐久度
				{
					outPutWeapon.setDuration(tooldef->Duration);
				}
				else
				{
					outPutWeapon.setDuration(newDur);
				}
			}
			m_World->getEffectMgr()->playParticleEffectAsync("particles/fusion_cage_chenggong.ent", BlockCenterCoord(m_BlockPos) + effPlaceThird[dir], 100, 0, 0, true);//成功特效
			setItem(&outPutWeapon,0, true);

			if (m_interactiveuin > 0) {
				ObserverEvent obevent;
				obevent.SetData_EventObj(m_interactiveuin); //和策划暂定方第一个玩家
				obevent.SetData_Craft(craftDef->ID);
				obevent.SetData_Item(craftDef->ResultID, 1);
				GetObserverEventManager().OnTriggerEvent("Craft.end", &obevent);
			}


			//dropOneItem(outPutWeapon, WCoord(dropPlaceThird[dir][0], dropPlaceThird[dir][1], dropPlaceThird[dir][2]));
			//outPutWeapon->clear();
		}
		else
		{
			//融合失败
			dropOneItem(m_ItemData_1, dropPlaceOne[dir]);
			dropOneItem(m_ItemData_2, dropPlaceTwo[dir]);
			m_World->getEffectMgr()->playParticleEffectAsync("particles/fusion_cage_shibai.ent", BlockCenterCoord(m_BlockPos) + effPlaceThird[dir], 100, 0, 0, true);//失败特效
			m_World->getEffectMgr()->playSound(BlockBottomCenter(m_BlockPos) + effPlaceThird[dir], "item.150029.fusion_fail", 1.0f, 1.0f);//失败音效
		}
		if (m_hEffect[3])
		{
			m_World->getEffectMgr()->stopParticleEffectByID(m_hEffect[3]);
			m_hEffect[3] = NULL;
		}
		m_ItemData_1.clear();
		m_ItemData_2.clear();
		if (m_sound)//释放音效
		{
			m_sound->stop();
			m_sound->Release();
			m_sound = NULL;
		}
		m_doneValue = 0;
		m_MaxValue = 0;
		adjustItemMesh();
		m_World->markBlockForUpdate(m_BlockPos);
	}
}

void ContainerFusionCage::setProgressBarPos()//调整进度条位置
{
#ifdef IWORLD_SERVER_BUILD
	return;
#endif
	if (m_Progress)
	{
		WCoord pos = BlockCenterCoord(m_BlockPos) + WCoord(0, int(2 * BLOCK_SIZE), 0);
		int blockData = m_World->getBlockData(m_BlockPos);
		int dir = blockData & 3;
		if (dir == DIR_NEG_X) pos = BlockCenterCoord(m_BlockPos) + WCoord(0, int(2 * BLOCK_SIZE), -50);
		else if (dir == DIR_POS_X) pos = BlockCenterCoord(m_BlockPos) + WCoord(0, int(2 * BLOCK_SIZE), 50);
		else if (dir == DIR_NEG_Z) pos = BlockCenterCoord(m_BlockPos) + WCoord(50, int(2 * BLOCK_SIZE), 0);
		else if (dir == DIR_POS_Z) pos = BlockCenterCoord(m_BlockPos) + WCoord(-50, int(2 * BLOCK_SIZE), 0);
		m_Progress->SetPosition(pos.toWorldPos());
	}
}

void ContainerFusionCage::updateDisplay(float dtime)
{
	if (m_ItemMesh_1)
	{
		if (m_vehicleWorld)
		{
			checkItemPos();
		}
		unsigned int dtick = TimeToTick(dtime);
		m_ItemMesh_1->UpdateTick(dtick);
	}
	if (m_ItemMesh_2)
	{
		if (m_vehicleWorld)
		{
			checkItemPos();
		}
		unsigned int dtick = TimeToTick(dtime);
		m_ItemMesh_2->UpdateTick(dtick);
	}
	if (m_ItemMesh_3)
	{
		if (m_vehicleWorld)
		{
			checkItemPos();
		}
		unsigned int dtick = TimeToTick(dtime);
		m_ItemMesh_3->UpdateTick(dtick);
	}
#ifdef IWORLD_SERVER_BUILD
	return;
#endif
	if (m_Progress)
	{
		if (m_doneValue == 0)
		{
			m_Progress->setVisible(false);
			m_Progress->setVale(0,20);
		}
		else
		{
			if (m_doneValue != 0 && m_MaxValue>0)
			{
				m_Progress->setVale(m_doneValue, m_MaxValue);
				m_Progress->setVisible(true);
			}
		}
		m_Progress->update(dtime);
	}
}

