#include "ClientActorBaseLua.h"

IMPLEMENT_SCENEOBJECTCLASS(ClientActorBaseLua)
ClientActorBaseLua::ClientActorBaseLua()
{
}

ClientActorBaseLua::~ClientActorBaseLua()
{

}

flatbuffers::Offset<FBSave::SectionActor> ClientActorBaseLua::save(SAVE_BUFFER_BUILDER &builder)
{
	return 0;
}


bool ClientActorBaseLua::load(const void *srcdata, int version)
{
	return true;
}

int ClientActorBaseLua::getObjType() const
{
	return 0;
}