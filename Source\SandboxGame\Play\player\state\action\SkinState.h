﻿#pragma once
#include "PlayerState.h"

class SkinState :public PlayerState//tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	SkinState(PlayerControl* host);
	virtual ~SkinState();
	virtual void doBeforeEntering() override;
	virtual std::string update(float dtime) override;
	virtual void doBeforeLeaving() override;
	virtual void OnTick(float elapse) final;
	//tolua_end

private:

}; //tolua_exports