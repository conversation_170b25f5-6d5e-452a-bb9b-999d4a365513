#include "ActorUpdateFrequency.h"

#include "ClientActor.h"
#include "world.h"
#include "WorldManager.h"
#include <assert.h>
IMPLEMENT_COMPONENTCLASS(ActorUpdateFrequency)
ActorUpdateFrequency::ActorUpdateFrequency():m_VisibleFrame(0),m_UpdateFrame(0), m_needUpdate(true)
{

}

bool ActorUpdateFrequency::checkNeedUpdate(float dtime, float &retdt)
{
	m_needUpdate = false;
	//m_VisibleFrame = ClientActor::m_CurActorFrame;
	flushVisibleFrame();
	//if (m_VisibleFrame != ClientActor::m_CurActorFrame) //not visible last frame
	//{
	//	return false;
	//}

	if (GetWorldManagerPtr() == NULL) return m_needUpdate;
	WCoord& center = GetWorldManagerPtr()->m_RenderEyePos;

	if (!GetOwner()) return false;
	m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return false;

	WCoord dp = m_owner->getPosition() - center;
	SInt64 lsq = dp.lengthSquared();
	if (lsq > 3200 * 3200)
	{
		if (m_UpdateFrame + 5 > m_VisibleFrame) return false;
	}
	if (m_owner->getObjType() != OBJ_TYPE_VEHICLE)
	{
		if (lsq > 1600 * 1600)
		{
			if (m_UpdateFrame + 2 > m_VisibleFrame) return false;
		}
	}
	else {
		if (lsq > 3000 * 3000)
		{
			if (m_UpdateFrame + 2 > m_VisibleFrame) return false;
		}
	}

	int n = m_VisibleFrame - m_UpdateFrame;
	if (n > 5) n = 5;
	float dt = dtime * float(n);
	retdt = dt;
	m_needUpdate = true;
	return m_needUpdate;
}
void ActorUpdateFrequency::finishUpdate()
{
	m_UpdateFrame = m_VisibleFrame;
}

void ActorUpdateFrequency::onEnterWorld(World *pworld)
{
	flushVisibleFrame();
	m_UpdateFrame  = m_VisibleFrame - 1;
}

void ActorUpdateFrequency::flushVisibleFrame()
{
	m_VisibleFrame = ClientActor::m_CurActorFrame;
}
IMPLEMENT_COMPONENTCLASS(EmptyUpdateFrequency)

EmptyUpdateFrequency::EmptyUpdateFrequency():ActorUpdateFrequency()
{

}
bool EmptyUpdateFrequency::checkNeedUpdate(float dtime, float &retdt)
{
	retdt = dtime;
	return true;
}
void EmptyUpdateFrequency::finishUpdate()
{

}