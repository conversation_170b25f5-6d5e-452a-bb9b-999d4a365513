#include "container_modelbase.h"
#include "ActorBody.h"
#include "ClientMob.h"
#include "FullyCustomModelMgr.h"
#include "EffectModel.h"
#include "LegacyOgreAnimationPlayer.h"
#include "LegacyMesh.h"
#include "LegacyOgreSkeleton.h"
#include "ClientInfoProxy.h"
#include "MusicManager.h"
#include "worldMesh/ModelItemMesh.h"
#include "DefManagerProxy.h"
#include "ActorHorse.h"
#include "PlayerControl.h"
#include "ActorBody.h"
#include "world.h"
#include "LivingAttrib.h"
#include "EffectManager.h"
#include "LuaInterfaceProxy.h"
#include "ClientItem.h"
#include "BlockMaterialMgr.h"
#include "BlockScene.h"
#include "Graphics/GeneratedTextures.h"
#include "ClientPlayer.h"

/**********************************ModleContainer Begin*****************************************/
ActorBodyModleBaseContainer::ActorBodyModleBaseContainer():m_FX(NULL), m_Model(NULL), m_body(NULL), m_bUsePackingFCM(false), m_animId(100101), m_animTick(0), m_version(-1), m_iCheckCustomModelBindTick(0), m_set(false)
{
	m_bodyRotate = Rainbow::Vector3f::zero;
	m_bodyOffset = Rainbow::Vector3f::zero;
	m_bodyScale = 1.0f;
	m_NeedTick = true;
}

ActorBodyModleBaseContainer::ActorBodyModleBaseContainer(WCoord blockPos) : WorldContainer(blockPos, 0), m_FX(NULL), m_Model(NULL), m_body(NULL), m_bUsePackingFCM(false), m_animId(100101), m_animTick(0), m_version(-1), m_iCheckCustomModelBindTick(0), m_set(false)
{
	m_bodyRotate = Rainbow::Vector3f::zero;
	m_bodyOffset = Rainbow::Vector3f::zero;
	m_bodyScale = 1.0f;
	m_NeedTick = true;
}

ActorBodyModleBaseContainer::~ActorBodyModleBaseContainer()
{
	if (m_body)
	{
		m_body->clearEntity();
		ENG_DELETE(m_body);
	}
	if (m_World && m_World->getEffectMgr())
	{
		m_World->getEffectMgr()->removeEffect(m_Model);
		m_World->getEffectMgr()->removeEffect(m_FX);
	}
	if (m_Model)
	{
		m_Model->setNeedClear();
		m_Model = NULL;
	}
	if (m_FX)
	{
		m_FX->setNeedClear();
		m_FX = NULL;
	}
}

bool ActorBodyModleBaseContainer::load(const void* srcdata)
{
	if (!srcdata)
	{
		return false;
	}
	auto src = reinterpret_cast<const FBSave::ContainerActorBodyModelBase*>(srcdata);
	if (!loadContainerCommon(src->basedata()))
	{
		return false;
	}
	m_version = src->version();
	m_animId = src->animId();
	m_animTick = src->animTick();
	m_headBoneId = src->headBoneId();
	m_headScale = src->headScale();
	m_mutateMob = src->mutateMob();
	if (src->customSkins())
	{
		m_customSkins = src->customSkins()->c_str();
	}
	auto skinShow = src->skinShow();
	if (skinShow)
	{
		size_t skinSize = skinShow->size();
		m_skinShow.clear();
		m_skinShow.resize(skinSize);
		for (int i = 0; i < skinSize; i++)
		{
			m_skinShow[i] = skinShow->Get(i)->c_str();
		}
	}
	auto bodyRotate = src->bodyRotate();
	if (bodyRotate)
	{
		Rainbow::Vector3f bodyEulgr(bodyRotate->x(), bodyRotate->y(), bodyRotate->z());
		m_bodyRotate = bodyEulgr;
	}
	auto bodyOffset = src->bodyOffset();
	if (bodyOffset)
	{
		Rainbow::Vector3f tempVec3(bodyOffset->x(), bodyOffset->y(), bodyOffset->z());
		m_bodyOffset = tempVec3;
	}
	auto headRotate = src->headRotate();
	if (headRotate)
	{
		Rainbow::Vector3f headEulgr(headRotate->x(), headRotate->y(), headRotate->z());
		m_headRotate = Rainbow::EulerToQuaternionf(headEulgr);
	}
	m_bodyScale = src->bodyScale();
	auto equipInfos = src->equipInfo();
	if (equipInfos)
	{
		size_t equipSize = equipInfos->size();
		for (int i = 0; i < equipSize; i++)
		{
			auto p = equipInfos->Get(i);
			if (p)
			{
				m_equipGrip[i].load(p);
			}
		}
	}
	return true;
}

//flatbuffers::Offset<FBSave::ChunkContainer> ActorBodyModleBaseContainer::save(SAVE_BUFFER_BUILDER& builder)
//{
//	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerActorBodyModelBase, saveModel(builder).Union());
//}

flatbuffers::Offset<FBSave::ContainerActorBodyModelBase> ActorBodyModleBaseContainer::saveModel(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);
	std::vector<flatbuffers::Offset<flatbuffers::String>> skinNames;
	for (auto& name : m_skinShow)
	{
		skinNames.push_back(builder.CreateString(name.c_str()));
	}
	std::vector<flatbuffers::Offset<FBSave::ItemGrid>> equipGrids;
	for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
	{
		equipGrids.push_back(m_equipGrip[i].save(builder));
	}
	std::vector<flatbuffers::Offset<FBSave::StretchForzenPlayerAvatarInfo>> avatarInfos;
	//Rainbow::Vector3f bodyEulgr = Rainbow::QuaternionToEuler(m_bodyRotate);
	Rainbow::Vector3f headEulgr = Rainbow::QuaternionToEuler(m_headRotate);
	auto actor = FBSave::CreateContainerActorBodyModelBase(builder, basedata, m_animId, m_animTick, m_version, 
		FBSave::CreateContainerEulerSave(builder, m_bodyOffset.x, m_bodyOffset.y, m_bodyOffset.z),
		FBSave::CreateContainerEulerSave(builder, m_bodyRotate.x, m_bodyRotate.y, m_bodyRotate.z), 
		m_bodyScale,
		FBSave::CreateContainerEulerSave(builder, headEulgr.x, headEulgr.y, headEulgr.z), 
		m_headBoneId, m_headScale, m_mutateMob, 
		builder.CreateString(m_customSkins.c_str()), builder.CreateVector(skinNames), 
		builder.CreateVector(equipGrids));
	return actor;
}

void ActorBodyModleBaseContainer::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	initBody();
	registerUpdateTick();
}

void ActorBodyModleBaseContainer::leaveWorld()
{
	if (m_body)
	{
		m_body->clearEntity();
		ENG_DELETE(m_body);
	}
	if (m_World && m_World->getEffectMgr())
	{
		m_World->getEffectMgr()->removeEffect(m_Model);
		m_World->getEffectMgr()->removeEffect(m_FX);
	}
	if (m_Model)
	{
		m_Model->setNeedClear();
		m_Model = NULL;
	}
	if (m_FX)
	{
		m_FX->setNeedClear();
		m_FX = NULL;
	}
	WorldContainer::leaveWorld();
}

void ActorBodyModleBaseContainer::updateTick(void)
{
	if (!m_World)
	{
		return;
	}
#ifndef IWORLD_SERVER_BUILD
	//if (m_mobModel && m_mobModel->getEntity() && m_WaitSyncModelDataList.size() > 0)
	//{
	//	m_iCheckCustomModelBindTick++;
	//	if (m_iCheckCustomModelBindTick >= 20)
	//	{
	//		m_iCheckCustomModelBindTick = 0;
	//		CustomModelMgr::updateSyncModelData(m_mobModel->getEntity(), m_WaitSyncModelDataList, m_bUsePackingFCM);
	//	}
	//}
	if (m_body)
	{
		resetModel(&m_Model);

		if (m_body)
		{
			// 处理武器的明暗变化
			Rainbow::Model* pWeaponModel = GetWeaponObjModel();
			if (nullptr != pWeaponModel)
			{
				WCoord center = getCenterPosition();
				Rainbow::Vector4f lightparam(0, 0, 0, 0);
				if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(center));
				pWeaponModel->SetInstanceData(lightparam);
				pWeaponModel->SetInstanceAmbient(Rainbow::ColourValue::ZERO);
			}
			 
		}
	}
	if (m_Model)
	{
		if (m_Model->getModel())
		{
			WCoord center = getCenterPosition();
			Rainbow::Vector4f lightparam(0, 0, 0, 0);
			if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(center));
			m_Model->getModel()->SetInstanceData(lightparam);
			m_Model->getEntity()->SetInstanceAmbient(Rainbow::ColourValue::ZERO);
		}
		else if(m_Model->getEntity())
		{
			WCoord center = getCenterPosition();
			Rainbow::Vector4f lightparam(0, 0, 0, 0);
			if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(center));
			m_Model->getEntity()->SetInstanceData(lightparam);
			m_Model->getEntity()->SetInstanceAmbient(Rainbow::ColourValue::ZERO);
		}
	}

#endif
}

static std::string getRuneEffect(BackPackGrid* grid, bool& isLightning) {
	std::string ret;
	isLightning = false;
	if (!grid)
		return ret;
	const GridRuneData& rune = grid->getRuneData();
	//只取一个最高等级的
	int highestLevel = 0;
	for (int i = 0; i < rune.getRuneNum(); i++)
	{
		const GridRuneItemData& runeitem = rune.getItemByIndex(i);
		int runelevel = runeitem.rune_id % 100;
		if (runelevel >= highestLevel) {
			highestLevel = runelevel;
			auto* runDef = GetDefManagerProxy()->getRuneDef(runeitem.rune_id);
			if (runDef)
			{
				ret = runDef->effect;
				if (runDef->EnchantType == ENCHANT_LIGHTNING_CHAIN) {
					isLightning = true;
				}
			}
			if (ret.empty())
			{
				ret = getHandRuneEffectName(runeitem.item_id);
				isLightning = false;
			}
		}
	}
	return ret;
}

void ActorBodyModleBaseContainer::resetModel(EffectModel** pmodel)
{
	if (!m_body)
	{
		return;
	}
	if (m_body->IsRebuildModel())
	{
		m_set = false;
	}
	if (m_set)
	{
		return;
	}
	World* pworld = m_World;
	if (!pworld)
	{
		return;
	}
	WCoord center = getCenterPosition();
	Rainbow::Entity* entity = m_body->getEntity();
	if (entity && entity->GetMainModel() != NULL)
	{
		m_set = true;
	/*	if (m_mobId > 0 && GetDefManagerProxy()->isStoreHorseById(m_mobId))
		{
			HorseActorBody* body = dynamic_cast<HorseActorBody*>(m_body);
			if (body)
			{
				auto def = GetDefManagerProxy()->getHorseDef(m_mobId);
				body->setEquipItemNoOwner(EQUIP_HEAD, 0, def);
				body->setEquipItemNoOwner(EQUIP_BREAST, 0, def);
				body->setEquipItemNoOwner(EQUIP_LEGGING, 0, def);
			}
		}
		else*/
		{
			//穿装备
			/*for (auto& p : m_equipInfo)
			{
				m_body->setEquipItem((EQUIP_SLOT_TYPE)p.slot, p.itemid);
			}*/
			for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
			{
				if (m_equipGrip[i].getItemID() > 0)
				{
					m_body->setEquipItem((EQUIP_SLOT_TYPE)i, m_equipGrip[i].getItemID());
				}
			}
		}

		WCoord pos(m_bodyOffset.x * BLOCK_SIZE, m_bodyOffset.y * BLOCK_SIZE,
					m_bodyOffset.z * BLOCK_SIZE);
		pos += center;
		if (nullptr == m_Model)
		{
			*pmodel = ENG_NEW(EffectModel)(m_World, entity, pos, 0);
		}
		else
		{
			*pmodel = m_Model;
			entity->SetPosition(pos.toWorldPos());
		}


		EffectModel* curModel = *pmodel;
		if (curModel == NULL)
		{
			return;
		}

		curModel->setEntityLightPos(center, true);

		Rainbow::Model* cmodel = curModel->getModel();
		if (cmodel == NULL && curModel->getEntity() != NULL)
		{
			cmodel = curModel->getEntity()->GetMainModel();
		}
		if (cmodel)
		{
			//calculate instancedata
			Rainbow::Vector4f lightparam(0, 0, 0, 0);
			m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(center));
			m_set = true;
			curModel->playAnim(m_animId);

			ActorBody::clearEquipItems(curModel->getModel());

			//pworld->getEffectMgr()->addEffect(m_FX);
			pworld->getEffectMgr()->addEffect(curModel);

			auto pWeaponObj = m_body->getWeaponModel();
			if (nullptr != pWeaponObj)
			{
				if (pWeaponObj->IsKindOf<Rainbow::Model>())
				{
					pWeaponObj->SetInstanceData(lightparam);
				}
				else if (pWeaponObj->IsKindOf<Rainbow::Entity>())
				{
					pWeaponObj->SetInstanceData(lightparam);
				}
				else if (pWeaponObj->IsKindOf<ModelItemMesh>())
				{
					ModelItemMesh* itemMesh = static_cast<ModelItemMesh*>(pWeaponObj);
					itemMesh->setInstanceData(lightparam);
				}
			}

			auto pHelmetObj = m_body->getHelmetModelModel();
			if (nullptr != pHelmetObj)
			{
				if (pHelmetObj->IsKindOf<Rainbow::Model>())
				{
					static_cast<Rainbow::Model*>(pHelmetObj)->SetInstanceData(lightparam);
				}
				else if (pHelmetObj->IsKindOf<Rainbow::Entity>())
				{
					static_cast<Rainbow::Entity*>(pHelmetObj)->SetInstanceData(lightparam);
				}
				else if (pHelmetObj->IsKindOf<ModelItemMesh>())
				{
					ModelItemMesh* itemMesh = static_cast<ModelItemMesh*>(pHelmetObj);
					itemMesh->setInstanceData(lightparam);
				}

			}
			for (auto& skinName : m_skinShow)
			{
				cmodel->ShowSkin(skinName.c_str(), false);
			}
			//cmodel->SetRotation(m_bodyRotate);
			curModel->setScale(m_bodyScale);

			//骨骼旋转
			Rainbow::SkeletonComponent* skeleton = cmodel->GetComponent<Rainbow::SkeletonComponent>();
			if (skeleton && m_headBoneId >= 0)
			{
				skeleton->SetBoneRotate(m_headBoneId, &m_headRotate, m_headScale);
			}
			//curModel->update(0);
			if (cmodel->IsKindOf<Rainbow::ModelLegacy>())
			{
				curModel->setYaw(-m_bodyRotate.y);
				Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(cmodel);
				//更新动画
				Rainbow::AnimationPlayer* anim = legacymodel->GetAnimPlayer();
				if (anim)
				{
					Rainbow::AnimPlayTrack* track = anim->findPlayTrack(m_animId);
					if (track)
					{
						track->m_CurTick = track->m_Start + m_animTick;
						track->m_Weight = 1;
					}
					anim->Tick(0);
					anim->SetTimeScale(0);
					anim->SetPause(true);
				}

				entity->SetTimeScale(0);
				if (legacymodel->GetModelData())
				{
					m_bounding = legacymodel->GetModelData()->m_Bounding;
				}
			}
			else if(cmodel->IsKindOf<Rainbow::ModelNew>())
			{
				curModel->setYaw(-m_bodyRotate.y);
				Rainbow::ModelNew* newModel = static_cast<Rainbow::ModelNew*>(cmodel);
				if (newModel)
				{
					//newModel->PlayAnim(m_animId, 1.0f, 0.0f);
					newModel->SetTimeScale(0);
				}

				entity->SetTimeScale(0);
			}
		}
		updateWeaponEffect();
	}
	m_body->setRebuildModel(false);
}

void ActorBodyModleBaseContainer::updateWeaponEffect()
{
	//武器模型有附魔特效
	if (m_body && m_set)
	{
		auto& grid = m_equipGrip[EQUIP_WEAPON];
		if (grid.getRuneData().getRuneNum() > 0) {//有符文  走符文逻辑
			bool isLightning = false;
			float scale = 1.0f;
			std::string runeEffectName = getRuneEffect(&grid, isLightning);
			if (runeEffectName.size() > 0) {
				if (isLightning)
				{
					scale = GetLuaInterfaceProxy().get_lua_const()->lightningWeaponEffectScale;
				}
				float fpsScale = scale * 1.5f;
				//if (bBodyNeedPlay && body)
				{
					Rainbow::MovableObject* weaponmodel = m_body->getWeaponModel();
					if (weaponmodel)
					{
						dynamic_cast<BaseItemMesh*>(weaponmodel)->playMotion(0,runeEffectName.c_str(), true, 10000, scale);
					}
				}

				//if (bCameraNeedPlay)
				//	g_pPlayerCtrl->m_CameraModel->playWeaponMotion(runeEffectName.c_str(), true, 10000, fpsScale);
			}
		}
	}
}

Rainbow::Model* ActorBodyModleBaseContainer::GetWeaponObjModel()
{
	if (!m_body)
	{
		return nullptr;
	}

	// 武器也需要变成雕像材质
	Rainbow::MovableObject* pWeaponObj = m_body->getWeaponModel();
	Rainbow::Model* pWeaponModel = nullptr;
	if (nullptr != pWeaponObj)
	{
		if (pWeaponObj->IsKindOf<Rainbow::Model>())
		{
			pWeaponModel = static_cast<Rainbow::Model*>(pWeaponObj);
		}
		else if (pWeaponObj->IsKindOf<Rainbow::Entity>())
		{
			Rainbow::Entity* pEntity = static_cast<Rainbow::Entity*>(pWeaponObj);
			pWeaponModel = pEntity->GetMainModel();
		}
		else
		if (pWeaponObj->IsKindOf<ModelItemMesh>())
		{
			ModelItemMesh* itemMesh = static_cast<ModelItemMesh*>(pWeaponObj);
			pWeaponModel = itemMesh->GetModel();
		}
	}

	return pWeaponModel;
}


void ActorBodyModleBaseContainer::changeEquip(const BackPackGrid* grid, int slot)
{
	if (grid != NULL && slot >= 0 && slot < MAX_EQUIP_SLOTS)
	{
		m_equipGrip[slot] = *grid;
		if (m_body && m_set)
		{
			if (slot == EQUIP_PIFENG)
			{
				m_body->equipPifengSpecial(EQUIP_PIFENG, m_equipGrip[slot].getItemID());
			}
			else
			{
				m_body->setEquipItem((EQUIP_SLOT_TYPE)slot, m_equipGrip[slot].getItemID());
			}
			updateWeaponEffect();
		}
	}
}

void ActorBodyModleBaseContainer::refreshEquip()
{
	for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
	{
		if (m_body && m_set)
		{
			if (i == EQUIP_PIFENG)
			{
				m_body->equipPifengSpecial(EQUIP_PIFENG, m_equipGrip[i].getItemID());
			}
			else
			{
				m_body->setEquipItem((EQUIP_SLOT_TYPE)i, m_equipGrip[i].getItemID());
			}
		}
	}
	updateWeaponEffect();
}

void ActorBodyModleBaseContainer::dropItems()
{
	if (!m_World || !m_World->getActorMgr())
	{
		return;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_World->getActorMgr());
	if (!actorMgr) return;
	for (size_t i = 0; i < MAX_EQUIP_SLOTS; i++)
	{
		BackPackGrid& itemgrid = m_equipGrip[i];

		if (!itemgrid.isEmpty())
		{
			WCoord pos;
			ClientItem* item = NULL;
			{

				pos = m_BlockPos * BLOCK_SIZE + WCoord(GenRandomInt(10, 90), GenRandomInt(10, 90), GenRandomInt(10, 90));
				item = actorMgr->spawnItem(pos, itemgrid);
			}

			if (item)
			{
				ActorLocoMotion* locmove = item->getLocoMotion();
				if (!locmove)
				{
					return;
				}
				float scale = 0.05f;
				locmove->m_Motion.x = GenGaussian() * scale;
				locmove->m_Motion.y = GenGaussian() * scale + 0.2f;
				locmove->m_Motion.z = GenGaussian() * scale;
			}
		}
		itemgrid.clear();
	}
}

void ActorBodyModleBaseContainer::setAnimId(int id)
{
	m_animId = id;
}

void ActorBodyModleBaseContainer::setAnimTick(UInt32 tick)
{
	m_animTick = tick;
}

void ActorBodyModleBaseContainer::setBodyRotate(int dir)
{
	m_bodyRotate = Rainbow::Vector3f::zero;
	if (dir == DIR_NEG_X)
	{
		m_bodyRotate.y = 90;
	}
	else if (dir == DIR_POS_X)
	{
		m_bodyRotate.y = 270;
	}
	else if (dir == DIR_NEG_Z)
	{
		m_bodyRotate.y = 180;
	}
	else if (dir == DIR_POS_Z)
	{
		m_bodyRotate.y = 0;
	}
}

void ActorBodyModleBaseContainer::setOffset(Rainbow::Vector3f offset)
{
	m_bodyOffset = offset;
}

void ActorBodyModleBaseContainer::setScale(float scale)
{
	m_bodyScale = scale;
}

/**********************************ModleMobContainer Begin*****************************************/
ModleMobContainer::ModleMobContainer() :m_mobId(-1)
{
	m_mobType = 0;
	m_matType = MMMT_NONE;
	m_matIntensity = 1.0f;
	m_matProportion = 1.0f;
}

ModleMobContainer::ModleMobContainer(WCoord blockPos)
	: ActorBodyModleBaseContainer(blockPos), m_mobId(-1)
{
	m_mobType = 0;
	m_WaitChangeMat = false;
	m_matType = MMMT_NONE;
	m_matIntensity = 1.0f;
	m_matProportion = 1.0f;
}

ModleMobContainer::~ModleMobContainer()
{
	
}

bool ModleMobContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerMobBodyModel*>(srcdata);
	if (!src)
	{
		return false;
	}
	if (!ActorBodyModleBaseContainer::load(src->basedata()))
	{
		return false;
	}
	if (src->mobFb())
	{
		size_t fbsize = src->mobFb()->size();
		m_mobFb.resize(fbsize);
		memcpy(m_mobFb.data(), src->mobFb()->data(), fbsize);
	}
	m_mobId = src->mobId();
	m_mobType = src->mobType();
	m_matType = (MODEL_MOB_MAT_TYPE)src->matType();
	if (src->customMatcapTex())
	{
		m_customMatcapTex = src->customMatcapTex()->c_str();
	}
	if (src->customDiffuseTex())
	{
		m_customDiffuseTex = src->customDiffuseTex()->c_str();
	}
	m_matIntensity = src->matIntensity();
	m_matProportion = src->matProportion();

	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> ModleMobContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	auto actor = saveMobModel(builder);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerMobBodyModel, actor.Union());
}

flatbuffers::Offset<FBSave::ContainerMobBodyModel> ModleMobContainer::saveMobModel(SAVE_BUFFER_BUILDER& builder)
{
	auto base = saveModel(builder);
	return FBSave::CreateContainerMobBodyModel(builder, base, m_mobId, builder.CreateVector(m_mobFb.data(),
		m_mobFb.size()), m_mobType, (int)m_matType, builder.CreateString(m_customMatcapTex.c_str()),
		builder.CreateString(m_customDiffuseTex.c_str()), m_matIntensity, m_matProportion);
}

void ModleMobContainer::initBody()
{
#ifndef IWORLD_SERVER_BUILD
	World* pworld = m_World;
	if (m_World == NULL)
	{
		return;
	}
	if (!pworld->getEffectMgr())
	{
		return;
	}
	if (!pworld->getActorMgr())
	{
		return;
	}
	if (m_mobId >= 0)
	{
		const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(m_mobId);
		if (def == NULL)
		{
			return;
		}
		if (GetDefManagerProxy()->isStoreHorseById(m_mobId))
		{
			m_body = ENG_NEW(HorseActorBody)(NULL);
		}
		else
		{
			m_body = ENG_NEW(ActorBody)(NULL);
		}
		ClientMob::initMobBody(m_body, def, false, false);

		if (m_body)
		{
			m_headRotate = Rainbow::Quaternionf::identity;
			m_headBoneId = -1;
			m_headScale = 1.0f;

			if (m_matType != MMMT_NONE)
			{
				m_WaitChangeMat = true;
			}

			// 如果是野人，装备棒子
			if (m_mobId == 3101)
			{
				const ItemDef* itemdef = GetDefManagerProxy()->getItemDef(12001);
				if (itemdef)
				{
					m_equipGrip[EQUIP_WEAPON].def = itemdef;
				}
			}
			// 毒尾蝎显示尾巴
			else if(m_mobId == 3824)
			{
				if (m_body)
				{
					m_body->showNecklace(1);
				}
			}
		}
	}
#endif
}

bool ModleMobContainer::setMobInfo(ClientMob* mob)
{
	if (GetClientInfoProxy())
	{
		m_version = GetClientInfoProxy()->GetClientVersion();
	}
	if (mob != NULL)
	{
		m_mobId = mob->getMonsterId();

		int mobObjType = mob->getObjType();
		if (mobObjType == OBJ_TYPE_ACTOR_PET)
			m_mobType = 2;
		else if (mobObjType == OBJ_TYPE_HOMELAND_LIVES)
			m_mobType = 1;

		MonsterDef* monsterDef = GetDefManagerProxy()->getMonsterDef(m_mobId);
		if (monsterDef == NULL)
		{
			return false;
		}
		//if (monsterDef->Type == MOB_BOSS)
		//{
		//	return false;
		//}
		flatbuffers::FlatBufferBuilder builder;
		flatbuffers::Offset<FBSave::SectionActor> fbActor = mob->save(builder);
		builder.Finish(fbActor);
		int size = builder.GetSize();
		m_mobFb.resize(size);
		memcpy(m_mobFb.data(), builder.GetBufferPointer(), size);
		if (mob->getBody())
		{
			m_animId = mob->getBody()->getNowPlaySeqID();
			Rainbow::Model* cmodel = mob->getBody()->getModel();

			if (cmodel)
			{
				if (cmodel->IsKindOf<Rainbow::ModelLegacy>())
				{
					Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(cmodel);
					if (legacymodel && legacymodel->GetAnimPlayer())
					{
						Rainbow::AnimationPlayer* anim = legacymodel->GetAnimPlayer();
						Rainbow::AnimPlayTrack* track = anim->findPlayTrack(m_animId);
						if (track)
						{
							m_animTick = track->m_CurTick - track->m_Start;
						}
					}
				}
				m_skinShow.clear();
				//保存哪些子模型没显示
				int skinNum = cmodel->GetModelMeshRendererNum();
				for (int i = 0; i < skinNum; i++)
				{
					auto render = dynamic_cast<Rainbow::MeshInstance*>(cmodel->GetIthModelMeshRenderer(i));
					if (render != NULL && !render->isShow())
					{
						m_skinShow.push_back(render->getName().c_str());
					}
				}
				//骨骼旋转
				Rainbow::SkeletonComponent* skeleton = cmodel->GetComponent<Rainbow::SkeletonComponent>();
				if (skeleton)
				{
					m_headBoneId = skeleton->m_RotateBoneID;
					m_headRotate = skeleton->m_BoneRotate;
					m_headScale = skeleton->m_BoneScale;
				}
				if (mob->getBody()->getEntity())
				{
					m_bodyRotate = Rainbow::QuaternionToEulerAngle(mob->getBody()->getEntity()->GetRotation());
				}
			}
		}
		//拿取装备信息
		LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(mob->getAttrib());
		if (attrib != NULL)
		{
			int itemId = 0;
			//m_equipInfo.reserve(MAX_EQUIP_SLOTS);
			for (char i = 0; i < MAX_EQUIP_SLOTS; i++)
			{
				BackPackGrid* grid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)i);
				m_equipGrip[i] = *grid;
			}
		}
	/*	Rainbow::GetMusicManager().PlaySound("sounds/misc/freeze.ogg", mob->getPosition().toVector3(), 1, 1);
		ActorHorse* horse = dynamic_cast<ActorHorse*>(mob);
		if (horse != NULL)
		{
			IPlayerControl* player = dynamic_cast<IPlayerControl*>(horse->getRiddenByActor());
			if (player != NULL)
			{
				player->dismountActor();
			}
		}*/
		return true;
	}
	return false;
}

bool ModleMobContainer::setMobInfo(int mobId)
{
	m_mobId = mobId;
	return true;
}

void ModleMobContainer::setMatType(int type)
{
	m_matType = (MODEL_MOB_MAT_TYPE)type;
}

void ModleMobContainer::setCustomMatcapTex(const string& name)
{
	m_customMatcapTex = name;
}

void ModleMobContainer::setCustomDiffuseTex(const string& name)
{
	m_customDiffuseTex = name;
}

void ModleMobContainer::setMatIntensity(float val)
{
	m_matIntensity = val;
}

void ModleMobContainer::setMatProportion(float val)
{
	m_matProportion = val;
}

void ModleMobContainer::enterWorld(World* pworld)
{
	ActorBodyModleBaseContainer::enterWorld(pworld);

	registerUpdateTick();
}

void ModleMobContainer::updateTick()
{
	ActorBodyModleBaseContainer::updateTick();

	if (!m_set)
	{
		return;
	}

	if (m_WaitChangeMat && m_body && m_body->isLoadModelFinished())
	{
		m_WaitChangeMat = false;
		// 关闭预制模型挂在骨骼上的各种特效
		auto model = m_body->getModel();
		if (model->IsKindOf<Rainbow::ModelNew>())
		{
			Rainbow::ModelNew* newModel = static_cast<Rainbow::ModelNew*>(model);
			Rainbow::SkinnedSkeleton* skeleton = newModel->GetskinnedSkeleton();
			if (skeleton)
			{
				auto attachments = skeleton->GetBoneAttachments();
				for (auto& attach : attachments)
				{
					if (attach.m_GameObject != nullptr)
					{
						attach.m_GameObject->SetActive(false);
					}
				}
			}
		}
		updateMaterialByMatType();
	}
}

void ModleMobContainer::resetModel(EffectModel** pmodel)
{
	ActorBodyModleBaseContainer::resetModel(pmodel);
	if (m_set)
	{
		if (m_mobId > 0 && GetDefManagerProxy()->isStoreHorseById(m_mobId))
		{
			HorseActorBody* body = dynamic_cast<HorseActorBody*>(m_body);
			if (body)
			{
				auto def = GetDefManagerProxy()->getHorseDef(m_mobId);
				body->setEquipItemNoOwner(EQUIP_HEAD, 0, def);
				body->setEquipItemNoOwner(EQUIP_BREAST, 0, def);
				body->setEquipItemNoOwner(EQUIP_LEGGING, 0, def);
			}
		}
	}
}

void ModleMobContainer::dropItems()
{
	// 雕像不应该掉落物品
	if (m_matType == MMMT_MONSTERSTATUE)
	{
		return;
	}

	ActorBodyModleBaseContainer::dropItems();
}

void ModleMobContainer::updateMaterialByMatType()
{
	std::string texPath;
	switch (m_matType)
	{
	case MMMT_MONSTERSTATUE:
		m_body->updateMaterial("Materials/MiniGame/MaterialTemplate/Character_NPR/Character_NPR_Opaque.templatemat");
		m_body->setCustomMatKeyword("ENABLE_MATCAP", true);
		m_body->setCustomMatFloat("_FlipY", 1.0);
		m_body->setCustomMatFloat("_process_maintex_BW", 0.0);
		m_body->setCustomMatFloat("_matcap_intensity", m_matIntensity);
		m_body->setCustomMatFloat("_matcap_brightness", m_matProportion);

		if (!m_customMatcapTex.empty())
		{
			texPath = "Resources/minigame/blocks/" + m_customMatcapTex;
			m_body->setCustomTexture("_matcap_texture", texPath.c_str());
		}

		if (!m_customDiffuseTex.empty())
		{
			if (m_customDiffuseTex.compare("default") == 0)
			{
				Rainbow::SharePtr<Rainbow::Texture2D> texture = Rainbow::NativeToSharePtr<Rainbow::Texture2D>(Rainbow::GetWhiteTexture());
				m_body->setCustomTexture("g_DiffuseTex", texture);
			}
			else
			{
				texPath = "Resources/minigame/blocks/" + m_customDiffuseTex;
				m_body->setCustomTexture("g_DiffuseTex", texPath.c_str());
			}
		}

		// 武器也需要变成雕像材质
		Rainbow::Model* pWeaponModel = GetWeaponObjModel();
		if (nullptr != pWeaponModel)
		{
			updateOneModelMaterialByMatType(pWeaponModel);
		}

		break;
	}
}

void ModleMobContainer::updateOneModelMaterialByMatType(Rainbow::Model* pModel)
{
	if (!pModel)
	{
		return;
	}

	Rainbow::SharePtr<Rainbow::FMaterial> pMtl = Rainbow::GetMaterialManager().LoadFromFile("Materials/MiniGame/MaterialTemplate/Character_NPR/Character_NPR_Opaque.templatemat");
	if (!pMtl)
		return;

	int iNum = pModel->GetModelMeshRendererNum();
	Rainbow::IModelMeshRenderer* pMeshRenderer = nullptr;
	auto pMtlInstance = pMtl->CreateInstance();
	std::string texPath;
	for (int i = 0; i < iNum; i++)
	{
		pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
		if (!pMeshRenderer)
			continue;

		int iSubNum = pMeshRenderer->GetSubMeshNum();
		for (int j = 0; j < iSubNum; j++)
		{
			pMeshRenderer->UpdateSubMeshMaterial(j, pMtlInstance);
			pMeshRenderer->SetKeyword("ENABLE_MATCAP", true);
			pMeshRenderer->SetFloat("_FlipY", 1.0);
			pMeshRenderer->SetFloat("_process_maintex_BW", 0.0);
			pMeshRenderer->SetFloat("_matcap_intensity", m_matIntensity);
			pMeshRenderer->SetFloat("_matcap_brightness", m_matProportion);
		}

		if (!m_customMatcapTex.empty())
		{
			texPath = "Resources/minigame/blocks/" + m_customMatcapTex;
			// 允许为空
			Rainbow::SharePtr<Rainbow::Texture2D> pTexture2D = nullptr;
			pTexture2D = Rainbow::GetAssetManager().LoadAsset<Rainbow::Texture2D>(texPath.c_str());
			pMeshRenderer->SetTexture("_matcap_texture", pTexture2D);
		}
		if (!m_customDiffuseTex.empty())
		{
			if (m_customDiffuseTex.compare("default") == 0)
			{
				Rainbow::SharePtr<Rainbow::Texture2D> texture = Rainbow::NativeToSharePtr<Rainbow::Texture2D>(Rainbow::GetWhiteTexture());
				pMeshRenderer->SetTexture("g_DiffuseTex", texture);
			}
			else
			{
				texPath = "Resources/minigame/blocks/" + m_customDiffuseTex;
				// 允许为空
				Rainbow::SharePtr<Rainbow::Texture2D> pTexture2D = nullptr;
				pTexture2D = Rainbow::GetAssetManager().LoadAsset<Rainbow::Texture2D>(texPath.c_str());
				pMeshRenderer->SetTexture("g_DiffuseTex", pTexture2D);
			}
		}
	}
}

/****************************************ModlePlayerContainer*****************************************************/

ModlePlayerContainer::ModlePlayerContainer():m_uin(-1),m_playerIndex(-1)
{

}

ModlePlayerContainer::ModlePlayerContainer(WCoord blockPos): ActorBodyModleBaseContainer(blockPos), m_uin(-1), m_playerIndex(-1)
{

}

ModlePlayerContainer::~ModlePlayerContainer()
{

}

bool ModlePlayerContainer::load(const void* srcdata)
{
	if (!srcdata)
	{
		return false;
	}
	if (!ActorBodyModleBaseContainer::load(srcdata))
	{
		return false;
	}
	auto src = reinterpret_cast<const FBSave::ContainerPlayerBodyModel*>(srcdata);
	auto avatarInfos = src->avatarInfo();
	if (avatarInfos)
	{
		size_t avatarSize = avatarInfos->size();
		m_avatarInfo.clear();
		m_avatarInfo.resize(avatarSize);
		for (int i = 0; i < avatarSize; i++)
		{
			auto p = avatarInfos->Get(i);
			if (p)
			{
				m_avatarInfo[i].part = p->part();
				m_avatarInfo[i].modelId = p->modelid();
			}
		}
	}
	m_uin = src->playerUin();
	m_playerIndex = src->playerIndex();
	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> ModlePlayerContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	auto base = saveModel(builder);
	std::vector<flatbuffers::Offset<FBSave::ContainerPlayerAvatarInfo>> avatarInfos;
	for (auto& info : m_avatarInfo)
	{
		avatarInfos.push_back(FBSave::CreateContainerPlayerAvatarInfo(builder, info.part, info.modelId));
	}
	auto actor = FBSave::CreateContainerPlayerBodyModel(builder, base, m_uin, m_playerIndex, builder.CreateVector(avatarInfos));
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerMobBodyModel, saveModel(builder).Union());
}

void ModlePlayerContainer::initBody()
{
#ifndef IWORLD_SERVER_BUILD
	World* pworld = m_World;
	if (m_World == NULL)
	{
		return;
	}
	if (!pworld->getEffectMgr())
	{
		return;
	}
	if (!pworld->getActorMgr())
	{
		return;
	}

	m_body = ENG_NEW(ActorBody)(NULL);
	m_body->initPlayer(m_playerIndex, m_mutateMob, m_customSkins.c_str());
	//初始化avatar
	for (auto& p : m_avatarInfo)
	{
		m_body->addAvatarPartModel(p.modelId, p.part,true);
	}
#endif
}

bool ModlePlayerContainer::setPlayerInfo(IClientPlayer* player)
{
	if (GetClientInfoProxy())
	{
		m_version = GetClientInfoProxy()->GetClientVersion();
	}
	ClientPlayer* playerTmp = player->GetPlayer();
	if (playerTmp && playerTmp->getBody())
	{
		m_uin = playerTmp->getUin();
		ActorBody* playerBody = playerTmp->getBody();
		m_playerIndex = playerBody->getPlayerIndex();
		m_mutateMob = playerBody->getMutateMob();
		m_customSkins = playerBody->getCustomSkins();
		if (playerBody->getEntity() && playerBody->getEntity()->GetMainModel())
		{
			Rainbow::Model* cmodel = playerBody->getEntity()->GetMainModel();
			if (cmodel)
			{
				if (cmodel->IsKindOf<Rainbow::ModelLegacy>())
				{
					Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(cmodel);
					if (legacymodel && legacymodel->GetAnimPlayer())
					{
						Rainbow::AnimationPlayer* anim = legacymodel->GetAnimPlayer();
						Rainbow::AnimPlayTrack* track = anim->findPlayTrack(m_animId);
						if (track)
						{
							m_animTick = track->m_CurTick - track->m_Start;
						}
					}
				}
				m_skinShow.clear();
				//保存哪些子模型没显示
				int skinNum = cmodel->GetModelMeshRendererNum();
				for (int i = 0; i < skinNum; i++)
				{
					auto render = dynamic_cast<Rainbow::MeshInstance*>(cmodel->GetIthModelMeshRenderer(i));
					if (render != NULL && !render->isShow())
					{
						m_skinShow.push_back(render->getName().c_str());
					}
				}
				//骨骼旋转
				Rainbow::SkeletonComponent* skeleton = cmodel->GetComponent<Rainbow::SkeletonComponent>();
				if (skeleton)
				{
					m_headBoneId = skeleton->m_RotateBoneID;
					m_headRotate = skeleton->m_BoneRotate;
					m_headScale = skeleton->m_BoneScale;
				}
				m_bodyRotate = Rainbow::QuaternionToEulerAngle(playerBody->getEntity()->GetRotation());
			}
		}
		//拿取装备信息
		LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(playerTmp->getAttrib());
		if (attrib != NULL)
		{
			int itemId = 0;
			for (char i = 0; i < MAX_EQUIP_SLOTS; i++)
			{
				BackPackGrid* grid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)i);
				m_equipGrip[i] = *grid;
			}
		}
		//拿取avatar 信息
		{
			char json[500];
			memset(json, 0, sizeof(json));
			MINIW::ScriptVM::game()->callFunction("StretchForzenAddAvatar", "d>s", (double)m_uin, json);
			m_avatarInfo.clear();
			jsonxx::Object jsonobj;
			if (jsonobj.parse(json))
			{
				if (jsonobj.has<jsonxx::Array>("data"))
				{
					jsonxx::Array arr = jsonobj.get<jsonxx::Array>("data");
					int size = arr.size();
					for (int i = 0; i < size; i++)
					{
						jsonxx::Array skin = arr.get<jsonxx::Array>(i);
						if (skin.has<jsonxx::Number>(0) && skin.has<jsonxx::Number>(1))
						{
							int part = skin.get<jsonxx::Number>(0);
							if (playerBody->IsShowAvatar(part))
							{
								m_avatarInfo.push_back(ModelAvatarInfo(part, skin.get<jsonxx::Number>(1)));
							}
						}
					}
				}
			}
			else
			{
				assert(0);
			}
		}
		//player->onDie();
		return true;
	}
	return false;
}
/** **********************************************************************SimpleModleBaseContainer************************************************************************************ */
SimpleModleBaseContainer::SimpleModleBaseContainer()
{
	m_pEntity = nullptr;
	m_curAnimId = -1;
}

SimpleModleBaseContainer::SimpleModleBaseContainer(WCoord BlockPos, int id, int dir)
	:WorldContainer(BlockPos, 0), m_modelId(id)
{
	m_pEntity = nullptr;
	m_curAnimId = -1;
	m_bodyRotate = Rainbow::Vector3f::zero;
	if (dir == DIR_NEG_X)
	{
		m_bodyRotate.y = 90;
	}
	else if (dir == DIR_POS_X)
	{
		m_bodyRotate.y = 270;
	}
	else if (dir == DIR_NEG_Z)
	{

	}
	else if (dir == DIR_POS_Z)
	{
		m_bodyRotate.y = 180;
	}
}

void SimpleModleBaseContainer::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateDisplay();

#ifndef IWORLD_SERVER_BUILD	

	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
		Rainbow::Entity::Destory(m_pEntity);
	}

	m_pEntity = Rainbow::Entity::Create();
	
#if ENTITY_MODIFY_MODEL_ASYNC
	m_pEntity->LoadAsync(getModelName().c_str(),false);
#else
	Rainbow::Model* model = NULL;
	model = g_BlockMtlMgr.getModel(getModelName().c_str());
	if (!model) return;
	m_pEntity->Load(model);
#endif

	m_pEntity->ShowSkins(true);

	WCoord pos = m_BlockPos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2);

	m_pEntity->SetRotation(m_bodyRotate.y, m_bodyRotate.x, m_bodyRotate.z);
	m_pEntity->SetPosition(pos.toWorldPos());

	if (m_World)
		m_pEntity->AttachToScene(pworld->getScene());
#endif
}

std::string SimpleModleBaseContainer::getModelName()
{
	char path[100] = { 0 };
	sprintf(path, "itemmods/%d/body.omod", getModelId());
	return path;
}

int SimpleModleBaseContainer::getModelId()
{
	return m_modelId;
}

void SimpleModleBaseContainer::leaveWorld()
{
	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
	}
	WorldContainer::leaveWorld();
}

SimpleModleBaseContainer::~SimpleModleBaseContainer()
{
	Rainbow::Entity::Destory(m_pEntity);
}

void SimpleModleBaseContainer::updateDisplay(float dtime)
{
	// 补光照
	if (m_pEntity)
	{
		Rainbow::Vector4f lightparam(0, 0, 0, 0);
		auto wp = m_pEntity->GetWorldPosition();
		if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(wp));
		lightparam.x += 0.2f;
		lightparam.y += 0.2f;
		m_pEntity->SetInstanceData(lightparam);
		unsigned int dtick = Rainbow::TimeToTick(dtime);
		m_pEntity->Tick(dtick);
	}
}

void SimpleModleBaseContainer::playAnim(int animId, int times)
{
	if (m_pEntity)
	{
		if (m_curAnimId != animId)
		{
			m_curAnimId = animId;
			m_pEntity->StopAnim();
			m_pEntity->PlayAnim(animId, times);
		}
	}
}

void SimpleModleBaseContainer::stopAnim()
{
	if (m_pEntity)
	{
		m_pEntity->StopAnim();
	}
}

bool SimpleModleBaseContainer::hasAnimPlaying(int anim)
{
	if (m_pEntity)
	{
		return m_pEntity->HasAnimPlaying(anim);
	}
	return false;
}

void SimpleModleBaseContainer::playSound(World* pworld, const char* name)
{
	pworld->getEffectMgr()->playPosSound(m_BlockPos, name, 1.0, 1.0, false, false);
}

bool SimpleModleBaseContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerSimpleModelBase*>(srcdata);
	if (!loadContainerCommon(src->basedata()))
	{
		return false;
	}
	auto bodyRotate = src->bodyRotate();
	if (bodyRotate)
	{
		Rainbow::Vector3f bodyEulgr(bodyRotate->x(), bodyRotate->y(), bodyRotate->z());
		m_bodyRotate = bodyEulgr;
	}
	m_modelId = src->modelId();
	return true;
}

void SimpleModleBaseContainer::setRotation(const Rainbow::Vector3f& rotate)
{
	m_bodyRotate = rotate;
	if (m_pEntity)
	{
		m_pEntity->SetRotation(m_bodyRotate.y, m_bodyRotate.x, m_bodyRotate.z);
	}
}

flatbuffers::Offset<FBSave::ChunkContainer> SimpleModleBaseContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerSimpleModelBase(builder, basedata, m_modelId, FBSave::CreateContainerEulerSave(builder, m_bodyRotate.x, m_bodyRotate.y, m_bodyRotate.z));
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerSimpleModelBase, actor.Union());
}

flatbuffers::Offset<FBSave::ContainerSimpleModelBase> SimpleModleBaseContainer::saveSimpleModel(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	return FBSave::CreateContainerSimpleModelBase(builder, basedata, m_modelId, FBSave::CreateContainerEulerSave(builder, m_bodyRotate.x, m_bodyRotate.y, m_bodyRotate.z));
}