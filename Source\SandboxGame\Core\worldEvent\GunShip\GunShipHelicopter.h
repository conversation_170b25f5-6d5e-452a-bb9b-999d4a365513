#pragma once
#include "Math/Vector3f.h"
#include <string>
#include <vector>
#include "SandboxGame.h"

class ClientMob;

class EXPORT_SANDBOXGAME GunShipHelicopter {
public:
    enum class MovementMode {
        IDLE,           // 静止
        PATROL,         // 巡逻
        COMBAT,         // 战斗
        ESCAPE,         // 逃跑
        MOVE_TO_TARGET  // 移动到目标点
    };

    enum class WeaponType {
        MACHINE_GUN,    // 机枪
        ROCKET,         // 火箭弹
        CANNON          // 机炮
    };

    GunShipHelicopter(const Rainbow::Vector3f& spawnPosition);
    ~GunShipHelicopter();

    // 生命周期
    void Init();
    void Update(float deltaTime);
    void Destroy();

    Rainbow::Vector3f GetCurrentPosition();

private:
    Rainbow::Vector3f m_position;
    Rainbow::Vector3f m_targetPosition;

    float m_health;

    bool IsDestroyed();

    ClientMob* mob_actor;
    int m_monster_id;
    
};