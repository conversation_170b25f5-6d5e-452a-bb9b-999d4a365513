#ifndef __CONTAINER_ELECTRIC_ELEMENT_H__
#define __CONTAINER_ELECTRIC_ELEMENT_H__

#include "container_world.h"
#include "SandboxGame.h"

//电子元件的射出类型
enum ContainerElectricElement_LaserType
{
	//无射出 
	ContainerElectricLaserType_None,
	//预览射线
	ContainerElectricLaserType_Preview,
	//正常射线
	ContainerElectricLaserType_Normal,
};
#define ContainerElectricElementLaserTypeFun(type, mem) \
bool laserIs##type(int dir) \
{\
	if (dir < 0 || dir >= 6)  {assert(0); return false;} \
	return mem[dir] == ContainerElectricLaserType_##type;\
}
#define ContainerElectricElementWithDrawLaserDelay 1

class EXPORT_SANDBOXGAME ContainerElectricElement;
class ContainerElectricElement : public WorldContainer
{
public:
	ContainerElectricElement();
	ContainerElectricElement(const WCoord& blockpos, int baseindex = 0);
	ContainerElectricElement(const WCoord& blockpos, int dir, int power);
	virtual ~ContainerElectricElement() {};
	virtual int getObjType() const override;
	virtual FBSave::ContainerUnion getUnionType() override
	{
		return FBSave::ContainerUnion_ContainerElectricBaseSave;
	}
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual bool load(const void* srcdata);
	//emitBlockLaser 和 updateBlockLaser有区分,emitBlockLaser主要是用于无射出到射出, updateBlockLaser用于更新.大多数情况下2个函数行为一样
	virtual int emitBlockLaser(int delay = 0);
	virtual void updateBlockLaser(int delay = 0);
	virtual void withDrawBlockLaser(int delay = 0);
	virtual void leaveWorld() override;
	int emitBlockLaserByDir(int dir);
	void withDrawBlockLaserByDir(int dir, int delay = 0);
	virtual int getPower(int dir);
	virtual int getPower();
	//设置延时一定要确保好时机,比如回收光线设置power为0,最好和回收有一样的延迟,射出同理
	virtual void setPower(int power, int delay = 0);
	bool IsTickPower() { return m_TickPowerCount > 0; };
	//这个接口调用一般是要设置power为0,并且回收光线时所用的
	bool shouldWithDrawPower();
	void setOutPutDirOpen(int dir, bool open, bool needEmit = false);
	bool getOutPutDirOpen(int dir);
	virtual void updateTick() override;
	void updateBlockLaserByDir(int dir, int delay = 0);
	void blockRemoveWithDrawBlockLaser();
	ContainerElectricElementLaserTypeFun(None, m_CurLaserType);
	ContainerElectricElementLaserTypeFun(Preview, m_CurLaserType);
	ContainerElectricElementLaserTypeFun(Normal, m_CurLaserType);
	unsigned int getCurEmnitLaserType(int dir) 
	{ 
		if (dir < 0 || dir >= 6) { assert(0); return 0; } 
		return m_CurLaserType[dir]; 
	}
	virtual void enterWorld(World* pworld) override;
	virtual int getDelayRate() { return 0; };
	bool shouldEmitBlockLaser(int power, int& delay);
	//int emitBlockPreviewlaser(int dir);
	virtual void mechaRemoveCallBack() override;
	virtual void resetDataByRotation(int dir);
public:
	//一般是外部调用
	static void withDrawRangeBlockLaserByDir(const WCoord& startPos, int num, int dir, World* pworld);
	//根据方块当前能量power控制射线方块的渲染
	static void setRayContainerRenderDir(const int dir, const int power, const WCoord& pos, World* pworld);
	static void clearContainerRenderDir(const int dir, const WCoord& pos, bool needCreate, World* pworld);
protected:
	virtual void _setPower(int power);
	flatbuffers::Offset<FBSave::ContainerElectricBaseSave> saveElectricContainer(SAVE_BUFFER_BUILDER& builder);
	void setDirLaserLength(int dir, unsigned int num);
	int getDirLaserLength(int dir);
	//名义上的回收，只是当前位置能量设为0
	void _withDrawBlockLaserByDir(int dir);

public:
	int m_nDir;
	int m_nPower;
	unsigned int m_OpenDir;
	unsigned int m_EmitLength;
	int m_Tickcount[6];
	int m_TickDir;
	int m_TickWithDrawCount[6];
	int m_TickWithDrawDir;
	//这个成员是标记当前射出的射线类型, 有可能与container所应射出的射线的设定不符
	unsigned int m_CurLaserType[6];
	int m_blockId;
	int m_TickPowerCount;
	int m_TickPower;
	int m_TickResetPowerCount;
	int m_oldPower;
};
//分发器
class ContainerElectricSplitter : public ContainerElectricElement
{
public:
	ContainerElectricSplitter() :ContainerElectricElement()
	{
		initDir();
		m_NeedTick = true;
	}

	ContainerElectricSplitter(const WCoord& blockpos, int dir, int power) : ContainerElectricElement(blockpos, dir, power)
	{
		initDir();
		m_NeedTick = true;
	}
	virtual FBSave::ContainerUnion getUnionType() override
	{
		return FBSave::ContainerUnion_ContainerElectricSplitterSave;
	}
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata) override;
	//virtual int emitBlockLaser(int delay = 0) override;
	virtual void withDrawBlockLaser(int dealy = 0) override;
private:
	void initDir();
};

//延时器
class ContainerElectricDelay : public ContainerElectricElement
{
public:
	ContainerElectricDelay() :ContainerElectricElement()
	{
		m_Lever = 1;
		setOutPutDirOpen(m_nDir, true);
		m_NeedTick = true;
	}

	ContainerElectricDelay(const WCoord& blockpos, int dir, int power) : ContainerElectricElement(blockpos, dir, power)
	{
		m_Lever = 1;
		setOutPutDirOpen(m_nDir, true);
		m_NeedTick = true;
	}
	virtual FBSave::ContainerUnion getUnionType() override
	{
		return FBSave::ContainerUnion_ContainerElectricDelaySave;
	}
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata) override;
	virtual int emitBlockLaser(int delay = 0) override;
	virtual void withDrawBlockLaser(int dealy = 0) override;
	void addLever();
	virtual int getDelayRate();
private:
	void _emitBlockLaser();
public:
	unsigned int m_Lever;
};

//阻隔器
class ContainerElectricResister : public ContainerElectricElement
{
public:
	ContainerElectricResister() :ContainerElectricElement()
	{
		m_Lever = 0;
		setOutPutDirOpen(m_nDir, true);
	}

	ContainerElectricResister(const WCoord& blockpos, int dir, int power) : ContainerElectricElement(blockpos, dir, power)
	{
		m_Lever = 0;
		setOutPutDirOpen(m_nDir, true);
	}
	virtual FBSave::ContainerUnion getUnionType() override
	{
		return FBSave::ContainerUnion_ContainerElectricResisterSave;
	}
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata) override;
	void addLever();
	int getPower(int dir);
public:
	unsigned int m_Lever;
};

//计数器
class ContainerElectricCounter : public ContainerElectricElement
{
public:
	ContainerElectricCounter() : ContainerElectricElement()
	{
	}
	ContainerElectricCounter(const WCoord& blockpos, int dir, int power) : ContainerElectricElement(blockpos, dir, power)
	{
	}
	virtual FBSave::ContainerUnion getUnionType() override
	{
		return FBSave::ContainerUnion_ContainerElectricCounterSave;
	}
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata) override;
	virtual void updateTick() override;
	virtual void enterWorld(World* pworld) override;

	DirectionType calculateInputPort(DirectionType dir = DIR_NOT_INIT);
	DirectionType getInputDir1();
	DirectionType getInputDir2();
	void powerAddOne();
	bool isComplete();
	void ContainerUpdate();
	void setWithdrawPending(int interval) { m_withdrawTickCount = interval; }
	bool isWithdrawPending() { return m_withdrawTickCount > 0; }

public:
	DirectionType m_upDir{ DIR_NOT_INIT };
	DirectionType m_leftDir{ DIR_NOT_INIT };
	bool m_lastInputPort1State{ false };
	bool m_lastInputPort2State{ false };
	int m_withdrawTickCount{ 0 };
};

#endif