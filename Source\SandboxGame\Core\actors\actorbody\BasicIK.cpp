#include "BasicIK.h"



void BasicIK::SetIDData(const BasicIKData& Data)
{
	// 设置骨骼节点
	m_BoneA = Data._boneA;
	m_BoneB = Data._boneB;
	m_BoneC = Data._boneC;
	// 设置目标对象
	m_Effector = Data._effector;
	// 设置极向量
	m_PoleVectorRelativeToEffector = Data._poleVectorRelativeToEffector;
	// 设置骨骼长度
	m_BoneALength = Data._boneALength;
	m_BoneBLength = Data._boneBLength;
	// 设置权重
	m_Weight = Data._weight;
	// 设置拉伸参数
	m_bEnableStretch = Data._bEnableStretch;
	m_StretchStartRatio = Data._stretchStartRatio;
	m_StretchMaxRatio = Data._stretchMaxRatio;
	// 设置主旋转轴
	m_PrimaryAxis = Data._primaryAxis;
	// 设置次旋转轴
	m_SecondaryAxis = Data._secondaryAxis;
	// 设置次旋转轴权重
	m_SecondaryAxisWeight = Data._secondaryAxisWeight;
	// 初始化
	m_Initialize = true;
}
// 解决两骨骼IK问题
// 参数说明:
// RootPos: 根节点位置
// JointPos: 关节位置
// EndPos: 末端位置
// JointTarget: 关节目标位置(用于定义关节平面)
// Effector: 目标位置
// OutJointPos: 输出的关节位置
// OutEndPos: 输出的末端位置
// UpperLimbLength: 上臂长度
// LowerLimbLength: 下臂长度
// bAllowStretching: 是否允许拉伸
// StartStretchRatio: 开始拉伸的比例
// MaxStretchScale: 最大拉伸比例
void BasicIK::SolveTwoBoneIK(const Rainbow::Vector3f& RootPos, const Rainbow::Vector3f& JointPos, const Rainbow::Vector3f& EndPos, const Rainbow::Vector3f& JointTarget, const Rainbow::Vector3f& Effector, Rainbow::Vector3f& OutJointPos, Rainbow::Vector3f& OutEndPos, double UpperLimbLength, double LowerLimbLength, bool bAllowStretching, double StartStretchRatio, double MaxStretchScale)
{
	// 计算目标位置和方向
	Rainbow::Vector3f DesiredPos = Effector;  // 目标位置
	Rainbow::Vector3f DesiredDelta = DesiredPos - RootPos;  // 目标方向向量
	double DesiredLength = DesiredDelta.Length();  // 目标距离

	// 计算最大肢体长度
	double MaxLimbLength = LowerLimbLength + UpperLimbLength;

	// 处理目标位置与根节点重合的特殊情况
	Rainbow::Vector3f	DesiredDir;
	if (DesiredLength < kSmallNumber)
	{
		DesiredLength = kSmallNumber;
		DesiredDir = Rainbow::Vector3f(1, 0, 0);  // 默认方向
	}
	else
	{
		DesiredDir = DesiredDelta.GetNormalizedSafe();  // 归一化方向向量
	}

	// 计算关节目标方向，用于定义关节平面
	Rainbow::Vector3f JointTargetDelta = JointTarget - RootPos;
	const double JointTargetLengthSqr = JointTargetDelta.LengthSqr();

	// 处理关节目标与根节点重合的特殊情况
	Rainbow::Vector3f JointPlaneNormal, JointBendDir;
	if (JointTargetLengthSqr < Rainbow::Sqr(kSmallNumber))
	{
		JointBendDir = Rainbow::Vector3f(0, 1, 0);
		JointPlaneNormal = Rainbow::Vector3f(0, 0, 1);
	}
	else
	{
		// 计算关节平面的法向量
		JointPlaneNormal = Rainbow::CrossProduct(DesiredDir, JointTargetDelta);

		// 处理特殊情况：当目标方向与关节位移方向平行时
		if (JointPlaneNormal.LengthSqr() < Rainbow::Sqr(kSmallNumber))
		{
			Rainbow::Vector3f& Axis1 = JointPlaneNormal;
			Rainbow::Vector3f& Axis2 = JointBendDir;

			// 找到最佳基向量
			const Rainbow::Vector3f NX = Rainbow::Abs(DesiredDir.x);
			const Rainbow::Vector3f NY = Rainbow::Abs(DesiredDir.y);
			const Rainbow::Vector3f NZ = Rainbow::Abs(DesiredDir.z);

			if (NZ > NX && NZ > NY)	Axis1 = Rainbow::Vector3f(1, 0, 0);
			else					Axis1 = Rainbow::Vector3f(0, 0, 1);

			// 计算正交基向量
			Rainbow::Vector3f Tmp = Axis1 - DesiredDir * Rainbow::DotProduct(Axis1, DesiredDir);
			Axis1 = Tmp.GetNormalizedSafe();
			Axis2 = Rainbow::CrossProduct(Axis1, DesiredDir);
		}
		else
		{
			JointPlaneNormal.Normalize();

			// 计算关节弯曲方向
			JointBendDir = JointTargetDelta - (Rainbow::DotProduct(JointTargetDelta, DesiredDir) * DesiredDir);
			JointBendDir.NormalizeSafe();
		}
	}

	//UE_LOG(LogAnimationCore, Log, TEXT("UpperLimb : %0.2f, LowerLimb : %0.2f, MaxLimb : %0.2f"), UpperLimbLength, LowerLimbLength, MaxLimbLength);

	// 处理拉伸逻辑
	if (bAllowStretching)
	{
		const double ScaleRange = MaxStretchScale - StartStretchRatio;
		if (ScaleRange > kSmallNumber && MaxLimbLength > kSmallNumber)
		{
			const double ReachRatio = DesiredLength / MaxLimbLength;
			const double ScalingFactor = (MaxStretchScale - 1.0) * Rainbow::Clamp((ReachRatio - StartStretchRatio) / ScaleRange, 0.0, 1.0);
			if (ScalingFactor > kSmallNumber)
			{
				// 应用拉伸
				LowerLimbLength *= (1.0 + ScalingFactor);
				UpperLimbLength *= (1.0 + ScalingFactor);
				MaxLimbLength *= (1.0 + ScalingFactor);
			}
		}
	}

	OutEndPos = DesiredPos;
	OutJointPos = JointPos;

	// 处理目标距离超过最大肢体长度的情况
	if (DesiredLength >= MaxLimbLength)
	{
		// 完全伸展肢体
		OutEndPos = RootPos + (MaxLimbLength * DesiredDir);
		OutJointPos = RootPos + (UpperLimbLength * DesiredDir);
	}
	else
	{
		// 使用余弦定理计算角度
		const double TwoAB = 2.0 * UpperLimbLength * DesiredLength;
		const double CosAngle = Rainbow::Clamp(((UpperLimbLength * UpperLimbLength) +
			(DesiredLength * DesiredLength) - (LowerLimbLength * LowerLimbLength)) / TwoAB, -1.0, 1.0);

		// 判断是否需要反向
		const bool bReverseUpperBone = (CosAngle < 0.0);

		// 计算角度
		const double Angle = Rainbow::acos(CosAngle);

		// 计算关节到目标线的距离
		const double JointLineDist = UpperLimbLength * Rainbow::sin(Angle);

		// 计算关节在目标线上的投影距离
		const double ProjJointDistSqr = (UpperLimbLength * UpperLimbLength) - (JointLineDist * JointLineDist);
		// although this shouldn't be ever negative, sometimes Xbox release produces -0.f, causing ProjJointDist to be NaN
		// so now I branch it. 						
		double ProjJointDist = (ProjJointDistSqr > 0.0) ? Rainbow::Sqrt(ProjJointDistSqr) : 0.0;
		if (bReverseUpperBone)
		{
			ProjJointDist *= -1.f;
		}

		// 计算最终关节位置
		OutJointPos = RootPos + (ProjJointDist * DesiredDir) + (JointLineDist * JointBendDir);
	}
}

// 基础两骨骼IK求解器
// 参数说明:
// BoneA: 第一个骨骼的变换矩阵
// BoneB: 第二个骨骼的变换矩阵
// Effector: 目标位置的变换矩阵
// PoleVector: 极向量(控制关节弯曲方向)
// PrimaryAxis: 主旋转轴
// SecondaryAxis: 次旋转轴
// SecondaryAxisWeight: 次旋转轴权重
// BoneALength: 第一个骨骼长度
// BoneBLength: 第二个骨骼长度
// bEnableStretch: 是否启用拉伸
// StretchStartRatio: 开始拉伸的比例
// StretchMaxRatio: 最大拉伸比例
void BasicIK::SolveBasicTwoBoneIK(Rainbow::Matrix4x4f& BoneA, Rainbow::Matrix4x4f& BoneB, Rainbow::Matrix4x4f& Effector, const Rainbow::Vector3f& PoleVector, const Rainbow::Vector3f& PrimaryAxis, const Rainbow::Vector3f& SecondaryAxis, float SecondaryAxisWeight, float BoneALength, float BoneBLength, bool bEnableStretch, float StretchStartRatio, float StretchMaxRatio)
{
	// 获取骨骼位置
	Rainbow::Vector3f RootPos = BoneA.GetPosition();
	Rainbow::Vector3f ElbowPos = BoneB.GetPosition();
	Rainbow::Vector3f EffectorPos = Effector.GetPosition();

	// 调用两骨骼IK求解器
	BasicIK::SolveTwoBoneIK(RootPos, ElbowPos, EffectorPos, PoleVector, EffectorPos, ElbowPos, EffectorPos, BoneALength, BoneBLength, bEnableStretch, StretchStartRatio, StretchMaxRatio);

	// 更新骨骼位置
	BoneB.SetPosition(ElbowPos);
	Effector.SetPosition(EffectorPos);

	// 计算第一个骨骼的旋转
	Rainbow::Vector3f Axis = BoneA.GetRotation() * PrimaryAxis;
	Rainbow::Vector3f Target1 = BoneB.GetPosition() - BoneA.GetPosition();

	Rainbow::Vector3f BoneBLocation = BoneB.GetPosition();
	if (!IsZeroVector(Target1, kSmallNumber) && !IsZeroVector(Axis, kSmallNumber))
	{
		Target1 = Target1.GetNormalizedSafe();

		// 处理完全伸展的特殊情况
		{
			Rainbow::Vector3f BaseDirection = (EffectorPos - RootPos).GetNormalizedSafe();
			if (Rainbow::Abs(Rainbow::DotProduct(BaseDirection, Target1) - 1.0f) < kSmallNumber)
			{
				// 偏移骨骼位置以计算旋转
				BoneBLocation += (PoleVector - BoneBLocation).GetNormalizedSafe() * kSmallNumber * 2.f;
				Target1 = (BoneBLocation - BoneA.GetPosition()).GetNormalizedSafe();
			}
		}

		// 计算第一个骨骼的主旋转
		Rainbow::Quaternionf Rotation1 = Rainbow::FromToQuaternion(Axis, Target1);
		BoneA.SetTR(BoneA.GetPosition(), (Rotation1 * BoneA.GetRotation()).GetNormalized());

		// 计算第一个骨骼的次旋转
		Axis = BoneA.GetRotation() * SecondaryAxis;

		if (SecondaryAxisWeight > kSmallNumber)
		{
			Rainbow::Vector3f Target2 = BoneBLocation - (EffectorPos + BoneA.GetPosition()) * 0.5f;
			Target2 = Target2 - Rainbow::DotProduct(Target2, Target1) * Target1;
			if (!IsZeroVector(Target2, kSmallNumber) && !IsZeroVector(Axis))
			{
				Target2 = Target2.GetNormalizedSafe();

				Rainbow::Quaternionf Rotation2 = Rainbow::FromToQuaternion(Axis, Target2);
				if (!Rainbow::CompareApproximately(SecondaryAxisWeight, 1.0f))
				{
					float RotationAngle;
					Rainbow::Vector3f RotationAxis;
					Rainbow::QuaternionfToAxisAngle(Rotation2, &RotationAxis, &RotationAngle);

					float clampedWeight = Rainbow::Clamp(SecondaryAxisWeight, 0.f, 1.f);
					Rotation2 = Rainbow::AxisAngleToQuaternionf(RotationAxis, RotationAngle * clampedWeight);
				}
				BoneA.SetTR(BoneA.GetPosition(), ((Rotation2 * BoneA.GetRotation()).GetNormalized()));
			}
		}
	}

	// 计算第二个骨骼的旋转
	Axis = BoneB.GetRotation() * PrimaryAxis;
	Target1 = Effector.GetPosition() - BoneBLocation;

	if (!IsZeroVector(Target1, kSmallNumber) && !IsZeroVector(Axis, kSmallNumber))
	{
		Target1 = Target1.GetNormalizedSafe();
		Rainbow::Quaternionf Rotation1 = Rainbow::FromToQuaternion(Axis, Target1);
		BoneB.SetTR(BoneB.GetPosition(), ((Rotation1 * BoneB.GetRotation()).GetNormalized()));

		if (SecondaryAxisWeight > kSmallNumber)
		{
			Axis = BoneB.GetRotation() * SecondaryAxis;
			Rainbow::Vector3f Target2 = BoneBLocation - (EffectorPos + RootPos) * 0.5f;
			Target2 = Target2 - Rainbow::DotProduct(Target2, Target1) * Target1;

			if (!IsZeroVector(Target2, kSmallNumber) && !IsZeroVector(Axis))
			{
				Target2 = Target2.GetNormalizedSafe();

				Rainbow::Quaternionf Rotation2 = Rainbow::FromToQuaternion(Axis, Target2);
				if (!Rainbow::CompareApproximately(SecondaryAxisWeight, 1.f))
				{
					float RotationAngle;
					Rainbow::Vector3f RotationAxis;
					Rainbow::QuaternionfToAxisAngle(Rotation2, &RotationAxis, &RotationAngle);

					float clampedWeight = Rainbow::Clamp(SecondaryAxisWeight, 0.f, 1.f);
					Rotation2 = Rainbow::AxisAngleToQuaternionf(RotationAxis, RotationAngle * clampedWeight);
				}
				BoneB.SetTR(BoneB.GetPosition(), ((Rotation2 * BoneB.GetRotation()).GetNormalized()));
			}
		}
	}
}

// 采样更新函数
void BasicIK::SampleInternal(float progressPercent)
{
	// 检查是否需要更新
	if (!UpdateCache() && !m_Initialize)
	{
		return;
	}

	// 检查权重是否有效
	if (m_Weight <= kSmallNumber || m_Weight > 1.0f ||
		progressPercent <= kSmallNumber || progressPercent > 1.0f)
	{
		return;
	}
	// 计算实际权重 (考虑过渡进度)
	float finalWeight = m_Weight * progressPercent;
	if (finalWeight <= kSmallNumber)
	{
		return;
	}

	// 计算极向量目标位置
	Rainbow::Vector3f PoleTarget = Rainbow::Vector3f::zero;
	if (m_Effector)
	{
		const Rainbow::Transform* PoleVectorSpaceTransform = m_Effector->GetTransform();
		if (m_PoleVectorKind == EControlRigVectorKind::kDirection)
		{
			PoleTarget = PoleVectorSpaceTransform->GetWorldRotation() * m_PoleVectorRelativeToEffector;
		}
		else if (m_PoleVectorKind == EControlRigVectorKind::kLocation)
		{
			PoleTarget = PoleVectorSpaceTransform->GetWorldRotation() * m_PoleVectorRelativeToEffector + PoleVectorSpaceTransform->GetWorldPosition();
		}
	}

	// 计算骨骼长度
	if (m_BoneALength < kSmallNumber)
	{
		Rainbow::Vector3f Scale = Rainbow::Vector3f::one;
		Rainbow::Vector3f Diff = m_BoneA->GetWorldPosition() - m_BoneB->GetWorldPosition();
		Diff = Diff * Scale;
		m_BoneALength = Diff.Length();
	}

	if (m_BoneBLength < kSmallNumber && m_Effector)
	{
		Assert(m_Effector->GetTransform());
		Rainbow::Vector3f Scale = Rainbow::Vector3f::one;
		Rainbow::Vector3f Diff = m_BoneB->GetWorldPosition() - m_Effector->GetTransform()->GetWorldPosition();
		Diff = Diff * Scale;
		m_BoneBLength = Diff.Length();
	}

	// 检查骨骼长度是否有效
	if (m_BoneALength < kSmallNumber || m_BoneBLength < kSmallNumber)
	{
		LogStringMsg("Item Lengths are not provided.\nEither set item length(s) or set effector item.");
		return;
	}

	// 获取骨骼变换矩阵
	Rainbow::Matrix4x4f boneATransform = m_BoneA->GetWorldTransform();
	Rainbow::Matrix4x4f boneBTransform = m_BoneB->GetWorldTransform();
	Rainbow::Matrix4x4f EffectorTransform = m_Effector->GetTransform()->GetWorldTransform();

	// 求解IK
	SolveBasicTwoBoneIK(boneATransform, boneBTransform, EffectorTransform, PoleTarget, m_PrimaryAxis, m_SecondaryAxis, m_SecondaryAxisWeight, m_BoneALength, m_BoneBLength, m_bEnableStretch, m_StretchStartRatio, m_StretchMaxRatio);

	// 存储原始变换用于插值
	Rainbow::Matrix4x4f originalBoneATransform = m_BoneA->GetWorldTransform();
	Rainbow::Matrix4x4f originalBoneBTransform = m_BoneB->GetWorldTransform();

	// 对骨骼A进行插值
	Rainbow::Quaternionf interpolatedRotA = Rainbow::Slerp(
		originalBoneATransform.GetRotation(),
		boneATransform.GetRotation(),
		finalWeight
	);
	Rainbow::Vector3f interpolatedPosA = Rainbow::Lerp(
		originalBoneATransform.GetPosition(),
		boneATransform.GetPosition(),
		finalWeight
	);
	m_BoneA->SetWorldRotation(interpolatedRotA);
	m_BoneA->SetWorldPosition(interpolatedPosA);

	// 对骨骼B进行插值
	Rainbow::Quaternionf interpolatedRotB = Rainbow::Slerp(
		originalBoneBTransform.GetRotation(),
		boneBTransform.GetRotation(),
		finalWeight
	);
	Rainbow::Vector3f interpolatedPosB = Rainbow::Lerp(
		originalBoneBTransform.GetPosition(),
		boneBTransform.GetPosition(),
		finalWeight
	);
	m_BoneB->SetWorldRotation(interpolatedRotB);
	m_BoneB->SetWorldPosition(interpolatedPosB);

	// 对骨骼C(如果存在)进行插值
	if (m_BoneC)
	{
		Rainbow::Matrix4x4f originalEffectorTransform = m_BoneC->GetWorldTransform();

		Rainbow::Quaternionf interpolatedRotC = Rainbow::Slerp(
			originalEffectorTransform.GetRotation(),
			EffectorTransform.GetRotation(),
			finalWeight
		);
		Rainbow::Vector3f interpolatedPosC = Rainbow::Lerp(
			originalEffectorTransform.GetPosition(),
			EffectorTransform.GetPosition(),
			finalWeight
		);
		m_BoneC->SetWorldRotation(interpolatedRotC);
		m_BoneC->SetWorldPosition(interpolatedPosC);
	}

	// 标记骨骼组件需要更新
	if (m_CachedSkeletonComp)
	{
		m_CachedSkeletonComp->Dirty();
	}
}

// Demo usage example
/*
void DemoBasicIK()
{
	// Create a simple arm chain with 3 bones
	GameObject* root = new GameObject("ArmRoot");
	GameObject* upperArm = new GameObject("UpperArm");
	GameObject* forearm = new GameObject("Forearm");
	GameObject* hand = new GameObject("Hand");

	// Setup hierarchy
	upperArm->SetParent(root);
	forearm->SetParent(upperArm);
	hand->SetParent(forearm);

	// Create bone nodes
	BoneNode* rootBone = new BoneNode(root);
	BoneNode* upperArmBone = new BoneNode(upperArm);
	BoneNode* forearmBone = new BoneNode(forearm);
	BoneNode* handBone = new BoneNode(hand);

	// Create IK controller
	BasicIK ikController;
	
	// Setup IK chain
	ikController.SetBones(upperArmBone, forearmBone, handBone);
	ikController.SetEffector(hand);
	ikController.SetBoneLengths(1.0f, 1.0f); // Set bone lengths
	ikController.SetWeight(1.0f);
	ikController.SetStretch(true, 0.8f, 1.2f); // Enable stretching with 20% max stretch
	ikController.SetPrimaryAxis(Vector3f(0, 0, 1)); // Z-axis as primary
	ikController.SetSecondaryAxis(Vector3f(0, 1, 0), 0.5f); // Y-axis as secondary with 50% weight
	
	// Set pole vector to control elbow direction
	ikController.SetPoleVector(Vector3f(0, 1, 0));

	// Create a target object to follow
	GameObject* target = new GameObject("IKTarget");
	target->SetPosition(Vector3f(2, 1, 0));

	// In your update loop:
	void Update(float dt)
	{
		// Update target position (example: move in a circle)
		float time = GetTime();
		target->SetPosition(Vector3f(
			2 * cos(time),
			1 + sin(time),
			0
		));

		// Update IK
		ikController.SampleInternal(dt);
	}
}
*/
