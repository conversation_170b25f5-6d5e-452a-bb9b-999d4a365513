#ifndef __ActorStorageBoxHorse_H__
#define __ActorStorageBoxHorse_H__

#include "ActorHorse.h"
#include "Core/blocks/container_world.h"

#define HORSE_ID 4716
class ActorStorageBoxHorse : public ActorHorse{ //tolua_exports
public:

	ActorStorageBoxHorse(void);
	~ActorStorageBoxHorse(void);

	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata, int version) override;

	virtual void tick() override;
	virtual void update(float dtime) override;
	virtual void enterWorld(World* pworld);

	virtual void onDie() override;

	virtual int getObjType() const override
	{
		return OBJ_TYPE_STORAGEBOXHORSE;
	}

	//tolua_begin
	static bool isStorageBoxHorse(int horseid) {
		if (horseid == HORSE_ID) {
			return true;
		}
		return false;
	}
	void openStorageBox();
	void dropItems();
	//tolua_end

private:
	void initStorageBox();
private:
	WorldStorageBox* m_wStorageBox;
}; //tolua_exports

#endif //__ACTORPUMPKINHORSE_H__

