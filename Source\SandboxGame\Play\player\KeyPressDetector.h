#pragma once

#include "SandboxGame.h"

class PlayerControl;

// 按键状态检测器类，封装所有按键相关逻辑
class EXPORT_SANDBOXGAME KeyPressDetector
{
public:
    KeyPressDetector(int keycode);
    virtual ~KeyPressDetector();
    
    // 主更新方法，处理所有按键逻辑
    void update(float dtime);
    
    // 重置按键状态
    void reset();
    
    // 获取当前状态
    bool isKeyDown() const { return m_keyState.m_isKeyDown; }
    bool isKeyHeld() const { return m_keyState.m_isKeyHeld; }
    bool isKeyUp() const { return m_keyState.m_isKeyUp; }
    bool isShortPress() const { return m_keyState.m_isShortPress; }
    bool isLongPress() const { return m_keyState.m_isLongPress; }
    float getPressTime() const { return m_pressTime; }
    
    // 设置长按阈值（秒）
    void setLongPressThreshold(float threshold) { m_longPressThreshold = threshold; }
    float getLongPressThreshold() const { return m_longPressThreshold; }
    
    // 添加tick方法，处理UI更新和交互检测
    void tick();
private:
    struct KeyState {
        unsigned int m_isKeyDown        : 1;        // 按键刚被按下
        unsigned int m_isKeyHeld        : 1;        // 按键正在被按住
        unsigned int m_isKeyUp          : 1;        // 按键刚被释放
        unsigned int m_isShortPress     : 1;        // 检测到短按
        unsigned int m_isLongPress      : 1;        // 检测到长按
        unsigned int m_keystatus        : 1;        // 按键的状态按下或者不按下
    } m_keyState;

    float m_pressTime;                      // 按键已按下的时间
    float m_longPressThreshold;             // 长按阈值（秒）
    int m_keyCode;                          // 按键的键码
}; 