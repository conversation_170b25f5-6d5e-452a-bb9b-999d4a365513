#include "LavaEffect.h"

#include "ClientActor.h"
#include "ActorTypes.h"//ATTACK_FIRE
#include "ConstAtLua.h"
#include "ActorAttrib.h"
#include "AttackedComponent.h"
#include "FireBurnComponent.h"
#include "ClientActorFuncWrapper.h"
#include "LuaInterfaceProxy.h"
using namespace MNSandbox;


void LavaEffect::executeEffect(ClientActor* pActor)
{
	if (!pActor)
	{
		assert(pActor);
		return;
	}

	ActorAttrib *attrib = pActor->getAttrib();
	if (attrib)
	{
		if (attrib->immuneToFire() <= 0)
		{ 
			auto FireBurnComp = pActor->sureFireBurnComponent();
			if(FireBurnComp)
				FireBurnComp->setFire(100, 1);
		}
		if (attrib->immuneToFire() <= 1)
		{
			auto component = pActor->getAttackedComponent();
			if (component)
			{
				component->attackedFromType_Base(ATTACK_FIRE, 4.0f * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv); //modify by null, �ҽ����˺���5��
			}
		}
	}
	//owner->m_FallDistance *= 0.5f;
	auto functionWrapper = pActor->getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setFallDistance(functionWrapper->getFallDistance() * 0.5f);
	}

}
