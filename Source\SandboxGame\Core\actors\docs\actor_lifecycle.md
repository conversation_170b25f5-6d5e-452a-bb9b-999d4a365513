# Actor生命周期管理

下面是游戏项目中Actor从创建到销毁的生命周期时序图：

```mermaid
sequenceDiagram
    participant Client
    participant ClientActorMgr
    participant ClientActor
    participant Components

    %% 创建与初始化
    Client->>ClientActorMgr: spawnActor()
    ClientActorMgr->>ClientActor: 创建Actor实例
    ClientActorMgr->>ClientActor: InitActor()
    ClientActor->>ClientActor: 初始化数据
    ClientActor->>ClientActor: enterWorld()
    ClientActor->>Components: 绑定移动、渲染等组件

    %% 更新循环
    loop 游戏主循环
        Client->>ClientActorMgr: tick()
        ClientActorMgr->>ClientActor: update(dtime)
        Note over ClientActor: 基于ActorUpdateFrequency更新
        ClientActorMgr->>ClientActor: 空间管理(Octree)
    end

    %% 状态管理
    Note over ClientActor: m_Flags/m_FlagsEx状态管理
    Note over ClientActor: isDead()/alive()检查
    Note over ClientActor: m_LiveTicks记录存活时间

    %% 销毁流程
    Client->>ClientActor: setNeedClear()
    Client->>ClientActorMgr: despawnActor()
    ClientActorMgr->>ClientActor: leaveWorld()
    ClientActorMgr->>ClientActor: release()
    Note over ClientActor: 引用计数为0时销毁
```

游戏项目中Actor生命周期管理流程如下：

  1. 创建与初始化:
    - ClientActorMgr::spawnActor() 创建并生成Actor
    - ClientActor::InitActor() 初始化Actor及其数据
    - ClientActor::enterWorld() Actor进入游戏世界
    - 组件绑定：Actor会绑定移动、渲染等必要组件
  2. 更新循环:
    - ClientActorMgr::tick() 管理器主循环更新所有Actor
    - ClientActor::update(float dtime) 基于时间的Actor更新
    - ActorUpdateFrequency 控制Actor更新频率(优化性能)
    - 空间管理使用Octree结构追踪Actor位置
  3. 状态管理:
    - ClientActor::m_Flags和m_FlagsEx 标记Actor各种状态
    - ClientActor::isDead()和alive() 检查生死状态
    - ClientActor::m_LiveTicks 追踪Actor存活时间
  4. 销毁流程:
    - ClientActor::setNeedClear() 标记准备移除
    - ClientActorMgr::despawnActor() 从世界中移除Actor
    - ClientActor::leaveWorld() Actor离开世界
    - ClientActor::release() 减少引用计数，可能销毁Actor


## Actor和Chunk之间的关系
Actor和Chunk之间的关系主要体现在空间管理和生命周期协调上：


  1. 基本关联:
    - 每个Actor通过ActorChunkPos组件追踪其所在的Chunk
    - Actor被添加到世界时，会被放入对应位置的Chunk中
    - 当Actor移动时，系统会检查它是否需要从一个Chunk移动到另一个Chunk
  2. Chunk内Actor管理:
    - addActorToChunk(): 将Actor添加到特定Chunk
    - removeActorFromChunk(): 将Actor从Chunk中移除
    - Actor在Chunk间移动时，会从旧Chunk移除并添加到新Chunk
  3. 大型Actor处理:
    - 游戏特别处理了跨越多个Chunk的大型Actor
    - m_largeActorChunk映射记录大型Actor占据的所有Chunk
    - m_chunkLargeActor反向映射记录每个Chunk中存在的大型Actor
    - 这种设计防止了在卸载Chunk时将大型Actor"分割"的问题
  4. Chunk加载/卸载保护:
    - CanRemoveChunk()确保不会卸载包含大型Actor部分的Chunk
    - 只有当大型Actor跨越的所有Chunk都准备卸载时，才允许卸载
  5. 性能优化:
    - 使用Octree空间分区进行高效查询
    - 基于与玩家距离的更新频率控制
    - 通过分块管理，实现了部分世界的加载/卸载

  这种Actor-Chunk关系设计使游戏能够高效地管理大型世界，只加载和更新玩家周围的实体，同时保持跨Chunk边界的大型实体的
  完整性。

  ## 空投飞机野指针Bug

  我有一个飞机actor会在世界地图里面飞行，会跨越多个Chunk，程序经常出现飞机actor的指针被释放，导致外部持有飞机actor
  野指针的bug,帮我看看是为什么?

● 这个问题很可能与大型Actor(飞机)跨越多个Chunk的处理机制有关。让我分析几个可能的原因：

  1. Chunk卸载问题：虽然系统应该防止包含大型Actor的Chunk被卸载，但可能有bug
  2. 引用计数问题：可能是引用计数管理不当，导致Actor被过早释放
  3. 大型Actor管理缺陷：在跨Chunk管理中可能存在漏洞
  4. 并发/异步问题：可能在多线程环境下存在竞态条件

  我需要查看相关代码来找出具体原因。主要关注：
  - ClientActorManager中的大型Actor处理
  - Actor引用计数机制
  - Chunk加载/卸载逻辑

● Task(I need to investigate why a flying airplane actor that traverses across multiple chunks is experiencing …
      memory issues where its pointer gets freed, causing dangling pointer bugs.…)
  ⎿  Done (31 tool uses · 73.3k tokens · 7m 58.4s)

● 你的飞机Actor被释放导致野指针问题的原因找到了，主要与Chunk管理机制有关。

  问题根源：

  飞机Actor被错误地设置为由Chunk管理（managedByChunk()返回true），导致当飞机跨越Chunk边界、某个Chunk被卸载时，飞机A
  ctor也被一并删除了。

  关键问题点：

  1. 默认管理机制：
  virtual bool managedByChunk()
  {
      return true; // 默认所有Actor都会被Chunk管理
  }
  1. 当Chunk卸载时，会一并清理其管理的所有Actor。
  2. 跨Chunk处理不当：
  即使系统有m_largeActorChunk和m_chunkLargeActor结构来跟踪跨Chunk的大型Actor，仍存在竞态条件或逻辑缺陷导致保护机制
  失效。
  3. 已知问题：
  从最近的提交记录看到：
  d3c8a16d fix(air_drop): 修复空投飞机野指针bug: 不让chunk托管，避免chunk被删除时，actor也被删除
  3. 表明这个问题已经被定位。

  解决方案：

  1. 修改飞机Actor类：
  重写managedByChunk()方法返回false：
  virtual bool managedByChunk() override
  {
      return false; // 防止大型飞行物体被Chunk管理
  }
  1. 类似于ActorBoat.h中的实现：
  virtual bool managedByChunk() override { return false; } //避免chunk管理，避免chunk被删除时，actor也被删除
  2. 检查引用管理：
  确保外部系统使用弱引用或安全的指针包装（如MNSandbox::AutoRef<ClientActor>）来引用飞机Actor，避免在Actor被释放后继
  续使用。

  这是一个典型的生命周期管理问题，对于跨多个Chunk的大型移动实体，应该将其从Chunk生命周期管理中排除，使用全局管理方
  式。
