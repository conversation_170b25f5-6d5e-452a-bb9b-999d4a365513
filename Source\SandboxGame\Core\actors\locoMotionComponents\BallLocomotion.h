#ifndef _BALL_LOCOMOTION_H_
#define _BALL_LOCOMOTION_H_

#include "ActorLocoMotion.h"

namespace Rainbow
{
	class EventContent;
	class RigidDynamicActor;
}
namespace MINIW
{
	class Joint;
}
class ClientActor;
class ClientPlayer;

class BallLocoMotion : public ActorLocoMotion //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(BallLocoMotion)
	//tolua_begin
	BallLocoMotion();
	//tolua_end
	virtual void prepareTick() override;
	virtual void tick() override;
	virtual void update(float dtime) override;
	virtual void updateBindActor() override;
	virtual void getRotation(Rainbow::Quaternionf &quat) override;
	virtual void doPickThrough(ClientActor *excludesactor = nullptr) override;
	virtual bool needFullRotation() override
	{
		return true;
	}
	virtual void setPosition(int x, int y, int z);
	//tolua_begin
	void attachPhysActor();
	void detachPhysActor();
	void attachPhysJoint(ClientPlayer *player);
	void detachPhysJoint();
	void checkPhysWorld();

	virtual void OnCollisionEnter(const Rainbow::EventContent* collision);

	Rainbow::RigidDynamicActor *m_PhysActor;
	MINIW::Joint *m_PhysJoint;

	Rainbow::WorldPos m_UpdatePos;
	Rainbow::Quaternionf m_UpdateRot;

	Rainbow::Quaternionf m_PrevRotateQuat;
	Rainbow::Quaternionf m_RotateQuat;

	int m_PosRotationIncrements;
	WCoord m_ServerPos;
	Rainbow::Quaternionf m_ServerRot;
	//std::vector<WCoord> m_preCheckPhy; 
	bool m_hasPhysActor;
	//tolua_end
}; //tolua_exports

#endif