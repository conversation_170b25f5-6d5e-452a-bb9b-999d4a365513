#include "InWaterComponent.h"

#include "ClientPlayer.h"
#include "ActorHorse.h"

#include "world.h"
#include "EffectManager.h"


IMPLEMENT_COMPONENTCLASS(InWaterComponent)

InWaterComponent::InWaterComponent()
	:m_IsInWaterLastTick(false)
{

}

void InWaterComponent::OnBeginPlay()
{
	if (!GetOwner()) return;
	ClientActor* owner = GetOwner()->ToCast<ClientActor>();
	if (!owner) return;
	m_IsInWaterLastTick = owner->isInWater();
	Super::OnBeginPlay();
}

void InWaterComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void InWaterComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void InWaterComponent::OnTick()
{
	if (!GetOwner()) return ;
	ClientActor* owner = GetOwner()->ToCast<ClientActor>();
	if (!owner) return ;
	if (!m_IsInWaterLastTick && owner->isInWater() && !owner->isDead() && !owner->isInLava())
	{
		onEnterWater();
	}
	m_IsInWaterLastTick = owner->isInWater();
}

void InWaterComponent::playEnterWaterSound()
{
	if (!GetOwner()) return;
	ClientActor* owner = GetOwner()->ToCast<ClientActor>();
	if (!owner) return;
	ActorLocoMotion* locoMotion = owner->getLocoMotion();
	if(!locoMotion)
		return;
	WCoord pos    = locoMotion->getPosition();
	World* pWorld = owner->getWorld();
	if(pWorld){
		pWorld->getEffectMgr()->playSound(pos, "env.splash", 1.0f, 1.0f);
		pWorld->getEffectMgr()->playParticleEffectAsync("particles/1024.ent", pos, 100);
		pWorld->getEffectMgr()->playParticleEffectAsync("particles/1025.ent", pos, 40);
	}
}

ClientPlayer* InWaterComponent::GetOwnerClientPlayer()
{
	return GetOwnerPlayer()->GetPlayer();
}

ClientActor* InWaterComponent::GetOwnerClientActor()
{
	return GetOwnerActor()->GetActor();
}
/////////////////////////////////////////////////////////////////////////////////////////

IMPLEMENT_COMPONENTCLASS(InWaterComponent_Horse)

void InWaterComponent_Horse::onEnterWater()
{
	ActorHorse *owner = dynamic_cast<ActorHorse*>(GetOwner());
	if (!owner || !owner->isUseSwimSpeed()) { return; }

	ActorLocoMotion* locoMotion = owner->getLocoMotion();
	if(!locoMotion)
		return;

	if (owner->hasWaterSkill(2)) {
		locoMotion->m_Motion.y = 1.0f; 
	}
	playEnterWaterSound();
}

/////////////////////////////////////////////////////////////////////////////////////////

IMPLEMENT_COMPONENTCLASS(InWaterComponent_Player)

void InWaterComponent_Player::onEnterWater()
{
	playEnterWaterSound();
}