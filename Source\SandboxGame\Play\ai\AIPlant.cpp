
#include "AIPlant.h"
#include "ClientMob.h"
#include "container_backpack.h"

#include "special_blockid.h"
#include "world.h"

#include "ActorLocoMotion.h"
#include "FindComponent.h"
#include "ActorBodySequence.h"
#include "MobAttrib.h"

AIPlant::AIPlant(ClientMob *pActor, int prob, int dist, int blockid, int itemid, float speed, int interactdist, int food, float foodreduce)
	: AIBase(pActor), m_iProb(prob), m_iDist(dist), m_iBlockID(blockid), m_iItemID(itemid), m_fSpeed(speed), m_iInteractDist(interactdist), m_iFood(food), m_fFoodReduce(foodreduce)
{
	setMutexBits(7);
}

AIPlant::~AIPlant()
{

}

bool AIPlant::willRun()
{
	if (!m_pMobActor)
		return false;

	if (0 != GenRandomInt(m_iProb))
		return false;

	if (m_pMobActor->getMobAttrib()->getFood() < m_iFood)
		return false;

	auto *blockDef = GetDefManagerProxy()->getBlockDef(m_iBlockID);
	if (!blockDef)
		return false;

	auto *itemDef = GetDefManagerProxy()->getItemDef(m_iItemID);
	if (!itemDef)
		return false;

	auto living = dynamic_cast<ActorLiving*>(m_pMobActor);
	auto livingAttr = living != nullptr ? living->getLivingAttrib() : nullptr;
	if (!livingAttr)
		return false;

	BackPackGrid *pweapon = livingAttr->getEquipGrid(EQUIP_WEAPON);
	if (!pweapon)
		return false;

	BackPackGrid *pto = NULL;
	bool hasTool = false;
	if (pweapon->getItemID() == m_iItemID)
		hasTool = true;
	else
	{
		
		PackContainer *bags = m_pMobActor->getBags();
		if (bags)
		{
			pto = bags->getGridByItemID(m_iItemID);
			if (pto)
			{
				hasTool = true;
			}
		}	
	}

	if (!hasTool)
		return false;

	float dist = (float)(m_iDist / BLOCK_SIZE);
	int chunkRange = (int)ceil(dist / 16);
	WCoord tmpPos;

	char cExtend[16];
	memset(cExtend, 0, sizeof(cExtend));
	sprintf(cExtend, "%d", m_iItemID);
	auto findComponent = m_pMobActor->getFindComponent();
	if (findComponent && findComponent->findNearestBlock(tmpPos, m_iBlockID, (int)dist, AIPlant::canPlant, cExtend, true))
	{
		m_TargetPos = BlockCenterCoord(tmpPos);
		m_pMobActor->faceWorldPos(m_TargetPos, 180.0f, 180.0f);

		if (pto)
		{
			//从背包里找到了满足条件的工具 交换到手上
			BackPackGrid tmp;
			tmp.setItem(*pto);
			pto->setItem(*pweapon);
			pweapon->setItem(tmp);

			if (m_pMobActor->getBody())
			{
				livingAttr->applyEquips(m_pMobActor->getBody(), EQUIP_WEAPON);
			}
		}
		return true;
	}

	return false;
}

bool AIPlant::continueRun()
{
	if (!m_pMobActor)
		return false;
	if (m_iTimer < 0)
		return false;
	if (m_TargetPos.y < 0)
		return false;
	if (m_pMobActor->getMobAttrib()->getFood() < m_iFood)
		return false;

	WCoord vec = m_pMobActor->getLocoMotion()->getPosition() - m_TargetPos;
	float dist = vec.length();
	if (dist > m_iDist)
		return false;
	else if (dist > m_iInteractDist && --m_TraceTimer <= 0)
	{
		m_TraceTimer = 4 + GenRandomInt(0, 6);
		if (!m_pMobActor->getNavigator()->tryMoveTo(m_TargetPos.x, m_TargetPos.y, m_TargetPos.z, m_fSpeed))
			return false;
	}

	return true;
}

void AIPlant::start()
{
	if (m_pMobActor)
	{
		m_pMobActor->getNavigator()->tryMoveTo(m_TargetPos.x, m_TargetPos.y, m_TargetPos.z, m_fSpeed);
	}
	m_iTimer = GenRandomInt(300, 400);
	m_TraceTimer = 0;
}

void AIPlant::reset()
{
	m_pMobActor->getNavigator()->clearPathEntity();
	m_TargetPos = WCoord(0, -1, 0);
}

void AIPlant::update()
{
	m_iTimer--;
	if (!m_pMobActor) return;

	WCoord vec = m_pMobActor->getLocoMotion()->getPosition() - m_TargetPos;
	float dist = vec.length();
	if (dist <= m_iInteractDist)
	{
		int toolId = m_pMobActor->getEquipItem(EQUIP_WEAPON);
		auto *itemDef = GetDefManagerProxy()->getItemDef(toolId);
		if (itemDef)
		{
			bool isPlant = false;

			//定义了脚本
			if (!itemDef->UseScript.empty() && (itemDef->UseTarget == ITEM_USE_CLICKBLOCK || itemDef->UseTarget == ITEM_USE_CLICKLIQUID))
			{
				m_pMobActor->playAnim(SEQ_ATTACK);

				bool scriptHandled = false;
				WCoord blockPos = CoordDivBlock(m_TargetPos);
				MINIW::ScriptVM::game()->callFunction(itemDef->UseScript.c_str(), "u[ClientMob]u[World]iiii>b", m_pMobActor, m_pMobActor->getWorld(), blockPos.x, blockPos.y, blockPos.z, (int)DIR_POS_Y, &scriptHandled);

				if (scriptHandled)
				{
					m_pMobActor->getMobAttrib()->setFood(m_pMobActor->getMobAttrib()->getFood() - m_fFoodReduce);
					isPlant = true;
				}
					
			}

			//摆块
			if (!isPlant && (toolId < SOC_BLOCKID_MAX/* || (toolId >= EX_BLOCKID_MIN && toolId < EX_BLOCKID_MAX)*/))//SOC 方块ID 范围 0-4095 不支持扩展id
			{
				WCoord blockPos = TopCoord(CoordDivBlock(m_TargetPos));
				bool result = m_pMobActor->placeBlock(toolId, blockPos.x, blockPos.y, blockPos.z, DIR_POS_Y, (float)blockPos.x, (float)(blockPos.y-1), (float)blockPos.z);
				if (result)
				{
					m_pMobActor->playAnim(SEQ_ATTACK);
					m_pMobActor->getMobAttrib()->setFood(m_pMobActor->getMobAttrib()->getFood() - m_fFoodReduce);
				}		
			}

		}
		
		m_TargetPos = WCoord(0, -1, 0);
	}
}

bool AIPlant::canPlant(ClientActor *actor, WCoord &blockpos, int itemid, std::string extend /* = "" */)
{
	if (!actor)
		return false;

	int targetid = atoi(extend.c_str());
	if (targetid <= 0)
		return false;

	int height = 1;
	auto *blockDef = GetDefManagerProxy()->getBlockDef(targetid);
	if (blockDef)
		height = blockDef->Height;

	for (int i = 1; i <= height; i++)
	{
		if (!IsAirBlockID(actor->getWorld()->getBlockID(WCoord(blockpos.x, blockpos.y + i, blockpos.z))))
			return false;
	}

	if (IsCropsBlock(itemid) && actor->getWorld()->getBlockData(blockpos.x, blockpos.y, blockpos.z) < 7) //农作物未成熟
		return false;

	WCoord pos = BlockCenterCoord(blockpos);
	bool canMove = actor->getNavigator()->tryMoveTo(pos.x, pos.y, pos.z, 0.1f);
	actor->getNavigator()->clearPathEntity();

	return canMove;
}
