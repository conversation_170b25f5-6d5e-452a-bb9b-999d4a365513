#include "TLFrameEventExplosion.h"
#include "ClientActor.h"
#include "ActorLocoMotion.h"
#include "world.h"

using namespace MNSandbox;

TLFrameEventExplosion::TLFrameEventExplosion() : m_targetType(0),
m_damage(0.0f), m_upHalf(false), m_smoking(false), m_explodeRadius(0)
{

}

TLFrameEventExplosion::~TLFrameEventExplosion()
{

}

void TLFrameEventExplosion::Start(SandboxTLData* data)
{
	if (data == nullptr) return;

	ClientActor* pOwner = data->GetOwner();
	if (pOwner == nullptr) return;

	this->doExplosion(pOwner);
}

bool TLFrameEventExplosion::eventInit(TLNodeDef* def, SandboxTLData* data)
{
	if (def == nullptr) return false;

	TLFrameEventExplosionDef* explosionDef = static_cast<TLFrameEventExplosionDef*>(def);

	m_targetType = explosionDef->targetType;

	// 片段内对象参数
	m_fromTLName = explosionDef->fromTLID;
	m_fromClipName = explosionDef->fromClipID;

	m_damage = explosionDef->damage;
	m_upHalf = explosionDef->uphalf;
	m_smoking = explosionDef->smoking;
	m_explodeRadius = explosionDef->explodeRadius;
	
	return true;
}

bool TLFrameEventExplosion::doExplosion(ClientActor* pActor)
{
	World* pWorld = pActor->getWorld();
	if (pWorld == nullptr) return false;

	const WCoord& position = WCoord(pActor->getLocoMotion()->getFramePosition());
	pWorld->createExplosionNew(nullptr, position + WCoord(0, 10, 0), m_explodeRadius, m_upHalf, m_damage, m_smoking, true);

	return true;
}
