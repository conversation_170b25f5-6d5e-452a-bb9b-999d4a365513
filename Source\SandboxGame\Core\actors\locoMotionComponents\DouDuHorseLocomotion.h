#ifndef __BOMB__LOCOMOTION_H__
#define __BOMB__LOCOMOTION_H__

#include "ActorLocoMotion.h"
#include "ActorHorse.h"
#include "HorseLocomotion.h"

class DouDuHorseLocomotion : public HorseLocomotion
{
public:
	DECLARE_COMPONENTCLASS(DouDuHorseLocomotion)

	DouDuHorseLocomotion();
	virtual ~DouDuHorseLocomotion();

	virtual void tick();
public:
	bool m_bRun;
	float speedMultiplier;
	float m_MaxSpeed;
	float m_fSpeedUpTime;
	float m_fSpeedUpVal;
	bool m_bWaitStopEffect;
};


#endif

