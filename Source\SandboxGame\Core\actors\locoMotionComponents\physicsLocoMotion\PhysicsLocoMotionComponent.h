#ifndef __COMPONENTLOCOMOTION_H__
#define __COMPONENTLOCOMOTION_H__

#include "PhysicsLocoMotion.h"

namespace Rainbow
{
	class RigidDynamicActor;
	class EventContent;
}

namespace MINIW {
	class Joint;
}

class PhysicsLocoMotionComponent : public PhysicsLocoMotion //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(PhysicsLocoMotionComponent)

	//tolua_begin
	PhysicsLocoMotionComponent();

	virtual void attachPhysActor(int id) override;
	virtual void detachPhysActor() override;
	void attachPhysJoint(Rainbow::RigidDynamicActor *actor);
	void detachPhysJoint();

	MINIW::Joint *m_PhysJoint;
	//tolua_end
}; //tolua_exports



#endif
