﻿#pragma once

#include "SandboxGame.h"
#include "ActorTypes.h"
#include "IActorBody.h"
#include "CoreCommonDef.h"
//#include "Entity/OgreEntity.h"
#include "ActorBodyAttachmentHandlerComponent.h"
#include "Math/LegacyBounding.h"
#include "ActorBodySequence.h"
#include "SandboxCallback.h"
#include "SandboxNotify.h"
#include "LegacyOgreColourValue.h"
#include "Graphics/Texture2D.h"
#include "CustomModelData.h"
#include "Mesh/LegacyOgreAnimationData.h"
#include "SandboxGame/SandboxGameDef.h"
#include "SandboxGame/Common/UGCModelLoader.h"
#include "ActorBodyAvatarComponent.h"
#include "ActorBodyUIComponent.h"
#include "ActorBodyEquipComponent.h"
#include "ActorBodyThornBallComponent.h"

enum class UgcAssetType;
//#include "world.h"
//class ModelView;
class ModelItemMesh;
class FullyCustomModel;
struct EquipmentPartDef;
class ClientActor;
class BaseItemMesh;
class ClientPlayer;
class World;
class ActorBodyUIComponent;
class ActorBodyAvatarComponent;
class ActorBodyEquipComponent;
class ActorBodyThornBallComponent;
class ActorBodyAttachmentHandlerComponent;
USE_NS_SANDBOX;

extern std::string Slot2BoneName[MAX_EQUIP_SLOTS];

namespace Rainbow
{
	class Model;
	class AnimPlayTrack;
	class NameText3D;
	class Text3D;
	class Image3D;
	class ProgressBarIn3D;
	class UIIn3DSceneMerger;
	class MeshInstance;
	class Entity;
	class MoveByTextMgr;
	class MusicClubChatBubble3D;
	class Voice3D;
	class ActorBodyAutoReleaseByMainEntity;
	class ImageBoard3D;
	class IModelMeshRenderer;
	class ModelData;
	class MovableObject;
	class GameObject;
	class AnimationData;
	class LegacyAnimationFrameEventData;
};
namespace jsonxx
{
	class Object;
}
class UnrealSkinMotion;
class ObjectCacheBase;

//将ActorBody中的ModelView解耦出来
//考虑兼容先用个宏开关控制
//这个开关lua层也用到了，修改的话注意一下 2021/8/20 code_by:huangfubin
//tolua_begin
#define MODELVIEW_DECOUPLE_FROM_ACTORBODY 1
//tolua_end

#define HUD_TEST 0

//tolua_begin
enum FACE_EXPRESSION
{
	EXPRESSION_IDLEACTION,
	EXPRESSION_SHOWTIME,
	EXPRESSION_STAND,
	EXPRESSION_BEHIT,
	EXPRESSION_ACT1,
	EXPRESSION_ACT2,
	EXPRESSION_ACT3,
	EXPRESSION_WINK,
	EXPRESSION_COUNT
};
//tolua_end

struct SeqMustAnimDesc//连续播放的硬直动画描述
{
	int _curIdx;
	std::vector<int> _animIdList;
	std::vector<float> _animPlayTimesList;
	Rainbow::AnimPlayTrack* _pAnimPlayTrack;
	SeqMustAnimDesc()
	{
		reset();
	}
	void addAnimId(int animId, float playTimes)
	{
		_animIdList.push_back(animId);
		_animPlayTimesList.push_back(playTimes);
	}
	void reset()
	{
		_curIdx = -1;
		_animIdList.clear();
		_animPlayTimesList.clear();
		_pAnimPlayTrack = NULL;
	}
};

// 
//tolua_begin
struct CustomMotionStruct
{
	unsigned int MotionID;
	unsigned int CusMotionID;
	std::string FileName;
	int BoneNum;
	std::vector<Rainbow::FixedString> RecordedBoneNames;
	std::vector<Rainbow::Vector3f> RecordedTranslations;
	std::vector<Rainbow::Quaternionf> RecordedRotations;
	std::vector<Rainbow::Vector3f> RecordedScales;
	std::vector<int> RecordedTicks;
};
//tolua_end

namespace MNSandbox
{
	struct ThronBallStruct;

	const Rainbow::FixedString MESH_FACE_NAME = "face";
	int GetPlayerSex(int modelId);
	void alterAvatarPartColorAsyn(Rainbow::IActorBody* actorbody,  int& modelId, int& partId, jsonxx::Object& aSkin, float& h, float& s, float& b, int& blockID);
}

class EXPORT_SANDBOXGAME ActorBody : public Rainbow::IActorBody { //tolua_exports
public:
//tolua_begin
	struct ModelAssetData
	{
		Rainbow::SharePtr<Rainbow::Asset>           m_ModelAsset;
		Rainbow::SharePtr<Rainbow::Asset>		    m_AnimAsset;
		Rainbow::SharePtr<Rainbow::AnimationData>	m_AnimData;
	};
//tolua_end

#pragma region init
//tolua_begin
	ActorBody(ClientActor* owner);
	virtual ~ActorBody();

	void GMChangeSkin(int nType = 1, int nSkinId = 0, const char* file = NULL);
    ModelAssetData createRoleModel(int model, bool async = false);
	/*初始化*****************************************************************************************************************/
	//mutate_mob = 0， 普通玩家; mutate_mob> 0, 变身野兽玩家
	bool initPlayer(int player_index, int mutate_mob = 0, const char* customjson = NULL, int replacemodle = 0, bool async = false, bool loadHigh = false);
	bool initMonster(const char* modelpath, float modelscale, bool has_avatar = false, const char* effect = NULL, const char* replacetex = NULL, const bool initModel = false);
//tolua_end
	// 
////////////////// no lua export begin
	Rainbow::Model* createRoleModelInstance(ModelAssetData& modelData);

	bool IsOldModelDataLoaded();
	void RegistCustomSkinLoadEvent();
	void RegistModelLoadEvent();

	void initCustomActor(ACTOR_MODEL_TYPE type, std::string modelmark = "", float modelscale = 1.0f, int iType = -1);
	void initFullyCustomActor(int type, FullyCustomModel* custommodel = NULL, std::string skey = "", bool isedit = false, float modelscale = 1.0f);
	void initExampleFCMActor(std::string skey);
	virtual void initEditingPakcingCMActor();
	void initTimeLinePlayer(Rainbow::GameObject* model);

	void initWildmanMob();
	void initCustomModel(std::string modelmark, float modelscale = 1.0f, int iType = -1);
	bool initOffcialModel(std::string modelmark, float modelscale = 1.0f, int iType = -1);
	void initBlockModel(int blockid, float modelscale = 1.0f, bool isInitBlockItemData = false);
	void initBaseModel(int iType, float modelscale = 1.0f);
	//@desc 单独展示avatar 3D资源
	//@param avatarmodel 模型ID
	//@param index 部位ID 定义参考AVATAR_PART_TYPE
	bool initAvatar(int avatarmodel, int index = 0);
	bool initCustomAvatar(std::string modelmark, int index);
	bool initItem(const char* zipPath, const char* omodPath, const char* resPath);
	void initUgcModel(std::string modelmark, UgcAssetType type, bool isTemp = false, float modelscale = 1.0f);
	void initModelWithModelComp(Rainbow::Entity* entity, float modelscale = 1.0f);

	ModelAssetData LoadModelData(const char* path, const char* animfile = NULL, bool async = false);
	static Rainbow::Model* LoadModel(const char* path, const char* animfile = NULL);

private:
	void OnInitOldRoleModelResLoadForPlayer(const Rainbow::EventContent* data);//模型资源加载后的初始化
	void OnInitCustomskinsModelResLoadForPlayer(const Rainbow::EventContent* data);//模型资源加载后的初始化
	void OnRoleModelCreated(Rainbow::Model* pModel);

	void OnInitModelResLoadForPlayer(const Rainbow::EventContent* data);//模型资源加载后的初始化
	void OnInitModelResLoadForNormal(const Rainbow::EventContent* data);//模型资源加载后的初始化

	void DoInitModelForNormal(Rainbow::Model* model);
	void DoInitModelResLoadForPlayer(Rainbow::Model* model);

	void InitUIActorbodyModel();
	void UpdateLocalCustomBounds();
	void CleanLoadModelData();

	Rainbow::Model* LoadDefaultModel();
	Rainbow::SharePtr<Rainbow::Asset> LoadDefaultAsset();
	void loadModel(Rainbow::Model* model);
	//同步模型的资源加载
	void SyncModelData();

/*初始化*****************************************************************************************************************/
#pragma endregion

/*回调*************************************************************************************************************/
#pragma region 回调
public:
	void onDie();
	void onRevive();
	void tick();
	virtual void update(float dtime);
	void onEnterWorld(World* pworld);
	//记录这个逻辑是否用在uimodelView里面的
	virtual void setIsInUI(bool isInUI);
	void onLeaveWorld();
	//void onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum);
	void OnComponentRemoved(const Rainbow::EventContent* evt);
#pragma endregion
/*回调*************************************************************************************************************/

///////////////////////////////////no lua export  end

#pragma region 基础操作
//tolua_begin
	bool isLoaded();
	void setPosition(int x, int y, int z) { setPosition(Rainbow::WorldPos(x, y, z)); }
	void getRotate(float& yaw, float& pitch, float& roll);
	/************************************
	// Method:    设置的角色locomotion旋转
	//			  如果开启了身体旋转插值，会有插值效果。
	// Parameter: float yaw
	// Parameter: float pitch
	// Parameter: float roll
	//************************************/
	void setRotate(float yaw, float pitch, float roll);
	/************************************
	// Method:    立刻转向，身体渲染的方向和locomotion中的方向一致
	// Parameter: yaw 身体旋转方向
	//************************************/
	void rotateTo(float yaw); //做移动到dir方向的动作
		/************************************
	// Method:    插值转向，身体渲染的方向和locomotion中的方向一致
	// Parameter: yaw 身体旋转方向
	//************************************/
	void lerpRotateTo(float yaw); //做移动到dir方向的动作

//tolua_end

////////////////// no lua export begin
	/*位置朝向**********************************************************************************************************/
	void clearMove();
	virtual void setPosition(const Rainbow::WorldPos& pos);
	virtual void setPosition(const Rainbow::Vector3f& pos);
	void setRotation(const Rainbow::Quaternionf& rot);
	//void UpdateOwnerActorTransform(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& rot);
	//void setMoveDir(const Rainbow::Vector3f &dir);
	void resetPos();

	const Rainbow::WorldPos getPosition();
	const Rainbow::Quaternionf getRotation();
	const Rainbow::Matrix4x4f getWorldMatrix();

	void stopLookAt();
	void rotateBodyTo(const WCoord& target, bool sync = true); //旋转身体朝向目标

	void setLockRenderLookAt(bool lock, WCoord target);
	/*位置朝向**********************************************************************************************************/


	void setMotionScale(const char* name, float fScale);		//设置特效大小
	float getMotionScale(const char* name);	//获取特效大小
	int getMotionCount();					//获取特效数
	const char* getMotionNameByIndex(int index, int class_type);//获取特效名
	const char* getCurMotionName();

	virtual Rainbow::Entity* getBodyEntity() { return m_Entity; }
	bool setBodyColor(unsigned int color, bool sheared, const  Rainbow::FixedString& exceptName = "");
	virtual unsigned int getBodyColor();

	float getModelScale() { return m_ModelScale; }
	float getRealScale() { return m_RealScale; }

	void getLocalBounds(Rainbow::BoxSphereBound& bound);

	void setFaceExpression(int i);
	void setFaceExpression(std::string face);

	void setBodyComplexion();
	void setBodyAmbient();

	/*野人*/
	void setHairColor(const char* skinname, unsigned int color, int hairId);//设置头发颜色
	void setFaceModel(int faceId);//设置面部

////////////////// no lua export end

//tolua_begin
	/*外观显示*********************************************************************************************************/
	virtual  void show(bool b, bool ignorenamedispobj = false, bool ignoremotion = true,bool ingnorehp = true) override;

	Rainbow::Vector3f getBindPointPos(int id, Rainbow::Vector3f* offset = NULL);
	virtual void getBindPointPos(int id, int& x, int& y, int& z) override;

	void setScale(float s);
	void setRealScale(float s);
	float getBodyScale() { return m_BodyScale; }
	void setHeadRotRange(float r) { m_HeadRotRange = r; }

	void setBodyType(int type) { m_BodyType = type; }//设置模型类型3.avatar， 4.ar
	virtual int getBodyType() { return m_BodyType; }

	unsigned int GetId() override { return m_Id; }

	virtual int getModelID() { assert(m_PlayerIndex >= 0); return MNSandbox::PlayerIndex2Model(m_PlayerIndex); }
	virtual int getGeniusLv() override { assert(m_PlayerIndex >= 0); return MNSandbox::PlayerIndex2Genius(m_PlayerIndex); }
	virtual int getSkinID() { assert(m_PlayerIndex >= 0);  return MNSandbox::PlayerIndex2Skin(m_PlayerIndex); }
	void changeGeniusLv(int lv) { setPlayerIndex(ComposePlayerIndex(getModelID(), lv, getSkinID())); }
	void setHighlight(bool b);

	void setCustomDiffuseTexture(const char* texturePath);
	void updateAutoTiling(const Rainbow::Vector3f& scale);
	// 绑定角色到挂点，坐骑预览实用
	bool BindActorBody(int anchorId, ActorBody* actor, bool resetRotation = false);
	bool unBindActorBody(ActorBody* actor);	//解绑坐骑上的玩家
//tolua_end
#pragma endregion

#pragma region Avatar
//tolua_begin
	bool addAvatarPartModel(int avatarmodel, int index, bool isAsync = false, int pointId = 0, bool isItem = false) override;

	void EquipAavatarPlayer(EQUIP_SLOT_TYPE slot, int itemid);
	void ClearEquipAvatar(EQUIP_SLOT_TYPE slot);

	void EquipAavatarPlayerByItemId(int itemid);
	void ClearAavatarPlayerByItemId(int itemid);

	void showAvatarPartModel(int index);
	void hideAvatarPartModel(int index, bool isItem = false) override;

	// 重置对应部位的互斥数据
	void resetAvatarPartShied(int index);
	// 添加对应部位的互斥数据
	void addAvatarPartShied(int index, int shieldId);

	// 清空所有跟当前装备位互斥的装备位，包括自身和互斥装备位
	bool clearAvatarPartShied(int index);

	// avt重置为默认状态
	void resetAvatarDefault();

	// avt穿上默认装
	void addDefaultAvatar(bool isAsync = false);

	// avt换脸
	bool exchangePartFace(int avatarmodel, int index, bool isShow, const char* path = nullptr, int skinModel = 0) override;
	
	// avt使用ar贴图
	bool resetAcotorTexture(bool isBlend = false) override;//使用前需要先设置源图

	bool alterBodyStyle(float hue, float saturation, float illumination, float sharpness, bool isBlend) override;
	bool alterAvatarPartColorAync(int modelID, int partID, float r, float g, float b, int block) override;
	bool apllyAvatarPartColor(int modelID, int partID);

	bool IsAvatarPlayer();
	int GetAvatarParID(int partType);
	bool IsShowAvatar(int partType) override;

	void ParseAvatarInfo(jsonxx::Object& avatarInfoJson);//解析def中的def->gamemod->GetAvatarInfo()穿上部件
//tolua_end
//tolua_begin
	int getAvatarListModelId(int index);
//tolua_end
	bool getAvatarListShowStatu(int index);

	bool hasCacheAvatar();
	bool addAvatarPartModelByPath(const Rainbow::FixedString& path, int index, bool isAsync = false);

	void checkAvatarPartEffect(int avatarmodel, int index);
	void addAvatarPartEffect(int avatarmodel, int index, int part);
	bool checkWholeBodyEffect();

	void setAvatarPartModelShow(int index, bool bShow);

	//特效部件
	int getEffectAvatarCount();
	void showEffectAvatar(bool show);
	bool isEffectAvatar(int index);

	bool exchangePartFaceASync(const char* avatarInfo, std::function<void()> callback);
	bool exchangePartFaceByPath(const  Rainbow::FixedString& avatarPath, const  Rainbow::FixedString& skinPath = "");

	void setARTexPaht(const char* path) override { if (!path) return; m_ARTexPath = path; }//这个拼写虽然反了，但是lua代码已经这样用起来了，如果要修改，lua一起改下
	int getAvtBodyID() override { return m_iAvtBodyID; }
	void setAvtBodyID(int id) { m_iAvtBodyID = id; }

	bool alterAvatarPartColor(float r, float g, float b, int partID, int modelID, int block) override;

private:
	// 调色逻辑重构后，旧的调色逻辑接口不对外开放
	bool alterAvatarPartColor(Rainbow::ColourValue clolur, int partID, int modelID, int block);

	bool alterBodyStyleInnel(float hue, float saturation, float illumination, float sharpness, bool isBlend);

	// 获取当前的逻辑装扮显示状态，相比IsShowAvatar接口，此处无关实际最终模型的显示状态，无关异步加载模型是否成功
	bool GetAvatarPartModelShow(int type);

	ActorBodyAvatarComponent* GetAvatarComponent();
#pragma endregion


#pragma region 模型表现
public:
////////////////// no lua export begin
	void saveOriMatMainTexs();
	void clearOriMatMainTexs();

	void setCustomTexture(const char* sType, const char* sPath, int nSubMeshIndex = -1, bool forceMainTex = false, bool isCubeMap = false);
	void setCustomMatVector(const char* sType, const Rainbow::Vector4f vec, int nSubMeshIndex = -1, bool isUpdateTillingData = true);
	void setCustomMatFloat(const char* sType, float val, int nSubMeshIndex = -1);
	void setCustomMatKeyword(const char* sType, bool flag);
	void setCustomMatCullMode(int type);
	void SetCustomMatAutoTiling(bool flag, int type, int nSubMeshIndex = -1);
	void setCustomTexture(const char* sType, Rainbow::SharePtr<Rainbow::Texture2D> tex);
	
	void updateMaterial(const std::string& sPath, bool transparencyShadowFlag = false, int nSubMeshIndex = -1);
	void updateMaterial(ACTOR_CUSTOM_MAT_TYPE matType, bool transparencyShadowFlag = false, int nSubMeshIndex = -1);
	void setCustomMatTransparent(float val, int nSubMeshIndex = -1);
	int getRealSubmeshNum();
	int getAnimTickTime(int anim);
	void setBillBoardText(const char* text);
	void setBillBoardTextNpc(bool bBillBoardTextNpc);
	virtual bool HasModelMeshShow() override;
	virtual void ShowModelMeshShow(bool isShow) override;
	void showRake(int id); //0: 隐藏所有，1：显示rake01  铁耙、钛耙
	void checkMeshToShow();//将showskin中的缓存，执行一遍。

	// 重置绑定对象的loca旋转，使得绑定对象的模型旋转跟主模型根节点旋转在世界坐标系一致
	void ResetBindingObjectRotation(int bindingIndex);

	void changeBodyTex(const char* texname, const char* meshname, bool sync = true);
	void applyBodyColor(unsigned int color, bool sheared, const Rainbow::FixedString& exceptName = "");

	static const char* GetActorCustomMatPath(ACTOR_CUSTOM_MAT_TYPE matType);

////////////////// no lua export end
#pragma endregion

#pragma region Equip
//tolua_begin
	static void clearEquipItems(Rainbow::Model* pmodel);
	virtual void setEquipItem(EQUIP_SLOT_TYPE slot, int itemid);

	void putonCustomEquipPart(EquipmentPartDef* pPartDef, int slot, int nPartIndex);//TODO:自定义装备: 挂载自定义装备部件
	void takeoffCustomEquip(int slot);

	EquipmentPartDef* GetUIEquipmentPartDef();

	void addCustomAnimMap(int key, std::string motionID);
	bool loadCusAnim();

	void addIdleAnimal(int animal, int prop);

	void updateToolModelTexture(int texureIndex = 0); //更改手持道具的模型贴图 code-by:liwentao

	virtual void showSaddle(int id); //0: 隐藏所有，  1: 显示saddle01, 2: 显示saddle02 马鞍
	void showNecklace(int id); //0: 隐藏所有，1：显示necklace01

	void showSkin(const char* skinname, bool show);//显示隐藏模型中某个节点
	void showAllSkins(bool show);

	void getSkinPart(char* name, EQUIP_SLOT_TYPE slot, int itemid);
	void getTexPath(char* name, EQUIP_SLOT_TYPE slot, int itemid);
//tolua_end

////////////////// no lua export begin
	void clearEquipSlot(EQUIP_SLOT_TYPE slot);
	void getEquitMesh(int itemid, int& equipmesh);	// 获取新增野人装备的模型
	void ReleaseUIEquipmentPartDef();
////////////////// no lua export end

//tolua_begin
	bool equipSkinWeapon(int itemid,int skinid = 0); //装备武器皮肤

	virtual const char* getCustomSkins() { return m_CustomSkins.c_str(); }
	void setCustomSkins(const char* customskin) { m_CustomSkins = customskin; }
//tolua_end
	//为假人做的一个放披风的接口
	void equipPifengSpecial(EQUIP_SLOT_TYPE slot, int itemid);
	// 设置装备的显示/隐藏
	void SetHideEquipAvatar(bool value);
	bool GetHideEquipAvatarState();

	bool isCustomEquip(int itemId);//自定义装备
	int getCurShowEquipItemId(EQUIP_SLOT_TYPE slot);

private:
	//TODO:自定义装备
	void setCustomEquip(EQUIP_SLOT_TYPE slot, int itemid);
	void releaseCustomEquip();
	bool isNormalEquip(int itemid);
	void putonNormalEquip(int itemid);
	void equipWeaponItem(EQUIP_SLOT_TYPE slot, int itemid);
	void equipPifengItem(EQUIP_SLOT_TYPE slot, int itemid);
	void equipShoeItem(EQUIP_SLOT_TYPE slot, int itemid, char* skinname);
	bool canPlayerWearEquip();

	ActorBodyEquipComponent* GetEquipComponent();
#pragma endregion
/*头顶显示相关*********************************************************************************************************/
#pragma region UI
public:
//tolua_begin
	void setHeadIconByPath(const char* imageResPath, const char* imageResUVName, int imageWidth /*= 0*/, int imageHeight /*= 0*/,bool isSync=true);
//tolua_end

	/*头顶显示相关*/
	void setDispayName(const char* name, int teamid, int texid = 0, const char* title = "");

	//texid 贴图名字 1卡卡 2妮妮
	void setShowDialog(bool state);
	void setVisibleDispayName(bool b);

//tolua_begin
	void setHPVisible(bool pVisible);
	bool getHPVisible();
//tolua_end

	void setHPVale(int pNow, int pTotale);
	void setHPColor(int r, int g, int b, int alpha = 255);
	void setHPTextrueName(const char* pBg, const char* pProgress);
	Rainbow::MoveByTextMgr* getHPChangeTextMgr();
	void createHPChangeText(const char* pStr, int pFontSize, const Rainbow::ColorQuad& pColor, bool isMainPlayer = false);
	void addNameTexId(int nameTexid);
	void setAchieveIconName(const char* texIcon, const char* texFrame);
	void setBPTitleIconName(std::string titleIcon);
	void setAchieveVisible(bool achieveVisible);
	void setBPTitleIconVisible(bool visible);
	void setVoiceIconVisible(bool voiceVisible);

//tolua_begin
	void setVipIconName(const char* iconPath, const char* texIcon, float scale);
	void setVipIconVisible(bool vipVisible);
//tolua_end
 
//tolua_begin
	void setVipNameColor(int colorR, int colorG, int colorB);
	int  getInnerGraphicsOffest(int itype);
//tolua_end

	void setDispayHomeBillBoard();//家园
	//护甲值
	void setArmorVisible(bool pVisible);
	bool getArmorVisible();
	void setArmorVale(int now, int max, int extra);
	void setArmorColor(int r, int g, int b, int alpha = 255);
	void setArmorTextrueName(const char* pBg, const char* pProgress);
	void setHpExtraValue(int val);
	void setHpTextDisplay(int);

	//tolua_begin
	void setNeedItemIcon(Rainbow::SharePtr<Rainbow::Texture2D> huires, int tick = -1, int huiresWidth = 62, int huiresHeight = 62);// 设置需求物品
	void setHeadDisplayIcon(int itemid, int tick = -1);
	//tolua_end


	void setDispayMusicClubChatBubble();		//创建音乐厅聊天气泡对象及其设置相关参数
	//2021-12-20 codeby: wangyang 会员聊天气泡
	void setMusicClubChatBubbleText(const char* text, bool isShow, int bubble = 0, float tickTime = 0); //设置聊天文字及其是否显示

	void setExchangeItemIcon(Rainbow::SharePtr<Rainbow::Texture2D> huires, int num = 1, int tick = -1, int huiresWidth = 62, int huiresHeight = 62);
	void setSaleItemIcon(Rainbow::SharePtr<Rainbow::Texture2D>  huires, int num = 1, int tick = -1, int huiresWidth = 62, int huiresHeight = 62);
	void setSaleItemIconByString(const std::string& pngName, int num = 1, int tick = -1, int huiresWidth = 128, int huiresHeight = 128);
	void showExchangeBubble(bool show); //只有m_exchangeItemicon和m_saleItemicon都设置过才会显示,单独显示没有意义

//tolua_begin
	void setHeadExchangeDisplayIcon(int exchageItem, int saleItem, bool isActor = false, int exchangeNum = 1, int saleNum = 1, int tick = -1);
//tolua_end

	void updateForOverHead(unsigned int dtick, Rainbow::Vector3f& pos);//update-头顶显示的
	ActorBodyUIComponent* GetUIComponent();
#pragma endregion
/*头顶显示相关*********************************************************************************************************/

/*动作特效**************************************************************************************************************/
#pragma region AnimationMotion

//tolua_begin
	virtual int getCurAnim(int layer) { return m_CurAnim[layer]; }
	virtual int getCurSeqID(int layer) override { return m_CurSeqID[layer]; }

	void playEffect(const Rainbow::FixedString& fxName, bool reset_play, int motion_class);
	void playMotion(const char* name, int motion_class = 0, bool force_play = false, float loopPlayTime = -1.0f);
	void playMotion(const char* name ,float loopPlayTime , const Rainbow::Vector3f& OffsetPosition, const Rainbow::Vector3f& rote, const Rainbow::Vector3f& scale,bool isLoop, int motion_class = 0);
//tolua_end

	void playAttack();
	bool hasPlayingMotion(const char* name);

//tolua_begin
	void stopMotion(const char* name);
	void stopMotion(int motion_class);
	void playCustomMotion(char* zipPath, const char* entPath, bool reset_play = true, int motion_class = 211111); // 211111 :是zipload完之后播放的默认class_type
//tolua_end

//tolua_begin
	virtual bool isPlayingSkinAct(); //20211020 codeby:chenwei 是否播放装扮互动中
//tolua_end

	void playSkinActMotion(const int act, int motion_class = 0, bool force_play = false); //20210929 codeby:chenwei 播放装扮特效接口
	void setNameAndHpVisible(bool isVisble);//20211020 codeby:wangyu 设置昵称和血条显示
	void mobOnDie();

//tolua_begin
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
	void playEffectParticle(int efType, const char* efName);
#endif
	void stopEffectParticle(bool isDetach);
//tolua_end
// 
//tolua_begin
	virtual void setFreeAnimLayer(bool free);
	virtual void setCurAnimId(int animId, int layer);
	void setCurAnim(int anim, int layer);
	void setCurAnimWeapon(int anim, int loopmode);
//tolua_end

	void skillplayAnimBySeqId(int seqId, int inputloopmode, int playLayer);
	void skillStopAnimBySeqId(int seq, bool reset = false);
	int getCurPlayAnimIds(std::vector<int>& list);

	int getAnimSeqId(int anim, int& aniLayer, int& inputloopmode);
//tolua_begin
	// 内部播放动画
	int playAnimInner(int anim, int inputloopmode = -1, float speed = 1.f, int aniLayer = -1); //返回实际的seq id
	// 外部播放动画，记录播放的seqid 需要检测播放状态，播放完需要复位
	int playAnim(int anim, int inputloopmode = -1, float speed = 1.f); //返回实际的seq id
	void playAnimByMotionId(int motionId, bool isLoop = false);

	virtual void playAnimBySeqId(int seq, int inputloopmode = -1, int playLayer = -1);
	virtual void stopAnimBySeqId(int seq, bool reset = false);
	virtual bool playAnimCheck(int seqId, int eInputLoopMode = -1, float speed = 1.f, int layer = -1);
	//IActorBody
	virtual int getNowPlaySeqID() override { return m_NowPlaySeqID; }

	virtual void stopAnim(int anim);
	virtual void stopAllAnim();
	bool playAct(int act);
	void stopAct();
	void setAct(int act);
	void addAnimModel(int fileId);

	void setFollowActId(int actId) { m_iFollowingActId = actId; };
//tolua_end

	bool CheckModelNewAnimLoad(int& seqId);

	void setActTrigger(int act);

	void setAnimSeq(int nSeq)
	{
		nSeq %= 100; //100后重置
		m_nAnimSeq = nSeq;
	}
	//20210926codeby: chenwei 获取动作序列
	virtual int getAnimSeq()
	{
		return m_nAnimSeq;
	}

//tolua_begin
	virtual bool getIsShow() {
		return m_bIsShow;
	}

	bool hasAnimPlaying(int anim);
	virtual bool hasAnimIdPlaying(int animId);
	bool hasSeqPlaying(int seq);
//tolua_end

//tolua_begin
	void setAnimSwitchIsCall(bool call) { m_bAnimSwitchCall = call; }

	//@playType：0右手 1左手 2全部
	void playWeaponAnim(int seqId, int loopMode = -1, float speed = 1.f, int layer = -1, int playType = 0);
	void stopWeaponAnim(int seqId);
	bool hasWeaponPlaying(int seqId, int playType = 0);

	// 新增参数playType ： 0-右手 1-左手 2-全部
	void playWeaponMotion(const char* motion, bool reset_play = true, int motion_class = 0, float motionScale = 1.0f, int playType = 0);
	void stopWeaponMotion(int motion_class);
	void	setIsMonsterSkin(bool b) { m_bIsMonsterSkin = b; }
//tolua_end

	void resetSeqAnimDesc();

	int seqType2ID(int seqtype);
	int actionIDConversion(int actid);

	bool getIsNeedLoadCusAnim() { return m_bIsNeedLoadCusAnim; }//是否需要加载自定义动作
	void setIsNeedLoadCusAnim(bool isNeed) { m_bIsNeedLoadCusAnim = isNeed; }
	void clearNeedLoadCusMotionID() { m_vNeedLoadCusMotionID.clear(); }
	/*const*/ SeqMustAnimDesc* getSeqMustAnimDesc() { return m_suSeqMustPlayDesc; }

//tolua_begin
	virtual bool hasAnimSeq(int seqId) override;
	void resetAnim(int seqId);
	virtual bool hasAnimPlayingById(int seqId);
	//IActorBody
	virtual bool hasAnimPlayEnd(int seqId) override;
	bool addAnimFrameEvent(int seqId, int keyFrame, const std::string& eventName, long long objId);
	void clearAnimFrameEvent(long long objid = -1);
	void clearPendingAnimEvents(int seqId = -1);
	// 0(kCullingModeAlways): 总是运行动画; 1(kCullingModeVisible): 只在看见的时候才运行
	void SetModelAnimPlayerCullingMode(int mode);
//tolua_end
	
	/*
	获取、设置m_nAct
	*/
	virtual int	 getActID() { return m_nAct; }
	void	setActID(int actid) { m_nAct = actid; }
	/*
		获取、设置m_nActTrigger
	*/
	virtual int		getActTriggerID() { return m_nActTrigger; }
	void	setActTriggerID(int id) { m_nActTrigger = id; }
	/*
		获取、设置m_iActSeqId
	*/
	virtual int		getActSeqID() const { return m_iActSeqId; }
	void	setActSeqID(int val) { m_iActSeqId = val; }

	//2021-09-14 codeby:chenwei 设置装扮互动副动作ID标记
	void setSideAct(bool isSideAct);
	virtual bool isSideAct();

	bool PlayActInHand(int actid, int playmode);

private:
	bool updateAction(int act); //20210929 codeby:chenwei 修改播放动作接口参数
	bool updateTriggerAction(int triggerAct); //20211020 codeby:chenwei 剥离触发器动作到新函数
	void clearAction();
	bool isInPlayerAction();

	void updatePlayAnim(float runwalkfactor);
	void updateSkinEffect();
	int convertExtremisAnim(int animBody);

	void playSkinEffect(int skin_id);

	void checkSeqMustPlayAnim();
	bool insertMotionToModel(int motionID);
	bool loadAnimToModel(int stateID, int motionID);

	void OnAnimationEvent(long long objId, int seqId, const std::string& eventName);
	void playSkinActCsvEffect(int seqId); //播放专属动作特效
	void playActorActionEffect(int seqId); //播放动作配置的特效和音效
	void stopActorActionEffect(int seqId); //播放动作配置的特效和音效

	void changeActorActionBySkin(int skinid); //根据皮肤切换动作
	int actionIDConversionReverse(int actid);
#pragma endregion
/*动作特效**************************************************************************************************************/

public:
	void shareShift(bool b);
	//tolua_begin
	void setNeedUpdateSkin(bool bupdate);
	//tolua_end
	void setShowUp(bool bupdate);

	void setIsInStarStationCabin(bool bInStarStationCabin, bool isSetViewMode = true);
	bool isInStarStationCabin();

	void revoverShapeHeight();
	void setkeeplook(bool keep);
	void checkSpectatorBody();
	void SetIsEdit(bool bEdit) { m_bIsEdit = bEdit; } //设置是否在编辑模式中
	void loadEditModelRes();

//tolua_begin
	void setSkinEffect3Playing(bool b) { m_SkinEffect3Playing = b; }	//设置皮肤特效是否显示中（不需要再显示）
//tolua_end

	void UpdateVisiableDistance();
	void SetForceCull(bool value);
#if DEBUG_MODE
	bool IsEntityReleaseing() { return m_IsEntityReleaseing; }
#endif

#pragma region UIModelView上相关的逻辑
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
	virtual void setIsAttachModelView(bool bAttach);
	bool getIsAttachModelView() { return m_IsAttachModelView; }
	virtual bool getIsInUI() { return m_IsInUI; }
#else
	/* tolua会导出接口，所以这里注释
	ModelView *getAttachedModelView(){ return m_UIModelView; }
	int getAttachedModelViewIndex() { return m_ModelViewIndex; }
	void attachUIModelView(ModelView *modelview, int index=0, bool ctrlRotate=true);
	void detachUIModelView(ModelView *modelview, int index=0);
	*/
#endif
	virtual void OnActorBodyRelease();   //根据具体的逻辑做释放管理的
	virtual void SetReleaseType(ActorBodyReleaseType onwerType);
	virtual int GetReleaseType() { return m_ReleaseType; }
	bool IsUIActorBody();

	virtual void AttachScene(Rainbow::GameScene* scene) override;
	void AttachCurrentScene();
	virtual Rainbow::GameScene* GetScene() const override;

	typedef std::function<void(ActorBody*, Rainbow::GameScene*)> AttachSceneCallback;
	AttachSceneCallback  OnAttactchScene;
	void SetObjectCache(ObjectCacheBase* cache);
	ObjectCacheBase* GetObjectCache();
#pragma endregion

#pragma region BaseInterface
//tolua_begin
	bool isPlayer() { return m_PlayerIndex > 0; }
	virtual int getCurAnimWeapon() override { return m_CurWeaponSeqID; }
	virtual Rainbow::ModelData* getModelData();
	virtual Rainbow::Model* getModel();

	bool isLoadModelFinished()
	{
		return !m_ModelIsLoading;
	}

//tolua_end


/////////////////////////////////////////////////////////
//no  lua export start
	Rainbow::Entity* getDorsumEntity();
	Rainbow::NameText3D* getNameDispObj();

	/*
		获取资源加载路径
	*/
	const char*	getResourcesPath() override { return m_ResourcePath.c_str(); }

	/*
		获取、设置m_pInitTex
	*/
	Rainbow::SharePtr<Rainbow::Texture2D> getInitTex() { return m_pInitTex; }
	//void			setInitTex(MINIW::Texture* val) { m_pInitTex = val; }

	/*
		获取m_FaceMesh
	*/
	Rainbow::IModelMeshRenderer* getFaceMesh() { return m_FaceMesh; }

	/*
		获取m_HelmetModel
	*/
	Rainbow::MovableObject* getHelmetModelModel();

	/*
		获取m_WeaponModel
	*/
	BaseItemMesh* getWeaponModel();
	/*
		获取m_WeaponModel_left
	*/
	BaseItemMesh* getLeftWeaponModel();
	/*
		获取、设置m_Entity
	*/
	Rainbow::Entity* getEntity() { return m_Entity; }
	void setEntity(Rainbow::Entity* entity);
	/*
		获取、设置m_shapeAnimEntity
	*/
	Rainbow::Entity* getShapeAnimEntity() { return m_shapeAnimEntity; }
	void setShapeAnimEntity(Rainbow::Entity* entity);

	/*
		获取、设置m_HeadBoneScale
	*/
	float	getHeadBoneScale() const { return m_HeadBoneScale; }
	void	setHeadBoneScale(float scale) { m_HeadBoneScale = scale; }
	/*
		获取、设置m_HeadBoneID
	*/
	int		getHeadBoneID() const { return m_HeadBoneID; }
	void	setHeadBoneID(int id) { m_HeadBoneID = id; }
	/*
		获取、设置m_ModelPath
	*/
	Rainbow::FixedString		getModelPath() const { return m_ModelPath; }
	void	setModelPath(Rainbow::FixedString& value) { m_ModelPath = value; }
	/*
		获取、设置m_bNeedRecoverShapeHeight
	*/
	bool	getIsNeedRecoverShapeHeight() const { return m_bNeedRecoverShapeHeight; }
	void	setIsNeedRecoverShapeHeight(bool value) { m_bNeedRecoverShapeHeight = value; }
	/*
		获取、设置m_NameObjHeight
	*/
	int		getNameObjHeight() const { return m_NameObjHeight; }
	void	setNameObjHeight(int height) { m_NameObjHeight = height; }
	/*
		获取、设置m_fBeforeShapeHeight
	*/
	float	getBeforeShapeHeight() const { return m_fBeforeShapeHeight; }
	void	setBeforeShapeHeight(float height) { m_fBeforeShapeHeight = height; }

	void	setShowSaddle(int id) { m_ShowSaddle = id; }
	void	setShowNecklace(int id) { m_ShowNecklace = id; }
	void	setShowRake(int id) { m_ShowRake = id; }

	void	setNameBlickStartTime(int time);

	bool	isMonsterSkin() { return m_bIsMonsterSkin; }
	virtual bool	isAvatarModel() { return m_CustomSkins.size() > 0; }

	/*
		获取、设置m_ctrlRotate
	*/
	bool	getControlRotation() { return m_ctrlRotate; }
	void	setControlRotation(bool b) { m_ctrlRotate = b; }
	void	setNeedAlpha(bool b) { m_NeedAlpha = b; }
	void	setNeedUpdateAnim(bool b);
	void	setNeedUpdateSkinEffect(bool b) { m_NeedUpdateSkinEffect = b; }
	//void	setNeedUpdateBallEffect(bool b) { m_NeedUpdateBallEffect = b; }
	void	setNeedUpdateLookUp(bool b) { m_NeedUpdateLookUp = b; }
	void	setNeedUpdateRenderYawOffset(bool b) { m_NeedUpdateRenderYawOffset = b; }
	/*
		是否看向某处
	*/
	bool	isLookAt() { return m_isLookAt; }

	/*
		获取、设置m_hairColor
	*/
	unsigned int	getHairColor() const { return m_hairColor; }
	void	setHairColor(unsigned int color) { m_hairColor = color; }
	/*
		获取、设置m_hairId
	*/
	int	getHairId() const { return m_hairId; }
	void setHairId(int hairId) { m_hairId = hairId; }
	/*
		设置m_hairSkinName
	*/
	void setHairSkinName(const std::string& hairskinname) { m_hairSkinName = hairskinname; }
	/*
		设置m_faceId
	*/
	void setFaceId(int faceid) { m_faceId = faceid; }

	//中断下载
	virtual void BreakAsyncLoad();

	EXPORT_VALUE(isLookAt, bool);
	/*
		看向的方向
	*/
	EXPORT_VALUE(LookAt, Rainbow::Vector3f);
	/*
		看向的偏航角 m_LookTargetYaw
	*/
	EXPORT_VALUE(LookTargetYaw, float);
	/*
		看向的俯仰角 m_LookTargetPitch
	*/
	EXPORT_VALUE(LookTargetPitch, float);
	/*
		看向的俯仰角差 m_DeltaLookPitch
	*/
	EXPORT_VALUE(DeltaLookPitch, float);
	/*
		看向的偏航角差  m_DeltaLookYaw
	*/
	EXPORT_VALUE(DeltaLookYaw, float);
	/*
		头部的偏航角 m_RotationYawHead
	*/
	EXPORT_VALUE(RotationYawHead, float);
	/*
		整体的偏航角  m_RenderYawOffset
	*/
	EXPORT_VALUE(RenderYawOffset, float);
	/*
		整体的目标偏航角  m_TargetYaw//旋转量
	*/
	EXPORT_VALUE(TargetYaw, float);
	
	void setModelType(int modelType) { m_ModelType = modelType; }
	int getModelType() { return m_ModelType; }
	void setExtraData(int extraData) { m_ExtraData = extraData; }
	int getExtraData() { return m_ExtraData; }

	bool isLerpRotation() const { return m_LerpRotation; }

	void	setPlayerIndex(int palyerIndex);
	//tolua_begin
	virtual int		getPlayerIndex() { return m_PlayerIndex; }
	int		getMutateMob() { return m_MutateMob; }
	void	resetBoneTM();
	//tolua_end

	virtual int		getPlayerFrameId() { return m_PlayerFrameId; }
	void	setPlayerFrameId(int playerFrameId) { m_PlayerFrameId = playerFrameId; }
	void	setMutateMob(int mutatemob) { m_MutateMob = mutatemob; }
	virtual float	getLookTargetPitch() { return m_LookTargetPitch; }
	void	setTakeoffAble(bool able) { m_takeoffAble = able; }
#pragma endregion

#pragma region ThornBall
public:
	void createThornBallMeshModel(int anchorId, Rainbow::Vector3f pos);
	void removeThornBallMesh(bool isAll, int num = 1);
	void showThornBallMesh(bool is);
	std::vector<MNSandbox::ThronBallStruct> getThornBallModeInfo();
private:
	ActorBodyThornBallComponent*    GetThornBallComponent();
	ActorBodyThornBallComponent*	m_ThornBallComponent = nullptr;
#pragma endregion

public:
	//tolua_begin
	Rainbow::Vector3f m_MoveDir;	//移动方向？C++内并没有使用逻辑，lua用？
	bool m_bIsShapeShift;		//是否连带控制名字等？

	int m_NameObjHeight;		//名字的高度
	int m_HeadEffectObjHeight;		//头顶特效的高度
	//tolua_end

	MNSandbox::Callback		m_ModelviewDetachEventCallbackHC;

	std::function<void(Rainbow::SharePtr<Rainbow::Texture2D>)> modAvtCallback;
	//Rainbow::Stabilizer<Rainbow::WorldPos, Rainbow::StabilizerHelper<Rainbow::WorldPos>> m_PositionStabilizer;
	friend class AbsBodyAR;
	friend class ActorBodyAvatarComponent;
	friend class ActorBodyUIComponent;
	friend class ActorBodyEquipComponent;
	friend class ActorBodyThornBallComponent;
	friend class ActorBodyAttachmentHandlerComponent;

	//这个接口直接将m_entity赋NULL, 需要保证m_pEntity已经被其他地方释放了.
	//适用于多个地方持有同一个entity时
	void clearEntity();

	//获取武器的枪口的位置
	bool GetWorldPosFromWeapon(int anchorId, Rainbow::Vector3f& boneWorldPos);

	void setOwnerActorNull() 
	{
		m_OwnerActor = nullptr;
		m_OwnerPlayer = nullptr;
	}
	ClientActor* getOwnerActor() {
		return m_OwnerActor;
	}
	void setHurtTick(int nValue) {
		m_HurtTicks = nValue;
	}
	bool IsUpdating() {
		return m_IsUpdating;
	}
	bool IsRebuildModel()
	{
		return m_bIsRebuildModel;
	}

	void setRebuildModel(bool bValue)
	{
		m_bIsRebuildModel = bValue;
	}
	void setInterpRotation(bool b) { m_bInterpRotation = b; }
	void setHeadLerpSpeed(float spd) { m_fHeadLerpSpeed = spd; }
	//tolua_begin
	void setBodyLerpSpeed(float spd) { m_fBodyLerpSpeed = spd; }
	float getBodyLerpSpeed() { return m_fBodyLerpSpeed; }
	//tolua_end
	void setDebugDrawLerpDirection(bool b) { m_bDebugDrawLerpDirection = b; }

	void ResetGameObjParent();
	void setAblePlayOtherAnim(bool type);

    //marked by David, 2024/7/31, setposition是高频调用的接口，不应该用事件的方式，事件通知方式额外开销太大
    // 通知
    //MNSandbox::Notify<> m_notifyPositionChanged; // 位置变化
    std::function<void()> m_notifyPositionChanged;

private:
	void InitFaceTexts(Rainbow::Model* model, core::string resourcePath);
	void updateLookAt(float dt);
	void updateRenderYawOffset(float dt);

	void checkReplaceTexture();
	void replaceStoreAndFaceTex();

	void updateForModelView(float dtime, Rainbow::Vector3f& pos, ClientActor* riding, bool setbindrot, Rainbow::Quaternionf& quat);//update-UI的
	void updateForPlayer(unsigned int dtick); //update-player
	void ReleaseMainEntity();
	void updateImpl(float dtime);
	void updatePlayerCtrl();
	void updatePosAndBindrot(ClientActor*& riding, Rainbow::Vector3f& pos, bool& setbindrot, Rainbow::Quaternionf& quat);
	void updateMusicChatBubble(Rainbow::Vector3f& pos);
	void updateShadow();

	int getAnimLayer(int seqid);
	// 是否为上半身动画
	bool isUpbodyAnim(int seqid);

protected:
	/*外部关联对象相关*/
	ClientActor* m_OwnerActor;				//绑定Actor对象
	ClientPlayer* m_OwnerPlayer;			//绑定Player对象
	bool m_LastSneak = false;      // 上一次动画的sneak状态
	int m_LastMoveDir = 0; // 上一次动画的移动方向
	World* m_World;							//绑定world
	bool m_LastRightClick = false; // 上一次动画的右键状态

	/*渲染对象和模型相关*/
	Rainbow::Entity* m_Entity;				//主渲染对象？
	//Rainbow::Model* m_Model;					//主模型？
	Rainbow::SharePtr<Rainbow::Texture2D> m_ExchangeTex;			//在制作mod的时候用于mod显示
	Rainbow::SharePtr<Rainbow::Texture2D>* m_FaceTexs;			//用于存放各种表情脸部贴图

	ActorBodyEquipComponent* m_EquipComponent = nullptr;

	// UI组件，存放UI相关数据
	ActorBodyUIComponent* m_UIComponent = nullptr;

#pragma region Resource
	/*资源控制变量*/
	Rainbow::SharePtr<Rainbow::Asset> m_LoadModelData;		//模型资源句柄
	Rainbow::FixedString m_ModelPath;			//模型文件路径
	Rainbow::FixedString m_ReplaceTex;		//替代纹理文件路径
	Rainbow::FixedString m_Effect;			//默认动作名
	core::string m_ResourcePath;				//记录模型和贴图的加载目录
	bool m_ModelIsLoading = false;			//模型资源是否正在加载

	Rainbow::IModelMeshRenderer* m_FaceMesh;		//模型面部

	Rainbow::SharePtr<Rainbow::Texture2D> m_pInitTex;			//模型面部纹理
	core::string m_ARTexPath;				//AR纹理路径
	Rainbow::SharePtr<Rainbow::Texture2D> m_pSourceTex;		//模型纹理数据-source
	Rainbow::SharePtr<Rainbow::Texture2D> m_pNowBodyTex;		//模型纹理数据-？
	Rainbow::SharePtr<Rainbow::Texture2D> m_pOcclusionTex;	//模型纹理数据-闭合？
	Rainbow::SharePtr<Rainbow::Texture2D> m_pTransitionTex;	//模型纹理数据-转换？
	Rainbow::PPtr<Rainbow::GameScene> m_AttachScene;
	ModelAssetData m_OldModelData;
#pragma endregion

	int m_ModelType = 0;
	int m_ExtraData = 0;

	bool m_NeedAlpha;																	//资源加载是否需要解析透明通道？
	short m_CheckCustomModelBindTick;													//执行客机异步加载模型数据的计数 - 每20个tick执行一次

	/*位置大小*/
	float m_ModelScale;						//大小比例
	float m_RealScale;						//大小比例
	int m_BodyType;							//body 类型 avatar==3 官方导入模型==99
	float m_BodyScale;						//大小比例

	/*旋转朝向*/
	/*笛卡尔坐标系
	pitch是围绕X轴旋转，也叫做俯仰角
	yaw是围绕Y轴旋转，也叫偏航角
	roll是围绕Z轴旋转，也叫翻滚角
	*/
	float m_YawOffsetHelper;			//偏航角度辅助
	int m_YawOffsetHelpTicks;			//偏航调整计数
	float m_HeadRotRange;				//头朝向范围
	Rainbow::Quaternionf m_qInterpRotation;
	Rainbow::Quaternionf m_qInterpYawHeadRotation;
	bool m_bInterpRotation;
	bool m_bDebugDrawLerpDirection;
	float m_fHeadLerpSpeed;
	float m_fBodyLerpSpeed;

	float m_LastRenderYawOffset;
	float m_LastRotationYawHead;
	int m_HurtTicks;					//受伤害特效播放倒计时

	bool m_ctrlRotate;					//可否控制旋转
	bool m_SkinEffect3Playing;			//皮肤特效显示中？
	bool m_SkinEffect4Playing;			//皮肤特效4显示中

	bool m_LerpRotation;				//旋转-？
	int m_LerpRotationTick;				//旋转周期				
	float m_OriginRotation;				//初始旋转值
	int m_LerpRotationStartMarker;		//累计旋转量

	bool m_NeedUpdateLookUp;			//是否需要更新朝向
	bool m_NeedUpdateRenderYawOffset;	//是否更新偏航转向？

	bool m_LockRenderYawOffset;			//强制使整体模型的偏航角朝向某个目标点
	WCoord m_LockRenderYawLookTarget;	//强制使整体模型的偏航角朝向的目标点
	std::map<std::string, bool> m_vMeshToShow;		//将要显示的模型内容

	/*动作控制*/
#pragma region Animation
	std::map<int, int> m_vIdleAnimals;				//待机动作库？
	std::vector<char*> m_pEffectID;				//播放过的特效
	int m_nAct;										//玩家做的动作？
	int m_nActTrigger;								//玩家触发的动作？
	int m_iActSeqId;								//自定义动作--这里是指指定动作？

	int m_nAnimSeq; // 动作序列号，如果动作需要支持打断，可以使用该序列号

	std::set<int> m_vLoadedAnimSeqs;	//按需加载过资源的动作ID

	bool m_bSideAct;  //2021-09-14 codeby:chenwei 是否是装扮互动动作中的副动作
	int m_nSkinActPartnerUin; //20210927 codeby：chenwei 舞伴uin

	int m_CurWeaponSeqID;					//武器的动作序列类型
	int m_CurAnim[2];						//播放的动作序列类型
	int m_CurSeqID[2];						//播放的动作序列ID
	bool m_bFreeAnimLayer = false;			//true为不受逻辑影响的动画播放
	int m_NowPlaySeqID;						//当前播放的动作序列ID
	int m_NowPlayAnimLayer;					//当前播放的动画层
	bool m_bIsCheckSeqList;					//是否要判断多段连续动画
	bool m_bIsWinking;						//是否在眨眼
	unsigned int m_unWinkTick;				//眨眼tick统计

	// 直接通过playAnim播放的动画，需要检查是否播放完成
	int m_CheckPlaySeq = 0; // 检查播放的seqid
	int m_CheckAnim = 0; // 检查播放的动画
	bool m_ForceSyncAnim = false; // 强制同步动画

#ifdef DEDICATED_SERVER
	std::map<int, uint64_t> m_ActivePlaySeqList;   // 云服用，记录最近的两个动作，用于hasanimplaying判断
#endif
	/*自定义动作*/
	std::map<int, int> m_vCustomAnimMap;	//自定义动作库
	bool m_bIsNeedLoadCusAnim;				//是否需要加载自定义动作
	int m_iCusAnimCount;					//自定义动作数量
	int m_CurMotionId;						//记录 playAnimByMotionId 参数 动作Id 0:已播放过
	bool m_CurMotionIdIsLoop;				//记录 playAnimByMotionId 参数 动作Id是否循环
	std::vector<int> m_vNeedLoadCusMotionID;//需要加载的自定义动作id
	bool m_bIsNeedRemoveCusMotion;			//是否需要移除自定义动作

	/*Avatar相关， 新引擎底层有资源cache机制，没必要在这里做cache了*/
	//std::map<int, Rainbow::SharePtr<Rainbow::Texture2D>> m_mAvtBasetex;	//Avatar纹理数据？
	//std::map<int, Rainbow::SharePtr<Rainbow::Texture2D>> m_mAvtMasktex;	//Avatar纹理数据-？

	SeqMustAnimDesc* m_suSeqMustPlayDesc=nullptr;				//连续播放的硬直动画描述
	int m_iAvtBodyID;									//avatar 模型id 暂时不支持游戏角色
	bool m_bAnimSwitchCall;								//动作切换是否需要回调lua 做一些特殊处理

#pragma endregion

	/*野人的一些设定*/
	std::string m_hairSkinName;				//发型名字
	int m_hairId;							//发型Id
	unsigned int m_hairColor;				//发型颜色
	int m_faceId;							//面部ID

	std::string m_CustomSkins;		//自定义皮肤特效路径
	WCoord m_LastSkinEffectPos;		//记录上一次特效播放位置
	int m_SkinEffectCount;			//播放计数
	bool m_SkinFlyingEffect;		//是否飞行特效

	bool m_NeedExchangeTexture;		//是否需要更换纹理
	bool m_NeedBackgroundloadModel;	//是否异步加载模型文件

	bool m_NeedUpdateAnim;			//是否需要更新状态？
	bool m_NeedUpdateSkinEffect;	//是否需要更新skin特效
	//bool m_NeedUpdateBallEffect;	//是否需要更新Ball特效
	bool m_IsInUI;					//是否用于UI显示

	bool m_hasAvatar;						//是否带avatar
	bool m_hasShearedColor;					//是否有颜色-剪切？
	bool m_Sheared;							//是否剪切？
	bool m_bIsMonsterSkin;					//怪物用了皮肤
	unsigned char m_ShowSaddle;				//id-马鞍
	unsigned char m_ShowNecklace;			//id-项链
	unsigned char m_ShowRake;				//id-耙状工具
	char m_ForceCull;                       //是否强行裁剪不显示
	unsigned int m_BodyColor;				//模型颜色
	int m_iImportModelType;					//导入模型的类型
	
	bool m_bIsShow;							//是否显示主体
	bool m_bkeeplook;						//是否保持看的动作
	bool m_bupdateSkin;						// Actor改变生物外观后是否更新显示模型部件
	bool m_bShowUp;							// Actor改变生物外观后是否模型需要上移显示
	bool m_bInStarStationCabin;				//actor是否在星站传送舱中
	bool m_IsUpdating = false;				//是否在更新中
#if DEBUG_MODE
	bool m_IsEntityReleaseing = false;
#endif
	bool m_bIsRebuildModel = false;
	//TODO:unrealskin
	//int m_nUSCurSkinType;					//0:无 1:角色 2:皮肤 3:自定义 4:自定义多帧
	//UnrealSkinMotion* m_pUSSkinMotion;		//虚拟皮肤动画？

#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
	bool m_IsAttachModelView;			//是否绑定了ModelView
#else
	ModelView* m_UIModelView;			//绑定的ModelView
	int m_ModelViewIndex;				//在ModelView中的索引
#endif

	std::map<std::string, const char*>  m_motionNames; //特效索引
	int m_iFollowingActId = 0;						//跟随动作id
	bool m_ablePlayerOtherAnim;
	bool m_ownerActorTriggerProjecttile;
#pragma region ActorBodyer

private:
	/*其他一些独立的控制变量*/
	int		m_PlayerIndex;				//player_index==-1表示不是player
	int		m_PlayerFrameId;			//玩家头像框id
	int		m_MutateMob;				//0是玩家，>0 是野兽
	int		m_HeadBoneID;				//骨骼ID-头
	float	m_HeadBoneScale;			//骨骼比例-头
	Rainbow::Entity* m_shapeAnimEntity;	//外观轮廓？测试用？
	float m_fBeforeShapeHeight;			//上一次轮廓高度
	bool m_bNeedRecoverShapeHeight;		//是否需要重置轮廓高度
	bool m_bIsEdit; //是否正在编辑中
	bool m_IsPlayerModel = false;

	// avatat组件，存avatar相关数据结构
	ActorBodyAvatarComponent* m_AvatarComponent = nullptr;
	ActorBodyAttachmentHandlerComponent* m_AttachmentHandlerComponent = nullptr;
#pragma endregion

	int  m_ReleaseType = kActorBodyNone;   //这个类的内存管理权
	int m_CurToolID;

	ObjectCacheBase* m_ObjectCache;
	unsigned int  m_Id;

	std::vector<UVInfo> m_uvInfo;
	AUTO_TILING_TYPE m_uvAutoTileType;
	std::vector<Rainbow::SharePtr<Rainbow::Texture2D>> m_oriMatMainTexs;

	bool m_takeoffAble;
#if HUD_TEST
	int m_titileId=-1;
#endif
#ifdef VERSION_MINICODE
	bool LoadPlayModelMiniCode(const char* modelPath, const char* animpath);
	Rainbow::SharePtr<Rainbow::AnimationData> m_LoadAnimationDataMiniCode;
	bool m_bNeedClearEquipMiniCode = false;
#endif

	bool SyncPlayAnim(int seqid, int layer, int targetSeqId);
}; //tolua_exports

EXPORT_SANDBOXGAME const Rainbow::FixedString& GetSkinEquipName(int slot, int level);
EXPORT_SANDBOXGAME const Rainbow::FixedString& GetSkinName(int slot, int level);
EXPORT_SANDBOXGAME extern int SeqType2ID(int seqtype, bool ispayer = false);
EXPORT_SANDBOXGAME extern int SeqID2Type(int seqId, bool ispayer = false);
