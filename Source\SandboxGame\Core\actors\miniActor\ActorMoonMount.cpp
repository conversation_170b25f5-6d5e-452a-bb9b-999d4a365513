#include "ActorMoonMount.h"
#include "ClientPlayer.h"
#include "MpActorManager.h"
#include "ProjectileFactory.h"
#include "ProjectileLocoMotion.h"
#include "RiddenComponent.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "WorldManager.h"
#include "ActorAttrib.h"
#include "MobAttrib.h"
#include "ClientActorProjectile.h"

using namespace MINIW;

ActorMoonMount::ActorMoonMount(void)
{
	for(size_t i = 0; i < sizeof(m_fSkillCD)/sizeof(float); ++i)
	{
		m_fSkillCD[i] = 0.0f;
	}

	m_fAttackSoundTime = 0.0f;
	m_bCanPlayEffectReadyToFire = false;
	m_MoonDelayTime =2.0f;
	m_MoonAttackSkillCDEffectTime =0.0f;
	m_bMounted = false;
}


ActorMoonMount::~ActorMoonMount(void)
{
}

bool ActorMoonMount::init(int monsterid)
{
	return ActorHorse::init(monsterid);
}

void ActorMoonMount::tick()
{	
	ActorHorse::tick();
	for(size_t i = 0; i < sizeof(m_fSkillCD)/sizeof(float); ++i)
	{
		m_fSkillCD[i] -= GAME_TICK_TIME;
		if (m_fSkillCD[i] <= Rainbow::EPSILON)
		{
			m_fSkillCD[i] = 0.0f;
		}
	}

	if (m_MoonAttackSkillCDEffectTime > Rainbow::EPSILON)
	{
 		m_MoonAttackSkillCDEffectTime -= GAME_TICK_TIME;
		if (m_MoonAttackSkillCDEffectTime <= Rainbow::EPSILON)
		{
			m_MoonAttackSkillCDEffectTime = 0.0f;
		}
	}

}

void ActorMoonMount::update(float dtime)
{
	ActorHorse::update(dtime);
#ifndef IWORLD_SERVER_BUILD
	if (m_fAttackSoundTime > Rainbow::EPSILON)
	{
		m_fAttackSoundTime -= dtime;
		if (m_fAttackSoundTime <= Rainbow::EPSILON)
		{
			if (m_Def)
			{
				auto sound = getSoundComponent();
				if (sound)
				{
					sound->playSound(m_Def->AttackStopSound2.c_str(), 1, 1, 6);
				}
			}
		}
	}
#endif

	if(m_MoonDelayTime >= Rainbow::EPSILON)
	{
		m_MoonDelayTime -= dtime;
		if (m_MoonDelayTime < Rainbow::EPSILON)
		{
			m_bCanPlayEffectReadyToFire = true;
		}
	}

	if(m_fSkillCD[0] <= Rainbow::EPSILON && m_bCanPlayEffectReadyToFire)
	{
		m_bCanPlayEffectReadyToFire = false;
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("horse_3478_callstar_cooldown");
			effectComponent->playBodyEffect("horse_3478_callstar_ready");
		}
	}

	if (getRiddenByActor() != NULL)
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(getRiddenByActor());
		if (player && player->hasUIControl())
			OnActorMoonMounted();
	}
}

void ActorMoonMount::checkBindPlayer(bool isinit)
{
	ActorHorse::checkBindPlayer(isinit);
	if(!m_pWorld || m_pWorld->isRemoteMode()) 
	{
		return;
	}

	if(m_BindUIN <= 0)
	{
		int mobid = m_Def->ID;
		if(mobid ==STAR_MOUNT_ID_LEVEL_1 || mobid == STAR_MOUNT_ID_LEVEL_2)
		{
			setNeedClear();
		}

		return;
	}
}

bool ActorMoonMount::getHorseFlySkills(float *skillvals)
{
	bool ret = false;
	if (hasHorseSkill(HORSE_SKILL_MOONRISE))
	{
		ret = getHorseSkill(HORSE_SKILL_MOONRISE, skillvals);
	}
	else
	{
		ret = ActorHorse::getHorseFlySkills(skillvals);
	}

	return ret;
}

float ActorMoonMount::getFallHurtRate()
{
	if (hasHorseSkill(HORSE_SKILL_MOONRISE))
	{
		return 0.2f;
	}
	else
	{
		return ActorHorse::getFallHurtRate();
	}
}

void ActorMoonMount::doStarFireSkill()
{
	if(hasHorseSkill(HORSE_SKILL_STARLIGHT))
	{
		if (!m_pWorld || m_pWorld->isRemoteMode())
		{
			return;
		}

		ClientPlayer *player = dynamic_cast<ClientPlayer*>(GetWorldManagerPtr()->findActorByWID(getRiddenByActorID()));
		if (!player)
		{
			return;
		}

		float skillvals[7] ={0.0};
		if (hasHorseSkill(HORSE_SKILL_STARLIGHT)) 
			getHorseSkill(HORSE_SKILL_STARLIGHT, skillvals);
		m_fSkillCD[0] = skillvals[0] <= Rainbow::EPSILON ? 30.0f: skillvals[0];
		ClientActorProjectile *projectile = ProjectileFactory::throwItemByActor(m_pWorld, this,0.0f, (int)skillvals[1], false, false);
		if (projectile)
		{
			projectile->setShootingActor(this);
			
			if(getLocoMotion())
			{
				projectile->m_StartPos = getLocoMotion()->getPosition();
			}
			ProjectileLocoMotion *project = static_cast<ProjectileLocoMotion *>(projectile->getLocoMotion());

			//设置投射物星星弹的位置
			projectile->setPosition(
				(int)(projectile->m_StartPos.x), 
				(int)(projectile->m_StartPos.y + 400), 
				(int)(projectile->m_StartPos.z));

			Rainbow::Vector3f dir =Rainbow::Vector3f(0.0f,0.0f,0.0f);
			float yaw = getLocoMotion()->m_RotateYaw;//获取坐骑Y轴旋转的角度
			if(getLocoMotion())
			{
				PitchYaw2Direction(dir,yaw,(-skillvals[2]));//获取投射物星星弹的发射方向 
			}

			if(project)
			{
				const ProjectileDef *projectileDef = GetDefManagerProxy()->getProjectileDef((int)skillvals[1], true);
				if (projectileDef)
				{
					project->setThrowableHeading(dir,projectileDef->InitSpeed,0.0f);
				}
				project->gotoPosition(project->getPosition(), dir.y, dir.x);
			}
					
		}

		if(getLocoMotion())
		{
			playAnim(SEQ_STARFIRE);
		}
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("horse_3478_callstar_fire");
		}

		if (m_Def)
		{
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound(m_Def->AttackSound2.c_str(), 1, 1, 6);
			}
		}

		PB_Horse_SkillCDHC skillCDHC;
		skillCDHC.set_actorid(getObjId());
		skillCDHC.set_index(1);
		skillCDHC.set_cd(m_fSkillCD[0]);
		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_HORSE_SKILLCD_HC, skillCDHC, this, false);
	}
}

void ActorMoonMount::doSkillAttack(ClientActor *target)
{
	if(m_fSkillCD[0] <= Rainbow::EPSILON)
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("horse_3478_callstar_ready");
		}
		doStarFireSkill();
		m_bCanPlayEffectReadyToFire = true;
	}
	else
	{
		if(m_MoonAttackSkillCDEffectTime <= Rainbow::EPSILON)
		{
			m_MoonAttackSkillCDEffectTime = 3.0f;
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("horse_3478_callstar_cooldown");
			}

			if(getLocoMotion())
			{
				playAnim(SEQ_STARFIRE_CD);
			}

			playAttackSound();
		}
		
	}	
}

void ActorMoonMount::startCharge()
{
	ActorHorse::startCharge();
}

void ActorMoonMount::endCharge()
{
	if(getFlying())
	{
		setFlying(false);
		if(hasHorseSkill(HORSE_SKILL_MOONRISE))
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect("horse_3478_fly");
			}
		}
	}

	if(!isInCharge()) 
	{
		return;
	}


	if(!m_pWorld->isRemoteMode() && getLocoMotion() && getLocoMotion()->m_OnGround)
	{
		float t = getChargeProgress();
		float skillvals[7] = {0.0f};
		if (getHorseFlySkills(skillvals))
		{
			m_fEnergy = 20* skillvals[2] * t;
			setFloatingStatus(true);

			setFlagBit(ACTORFLAG_FLOATAGE, true);
			setHorseFlagBit(HORSE_FLAG::FLYING, true);//飞行	// 20220422：增加飞行状态 codeby： huangrui
			if(hasHorseSkill(HORSE_SKILL_MOONRISE))
			{
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("horse_3478_fly");
				}
			}

			playAnim(SEQ_MOONFLY);
			if (m_Def)
			{
				auto sound = getSoundComponent();
				if (sound)
				{
					sound->playSound(m_Def->AttackSound2.c_str(), 1, 1, 6);
				}
			}
		}
		else
		{
			getLocoMotion()->m_Motion.y = (m_JumpHeight - 40.0f)*t + 40.0f;

			Rainbow::Vector3f fdir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
			float forwardspeed = 20.0f;

			getLocoMotion()->m_Motion.x += fdir.x*forwardspeed*t;
			getLocoMotion()->m_Motion.z += fdir.z*forwardspeed*t;
			playAnim(SEQ_JUMP);
		}
	}

	if (m_pWorld->isRemoteMode() && getLocoMotion() && getLocoMotion()->m_OnGround)
	{
		float skillvals[7] = {0.0f};
		if (getHorseFlySkills(skillvals))
		{
			float t = getChargeProgress();
			m_fEnergy = 20 * skillvals[2] * t;
			setFloatingStatus(true);
			setFlagBit(ACTORFLAG_FLOATAGE, true);
		}

	}

	resetCurCharge();
}

void ActorMoonMount::setSkillCD(int index, float cd)
{
	if (index < 0 || index >= (sizeof(m_fSkillCD)/sizeof(float)))
	{
		return;
	}

	m_fSkillCD[index] = cd;
}

float ActorMoonMount::getSkillCD(int index)
{
	if (index < 0 || index >= (sizeof(m_fSkillCD)/sizeof(float)))
	{
		return 0.0f;
	}

	return m_fSkillCD[index];
}

void ActorMoonMount::playAttackSound()
{
	m_fAttackSoundTime = 0.2f;
}

void ActorMoonMount::OnActorMoonMounted()
{
	if(m_bMounted)
	{
		return;
	}

	int mobid = m_Def ? m_Def->ID : 0;
	if(mobid == STAR_MOUNT_ID_LEVEL_1 || mobid == STAR_MOUNT_ID_LEVEL_2) 
	{
		m_bMounted = true;
		MINIW::ScriptVM::game()->callFunction("onActorMoonMounted", NULL);
	}
}

void ActorMoonMount::OnActorMoonDismounted()
{
	if(!m_bMounted)
	{
		return;
	}

	int mobid = m_Def ? m_Def->ID : 0;
	if(mobid == STAR_MOUNT_ID_LEVEL_1 || mobid == STAR_MOUNT_ID_LEVEL_2) 
	{
		m_bMounted = false;
		MINIW::ScriptVM::game()->callFunction("onActorMoonDismounted", NULL);
	}
}


bool ActorMoonMount::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorMoonMount *>(srcdata);

	if(!ClientMob::load(src->mobdata(), version))
	{
		return false;
	}

	setRiddenByActorObjId(src->ridden());
	if(getRiddenByActorID() > 0)
	{
		DestroyComponent(GetComponentByName("NavigationPath"));
	}

	if(src->equips())
	{
		for(size_t i=0; i<src->equips()->size(); ++i)
		{
			const FBSave::ItemIndexGrid *itemsrc = src->equips()->Get(i);

			int offset = itemsrc->index() - HORSE_EQUIP_INDEX;
			m_EquipGrids[offset].load(itemsrc);
		}
	}
	if(src->skills())
	{
		for(size_t i=0; i<src->skills()->size(); ++i)
		{
			int s = src->skills()->Get(i);
			m_Skills[i].id = s & 0xffff;
			m_Skills[i].active = s >> 16;
		}
	}
	if (src->skillcd() && src->skillcd()->size() <= (sizeof(m_fSkillCD)/sizeof(float)))
	{
		for (size_t i = 0; i < src->skillcd()->size(); ++i)
		{
			m_fSkillCD[i] = src->skillcd()->Get(i);
		}
	}
	if (src->maxhp() > 0)
	{
		getAttrib()->initMaxHP(src->maxhp());
	}
	if (version == 0) getAttrib()->initMaxHP(getAttrib()->getMaxHP()*5.0f);

	m_LandSpeed = src->landspeed();
	m_FlySpeed = src->flyspeed();
	m_SwimSpeed = src->swimspeed();
	m_JumpHeight = src->jumpheight();
	m_BindUIN = src->binduin();
	m_ArmorSlotOpen = src->armoropen()==1;
	auto otherriddens = src->otherriddens();
	if(otherriddens)
	{
		for(size_t i=0; i<otherriddens->size(); ++i)
		{
			m_OtherRiddens[i] = otherriddens->Get(i);
		}
	}

	if(m_FlySpeed == 0) 
	{
		m_FlySpeed = m_LandSpeed;
	}

	MobAttrib *attr = dynamic_cast<MobAttrib *>(getAttrib());
	if(attr)
	{
		attr->equip(EQUIP_HEAD, &m_EquipGrids[0]);
		if (m_ArmorSlotOpen) attr->equip(EQUIP_BREAST, &m_EquipGrids[1]);
	}

	m_fEnergy = src->energy();
	m_bTired = src->tired() != 0;
	if (m_bTired)
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("mob_3421_tired");
		}
	}

	m_iUseSwimSpeed = 0;
	if (hasHorseSkill(HORSE_SKILL_FLOAT)) 
	{
		m_iUseSwimSpeed += 1;
		getLocoMotion()->m_yOffset = -80;
	}

	if (hasHorseSkill(HORSE_SKILL_DIVING)) 
	{
		m_iUseSwimSpeed += 2;
		getLocoMotion()->m_yOffset = -20;
	}

	if (hasHorseSkill(HORSE_SKILL_WATER_RUSH)) 
	{
		m_iUseSwimSpeed += 4;
		getLocoMotion()->m_yOffset = -20;
		float skillvals[7];
		if (getHorseSkill(HORSE_SKILL_WATER_RUSH, skillvals)) 
		{
			m_iChargeAddSpeed = (int)skillvals[2];
			m_iChargeAddSpeed = m_iChargeAddSpeed <= 0 ? 1 : m_iChargeAddSpeed;
		}
	}

	return true;
}

flatbuffers::Offset<FBSave::SectionActor> ActorMoonMount::save(SAVE_BUFFER_BUILDER &builder)
{
	auto mobdata = ClientMob::saveMob(builder);

	flatbuffers::Offset<FBSave::ItemIndexGrid> grids[MAX_EQUIPS];
	int count = 0;

	for(size_t i=0; i<MAX_EQUIPS; ++i)
	{
		if(m_EquipGrids[i].isEmpty()) 
		{
			continue;
		}

		grids[count++] = m_EquipGrids[i].saveWithIndex(builder);
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> items = 0;
	if(count > 0) items = builder.CreateVector(grids,count);

	int skills[MAX_SKILLS];
	for(size_t i=0; i<MAX_SKILLS; ++i)
	{
		skills[i] = m_Skills[i].id | (m_Skills[i].active << 16);
	}

	flatbuffers::Offset<flatbuffers::Vector<uint64_t>> otherriddens = 0;
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->getNumRiddenPos() > 1)
	{
		uint64_t riddens[16];
		for(int i=1; i< RidComp->getNumRiddenPos(); ++i)
		{
			riddens[i-1] = m_OtherRiddens[i-1];
		}
		otherriddens = builder.CreateVector(riddens, RidComp->getNumRiddenPos()-1);
	}

	flatbuffers::Offset<flatbuffers::Vector<float>> skillcdsoffset = 0;
	std::vector<float> skillcds;
	for (size_t i = 0; i<(sizeof(m_fSkillCD)/sizeof(float)); ++i)
	{
		skillcds.push_back(m_fSkillCD[i]);
	}
	skillcdsoffset = builder.CreateVector(skillcds);
	auto starMount = FBSave::CreateActorMoonMount(
		builder, mobdata, getRiddenByActorID(), items, 
		builder.CreateVector(skills,MAX_SKILLS), 
		(int16_t)getAttrib()->getMaxHP(), m_LandSpeed, m_FlySpeed,
		m_SwimSpeed, m_JumpHeight, m_BindUIN, 
		m_ArmorSlotOpen?1:0, otherriddens, m_fEnergy, 
		m_bTired, 
		skillcdsoffset);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorMoonMount, starMount.Union());
}
