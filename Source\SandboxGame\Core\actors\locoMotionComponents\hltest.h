#pragma once

#include "ClientInfoProxy.h"
#include "ClientGame.h"
#include "OgreWCoord.h"
class _tmpRecordMove {
	std::ostringstream oss;
	long long uin;
	const WCoord &pos;
public:
	static long long record;
	static _tmpRecordMove* theone;
	static int heightmax;

	_tmpRecordMove(long long uin, const WCoord &_pos) :uin(uin), pos(_pos) {
		if (!theone)
			theone = this;
		oss << "start pos=" << _pos.__tostring();
		
	}
	~_tmpRecordMove()
	{
		if (theone)
			theone = nullptr;
		if (record != uin)
			return;
		LOG_INFO("%llu|hltest %s", GetClientInfoProxy()->getCurGame()->getGameTick(), oss.str().c_str());
	}
	void addMoveString(const Rainbow::Vector3f& move, const std::string& reason)
	{
		if (record != uin)
			return;
		char tmp[64];
		snprintf(tmp, sizeof(tmp), "|%s=%.2f,%.2f,%.2f", reason.c_str(), move.x, move.y, move.z);
		oss << tmp;
	}
	void addMoveString(const WCoord& move, const std::string& reason)
	{
		if (record != uin)
			return;
		char tmp[64];
		snprintf(tmp, sizeof(tmp), "|%s=%d,%d,%d", reason.c_str(), move.x, move.y, move.z);
		oss << "|" << reason << "=" << move.__tostring();
	}
	bool testing()
	{
		return record == uin;
	}
	static int getHeightMax()
	{
		return heightmax;
	}
};
long long _tmpRecordMove::record = 0;
_tmpRecordMove* _tmpRecordMove::theone = nullptr;
int _tmpRecordMove::heightmax = 3724;

_tmpRecordMove* getHltestLogger() {
	return _tmpRecordMove::theone;
}
