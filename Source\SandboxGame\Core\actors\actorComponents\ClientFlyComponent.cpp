#include "ClientFlyComponent.h"
#include "defdata.h"
#include "LivingLocoMotion.h"
#include "world.h"
#include "ActorBody.h"
#include "SunHurtComponent.h"
#include "ClientActorHelper.h"
#include "RiddenComponent.h"
#include "TemperatureComponent.h"
#include "VacantComponent.h"
#include "ActorAttrib.h"
#include "navigationpath.h"
#include "ClientActorFuncWrapper.h"
#include "ClientMob.h"
#include "proto_hc.pb.h"
#include "ActorVision.h"
IMPLEMENT_COMPONENTCLASS(ClientFlyComponent)

using namespace game::common;

ClientFlyComponent::ClientFlyComponent()
{
	m_HpProgressOffsetY = 0;
}

ClientFlyComponent::~ClientFlyComponent()
{

}

float ClientFlyComponent::getAirSpeed()
{
	if (!GetOwner()) return 0.0f;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return 0.0f;


	return static_cast<float>(m_owner->m_Def->Speed);
}

ActorLocoMotion* ClientFlyComponent::newLocoMotion()
{
	if (!GetOwner()) return NULL;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return NULL;
	LivingLocoMotion* loco = m_owner->CreateComponent<LivingLocoMotion>("LivingLocoMotion");
	loco->initMoveAbility(MoveAbilityType::FlyLoc);
	//FlyLocomotion* loco = new FlyLocomotion(this);
	loco->m_SpeedInAir = getAirSpeed();
	return loco;
}

bool ClientFlyComponent::init()
{
	if (!GetOwner()) return false;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return false;
	m_owner->setObjType(OBJ_TYPE_FLYMONSTER);
	m_owner->getBody()->setNeedUpdateAnim(false);
	m_owner->getBody()->setNeedUpdateSkinEffect(false);
	m_owner->getBody()->setNeedUpdateRenderYawOffset(false);
	m_owner->getBody()->setNeedUpdateLookUp(false);
	m_owner->getBody()->setControlRotation(false);
	m_owner->m_Mass = 1000;

	auto compSunHurt = m_owner->GetComponent<SunHurtComponent>();
	if (compSunHurt)
	{
		compSunHurt->SetCanPlayEffect(false);
	}
	return true;
}

void ClientFlyComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
}

void ClientFlyComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnLeaveOwner(owner);
}

void ClientFlyComponent::tick()
{
    OPTICK_EVENT();


	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return ;
	/*	ClientMob::tick();
*/
//-------------------------ClientActor tick begin-------------------------
	m_owner->UpdateOctree();
	m_owner->getLocoMotion()->tick();
	m_owner->getLocoMotion()->doBlockCollision();
	m_owner->getLocoMotion()->doPickThrough();
	if (m_owner->getAttrib()) m_owner->getAttrib()->tick();
	if (m_owner->getBody()) m_owner->getBody()->tick();
	if (m_owner->getVision()) m_owner->getVision()->clearAICanSeeCache();
	//-------------------------ClientActor tick end-------------------------

	m_owner->livingHPtick();
	m_owner->mobHPTick();

	auto* tempComp = m_owner->getTemperatureComponent();
	if (tempComp)
	{
		tempComp->OnTick();
	}

	auto* vacantComp = m_owner->getVacantComponent();
	if (vacantComp)
	{
		vacantComp->OnTick();
	}

	if (m_owner->getWorld()->isRemoteMode())
	{
		return;
	}

	auto navigator = m_owner->getNavigator();
	auto functionWrapper = m_owner->getFuncWrapper();
	if (navigator && functionWrapper && !functionWrapper->getSimulateFly())
	{
	

		if (!navigator->noPath())
		{
			navigator->clearMoveForward();
		}
		if (navigator->noPath())
		{
			navigator->clearPathEntity();
		}
		navigator->tick();
	}

	//-------------------------ActorLiving tick begin-------------------------

	m_owner->attackTick();

	//-------------------------ClientMob tick begin-------------------------
	m_owner->MobTick();
	//-------------------------ClientMob tick end-------------------------


}

bool ClientFlyComponent::needSaveInChunk()
{
	if (!GetOwner()) return false;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return false;

	if (m_owner->getMasterObjId())
	{
		return false;
	}
	else
	{
		return true;
	}
}

flatbuffers::Offset<FBSave::SectionActor> ClientFlyComponent::save(flatbuffers::FlatBufferBuilder& builder, flatbuffers::Offset<FBSave::ActorMob>& mobData)
{
	if (!GetOwner()) return NULL;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return NULL;
	auto mob = saveMob(builder, mobData);

	return m_owner->saveSectionActor(builder, FBSave::SectionActorUnion_ActorFlyMob, mob.Union());
}

flatbuffers::Offset<FBSave::ActorFlyMob> ClientFlyComponent::saveMob(flatbuffers::FlatBufferBuilder& builder, flatbuffers::Offset<FBSave::ActorMob>& mobData)
{
	if (!GetOwner()) return NULL;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return NULL;
	//auto mobdata = m_owner->saveMob(builder);
	LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	auto moveTarget = WCoordToCoord3(loco->m_MoveTarget);
	//916冒险 2021/08/18 codeby:wudeshen
	char luaData[512] = "";

	char szScriptFun[256];
	snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SaveData", m_owner->getDef()->ID);
	MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]>s", this, luaData);

	auto mob = FBSave::CreateActorFlyMob(builder, mobData, &moveTarget, builder.CreateString(luaData));

	return mob;
}

void ClientFlyComponent::applyActorCollision(ClientActor* actor)
{
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return ;
	//if (actor->m_RidingActor != getObjId() && actor->m_RiddenByActor != getObjId())
	auto actorRidComp = actor->getRiddenComponent();
	bool checkRiding = false;
	bool checkRidden = false;
	if (actorRidComp)
	{
		checkRiding = actorRidComp->checkRidingByActorObjId(m_owner->getObjId());
		checkRidden = actorRidComp->checkRiddenByActorObjId(m_owner->getObjId());
	}
	if (!checkRiding && !checkRidden)
	{
		WCoord dpos = actor->getLocoMotion()->getPosition() - m_owner->getLocoMotion()->getPosition();
		float x = static_cast<float>(dpos.x) / BLOCK_FSIZE;
		float z = static_cast<float>(dpos.z) / BLOCK_FSIZE;
		float y = static_cast<float>(dpos.y) / BLOCK_FSIZE;

		float max_xz = Rainbow::Max(Abs(x), Abs(z));
		if (max_xz > 0)
		{
			float r = Sqrt(max_xz);
			x = x / r;
			y = y / r;
			z = z / r;
			float inv_r = 1.0f / r;

			if (inv_r > 1.0f) inv_r = 1.0f;

			x *= inv_r * 5.0f * (1.0f - ACTOR_COLLIDE_DEC);
			z *= inv_r * 5.0f * (1.0f - ACTOR_COLLIDE_DEC);
			y *= inv_r * 5.0f * (1.0f - ACTOR_COLLIDE_DEC);

			//给自己反弹
			LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
			if (loco != NULL)
			{
				loco->m_ColliderMotion.x = -x;
				loco->m_ColliderMotion.y = -y;
				loco->m_ColliderMotion.z = -z;
			}

			if (m_owner->m_Def->Mass > 0.2f * actor->getMass())
			{
				//弹开别人
				LivingLocoMotion* loco2 = dynamic_cast<LivingLocoMotion*>(actor->getLocoMotion());
				if (loco2 != NULL)
				{
					loco2->m_ColliderMotion.x = x;
					loco2->m_ColliderMotion.y = y;
					loco2->m_ColliderMotion.z = z;
				}
				else
				{
					actor->getLocoMotion()->addMotion(x, y, z);
				}
			}
		}
		else
		{
			LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
			if (loco != NULL)
			{
				loco->m_ColliderMotion.x = 0;
				loco->m_ColliderMotion.y = 0;
				loco->m_ColliderMotion.z = 0;
			}
		}
	}
}

bool ClientFlyComponent::load(const void* srcdata, int version)
{
	if (!GetOwner()) return false;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return false;
	auto src = reinterpret_cast<const FBSave::ActorFlyMob*>(srcdata);

	if (!m_owner->todoLoad(src->mobdata(), version))
	{
		return false;
	}
	m_owner->getLocoMotion()->m_Motion = Rainbow::Vector3f::zero;
	LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	if (loco)
	{
		loco->m_MoveTarget = Coord3ToWCoord(src->moveTarget());
	}

	if (src->luadata())
	{
		char szScriptFun[256];
		snprintf(szScriptFun, sizeof(szScriptFun), "F%d_LoadData", m_owner->getDef()->ID);
		MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]s", this, src->luadata()->c_str());
	}

	return true;
}

// 兼容老存档蝙蝠为ClientMob的情况 codeby shitengkai 20220825
bool ClientFlyComponent::loadFromClientMobFB(const void* srcdata, int version)
{
	if (!GetOwner()) return false;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return false;

	auto src = reinterpret_cast<const FBSave::ActorMob*>(srcdata);
	if (!m_owner->todoLoad(src, version))
	{
		return false;
	}
	m_owner->getLocoMotion()->m_Motion = Rainbow::Vector3f::zero;
	LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());

	if (loco)
	{
		loco->m_MoveTarget = loco->getPosition();
	}
	return true;
}

int ClientFlyComponent::saveToPB(game::hc::PB_GeneralEnterAOIHC* pb)
{
	if (!GetOwner()) return -1;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return -1;

	PB_ActorFlyMob* flyMob = pb->mutable_actorflymob();
	PB_ActorMob* actorMob = flyMob->mutable_mobdata();
	if (m_owner->saveToMobPB(actorMob) != 0)
		return -1;

	LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	PB_Vector3* movetarget = flyMob->mutable_movetarget();
	movetarget->set_x(loco->m_MoveTarget.x);
	movetarget->set_y(loco->m_MoveTarget.y);
	movetarget->set_z(loco->m_MoveTarget.z);

	//916冒险 2021/08/18 codeby:wudeshen
	char luaData[512] = "";

	char szScriptFun[256];
	snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SaveData", m_owner->getDef()->ID);
	MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]>s", this, luaData);

	if (luaData[0] != '\0')
		flyMob->set_luadata(luaData);
	return (0);

}
int ClientFlyComponent::LoadFromPB(const game::hc::PB_GeneralEnterAOIHC& pb)
{
	if (!GetOwner()) return -1;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return -1;

	const PB_ActorFlyMob& flyMob = pb.actorflymob();
	const PB_ActorMob& actorMob = flyMob.mobdata();
	if (m_owner->LoadFromMobPB(actorMob) != 0)
		return -1;

	m_owner->getLocoMotion()->m_Motion = Rainbow::Vector3f::zero;
	LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	if (loco)
	{
		const PB_Vector3& pos = flyMob.movetarget();
		loco->m_MoveTarget.setElement(pos.x(), pos.y(), pos.z());
	}

	if (flyMob.has_luadata())
	{
		char szScriptFun[256];
		snprintf(szScriptFun, sizeof(szScriptFun), "F%d_LoadData", m_owner->getDef()->ID);
		MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]s", this, flyMob.luadata().c_str());
	}
	return (0);
}

void ClientFlyComponent::moveToPosition(const WCoord& pos, float yaw, float pitch, int interpol_ticks)
{
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return ;
	LivingLocoMotion* locmove = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
	locmove->m_SyncSteps = 5;

	locmove->m_SyncPos = pos;
	locmove->m_SyncYaw = yaw;
	locmove->m_SyncPitch = pitch;
}

bool ClientFlyComponent::canDespawn()
{
	return false;
}

void ClientFlyComponent::onDie()
{
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return ;
	if (m_owner->getLocoMotion())
	{
		LivingLocoMotion* loco = static_cast<LivingLocoMotion*>(m_owner->getLocoMotion());
		if (loco && loco->isLeader())
		{
			loco->setLeader(false);
		}
	}

	m_owner->getBody()->setCurAnim(SEQ_DIE, 0);
}

void ClientFlyComponent::setHpProgressBarOffsetY(int offsetY)
{
	m_HpProgressOffsetY = offsetY;
}

int ClientFlyComponent::getHpProgressOffsetY()
{
	return m_HpProgressOffsetY;
}