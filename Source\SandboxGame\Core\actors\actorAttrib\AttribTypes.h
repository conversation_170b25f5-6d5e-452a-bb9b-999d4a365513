
#ifndef __ATTRIBTYPES_H__
#define __ATTRIBTYPES_H__
#include "ActorTypes.h"

//tolua_begin
#define EXP_STAR_RATIO 100

enum ImmuneAttackType
{
	Punch_Immune = 1,//近程免疫
	Range_Immune = 2,//远程
	Explode_Immune = 4,//爆炸
	Fire_Immune = 8,//燃烧
	Poison_Immune = 16,//毒
	Wither_Immune = 32,//凋零（暗）
	Falling_Immune = 64,//掉落
	Wall_Immune = 128,//窒息
	Physics_Immune = 256,//物理
	Magic_Immune = 512,//元素
	Anvil_Immune = 1024,//被砸中
	Cactus_Immune = 2048,//仙人掌
	Antiinjury_Immune = 4096,//反伤
	Laser_Immune = 8192,//激光
	Fixed_Immune = 16384,//固定伤害
	Drown_Immune = 32768,//溺水
	Suffocate_Immune = 65536,//水下生物在空气中窒息
	True_Immune = 131072, //真实伤害免疫
	Thron_Immune = 262144, //刺球
	Ice_Immune = 524288,//冰
	ALL_Immune = 1048575,//前面所有值的总和
};

/**
@TODO: 移除tolua
*/
enum AttrType
{
	ATTRT_MAX_HP = 1, //最大HP
	ATTRT_CUR_HP = 2,
	ATTRT_HP_RECOVER = 3, //HP恢复
	ATTRT_LIFE_NUM = 4, //生命数
	ATTRT_MAX_HUNGER = 5, //最大饥饿值
	ATTRT_CUR_HUNGER = 6, //当前饥饿值
	ATTRT_MAX_OXYGEN = 7, //最大氧气值
	ATTRT_CUR_OXYGEN = 8, //当前氧气值
	ATTRT_RECOVER_OXYGEN = 9, //氧气恢复速度
	ATTRT_WALK_SPEED = 10, //移动速度
	ATTRT_RUN_SPEED = 11, //奔跑速度
	ATTRT_SNEAK_SPEED = 12, //潜行速度
	ATTRT_SWIN_SPEED = 13, //游泳速度
	ATTRT_JUMP_POWER = 14, //跳跃力
	ATTRT_DODGE = 15, //闪避率
	ATTRT_ATK_MELEE = 16, //近战攻击
	ATTRT_ATK_REMOTE = 17, //远程攻击
	ATTRT_DEF_MELEE = 18, //近战防御
	ATTRT_DEF_REMOTE = 19, //远程防御
	ATTRT_DIMENSION = 20, //模型大小
	ATTRT_SCORE = 21, //分数
	ATTRT_LEVEL = 22, //(星星数)等级
	ATTRT_LEVELMODE_INCREMENT = 26,		//等级经验增加量
	ATTRT_LEVELMODE_CURLEVEL = 27,		//当前等级
	ATTRT_LEVELMODE_CUREXP = 28,		//当前经验
	ATTRT_CUR_OVERFLOWABLE_HP,//可溢出的HP值
	ATTRT_OVERFLOW_HP,//HP溢出值
	ATTRT_CUR_STRENGTH,
	ATTRT_MAX_STRENGTH,//最大体力
	ATTRT_OVERFLOW_STRENGTH,//溢出值体力
	ATTRT_STRENGTH_RESTORE,//体力恢复
	ATTRT_TEMPERATURE, // 体温
	ATTRT_RADIATION, // 辐射值
	ATTRT_CUR_THIRST,
	ATTRT_MAX_THIRST,//最大口渴
	ATTRT_OVERFLOW_THIRST,//溢出值口渴
	ATTRT_THIRST_RESTORE,//口渴恢复
	ATTRT_ARMOR, //护甲值
	ATTRT_PERSEVERANCE, //毅力值
	ATTRT_VACANT_ENERGY //虚空能量
};

/**
@TODO: 移除tolua
*/
enum BuffAttrType
{
	//EffectBank表
	BUFFATTRT_DIG_SPEED = 2001,   //挖掘速度
	BUFFATTRT_JUMP_SPEED = 2002,  //跳跃速度
	BUFFATTRT_CONTINUE_CHG_HUNGER = 2003, //持续改变饥饿度
	BUFFATTRT_CONTINUE_CHG_HP = 2004, //持续恢复生命值
	BUFFATTRT_VIEW_BRIGHT = 2005, //视野亮度
	BUFFATTRT_HUNGER_SPEED = 2006, //饥饿流失速度
	BUFFATTRT_KNOCK = 2007, //击退强化
	BUFFATTRT_KNOCK_RESIST = 2008, //击退抵抗
	BUFFATTRT_DAMAGE_ABSORB = 2009, //伤害吸收
	BUFFATTRT_ATTACK_FLY = 2010, //攻击击飞
	BUFFATTRT_ATTACK_ADD = 2011, //攻击附加状态
	BUFFATTRT_DAMAGE_ADD = 2012, //反伤

	BUFFATTRT_MODEL_CHANGE = 2014, //改变模型(怪物)
	BUFFATTRT_INVULNERABLE = 2016, //无敌
	BUFFATTRT_BUBBLE = 2017, //泡泡
	BUFFATTRT_CONTINUE_REDUCE_HP = 2020, //持续受到伤害
	BUFFATTRT_FORBID_EAT = 2021,    //禁食
	BUFFATTRT_STATE__BRGIN_DROP = 2022,   //在buff开始时出现掉落物
	BUFFATTRT_TIME_DROP = 2023,   //在buff持续期间每隔一段时间产生掉落物
	BUFFATTRT_FORBID_RUN = 2024,   //禁止奔跑
	BUFFATTRT_EXP_GAIN_SPEED = 2025,   //经验获取速度
	BUFFATTRT_HP_RECOVER_SPEED = 2026,	//生命恢复速度
	BUFFATTRT_CONTINUE_CHG_STRENGTH = 2027, //持续恢复体力值
	BUFFATTRT_STATE_END_DROP = 2028, //在buff结束时出现掉落物	
	BUFFATTRT_RELEASE_TIRED = 2029, //疲劳解除
	//2021/07/09 修改：巴拉拉魔棒 codeby:wudeshen
	BUFFATTRT_MODEL_CHANGE_ROLESKIN = 2030, //改变模型(皮肤)
	BUFFATTRT_CONSUME_STRENGTH_DAMAGE = 2031, //消耗体力值
	BUFFATTRT_IMMUNE_BAD_EFFECT = 2032, // 状态免疫(流血)
	BUFFATTRT_ADDITIONAL_STATUS = 2033, // 附增状态(麻痹)
	BUFFATTRT_FORBID_OPERATE = 2034, // 禁止操作
    BUFFATTRT_IMMUNE_NATURE_EFFECT = 2035, // 状态类型免疫
	BUFFATTRT_CONSUME_THIRST_DAMAGE = 2036, //消耗口渴值

	BUFFATTRT_ATTR_SHAPESHIFT = 2039, // 属性变身（改变属性）
	BUFFATTRT_ATTR_RIGHT_CLICK = 2040,// 属性变身时，右键配置的能力
	BUFFATTRT_SCREEN_DECALS = 2041,//  屏幕贴花
	BUFFATTRT_STOP_ACTION = 2042,// 停止动作(冻结)
	BUFFATTRT_CHANGE_MODEL_TEXTURE = 2043, // 改变角色模型贴图
	BUFFATTRT_HANG_MODEL = 2044, // 之前是挂载模型，后面需求改成播放特效
	BUFFATTRT_END_CHANGE_ATTRIBUTE = 2045, // 结束改变属性
	BUFFATTRT_END_PLAY_EFFECT = 2046, // 结束播放特效
	BUFFATTRT_TEMPERATURE_IMPACT_DURATION = 2047, //温度影响持续时间
	BUFFATTRT_DAMAGE_REMOVE_BUFF = 2048, //受到一定伤害解除buff
	BUFFATTRT_CONTINUED_CHANGE_TEMPERATURE = 2049, //持续改变温度
	BUFFATTRT_TEMPERATURED_CHANGE = 2050, //温度改变

	BUFFATTRT_AROUND_TEMPERATURE_CHANGE = 2051, //改变周围温度
	BUFFATTRT_LIQUID_BLOCK_CONGEAL = 2052, //方块凝结
	BUFFATTRT_AROUND_RANDOM_CREATE_BLOCK = 2053, //周围随机生成方块
	BUFFATTRT_RANDOM_ATTACKER_GAINSTATE = 2054, // 使攻击者随机获得buff
	BUFFATTRT_RANDOM_BEATTACK_GAINSTATE = 2055, // 被攻击随机获得buff
	BUFFATTRT_PROB_MOREDAMAGE_BEATTACK_HASBUFF = 2056, // 对有@1buff的生物造成伤害时，有@2概率对其造成@3的@4
	BUFFATTRT_CONTINUE_CHG_PER_HP = 2057, //持续恢复百分比生命值
	BUFFATTRT_RADIATION_RESISTANCE = 2058, //辐射抵抗
	//EffectBankEnum表
	BUFFATTRT_WALK_SPEED = 1001,   //行走速度
	BUFFATTRT_RUN_SPEED = 1002,    //奔跑速度
	BUFFATTRT_SWIM_SPEED = 1003,   //游泳速度
	BUFFATTRT_SNEAK_SPEED = 1004,  //潜行速度
	BUFFATTRT_ALL_SPEED = 1005,    //所有速度
	BUFFATTRT_HP_MAX = 1006,       //最大生命值
	BUFFATTRT_HUNGER_MAX = 1007,   //最大饥饿度
	BUFFATTRT_MELEE_DAMAGE = 1008,    //近程伤害
	BUFFATTRT_REMOTE_DAMAGE = 1009,   //远程伤害
	BUFFATTRT_EXPLODE_DAMAGE = 1010,  //爆炸伤害
	BUFFATTRT_FIRE_DAMAGE = 1011,     //火焰伤害
	BUFFATTRT_POISON_DAMAGE = 1012,   //毒素伤害
	BUFFATTRT_CONFUSION_DAMAGE = 1013,//混乱伤害
	BUFFATTRT_MONSTER_DAMAGE = 1014,  //对魔物的伤害
	BUFFATTRT_ACTOR_DAMAGE = 1015,    //对动物的伤害
	BUFFATTRT_PLAYER_DAMAGE = 1016,   //对玩家的伤害
	BUFFATTRT_SAVAGE_DAMAGE = 1017,   //对野人的伤害
	BUFFATTRT_ALL_DAMAGE = 1018,      //所有伤害
	BUFFATTRT_MELEE_HURT = 1019,    //近程受击伤害
	BUFFATTRT_REMOTE_HURT = 1020,   //远程受击伤害
	BUFFATTRT_EXPLODE_HURT = 1021,  //爆炸受击伤害
	BUFFATTRT_FIRE_HURT = 1022,     //火焰受击伤害
	BUFFATTRT_POISON_HURT = 1023,   //毒素受击伤害
	BUFFATTRT_CONFUSION_HURT = 1024,//混乱受击伤害
	BUFFATTRT_MONSTER_HURT = 1025,  //对魔物的受击伤害
	BUFFATTRT_ACTOR_HURT = 1026,    //对动物的受击伤害
	BUFFATTRT_PLAYER_HURT = 1027,   //对玩家的受击伤害
	BUFFATTRT_SAVAGE_HURT = 1028,   //对野人的受击伤害
	BUFFATTRT_FALL_HURT = 1029,     //跌落受击伤害
	BUFFATTRT_ALL_HURT = 1030,      //所有受击伤害
	BUFFATTRT_MELEE_DEF = 1031,    //近战防御
	BUFFATTRT_REMOTE_DEF = 1032,   //远程防御
	BUFFATTRT_EXPLODE_DEF = 1033,  //爆炸防御
	BUFFATTRT_FIRE_DEF = 1034,     //燃烧防御
	BUFFATTRT_POISON_DEF = 1035,   //毒素防御
	BUFFATTRT_CONFUSION_DEF = 1036,//混乱防御
	BUFFATTRT_ALL_DEF = 1037,      //所有防御力
	BUFFATTRT_GOODLUCK_DIG = 1038,    //幸运挖掘
	BUFFATTRT_GOODLUCK_HUNTING = 1039,//幸运狩猎
	BUFFATTRT_BODY_ALL = 1040,   //全身
	BUFFATTRT_BODY_HEAD = 1041,  //头部
	BUFFATTRT_ENV_INWATER = 1042,//水下
	BUFFATTRT_ENV_SPACE = 1043,  //太空
	BUFFATTRT_MELEE_ATK = 1044,    //近程攻击
	BUFFATTRT_REMOTE_ATK = 1045,   //远程攻击
	BUFFATTRT_EXPLODE_ATK = 1046,  //爆炸攻击
	BUFFATTRT_FIRE_ATK = 1047,     //火焰攻击
	BUFFATTRT_POISON_ATK = 1048,   //毒素攻击
	BUFFATTRT_CONFUSION_ATK = 1049,//混乱攻击
	BUFFATTRT_ALL_ATK = 1050,      //所有的攻击
	BUFFATTRT_FIXED_HURT = 1051,   //固定受击伤害
	BUFFATTRT_STRENGTH = 1052,               //体力值
	BUFFATTRT_OVERFLOW_STRENGTH = 1053,      //溢出体力值
	BUFFATTRT_ALL_SPEND_STRENGTH = 1054,     //所有体力值消耗类型
	BUFFATTRT_RUN_SPEND_STRENGTH = 1055,    //奔跑
	BUFFATTRT_SWIM_SPEND_STRENGTH = 1056,    //游泳
	BUFFATTRT_RUN_JUMP_SPEND_STRENGTH = 1057,//奔跑时跳跃
	BUFFATTRT_DIG_BLOCK_SPEND_STRENGTH = 1058, //挖掘方块
	BUFFATTRT_FOCO_ENERGIA_TOOL_SPEND_STRENGTH = 1059, //蓄力使用工具
	BUFFATTRT_JUMP_SECOND_SPEND_STRENGTH = 1064, //二段跳
	BUFFATTRT_ALL_RECOVER_STRENGTH = 1060,     //所有体力值恢复类型
	BUFFATTRT_STOP_RECOVER = 1061,			   //静止
	BUFFATTRT_WALK_RECOVER_STRENGTH = 1062,    //行走
	BUFFATTRT_SNEAK_RECOVER_STRENGTH = 1063,    //潜行
	BUFFATTRT_TRUE_DAMAGE = 1064,				//真实伤害
	BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_ALL = 1065,			//所有剧烈运动行为
	BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_RUN = 1066,			//奔跑
	BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_SWIM = 1067,		//游泳
	BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_RUN_JUMP = 1068,	//奔跑时跳跃
	BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_DIG = 1069,			//挖掘
	BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_CHARING = 1070,	//使用工具蓄力
	BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_RUN_SECOND_JUMP = 1071, //二段跳
	BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_ATTACK = 1072,      //攻击
    BUFFATTRT_TEMPERATURE  = 1073,           //温度
	BUFFATTRT_LOW_TEMPERATURE = 1074,        //低温
	BUFFATTRT_HIGH_TEMPERATURE = 1075,       //高温
	BUFFATTRT_ICE_HARM = 1076,				//冰冻伤害
	BUFFATTRT_ICE_TEMPERATURE = 1077,       //冰冻受伤
	BUFFATTRT_ICE_BRUISE = 1078,			//冰冻防御
	BUFFATTRT_ICE_ATK = 1079,				//冰冻攻击力
	BUFFATTRT_ICE_PROPERTY = 1080,			//冰冻属性伤害
	BUFFATTRT_ZOMBIE_DAMAGE = 1081,			// 对僵尸的伤害
	BUFFATTRT_ZOMBIE_HURT = 1082,			// 对僵尸的受击伤害
	BUFFATTRT_FIREARM_DAMAGE = 1083,		// 枪械伤害（新枪组件生效）
	BUFFATTRT_REPEL_DISTANCE = 1084,		// 击退距离（新枪组件生效）
	BUFFATTRT_SHRAPNEL_COUNT = 1085,		// 弹片数量（新枪一次开枪弹道数）（新枪组件生效）
	BUFFATTRT_BULLET_EXPEND = 1086,			// 消耗子弹数（新枪组件生效）
	BUFFATTRT_FIREARM_DAMAGE_PERCENT = 1087,// 枪械伤害百分比（新枪组件生效）
	BUFFATTRT_PENETRANCE_PERCENT = 1088,	// 穿透率百分比（新枪组件生效）
	BUFFATTRT_RELOADRATE_PERCENT = 1089,	// 换弹速度百分比（新枪组件生效）
	BUFFATTRT_RECOIL_PERCENT = 1090,		// 后座力百分比（新枪组件生效）
	BUFFATTRT_PITCH_RECOIL_PERCENT = 1091,	// 垂直后座力百分比（新枪组件生效）
	BUFFATTRT_YAW_RECOIL_PERCENT = 1092,	// 水平后座力百分比（新枪组件生效）
	BUFFATTRT_ACCURACY_PERCENT = 1093,		// 精准度（散布）百分比（新枪组件生效）
	BUFFATTRT_HIPFIRE_ACCURACY_PERCENT = 1094,		// 腰射精准度（散布）百分比（新枪组件生效）
	BUFFATTRT_IRONSIGHT_ACCURACY_PERCENT = 1095,	// 机瞄精准度（散布）百分比（新枪组件生效）
	BUFFATTRT_HIPFIRE_MOVESPEED_PERCENT = 1096,		// 腰射移动速度百分比（新枪组件生效）
	BUFFATTRT_IRONSIGHT_MOVESPEED_PERCENT = 1097,	// 机瞄移动速度百分比（新枪组件生效）
	BUFFATTRT_FIREARM_HEADDAMAGE_PERCENT = 1098,	// 头部伤害百分比（新枪组件生效）
	BUFFATTRT_AIM_TIME_PERCENT = 1099,		// 瞄准时间百分比（新枪组件生效）
	BUFFATTRT_FIRE_RATE_PERCENT = 1100,		// 射速百分比（新枪组件生效）
	BUFFATTRT_FIREARM_HIT_BUFF = 1101,		// 枪械命中buff
	BUFFATTRT_FIREARM_SKILL_CHANGE = 1102,	// 枪械技能id修改
	BUFFATTRT_FIREARM_PROJECTILE_CHANGE = 1103,		// 枪械投掷物id修改
	BUFFATTRT_MAGIC_DEF = 1104,		//元素攻击防御
	BUFFATTRT_PHYSICS_DEF = 1105,   //物理攻击防御
	BUFFATTRT_ATTACK_GUN = 1106,	//枪械伤害加成（伤害计算公式生效）
};

enum
{
	Actor_Walk_Speed = 0,
	Actor_Run_Speed,
	Actor_Sneak_Speed,
	Actor_Swim_Speed,
	Actor_Jump_Speed,
	Actor_Speed_Type_Count,
};

/**
@TODO: 移除tolua
*/
//疲劳状态
enum
{
	FATIGUE_FIRST_LEVEL,
	FATIGUE_SECOND_LEVEL,
	FATIGUE_THIRD_LEVEL,
	FATIGUE_FOURTH_LEVEL,
	FATIGUE_FIFTH_LEVEL,
	FATIGUE_MAX_LEVEL,
};

/**
@TODO: 移除tolua
*/
//饥饿状态
enum
{
	HUNGER_FIRST_LEVEL,
	HUNGER_SECOND_LEVEL,
	HUNGER_THIRD_LEVEL,
	HUNGER_FOURTH_LEVEL,
	HUNGER_FIFTH_LEVEL,
	HUNGER_MAX_LEVEL,
};

//性格
enum
{
	DISPOSITION_NOT,
	DISPOSITION_BRAVE,				//勇敢
	DISPOSITION_GOOD_TALKING,		//能说会道
	DISPOSITION_POPULAR_BY_ANIMALS,	//受动物欢迎
	DISPOSITION_HELPFUL,			//乐于助人
	DISPOSITION_LAZY,				//懒惰
};

#define STATUS_UI_BASE_VAL 100000
enum StatusUIType
{
	Type_Enum = 1,  //枚举选项
	Type_Slider = 2,//滑动条数值
	Type_Switch = 3,//开关
	Type_Option = 4,//选择库
};

enum EffectBankParaType
{
	ParaType_Temperature = 100203,
	ParaType_Switch      = 300001,  //开关
	ParaType_Option_Model       = 400001,  //模型库
	ParaType_Option_Particles   = 400007,  //特效库
	ParaType_Option_Img         = 400008,  //图片
};

//人物属性枚举
enum ActorAttribType
{
	ActorAttribType_Hp, //血量
	ActorAttribType_Armor, //护甲
	ActorAttribType_Perseverance, //毅力
	ActorAttribType_Strength, //体力
	ActorAttribType_Thirst, //口渴
};

//tolua_end

const float START_ATTACK = 1.0f;
const float VELOCITY_FB = 10.0f;//440.0f;
const float VELOCITY_INAIR = 2.0f;
const int MAX_HURTRESISTANT_TIME = 20;

inline int AtkType2ArmorIndex(ATTACK_TYPE atktype)
{
	/*if (atktype < MAX_MAGIC_ATTACK || atktype == PHYSICS_ATTACK)
	{
		if (atktype == ATTACK_PUNCH || atktype == ATTACK_EXPLODE)
			return 0;
		else if (atktype == ATTACK_RANGE)
			return 1;
		else
			return 2;
	}*/
	int index = 0;
	if (atktype < MAX_PHYSICS_ATTACK)
	{
		index = atktype;
	}
	else if(atktype >= ATTACK_FIRE && atktype <= MAX_MAGIC_ATTACK)
	{
		index = atktype + 1;
	}
	else if (atktype == PHYSICS_ATTACK)
	{
		index = MAX_PHYSICS_ATTACK;
	}

	if (index >= 0 && index < 10)
	{
		return index;
	}

	assert(false && "AtkType2ArmorIndex error!");
	return 0;
}
inline ATTACK_TYPE ArmorIndex2AtkType(int armorIndex)
{
	ATTACK_TYPE atkType = ATTACK_PUNCH;
	if (armorIndex < 3)
	{
		atkType = (ATTACK_TYPE)armorIndex;
	}
	else if (armorIndex >= 4 && armorIndex <= 9)
	{
		atkType = (ATTACK_TYPE)(armorIndex - 1);
	}
	else if (armorIndex == 3)
	{
		atkType = PHYSICS_ATTACK;
	}

	if ((atkType >= ATTACK_PUNCH && atkType <= MAX_MAGIC_ATTACK) || atkType == PHYSICS_ATTACK)
	{
		return atkType;
	}

	assert(false && "ArmorIndex2AtkType error!");
	return ATTACK_PUNCH;
}


const int SLIDER_VALUE_ERROR = -1000000;

float GetSliderValue(int sliderId, float value);

#endif
