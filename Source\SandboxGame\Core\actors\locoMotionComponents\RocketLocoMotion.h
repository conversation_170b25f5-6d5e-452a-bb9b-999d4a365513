
#ifndef __ROCKET_LOCOMOTION_H__
#define __ROCKET_LOCOMOTION_H__

#include "ActorLocoMotion.h"

class RocketLocoMotion : public ActorLocoMotion
{
public:
	DECLARE_COMPONENTCLASS(RocketLocoMotion)

	RocketLocoMotion();
	virtual void tick() override;
	virtual void prepareTick() override;
	virtual void update(float dtime) override;
	virtual void getRotation(Rainbow::Quaternionf &quat) override;
	virtual bool needFullRotation() override
	{
		return true;
	}

	void checkBlockCollision();

	int getAddSpeed();
public:
	float m_RotationRoll;
	float m_PrevRotateRoll;

	Rainbow::WorldPos m_UpdatePos;
	Rainbow::Quaternionf m_UpdateRot;

	Rainbow::Quaternionf m_PrevRotateQuat;
	Rainbow::Quaternionf m_RotateQuat;

	int m_PosRotationIncrements;
	WCoord m_ServerPos;
	Rainbow::Quaternionf m_ServerRot;

	int m_AddSpeed;
};

#endif