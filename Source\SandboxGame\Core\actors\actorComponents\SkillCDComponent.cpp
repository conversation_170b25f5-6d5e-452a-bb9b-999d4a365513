#include "SkillCDComponent.h"
#include "ClientPlayer.h"
#include "GameNetManager.h"
#include "WorldManager.h"
#include "WeaponSkinMgr.h"
#include "SandboxIdDef.h"
#include "LivingAttrib.h"
#include "IMiniDeveloperProxy.h"

using namespace MNSandbox;

IMPLEMENT_COMPONENTCLASS(SkillCDComponent)

SkillCDComponent::SkillCDComponent()
{
	m_player = nullptr;
}

SkillCDComponent::~SkillCDComponent()
{
	m_player = NULL;
	m_SkillCD.clear();
}

void SkillCDComponent::onUpdate(float dttime)
{
	auto it = m_SkillCD.begin();
	while (it != m_SkillCD.end())
	{
		it->second -= dttime;
		if (it->second < 0)
		{
			m_SkillCD.erase(it++);
		}
		else
			it++;
	}
}

void SkillCDComponent::setSkillCD(int itemid, float cd)
{
	m_player = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_player) { return; }
	float fvalue = cd;

	float triggercd = MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->getItemCdForTrigger(m_player->getUin(), itemid);
	if (triggercd >= 0.0f)
	{
		fvalue = triggercd;
	}
	// 减少X%的蓄力冷却时间 
	WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
	if (weaponSkinMgr != NULL)
	{
		const char* attrType = "WeaponSkin_System_SkillCD";
		float fpercent = weaponSkinMgr->GetSkinAdditionPercent(attrType, m_player);
		if (fpercent > 0.0f) fvalue = fvalue * (1 - fpercent);
	}
	{// - - 武器技能冷却时间降低：百分比数值，降低使用的间隔时间 renjie
		if (m_player->getLivingAttrib())
		{
			float decVal= m_player->getLivingAttrib()->getEnchantWeponSkillDec();
			float valTmp = fvalue * decVal;
			if (fvalue - valTmp >= 0.0f)
			{
				fvalue -= valTmp;
			}
			
		}
	}
	
	
	auto iter = m_SkillCD.find(itemid);
	if (iter != m_SkillCD.end())
	{
		iter->second = fvalue;
	}
	else
	{
		m_SkillCD[itemid] = fvalue;
	}
}

void SkillCDComponent::syncSkillCD(int itemid, float cd)
{
	m_player = dynamic_cast<ClientPlayer*>(GetOwner());
	if (!m_player) { return; }
	float fvalue = cd;
	float triggercd = MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->getItemCdForTrigger(m_player->getUin(), itemid);
	if (triggercd >= 0.0f)
	{
		fvalue = triggercd;
	}
	World* pworld = m_player->getWorld();
	int uin = m_player->getUin();
	// 将动作同步到客机
	if (pworld && !pworld->isRemoteMode())
	{
		PB_SkillCDHC skillCDHC;
		skillCDHC.set_itemid(itemid);
		skillCDHC.set_cd(fvalue);

		GameNetManager::getInstance()->sendToClient(uin, PB_SKILLCD_HC, skillCDHC);
	}
}
void SkillCDComponent::setItemSkillCD(int itemid, float cd)
{
	auto iter = m_SkillCD.find(itemid + 1000000);
	if (iter != m_SkillCD.end())
	{
		iter->second = cd;
	}
	else
	{
		m_SkillCD[itemid + 1000000] = cd;
	}
}
float SkillCDComponent::getSkillCD(int itemid)
{
	//如果是创造模式或编辑模式, 技能无CD.
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode() && itemid != ITEM_BASKETBALLWEAR)
	{
		return 0;
	}

	if (itemid < 1000000)
	{
		float totalCD = 0;
		if (!GetDefManagerProxy()->IsInit()) return 0;
		const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(itemid);
		if (itemdef)
		{
			for (int i = 0; i < (int)itemdef->SkillID.size(); i++)
			{
				const ItemSkillDef *skilldef = GetDefManagerProxy()->getItemSkillDef(itemdef->SkillID[i]);
				if (skilldef)
				{
					if (getSkillCD(itemdef->SkillID[i] + 1000000) > totalCD)
					{
						totalCD = getSkillCD(itemdef->SkillID[i] + 1000000);
					}
				}
			}
		}
		if (totalCD > 0)
			return totalCD;
	}

	auto iter = m_SkillCD.find(itemid);
	if (iter != m_SkillCD.end())
	{
		return iter->second;
	}

	return 0.0f;
}

float SkillCDComponent::getTotalSkillCD(int itemid)
{
	if (m_player)
	{
		float triggercd = MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->getItemCdForTrigger(m_player->getUin(), itemid);
		if (triggercd >= 0.0f)
		{
			return triggercd;
		}
	}
	float totalCD = 0;
	const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(itemid);
	if (itemdef)
	{
		for (int i = 0; i < (int)itemdef->SkillID.size(); i++)
		{
			const ItemSkillDef *skilldef = GetDefManagerProxy()->getItemSkillDef(itemdef->SkillID[i]);
			if (skilldef)
			{
				if (getSkillCD(itemdef->SkillID[i] + 1000000) > 0 && totalCD < skilldef->CDTime)
				{
					totalCD = skilldef->CDTime;
				}
			}
		}
	}

	if (totalCD > 0)
		return totalCD;

	const ToolDef* toolDef = GetDefManagerProxy()->getToolDef(itemid);
	if (toolDef)
	{
		return 	toolDef->SkillCD;
	}
	return 0;
}

void SkillCDComponent::saveToPB(game::common::PB_SkillCDData* skillCDData)
{
	skillCDData->set_numskillcd(m_SkillCD.size());

	int index = 0;
	auto iter = m_SkillCD.begin();
	for (; iter != m_SkillCD.end(); iter++)
	{
		skillCDData->add_itemid(iter->first);
		skillCDData->add_cd(iter->second);

		index++;
		if (index >= MAX_SKILLCDS) break;
	}

}
void SkillCDComponent::load(const flatbuffers::Vector<flatbuffers::Offset<FBSave::SkillCDData>> *skillCDData)
{
	m_SkillCD.clear();
	if (skillCDData)
	{
		for (size_t i = 0; i < skillCDData->size(); i++)
		{
			auto src = skillCDData->Get(i);
			m_SkillCD[src->itemid()] = src->cd();
		}
	}
}
flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::SkillCDData>>> SkillCDComponent::save(flatbuffers::FlatBufferBuilder &builder)
{
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::SkillCDData>>> skillcdsoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::SkillCDData>> skillcds;
	auto iter = m_SkillCD.begin();
	for (; iter != m_SkillCD.end(); iter++)
	{
		skillcds.push_back(FBSave::CreateSkillCDData(builder, iter->first, iter->second));
	}
	skillcdsoffset = builder.CreateVector(skillcds);
	return skillcdsoffset;
}