/**********************************************
*	FUNC:	��Ϊ��������չ�ڵ�(����)
*	FILE:	BTNTaskMoveTo
*	BY:		chenzh
*	TIME:	2020-6-16
*/
#include "BTNodeDef.h"
#include "BTNTaskMoveTo.h"
#include "BTNodeRunData.h"
#include "BTBlackboard.h"
#include "ClientActor.h"
#include "ClientMob.h"
#include "ClientActorManager.h"
#include "ActorLocoMotion.h"
#include "world.h"
#include "Math/Vector3f.h"
#include "OgreBlock.h"
#include "json/jsonxx.h"
#include <assert.h>
#include "Common/OgreShared.h"
#include "PathEntity.h"
#include "ClientActorFuncWrapper.h"
#include "navigationpath.h"
#include "OgreScriptLuaVM.h"
#include "BehaviorTreeInstance.h"

const std::string BTNTaskMoveTo::ms_paramkey_bbkey_targetpos = "bbkey_targetpos";
const std::string BTNTaskMoveTo::ms_paramkey_bbkey_limitrange = "bbkey_limitrange";
const std::string BTNTaskMoveTo::ms_paramkey_speed = "speed";
const std::string BTNTaskMoveTo::ms_paramkey_speed_ratio = "bbkey_speed_ratio";
const std::string BTNTaskMoveTo::ms_paramkey_avoidWater = "avoidWater";
const std::string BTNTaskMoveTo::ms_paramkey_CollideHorizontallyStop = "CollideHorizontallyStop";
const std::string BTNTaskMoveTo::ms_paramkey_minDistance = "minDistance";
const std::string BTNTaskMoveTo::ms_paramkey_exact = "exact";

 const int BTNTaskMoveTo::mi_paramkey_bbkey_targetpos = 1;
 const int BTNTaskMoveTo::mi_paramkey_bbkey_limitrange = 2;
 const int BTNTaskMoveTo::mi_paramkey_speed = 3;
 const int BTNTaskMoveTo::mi_paramkey_speed_ratio = 4;
 const int BTNTaskMoveTo::mi_paramkey_avoidWater = 5;
 const int BTNTaskMoveTo::mi_paramkey_minDistance = 6;
 const int BTNTaskMoveTo::mi_paramkey_exact = 7;

//--------------------------------------------------------
BTNTaskMoveTo::BTNTaskMoveTo()
	: m_Speed(1.0f), 
	m_SpeedRatio(1.0f),
	m_CollideHorizontallyStop(false),
	m_limitRange(100),
	m_minDistanceSqr(100*100), 
	m_bIsAvoidWater(true),
	m_bExact(false)
{
}

BTNTaskMoveTo::~BTNTaskMoveTo()
{
}
int BTNTaskMoveTo::InitValue(BTNodeBase* node)
{
	BTNTaskMoveTo* tnode = static_cast<BTNTaskMoveTo*>(node);
	m_KeyTargetPos = tnode->m_KeyTargetPos;
	m_KeyLimitRange = tnode->m_KeyLimitRange;
	m_Speed = tnode->m_Speed;
	m_keySpeedRation = tnode->m_keySpeedRation;
	m_bIsAvoidWater = tnode->m_bIsAvoidWater;
	m_minDistanceSqr = tnode->m_minDistanceSqr;
	m_bExact = tnode->m_bExact;
	return BTNodeTask::InitValue(tnode);
}
bool BTNTaskMoveTo::SetParam(const std::string& key, const BTLuaData& data)
{
	if (key.compare(ms_paramkey_bbkey_targetpos) == 0)
	{
		if (data.GetType() == BTLuaDataType_String)
		{
			m_KeyTargetPos = data.GetValue_String();
			return true;
		}
	}
	else if (key.compare(ms_paramkey_bbkey_limitrange) == 0)
	{
		if (data.GetType() == BTLuaDataType_String)
		{
			m_KeyLimitRange = data.GetValue_String();
			return true;
		}
	}
	else if (key.compare(ms_paramkey_speed) == 0)
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			m_Speed = (float)data.GetValue_Number();
			return true;
		}
	}
	else if (key.compare(ms_paramkey_speed_ratio) == 0)
	{
		if (data.GetType() == BTLuaDataType_String)
		{
			m_keySpeedRation = data.GetValue_String();
			return true;
		}
	}
	else if (key.compare(ms_paramkey_avoidWater) == 0)
	{
		if (data.GetType() == BTLuaDataType_Bool)
		{
			m_bIsAvoidWater = data.GetValue_Bool();
			return true;
		}
	}
	else if (key.compare(ms_paramkey_minDistance) == 0)
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			float mindis = (float)data.GetValue_Number();
			mindis *= BLOCK_SIZE;
			m_minDistanceSqr = (int)(mindis * mindis);
			return true;
		}
	}
	else if (key.compare(ms_paramkey_exact) == 0)
	{
		if (data.GetType() == BTLuaDataType_Bool)
		{
			m_bExact = data.GetValue_Bool();
			return true;
		}
	}
	//else if (key.compare(ms_paramkey_CollideHorizontallyStop) == 0)//�������ֵ������
	//{
	//	if (data.GetType() == BTLuaDataType_Bool)
	//	{
	//		m_CollideHorizontallyStop = data.GetValue_Bool();
	//		return true;
	//	}
	//}
	return BTNodeTask::SetParam(key, data);
}

bool BTNTaskMoveTo::SetParam(const int& key, const BTLuaData& data)
{
	switch (key)
	{
	case mi_paramkey_bbkey_targetpos:
	{
		if (data.GetType() == BTLuaDataType_String)
		{
			m_KeyTargetPos = data.GetValue_String();
			return true;
		}
	}
		break;
	case mi_paramkey_bbkey_limitrange:
	{
		if (data.GetType() == BTLuaDataType_String)
		{
			m_KeyLimitRange = data.GetValue_String();
			return true;
		}
	}
		break;
	case mi_paramkey_speed:
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			m_Speed = (float)data.GetValue_Number();
			return true;
		}
	}
		break;
	case mi_paramkey_speed_ratio:
	{
		if (data.GetType() == BTLuaDataType_String)
		{
			m_keySpeedRation = data.GetValue_String();
			return true;
		}
	}
		break;
	case mi_paramkey_avoidWater:
	{
		if (data.GetType() == BTLuaDataType_Bool)
		{
			m_bIsAvoidWater = data.GetValue_Bool();
			return true;
		}
	}
		break;
	case mi_paramkey_minDistance:
	{
		if (data.GetType() == BTLuaDataType_Number)
		{
			float mindis = (float)data.GetValue_Number();
			mindis *= BLOCK_SIZE;
			m_minDistanceSqr = (int)(mindis * mindis);
			return true;
		}
	}
		break;
	case mi_paramkey_exact:
	{
		if (data.GetType() == BTLuaDataType_Bool)
		{
			m_bExact = data.GetValue_Bool();
			return true;
		}
	}
		break;
	default:
		break;
	}

	return BTNodeTask::SetParam(key, data);
}

bool BTNTaskMoveTo::ActivateCondition(const BTNodeRunData& rundata)
{
	if (!BTNodeTask::ActivateCondition(rundata))
		return false;

	if (!rundata.GetBlackboard() || !rundata.GetBlackboard()->GetData_Vector3(m_KeyTargetPos.c_str(), m_TargetPos)&&!GetValPos(mi_paramkey_bbkey_targetpos, rundata, m_TargetPos)) // ��ȡĿ��λ��
		return false;

	double range = 0.0;
	if (rundata.GetBlackboard() && rundata.GetBlackboard()->GetData_Number(m_KeyLimitRange.c_str(), range)) //��ȡѰ·��Χ��û�о���Ĭ�ϵ�
	{
		m_limitRange = (int)range;
	}
	else if(GetValNum(mi_paramkey_bbkey_limitrange, rundata,m_limitRange))
	{

	}
	GetValNum(mi_paramkey_speed, rundata, m_Speed);
	GetValBool(mi_paramkey_avoidWater, rundata,m_bIsAvoidWater);
	float mindis;
	if (GetValNum(mi_paramkey_minDistance, rundata, mindis))
	{
		mindis *= BLOCK_SIZE;
		m_minDistanceSqr = (int)(mindis * mindis);
	}
	GetValBool(mi_paramkey_exact, rundata,m_bExact);
	auto functionWrapper = m_AttachObj->getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setCanSwimming(true);
		functionWrapper->setAvoidWater(m_bIsAvoidWater);
		functionWrapper->setCanPassClosedWoodenDoors(true);
	}
	NavigationPath* npath = m_AttachObj->getNavigator(); // ����·��
	if (npath)
	{
		WCoord pos = m_TargetPos;
		pos = BlockCenterCoord(pos);
		PathEntity* pathentity = NULL;
		if (m_bExact)
		{
			pathentity = npath->getPathTo(pos.x, pos.y, pos.z, m_limitRange*BLOCK_SIZE, 0);
		}
		else
		{
			pathentity = npath->getPathTo(pos.x, pos.y, pos.z, m_limitRange*BLOCK_SIZE);
		}
		if (pathentity)
		{
			npath->setPath(pathentity, m_Speed* m_SpeedRatio);
			return npath->getPath() != NULL;
		}
	}

	return true; // û��·��
}

bool BTNTaskMoveTo::RunBefore(const BTNodeRunData& rundata)
{
	if (!BTNodeTask::RunBefore(rundata))
		return false;

	double ratio;
	if (rundata.GetBlackboard() && rundata.GetBlackboard()->GetData_Number(m_keySpeedRation.c_str(), ratio))
	{
		m_SpeedRatio = (float)ratio;
	}
	else if (GetValNum(mi_paramkey_speed_ratio, rundata,m_SpeedRatio))
	{

	}

	
	//m_TargetPos.

	NavigationPath* npath = m_AttachObj->getNavigator();
	if (!npath->getPath())
	{
		m_AttachObj->SetAIJumping(false);
		if (m_AttachObj->isInWater() || m_AttachObj->HandleLavaMovement())
		{
			m_AttachObj->SetAIJumping(true);
		}
		//return false;
	}

	if (m_CollideHorizontallyStop)
	{
		// ˮƽײ��ʵ��
		ClientMob* pMob = dynamic_cast<ClientMob*>(m_AttachObj);
		if (pMob)
		{
			if (pMob->getLocoMotion()->m_CollidedHorizontally)
			{
				return false;
			}
		}
	}

	return true;
}
void  BTNTaskMoveTo::SendDataToDebug()
{
	if (!IsDebugMode())
	{
		return;
	}
	ScriptVM*	LuaVM = MINIW::ScriptVM::game();
	int treeid = GetBehaviorTreeInstance()->GetInstanceID();
	int id = 0;
	LuaVM->callFunction("BTDebugBegainSendData", ">i",  &id);
	if (id > 0)
	{
		BTLuaData temp;
		temp.SetValue_Vector3(m_TargetPos.x, m_TargetPos.y, m_TargetPos.z);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id,&temp);
		temp.Clear();
		temp.SetValue_Number(m_limitRange);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();
		temp.SetValue_Number(m_Speed);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();
		temp.SetValue_Number(m_SpeedRatio);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();
		temp.SetValue_Bool(m_bIsAvoidWater);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();
		temp.SetValue_Number(m_minDistanceSqr);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();
		temp.SetValue_Bool(m_bExact);
		LuaVM->callFunction("BTDebugAddValue", "iu[BTLuaData]", id, &temp);
		temp.Clear();
		LuaVM->callFunction("BTDebugEndSendData", "isi", treeid, m_nodeid.c_str(),id);
	}
}

BTNODERESULT BTNTaskMoveTo::RunActive(const BTNodeRunData& rundata)
{

	// �ڷ�Χ�ڳɹ�
	if (CheckMinDistance())
		return BTNODERESULT_SUCCESS;

	// û��·��
	NavigationPath* npath = m_AttachObj->getNavigator();
	PathEntity* pathentity = npath ? npath->getPath() : NULL;
	if (!pathentity)
	{
		return BTNODERESULT_FAIL;
	}

	m_AttachObj->SetAIJumping(false);
	if (m_AttachObj->isInWater() || m_AttachObj->HandleLavaMovement())
	{
		m_AttachObj->SetAIJumping(true);
	}

	if (pathentity && pathentity->isFinished()) // Ѱ·���
		return BTNODERESULT_SUCCESS;

	return BTNODERESULT_WAIT;
}

void BTNTaskMoveTo::OnStart(const BTNodeRunData& rundata)
{
	BTNodeTask::OnStart(rundata);

	// actived
}

void BTNTaskMoveTo::OnEnd()
{
	// disactived
	m_AttachObj->getNavigator()->clearPathEntity();
	m_AttachObj->SetAIJumping(false);
	BTNodeTask::OnEnd();
}

bool BTNTaskMoveTo::CheckMinDistance()
{
	WCoord objpos = m_AttachObj->getPosition();
	WCoord tgtpos = BlockCenterCoord(WCoord(m_TargetPos));
	objpos.y = 0; // ֻ���x��z�����
	tgtpos.y = 0;
	return objpos.squareDistanceTo(tgtpos) < m_minDistanceSqr;
}
