
#include "ClientMob.h"
#include "ClientActorManager.h"
#include "world.h"
#include "container_RevivalStatue.h"
#include "WorldManager.h"
#include "BlockRevivalStatue.h"
#include "ClientActorHelper.h"
#include "SummonMonsterSiegeMgr.h"
#include "DangerNightManager.h"
#include "SandboxIdDef.h"
#include "ActorLocoMotion.h"
#include "IPlayerControl.h"
#include "BlockMaterialMgr.h"
#include "BlockGrayHerbs.h"
#include "chunk.h"
#include "SandboxEventDispatcherManager.h"
#include "PlayerControl.h"
//初级游戏难度游戏天数
const static int PRIMARY_GAME_DAYS = 30;
//普通游戏难度游戏天数
const static int NORMAL_GAME_DAYS = 50; 
//搜寻半径
const static int SEARCH_PLAYER_RADIUS = 32;
//方块范围半径
const static int BLOCK_RADIUS_RANGE = 32;

flatbuffers::Offset<FBSave::ChunkContainer> ContainerRevivalStatue::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	std::vector<FBSave::Coord3>revivePoints;
	for(size_t i=0; i<m_revivePoint.size(); i++)
	{
		revivePoints.push_back(WCoordToCoord3(m_revivePoint[i]));
	}
	
	auto actor = FBSave::CreateContainerRevivalStatue(builder, basedata, builder.CreateVectorOfStructs(revivePoints), builder.CreateVector(m_mapid, MAX_WORLD_EX));
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerRevivalStatue, actor.Union());
}

bool ContainerRevivalStatue::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerRevivalStatue *>(srcdata);
	loadContainerCommon(src->basedata());

	if (src->revivePoint() == NULL || src->mapid() == NULL)
	{
		return false;
	}

	m_revivePoint.resize(src->revivePoint()->size());
	for(size_t i=0; i<m_revivePoint.size(); i++)
	{
		m_revivePoint[i] = Coord3ToWCoord(src->revivePoint()->Get(i));
	}

	for(size_t i=0; i<src->mapid()->size(); i++)
	{
		int mapid = src->mapid()->Get(i);
		if(i < MAX_WORLD_EX) 
		{
			m_mapid[i] = mapid;
		}
	}

	return true;
}

int ContainerRevivalStatue::getObjType() const
{
	return OBJ_TYPE_EFFECT;
}

void ContainerRevivalStatue::enterWorld(World *pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();

	if(pworld->onClient())
	{
	}

	resetActiveStatus();
}

void ContainerRevivalStatue::leaveWorld()
{
	WorldContainer::leaveWorld();
}

void ContainerRevivalStatue::activeRevivalStatueBroken()
{
	if (!m_World)
		return;
	SummonMonsterSiegeMgr* pMonsterSiegeMgr = dynamic_cast<SummonMonsterSiegeMgr*>(m_World->GetMonsterSiegeMgr());
	if (!pMonsterSiegeMgr)
		return;
	unsigned short mapid = m_World->getCurMapID();
	//激活的石质雕像被破坏
	if (!m_isRevivalStatueActive[mapid])
		return;
	pMonsterSiegeMgr->RevivalStatueBroken();
}

void ContainerRevivalStatue::updateTick()
{
	if (m_World == NULL)
		return;
	
#ifdef IWORLD_SERVER_BUILD
	//云服不走以下逻辑
	return;
#endif

	resetActiveStatus();

	tryStartMonsterSiege();
}

bool ContainerRevivalStatue::getActiveStatus()
{
	if (m_World == NULL)
		return false;
	unsigned short mapid = m_World->getCurMapID();
	return m_isRevivalStatueActive[mapid];
}

void ContainerRevivalStatue::resetActiveStatus()
{
	if (m_World == NULL || g_WorldMgr == NULL)
		return;
	unsigned short mapid = m_World->getCurMapID();

	const WCoord& revivePoint = g_WorldMgr->getRevivePointEx(m_World);
	if (revivePoint.x == m_BlockPos.x && revivePoint.y == m_BlockPos.y && revivePoint.z == m_BlockPos.z)
		m_isRevivalStatueActive[mapid] = true;
	else
		m_isRevivalStatueActive[mapid] = false;
}

void ContainerRevivalStatue::tryStartMonsterSiege()
{
	if (m_World == NULL || g_WorldMgr == NULL || g_pPlayerCtrl == NULL)
		return;

	unsigned short mapid = m_World->getCurMapID();

	auto taskResult = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_GetTaskState", MNSandbox::SandboxContext(nullptr).SetData_Number("objId", g_pPlayerCtrl->getObjId()).SetData_Number("taskId", 1002014));
	bool isCompleteTask = (taskResult.IsSuccessed() && taskResult.GetData_Number("taskState") == TASK_COMPLETE);

	//开始攻城的条件：游戏天数大于30天，或者完成1002014任务，并且有玩家在激活的石质雕像附近32个方块半径内，并且在规定时间内就开启怪物攻城
	if (g_WorldMgr->getWorldTimeDay() < PRIMARY_GAME_DAYS && !isCompleteTask)
		return;
	
	if (!m_isRevivalStatueActive[mapid])
		return;
	
	DangerNightManager* pDangerNightMgr = dynamic_cast<DangerNightManager*>(g_WorldMgr->getDangerNightManager());
	if (pDangerNightMgr == NULL)
		return;

	//判断是否在攻城的合法时间内
	if (!pDangerNightMgr->canStartMonsterSiege())
		return;

	SummonMonsterSiegeMgr* pMonsterSiegeMgr = dynamic_cast<SummonMonsterSiegeMgr*>(m_World->GetMonsterSiegeMgr());
	if (pMonsterSiegeMgr == NULL)
		return;

	//正在召唤怪物攻城
	if (pMonsterSiegeMgr->IsSummoningMonster())
		return;

	MNSandbox::SandboxContext contextRevive;
	contextRevive.SetData_UserObject("revival_statue_point", m_BlockPos);
	MNSandbox::SandboxResult result = m_World->Event().Emit("IsNearDragonCup", contextRevive);
	//如果附近32格方块半径内有黑龙方块就不开启怪物攻城
	if (result.HasKey("dragoncup_point"))
	{
		//获取到的黑龙方块的位置
		WCoord dragonCupPoint = result.GetData_UserObject<WCoord>("dragoncup_point");
		return;
	}

	SiegeDifficultyLevel difficultyLevel = SiegeDifficultyLevel::DifficultyLevel_Primary;
	//激活的石质雕像底部有虚空晶块就提升难度
	if (m_World->getBlockID(DownCoord(m_BlockPos)) == BLOCK_VOID_CRYSTAL)
	{
		difficultyLevel = SiegeDifficultyLevel::DifficultyLevel_Medium;
	}
	else if(g_WorldMgr->getWorldTimeDay() >= NORMAL_GAME_DAYS)
	{
		difficultyLevel = SiegeDifficultyLevel::DifficultyLevel_Normal;
	}
	
	ActorManager* pActorManager = dynamic_cast<ActorManager*>(m_World->getActorMgr());
	if (pActorManager == NULL)
		return;

	WCoord statueWorldPos(m_BlockPos.x * BLOCK_SIZE, m_BlockPos.y * BLOCK_SIZE, m_BlockPos.z * BLOCK_SIZE);
	std::vector<ClientPlayer*> players;
	//判断是否有玩家在激活的石碑方块32半径内
	pActorManager->selectNearAllPlayers(players, statueWorldPos, SEARCH_PLAYER_RADIUS * BLOCK_SIZE);
	if (players.size() == 0)
		return;

	pMonsterSiegeMgr->SetRevivalStatuePos(statueWorldPos);
	
	//自己召唤自己的,询问了策划，这里的逻辑是只判断自己是不是站在自己激活的石质雕像内，
	//如果是就只管召唤自己的怪物攻城
	std::vector<int> playerUins;
	for (IClientPlayer* player : players)
	{
		int playerUin = player->getUin();
		if (g_pPlayerCtrl->getUin() == playerUin)
		{
			playerUins.push_back(playerUin);
		}
	}

	pMonsterSiegeMgr->StartSummonMonster(difficultyLevel, playerUins);
}
