

#ifndef __ARROW_LOCOMOTION_H__
#define __ARROW_LOCOMOTION_H__

#include "ActorLocoMotion.h"
class ArrowLocoMotion : public ActorLocoMotion
{
public:
	DECLARE_COMPONENTCLASS(ArrowLocoMotion)
	ArrowLocoMotion();
	virtual ~ArrowLocoMotion()
	{

	}

	virtual void tick();
	virtual void setThrowableHeading(const Rainbow::Vector3f &dir, float vel, float deviation);

private:
	void tickInAir();
	void tickInGround();

public:
	bool m_InGround;
	int m_TicksInAir;
	int m_TicksInGround;
	int m_InBlockID;
	int m_InBlockData;
	WCoord m_BlockPos;
	int m_ArrowShake;
};

#endif