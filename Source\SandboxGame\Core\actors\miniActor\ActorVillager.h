#ifndef __ACTORVILLAGER_H__
#define __ACTORVILLAGER_H__

#include "actors/generalActor/ClientMob.h"
#include "actors/actorAttrib/VillagerAttrib.h"
#include "SandboxGame.h"

class BindActorComponent;
////tolua_begin
//enum
//{
//	DEMAND_NOT,
//	DEMAND_GIFT,
//	DEMAND_HAIR_DYE,
//	DEMAND_HAIR_CUT,
//	DEMAND_TAMED,
//	DEMAND_FOOD,
//	DEMAND_TOOL,
//};
////tolua_end
////tolua_begin
//enum
//{
//	PROFESSION_NOT,
//	PROFESSION_WOODCUTTER,			//樵夫
//	PROFESSION_MELEE_GUARD,			//近战守卫
//	PROFESSION_FARMER,				//农夫
//	PROFESSION_REMOTE_GUARD,		//远程守卫
//	PROFESSION_HELPER,				//助手
//	PROFESSION_HUNTER,              //猎人 和比远程守卫多拾取的功能
//	PROFESSION_ARCHITECT,           //建造师
//};
////tolua_end
class EXPORT_SANDBOXGAME ActorVillager;
class ActorVillager : public ClientMob //tolua_exports
{ //tolua_exports
	DECLARE_SCENEOBJECTCLASS(ActorVillager)
public:

	//tolua_begin
	ActorVillager();
	void createEvent();

	virtual bool canCarried(ClientPlayer* player);
	//tolua_end
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool load(const void *srcdata, int version) override;
	virtual int getObjType()const override;
	virtual bool init(int monsterid) override;
	virtual void tick();
	virtual bool interact(ClientActor*player, bool onshift = false, bool isMobile = false) override;

	virtual bool isDead();
	virtual bool attackedFrom(OneAttackData &atkdata, ClientActor *attacker);
	virtual void onDie();
	virtual bool supportSaveToPB()
	{
		return false;
	}

	virtual int sleepInBed(const WCoord &blockpos) override;
	virtual void wakeUp() override;

	virtual void beHurtOnTrigger(float hp);
	virtual void warnThief(ClientActor* thief);
	virtual void attackThief(ClientActor* thief);
	virtual void enterWorld(World *pworld) override;
	//tolua_begin
	VillagerAttrib *getVillagerAttrib()
	{
		return static_cast<VillagerAttrib *>(getAttrib());
	}

	//设置头发颜色
	void setHairColor( unsigned int color, int hairId = 0);
	//获取头发颜色
	unsigned int getHairColor() {return m_hairColor;}
	//设置随机发型
	void setRandHair(int hairId = 0);
	//剪头发
	void setHairId(int hairId);
	//获取显示的发型id
	int getHairId() { return m_hairId; }
	//设置面部表情
	void setFaceId(int faceId);
	//获取显示的面部id
	int getFaceId() { return m_faceId; }
	//获取子模型模型名字
	/* slot 获取模型上的位置 ，priority < 0 获取优先级，如果有装备填2 level 获取等级 */
	std::string getSubModeName(int slot, int priority = -1, int level = 1);
	/* slot 获取模型上的位置 ，priority < 0 获取优先级，如果有装备填2 level 获取等级 */
	std::string getIceSubModeName(int slot, int priority = -1, int level = 1);

	void updateBodyCloth();
	void notifyProfressCloth(std::string name, bool bshow);
	bool getIsTraitor() { return m_bTraitor; }  //获取野人是否叛逃
	void setIsTraitor(bool state); // { m_bTraitor = state; }  //设置野人是否叛逃
	bool getGuardForPlayer() { return b_isGuardForPlayer; } //设置是否为玩家护卫
	void setGuardForPlayer(bool b); //设置为玩家护卫
	void addFreezingBuffer();
	bool isFreezing();//是否冰冻状态
	int getSpawnTime() { return m_spawnTime; }  //获取出生时间
	void setSpawnTime(int time) { m_spawnTime = time; }  //设置出生时间

	void sitInChair(const WCoord blockpos); // 设置坐下
	void sitInBackround(); // 站起方块
	void standUp(); // 原地站起

	virtual void setBedBindPos(WCoord pos) override
	{
		m_BedBindPos = pos;
	}
	virtual WCoord getBedBindPos() override
	{
		return m_BedBindPos;
	}

	void setWorkBindPos(WCoord pos)
	{
		m_WorkBindPos = pos;
	}

	WCoord getWorkBindPos()
	{
		return m_WorkBindPos;
	}

	void setProfession(short profession)
	{
		m_iProfession = profession;
		if (m_iProfession == PROFESSION_MELEE_GUARD || m_iProfession == PROFESSION_REMOTE_GUARD)
		{
			auto* attrib = getVillagerAttrib();
			if (attrib)
			{
				attrib->setSleepTime(10.0f);
				attrib->setStayupTime(12.0f);
				attrib->setWakeupTime(16.0f);
			}
		}
	}

	short getProfession()
	{
		return m_iProfession;
	}

	bool isFightProfession() const 
	{
		return m_iProfession == PROFESSION_MELEE_GUARD || m_iProfession == PROFESSION_REMOTE_GUARD || m_iProfession == PROFESSION_HUNTER;
	}

	bool isIceVillage() const;
	void saveTickTime(unsigned int idx);
	unsigned int getTickDuration(unsigned int idx) const;
	
	void setDemand(short demand);

	short getDemand()
	{
		return m_iDemand;
	}

	void setFoodByTamed(short foodid)
	{
		m_iFoodByTamed = foodid;
	}

	short getFoodByTamed()
	{
		return m_iFoodByTamed;
	}

	void checkDefection();  //检测触发野人叛逃
	void generateKeepsakePackage(bool isDefect); //野人生成纪念包裹

	//设置冷却时间
	void setCoolingTime(int ctype, int time);
	//获取冷却时间
	int getCoolingTime(int ctype);
	bool userNameBrand(int operateUin);
	virtual bool IsExtremis();
	void checkExtremisForBed(bool bExtremis); //跟新床的状态

	void addHuggerByType(const char* str, int baseVal = 1);
	void statisticEvent(int eventid, int uin);

	void setRandomNameId(int id);
	void setWorship(bool b) { b_worship = b; };
	bool getWorship() { return b_worship; };
	void setIceGirl(short int isGril) { m_iceGirl = isGril; };//mobs.lua set
	void seticedefaultEmoji(std::string emoji) { m_icedefaultEmoji = emoji; };//mobs.lua set
	//tolua_end
private:
	bool useBandage(ClientPlayer *player);
	bool beTamed(ClientPlayer *player);
	bool awardedProfession(ClientPlayer *player);
	bool relieveProfession(ClientPlayer *player);
	bool equipItem(ClientPlayer *player);
	void throwItem(BackPackGrid &grid);
	bool giveFood(ClientPlayer *player);
	bool giveTool(ClientPlayer *player);
	bool giveGift(ClientPlayer *player);
	bool dyeHair(ClientPlayer *player);
	bool cutHair(ClientPlayer *player);
	bool giveCorp(ClientPlayer *player);
	int findTool(int tooltype, int toolid=0);
	bool carriedActor(ClientPlayer *player);
	void checkIsReachWakeUpTime();
	void checkMoveDist();
protected:
	int m_hairId;				//显示的发型ID
	unsigned int m_hairColor;	//显示的发型颜色
	int m_faceId;				//显示的面部ID
	WCoord m_BedBindPos;		//绑定床的位置
	WCoord m_WorkBindPos;		//工作点
	short m_iProfession;		//职业
	bool m_bTraitor;			//叛逃者
	short m_iDemand;			//需求
	int m_spawnTime;			//出生时间点
	short m_iFoodByTamed;		//驯服所需食物
	int m_coolingTimes[100];	//冷却时间
	int m_iNeedJudgeCountDown;	//触发需求助手服务的倒计时
	bool m_bCheckWakeUpTime;    //是否检查起床时间
	WCoord m_OldPos;			//用来检测移动距离的
	int m_iCheckMoveDistTick;   //每隔一段时间检测移动距离
	int m_randomNameId;			//随机野人名字表里的id，名字在不同客户端需要多语言显示
	bool b_isGuardForPlayer;    //护卫玩家
	bool b_worship;             //崇拜状态
	unsigned int m_saveTimeTick[2];//b记录时间
	std::vector<int>m_bufferToAdd;//buffer添加
	short int m_iceGirl;			// 0boy 1girl; //冰原村民模型skin才有这个区分
	std::string m_icedefaultEmoji;  //冰原默认表情
protected:
	virtual bool canDespawn();

	MNSandbox::AutoRef<MNSandbox::Listener<IClientPlayer*, std::string, bool>> m_listenerVillager1;
}; //tolua_exports

class ClientSouvenir : public ClientMob //tolua_exports
{ //tolua_exports
	DECLARE_SCENEOBJECTCLASS(ClientSouvenir)
public:
	//tolua_begin
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool supportSaveToPB()
	{
		return false;
	}

	virtual int getObjType() const override;

	virtual ActorLocoMotion *newLocoMotion();

	virtual void enterWorld(World *pworld);

	//virtual void leaveWorld(bool keep_inchunk);

	virtual bool load(const void *srcdata, int version);

	virtual bool leftClickInteract(ClientActor*player)override;
	//tolua_end
protected:
	virtual BindActorComponent* getBindActorCom() override;
	virtual bool canDespawn()
	{
		return false;
	}
}; //tolua_exports


#endif
