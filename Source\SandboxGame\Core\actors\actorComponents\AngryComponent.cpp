#include "AngryComponent.h"

#include "ClientMob.h"
#include "ToAttackTargetComponent.h"
IMPLEMENT_COMPONENTCLASS(AngryComponent)

AngryComponent::AngryComponent()
{

	m_Angry = false;
	m_AIAngry = false;
}

AngryComponent::~AngryComponent()
{

}

void AngryComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void AngryComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void AngryComponent::OnTick()
{
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_owner) return ;
	bool toAtkTarget = false;
	auto targetComponent = m_owner->getToAttackTargetComponent();
	if (targetComponent)
	{
		toAtkTarget = targetComponent->hasTarget();
	}

	if (getAngry() && !toAtkTarget)
	{
		setAngry(false);
	}
	else if (!getAngry() && toAtkTarget && !m_owner->getTamed())
	{
		setAngry(true);
	}
}
