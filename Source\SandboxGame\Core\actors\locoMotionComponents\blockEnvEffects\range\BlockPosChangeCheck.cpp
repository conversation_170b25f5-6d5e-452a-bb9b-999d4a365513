#include "BlockPosChangeCheck.h"

#include "ClientActor.h"

using namespace MNSandbox;


BlockPosChangeCheck::BlockPosChangeCheck() : m_PreBlockPos(0, -10000,0)
{

}

bool BlockPosChangeCheck::isInRange(ClientActor* pActor, BlockEnvEffectBase* pEffect)
{
	ClientActor* owner = pActor;// context.GetData_Usertype<ClientActor>("owner");
	if (!owner)	return false;

	const WCoord& pos = owner->getPosition();

	const WCoord& curBlockPos = pos / BLOCK_SIZE;
	if (m_PreBlockPos.y == -10000) {
		m_PreBlockPos = curBlockPos;
		return true;
	}

	if (m_PreBlockPos == curBlockPos) {
		return false;
	}

	m_PreBlockPos = curBlockPos;
	return true;
}