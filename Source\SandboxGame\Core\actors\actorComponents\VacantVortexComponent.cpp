#include "VacantVortexComponent.h"
#include "world.h"
#include "SoundComponent.h"
#include "EffectComponent.h"
#include "ClientActorFuncWrapper.h"
#include "navigationpath.h"
#include "PlayerControl.h"
#include "MpActorManager.h"
#include "MpActorTrackerEntry.h"
IMPLEMENT_COMPONENTCLASS(VacantVortexComponent)

VacantVortexComponent::VacantVortexComponent()
{

}

VacantVortexComponent::~VacantVortexComponent()
{

}

void VacantVortexComponent::init()
{
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (m_owner == NULL)
	{
		return;
	}
	m_teleportCd = 20 * 1.5; // 传送cd时间

	auto functionWrapper = m_owner->getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setCanMove(false);
	}

	m_owner->setImmuneAttackType(ATTACK_ALL);

	m_nVortexOwnerOjbid = 0;
	m_nEnterObjid = 0;
	m_nExitObjid = 0;
	m_nVortexLiveTick = 0;

	Vector3f offSetPos = Vector3f(0, 150, 0);
	Vector3f rotate = Vector3f(0, 0, 0);
	m_owner->PlayBodyEffect("wl_chuansong_01", -1, offSetPos, rotate, 1.0f, true);
}

void VacantVortexComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	init();
	BindOnTick();
}

void VacantVortexComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void VacantVortexComponent::OnTick()
{
	if (!GetOwner()) return;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	World* pWorld = m_owner->getWorld();
	if (!(m_owner && pWorld && !m_owner->needClear()))
	{
		return;
	}

	map<long long, int>::iterator it;
	for (it = actorTeleportCd.begin(); it != actorTeleportCd.end(); )
	{
		it->second--;
		if (it->second <= 0) {
			actorTeleportCd.erase(it++);
		}
		else {
			it++;
		}
	}

	if ((m_nExitObjid != 0) && (m_nEnterObjid != 0))
	{
		m_nVortexLiveTick = m_nVortexLiveTick + 1;
		int nTime = 60; // 漩涡生存时间
		if (m_nVortexLiveTick >= 20 * nTime)
		{
			m_owner->setNeedClear();
		}
	}

	if (!m_owner->isDead() && !m_owner->needClear()) {
		CollideAABB box;
		m_owner->getCollideBox(box);
		std::vector<IClientActor*>actors;
		pWorld->getActorsInBoxExclude(actors, box, m_owner);
		for (size_t i = 0; i < actors.size(); i++)
		{
			ClientActor* actor = actors[i]->GetActor();
			if (!actor->needClear())
			{
				onCollideWithActor(actor);
			}
		}
	}
}

void VacantVortexComponent::onCollideWithActor(ClientActor* actor)
{
	if (!GetOwner()) return ;
	ClientMob* m_owner = dynamic_cast<ClientMob*>(GetOwner());
	if (!(m_owner && m_owner->getActorMgr() && m_owner->getWorld() && m_owner->getWorld()->getMpActorMgr()))
	{
		return;
	}

	ClientMob* m_vortexOwner = dynamic_cast<ClientMob*>(m_owner->getActorMgr()->findActorByWID(getVortexOwnerOjbid()));
	if (m_vortexOwner == NULL)
	{
		return;
	}

	//bool bCheck = false;
	/*if (getVortexOwnerOjbid() == actor->getObjId())
	{
		bCheck = true;
	}
	else
	{
		ClientPlayer* pTamedOwner = m_vortexOwner->getTamedOwner();
		if (pTamedOwner && pTamedOwner->getObjId() == actor->getObjId())
		{
			bCheck = true;
		}
	}*/

	if (actor && !actor->isDead() && !actor->needClear())
	{
		if (actorTeleportCd[actor->getObjId()]) {
			return;
		}

		long long moveToObjid = 0;
		if (getEnterObjid() == m_owner->getObjId())
		{
			moveToObjid = getExitObjid();
		}
		else if (getExitObjid() == m_owner->getObjId())
		{
			moveToObjid = getEnterObjid();
		}

		ClientMob* target = dynamic_cast<ClientMob*>(m_owner->getActorMgr()->findActorByWID(moveToObjid));
		if (target == NULL)
			return;

		WCoord pos;
		//WCoord pos = WCoord(target->getPosition().x, target->getPosition().y, target->getPosition().z);
		MINIW::ScriptVM::game()->callFunction("GetVacantVortexTeleportPos", "u[ClientMob]u[ClientMob]>iiii", actor, target, &pos.x, &pos.y, &pos.z, &m_teleportCd);

		WCoord blockpos = CoordDivBlock(pos);
		if (m_vortexOwner->getWorld()->getChunk(blockpos) == NULL)
		{
			return;
		}

		actor->getLocoMotion()->gotoPosition(pos);
		addPlayerCd(actor->getObjId(), m_teleportCd);

		if (actor->getObjType() == OBJ_TYPE_ROLE)
		{
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
			if (player)
			{
				MpActorTrackerEntry* entry = m_owner->getWorld()->getMpActorMgr()->getTrackerEntry(player->getObjId());
				if (entry) entry->sendActorMovementToClient(player->getUin(), actor, player->getLocoMotion()->m_RotateYaw, player->getLocoMotion()->m_RotationPitch);
			}
		}

		auto pTargetVacantVortexComponent = target->getVacantVortexComponent();
		if (pTargetVacantVortexComponent)
		{
			pTargetVacantVortexComponent->addPlayerCd(actor->getObjId(), m_teleportCd);
		}
		if (actor->getNavigator())
		{
			actor->getNavigator()->clearPathEntity();
		}

		auto effectComp = actor->getEffectComponent();
		if (effectComp) {
			effectComp->stopBodyEffect("portal_feedback");
			effectComp->playBodyEffect("portal_feedback");
		}

		//if (!GetClientInfoProxy()->isPureServer()) // 不是云服,播放贴脸特效贴图
		//{
		//	bool bShowVacantEffect = false;
		//	if (actor == g_pPlayerCtrl)
		//	{
		//		bShowVacantEffect = true;
		//	}
		//	else
		//	{
		//		auto RidComp = g_pPlayerCtrl->getRiddenComponent();
		//		if (RidComp && RidComp->isRiding())
		//		{
		//			ActorHorse* riding = dynamic_cast<ActorHorse*>(RidComp->getRidingActor());
		//			if (riding == actor)
		//			{
		//				bShowVacantEffect = true;
		//			}
		//		}
		//	}
		//	if (bShowVacantEffect)
		//	{
		//		// 美术资源没有出来，暂时屏蔽
		//		/*auto pVacantEffectComponent = g_pPlayerCtrl->GetComponent<VacantEffectComponent>();
		//		if (!pVacantEffectComponent)
		//		{
		//			pVacantEffectComponent = g_pPlayerCtrl->CreateComponent<VacantEffectComponent>("VacantEffectComponent");
		//		}
		//		pVacantEffectComponent->start();*/
		//	}
		//}

		auto soundComp = actor->GetComponent<SoundComponent>();
		if (soundComp)
		{
			soundComp->playSound("ent.portal.portal", 1, 1, 6);
		}
	}
}

void VacantVortexComponent::addPlayerCd(long long playerId, int tickNum/* = -1 */)
{
	if (tickNum <= 0) {
		tickNum = m_teleportCd;
	}
	actorTeleportCd[playerId] = tickNum;
}

void VacantVortexComponent::CreateComponentData(jsonxx::Object& componentData)
{
	jsonxx::Object userData;
	userData.import("vortexOwnerOjbid", jsonxx::String(to_string(getVortexOwnerOjbid()).c_str()));
	userData.import("enterObjid", jsonxx::String(to_string(getEnterObjid()).c_str()));
	userData.import("exitObjid", jsonxx::String(to_string(getExitObjid()).c_str()));
	userData.import("vortexLiveTick", jsonxx::Number(getVortexLiveTick()));
	componentData.import("VacantVortexComponent", jsonxx::Object(userData));
}

void VacantVortexComponent::LoadComponentData(const jsonxx::Object& componentData, bool fromArchive)
{
	if (componentData.has<jsonxx::Object>("VacantVortexComponent"))
	{
		jsonxx::Object userData = componentData.get<jsonxx::Object>("VacantVortexComponent");
		if (userData.has<jsonxx::String>("vortexOwnerOjbid"))
		{
			setVortexOwnerOjbid(std::stoll(userData.get<jsonxx::String>("vortexOwnerOjbid")));
		}
		if (userData.has<jsonxx::String>("enterObjid"))
		{
			setEnterObjid(std::stoll(userData.get<jsonxx::String>("enterObjid")));
		}
		if (userData.has<jsonxx::String>("exitObjid"))
		{
			setExitObjid(std::stoll(userData.get<jsonxx::String>("exitObjid")));
		}
		if (userData.has<jsonxx::Number>("vortexLiveTick"))
		{
			setVortexLiveTick(userData.get<jsonxx::Number>("vortexLiveTick"));
		}
	}
}