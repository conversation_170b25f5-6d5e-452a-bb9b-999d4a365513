#include "VacantEffectComponent.h"
#include "Graphics/ScreenManager.h"
#include "UIRenderer.h"
#include "PlayerControl.h"
#include "GLoader.h"
#include "ClientAppProxy.h"

using namespace MNSandbox;
using namespace Rainbow;
using namespace Rainbow::UILib;

IMPLEMENT_COMPONENTCLASS(VacantEffectComponent)

VacantEffectComponent::VacantEffectComponent()
{
	m_TextureMaterial = UIRenderer::GetInstance().CreateInstance(),
	m_gloader = fairygui::GLoader::create();
	m_gloader->retain();
	m_TextureAlpha = 255;

	//CommonResource\Script\csvdef\DevUIResource.csv
	string strLongId = to_string(24003);
	GetClientAppProxy()->DevUISetIconByResIdExProxy(m_gloader, strLongId);
	if (m_gloader && m_gloader->IsContentLoaded())
	{
		Rainbow::SequenceTexture* seqtex = static_cast<Rainbow::SequenceTexture*>(m_gloader->getSequenceTexture());
		fairygui::FUISprite* psprite = m_gloader->getContent();
		if (psprite)
		{
			m_Rect = psprite->getTextureRect();
			m_Texture = psprite->getTexture().CastTo<Rainbow::Texture2D>();
		}
	}
}

void VacantEffectComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void VacantEffectComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void VacantEffectComponent::OnTick()
{
	m_nShowTick = m_nShowTick - 1;
	if (m_nShowTick <= 0)
	{
		m_nShowTick = 0;
		m_TextureAlpha = 0;
	}
}

void VacantEffectComponent::RenderUI()
{
	if (m_Texture)
	{
		ScreenManager& screenManager = GetScreenManager();
		int ScreenWidth = screenManager.GetWidth();
		int ScreenHeight = screenManager.GetHeight();
		UIRenderer::GetInstance().BeginDraw(m_TextureMaterial, m_Texture);
		UIRenderer::GetInstance().StretchRect(0, 0, (float)ScreenWidth, (float)ScreenHeight, ColorRGBA32(255, 255, 255, m_TextureAlpha), m_Rect.origin.x, m_Rect.origin.y, m_Rect.size.width, m_Rect.size.height);
		UIRenderer::GetInstance().EndDraw();
	}
}

VacantEffectComponent::~VacantEffectComponent()
{
	m_Texture = nullptr;
	CC_SAFE_RELEASE_NULL(m_gloader);
}

void VacantEffectComponent::start()
{
	m_TextureAlpha = 255;
	m_nShowTick = 20;
}