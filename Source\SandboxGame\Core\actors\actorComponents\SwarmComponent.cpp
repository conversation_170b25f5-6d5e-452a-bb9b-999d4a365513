#include "SwarmComponent.h"
#include "ClientMob.h"
#include "EffectComponent.h"

IMPLEMENT_COMPONENTCLASS(SwarmComponent)

SwarmComponent::SwarmComponent(){
	m_followSwarmIds.clear();
}

void SwarmComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	ClientActor* pOwnerActor = dynamic_cast<ClientActor*>(owner);
	if (pOwnerActor)
	{
		pOwnerActor->BindSwarmComponent(this);
	}
	BindOnTick();
}
void SwarmComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
	ClientActor* pOwnerActor = dynamic_cast<ClientActor*>(owner);
	if (pOwnerActor)
	{
		pOwnerActor->BindSwarmComponent(NULL);
	}
}

void SwarmComponent::addFollowSwarmId(int id) {

	if (isContainFollowSwarmId(id))
	{
		return;
	}
	
	if (m_followSwarmIds.size() >= 3)
	{
		std::vector<int> containIds;
		//const std::vector<int> mobIds{ 3604,3605,3606,3607,3609,3610,3611,3612,3613,3614,3615,3616,3617,3618,3619 };
		//ClientMob* myMob = GetOwnerMob();
		ClientMob* ownerMob = dynamic_cast<ClientMob*>(GetOwnerActor());
		if (!ownerMob) return;
		std::vector<ClientActor*> allMobs = ownerMob->selectAllMobs();
		int size = allMobs.size();
		int n = m_followSwarmIds.size();
		for (size_t i0 = 0; i0 < n; i0++)
		{
			bool isContain = false;
			for (size_t i1 = 0; i1 < size; i1++) {
				ClientActor* mob = allMobs[i1];
				if (mob && mob->getSwarmComponent() && m_followSwarmIds[i0] == mob->getSwarmComponent()->getSwarmId()) {
					isContain = true;
					break;
				}
			}
			if (!isContain)
			{
				m_followSwarmIds.erase(m_followSwarmIds.begin() + i0);
				break;
			}
		}
	}
	if (m_followSwarmIds.size() < 3)
	{
		m_followSwarmIds.push_back(id);

	}
}

bool SwarmComponent::isContainFollowSwarmId(int id) {
	int n = m_followSwarmIds.size();
	for (size_t i = 0; i < n; i++)
	{
		if (id == m_followSwarmIds[i])
		{
			return true;
		}
	}
	return false;
}

void SwarmComponent::OnTick() {
	if (m_isHasFluorescence)
	{
		if (!GetOwner()) return;
		ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
		if (!m_owner) return;
		tickNum++;
		if (tickNum>1200)
		{
			m_isHasFluorescence = false;
			auto effectCom = m_owner->getEffectComponent();
			if (effectCom)
			{
				effectCom->stopBodyEffect("110129_2");
			}
			//m_owner->stopBodyEffect("110129_2");
		}
	}
}

void SwarmComponent::setFluorescence(bool isHasFluorescence) {
	m_isHasFluorescence = isHasFluorescence;
	tickNum = 0;
	if (!GetOwner()) return ;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return ;
	if (isHasFluorescence)
	{
		auto effectCom = m_owner->getEffectComponent();
		if (effectCom)
		{
			effectCom->playBodyEffect("110129_2");
		}
		//m_owner->playBodyEffect("110129_2");
	}
}


SwarmComponent::~SwarmComponent(){

}