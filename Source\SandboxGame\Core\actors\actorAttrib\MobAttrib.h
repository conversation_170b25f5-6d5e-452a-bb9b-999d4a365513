
#ifndef __MOBATTRIB_H__
#define __MOBATTRIB_H__

#include "LivingAttrib.h"
#include "SandboxGame.h"
// 添加这行以确保Android编译时能看到完整的继承链
#include "ActorComponent_Base.h"

class BackPackGrid;
class PackContainer;
class BackPack;
struct MonsterDef;

struct DropItem {
	int itemId;
	int itemNum;
	int dropRate;
};

class EXPORT_SANDBOXGAME MobAttrib;
class MobAttrib : public LivingAttrib{//tolua_exports
public:
	DECLARE_COMPONENTCLASS(MobAttrib)
	//tolua_begin
	MobAttrib();
	virtual ~MobAttrib();

	virtual void init(const MonsterDef *def);
	const MonsterDef *getDef()
	{
		return m_Def;
	}

	static float defSpeed2MoveSpeed(int speed);

	virtual float getMoveSpeed(int type = 0); //not_auto_export_to_lua
	virtual float getMoveBaseSpeed(int type = 0);
	virtual void setMoveBaseSpeed(float speed, int type = 0);
	void setFood(float food)
	{
		m_Food = food;
		calculateHungryStatus();
	}
	float getFood() { return m_Food; }
	void setFoodLmtAndMax(float fFoodLmt, float fFoodLmtMax);
	void setFoodLmt(float fFoodLmt)
	{
		m_fFoodLmt = fFoodLmt;
		calculateHungryStatus();
	}
	float getFoodLmt() { return m_fFoodLmt; }
	void setFoodLmtMax(float fFoodLmtMax)
	{
		m_fFoodLmtMax = fFoodLmtMax;
		calculateHungryStatus();
	}
	float getFoodLmtMax() { return m_fFoodLmtMax; }
	bool getHungryStatus() { return m_bHungryStatus; }
	float getMaxFood();
	// 设置物理元素攻击防御(玩法模式用)
	void setAttackPhysical(float val);
	void setAttackElem(float val);
	void setDefPhysical(float val);
	void setDefElem(float val);
	// 获取物理元素攻击防御(玩法模式用)
	float getAttackPhysical() const;
	float getAttackElem() const;
	float getDefPhysical() const;
	float getDefElem() const;

	BackPackGrid* findItem(int itemid);

	void resetDiffModeAttr();

	virtual void equip(EQUIP_SLOT_TYPE t, int itemid, int durable, int toughness, int maxdurable);
	virtual void equip(EQUIP_SLOT_TYPE t, BackPackGrid* itemgrid);

	virtual int getEquipItem(EQUIP_SLOT_TYPE t) override;//not_auto_export_to_lua
	virtual int damageEquipItem(EQUIP_SLOT_TYPE t, int damage);
	virtual void dropEquipItems();
	virtual BackPackGrid *getEquipGrid(EQUIP_SLOT_TYPE t);//not_auto_export_to_lua
	void dropBagsItems();
	void dropSkinningsItems();
	void dropThornBallItems();
	
	void dropOneEquipItem(EQUIP_SLOT_TYPE slot);
	PackContainer *getBags();
	void onCurToolUsed(int num = -1); //ignore_durable: 忽略耐久， 直接减1
	void setBagItem(int index, BackPackGrid *itemgrid);
	int getFoodReduce();
	void setDropItem(int rate, int itemId, int num = 1);

	virtual float getAttackBaseLua(int attacktype);
	virtual float getArmorBaseLua(int attacktype);
	//tolua_end
	virtual void applyEquipToughness();
	virtual void applyEquipToughness(int itemid);
	void recoverToughness();
	void recoverAllToughness();
	bool needRecoverToughness() { return m_ToughnessTotal < m_ToughnessTotalMax; }
	bool canDropItem();
public:
	//tolua_begin
	void addFood(float val);
	//tolua_end
protected:
	float m_Food;
	float m_fFoodLmt;		//饥饿值小于等于x时，进入饥饿状态
	float m_fFoodLmtMax;	//饥饿值大于x时，退出饥饿状态
	bool  m_bHungryStatus;	//处于饥饿状态
	// 物理元素攻击防御
	float m_fAttackPhysical, m_fAttackElem;
	float m_fDefPhysical, m_fDefElem;

protected:
	virtual void onDie();
	void dropItem(bool burn=false);
	virtual float getBasicAttackPoint(ATTACK_TYPE atktype);
	virtual float getBasicArmorPoint(ATTACK_TYPE atktype);
	virtual void calculateHungryStatus();
private:
	const MonsterDef *m_Def;
	PackContainer *m_Equips;
	PackContainer *m_Bags;

	std::unordered_map<int, DropItem> m_dropRates;

	short m_iDecayExtremisTick;
};//tolua_exports


#endif
