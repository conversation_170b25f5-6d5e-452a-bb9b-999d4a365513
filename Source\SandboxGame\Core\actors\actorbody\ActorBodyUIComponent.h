﻿#pragma once

#include "ActorBody.h"

class World;
class ActorBody;

namespace Rainbow
{
	class NameText3D;
	class Text3D;
	class Image3D;
	class ProgressBarIn3D;
	class UIIn3DSceneMerger;
	class MeshInstance;
	class Entity;
	class MoveByTextMgr;
	class MusicClubChatBubble3D;
	class Voice3D;
	class ActorBodyAutoReleaseByMainEntity;
	class ImageBoard3D;
	class IModelMeshRenderer;
	class ModelData;
	class MovableObject;
	class GameObject;
	class AnimationData;
	class LegacyAnimationFrameEventData;
};

class ActorBodyUIComponent
{
	friend class ActorBody;
public:

	ActorBodyUIComponent(ActorBody* actorBody);
	~ActorBodyUIComponent();

	void setDispayName(const char* name, int teamid, int texid = 0, const char* title = "");
	void InitVipIcon(int texid);
	void InitNeedItemIconPtr(int texid);
	void InitNeedItemBoard(int texid);
	void InitDialog(int texid);
	void InitAchieveBoard(int texid);
	void InitAchieveIcon(int texid);
	void setDispayHomeBillBoard();
	void setNeedItemIcon(Rainbow::SharePtr<Rainbow::Texture2D> huires, int tick = -1, int huiresWidth = 62, int huiresHeight = 62);
	void setBillBoardText(const char* text);
	void setBillBoardTextNpc(bool bBillBoardTextNpc);
	void setDispayMusicClubChatBubble();
	void initDisplayVoiceIcon();
	void setMusicClubChatBubbleText(const char* text, bool isShow, int bubble, float tickTime);
	void checkAndNewHPOBJ();
	void setHPVisible(bool pVisible);
	bool getHPVisible();
	void setHPVale(int pNow, int pTotale);
	void setHpExtraValue(int val);
	void setHpTextDisplay(int type);
	void setHPColor(int r, int g, int b, int alpha/* =255 */);
	void setHPTextrueName(const char* pBg, const char* pProgress);
	Rainbow::MoveByTextMgr* getHPChangeTextMgr();
	void createHPChangeText(const char* pStr, int pFontSize, const Rainbow::ColorQuad& pColor, bool isMainPlayer /*false*/);
	void checkAndNewArmorOBJ();
	void setArmorVisible(bool pVisible);
	bool getArmorVisible();
	void setArmorVale(int now, int max, int extra);
	void setArmorColor(int r, int g, int b, int alpha);
	void setArmorTextrueName(const char* pBg, const char* pProgress);
	void setShowDialog(bool state);
	void setVisibleDispayName(bool b);
	void addNameTexId(int nameTexid);
	void setAchieveIconName(const char* texIcon, const char* texFrame);
	void setAchieveVisible(bool achieveVisible);
	void setBPTitleIconName(std::string str);
	void setBPTitleIconVisible(bool visible);
	void setVipIconName(const char* iconPath, const char* texIcon, float scale);
	void setVipIconVisible(bool vipVisible);
	void setVoiceIconVisible(bool voiceVisible);
	void setHeadIconByPath(const char* imageResPath, const char* imageResUVName, int imageWidth /*= 0*/, int imageHeight /*= 0*/, bool isSync/*=true*/);
	void setVipNameColor(int colorR, int colorG, int colorB);
	void setNameAndHpVisible(bool isVisble);
	void setHeadDisplayIcon(int itemid, int tick /* = -1 */);
	void initExchangeItemIcon();
	void setExchangeItemIcon(Rainbow::SharePtr<Rainbow::Texture2D> huires, int num = 1, int tick = -1, int huiresWidth = 62, int huiresHeight = 62);
	void setSaleItemIcon(Rainbow::SharePtr<Rainbow::Texture2D>  huires, int num = 1, int tick = -1, int huiresWidth = 62, int huiresHeight = 62);
	void setSaleItemIconByString(const std::string& pngName, int num = 1, int tick = -1, int huiresWidth = 128, int huiresHeight = 128);
	void showExchangeBubble(bool show); //只有m_exchangeItemicon和m_saleItemicon都设置过才会显示,单独显示没有意义
	void setHeadExchangeDisplayIcon(int exchageItem, int saleItem, bool isActor = false, int exchangeNum = 1, int saleNum = 1, int tick = -1);
	int  getInnerGraphicsOffest(int itype);

	void updatemForChatBubble3D(float dtime, unsigned int dtick, Rainbow::Vector3f& pos);//update 聊天气泡的
	void updateForHp(float dtime, unsigned int dtick, Rainbow::Vector3f& pos);//update-hp的
	void updateForArmor(float dtime, unsigned int dtick, Rainbow::Vector3f& pos);
	void updateForOverHead(unsigned int dtick, Rainbow::Vector3f& pos);//update-头顶显示的

	void onEnterWorld(World* pworld);

	void onLeaveWorld();

private:
	ActorBody* m_ActorBody = nullptr;

	/*头顶显示相关*/
	int m_TeamId = -1;
	int m_TexId = -1;

	Rainbow::NameText3D* m_NameDispObj;		//名字对象
	Rainbow::ProgressBarIn3D* m_HPProgressObj;//血条比例对象？
	float m_HPProcessWidth = -1.f;
	Rainbow::MoveByTextMgr* m_HPChangeEffMgr;//血条特效管理？
	int m_NameBlickStartTime;				//头顶信息闪烁开始时间
	bool m_HPVisible;						//血条的可见属性

	Rainbow::ProgressBarIn3D* m_ArmorProgressObj;//护甲比例对象
	Rainbow::MoveByTextMgr* m_ArmorChangeEffMgr;//护甲特效管理
	bool m_ArmorVisible;						//护甲的可见属性

	Rainbow::Text3D* m_BillBoardText;		// 家园告示牌文字
	Rainbow::Image3D* m_BillBoardBkg;			// 家园告示牌背景图
	Rainbow::Image3D* m_AchieveBoard;			// 勋章底板
	Rainbow::Image3D* m_AchieveIcon;			// 勋章图标
	Rainbow::Image3D* m_TitleIcon;			// 称号图标
	Rainbow::Image3D* m_Dialog;				// 对话框
	Rainbow::UIIn3DSceneMerger* m_NeedItemMerger;
	Rainbow::Image3D* m_NeedItemBoard;		// 需求物品底板
	Rainbow::Image3D* m_NeedItemIcon;			// 需求物品图标
	Rainbow::Image3D* m_NeedItemIconPtr;
	Rainbow::Image3D* m_VipIcon;				// 会员图标
	Rainbow::ImageBoard3D* m_ImageBoard;      // 物品出售与交换
	bool m_isVip;							// 是否是会员

	Rainbow::MusicClubChatBubble3D* m_MusicClubChatBubbleObj;   //音乐厅聊天气泡对象
	Rainbow::Voice3D* m_VoiceObj; //语音显示图片

	bool m_bInviteActStart;  //2021-10-20 codeby:wangyu 新增互动动作开始标记

	// 相对于相机的距离
	Rainbow::Vector3f	m_RelativePosFromCamera;
	Rainbow::Vector3f  m_vLastHeadPos;
	unsigned int m_unOverHeadTick;			//OverHead

};