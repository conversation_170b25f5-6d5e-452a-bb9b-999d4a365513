#include "ClientActorSnowBall.h"
#include "ActorLocoMotion.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "OgreUtils.h"
#include "Entity/OgreModel.h"
#include "Entity/OgreEntity.h"
#include "BlockScene.h"
#include "ClientActorManager.h"
#include "ClientPlayer.h"
#include "backpack.h"
#include "ActorAttrib.h"
#include "ClientMob.h"
#include "special_blockid.h"
#include "EffectManager.h"
#include "ActorCSProto.h"
#include "ClientItem.h"
#include "BaseItemMesh.h"
#include "ProjectileLocoMotion.h"
#include "DefManagerProxy.h"
#include "OgreRay.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "ClientActorHook.h"
//#include "OgreRenderTypes.h"
#include "OgrePrerequisites.h"
#include "OgreBezierCurve.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "GameCamera.h"
#include "PlayerControl.h"
#include "CameraModel.h"
#include "Math/Matrix4x4f.h"

#include "ClientActorHelper.h"
#include "AttackedComponent.h"
#include "RiddenComponent.h"
#include "ActorBody.h"
#include "LuaInterfaceProxy.h"

ClientActorHook::ClientActorHook() : m_nState(0), m_nHandItem(0), m_nLifeTick(6*20)
{
	
}

ClientActorHook::~ClientActorHook()
{
	if (m_EntityModel)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntityModel);
	}
}

flatbuffers::Offset<FBSave::SectionActor> ClientActorHook::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	auto startpos = WCoordToCoord3(m_StartPos);
	auto blockpos = WCoordToCoord3(m_blockpos);
	auto obj = FBSave::CreateActorHook(builder, basedata, m_ShootingActorID, m_ItemID, &startpos, m_nState, &blockpos, m_nHandItem);
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorHook, obj.Union());
}


bool ClientActorHook::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorHook *>(srcdata);
	loadActorCommon(src->basedata());
	init(src->itemid(), NULL);
	m_ShootingActorID = src->shooter();
	m_StartPos = Coord3ToWCoord(src->startpos());
	m_blockpos = Coord3ToWCoord(src->blockpos());
	m_nState = src->state();
	m_nHandItem = src->handitem();
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (loco)
	loco->syncPos = getLocoMotion()->getPosition();
	return true;
}

void ClientActorHook::onImpactWithActor(ClientActor *actor, const std::string& partname)
{
	if(m_nState == 0)
	{
		m_nState = 1;
	}
	onAttackActor(actor);
}

void ClientActorHook::onAttackActor(ClientActor *actor)
{
	ClientPlayer *player = getShootingPlayer();
	if(actor && player && actor->getObjId() != m_ShootingActorID)
	{

		Rainbow::Vector3f motion;
		Rainbow::Vector3f dir = (getLocoMotion()->getPosition() - player->getPosition()).toVector3();
		dir  = MINIW::Normalize(dir);
		//if(player->m_RidingActor != actor->getObjId())
		auto playerRidComp = player->getRiddenComponent();
		if (!(playerRidComp && playerRidComp->checkRidingByActorObjId(getObjId())))
		{
			motion = dir;
			motion *= 50.0f;
			actor->setMotionChange(motion);
			OneAttackData atkdata;
			//memset(&atkdata, 0, sizeof(atkdata));
			// 新伤害计算系统 code-by:liya
			if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
			{
				// 近战、远程、元素伤害
				int damageType = m_ProjectileDef->DamageType;
				if (m_ProjectileDef->AttackType == 0)
				{
					if (damageType == 0 || damageType == 2 || damageType == 3)
					{
						assert(false && "Projectiledef damageType error!");
					}
					atkdata.atkTypeNew = (1 << damageType);
					atkdata.atkPointsNew[damageType] = m_ProjectileDef->AttackValue;
				}
				// 独立爆炸伤害
				else
				{
					atkdata.atkTypeNew = (1 << ATTACK_EXPLODE);
					if (damageType < 4)
					{
						atkdata.explodePoints[ATTACK_PUNCH] = m_ProjectileDef->AttackValue;
					}
					else
					{
						atkdata.explodePoints[damageType] = m_ProjectileDef->AttackValue;
					}
				}
			}
			else
			{
				atkdata.atktype = ATTACK_RANGE;
				atkdata.atkpoints = m_ProjectileDef->AttackValue;
			}
			atkdata.damage_armor = true;
			atkdata.fromplayer = player;
			if (atkdata.atkpoints != 0 || atkdata.buffId != 0)
			{
				auto component = actor->getAttackedComponent();
				if (component)
				{
					component->attackedFrom(atkdata, this);
				}
			}
		}

		//钩子收回
		 WCoord nowpos = getLocoMotion()->getPosition();
		 nowpos -= m_StartPos;
		 int len = (int)nowpos.lengthSquared();
		 if(len > (500*500))
		 {
			motion = -dir;
			motion *= 500.0f;
			WCoord position = getLocoMotion()->getPosition();
			position.x += (int)motion.x;
			position.y += (int)motion.y;
			position.z += (int)motion.z;
			getLocoMotion()->setPosition(position.x, position.y, position.z);
		 }
		 else
		 {
			 //收回
			setNeedClear();
			player->setHookObj(0); 
			m_nState = 3;
		 }
	} 
}

ClientPlayer *ClientActorHook::getShootingPlayer()
{
   ClientActor *actor = getShootingActor(); 
   if(!actor)
   {
		return NULL;
   }
   ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
   return player;
}

void ClientActorHook::onImpactWithBlock(const WCoord *blockpos, int face)
{ 
    m_blockpos = *blockpos;

	ClientPlayer *player = getShootingPlayer();
	if(!player)
    {
		return;
    }
	m_nState = 2;
}

void ClientActorHook::onCollideWithPlayer(ClientActor*player)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		if (m_nState == 1 || m_nState == 2)
		{
			if (pTempPlayer && pTempPlayer->getObjId() == m_ShootingActorID)
			{
				//收回
				setNeedClear();
				pTempPlayer->setHookObj(0);
			}
		}
		else if (m_nState == 0 && player->getObjId() != m_ShootingActorID)
		{
			m_nState = 1;
			onAttackActor(player);
		}
	}
}

void ClientActorHook::update(float dtime)
{
	ClientActor::update(dtime);

	if (!m_EntityModel)
		return;

	ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	#ifndef IWORLD_SERVER_BUILD
		m_EntityModel->SetPosition(loc->getFramePosition());
	#endif
	float angle = 180;
	if(m_nState == 1)
	{
		angle = 0.0f;
	}
	#ifndef IWORLD_SERVER_BUILD
		m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch+angle, 0);
		m_EntityModel->UpdateTick(Rainbow::TimeToTick(dtime));

		RenderCurve();

	#endif

}

void ClientActorHook::tick()
{
	ClientActorProjectile::tick();

	if (m_pWorld->isRemoteMode())
	   return;

	ClientPlayer *player = getShootingPlayer();
	if(!player || player->isDead())
	{
		setNeedClear();
		if (player) 
			player->setHookObj(0); 
		m_nState = 3; 
		return;
	}

	if(m_nLifeTick-- == 0)
	{
		setNeedClear(); 
		player->setHookObj(0);  
		m_nState = 3; 
	}

	if(m_nHandItem != player->getCurToolID())
	{
		setNeedClear(); 
		player->setHookObj(0);  
		m_nState = 3; 
		return;
	}

	if(m_nState == 0 && player->getHookObj() == 0)
	{
		player->setHookObj(getObjId());
	}

	if(m_nState == 2)
	{
		 int blockid = m_pWorld->getBlockID(m_blockpos);
		 if(IsAirBlockID(blockid) || blockid == BLOCK_UNLOAD)
		 {
			m_nState = 1; 
			player->setHookObj(0);  
			return;
		 }

		WCoord playerpos = player->getLocoMotion()->getPosition();
		WCoord nowpos = getLocoMotion()->getPosition();
		nowpos -= playerpos;
		int len = (int)nowpos.lengthSquared();
		if(len < (100*100))
		{
		   setNeedClear();
		   m_nState = 3; 
		   player->setHookObj(0);  
		   return;
		}
		if(player->getHookObj() == 0)
		{
			m_nState = 3;
			setNeedClear();
			return;
		}

		//移动主人
		Rainbow::Vector3f motion;
		Rainbow::Vector3f dir = (getLocoMotion()->getPosition() - player->getPosition()).toVector3();
		dir  = MINIW::Normalize(dir);
		//PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
		motion = dir;
		motion *= 150.0f; 
		player->setMotionChange(motion, false, false, false);
	}
	else if(m_nState == 0)
	{
		 WCoord nowpos = getLocoMotion()->getPosition();
		 nowpos -= m_StartPos;
		 int len = (int)nowpos.lengthSquared();
		 if(len > (3000*3000))
		 {
			m_nState = 1;
		 }
	}
	else if(m_nState == 1)
	{
		 //移动发射物
		Rainbow::Vector3f &motion = getLocoMotion()->m_Motion;
		Rainbow::Vector3f dir = (getLocoMotion()->getPosition() - player->getPosition()).toVector3();
		dir  = MINIW::Normalize(dir);
		//PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
		motion = -dir;
		motion *= 150.0f; 
	}
}

void ClientActorHook::init(int itemid, ClientActor *shooter)
{
	m_ProjectileDef = GetDefManagerProxy()->getProjectileDef(itemid, true);
	m_ItemID = m_ProjectileDef->ID;
	if(itemid != m_ItemID)
	{
		auto tooDef = GetDefManagerProxy()->getToolDef(m_ItemID);
		if(tooDef)
			m_Durable = tooDef->Duration;
		else
			m_Durable = -1;
	}
#ifndef IWORLD_SERVER_BUILD
	char modelPath[128];
	sprintf(modelPath, "itemmods/%s/body2.omod", m_ProjectileDef->Model.c_str());
	m_EntityModel = g_BlockMtlMgr.getEntity(modelPath);
#endif
	ClientPlayer *player = dynamic_cast<ClientPlayer*>(shooter);
	if(player)
	m_nHandItem = player->getCurToolID();

	if (m_EntityModel && m_EntityModel->GetMainModel())
	{
		m_EntityModel->GetMainModel()->SetInstanceData(Rainbow::Vector4f(1.0f, 1.0f, 0, 0));
		if (m_pWorld)
		{
			m_EntityModel->AttachToScene(m_pWorld->getScene());
		}
	}
	// 啥意思???（m_InstanceData应该是shader里面用的一个Vector4f，涉及了一点儿shader计算）

	ProjectileLocoMotion *locmove = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	locmove->setBound((int)m_ProjectileDef->Bounds, (int)m_ProjectileDef->Bounds);
	locmove->m_yOffset = locmove->m_BoundHeight/2;
	locmove->m_Gravity = m_ProjectileDef->Gravity;
	locmove->m_SpeedDecay = m_ProjectileDef->SpeedDecay;
	locmove->m_TriggerCondition = (TRIGGER_CONDITION)m_ProjectileDef->TriggerCondition;

	m_ShootingActorID = 0;
	m_ImpactTimeMark = -1;
	m_AttachedEffect = 0;

	m_StartPos = WCoord(0,0,0);
	m_AttackPoints = 0;
	m_BuffAttackAdd = 0;
	m_KnockbackStrength = 0;
	m_BuffId = 0;

	// 投掷物发射前，处理技能相关
	processSkillOnInit(shooter);
}

void ClientActorHook::RenderCurve()
{

	ClientActor *actor = getShootingActor(); 
	if(!actor)
	{
		return;
	}

	WCoord handpos;
	if(actor == g_pPlayerCtrl && g_pPlayerCtrl->getCamera()->getMode() == CAMERA_FPS)
	{
		Rainbow::Vector3f offset(0.0f, 0.0f, 10.0f);
		if(g_pPlayerCtrl->m_CameraModel && g_pPlayerCtrl->m_CameraModel->m_HandModel)
		{
			Rainbow::Matrix4x4f tm = g_pPlayerCtrl->m_CameraModel->m_HandModel->GetAnchorWorldMatrix(101);
			handpos = tm.MultiplyPoint3(offset);
		}
		else
		{
			if(actor->getBody() != NULL)
			{
				handpos = actor->getBody()->getBindPointPos(101, &offset);
			}
			else
			{
				return;
			}
		}
	}
	else
	{
		Rainbow::Vector3f offset(0.0f, 0.0f, 50.0f);
		if(actor->getBody() != NULL)
		{
			handpos = actor->getBody()->getBindPointPos(101, &offset);
		}
		else
		{
			return;
		}
	}
	if (g_pPlayerCtrl && g_pPlayerCtrl->getCamera())
	{
		MINIW::CatmullRomCurve cc;
		Rainbow::Vector3f p1(0.0f, 0.0f, 0.0f);
		Rainbow::Vector3f p2 = (handpos - getLocoMotion()->getPosition()).toVector3();

		Rainbow::Vector3f p0, p3;
		p0 = p1*2.0f - p2;

		Rainbow::Vector3f dir0 = g_pPlayerCtrl->getCamera()->getLookDir();
		Rainbow::Vector3f dir3 = dir0;

		p3 = p2*2.0f - p1;

		cc.setControlPoints(p0, p1, p2, p3);
		cc.setNormals(dir0, dir3);
		m_pWorld->getRender()->getCurveRender()->addCurve(2, cc, getLocoMotion()->getPosition(), 6.0f, 3.0f);
	}
}

void ClientActorHook::enterWorld(World *pworld)
{
	ClientActor::enterWorld(pworld);

	pworld->addSpecialUnit(this);
	if (m_EntityModel)
	{
		m_EntityModel->AttachToScene(pworld->getScene());
	}
}

void ClientActorHook::leaveWorld(bool keep_inchunk)
{
	m_pWorld->removeSpecialUnit(this);

	ClientActor::leaveWorld(keep_inchunk);
	if (m_EntityModel)
	{
		m_EntityModel->DetachFromScene();
	}
}

void ClientActorHook::getViewBox(CollideAABB &box)
{	
	WCoord pos = getLocoMotion()->getPosition();
	WCoord dir = pos - m_StartPos;
	box.dim = dir;
	box.pos = m_StartPos; 
}