#include "ActorBodyAvatarComponent.h"
#include "BlockScene.h"
#include "WorldRender.h"
#include "ClientItem.h"
#include "SandBoxManager.h"

#include "ModelItemMesh.h"
#include "GameMode.h"
#include "OgreUtils.h"
#include "BlockMesh.h"
#include "special_blockid.h"
#include "PlayerControl.h"
#include "MpActorManager.h"
#include "GameCamera.h"
#include "CameraManager.h"

#include "RecordPkgManager.h"
#include "GameNetManager.h"
#include "CustomModelMgr.h"
#include "FullyCustomModelMgr.h"

#include "ActorVillager.h"
#include "ImportCustomModelMgr.h"
#include "Pkgs/PkgUtils.h"
#include "Entity/LegacySequenceMap.h"

#include "PlayerAttrib.h"
#include "Texture/LegacyTextureUtils.h"

#include "CarryComponent.h"
#include "RiddenComponent.h"
#include "ClientActorFuncWrapper.h"
#include "backpack.h"
#include "ActorGeniusMgr.h"
#include "ActorFishingVillager.h"
#include "BlockMaterialMgr.h"
#include "CustomModel.h"

#include "Optick/optick.h"
#include "Entity/ModelRenderer.h"
#include "Entity/ModelAnimationPlayer.h"
#include "PlayerLocoMotion.h"
#include "UGCEntity.h"
#include "Plugin.h"
#include "SandboxGameDef.h"
#include "UgcAssetMgr.h"


using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

namespace MNSandbox
{
	static int GetAvatarPartIDByItemId(int itemid)
	{
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
		if (tooldef)
		{
			return tooldef->AvatarPartID;
		}

		return 0;
	}

	static AVATAR_PART_TYPE GetAvatarPartTypeByItemType(int slot)
	{
		AVATAR_PART_TYPE avatarType = AVATAR_PART_TYPE::MAX;
		if (slot == 8)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD;
		else if (slot == 9)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST;
		else if (slot == 10)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_LEGGING;
		else if (slot == 11)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_SHOE;
		else if (slot == 16)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_PIFENG;
		else if (slot == 35)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD_LINING;
		else if (slot == 36)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST_LINING;
		else if (slot == 37)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_LEGGING_LINING;
		else if (slot == 38)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_SHOE_LINING;
		else
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_WEAPON;

		return avatarType;
	}

	static AVATAR_PART_TYPE GetAvatarPartTypeByEquipAvatarType(AVATAR_PART_TYPE equipType)
	{
		AVATAR_PART_TYPE avatarType = AVATAR_PART_TYPE::MAX;
		if (equipType == AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD)
			avatarType = AVATAR_PART_TYPE::HEAD;
		else if (equipType == AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST)
			avatarType = AVATAR_PART_TYPE::JACKET;
		else if (equipType == AVATAR_PART_TYPE::AVATAR_EQUIP_LEGGING)
			avatarType = AVATAR_PART_TYPE::TROUSERS;
		else if (equipType == AVATAR_PART_TYPE::AVATAR_EQUIP_SHOE)
			avatarType = AVATAR_PART_TYPE::SHOE;
		else if (equipType == AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD_LINING)
			avatarType = AVATAR_PART_TYPE::HEAD;
		else if (equipType == AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST_LINING)
			avatarType = AVATAR_PART_TYPE::JACKET;
		else if (equipType == AVATAR_PART_TYPE::AVATAR_EQUIP_LEGGING_LINING)
			avatarType = AVATAR_PART_TYPE::TROUSERS;
		else if (equipType == AVATAR_PART_TYPE::AVATAR_EQUIP_SHOE_LINING)
			avatarType = AVATAR_PART_TYPE::SHOE;
		else if (equipType == AVATAR_PART_TYPE::AVATAR_EQUIP_PIFENG)
			avatarType = AVATAR_PART_TYPE::BACK_ORNAMENT;
		else if (equipType == AVATAR_PART_TYPE::AVATAR_EQUIP_WEAPON)
			avatarType = AVATAR_PART_TYPE::HAND_ORNAMENT;

		return avatarType;
	}

	// static AVATAR_PART_TYPE GetEquipAvatarTypeByAvatarType(AVATAR_PART_TYPE equipType)
	// {
	// 	AVATAR_PART_TYPE avatarType = AVATAR_PART_TYPE::MAX;
	// 	if (equipType == AVATAR_PART_TYPE::HEAD)
	// 		avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD;
	// 	else if (equipType == AVATAR_PART_TYPE::JACKET)
	// 		avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST;
	// 	else if (equipType == AVATAR_PART_TYPE::TROUSERS)
	// 		avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_LEGGING;
	// 	else if (equipType == AVATAR_PART_TYPE::SHOE)
	// 		avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_SHOE;
	// 	else if (equipType == AVATAR_PART_TYPE::BACK_ORNAMENT)
	// 		avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_PIFENG;
	// 	else if (equipType == AVATAR_PART_TYPE::HAND_ORNAMENT)
	// 		avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_WEAPON;

	// 	return avatarType;
	// }

	static AVATAR_PART_TYPE GetAvatarPartTypeBySlot(EQUIP_SLOT_TYPE slot)
	{
		AVATAR_PART_TYPE avatarType = AVATAR_PART_TYPE::MAX;
		if (slot == EQUIP_SLOT_TYPE::EQUIP_HEAD)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_BREAST)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_LEGGING)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_LEGGING;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_SHOE)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_SHOE;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_PIFENG)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_PIFENG;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_HEAD_LINING)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD_LINING;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_BREAST_LINING)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST_LINING;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_LEGGING_LINING)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_LEGGING_LINING;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_SHOE_LINING)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_SHOE_LINING;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_WEAPON)
			avatarType = AVATAR_PART_TYPE::AVATAR_EQUIP_WEAPON;

		return avatarType;
	}

	static AVATAR_PART_TYPE GetOriginAvatarPartTypeBySlotType(EQUIP_SLOT_TYPE slot)
	{
		AVATAR_PART_TYPE avatarType = AVATAR_PART_TYPE::MAX;
		if (slot == EQUIP_SLOT_TYPE::EQUIP_HEAD)
			avatarType = AVATAR_PART_TYPE::HEAD;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_BREAST)
			avatarType = AVATAR_PART_TYPE::JACKET;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_LEGGING)
			avatarType = AVATAR_PART_TYPE::TROUSERS;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_SHOE)
			avatarType = AVATAR_PART_TYPE::SHOE;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_PIFENG)
			avatarType = AVATAR_PART_TYPE::BACK_ORNAMENT;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_HEAD_LINING)
			avatarType = AVATAR_PART_TYPE::HEAD;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_BREAST_LINING)
			avatarType = AVATAR_PART_TYPE::JACKET;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_LEGGING_LINING)
			avatarType = AVATAR_PART_TYPE::TROUSERS;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_SHOE_LINING)
			avatarType = AVATAR_PART_TYPE::SHOE;
		else if (slot == EQUIP_SLOT_TYPE::EQUIP_WEAPON)
			avatarType = AVATAR_PART_TYPE::HAND_ORNAMENT;

		return avatarType;
	}


	static AVATAR_PART_TYPE GetAvatarPartTypeByItemId(int itemid)
	{
		AVATAR_PART_TYPE slot = AVATAR_PART_TYPE::MAX;
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
		if (tooldef)
		{
			slot = GetAvatarPartTypeByItemType(tooldef->Type);
		}

		return slot;
	}

	static EQUIP_SLOT_TYPE GetEquipSlotTypeByItemId(int itemid)
	{
		EQUIP_SLOT_TYPE ret = EQUIP_SLOT_TYPE::MAX_EQUIP_SLOTS;

		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
		if (tooldef)
		{
			int slot = tooldef->Type;
			if (slot == 8)
				ret = EQUIP_SLOT_TYPE::EQUIP_HEAD;
			else if (slot == 9)
				ret = EQUIP_SLOT_TYPE::EQUIP_BREAST;
			else if (slot == 10)
				ret = EQUIP_SLOT_TYPE::EQUIP_LEGGING;
			else if (slot == 11)
				ret = EQUIP_SLOT_TYPE::EQUIP_SHOE;
			else if (slot == 16)
				ret = EQUIP_SLOT_TYPE::EQUIP_PIFENG;
			else if (slot == 35)
				ret = EQUIP_SLOT_TYPE::EQUIP_HEAD_LINING;
			else if (slot == 36)
				ret = EQUIP_SLOT_TYPE::EQUIP_BREAST_LINING;
			else if (slot == 37)
				ret = EQUIP_SLOT_TYPE::EQUIP_LEGGING_LINING;
			else if (slot == 38)
				ret = EQUIP_SLOT_TYPE::EQUIP_SHOE_LINING;
			else
				ret = EQUIP_SLOT_TYPE::EQUIP_WEAPON;
		}

		return ret;
	}
}

ActorBodyAvatarComponent::ActorBodyAvatarComponent(ActorBody* actorBody) : m_ActorBody(actorBody)
{
	m_AvatarSkinModeResCount = 0;
	for (int i = 0; i < AVATAR_PART_TYPE::MAX; i++)
	{
		m_AvatarSkinPointIDs[i] = 0;
		m_AvatarSkinModelRes[i] = nullptr;
		m_AvatarSkinModel[i] = NULL;
		m_AvatarSkinEffect[i] = NULL;
	}

	m_CacheAvatarModelParts.clear_dealloc();

	m_bHideEquipAvatar = false;
	m_AvatarPartShiedlMap.clear();
	resetAvatarPartList();

	m_ref = SANDBOX_NEW(MNSandbox::Ref);
	m_ugcModelLoader = SANDBOX_NEW(UGCModelLoader, m_ref.get());
	m_ugcModeResLoadCount = 0;
}

ActorBodyAvatarComponent::~ActorBodyAvatarComponent()
{
	SANDBOX_DELETE(m_ugcModelLoader);
	SANDBOX_RELEASE(m_ref);

}

bool ActorBodyAvatarComponent::GetAvatarPartModelShow(int type)
{
	assert(type >= AVATAR_PART_TYPE::BODY && type < AVATAR_PART_TYPE::MAX);
	if (type >= AVATAR_PART_TYPE::BODY && type < AVATAR_PART_TYPE::MAX)
	{
		return m_iAvatarPartList[type] > 0;
	}

	return false;
}

bool ActorBodyAvatarComponent::GetOriginAvatarPartModelShow(int type)
{
	assert(type >= AVATAR_PART_TYPE::BODY && type < AVATAR_PART_TYPE::MAX);
	if (type >= AVATAR_PART_TYPE::BODY && type < AVATAR_PART_TYPE::MAX)
	{
		return m_bAvatarPartListStatu[type];
	}

	return false;
}

void ActorBodyAvatarComponent::resetAvatarPartList()
{
	m_iAvatarPartList[AVATAR_PART_TYPE::BODY] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::HEAD] = 2;
	m_iAvatarPartList[AVATAR_PART_TYPE::FACE] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::FACE_ORNAMENT] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::JACKET] = 3;
	m_iAvatarPartList[AVATAR_PART_TYPE::HAND_ORNAMENT] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::TROUSERS] = 4;
	m_iAvatarPartList[AVATAR_PART_TYPE::SHOE] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::BACK_ORNAMENT] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::FOOTPRINT] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::SKIN] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::RIGHT_HAND] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::RIGHT_SHOE] = 0;

	m_iAvatarPartList[AVATAR_PART_TYPE::HEAD_EFFECT] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::FACE_EFFECT] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::WHOLE_BODY_EFFECT] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::HAND_EFFECT] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::TRAILING_EFFECT] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::BG_EFFECT] = 0;

	m_iAvatarPartList[AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::AVATAR_EQUIP_LEGGING] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::AVATAR_EQUIP_SHOE] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::AVATAR_EQUIP_PIFENG] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD_LINING] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST_LINING] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::AVATAR_EQUIP_LEGGING_LINING] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::AVATAR_EQUIP_SHOE_LINING] = 0;
	m_iAvatarPartList[AVATAR_PART_TYPE::AVATAR_EQUIP_WEAPON] = 0;

	for (int i = AVATAR_PART_TYPE::BODY; i < AVATAR_PART_TYPE::MAX; i++)
	{
		m_bAvatarPartListStatu[i] = GetAvatarPartModelShow(i);
	}

	m_AvatarPartShiedlMap.clear();
}

bool ActorBodyAvatarComponent::hasCacheAvatar()
{
	return m_CacheAvatarParts.size() > 0 || m_CacheAvatarModelParts.size() > 0;
}

void ActorBodyAvatarComponent::AddCacheAvatarPart(int avatarModel, int index)
{
#ifndef IWORLD_SERVER_BUILD		
	bool isExist = false;
	for (size_t idx = 0; idx < m_CacheAvatarParts.size(); idx++)
	{
		if (m_CacheAvatarParts[idx].m_Index == index && m_CacheAvatarParts[idx].m_Avatarmodel == avatarModel)
		{
			isExist = true;
			break;
		}
	}
	if (!isExist)
	{
		auto& part = m_CacheAvatarParts.emplace_back();
		part.m_Index = index;
		part.m_Avatarmodel = avatarModel;
	}
#endif		
}

void ActorBodyAvatarComponent::AddCacheAvatarModelPart(int avatarModel, int index,int QualityLevelColor, Rainbow::SharePtr<Rainbow::Asset> partModle, Rainbow::SharePtr<Rainbow::Texture2D> partBaseTex,
	Rainbow::SharePtr<Rainbow::Texture2D> partMaskTex)
{
#ifndef IWORLD_SERVER_BUILD		
	bool isExist = false;
	int curIndex = 0;
	for (size_t idx = 0; idx < m_CacheAvatarModelParts.size(); idx++)
	{
		if (m_CacheAvatarModelParts[idx].m_Index == index)
		{
			curIndex = idx;
			isExist = true;
			break;
		}
	}

	if (!isExist)
	{
		auto& part = m_CacheAvatarModelParts.emplace_back();
		part.m_Index = index;
		part.m_Avatarmodel = avatarModel;
		part.m_PartModle = partModle;
		part.QualityLevelColor = QualityLevelColor;
		part.m_PartBaseTex = partBaseTex;
		part.m_PartMaskTex = partMaskTex;
	}
	else
	{
		m_CacheAvatarModelParts[curIndex].m_Index = index;
		m_CacheAvatarModelParts[curIndex].m_Avatarmodel = avatarModel;
		m_CacheAvatarModelParts[curIndex].m_PartModle = partModle;
		m_CacheAvatarModelParts[curIndex].QualityLevelColor = QualityLevelColor;
		m_CacheAvatarModelParts[curIndex].m_PartBaseTex = partBaseTex;
		m_CacheAvatarModelParts[curIndex].m_PartMaskTex = partMaskTex;
	}
#endif		
}

void ActorBodyAvatarComponent::UpdateCacheAvatarParts()
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_ActorBody->m_Entity == nullptr) return;
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (model == nullptr) return;

	for (size_t idx = 0; idx < m_CacheAvatarParts.size(); idx++)
	{
		addAvatarPartModel(m_CacheAvatarParts[idx].m_Avatarmodel, m_CacheAvatarParts[idx].m_Index,0);//@soc2024
	}

	CleanCacheAvatarParts();
#endif		
}

void ActorBodyAvatarComponent::CleanCacheAvatarParts()
{
	m_CacheAvatarParts.clear_dealloc();
}


void ActorBodyAvatarComponent::CleanCacheAvatarModelParts()
{
	m_CacheAvatarModelParts.clear_dealloc();
}

void ActorBodyAvatarComponent::loadAvatarPartTexAsyn(int modelID, Rainbow::SharePtr<Rainbow::Texture2D>& baseTex, Rainbow::SharePtr<Rainbow::Texture2D>& maskTex)
{
	char basepath[512] = { 0 };
	char maskpath[512] = { 0 };

	if (modelID > 5)
	{
		snprintf(basepath, sizeof(basepath), "entity/avatar/1000_%d/%d.png_", modelID, modelID);
		snprintf(maskpath, sizeof(maskpath), "entity/avatar/1000_%d/mask_%d.png_", modelID, modelID);
	}
	else if (modelID > 0)
	{
		sprintf(basepath, "entity/player/player12/%d.png_", modelID);
		sprintf(maskpath, "entity/player/player12/mask_%d.png_", modelID);
	}

	baseTex = PkgUtils::LoadTextureReadAbleAsync<Texture2D>(basepath);
	maskTex = PkgUtils::LoadTextureReadAbleAsync<Texture2D>(maskpath);
	maskTex->SetIsUnreloadable(true);
}

bool ActorBodyAvatarComponent::HasAvatarModelPartTexLoaded(int avatarModel, int index)
{
	bool isExist = false;
	int curIndex = 0;
	for (size_t idx = 0; idx < m_CacheAvatarModelParts.size(); idx++)
	{
		if (m_CacheAvatarModelParts[idx].m_Index == index
			&& m_CacheAvatarModelParts[idx].m_Avatarmodel == avatarModel)
		{
			curIndex = idx;
			isExist = true;
			break;
		}
	}

	if (isExist)
	{
		return m_CacheAvatarModelParts[curIndex].m_PartBaseTex.IsValid()
			&& m_CacheAvatarModelParts[curIndex].m_PartMaskTex.IsValid();
	}

	return false;
}

bool ActorBodyAvatarComponent::addAvatarPartModel(int avatarmodel, int index, int QualityLevelColor,bool isAsync, int pointId, bool isItem)
{
#ifdef IWORLD_SERVER_BUILD
	return true;
#else	
	OPTICK_EVENT();
	LogStringMsg("addAvatarPartModel:%d,%d", avatarmodel, index);
	//判断不是avt模型 而是皮肤模型的时候
	int skin_id = (m_ActorBody->isPlayer()) ? m_ActorBody->getSkinID() : 0;
	bool isSkinModel = (skin_id > 0);
	bool isBasicModel = avatarmodel == 2 || avatarmodel == 3 || avatarmodel == 4;
	if (isSkinModel && isBasicModel)
	{
		return false;
	}
	if (avatarmodel < 0)
	{
		return false;
	}

	if (index < AVATAR_PART_TYPE::BODY || index >= AVATAR_PART_TYPE::MAX)
	{
		return false;
	}

	if (index == 9)
	{
		m_iAvatarPartList[AVATAR_PART_TYPE::FOOTPRINT] = avatarmodel;
		return true;
	}

	if (m_ActorBody->m_LoadModelData && m_ActorBody->m_LoadModelData->IsAsync() && !m_ActorBody->isAvatarModel())
	{
		//wait mainmodel load finish!
		AddCacheAvatarPart(avatarmodel, index);
		LogStringMsg("addAvatarPartModel return false AddCacheAvatarPart");
		return false;
	}


	//LOG_INFO("addAvatarPartModel:%p,%d,%d",this,avatarmodel,index);
	if (GetRecordPkgManager().isRecordStarted() && m_ActorBody->m_OwnerActor)
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_ActorBody->m_OwnerActor);
		if (player && g_pPlayerCtrl)
		{
			PB_PlayerAddAvartarHC addAvartarHC;
			addAvartarHC.set_uin(player->getUin());
			addAvartarHC.set_avatarmodel(avatarmodel);
			addAvartarHC.set_index(index);
			GetGameNetManagerPtr()->sendToClient(g_pPlayerCtrl->getUin(), PB_PLAYER_ADDAVARTAR_HC, addAvartarHC);
		}
	}

	int checkcode = 1;
	char modelPath[512] = { 0 };
	bool needalpha = avatarmodel == 26;

	SharePtr<Rainbow::Asset>		partModle = nullptr;
	SharePtr<Rainbow::Texture2D>	baseTex = nullptr;
	SharePtr<Rainbow::Texture2D>	maskTex = nullptr;

	if (m_ActorBody->m_LoadModelData && m_ActorBody->m_LoadModelData->IsAsync() && m_ActorBody->m_Entity && m_ActorBody->m_Entity->GetMainModel() == nullptr)
	{
		m_ActorBody->SyncModelData();
	}

	if (!m_ActorBody->m_Entity)
	{
		return false;
	}
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (model == NULL)
	{
		return false;
	}

	bool isNewModel = false;
	if (model->IsKindOf<ModelNew>())
	{
		isNewModel = true;
	}

	// 换脸和肤色的的情况下
	if (AVATAR_PART_TYPE::FACE == index || AVATAR_PART_TYPE::SKIN == index)
	{
		// 检查下载是否完成
		MINIW::ScriptVM::game()->callFunction("AvtCallTable", "iiu[ActorBody]>i", 1, avatarmodel, m_ActorBody, &checkcode);
		if (checkcode != 0)
		{
			return false;
		}

		if (!isItem)
		{
			m_iAvatarPartList[index] = avatarmodel;
			m_bAvatarPartListStatu[index] = avatarmodel > 0;
		}
	}
	else
	{
		if (avatarmodel <= 5)
		{
			sprintf(modelPath, "entity/player/player12/%d.prefab", avatarmodel);
			SharePtr<Rainbow::Prefab> prefab = isAsync ? GetAssetManager().LoadAssetAsync<Prefab>(modelPath) : GetAssetManager().LoadAsset<Prefab>(modelPath);
			if (prefab)
			{
				partModle = prefab.CastTo<Asset>();
			}

			if (!partModle)
			{
				LogStringMsg("addAvatarPartModel return false avatarmodel <= 5 and partModle is nil");
			}
			if (!isItem)
			{
				m_iAvatarPartList[index] = avatarmodel;
				m_bAvatarPartListStatu[index] = avatarmodel > 0;
			}
		}
		else
		{
			// 只有同步加载需要检查资源是否提前下载，异步加载底层会自动处理下载流程
			if (!isAsync)
			{
				MINIW::ScriptVM::game()->callFunction("AvtCallTable", "iiu[ActorBody]>i", 1, avatarmodel, m_ActorBody, &checkcode);
				if (checkcode != 0)
				{
					return false;
				}
			}

			//使用的是皮肤模型的时候先判断是否有可使用的挂点部件
			if (!isItem)
			{
				m_iAvatarPartList[index] = avatarmodel;
				m_bAvatarPartListStatu[index] = avatarmodel > 0;
			}

			if (isSkinModel && equipAvatarSkin(avatarmodel, index, pointId)) {
				checkAvatarPartEffect(avatarmodel, index);
				return true;
			}
			if (GetAvatarPartModelShow(AVATAR_PART_TYPE::HEAD_EFFECT))
			{
				int effectHeadHight = 0;
				MINIW::ScriptVM::game()->callFunction("GetHeatAvtEffectHight", "i>i", m_iAvatarPartList[AVATAR_PART_TYPE::HEAD_EFFECT], &effectHeadHight);
				m_ActorBody->m_HeadEffectObjHeight = effectHeadHight;
			}

			// 所有的新avatar特效资源强制异步加载
			if (index >= AVATAR_PART_TYPE::RIGHT_HAND && index <= AVATAR_PART_TYPE::BG_EFFECT)
			{
				isAsync = true;
			}

			snprintf(modelPath, sizeof(modelPath), "entity/avatar/1000_%d/%d.prefab", avatarmodel, avatarmodel);
			SharePtr<Rainbow::Prefab> prefab = isAsync ? GetAssetManager().LoadAssetAsync<Prefab>(modelPath) : GetAssetManager().LoadAsset<Prefab>(modelPath);
			if (prefab)
			{
				partModle = prefab.CastTo<Asset>();
			}
			else
			{
				//没有可使用的部件模型资源 再判断是否有可使用挂点的部件资源
				if (equipAvatarSkin(avatarmodel, index, pointId)) {
					checkAvatarPartEffect(avatarmodel, index);
					return true;
				}

				// 只有同步加载需要检查资源是否提前下载，异步加载底层会自动处理下载流程
				if (!isAsync)
				{
					MINIW::ScriptVM::game()->callFunction("AvtCallTable", "ii", 2, avatarmodel);
					LogStringMsg("addAvatarPartModel return false avatarmodel > 5 and partModle is nil, modelPathStr=%s", modelPath);
				}

				return false;
			}

			//自带全身特效
			if (checkWholeBodyEffect())
			{
				if (m_ActorBody->isPlayer())
				{
					RoleSkinDef* skinDef = GetDefManagerProxy()->getRoleSkinDef(m_ActorBody->getSkinID());
					if (skinDef && m_ActorBody->m_Entity)
					{
						m_ActorBody->m_Entity->StopMotion(skinDef->Effect);
					}
				}
			}
		}
	}

	LogStringMsg("addAvatarPartModel:%d,%d,goUID:%d", avatarmodel, index, model->GetGameObject()->GetUID());

	AddCacheAvatarModelPart(avatarmodel, index, QualityLevelColor,partModle,baseTex, maskTex);

	if (!isAsync) UpdateCacheAvatarModelParts(true);

	return true;

#endif	
}

bool ActorBodyAvatarComponent::addAvatarPartModelByPath(const Rainbow::FixedString& path, int index, bool isAsync /* = false */)
{
#ifdef IWORLD_SERVER_BUILD
	return true;
#else	
	OPTICK_EVENT();
	//判断不是avt模型 而是皮肤模型的时候
	int skin_id = (m_ActorBody->m_PlayerIndex > 0) ? m_ActorBody->getSkinID() : 0;
	bool isSkinModel = (skin_id > 0);
	if (isSkinModel)
	{
		return false;
	}

	if (index < AVATAR_PART_TYPE::BODY || index >= AVATAR_PART_TYPE::MAX)
	{
		return false;
	}

	if (index == 9)
	{
		return true;
	}

	if (2 == index)
	{
		return false;
	}

	int checkcode = 1;
	char modelPath[512] = { 0 };

	SharePtr<Rainbow::Asset>	 partModle;

	if (m_ActorBody->m_LoadModelData && m_ActorBody->m_LoadModelData->IsAsync() && m_ActorBody->m_Entity && m_ActorBody->m_Entity->GetMainModel() == nullptr)
	{
		m_ActorBody->SyncModelData();
	}

	if (!m_ActorBody->m_Entity)
	{
		return false;
	}
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (model == NULL)
	{
		return false;
	}

	bool isNewModel = false;
	if (model->IsKindOf<ModelNew>())
	{
		isNewModel = true;
	}

	strcpy(modelPath, path.c_str());

	SharePtr<Rainbow::Prefab> prefab = nullptr;
	FileInfoResult* assetResult = nullptr;
	GetFileManager().CheckAndGetInfo(modelPath, assetResult);
	if (assetResult && assetResult->isExist)
	{
		// 所有的翻新avatar部件资源强制异步加载
		if (index >= AVATAR_PART_TYPE::RIGHT_HAND && index <= AVATAR_PART_TYPE::BG_EFFECT)
		{
			isAsync = true;
		}

		prefab = isAsync ? GetAssetManager().LoadAssetAsync<Prefab>(modelPath) : GetAssetManager().LoadAsset<Prefab>(modelPath);

		if (prefab)
		{
			//partModle = prefab.CastTo<Asset>();
			static_cast<ModelNew*>(model)->AddAvatar(index, prefab, NULL, NULL);
			model->ShowAvatar(index, true);
		}
	}
	else
	{
		return false;
	}

	return true;

#endif	
}

void ActorBodyAvatarComponent::UpdateCacheAvatarPartColor(int idx, int index, int avatarmodel)
{
	if (m_ActorBody->getEntity() == nullptr) return;

	// 处理avatar部件调色
	if (m_CacheAvatarModelParts[idx].m_PartBaseTex && m_CacheAvatarModelParts[idx].m_PartBaseTex->IsLoaded()
		&& m_CacheAvatarModelParts[idx].m_PartMaskTex && m_CacheAvatarModelParts[idx].m_PartMaskTex->IsLoaded())
	{
		Rainbow::Model* model = m_ActorBody->getEntity()->GetMainModel();
		if (model)
		{
			SharePtr<Texture2D> avatarColorTex = Model::GetAvatarPartColorTexture(m_CacheAvatarModelParts[idx].m_PartBaseTex, m_CacheAvatarModelParts[idx].m_PartMaskTex, 3, m_CacheAvatarModelParts[idx].m_PartBlock, m_CacheAvatarModelParts[idx].m_PartColor);
			if (avatarColorTex)
			{
				model->SetAvatarPartTextue((Model::Avatar_Parts)index, avatarColorTex);
			}
		}
	}
}

// 异步换装支持换肤色和换脸
void ActorBodyAvatarComponent::UpdateCacheAvatarModelParts(bool forceload)
{
	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD		
	if (m_CacheAvatarModelParts.size() == 0)
	{
		return;
	}

	if (m_ActorBody->getEntity() == nullptr) return;
	Rainbow::Model* model = m_ActorBody->getEntity()->GetMainModel();
	if (model == nullptr) return;

	bool isNewModel = false;
	if (model->IsKindOf<ModelNew>())
	{
		isNewModel = true;
	}

	bool needReset = false;
	bool exchangeface = false;

	for (int idx = 0; idx < m_CacheAvatarModelParts.size(); idx++)
	{
		// 如果有调色贴图，同时检查模型本身和调色贴图资源是否异步加载完成
		if ((m_CacheAvatarModelParts[idx].m_PartModle.IsValid() && !m_CacheAvatarModelParts[idx].m_PartModle->IsLoaded())
			|| (m_CacheAvatarModelParts[idx].m_PartBaseTex.IsValid() && !m_CacheAvatarModelParts[idx].m_PartBaseTex->IsLoaded())
			|| (m_CacheAvatarModelParts[idx].m_PartMaskTex.IsValid() && !m_CacheAvatarModelParts[idx].m_PartMaskTex->IsLoaded()))
		{
			continue;
		}

		{
			int index = m_CacheAvatarModelParts[idx].m_Index;
			int avatarmodel = m_CacheAvatarModelParts[idx].m_Avatarmodel;
			bool bModelShow = GetAvatarPartModelShow(index) || avatarmodel <= 5;
			bool bOrigneShow = GetOriginAvatarPartModelShow(index);

			if (bOrigneShow || bModelShow)
			{
				// 兼容异步换脸和肤色
				if (AVATAR_PART_TYPE::FACE == index || AVATAR_PART_TYPE::SKIN == index)
				{
					// 单帧换脸和肤色只用调用一次即可
					if (!exchangeface)
					{
						// 接口兼容换脸
						if (AVATAR_PART_TYPE::FACE == index)
						{
							exchangePartFace(avatarmodel, AVATAR_PART_TYPE::FACE, true, "", getAvatarListModelId(AVATAR_PART_TYPE::SKIN));
						}
						// 接口兼容换肤色
						else if (AVATAR_PART_TYPE::SKIN == index)
						{
							exchangePartFace(getAvatarListModelId(AVATAR_PART_TYPE::FACE), AVATAR_PART_TYPE::FACE, true, "", avatarmodel);
						}
						exchangeface = true;
					}
				}
				else
				{
					SharePtr<Rainbow::Asset> partModle = m_CacheAvatarModelParts[idx].m_PartModle;
					// 有模型资源的情况下
					if (partModle)
					{
						if (isNewModel && partModle->IsKindOf<Prefab>())
						{
							if (model->IsKindOf<ModelNew>())  //只有新资源用新prefab
							{
								static_cast<ModelNew*>(model)->AddAvatar(index, partModle.CastTo<Prefab>(), NULL, NULL);
							}
						}
						else
						{
							if (partModle->IsKindOf<ModelData>())
							{
								model->AddAvatar(index, partModle.CastTo<ModelData>(), NULL, NULL);
							}
						}

						if (m_ActorBody->getIsInUI() || !m_ActorBody->isEffectAvatar(index))
						{
							model->ShowAvatar(index, bModelShow);
						}

						checkAvatarPartEffect(avatarmodel, index);

						UpdateAvatarShiedPartsModel(index);

						// 装备的优先级高于装扮，处理装备显示挤掉装扮的情况
						if (GetOriginAvatarPartModelShow(index))
						{
							UpdateEquipAvatarPartsShieldHide((AVATAR_PART_TYPE)index);
						}

						needReset = true;
						partModle = nullptr;
					}

					UpdateCacheAvatarPartColor(idx, index, avatarmodel);
				}
			}

			m_CacheAvatarModelParts.removeAt(idx);
			idx--;
		}
	}

	if (needReset)
	{
		m_ActorBody->getEntity()->SetInstanceAmbient(m_ActorBody->getIsInUI() ? ColourValue::White : ColourValue::ZERO);
		if (m_ActorBody->getIsInUI())
		{
			m_ActorBody->getEntity()->SetInstanceData(Vector4f::one, true);
		}
		m_ActorBody->getEntity()->ResetXrayEnable();
	}

#endif		
}

void ActorBodyAvatarComponent::setAvatarPartModelShow(int index, bool bShow)
{
#ifndef IWORLD_SERVER_BUILD	
	auto m_Entity = m_ActorBody->getEntity();
	if (m_Entity && m_Entity->GetMainModel())
	{
		m_Entity->GetMainModel()->ShowAvatar(index, bShow);

		if (!bShow)
		{
			takeOffAvatarSkin(index);
		}

		//记录部件id 隐藏的时候转负数
		if (index < AVATAR_PART_TYPE::MAX)
		{
			m_iAvatarPartList[index] = bShow ? abs(m_iAvatarPartList[index]) : abs(m_iAvatarPartList[index]) * -1;
		}

		if (!bShow)
		{
			if (!m_ActorBody->checkWholeBodyEffect())
			{
				if (m_ActorBody->isPlayer()) {
					RoleSkinDef* skinDef = GetDefManagerProxy()->getRoleSkinDef(m_ActorBody->getSkinID());
					if (skinDef && m_Entity)
					{
						m_Entity->PlayMotion(skinDef->Effect, false, 0);
					}
				}
			}
		}
	}
#endif
}

void ActorBodyAvatarComponent::hideAvatarPartModel(int index, bool isItem)
{
#ifndef IWORLD_SERVER_BUILD	
	if (m_ActorBody->m_Entity && m_ActorBody->m_Entity->GetMainModel())
	{
		m_ActorBody->m_Entity->GetMainModel()->ShowAvatar(index, false);

		takeOffAvatarSkin(index);

		//记录部件id 隐藏的时候转负数
		if (index < AVATAR_PART_TYPE::MAX && !isItem)
		{
			m_iAvatarPartList[index] = abs(m_iAvatarPartList[index]) * -1;
			m_bAvatarPartListStatu[index] = false;
		}

		if (!checkWholeBodyEffect())
		{
			if (m_ActorBody->m_PlayerIndex > 0) {
				RoleSkinDef* skinDef = GetDefManagerProxy()->getRoleSkinDef(m_ActorBody->getSkinID());
				if (skinDef && m_ActorBody->m_Entity)
				{
					m_ActorBody->m_Entity->PlayMotion(skinDef->Effect, false, 0);
				}
			}
		}
	}
#endif
}

void ActorBodyAvatarComponent::SetAvatarModelPartColor(int avatarModel, int index,
	float r, float g, float b, int block,
	Rainbow::SharePtr<Rainbow::Texture2D> partBaseTex,
	Rainbow::SharePtr<Rainbow::Texture2D> partMaskTex)
{
	if (block < 0 || block >= 3)
	{
		return;
	}

#ifndef IWORLD_SERVER_BUILD		
	bool isExist = false;
	int curIndex = 0;
	for (size_t idx = 0; idx < m_CacheAvatarModelParts.size(); idx++)
	{
		if (m_CacheAvatarModelParts[idx].m_Index == index
			&& m_CacheAvatarModelParts[idx].m_Avatarmodel == avatarModel)
		{
			curIndex = idx;
			isExist = true;
			break;
		}
	}

	if (!isExist)
	{
		auto& part = m_CacheAvatarModelParts.emplace_back();
		part.m_Index = index;
		part.m_Avatarmodel = avatarModel;
		part.m_PartModle = nullptr;
		part.m_PartColor[block] = ColourValue(r, g, b);
		part.m_PartBlock[block] = block;

		if (partBaseTex) part.m_PartBaseTex = partBaseTex;
		if (partMaskTex) part.m_PartMaskTex = partMaskTex;
	}
	else
	{
		m_CacheAvatarModelParts[curIndex].m_PartColor[block] = ColourValue(r, g, b);
		m_CacheAvatarModelParts[curIndex].m_PartBlock[block] = block;

		if (partBaseTex) m_CacheAvatarModelParts[curIndex].m_PartBaseTex = partBaseTex;
		if (partMaskTex) m_CacheAvatarModelParts[curIndex].m_PartMaskTex = partMaskTex;
	}

#endif		
}

bool ActorBodyAvatarComponent::alterAvatarPartColorAync(int modelID, int partID, float r, float g, float b, int block)
{
	SharePtr<Rainbow::Texture2D>	baseTex = nullptr;
	SharePtr<Rainbow::Texture2D>	maskTex = nullptr;

	if (modelID > 0 && !HasAvatarModelPartTexLoaded(modelID, partID))
	{
		loadAvatarPartTexAsyn(modelID, baseTex, maskTex);
	}

	SetAvatarModelPartColor(modelID, partID,
		r, g, b, block,
		baseTex,
		maskTex);

	return true;
}

bool ActorBodyAvatarComponent::apllyAvatarPartColor(int modelID, int partID)
{
	int curIndex = -1;
	for (size_t idx = 0; idx < m_CacheAvatarModelParts.size(); idx++)
	{
		if (m_CacheAvatarModelParts[idx].m_Index == partID
			&& m_CacheAvatarModelParts[idx].m_Avatarmodel == modelID)
		{
			curIndex = idx;
			break;
		}
	}

	if (curIndex != -1)
	{
		// 这里要保证异步加载贴图成功
		if ((m_CacheAvatarModelParts[curIndex].m_PartBaseTex.IsValid() && !m_CacheAvatarModelParts[curIndex].m_PartBaseTex->IsLoaded())
			|| (m_CacheAvatarModelParts[curIndex].m_PartMaskTex.IsValid() && !m_CacheAvatarModelParts[curIndex].m_PartMaskTex->IsLoaded()))
		{
			return false;
		}

		UpdateCacheAvatarModelParts();
		return true;
	}

	return false;
}

bool ActorBodyAvatarComponent::SetAvatarBodyAndFaceTexture(Rainbow::Model* model, const char* sBodyPath, const char* sFacePath)
{
	if (!model) return false;

	SharePtr<Texture2D> basePtex = GetAssetManager().LoadAssetAsync<Texture2D>(sBodyPath);
	if (!basePtex)
	{
		return false;
	}

	SharePtr<Texture2D> eyeTex = GetAssetManager().LoadAssetAsync<Texture2D>(sFacePath);
	if (!eyeTex)
	{
		return false;
	}
	// 脸和身体的图分开
	model->SetTexture("g_AvatarFaceTex", eyeTex, "1");
	model->SetTexture("g_DiffuseTex", basePtex, "1");
	// 单独设置耳朵
	model->SetTextureAll("g_DiffuseTex", basePtex, "part1");
	return true;
}

bool ActorBodyAvatarComponent::SetAvatarBodyAndFace(Rainbow::Model* model, Rainbow::SharePtr<Rainbow::Texture2D> bodyTex, Rainbow::SharePtr<Rainbow::Texture2D> faceTex)
{
	if (!model) return false;

	if (!bodyTex)
	{
		return false;
	}

	if (!faceTex)
	{
		return false;
	}

	// 脸和身体的图分开
	model->SetTexture("g_AvatarFaceTex", faceTex, "1");
	model->SetTexture("g_DiffuseTex", bodyTex, "1");
	// 单独设置耳朵
	model->SetTextureAll("g_DiffuseTex", bodyTex, "part1");
	return true;
}


bool ActorBodyAvatarComponent::exchangePartFace(int avatarmodel, int index, bool isShow, const char* texturePath /*=NULL*/, int skinModel /*=0*/)
{
	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD
	if (index != 2)
		return false;


	if (!m_ActorBody->m_Entity) return false;

	if (m_ActorBody->m_LoadModelData && m_ActorBody->m_LoadModelData->IsAsync() && m_ActorBody->m_Entity->GetMainModel() == nullptr)
	{
		m_ActorBody->SyncModelData();
	}

	Rainbow::PPtr<Rainbow::Model> model(m_ActorBody->m_Entity->GetMainModel());
	if (!model) return false;

	if (!isShow) avatarmodel = 7;

	int checkcode = 1;
	char bodypath[256];
	char eyepath[256];
	sprintf(eyepath, "entity/player/player12/default_empty.png");

	if (skinModel == 0)
	{
		if (avatarmodel == 7)
		{
			sprintf(bodypath, "entity/player/player12/body.png");
		}
		else {
			sprintf(bodypath, "entity/player/player12/face.png");
		}
	}
	else
	{
		// 换肤色
		sprintf(bodypath, "entity/avatar/1000_%d/%d.png_", skinModel, skinModel);
	}

	if (!GetFileManager().IsFileExist(bodypath))
		return false;

	m_iAvatarPartList[AVATAR_PART_TYPE::FACE] = avatarmodel;
	m_iAvatarPartList[AVATAR_PART_TYPE::SKIN] = skinModel;

	// 换肤色跟眼睛 
	if ((avatarmodel != 7 && isShow) || skinModel != 0)
	{
		MINIW::ScriptVM::game()->callFunction("AvtCallTable", "iiu[ActorBody]>i", 1, avatarmodel, m_ActorBody, &checkcode);
		if (checkcode != 0)
			return false;

		SharePtr<Texture2D> partPtex;
		// 换眼睛
		if (avatarmodel != 7 && avatarmodel != 0 && isShow)
		{
			sprintf(eyepath, "entity/avatar/1000_%d/%d.png_", avatarmodel, avatarmodel);
		}
		else
		{
			// 默认的眼睛
			sprintf(eyepath, "entity/player/player12/eye.png_");
		}

		model = m_ActorBody->m_Entity->GetMainModel();
		if (!model) return false;

		return SetAvatarBodyAndFaceTexture(model, bodypath, eyepath);
	}
	else
	{
		if (!m_ActorBody->m_Entity) return false;
		Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
		if (!model) return false;

		return SetAvatarBodyAndFaceTexture(model, bodypath, eyepath);
	}
#endif	
	return true;
}

bool ActorBodyAvatarComponent::exchangePartFaceASync(const char* avatarInfo, std::function<void()> callback)
{
#ifndef IWORLD_SERVER_BUILD
	jsonxx::Array avatarInfoJson;
	if (!avatarInfoJson.parse(avatarInfo))
	{
		//获取不到JSON数据
		LOG_INFO("parse json fail");
		return false;
	}

	m_ugcModeResLoadCount = 0;

	char bodypath[256] = { 0 };
	char eyepath[256] = { 0 };
	sprintf(bodypath, "entity/player/player12/face.png");
	sprintf(eyepath, "entity/player/player12/eye.png_");

	std::string strBodypath(bodypath);
	size_t avtPartNum = avatarInfoJson.size();
	LOG_INFO("ActorBody::exchangePartFaceASyn:%d", avtPartNum);

	for (size_t i = 0; i < avtPartNum; i++)
	{
		jsonxx::Object aSkin = avatarInfoJson.get<jsonxx::Object>(i);
		if (aSkin.has<jsonxx::String>("modelId") && aSkin.has<jsonxx::Number>("part"))
		{
			string modelName = aSkin.get<jsonxx::String>("modelId");
			int iPart = (int)aSkin.get<jsonxx::Number>("part");

			UgcModelParam* modelinfo = UgcAssetMgr::GetInstancePtr()->ParseModelStr(modelName);
			if (modelinfo == NULL)
				continue;

			std::string modelpath(modelinfo->modelPath);
			//LOG_INFO("ActorBody::exchangePartFaceASync modelinfo->modelPath:%s", modelinfo->modelPath.c_str());
			if (iPart == AVATAR_PART_TYPE::FACE || iPart == AVATAR_PART_TYPE::SKIN)
			{
				m_ugcModelLoader->LoadTextureAsync(modelinfo->modelPath, true, [&, callback, avtPartNum, iPart, modelpath, strBodypath](bool isSuccess, Rainbow::SharePtr<Rainbow::Texture2D> pTex) -> void {
					m_ugcModeResLoadCount++;
					if (isSuccess && pTex)
					{
						SharePtr<Texture2D> basePtex = GetAssetManager().LoadAssetAsync<Texture2D>(strBodypath.c_str());
						SharePtr<Texture2D> partPtex;

						if (basePtex == NULL)
						{
							return;
						}

						if (iPart == AVATAR_PART_TYPE::FACE)
						{
							partPtex = pTex;
							strcpy(eyepath, modelpath.c_str());
						}
						else if (iPart == AVATAR_PART_TYPE::SKIN)
						{
							basePtex = pTex;
							strcpy(bodypath, modelpath.c_str());
						}

						if (partPtex && basePtex)
						{
							Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
							if (model == NULL) return;

							SetAvatarBodyAndFace(model, basePtex, partPtex);
						}


					}
					else
					{
						LOG_WARNING("ActorBody::exchangePartFaceASync m_ugcModelLoader->LoadTextureAsync Failed:%s", modelpath.c_str());
					}

					if (m_ugcModeResLoadCount == avtPartNum)
					{
						m_ugcModeResLoadCount = 0;
						callback();
					}
					});
			}
			else
			{
				m_ugcModelLoader->LoadPrefabAsync(modelinfo->modelPath, [&, callback, avtPartNum, modelpath, iPart](bool success, Rainbow::SharePtr<Rainbow::Asset> asset) {
					m_ugcModeResLoadCount++;
					if (success)
					{
						if (asset && m_ActorBody->m_Entity->GetMainModel())
						{
							Model* pMainModel = m_ActorBody->m_Entity->GetMainModel();
							bool isNewModel = false;
							if (pMainModel->IsKindOf<ModelNew>())
							{
								isNewModel = true;
							}

							if (isNewModel && asset->IsKindOf<Prefab>())
							{
								static_cast<ModelNew*>(pMainModel)->AddAvatar(iPart, asset.CastTo<Prefab>(), NULL, NULL);
								pMainModel->ShowAvatar(iPart, true);
							}
							else
							{
								if (asset && asset->IsKindOf<ModelData>())
								{
									pMainModel->AddAvatar(iPart, asset.CastTo<ModelData>(), NULL, NULL);
									pMainModel->ShowAvatar(iPart, true);
								}
							}
						}
					}
					else
					{
						LOG_WARNING("ActorBody::exchangePartFaceASync m_ugcModelLoader->LoadPrefabAsync Failed:%s", modelpath.c_str());
					}

					if (m_ugcModeResLoadCount == avtPartNum)
					{
						m_ugcModeResLoadCount = 0;
						callback();
					}
					});
			}
		}
	}
#endif

	return true;
}

bool ActorBodyAvatarComponent::exchangePartFaceByPath(const Rainbow::FixedString& avatarPath, const Rainbow::FixedString& skinPath /* = "" */)
{
	OPTICK_EVENT();
#ifndef IWORLD_SERVER_BUILD

	if (!m_ActorBody->m_Entity) return false;
	if (m_ActorBody->m_LoadModelData && m_ActorBody->m_LoadModelData->IsAsync() && m_ActorBody->m_Entity->GetMainModel() == nullptr)
	{
		m_ActorBody->SyncModelData();
	}
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (model == NULL) return false;

	char bodypath[256] = { 0 };
	char eyepath[256] = { 0 };
	sprintf(bodypath, "entity/player/player12/face.png");
	sprintf(eyepath, "entity/player/player12/eye.png_");

	SharePtr<Texture2D> basePtex = GetAssetManager().LoadAssetAsync<Texture2D>(bodypath);
	if (basePtex == NULL)
	{
		return false;
	}

	// 换肤色跟眼睛 
	if (avatarPath.IsVaild() || skinPath.IsVaild())
	{
		SharePtr<Texture2D> partPtex;
		// 换眼睛
		if (avatarPath.IsVaild())
		{
			strcpy(eyepath, avatarPath.c_str());
			partPtex = GetAssetManager().LoadAssetAsync<Texture2D>(eyepath);
		}
		else
		{
			// 默认的眼睛
			sprintf(eyepath, "entity/player/player12/eye.png_");
			partPtex = GetAssetManager().LoadAssetAsync<Texture2D>(eyepath);
		}
		// 换肤色
		if (skinPath.IsVaild())
		{
			basePtex = GetAssetManager().LoadAssetAsync<Texture2D>(skinPath.c_str());
			if (basePtex == NULL)
			{
				return false;
			}
		}

		if (!partPtex)
		{
			return false;
		}

		return SetAvatarBodyAndFace(model, basePtex, partPtex);
	}
	else
	{
		SharePtr<Texture2D> partPtex = GetAssetManager().LoadAssetAsync<Texture2D>(eyepath);
		return SetAvatarBodyAndFace(model, basePtex, partPtex);
	}
#endif	
	return true;
}


void ActorBodyAvatarComponent::UpdateAvatarShiedPartsModel(int index)
{
	//处理shield部位互斥
	if (m_AvatarPartShiedlMap.size() > 0)
	{
		{
			// 处理当前部位跟别的部位互斥，隐藏被互斥的部位
			auto iter = m_AvatarPartShiedlMap.find(index);
			if (iter != m_AvatarPartShiedlMap.end())
			{
				for (auto shieldPart : iter->second)
				{
					hideAvatarPartModel(shieldPart);
				}
			}
		}

		// 处理当前部位被别的部位互斥，隐藏自身
		if (hasAvatarPartShied(index))
		{
			hideAvatarPartModel(index);
		}
	}
}


void ActorBodyAvatarComponent::UpdateCacheAvatarSkinModelResParts()
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_ActorBody->m_Entity == nullptr) return;
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (model == nullptr) return;

	if (m_AvatarSkinModeResCount <= 0)
	{
		return;
	}

	m_AvatarSkinModeResCount = 0;
	for (int i = 0; i < AVATAR_PART_TYPE::MAX; i++)
	{
		if (!m_AvatarSkinModelRes[i])
		{
			continue;
		}

		if (m_AvatarSkinModelRes[i].IsValid() && !m_AvatarSkinModelRes[i]->IsLoaded())
		{
			m_AvatarSkinModeResCount++;
			continue;
		}

		{
			SharePtr<Rainbow::Asset> partModle = m_AvatarSkinModelRes[i];

			Entity* pEntity = Entity::Create();

			Rainbow::Model* pModel = Model::CreateInstanceFromAsset(partModle);

			if (!partModle)
			{
				if (!pEntity->IsInScene())
				{
					DESTORY_GAMEOBJECT_BY_COMPOENT(pEntity);
				}
				return;
			}

			if (partModle && pModel)
			{
				pEntity->Load(pModel);

				m_AvatarSkinModel[i] = pEntity;

				int point_id = m_AvatarSkinPointIDs[i] > 0 ? m_AvatarSkinPointIDs[i] : getAvatarSkinAnchorId(i);

				if (m_ActorBody->m_Entity) //切换视角后定制皮肤不显示
				{
					m_ActorBody->m_Entity->BindObject(point_id, m_AvatarSkinModel[i]);
				}
				else
				{
					DESTORY_GAMEOBJECT_BY_COMPOENT(m_AvatarSkinModel[i]);
					takeOffAvatarSkinEffect(i);
				}
			}

			if (!pEntity->IsInScene())
			{
				DESTORY_GAMEOBJECT_BY_COMPOENT(pEntity);
			}

			m_AvatarSkinPointIDs[i] = 0;
			m_AvatarSkinModelRes[i] = nullptr;
		}
	}
#endif		
}

void ActorBodyAvatarComponent::resetAvatarPartShied(int index)
{
	assert(index >= AVATAR_PART_TYPE::BODY && index < AVATAR_PART_TYPE::MAX);

	if (index >= AVATAR_PART_TYPE::BODY && index < AVATAR_PART_TYPE::MAX)
	{
		for (auto iter = m_AvatarPartShiedlMap.begin(); iter != m_AvatarPartShiedlMap.end(); iter++)
		{
			if (iter->first == index)
				m_AvatarPartShiedlMap[index].clear();
		}
	}
}

void ActorBodyAvatarComponent::addAvatarPartShied(int index, int shieldId)
{
	assert(index >= AVATAR_PART_TYPE::BODY && index < AVATAR_PART_TYPE::MAX);
	assert(shieldId >= AVATAR_PART_TYPE::BODY && shieldId < AVATAR_PART_TYPE::MAX);

	if (index >= AVATAR_PART_TYPE::BODY && index < AVATAR_PART_TYPE::MAX
		&& shieldId >= AVATAR_PART_TYPE::BODY && shieldId < AVATAR_PART_TYPE::MAX)
	{
		m_AvatarPartShiedlMap[index].push_back(shieldId);
	}
}

bool ActorBodyAvatarComponent::hasAvatarPartShied(int typeId)
{
	assert(typeId >= AVATAR_PART_TYPE::BODY && typeId < AVATAR_PART_TYPE::MAX);
	for (auto iter = m_AvatarPartShiedlMap.begin(); iter != m_AvatarPartShiedlMap.end(); iter++)
	{
		for (auto shieldPart : iter->second)
		{
			if (shieldPart == typeId)
			{
				return true;
			}
		}
	}

	return false;
}

bool ActorBodyAvatarComponent::isAvatarPartShiedParts(int typeId)
{
	assert(typeId >= AVATAR_PART_TYPE::BODY && typeId < AVATAR_PART_TYPE::MAX);
	for (auto iter = m_AvatarPartShiedlMap.begin(); iter != m_AvatarPartShiedlMap.end(); iter++)
	{
		if (iter->first == typeId)
			return true;
	}

	return false;
}

bool ActorBodyAvatarComponent::clearAvatarPartShied(int index)
{
	assert(index >= AVATAR_PART_TYPE::BODY && index < AVATAR_PART_TYPE::MAX);

	if (index >= AVATAR_PART_TYPE::BODY && index < AVATAR_PART_TYPE::MAX)
	{
		for (auto iter = m_AvatarPartShiedlMap.begin(); iter != m_AvatarPartShiedlMap.end(); iter++)
		{
			if (iter->first == index)
			{
				if (GetAvatarPartModelShow(index))
				{
					hideAvatarPartModel(index);
				}
				iter->second.clear();
			}

			for (auto shieldPart : iter->second)
			{
				if (shieldPart == index)
				{
					if (GetAvatarPartModelShow(iter->first))
					{
						hideAvatarPartModel(iter->first);
					}
					iter->second.clear();
					break;
				}
			}
		}

		return true;
	}

	return false;
}


void ActorBodyAvatarComponent::ClearEquipAvatar(EQUIP_SLOT_TYPE slot)
{
	AVATAR_PART_TYPE avatarType = GetAvatarPartTypeBySlot(slot);

	// 任何情况下，脱掉装备要清空原始显示状态
	if (avatarType != AVATAR_PART_TYPE::MAX)
	{
		m_bAvatarPartListStatu[avatarType] = false;
	}

	// 处理装备和装扮的互斥逻辑
	if (!m_bHideEquipAvatar && GetAvatarPartModelShow(avatarType))
	{
		// 处理直接互斥对应的原始装备位显示
		AVATAR_PART_TYPE origineavatarType = GetOriginAvatarPartTypeBySlotType(slot);
		bool origineShow = GetOriginAvatarPartModelShow(origineavatarType);
		if (origineShow && !GetAvatarPartModelShow(origineavatarType))
		{
			setAvatarPartModelShow(origineavatarType, true);
		}

		setAvatarPartModelShow(avatarType, false);
		UpdateAvatarPartShieldShow();
	}
}

void ActorBodyAvatarComponent::SetHideEquipAvatar(bool value)
{
	if (m_bHideEquipAvatar != value)
	{
		m_bHideEquipAvatar = value;

		for (int j = AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD; j < AVATAR_PART_TYPE::MAX; j++)
		{
			if (GetOriginAvatarPartModelShow(j))
			{
				setAvatarPartModelShow(j, !m_bHideEquipAvatar);
			}
		}

		if (m_bHideEquipAvatar)
		{
			for (int i = AVATAR_PART_TYPE::BODY; i <= AVATAR_PART_TYPE::SKIN; i++)
			{
				if (GetOriginAvatarPartModelShow(i))
				{
					setAvatarPartModelShow(i, GetOriginAvatarPartModelShow(i));
				}
			}
		}

		{
			UpdateAvatarPartShieldShow();
		}
	}
}

bool ActorBodyAvatarComponent::GetHideEquipAvatarState()
{
	return m_bHideEquipAvatar;
}

void ActorBodyAvatarComponent::HideEquipAvatarParts(bool& hasEquips, AVATAR_PART_TYPE destType)
{
	hasEquips = false;

	if (m_bHideEquipAvatar)
	{
		return;
	}

	// 处理直接冲突隐藏
	for (int i = AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD; i < AVATAR_PART_TYPE::MAX; i++)
	{
		if (GetAvatarPartModelShow(i))
		{
			AVATAR_PART_TYPE avatarType = GetAvatarPartTypeByEquipAvatarType((AVATAR_PART_TYPE)i);

			if (avatarType != AVATAR_PART_TYPE::MAX && m_ActorBody->IsShowAvatar(avatarType))
			{
				setAvatarPartModelShow(avatarType, false);

				if (destType != AVATAR_PART_TYPE::MAX && avatarType == destType)
				{
					hasEquips = false;
					return;
				}
			}

			hasEquips = true;
		}
	}
}

// 装备互斥装扮显示逻辑处理
void ActorBodyAvatarComponent::UpdateAvatarPartShieldShow()
{
	bool hasEquip = false;
	HideEquipAvatarParts(hasEquip);

	// 处理部件有shield id情况下的显示/隐藏
	for (int i = AVATAR_PART_TYPE::BODY; i <= AVATAR_PART_TYPE::SKIN; i++)
	{
		UpdateShiledPartModelShow(i);
	}

	// UpdateShiledPartModelShow(AVATAR_PART_TYPE::JACKET);
}

// 装备互斥装扮显示逻辑处理
void ActorBodyAvatarComponent::UpdateEquipAvatarPartsShieldHide(AVATAR_PART_TYPE destAvatarType)
{
	if (destAvatarType >= AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD)
	{
		if (m_bHideEquipAvatar)
		{
			setAvatarPartModelShow(destAvatarType, false);
		}
		else
		{
			destAvatarType = GetAvatarPartTypeByEquipAvatarType(destAvatarType);
		}
	}

	if (destAvatarType >= HEAD_EFFECT)
	{
		return;
	}

	bool hasEquips = false;
	HideEquipAvatarParts(hasEquips, destAvatarType);
	if (!hasEquips) return;

	// 处理部件有shield id情况下的显示/隐藏
	for (int i = AVATAR_PART_TYPE::BODY; i <= AVATAR_PART_TYPE::SKIN; i++)
	{
		UpdateShiledPartModelShow(i);
	}

	// 处理部件有shield id情况下的显示/隐藏
	// UpdateShiledPartModelShow(AVATAR_PART_TYPE::JACKET);
}

void ActorBodyAvatarComponent::UpdateShiledPartModelShow(int i)
{
	if (i >= AVATAR_PART_TYPE::BODY && i <= AVATAR_PART_TYPE::SKIN)
	{
		if (GetOriginAvatarPartModelShow(i) && isAvatarPartShiedParts(i))
		{
			bool canShow = true;

			for (int j = AVATAR_PART_TYPE::AVATAR_EQUIP_HEAD; j < AVATAR_PART_TYPE::MAX; j++)
			{
				if (GetAvatarPartModelShow(j))
				{
					AVATAR_PART_TYPE checkType = GetAvatarPartTypeByEquipAvatarType((AVATAR_PART_TYPE)j);

					if (checkType != AVATAR_PART_TYPE::MAX
						&& (checkType == i || hasAvatarPartShied(checkType)))
					{
						canShow = false;
						break;
					}
				}
			}

			if (GetAvatarPartModelShow(i) != canShow)
			{
				setAvatarPartModelShow(i, canShow);

				// 如果挤掉的是衣服
				if (!canShow && i == AVATAR_PART_TYPE::JACKET)
				{
					// 如果没有默认衣服对应的装备，补充默认的3装扮到AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST装扮位
					if (!m_ActorBody->IsShowAvatar(AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST)
						&& !GetAvatarPartModelShow(AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST) && !GetAvatarPartModelShow(AVATAR_PART_TYPE::JACKET))
					{
						m_ActorBody->addAvatarPartModel(3, AVATAR_PART_TYPE::AVATAR_EQUIP_BREAST, true, 0, true);
					}
				}
			}
		}
	}
}

void ActorBodyAvatarComponent::SetEquipAvatarPart(EQUIP_SLOT_TYPE slotType, int itemid)
{
	AVATAR_PART_TYPE avatarType = GetAvatarPartTypeBySlot(slotType);

	if (avatarType != AVATAR_PART_TYPE::MAX)
	{
		m_bAvatarPartListStatu[avatarType] = itemid > 0;
		m_iAvatarPartList[avatarType] = itemid;

		if (m_ActorBody->IsAvatarPlayer())
		{
			AVATAR_PART_TYPE origineAvatarType = GetOriginAvatarPartTypeBySlotType(slotType);
			if (origineAvatarType != AVATAR_PART_TYPE::MAX)
			{
				setAvatarPartModelShow(origineAvatarType, itemid <= 0);
			}
		}
	}
}

void ActorBodyAvatarComponent::SetEquipAvatar(EQUIP_SLOT_TYPE slotType, int nAnchorID, bool skipEquip)
{
	if (!m_bHideEquipAvatar && !skipEquip)
	{
		SetEquipAvatarPart((EQUIP_SLOT_TYPE)slotType, nAnchorID);
		UpdateAvatarPartShieldShow();
	}
}

void ActorBodyAvatarComponent::EquipAavatarPlayer(EQUIP_SLOT_TYPE slot, int itemid)
{
	AVATAR_PART_TYPE avatarType = GetAvatarPartTypeBySlot(slot);
	int avatarID = GetAvatarPartIDByItemId(itemid);
	const ItemDef* itemdef = GetDefManagerProxy()->getItemDef(itemid);
	if (avatarID > 0)
	{ 
		addAvatarPartModel(avatarID, avatarType, itemdef->QualityLevel, true, 0, false);
	}
}

void ActorBodyAvatarComponent::EquipAavatarPlayerByItemId(int itemid)
{
	EQUIP_SLOT_TYPE slot = GetEquipSlotTypeByItemId(itemid);
	if (slot == EQUIP_SLOT_TYPE::MAX_EQUIP_SLOTS) return;
	EquipAavatarPlayer(slot, itemid);
}

void ActorBodyAvatarComponent::ClearAavatarPlayerByItemId(int itemid)
{
	EQUIP_SLOT_TYPE slot = GetEquipSlotTypeByItemId(itemid);
	if (slot == EQUIP_SLOT_TYPE::MAX_EQUIP_SLOTS) return;
	ClearEquipAvatar(slot);
}

void ActorBodyAvatarComponent::showEffectAvatar(bool show)
{
	m_bAvaterEffectShow = show;

	if (m_ActorBody->m_OwnerActor && m_ActorBody->m_OwnerActor->IsVisible() && m_ActorBody->m_Entity)
	{
		Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
		if (model)
		{
			for (int i = AVATAR_PART_TYPE::HEAD_EFFECT; i <= AVATAR_PART_TYPE::BG_EFFECT; i++)
			{
				if (GetAvatarPartModelShow(i) && m_iAvatarPartList[i] != AVATAR_PART_TYPE::TRAILING_EFFECT)
				{
					if (m_bAvaterEffectShow)
					{
						if (!model->IsShowAvatar(i))
						{
							model->ShowAvatar(i, true);
						}
					}
					else
					{
						if (model->IsShowAvatar(i))
						{
							model->ShowAvatar(i, false);
						}
					}
				}
			}
		}

	}
}

void ActorBodyAvatarComponent::checkAvatarPartEffect(int avatarmodel, int index)
{
	if (index == AVATAR_PART_TYPE::HAND_ORNAMENT || index == AVATAR_PART_TYPE::SHOE)
	{
		int right = AVATAR_PART_TYPE::RIGHT_HAND;
		if (index == AVATAR_PART_TYPE::SHOE)
		{
			right = AVATAR_PART_TYPE::RIGHT_SHOE;
		}
		int location = 0;
		SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "GetAvatrPartLocationById", "i>i", avatarmodel, &location);
		if (location == 0)
		{
			addAvatarPartEffect(avatarmodel, index, 1);
			addAvatarPartEffect(avatarmodel, right, 2);
		}
		if (location == 1)
		{
			addAvatarPartEffect(avatarmodel, index, 1);
		}
		if (location == 2)
		{
			addAvatarPartEffect(avatarmodel, right, 2);
		}
	}
	else
	{
		addAvatarPartEffect(avatarmodel, index, 0);
	}
}

void ActorBodyAvatarComponent::addAvatarPartEffect(int avatarmodel, int index, int part)
{
	if (index >= AVATAR_PART_TYPE::HEAD_EFFECT && index <= AVATAR_PART_TYPE::BG_EFFECT)
	{
		return;
	}
	char effectPath[128] = { 0 };
	//char zipPath[128] = { 0 };
	Rainbow::ColourValue cv(0.2f, 0.2f, 0.2f, 0.2f);// (1.0f, 1.0f, 1.0f, 1.0f);
	//snprintf(zipPath, sizeof(zipPath), "%sdata/http/productions/1000_%d.zip", GetClientInfoProxy()->getDataDir(), avatarmodel);
	if (part == 0)
	{
		snprintf(effectPath, sizeof(effectPath), "entity/avatar/1000_%d/%d.ent", avatarmodel, avatarmodel);
	}
	else
	{
		snprintf(effectPath, sizeof(effectPath), "entity/avatar/1000_%d/%d_%d.ent", avatarmodel, avatarmodel, part);
	}

	if (m_AvatarSkinEffect[index])
	{
#if ENTITY_MODIFY_MODEL_ASYNC
		m_AvatarSkinEffect[index]->LoadAsync(effectPath);
#else
		SharePtr<EntityData> avt_model = GetAssetManager().LoadAsset<EntityData>(effectPath);
		if (avt_model)
		{
			m_AvatarSkinEffect[index]->Load(avt_model);
		}
#endif
	}
	else
	{
		Entity* pEntity = Entity::Create();
		int point_id = getAvatarSkinEffectAnchorId(index);
		pEntity->SetInstanceAmbient(cv);
		m_AvatarSkinEffect[index] = pEntity;
		m_ActorBody->getEntity()->BindObject(point_id, m_AvatarSkinEffect[index]);

#if ENTITY_MODIFY_MODEL_ASYNC
		m_AvatarSkinEffect[index]->LoadAsync(effectPath);
#else
		SharePtr<EntityData> avt_model = GetAssetManager().LoadAsset<EntityData>(effectPath);
		if (avt_model)
		{
			m_AvatarSkinEffect[index]->Load(avt_model);
		}
#endif

	}
}

bool ActorBodyAvatarComponent::checkWholeBodyEffect()
{
	if (m_ActorBody->isPlayer()) {
		RoleSkinDef* skinDef = GetDefManagerProxy()->getRoleSkinDef(m_ActorBody->getSkinID());
		if (skinDef && (GetAvatarPartModelShow(AVATAR_PART_TYPE::WHOLE_BODY_EFFECT) && skinDef->EffectType == 3))
		{
			return true;
		}
	}
	return false;
}


bool ActorBodyAvatarComponent::setAvatarSkinModel(int avatarmodel, int index, int pointId)
{
	char modelPath[512] = { 0 };

	SharePtr<Rainbow::Asset>	 partModle;
	SharePtr<Rainbow::Texture2D> basetexture;
	SharePtr<Rainbow::Texture2D> emissiveTex;

	Entity* pEntity = Entity::Create();;
	Rainbow::Model* pModel = NULL;

	snprintf(modelPath, sizeof(modelPath), "entity/avatar/1000_%d/%d_2.prefab", avatarmodel, avatarmodel);

	auto prefab = GetAssetManager().LoadAssetAsync<Prefab>(modelPath);
	if (prefab)
	{
		m_AvatarSkinPointIDs[index] = pointId;
		m_AvatarSkinModelRes[index] = prefab.CastTo<Asset>();
		m_AvatarSkinModeResCount++;
		return true;
	}

	return false;
}

//绑定定制皮肤
bool ActorBodyAvatarComponent::equipAvatarSkin(int modelid, int index, int pointId)
{
	//非面饰、手饰、背饰不使用绑点绑定
	if (index != Model::Avatar_Parts_Face_Accessories && index != Model::Avatar_Parts_Glove && index != Model::Avatar_Parts_Back_Accessories)
	{
		return false;
	}
	//如果是普通玩家
	if (m_ActorBody->m_MutateMob == 0)
	{
		if (m_AvatarSkinModel[index])
		{
			if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->UnbindObject(m_AvatarSkinModel[index]);
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_AvatarSkinModel[index]);
		}
		if (m_AvatarSkinEffect[index])
			takeOffAvatarSkinEffect(index);

		if (setAvatarSkinModel(modelid, index, pointId))
		{
			return true;
		}
		return false;
	}
	return false;
}

//脱下定制皮肤
void ActorBodyAvatarComponent::takeOffAvatarSkin(int index)
{
	if (m_AvatarSkinModel[index])
	{
		if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->UnbindObject(m_AvatarSkinModel[index]);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_AvatarSkinModel[index]);
	}
	if (m_AvatarSkinEffect[index])
	{
		takeOffAvatarSkinEffect(index);
	}
}

void ActorBodyAvatarComponent::takeOffAvatarSkinEffect(int index)
{
	if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->UnbindObject(m_AvatarSkinEffect[index]);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_AvatarSkinEffect[index]);
	if (index == AVATAR_PART_TYPE::HAND_ORNAMENT && m_AvatarSkinEffect[AVATAR_PART_TYPE::RIGHT_HAND])
	{
		if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->UnbindObject(m_AvatarSkinEffect[AVATAR_PART_TYPE::RIGHT_HAND]);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_AvatarSkinEffect[AVATAR_PART_TYPE::RIGHT_HAND]);
	}
	if (index == AVATAR_PART_TYPE::SHOE && m_AvatarSkinEffect[AVATAR_PART_TYPE::RIGHT_SHOE])
	{
		if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->UnbindObject(m_AvatarSkinEffect[AVATAR_PART_TYPE::RIGHT_SHOE]);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_AvatarSkinEffect[AVATAR_PART_TYPE::RIGHT_SHOE]);
	}
}

void ActorBodyAvatarComponent::releaseAvatarSkin()
{
	m_AvatarSkinModeResCount = 0;
	for (int i = 0; i < AVATAR_PART_TYPE::MAX; i++)
	{
		m_AvatarSkinPointIDs[i] = 0;
		m_AvatarSkinModelRes[i] = nullptr;
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_AvatarSkinModel[i]);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_AvatarSkinEffect[i]);
	}
}

int ActorBodyAvatarComponent::getAvatarSkinAnchorId(int slot)
{
	int point_id = 0;
	switch (slot)
	{
	case AVATAR_PART_TYPE::HAND_ORNAMENT:	//手饰
		point_id = 100;
		break;
	case AVATAR_PART_TYPE::BACK_ORNAMENT:	 //背饰
		point_id = 105;
		break;
	case AVATAR_PART_TYPE::FACE_ORNAMENT:		//面饰
		point_id = 106;
		break;
	}
	return point_id;
}

int ActorBodyAvatarComponent::getAvatarSkinEffectAnchorId(int slot)
{
	int point_id = 0;	//整体裸模
	switch (slot)
	{
	case AVATAR_PART_TYPE::HEAD:	//头饰
		point_id = 107;
		break;
	case AVATAR_PART_TYPE::FACE_ORNAMENT:	//面饰
		point_id = 106;
		break;
	case AVATAR_PART_TYPE::JACKET:		//上衣
		point_id = 105;
		break;
	case AVATAR_PART_TYPE::HAND_ORNAMENT:	//左手
		point_id = 100;
		break;
	case AVATAR_PART_TYPE::TROUSERS:		//裤子
		point_id = 105;
		break;
	case AVATAR_PART_TYPE::SHOE:		//鞋子
		point_id = 102;
		break;
	case AVATAR_PART_TYPE::BACK_ORNAMENT:	 //背饰
		point_id = 105;
		break;
	case AVATAR_PART_TYPE::RIGHT_HAND:	//右手
		point_id = 101;
		break;
	case AVATAR_PART_TYPE::RIGHT_SHOE:	//右脚
		point_id = 103;
		break;
	}
	return point_id;
}

