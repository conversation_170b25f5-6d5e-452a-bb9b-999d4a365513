
#ifndef __LIVINGLOCOMOTION_H__
#define __LIVINGLOCOMOTION_H__

#include "ActorLocoMotion.h"
#include "OgreWCoord.h"
#include "SandboxGame.h"
//#include "OgreVector3.h"
#include "Smoother.h"
#include "defdata.h"

namespace Rainbow
{
	class RigidStaticActor;
	class Entity;
}
class BlockEnvEffectsComponent;

//tolua_begin
enum BehaviorType
{
	None = 0x00000,
	Seek = 0x00002,//Ѱ��
	Flee = 0x00004,//����
	Arrive = 0x00008,//����
	Wander = 0x00010,//����
	Cohesion = 0x00020,//����
	Separation = 0x00040,//����
	Allignment = 0x00080,//����
	ObstacleAvoidance = 0x00100,//��ܹ����
	WaterAvoidance = 0x00200,//���ˮ��
	FollowPath = 0x00400,//����·��
	Pursuit = 0x00800,//׷��
	Evade = 0x01000,//�ӱ�
	Interpose = 0x02000,//����
	Hide = 0x04000,//����
	Flock = 0x08000,//��Ⱥ���
	OffsetPursuit = 0x10000,//����׷��
	LashTagert = 0x20000,
};
//tolua_end

enum MoveAbilityType //�ƶ���������
{
	LandLoc = 0,	//����
	FlyLoc,			//����
	AquaticLoc,		//ˮ��
	OnWater,		//ˮ��
	Climbing,       //��ǽ
	LandLoc_CanSwin, //���� + ��Ӿ
};

class EXPORT_SANDBOXGAME LivingLocoMotion : public ActorLocoMotion //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(LivingLocoMotion)
	//tolua_begin
	LivingLocoMotion();
	virtual ~LivingLocoMotion();

	virtual void tick() override;
	virtual void gotoPosition(const WCoord &pos, float yaw, float pitch) override;
	virtual void doJump();
	void doJumpTarget();

	void setMoveForward(float vel);
	void setMoveStrafing(float vel);
	virtual void setTarget(const WCoord &target, float speed) override; //���������Ŀ���,  ����Ŀ���ᷢ��Ϣ,  NULL�����Ŀ��
	void clearTarget();
	float calGravityMotionY(float my);
	void setBoundJump(bool bound) { m_bBoundJump = bound; }
	bool canDoJump(); //����Ƿ����Ծ
	const WCoord& GetMoveTarget() { return m_MoveTarget; }

	void setJumpToTarget(const WCoord &target);
	void checkPhysWorld();
	/*virtual bool needFullRotation() override;*/

	//-------------------------------------------- begin
	void tryMovoTo(int x, int y, int z, BehaviorType type = Wander);
	bool isBehaviorOn(BehaviorType type)
	{
		return (m_BehaviorFlag & type) == type;
	}
	void setBehaviorOn(BehaviorType type)
	{
		m_BehaviorFlag |= type;
	}
	void setBehaviorOff(BehaviorType type)
	{
		if (isBehaviorOn(type))
			m_BehaviorFlag ^= type;
	}
	void resetVelocity()
	{
		m_Velocity = Rainbow::Vector3f::zero;
	}
	//-------------------------------------------- end
	virtual float getJumpHeight();
	//tolua_end
	bool isMovementStop();
	void setJumpToTargetEnforced(const WCoord& target);
	
	bool checkMoveTypeCheat();
	int getMoveType() { return m_UseMoveType.Val(); }
	virtual void setMoveType(int val);
	bool checkGravityCheat();
	int getUseGravity() { return m_UseGravity.Val(); }
	void calculateMove();

	void initMoveAbility(MoveAbilityType type);
	void setLocoMotionType(MoveAbilityType type);

	MoveAbilityType getLocoMotionType()
	{
		return m_nLocType;
	}

	bool checkMoveAbilityFlag (MoveAbilityType type)
	{
		return m_MoveAbilityFlag.CheckFlag(type);
	}

	void setMoveAbilityFlag(MoveAbilityType type, bool value)
	{
		m_MoveAbilityFlag.SetFlag(type, value);
	}

	//----------------------------------- begin
	void SetBehaviorTypeWeight(BehaviorType type, float weight);
	void UpdateRotation();
	float GetSpeedInAir();

	virtual void initPosition(const WCoord& pos);
	void SetSmoothRotation(bool b);

	void SetAutoStop(bool b)
	{
		isAutoStop = b;
	}
	float GetSpeedInWater();

	float getSpeed();
	WCoord getHitBlockPos();

	void setLeader(bool isLeader) { //�������
		m_isLeader = isLeader;
	}
	bool isLeader() {
		return m_isLeader;
	}
	//--------------------------------------------------end

	void attachPhysActor();
	void detachPhysActor();
	void updateJumping(bool isplayerrigidbody = false);
protected:
	virtual bool prepareJump(int &cooldown);
	virtual float getGravityFactor(bool up); //up ? ����: �½�
	void updateJetpackMove();
	
	void UpdateClimbTree();
	void UpdateClimb();
	virtual float getWaterJumpingSpeed();
	virtual void autoStep();
	virtual void moveEntityWithHeading(float strafing, float forward);
	void GetRiddenActorInputInfo(float& strafing, float& forward);// �����˷�������������ƶ�
	void TurnToRiddenActorDir(LivingLocoMotion* riddenloc);// �����˷������������ת��
	bool updateMoveTarget(bool* climbing = nullptr);//����Ŀ��: return true
	virtual void collideWithNearbyActors();
	virtual bool checkSeatInWaterSkill(int iSkillId) { return false; }//1 == ��ˮ, 2 == ǱӾ, 4 == ˮ��ͻ��
	virtual bool checkCanUpAndDown(float fRotationPitch) { return false; }
	virtual bool isDoingRush() { return false; }
	virtual float getMoveReduceRatio() const {return 0.98f;}

	void moveEntityWithDirection();

	//---------------------------------begin
	void calculateSteering();
	Rainbow::Vector3f seek(const WCoord& target);
	Rainbow::Vector3f flee(const WCoord& target);

	Rainbow::Vector3f pursuit(ClientActor& target);
	Rainbow::Vector3f evade(ClientActor& evader);

	Rainbow::Vector3f surfaceAvoidance();
	Rainbow::Vector3f obstacleAvoidance();

	bool AccumulateForce(Rainbow::Vector3f& SteeringForce, const Rainbow::Vector3f& addTo);
	//---------------------------------end
public:
	//tolua_begin
	float m_MoveForward;
	float m_MoveStrafing;
	bool m_bBoundJump;

	//--------------------------------begin
	WCoord m_MoveTarget;
	bool m_HasTarget;
	float m_SpeedMultiple;
	//--------------------------------end
	//tolua_end
	
	//------------------------------begin
	int m_SyncSteps;
	WCoord m_SyncPos;
	float m_SyncYaw;
	float m_SyncPitch;
	int m_BehaviorFlag;  //��ƽ��FlyLocomotion��AquaticLocomotion���ƶ����� m_MoveAbilityFlag����֮�� �ٰ�m_BehaviorFlag�ɵ�
	Rainbow::Vector3f m_Velocity;
	Rainbow::Vector3f m_ColliderMotion;
	Rainbow::Vector3f m_SteeringForce;
	WORLD_ID m_FearPlayerId;
	float m_StopDist;
	float m_SpeedInAir;

	WORLD_ID m_FearMobId;
	WCoord m_FearPos;
	float m_SpeedInWater;
	bool isAutoStop;//�й�״̬�µ���Ŀ�ĵ��Զ�ͣ�²��ٷ��ҡͷ
	int m_TeamNo;//������
	int m_TeamNum;//��������������
	bool m_jumpRote;
	//-----------------------------end
	
protected:
	//WCoord m_MoveTarget;
	WCoord m_JumpTarget;
	float m_MoveTargetSpeed; //<0 ��ʾû��Ŀ��

	AntiSetting::ProctectVal m_UseMoveType;
	AntiSetting::ProctectVal m_UseGravity;

	//-------------------------------begin
	float m_MaxSteeringForce;
	bool m_SmoothRotationOn;

	float obstacleAvoidanceWeight;
	float surfaceAvoidanceWeight;
	float fleeWeight;
	float wanderWeight;
	float pursuitWeight;

	Smoother<Rainbow::Vector3f>* m_smoother;
	int m_StepTick;
	bool m_isLeader;
	//----------------------------end
    BlockEnvEffectsComponent* m_pBlockEnvEffectsComponent = nullptr;
    bool m_bBlockEnvEffectsComponentInited = false;
    BlockEnvEffectsComponent* getBlockEnvEffectsComponent(bool force = false);
private:
	bool m_enforcedJumpTarget{ false };
	MoveAbilityType m_nLocType;   //��FlyLocomotion��AquaticLocomotion�ںϽ����󣬵�ǰ����ʹ�õ�loc��ʽ
	MNSandbox::FlagsInt m_MoveAbilityFlag;  //��ǰ����ӵ�е��ƶ�����������ֻ��living��Fly��Aquatic�����࣬�����ǻ���ģ�������Ҫ��ɸ���С���ͣ������ǿ��Բ���֮��Ϳ��԰�m_nLocTypeȥ���ˣ�
	MonsterDef* m_MonsterDef = nullptr;
private:
	void OnWaterTick();

}; //tolua_exports

class SouvenirMotion : public LivingLocoMotion //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(SouvenirMotion)

	//tolua_begin

	SouvenirMotion() : m_PhysActor(NULL)
	{
	
	}

	virtual void tick() override;
	void attachPhysActor();
	void detachPhysActor();

	Rainbow::RigidStaticActor *m_PhysActor;
	//tolua_end
}; //tolua_exports

EXPORT_SANDBOXGAME void CheckMotionValid(Vector3f& motion);
#endif