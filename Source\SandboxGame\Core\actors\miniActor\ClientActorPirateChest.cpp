#include "ClientActorPirateChest.h"
#include "LuaInterfaceProxy.h"
#include "container_world.h"
#include "ProjectileLocoMotion.h"
#include "special_blockid.h"
#include "world.h"

ClientActorPirateChest::ClientActorPirateChest()
{
	//m_ItemID = ITEM_AIR_BALL;
}

ClientActorPirateChest::~ClientActorPirateChest()
{

}

void ClientActorPirateChest::init(int itemid, ClientActor* shooter /*= nullptr*/)
{
	ClientActorProjectile::init(itemid, shooter);

}

void ClientActorPirateChest::tick()
{
	ClientActorProjectile::tick();
	int i = 1;
	
	WCoord blockStart(getLocoMotion()->getPosition());
	blockStart = blockStart/BLOCK_SIZE;
	while (IsWaterBlockID(getWorld()->getBlockID(WCoord(blockStart.x, blockStart.y + i, blockStart.z))))
	{
		i++;
	}
	if (IsWaterBlockID(getWorld()->getBlockID(WCoord(blockStart.x, blockStart.y + i - 1, blockStart.z))))
	{
		WCoord blockwater(blockStart.x, blockStart.y + i, blockStart.z);
		//getWorld()->getEffectMgr()->playParticleEffect("particles/todo.ent", blockwater * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2), 100, GenRandomFloat() * 360.0f, 0, false);
	}
}

flatbuffers::Offset<FBSave::SectionActor> ClientActorPirateChest::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveActorCommon(builder);

	auto obj = FBSave::CreateActorPirateChest(builder, basedata, m_ShootingActorID, m_ItemID);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorPirateChest, obj.Union());
}

bool ClientActorPirateChest::load(const void* srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorPirateChest*>(srcdata);
	loadActorCommon(src->basedata());

	init(src->itemid());

	m_ShootingActorID = src->shooter();

	auto locomotion = getLocoMotion();
	if (locomotion)
	{
		ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion*>(locomotion);
		if (loco)
			loco->syncPos = loco->getPosition();
	}

	return true;
}

void ClientActorPirateChest::onImpactWithActor(ClientActor* actor, const std::string& partname)
{


}

void ClientActorPirateChest::onImpactWithBlock(const WCoord* blockpos, int face)
{
	WCoord pos = WCoord(blockpos->x, blockpos->y, blockpos->z);
	switch (face)
	{
	case 0:
		pos.x -= 1;
		break;
	case 1:
		pos.x += 1;
		break;
	case 2:
		pos.z -= 1;
		break;
	case 3:
		pos.z += 1;
		break;
	case 4:
		pos.y -= 1;
		break;
	case 5:
		pos.y += 1;
		break;
	}
	getWorld()->setBlockAll(pos, 801, 0);
	std::vector<ItemDataConst>* PirateChest = &(GetLuaInterfaceProxy().get_lua_const()->PirateChest_Treasure);
	auto box =  dynamic_cast<WorldStorageBox*>(getWorld()->getContainerMgr()->getContainer(pos));
	if (box != nullptr)
	{
		for (int i = 0; i < PirateChest->size(); i++)
		{
			auto itemdata = PirateChest->at(i);
			int prob = GenRandomInt(1, 100);
			if (itemdata.Probability >= prob)
			{
				box->addItemByCount(itemdata.ItemID, itemdata.Num);
			}
		}
	}
	setNeedClear();
}
