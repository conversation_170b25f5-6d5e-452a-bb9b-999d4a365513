#include "ActorVillager.h"
#include "LuaInterfaceProxy.h"
#include "ClientPlayer.h"
#include "DefManagerProxy.h"
#include "ClientActorManager.h"
#include "container_backpack.h"
#include "ClientItem.h"
#include "ObserverEventManager.h"
#include "BlockMaterialMgr.h"
#include "BlockFurniture.h"
#include "BlockBed.h"
#include "container_world.h"
#include "MpActorManager.h"
#include "AIFunctionMgr.h"
#include "backpack.h"
#include "LivingLocoMotion.h"
#include "PlayerControl.h"
#include "SandBoxManager.h"
#include "ClientActorHelper.h"
#include "BindActorComponent.h"
#include "Plugin.h"
#include "SandboxListener.h"
#include "CarryComponent.h"
#include "SoundComponent.h"
#include "EffectComponent.h"
#include "TriggerComponent.h"
#include "RiddenComponent.h"
#include "TemperatureComponent.h"
#include "BlockVillageTotemIce.h"
#include "ActorBody.h"
#include "SandboxIdDef.h"
#include "special_blockid.h"
#include "WorldManager.h"

using namespace MNSandbox;

IMPLEMENT_SCENEOBJECTCLASS(ActorVillager)
IMPLEMENT_SCENEOBJECTCLASS(ClientSouvenir)
ActorVillager::ActorVillager() : m_hairId(0), m_hairColor(0), m_faceId(0), m_BedBindPos(WCoord(0, -1, 0)), m_OldPos(WCoord(0, -1, 0)), m_iCheckMoveDistTick(20),
	m_WorkBindPos(WCoord(0, -1, 0)), m_iProfession(0), m_bTraitor(false), m_iDemand(0), m_iFoodByTamed(0), m_iNeedJudgeCountDown(0),m_bCheckWakeUpTime(false),
	m_spawnTime(0), m_randomNameId(-1), b_isGuardForPlayer(false), b_worship(false), m_iceGirl(1), m_icedefaultEmoji("errorEmoji")
{
	for (auto& item: m_saveTimeTick)
	{
		item = 0;
	}

	//CreateComponent<CarryComponent>("CarryComponent");

	createEvent();
}
void ActorVillager::createEvent()
{
	typedef ListenerFunctionRef<float> ListenerHurtOnTrigger;
	ListenerHurtOnTrigger* listenerHurtOnTrigger = SANDBOX_NEW(ListenerHurtOnTrigger, [&](float hp) -> void {
		this->beHurtOnTrigger(hp);
	});
	Event2().Subscribe("beHurtOnTrigger", listenerHurtOnTrigger);

	typedef ListenerFunctionRef<bool, ClientPlayer*> ListenerGuardForPlayerOnTrigger;
	ListenerGuardForPlayerOnTrigger* listenerGuardForPlayerOnTrigger = SANDBOX_NEW(ListenerGuardForPlayerOnTrigger, [&](bool b, ClientPlayer* player) -> void {
		auto l = this->getPosition() - player->getPosition();
		if (l.length() < 32*BLOCK_SIZE) //32个格子*100
		{
			this->setGuardForPlayer(b);
		}
	});
	Event2().Subscribe("beGuardForPlayerOnTrigger", listenerGuardForPlayerOnTrigger);

	//玩家主动攻击Mob, 守卫去帮忙
	typedef ListenerFunctionRef<ClientActor*/*mob*/, ClientActor*/*player*/> ListenerPlayerReqHelpAttackMobOnTrigger;
	auto listenerPlayerReqHelpAttackMobOnTrigger = SANDBOX_NEW(ListenerPlayerReqHelpAttackMobOnTrigger, [&](ClientActor* mob, ClientActor* player) -> void {
		ObserverEvent obevent;
		auto pos = mob->getPosition();
		obevent.SetData_EventObj(this->getObjId());
		obevent.SetData_ToObj(mob->getObjId());
		obevent.SetData_Position((float)pos.x, (float)pos.y, (float)pos.z);
		ObserverEventManager::getSingleton().OnTriggerEvent("Actor.NewBeHurt", &obevent);
	});
	Event2().Subscribe("bePlayerReqHelpAttackMobOnTrigger", listenerPlayerReqHelpAttackMobOnTrigger);

	//玩家受到伤害,请求野人伙伴来帮忙
	typedef ListenerFunctionRef<ClientActor*/*mob*/, ClientActor*/*player*/> ListenerPlayerReqHelpOnTrigger;
	auto listenerPlayerReqHelpOnTrigger = SANDBOX_NEW(ListenerPlayerReqHelpOnTrigger, [&](ClientActor* mob, ClientActor* player) -> void {
		auto pos = mob->getPosition();
		// 观察者事件接口 - 援助
		ObserverEvent obevent;
		obevent.SetData_EventObj(this->getObjId());
		obevent.SetData_ToObj(mob->getObjId());
		obevent.SetData_HelperObjid(player->getObjId());
		obevent.SetData_Position((float)pos.x, (float)pos.y, (float)pos.z);
		GetObserverEventManager().OnTriggerEvent("Actor.ReqHelp", &obevent);
	});
	Event2().Subscribe("bePlayerReqHelpOnTrigger", listenerPlayerReqHelpOnTrigger);

	//boss 砸到村民冰冻
	typedef ListenerFunctionRef<int> ListenerBuffer;
	ListenerBuffer* listenerBuffer = SANDBOX_NEW(ListenerBuffer, [&](int bufferId) -> void {
		if (1033 == bufferId || 1034 == bufferId || 1037 == bufferId)
			this->ResetBTree();
		});
	Event2().Subscribe("hitActorVillager", listenerBuffer);

	//bool canCarried(ClientPlayer *player)
	typedef ListenerFunctionRef<bool&, ClientPlayer*> ListenerCanCarried;
	ListenerCanCarried* listenerCanCarried = SANDBOX_NEW(ListenerCanCarried, [&](bool& ret, ClientPlayer* player) -> void {
		ret = this->canCarried(player);
	});
	Event2().Subscribe("canCarried", listenerCanCarried);

	typedef ListenerFunctionRef<IClientPlayer*, std::string, bool> Listener1;
	m_listenerVillager1 = SANDBOX_NEW(Listener1, [&](IClientPlayer* player, std::string type, bool b) -> void {
		if (type == "warn")
		{
			this->warnThief(dynamic_cast<ClientActor*>(player));
		}
		else if (type == "attack")
		{
			this->attackThief(dynamic_cast<ClientActor*>(player));
		}
		else if (type == "Worship")
		{
			this->setWorship(b);
		}
		});
	Event2().Subscribe("Villager_func", m_listenerVillager1);

	typedef ListenerFunctionRef<bool&, int> Listener2;
	Listener2* listener2 = SANDBOX_NEW(Listener2, [&](bool& is, int profession) -> void {
		if (this->getProfession() == profession)
		{
			is = true;
		}
		is = false;
		});
	Event2().Subscribe("Villager_profession", listener2);

	typedef ListenerFunctionRef<bool&, const WCoord&> Listener3;
	Listener3* listener3 = SANDBOX_NEW(Listener3, [&](bool& is, const WCoord& flagpoint) -> void {
		if (this->getWorkBindPos() == flagpoint)
		{
			is = true;
		}
		is = false;
		});
	Event2().Subscribe("Villager_WorkBindPos", listener3);

	typedef ListenerFunctionRef<const WCoord&> Listener4;
	Listener4* listener4 = SANDBOX_NEW(Listener4, [&](const WCoord& villagePos) -> void {
		this->setWorkBindPos(villagePos);
		});
	Event2().Subscribe("Villager_setWorkBindPos", listener4);

	typedef ListenerFunctionRef<WCoord&> Listener5;
	Listener5* listener5 = SANDBOX_NEW(Listener5, [&](WCoord& villagePos) -> void {
		villagePos = this->getWorkBindPos();
		});
	Event2().Subscribe("Villager_getWorkBindPos", listener5);
}
bool ActorVillager::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorVillager *>(srcdata);
	if (!src)
		return false;
	
    const FBSave::ActorMob* tempsrc = reinterpret_cast<const FBSave::ActorMob*>(src->mobdata());
    if(tempsrc){
        auto def = GetDefManagerProxy()->getMonsterDef(tempsrc->defid());
        if(!def || def->Type != 10)
            return false;
    }
    
	if (!ClientMob::load(src->mobdata(), version))
	{
		return false;
	}

	if (src->carried() != 0)
	{
		auto CarryComp = sureCarryComponent();

		if (CarryComp)
		{
			//m_CarriedByActor = src->carried();
			CarryComp->setCarriedActorID(src->carried());
		}
	}

    if (m_Def == NULL || m_Def->Type != 10) return false;

	if (src->hairid() > 0)
	{
		m_hairId = src->hairid();
		m_hairColor = src->haircolor();
		setHairColor(m_hairColor, m_hairId);
	}

	if (src->faceid() > 0)
	{
		m_faceId = src->faceid();
		setFaceId(m_faceId);
	}

	if (src->randomnameid() > -1) {
		m_randomNameId = src->randomnameid();
		setRandomNameId(m_randomNameId);
	}

	VillagerAttrib *attrib = static_cast<VillagerAttrib *>(getAttrib());
	if (attrib)
	{
		attrib->setFatigue(src->fatigue());
		attrib->setExtremisVal(src->extremisval());
		attrib->setHugger(src->hunger());
		attrib->setHuggerStatus(src->hungerstatus());
		attrib->setDisposition(src->disposition());
		attrib->setFavor(src->favor());
		attrib->setSpeedOfBreakingBlock(src->speedofbreakingblock());
		attrib->setWakeupTime(src->wakeuptime());
		attrib->setSleepTime(src->sleeptime());
		attrib->setStayupTime(src->stayuptime());
		attrib->setStayingUp(src->isstayingup());
	}

	if (src->bedbindpos())
		m_BedBindPos = Coord3ToWCoord(src->bedbindpos());

	if (src->workbindpos())
		m_WorkBindPos = Coord3ToWCoord(src->workbindpos());

	m_iProfession = src->profession();
	m_bTraitor = src->traitor();

	m_iDemand = src->demand();
	if (src->spawntime())
	{
		setSpawnTime(src->spawntime());
	}

	m_iFoodByTamed = src->foodbytamed();
	m_iceGirl = src->icegirl();
	updateBodyCloth();
	return true;
}

flatbuffers::Offset<FBSave::SectionActor> ActorVillager::save(SAVE_BUFFER_BUILDER &builder)
{
	auto mobdata = ClientMob::saveMob(builder);

	VillagerAttrib *attrib = static_cast<VillagerAttrib *>(getAttrib());

	auto CarryComp = getCarryComponent();
	WORLD_ID CarriedActorID = 0;
	if (CarryComp)
	{
		CarriedActorID = CarryComp->getCarriedActorID();
	}

	FBSave::Coord3 tmpBedBindPos = WCoordToCoord3(m_BedBindPos);
	FBSave::Coord3 tmpWorkBindPos = WCoordToCoord3(m_WorkBindPos);
	auto villager = FBSave::CreateActorVillager(
		builder, 
		mobdata,
		CarriedActorID,
		m_hairId, 
		m_hairColor, 
		m_faceId, 
		(int8_t)attrib->getFatigue(), 
		attrib->getExtremisVal(), 
		&tmpBedBindPos,
		attrib->getHugger(), 
		(int8_t)attrib->getHuggerStatus(), 
		&tmpWorkBindPos, 
		(int8_t)m_iProfession, 
		m_bTraitor, 
		(int8_t)attrib->getDisposition(), 
		attrib->getFavor(), 
		(int8_t)m_iDemand,
		attrib->getSpeadOfBreakingBlock(), 
		(int16_t)attrib->getWakeupTime(), 
		(int16_t)attrib->getSleepTime(), 
		(int16_t)attrib->getStayupTime(), 
		m_spawnTime, 
		attrib->isStayingUp(), 
		m_iFoodByTamed,
		m_randomNameId,
		m_iceGirl);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorVillager, villager.Union());
}

int ActorVillager::getObjType() const
{
	return OBJ_TYPE_VILLAGER;
}

bool ActorVillager::init(int monsterid)
{
	if (!ClientMob::init(monsterid))
		return false;

	auto *attrib = getVillagerAttrib();
	if(attrib)
		attrib->setExtremisVal(GetLuaInterfaceProxy().get_lua_const()->actor_max_extremisval);
	
	return true;
}

void ActorVillager::tick()
{
	ClientMob::tick();
	if (!m_bufferToAdd.empty()&& getBody() && getBody()->getModel())
	{
		addFreezingBuffer();
		m_bufferToAdd.clear();
	}
	//检查野人是否叛逃
	checkDefection();
	//检查起床时间
	checkIsReachWakeUpTime();

	auto CarryComp = getCarryComponent();
	if (CarryComp && !m_pWorld->isRemoteMode())
	{
		auto *actor = CarryComp->getCarriedActor();
		if (actor && getLocoMotion() && actor->getLocoMotion())
		{
			getLocoMotion()->m_RotateYaw = getLocoMotion()->m_PrevRotateYaw = actor->getLocoMotion()->m_RotateYaw + 90;

			auto actorCarryComp = actor->getCarryComponent();
			if (actor && actorCarryComp && /*actor->m_CarryingActor != getObjId()*/!actorCarryComp->checkCarringActor(getObjId()))
			{
				CarryComp->setCarriedActor(NULL);
				WCoord pos = actor->getPosition();
				pos.y += 90;
				int range = BLOCK_SIZE * 3 / 2;
				pos.x += GenRandomInt(range) - GenRandomInt(range);
				pos.z += GenRandomInt(range) - GenRandomInt(range);
				setPosition(pos);
			}

			if (isDead() || needClear())
			{
				if (actor && actorCarryComp &&/*actor->m_CarryingActor == getObjId()*/actorCarryComp->checkCarringActor(getObjId()))
				{
					ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
					if(player)
						player->carryActor(NULL, player->getPosition());
				}
			}
		}
		else if (CarryComp->isCarried())
		{
			CarryComp->setCarriedActor(NULL);
			if (actor)
			{
				WCoord pos = actor->getPosition();
				pos.y += 90;
				int range = BLOCK_SIZE * 3 / 2;
				pos.x += GenRandomInt(range) - GenRandomInt(range);
				pos.z += GenRandomInt(range) - GenRandomInt(range);
				setPosition(pos);
			}
		}
	}
	checkMoveDist();
}

bool ActorVillager::interact(ClientActor*player, bool onshift /* = false */, bool isMobile /* = false */)
{
	auto CarryComp = getCarryComponent();
	if (isSleeping() || (CarryComp && CarryComp->isCarried()))  //睡觉、被扛起状态不能进行交互
		return true;
	
	if(player == NULL) return true;
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		int currItemID = pTempPlayer->getCurToolID();
		pTempPlayer->updateTaskSysProcess(TASKSYS_INTERACT_ACTOR, currItemID, m_Def->ID);
		if (currItemID > 0)	//非空手时不管交互结果怎样都打断行为树
			ResetBTree();
		int status = 0;  // 交互状态

		if (useBandage(pTempPlayer))  //使用绷带
		{
			status = 8;
		}
		else if (!IsExtremis())   //濒死不能进行以下交互
		{
			if (beTamed(pTempPlayer))	//驯服
			{
				status = 2;
			}
			else if (awardedProfession(pTempPlayer))	//授予职业
			{
				status = 3;
			}
			else if (relieveProfession(pTempPlayer)) //解除职业
			{
				status = 4;
			}
			else if (equipItem(pTempPlayer))  //穿装备
			{
				status = 7;
			}
			else if (giveFood(pTempPlayer)) //给与食物
			{
				status = 6;
			}
			else if (giveTool(pTempPlayer))  //给与工具
			{
				status = 5;
			}
			else if (giveGift(pTempPlayer))   //赠与礼物
			{
				status = 9;
			}
			else if (dyeHair(pTempPlayer)) //染发
			{
				status = 10;
			}
			else if (cutHair(pTempPlayer))  //剪发
			{
				status = 11;
			}
			else if (giveCorp(pTempPlayer))  //给与树苗或种子
			{
				status = 12;
			}
		}

		if (status != 0)
		{	// 野人接受道具
			ObserverEvent_ActorItem obevent(getObjId(), status, pTempPlayer->getUin());
			GetObserverEventManager().OnTriggerEvent("Actor.InteractEvent", &obevent);
			// 上报埋点
			if (g_pPlayerCtrl)
				MINIW::ScriptVM::game()->callFunction("PlayerAndActorVillagerInteractReport", "iii", this->getDef()->ID, currItemID, status);
			return true;
		}

		if (carriedActor(pTempPlayer)) //扛起
			return true;

		if (currItemID > 0)
		{
			// 野人拒绝道具
			ObserverEvent_ActorItem obevent(getObjId(), 1, pTempPlayer->getUin());
			GetObserverEventManager().OnTriggerEvent("Actor.InteractEvent", &obevent);
		}
	}
	return false;
}

bool ActorVillager::useBandage(ClientPlayer *player)
{
	if(player == NULL) return false;
	if (player->getCurToolID() != ITEM_BANDAGE)
		return false;

	VillagerAttrib *attrib = getVillagerAttrib();
	if (attrib)
	{
		if (attrib->isExtremis()) //濒死状直接回满濒死值
		{
			attrib->setExtremisVal(attrib->m_iMaxExtremisVal);
			if (g_pPlayerCtrl)
			{
				statisticEvent(31007, g_pPlayerCtrl->getUin());
			}
			player->addSFActivity(SFACTIVITY_WILDSAFE, 1, 1, !player->hasUIControl());
		}		

		auto *fooDef = GetDefManagerProxy()->getFoodDef(ITEM_BANDAGE);
		if (fooDef)
			attrib->addHP((float)fooDef->HealAmount);

		player->shortcutItemUsed();

		if (g_pPlayerCtrl)
			statisticEvent(31005, g_pPlayerCtrl->getUin());

		return true;
	}

	return false;
}

bool ActorVillager::beTamed(ClientPlayer *player)
{
	if (getDemand() != DEMAND_TAMED)  //不是被驯服需求
		return false;

	if (getTamedOwnerID() > 0)		//已经是驯服状态了
		return false;
	
	if(player == NULL) return false;
	if (getFoodByTamed() != player->getCurToolID())  //驯服的道具不满足
		return false;


	setTamedOwnerUin(player->getUin());
	setFoodByTamed(0);
	setDemand(DEMAND_NOT);

	player->shortcutItemUsed();

	if (GetWorldManagerPtr())
	{
		WCoord position = getPosition();
		position.x /= 100;
		position.y /= 100;
		position.z /= 100;
		std::vector<WCoord> PlayerTotem = GetWorldManagerPtr()->getWorldInfoManager()->getAllVillageTotems(player->getUin());
		if (PlayerTotem.size() > 0 && position.distanceTo(PlayerTotem[0])<=128)
		{
			GetWorldManagerPtr()->getWorldInfoManager()->addNewVillager(player->getUin(), getObjId());
			setWorkBindPos(PlayerTotem[0]);
		}
		else 
		{
			std::vector<WCoord> WildTotem = GetWorldManagerPtr()->getWorldInfoManager()->getAllVillageTotems(0);
			if (WildTotem.size() > 0) 
			{
				for (size_t i = 0; i < WildTotem.size(); i++)
				{
					if (position.distanceTo(WildTotem[i]) <= 128)
					{
						WCoord curPos = getWorkBindPos();
						if(player && player->getWorld())
						{
							int id = player->getWorld()->getBlockID(curPos);
							//对于建筑工人，驯服后继续执行之前的建造工作
							if(id != 150005)
							{
								setWorkBindPos(WildTotem[i]);
							}
						}
						BlockVillageTotemIce::AddVillage(m_pWorld, WildTotem[i], getObjId());
						BlockVillageTotemIce::ReBindVillagerWorkPosByVillagerChange(m_pWorld, WildTotem[i], getObjId());
						
					}
				}
			}

			GetWorldManagerPtr()->getWorldInfoManager()->addNewVillager(player->getUin(), getObjId());	
		}

		

		m_spawnTime = GetWorldManagerPtr()->getWorldTime();  //驯服后才设置出生时间
	}	

	PB_PlayerTameActorHC playerTameActorHC;
	playerTameActorHC.set_actorid(getObjId());
	playerTameActorHC.set_playeruin(player->getUin());

	m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYER_TAME_ACTOR_HC, playerTameActorHC, this);

	//随机野人名字id
	int num = GetDefManagerProxy()->getWildmanNameDefNum();
	int rnd = GenRandomInt(num) + 1;
	setRandomNameId(rnd);

	//埋点
	if (g_pPlayerCtrl)
		statisticEvent(31000, g_pPlayerCtrl->getUin());

	player->addSFActivity(SFACTIVITY_TAMEMOB, 3101, 1, !player->hasUIControl());

	return true;
}

bool ActorVillager::awardedProfession(ClientPlayer *player)
{
	if (getProfession() > PROFESSION_NOT)
		return false;
	int oldprofession = getProfession();
	if(player == NULL) return false;
	if (getTamedOwnerID() != player->getUin())
		return false;

	if (!m_Def)
		return false;

	int itemId = player->getCurToolID();
	auto *toolDef = GetDefManagerProxy()->getToolDef(itemId);
	if (!toolDef && itemId != BLOCK_CRAFTTABLE)	//不是工具，也不是工具箱
		return false;
	if (isDoubleWeapon(itemId))
		return false;//村民不允许获得双持武器
	std::string oldPiFeng = getSubModeName(EQUIP_PIFENG);
	std::string oldHead = getSubModeName(EQUIP_HEAD);
	std::string oldBreast = getSubModeName(EQUIP_BREAST);
	bool ret = false;
	if (m_Def->ID != 3234 && this->isIceVillage() && toolDef) //野人转化的村民 临时用它来做冰原村民 转换成可以战斗的人员
	{
		std::map<int, int> type_to_profession;
		type_to_profession[1] = PROFESSION_WOODCUTTER; //樵夫
		type_to_profession[4] = PROFESSION_FARMER;     //农夫
		type_to_profession[6] = PROFESSION_MELEE_GUARD;//近战守卫
		int prefession = -1;
		auto it = type_to_profession.find(toolDef->Type);
		//根据TODO m_Def->ID编号来判断职业：远程守卫Or冰原猎人
		if (IsLongRangeWeaponID(toolDef->ID)) //远程武器类道具 toolDef->Type == 7   
		{
			prefession = PROFESSION_HUNTER;
			ret = true;
		}
		else if (IsArchitectWeaponID(toolDef->ID)) //建造工人
		{
			prefession = PROFESSION_ARCHITECT;
			ret = true;
		}
		else if (it != type_to_profession.end())
		{
			prefession = it->second;
			ret = true;
		}
		if (ret)
		{
			setProfession(prefession);
			player->addSFActivity(SFACTIVITY_WILDPRO, prefession, 1, !player->hasUIControl());
		}
	}
	else if (m_Def->ID == 3200 && toolDef) //野人转化的村民
	{
		if (IsArchitectWeaponID(toolDef->ID)) //建造工人
		{
			ret = true;
			setProfession(PROFESSION_ARCHITECT);
			player->addSFActivity(SFACTIVITY_WILDPRO, PROFESSION_ARCHITECT, 1, !player->hasUIControl());
		}
		else if (toolDef->Type == 1)  //斧子类道具
		{
			ret = true;
			setProfession(PROFESSION_WOODCUTTER);
			player->addSFActivity(SFACTIVITY_WILDPRO, PROFESSION_WOODCUTTER, 1, !player->hasUIControl());
		}
		else if (toolDef->Type == 6) //近战武器类道具
		{
			ret = true;
			setProfession(PROFESSION_MELEE_GUARD);
			player->addSFActivity(SFACTIVITY_WILDPRO, PROFESSION_MELEE_GUARD, 1, !player->hasUIControl());
		}
	}
	else if (m_Def->ID == 3201 && toolDef)  //野人猎手转化的村民
	{
		if (IsArchitectWeaponID(toolDef->ID)) //建造工人
		{
			ret = true;
			setProfession(PROFESSION_ARCHITECT);
			player->addSFActivity(SFACTIVITY_WILDPRO, PROFESSION_ARCHITECT, 1, !player->hasUIControl());
		}
		if (toolDef->Type == 4)  //锄头类道具
		{
			ret = true;
			setProfession(PROFESSION_FARMER);
			player->addSFActivity(SFACTIVITY_WILDPRO, PROFESSION_FARMER, 1, !player->hasUIControl());
		}
		else if (IsLongRangeWeaponID(toolDef->ID)) //远程武器类道具 toolDef->Type == 7
		{
			ret = true;
			setProfession(PROFESSION_REMOTE_GUARD);
			player->addSFActivity(SFACTIVITY_WILDPRO, PROFESSION_REMOTE_GUARD, 1, !player->hasUIControl());
		}
	}
	else if ((m_Def->ID == 3202|| getDef()->ID == 3234) && itemId == BLOCK_CRAFTTABLE)  //对萌宝转化的村民使用工匠台
	{
		ret = true;
		setProfession(PROFESSION_HELPER);
		player->addSFActivity(SFACTIVITY_WILDPRO, PROFESSION_HELPER, 1, !player->hasUIControl());
	}

	if (ret)
	{
		auto *livingAttr1 = player->getLivingAttrib();
		auto *livingAttr2 = getLivingAttrib();

		if (livingAttr1 && livingAttr2)
		{
			auto *grid1 = livingAttr1->getEquipGrid(EQUIP_WEAPON);
			auto *grid2 = livingAttr2->getEquipGrid(EQUIP_WEAPON);

			if(grid2 && grid1)
				grid2->setItem(*grid1);

			if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode())  && grid1)
			{
				if (player->getBackPack())
				{
					int index = player->getBackPack()->getShortcutStartIndex() + player->getCurShortcut();
					player->getBackPack()->afterChangeGrid(index);
				}
				if(grid1) grid1->clear();
			}
			player->shortcutItemUsed();
			if (!grid1->def) //手持装备0,清理手上装备  
				livingAttr1->applyEquips(player->getBody(), EQUIP_WEAPON);
		}

		if (getBody())
		{
			getBody()->showSkin(oldPiFeng.c_str(), false);
			notifyProfressCloth(oldPiFeng, false);
			getBody()->showSkin(oldHead.c_str(), false);
			notifyProfressCloth(oldHead, false);
			getBody()->showSkin(oldBreast.c_str(), false);
			notifyProfressCloth(oldBreast, false);

			std::string piFeng = getSubModeName(EQUIP_PIFENG);
			std::string head = getSubModeName(EQUIP_HEAD);
			std::string breast = getSubModeName(EQUIP_BREAST);
			
			getBody()->showSkin(piFeng.c_str(), true);
			notifyProfressCloth(piFeng, true);
			getBody()->showSkin(head.c_str(), true);
			notifyProfressCloth(head, true);
			getBody()->showSkin(breast.c_str(), true);
			notifyProfressCloth(breast, true);

			if(livingAttr2) livingAttr2->applyEquips(getBody(), EQUIP_WEAPON);
		}

		if (GetWorldManagerPtr())
			GetWorldManagerPtr()->getWorldInfoManager()->redistributeVillagersByProfession(player->getUin(), getProfession());

		if (m_iProfession == PROFESSION_MELEE_GUARD || m_iProfession == PROFESSION_REMOTE_GUARD)
		{
			auto *attrib = getVillagerAttrib();
			if (attrib)
			{
				attrib->setSleepTime(10.0f);
				attrib->setStayupTime(12.0f);
				attrib->setWakeupTime(16.0f);
			}
		}
		if (oldprofession != getProfession())
		{
			int bindID = m_pWorld->getBlock(getWorkBindPos()).getResID();
			if (bindID == 150001) 
			{
				BlockVillageTotemIce::ReBindVillagerWorkPosByVillagerChange(m_pWorld, getWorkBindPos(), getObjId());
			}
			
		}
		//埋点
		if (g_pPlayerCtrl)
			statisticEvent(31002, g_pPlayerCtrl->getUin());

		if (oldprofession != getProfession())
		{
			if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				//通知客机 职业变化
				jsonxx::Object context;
				char objid_str[128];
				sprintf(objid_str, "%lld", getObjId());
				context << "objid" << objid_str;
				context << "profession" << getProfession();
				GetSandBoxManager().sendBroadCast( "PB_ACTOR_VILLAGER_INFO", context.bin(), context.binLen());
			}
		}
	}

	return ret;
}

bool ActorVillager::relieveProfession(ClientPlayer *player)
{
	if (getProfession() == PROFESSION_NOT)
		return false;
	if(player == NULL) return false;
	if (player->getCurToolID() != ITEM_MILK_SUGAR)
		return false;

	if (getTamedOwnerID() != player->getUin())
		return false;

	std::string oldPiFeng = getSubModeName(EQUIP_PIFENG);
	std::string oldHead = getSubModeName(EQUIP_HEAD);
	std::string oldBreast = getSubModeName(EQUIP_BREAST);

	//掉落所有东西
	auto *mobAttrib = dynamic_cast<MobAttrib *>(getAttrib());
	if (mobAttrib)
	{
		mobAttrib->dropBagsItems();
		mobAttrib->dropEquipItems();
	}

	if (m_iProfession == PROFESSION_MELEE_GUARD || m_iProfession == PROFESSION_REMOTE_GUARD)
	{
		auto *attrib = getVillagerAttrib();
		if (attrib)
		{
			attrib->setSleepTime(18.0f);
			attrib->setStayupTime(0.0f);
			attrib->setWakeupTime(6.0f);
		}
	}

	setProfession(PROFESSION_NOT);
	if (getBody())
	{	
		if(mobAttrib)
			mobAttrib->applyEquips(getBody(), MAX_EQUIP_SLOTS);

		getBody()->showSkin(oldPiFeng.c_str(), false);
		notifyProfressCloth(oldPiFeng, false);
		getBody()->showSkin(oldHead.c_str(), false);
		notifyProfressCloth(oldHead, false);
		getBody()->showSkin(oldBreast.c_str(), false);
		notifyProfressCloth(oldBreast, false);

		std::string piFeng = getSubModeName(EQUIP_PIFENG);
		std::string head = getSubModeName(EQUIP_HEAD);
		std::string breast = getSubModeName(EQUIP_BREAST);

		getBody()->showSkin(piFeng.c_str(), true);
		notifyProfressCloth(piFeng, true);
		getBody()->showSkin(head.c_str(), true);
		notifyProfressCloth(head, true); 
		getBody()->showSkin(breast.c_str(), true);
		notifyProfressCloth(breast, true);
	}

	player->shortcutItemUsed();

	if (GetWorldManagerPtr())
		GetWorldManagerPtr()->getWorldInfoManager()->redistributeVillagersByProfession(player->getUin(), getProfession());

	return true;
}

bool ActorVillager::equipItem(ClientPlayer *player)
{
	if (getTamedOwnerID() <= 0)
		return false;
	if(player == NULL) return false;
	auto *toolDef = GetDefManagerProxy()->getToolDef(player->getCurToolID());
	if (!toolDef || toolDef->Type < 8 || toolDef->Type > 11)   //不是能穿的装备
		return false;
	if (toolDef->ID >= ITEM_DIVING_MASK && toolDef->ID <= ITEM_DIVING_FINS_SUPER) return false;// 野人不能穿海洋装备
	if (11232 == toolDef->ID || 12246 == toolDef->ID) return false;//野人不能穿 陨魔假面 野人面具
	//if(getProfession() == PROFESSION_MELEE_GUARD || getProfession() == PROFESSION_REMOTE_GUARD)		//近战守卫、远程守卫
	//{
	//	if (getTamedOwnerID() != player->getUin())		//主人才能换装备
	//		return false;
	//}
	//else if (toolDef->Level > 1 && toolDef->ID != ITEM_GARLAND)							//非近战守卫、远程守卫，只能穿皮甲或者花冠
	//{
	//	return false;
	//}
	if (getTamedOwnerID() != player->getUin())		//主人才能换装备
		return false;

	if(getProfession() == PROFESSION_NOT)		//普通村民
	{
		return false;
	}

	auto *livingAttr1 = player->getLivingAttrib();
	auto *livingAttr2 = getLivingAttrib();

	if (livingAttr1 && livingAttr2)
	{
		auto *grid1 = livingAttr1->getEquipGrid(EQUIP_WEAPON);

		EQUIP_SLOT_TYPE equipType = (EQUIP_SLOT_TYPE)(toolDef->getSlotType()/*toolDef->Type - 8*/);
		auto *grid2 = livingAttr2->getEquipGridWithType(equipType);

		//int throwid = grid2->getItemID();
		if (grid2 && grid2->getItemID() > 0)
		{
			//BackPackGrid throwgrid;
			//SetBackPackGrid(throwgrid, grid2->getItemID(), grid2->getNum(), grid2->getDuration(), grid2->userdata, grid2->getNumEnchant(), 0, grid2->userdata_str.c_str());
			throwItem(*grid2);
		}
			
		std::string name = getSubModeName(equipType);
		getBody()->showSkin(name.c_str(), false);
		notifyProfressCloth(name, false);

		if(grid2 && grid1) grid2->setItem(*grid1);
		livingAttr2->applyEquips(getBody(), equipType);
		

		if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
		{
			if(grid1)
				grid1->clear();

			if (player->getBackPack())
			{
				int index = player->getBackPack()->getShortcutStartIndex() + player->getCurShortcut();
				player->getBackPack()->afterChangeGrid(index);
			}
			player->applyEquips(EQUIP_WEAPON);
		}

		player->shortcutItemUsed();
	}

	return true;
}

void ActorVillager::throwItem(BackPackGrid &grid)
{
	BackPackGrid throwgrid(grid);

	Rainbow::Vector3f dir;
	ClientItem* item = getActorMgr()->spawnItem(getPosition() + WCoord(0, 110, 0), throwgrid);
	if (item == NULL)
		return;

	item->setDelayPickTicks(40);

	if(getLocoMotion() == NULL) return;
	Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
	float r = 30.0f;
	dir = getLocoMotion()->getLookDir();

	motion.x = dir.x * r;
	motion.z = dir.z * r;
	motion.y = dir.y * r + 10.0f;
	r = 2.0f * GenRandomFloat();
	float angle = GenRandomFloat() * 360.0f;
	motion.x += r * CosByAngle(angle);
	motion.z += r * SinByAngle(angle);
	motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;

	grid.clear();
}

bool ActorVillager::giveFood(ClientPlayer *player)
{
	if(player == NULL) return false;
	auto *foodDef = GetDefManagerProxy()->getFoodDef(player->getCurToolID());
	if (!foodDef || foodDef->ChoosePriority <= 0)   //不是能吃的食物
		return false;

	auto *attrib = getVillagerAttrib();
	if (!attrib)
		return false;

	auto *livingAttr = player->getLivingAttrib();

	if (livingAttr)
	{
		auto *grid = livingAttr->getEquipGrid(EQUIP_WEAPON);

		PackContainer *bags = getBags();
		if (bags && grid && grid->getItemID() > 0)
		{
			int putNum = bags->addItem_byGridWithNum(grid, 1);
			if (putNum > 0)
			{
				int num = grid->getNum() - putNum;
				if (num <= 0)
					grid->clear();
				else
					grid->setNum(num);

				if (player->getBody())
					livingAttr->applyEquips(player->getBody(), EQUIP_WEAPON);

				if (player->getBackPack())
				{
					int index = player->getBackPack()->getShortcutStartIndex() + player->getCurShortcut();
					player->getBackPack()->afterChangeGrid(index);
				}
				
				return true;
			}
		}
	}

	return false;
}

bool ActorVillager::giveTool(ClientPlayer *player)
{
	/*if (getTamedOwnerID() != player->getUin())
		return false;*/
	if(player == NULL) return false;
	if (m_iProfession == PROFESSION_HELPER)  //助手不接受任何工具
		return false;

	int itemId = player->getCurToolID();
	auto *toolDef = GetDefManagerProxy()->getToolDef(itemId);
	if (!toolDef)   //不是工具
		return false;

	int needToolType = -1;
	int needToolId = 0;
	if (m_iProfession == PROFESSION_WOODCUTTER && toolDef && (toolDef->Type == 1 || toolDef->Type == 3))
	{
		needToolType = toolDef->Type;
	}
	else if (m_iProfession == PROFESSION_MELEE_GUARD && toolDef && toolDef->Type == 6)
	{
		needToolType = toolDef->Type;
	}
	else if (m_iProfession == PROFESSION_FARMER && toolDef && toolDef->Type == 4)
	{
		needToolType = toolDef->Type;
	}
	else if (m_iProfession == PROFESSION_REMOTE_GUARD && toolDef && IsLongRangeWeaponID(toolDef->ID))//(toolDef->Type == 7 || toolDef->ID == 12002))
	{
		needToolType = 7;
		needToolId = 12002;
	}
	/*else if (getProfession() == PROFESSION_HELPER && itemId == 800)
	{
		needToolId = 800;
	}*/

	if (needToolType == -1 && needToolId == 0)
		return false;

	if (findTool(needToolType, needToolId) != -1)
		return false;

	if (isDoubleWeapon(itemId))
		return false;//村民不允许获得双持武器
	
	auto *livingAttr1 = player->getLivingAttrib();
	auto *livingAttr2 = getLivingAttrib();

	if (livingAttr1 && livingAttr2)
	{
		auto *grid1 = livingAttr1->getEquipGrid(EQUIP_WEAPON);
		auto *grid2 = livingAttr2->getEquipGrid(EQUIP_WEAPON);

		PackContainer *bags = getBags();
		if (bags && grid2 && grid2->getItemID() > 0)
		{
			int hasNum = grid2->getNum();
			int putNum = bags->addItem_byGrid(grid2);
			if (putNum < hasNum) //放不下了 扔掉;
			{
				grid2->setNum(hasNum - putNum);
				throwItem(*grid2);
			}
		}
		
		if(grid2) grid2->setItem(*grid1);

		if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
		{
			if (player->getBackPack())
			{
				int index = player->getBackPack()->getShortcutStartIndex() + player->getCurShortcut();
				player->getBackPack()->afterChangeGrid(index);
			}
			if(grid1) grid1->clear();
		}

		player->shortcutItemUsed();
		if (!grid1->def) //手持装备0,清理手上装备  
			livingAttr1->applyEquips(player->getBody(), EQUIP_WEAPON);
	}

	if (getBody())
	{
		livingAttr2->applyEquips(getBody(), EQUIP_WEAPON);
	}

	return true;
}

bool ActorVillager::giveGift(ClientPlayer *player)
{
	if(player == NULL) return false;
	if (getDemand() != DEMAND_GIFT)
		return false;

	if (getFoodByTamed() != player->getCurToolID())
		return false;

	auto *livingAttr = player->getLivingAttrib();

	if (livingAttr)
	{
		auto *grid = livingAttr->getEquipGrid(EQUIP_WEAPON);

		PackContainer *bags = getBags();
		if (bags && grid && grid->getItemID() > 0)
		{
			bags->addItem_byGrid(grid);
		}

		if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) && grid)
			grid->clear();

		player->shortcutItemUsed();

		setFoodByTamed(0);
		setDemand(DEMAND_NOT);
		return true;
	}

	return false;
}

bool ActorVillager::dyeHair(ClientPlayer *player)
{
	if (getDemand() != DEMAND_HAIR_DYE)
		return false;
	if(player == NULL) return false;
	int itemId = player->getCurToolID();
	if (getFoodByTamed() != itemId)
		return false;

	auto *itemDef = GetDefManagerProxy()->getItemDef(itemId);
	if (!itemDef || itemDef->ItemGroup != ITEM_BONE_POWDER)  //不是染料
		return false;

	unsigned int color;

	MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", 600 + itemId - 11500, 0, &color);

	setHairColor(color, m_hairId);
	auto *attrib = getVillagerAttrib();
	if (attrib)
		attrib->addFavor(10);

	player->shortcutItemUsed();

	//setFoodByTamed(0);
	//setDemand(DEMAND_NOT);
	return true;
}

bool ActorVillager::cutHair(ClientPlayer *player)
{
	if (getDemand() != DEMAND_HAIR_CUT)
		return false;

	int hairId = m_hairId;
	while (hairId == m_hairId)
	{
		hairId = rand() % 3 + 1;
	}

	setHairId(hairId);
	auto *attrib = getVillagerAttrib();
	if (attrib)
		attrib->addFavor(10);
	if(player == NULL) return false;
	player->shortcutItemUsed();

	//setDemand(DEMAND_NOT);
	return true;
}
bool ActorVillager::giveCorp(ClientPlayer *player)
{
	if(player == NULL) return false;
	int profression = getProfession();
	int itemId = player->getCurToolID();
	if (profression == PROFESSION_WOODCUTTER)
	{
		if ( ! isSapling(itemId)) { return false; } // 不是树苗
	}
	else if (profression == PROFESSION_FARMER)
	{
		if (itemId != 11400 && itemId != 236 && itemId != 241 ) { return false; } // 不是农作物
	}
	else { return false; }

	auto *livingAttr = player->getLivingAttrib();
	if (livingAttr)
	{
		BackPackGrid *grid = livingAttr->getEquipGrid(EQUIP_WEAPON);

		PackContainer *bags = getBags();
		if (bags && grid && grid->getNum() > 0)
		{
			int num =  bags->addItem_byGridWithNum(grid,1);
			if (num > 0)
			{
				int index = player->getBackPack()->getShortcutStartIndex() + player->getCurShortcut();
				if(player->getBackPack()) player->getBackPack()->afterChangeGrid(index);
				player->shortcutItemUsed();
				int lastnum = grid->getNum();
				if (lastnum <= 0)
				{
					grid->clear();
				}
			}
		}
	}
	return true;
}

int ActorVillager::findTool(int tooltype, int toolid/* =0 */)
{
	auto *livingAttr = getLivingAttrib();
	if (livingAttr)
	{
		auto *grid = livingAttr->getEquipGrid(EQUIP_WEAPON);
		if(grid){
			int itemId = grid->getItemID();
			if (itemId > 0)
			{
				auto *toolDef = GetDefManagerProxy()->getToolDef(itemId);
				if ((toolDef && toolDef->Type == tooltype) || (toolid > 0 && toolid == itemId))
					return grid->getIndex();
			}
		}
	}

	PackContainer *bags = getBags();
	if (bags)
	{
		for (size_t i = 0; i < bags->m_Grids.size(); i++)
		{
			BackPackGrid &grid = bags->m_Grids[i];
			if (grid.isEmpty())
				continue;

			int itemId = grid.getItemID();
			auto *toolDef = GetDefManagerProxy()->getToolDef(itemId);
			if ((toolDef && toolDef->Type == tooltype) || (toolid > 0 && toolid == itemId))
				return grid.getIndex();
		}
	}

	return -1;
}

bool ActorVillager::carriedActor(ClientPlayer *player)
{
	if(player == NULL) return false;
	if (canCarried(player))
	{
		player->carryActor(this);
		ResetBTree();
		return true;
	}

	return false;
}

bool ActorVillager::canCarried(ClientPlayer *player)
{
	if(player == NULL) return false;
	auto RidComp = player->getRiddenComponent();
	if (RidComp && RidComp->isRiding())
		return false;

	if (player->isSleeping())
		return false;

	auto CarryComp = player->getCarryComponent();
	if (CarryComp && CarryComp->isCarrying())
		return false;

	if (CarryComp && CarryComp->isCarried())
		return false;

	VillagerAttrib *attrib = getVillagerAttrib();
	if (attrib && attrib->isExtremis())  //濒死状态才能扛起
	{
		if (player->getCurToolID() == ITEM_BANDAGE)
			return false;
		else
			return true;
	}

	return false;
}

bool ActorVillager::isDead()
{
	if (getVillagerAttrib())
		return getVillagerAttrib()->isDead();

	else return false;
}

bool ActorVillager::attackedFrom(OneAttackData &atkdata, ClientActor *attacker)
{
	auto CarryComp = getCarryComponent();

	if (CarryComp && CarryComp->isCarried())
	{
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(CarryComp->getCarriedActor());
		if (player)
		{
			bool b = player->attackedFrom(atkdata, attacker);
			if (player->isDead())
				player->carryActor(NULL, player->getPosition());

			return b;
		}
	}

	return ClientMob::attackedFrom(atkdata, attacker);
}

void ActorVillager::onDie()
{
	if (m_NeedClearTicks != -1) //设置血条-1 后和tick都会多次调用，这里保证只能死亡的时候调用一次
	{
		return;
	}
	if(getWorld() == NULL) return;

	//埋点
	if(g_pPlayerCtrl)
		statisticEvent(31004, g_pPlayerCtrl->getUin());

	WorldBed* container = dynamic_cast<WorldBed*>(getWorld()->getContainerMgr()->getContainer(getBedBindPos()));
	if (container)
	{
		container->setBedStatus(ENUM_BED_STATUS_DIED);
	}
	if (GetWorldManagerPtr())
	{
		//更新绑定床状态
		GetWorldManagerPtr()->getWorldInfoManager()->changeVillageBedRelationship(getTamedOwnerID(), getObjId(), getBedBindPos(), ENUM_BED_STATUS_DIED);
		//主人的驯服列表删除
		GetWorldManagerPtr()->getWorldInfoManager()->removeVillager(getTamedOwnerID(), getObjId());
		//重新分配工作点
		GetWorldManagerPtr()->getWorldInfoManager()->redistributeVillagersByProfession(getTamedOwnerID(), getProfession());
	}
	ClientMob::onDie();
}

void ActorVillager::setIsTraitor(bool state)
{
	m_bTraitor = state;
	World * pworld = getWorld();
	if (state && pworld)
	{
		WorldBed* container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(getBedBindPos()));
		if (container && container->getBindActor() == getObjId())
		{
			container->setBedStatus(ENUM_BED_STATUS_ESCAPE);
		}
		if (GetWorldManagerPtr())
		{
			//更新绑定床状态
			GetWorldManagerPtr()->getWorldInfoManager()->changeVillageBedRelationship(getTamedOwnerID(), getObjId(), getBedBindPos(), ENUM_BED_STATUS_ESCAPE);
			//主人的驯服列表删除
			GetWorldManagerPtr()->getWorldInfoManager()->removeVillager(getTamedOwnerID(), getObjId());
			//重新分配工作点
			GetWorldManagerPtr()->getWorldInfoManager()->redistributeVillagersByProfession(getTamedOwnerID(), getProfession());
		}

		//埋点
		if (g_pPlayerCtrl)
			statisticEvent(31001, g_pPlayerCtrl->getUin());
		
	}
}

void ActorVillager::setGuardForPlayer(bool b)
{
	if (m_iProfession == PROFESSION_MELEE_GUARD || m_iProfession == PROFESSION_REMOTE_GUARD
		|| m_iProfession == PROFESSION_HUNTER)
	{
		b_isGuardForPlayer = b;
		if (this->getBody())
		{
			this->getBody()->setHeadDisplayIcon(12711, b ? -1 : 1);//-1是无限长,1是时长为1ms
			this->setFaceId(b ? 3 : 1);//设置表情 战斗号角状态是3 退出是1
		}
	}
}

void ActorVillager::addFreezingBuffer()
{
	auto comp = this->getTemperatureComponent();
	if (comp)
	{
		if (getBody() && getBody()->getModel())
			comp->AddBuff(1034);
		else
			m_bufferToAdd.emplace_back(1034);
	}
}

bool ActorVillager::isFreezing()
{
	auto comp = this->getTemperatureComponent();
	if (comp)
	{
		LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(this->getAttrib());
		return attrib->hasBuff(1033)|| attrib->hasBuff(1034)|| attrib->hasBuff(1037);
	}
	return false;
}

void ActorVillager::setHairColor(unsigned int color, int hairId)
{
	if(getBody())
	{
		int id = hairId;
		if (id <= 0)
			id = m_hairId > 0 ? m_hairId : 1;

		std::string skinname = "head_base_";
		getBody()->setHairColor(skinname.c_str(), color, id);
	
		m_hairColor = color;
		m_hairId = id;

		char hairstr[5];
		sprintf(hairstr, "%d", hairId);
		notifyVillagerChange(2, color, hairstr);
	}	
}

void ActorVillager::setRandHair(int hairId)
{
	if (m_hairId == 0)
	{
		int id = hairId;
		if (id == 0)
		{
			id = rand()%3 + 1;
		}

		setHairId(id);
	}
}

void ActorVillager::setHairId(int hairId)
{
	if (hairId > 0)
		setHairColor(m_hairColor, hairId);
}

/* FaceId
1普通
2闭眼
3生气眼
4高兴眼
5悲伤眼
6黑眼圈
*/
void ActorVillager::setFaceId(int faceId)
{
	if (this->getDef()->ID >= 3233 && this->getDef()->ID <= 3239)
	{
		char path[256];
		for (int i = 1; i <= 18; i++)
		{
			sprintf(path, "face%d", i);
			getBody()->showSkin(path, false);
		}
		m_faceId = faceId;
		if (faceId == 1)//默认正常的表情
		{
			getBody()->showSkin(m_icedefaultEmoji.c_str(), true);
		}
		else
		{
			int off = 12;
			if (m_iceGirl > 0)//女孩的
				off = 7;
			sprintf(path, "face%d", off + (faceId > 6 ? 1 : faceId));
			getBody()->showSkin(path, true);
		}
		notifyVillagerChange(3, faceId);
		return;
	}

	if (getBody() && faceId > 0)
	{
		m_faceId = faceId;
		getBody()->setFaceModel(m_faceId);
		notifyVillagerChange(3, faceId);
	}
}

void ActorVillager::setRandomNameId(int nameId)
{
	if (getBody() && nameId > -1)
	{
		m_randomNameId = nameId;
		const char* wildname = GetDefManagerProxy()->getWildmanNameDef(m_randomNameId);
		if (wildname[0]) setDisplayName(std::string(wildname));
		notifyVillagerChange(4, nameId);
	}
}

std::string ActorVillager::getSubModeName(int slot, int priority, int level)
{
	if (isIceVillage())
	{
		return getIceSubModeName(slot, priority, level);
	}
	char name[64] = {};
	if (slot >= 0 && slot < 5)
	{
		std::string  eqname[6] = { "head" ,"breast","leg","shoe","back" };
		std::string  proname[3] = { "base" ,"pro","eq" };

		if (priority == -1)
		{
			int profess = getProfession();
			int equipid = getEquipItem(slot);
			if (equipid != 0)
			{
				priority = 2;
				const ToolDef * mobtool = GetDefManagerProxy()->getToolDef(equipid);
				if (mobtool)
				{
					level = mobtool->Level;
				}
			}
			else if (profess != 0)
			{
				priority = 1;
				level = profess;
			}
		}

		if (priority == 2 && slot < 4) //eq
		{
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[2].c_str(), level);
		}
		else if (priority == 1 && (slot == 0 || slot == 4)) //pro
		{
			if (7 == m_iProfession) //建造师
			{
				if (slot == 0)
					sprintf(name, "%s", "head_pro_5");
				else
					sprintf(name, "%s","back_pro_9");
			}
			else
			{
				sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), level);
			}
		}
		else
		{
			if (slot == 0)//头部
			{
				sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[0].c_str(), ((getDefID() == 3200 || getDefID() == 3202) ? m_hairId : m_hairId + 3));
			}
			else if (7 == m_iProfession && 1== slot) //建造师
			{
				sprintf(name, "%s", "breast_pro_9");
			}
			else
			{
				sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[0].c_str(), (getDefID() == 3202 ? 3 : (getDefID() == 3200 ? 1 : 2)));
			}
		}
	}

	return std::string(name);
}

std::string ActorVillager::getIceSubModeName(int slot, int priority /*= -1*/, int level /*= 1*/)
{
	char name[64] = {};
	if (slot < 0 || slot >= 5)
		return std::string(name);
	
	std::string  eqname[6] = { "head" ,"breast","leg","shoe","back" };
	std::string  proname[3] = { "base" ,"pro","eq" };
	//https://pixso.cn/app/board/-4PS6t2X8MdEHS5BwbyL1A?isBoard=1 模型资源说明  参考 mobs.lua MobIceModleinit
	if (priority == -1)
	{
		int equipid = getEquipItem(slot);
		if (equipid != 0)
		{
			priority = 2;
			const ToolDef* mobtool = GetDefManagerProxy()->getToolDef(equipid);
			if (mobtool)
			{
				level = mobtool->Level; //csv 填表的 
				sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[2].c_str(), level); //head_eq_1
				return std::string(name); //玩家给村民 装备的模型名称
			}
		}
	}
	//村民根据职业默认的模型名称
	int profess = getProfession();
	//head_base_1
	if (0 == profess)
	{
		if (1 == slot) //breast
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[0].c_str(), 1);
		else
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[0].c_str(), 1 + m_iceGirl);
	}
	//head_pro_1
	else if (1 == profess) //樵夫
	{
		if (4 == slot) //back
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 1);
		else
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 1 + m_iceGirl);
	}
	else if (2 == profess) //近战守卫
	{
		if (4 == slot) //back
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 3);
		else
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 3 + m_iceGirl);
	}
	else if (3 == profess) //农夫
	{
		if (4 == slot) //back
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 6);
		else
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 5 + m_iceGirl);
	}
	else if (5 == profess) //助手
	{
		if (1 == slot) //breast
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[0].c_str(), 2);
		else if (4 == slot) //back
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 5);
		else
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[0].c_str(), 1 + m_iceGirl);
	}
	else if (6 == profess) //猎人
	{
		if (4 == slot) //back
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 8);
		else
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 7 + m_iceGirl);
	}
	else if (7 == profess) //建造师
	{
		if (4 == slot) //back
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 9);
		else
			sprintf(name, "%s_%s_%d", eqname[slot].c_str(), proname[1].c_str(), 9 + m_iceGirl);
	}
	return std::string(name);
}

void ActorVillager::updateBodyCloth()
{
	if (isIceVillage())
	{
		//冰原村民重置成基础模型
		this->showAllSkins(false);
		this->showSkin("body", true);//通用
		this->showSkin("shoe_base1", true);//通用
		if (m_faceId == 0) m_faceId = 1;
		this->setFaceId(m_faceId);
		
		int temp = m_iProfession;
		for (int slot = 0; slot < EQUIP_WEAPON; slot++)
		{
			//先隐藏没职业的模型
			m_iProfession = 0;
			this->showSkin(getSubModeName(slot).c_str(), false); 

			//自定义模型 跳过 否则会出现同位置的普通装备 
			int equipId = getEquipItem(slot);
			if (getBody() && getBody()->isCustomEquip(equipId))
				continue;
			m_iProfession = temp;
			this->showSkin(getSubModeName(slot).c_str(), true);//根据是否有职业显示相应的模型
		}
		return;
	}

	for (int slot = 0; slot < EQUIP_WEAPON; slot++)
	{
		//自定义模型 跳过 否则会出现同位置的普通装备 
		int equipId = getEquipItem(slot);
		if (getBody() && getBody()->isCustomEquip(equipId))
			continue;

		this->showSkin(getSubModeName(slot).c_str(), true);
	}
}
void ActorVillager::notifyProfressCloth(std::string name, bool bshow)
{
	this->notifyVillagerClothChange(name, bshow);
}
void ActorVillager::checkDefection()
{
	if (getIsTraitor())
	{
		setNeedClear();
		generateKeepsakePackage(true);
	}
}

void ActorVillager::generateKeepsakePackage(bool isDefect)
{
	ClientPlayer *player = getTamedOwner();
	if (player)
	{
		//野人死亡或者叛逃，生成野人纪念包裹
		WCoord aMobPos = getPosition();
		float x=0, y=0, z=0;
		getFaceDir(x, y, z);
		if (abs(x) >= abs(z))
		{
			x > 0.0f ? aMobPos.x = aMobPos.x + 1 : aMobPos.x = aMobPos.x - 1;
		}
		else
		{
			z > 0.0f ? aMobPos.z = aMobPos.z + 1 : aMobPos.z = aMobPos.z - 1;
		}
		ClientMob *aMob = getActorMgr()->spawnMob(aMobPos, 3203, false, false);
		if(aMob == NULL) return ;
		//根据野人类型添加纪念品，并附带野人信息
		int keepsakeId = 0;
		if (getDef()->ID == 3200)
		{
			keepsakeId = 12595;
		}
		else if (getDef()->ID == 3201)
		{
			keepsakeId = 12596;
		}
		else if (getDef()->ID == 3202|| getDef()->ID == 3204)
		{
			keepsakeId = 12597;
		}
		else if (getDef()->ID>=3233&& getDef()->ID <=3239)
		{
			keepsakeId = 12624;
		}
		aMob->addBagsItem(keepsakeId, 1);
		//纪念内容（野人名字|野人雇主名字|野人职业|野人性格|野人存活天数|野人是否叛逃|野人死亡/叛逃原因）
		std::string mobName = getDisplayName();
		std::string mobOwnerName = "";
		if (getTamedOwner() != NULL)
		{
			mobOwnerName = getTamedOwner()->getNickname();
			aMob->setTamedOwnerUin(getTamedOwnerID());
		}
		int mobProfession = getProfession();
		int mobDisposition = getVillagerAttrib() != NULL ? getVillagerAttrib()->getDisposition() : 0;
		int mobLivingDay = 0;
		if (GetWorldManagerPtr())
		{
			mobLivingDay = (GetWorldManagerPtr()->getWorldTime() - getSpawnTime()) / TICKS_ONEDAY + 1;
		}
		int mobIsDefect = isDefect ? 1 : 0;
		int mobReason = getLastAttackedType();
		if(getVillagerAttrib())
		{
			if (!mobIsDefect)
			{
				mobReason = getVillagerAttrib()->getImmuneTypeByAttackType(mobReason);
			}
			else
			{
				mobReason = getVillagerAttrib()->getHuggerStatus() > getVillagerAttrib()->getFatigue() ? 1 : 2;
			}
		}
		char keepsakeStr[512];
		sprintf(keepsakeStr, "%s|%s|%d|%d|%d|%d|%d", mobName.c_str(), mobOwnerName.c_str(), mobProfession, mobDisposition, mobLivingDay, mobIsDefect, mobReason);
		if(aMob->getBags() && aMob->getBags()->index2Grid(0)) aMob->getBags()->index2Grid(0)->setUserdataStr(keepsakeStr);
		//如果是叛逃，还要添加信纸，并附带叛逃信息
		if (isDefect && aMob->getBags())
		{
			aMob->addBagsItem(ITEM_LETTERS, 1);
			BackPackGrid* grid = aMob->getBags()->index2Grid(1);
			if(grid) 
			{
				grid->setUserDataInt(mobReason);
				grid->setUserdataStr(mobName.c_str());
			}
		}
		//最后添加野人物品
		MobAttrib *attrib = static_cast<MobAttrib *>(getAttrib());
		MobAttrib *mobAttrib = static_cast<MobAttrib *>(aMob->getAttrib());
		if (mobAttrib && mobAttrib->getBags())
		{
			if(attrib && attrib->getBags()){
				//背包物品
				for (int i = 0; i < getBags()->getGridCount(); i++)
				{
					BackPackGrid *grid = getBags()->index2Grid(i);
					if (grid)
					{
						int itemId = grid->getItemID();
						if (itemId && itemId > 0)
						{
							int itemCount = grid->getNum();
							mobAttrib->getBags()->addItem_byGrid(grid);
						}
					}
				}
			}
			if (attrib)
			{
				//装备物品
				for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
				{
					BackPackGrid* grid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)i);
					if (grid)
					{
						int itemId = grid->getItemID();
						if (11810 != itemId && itemId > 0)
						{
							mobAttrib->getBags()->addItem_byGrid(grid);
						}
					}
				}
			}
		}
	}
}

void ActorVillager::sitInChair(const WCoord blockpos) // 设置坐下
{
	if(getLocoMotion() == NULL) return;
	getLocoMotion()->m_yOffset = 0;

	if (m_pWorld  &&  m_pWorld->blockExists(blockpos))
	{
		int blockdata = m_pWorld->getBlockData(blockpos);
		int dir = blockdata & 3;
		BedLogicHandle::setBedOccupied(m_pWorld, blockpos, true); // 设置方块占用
		WCoord playerpos = blockpos * BLOCK_SIZE + WCoord(50, 60, 50);
		getLocoMotion()->setPosition(playerpos.x, playerpos.y, playerpos.z);
		float YawArray[4] = { 90.0f, -90.0f, 0.0f, 180.0f };
		getLocoMotion()->m_RotateYaw = YawArray[dir];
		getLocoMotion()->m_RotationPitch = 0;
	}
	else
	{
		WCoord playerpos = blockpos * BLOCK_SIZE + WCoord(50, 50, 50);
		getLocoMotion()->setPosition(playerpos.x, playerpos.y, playerpos.z);
		getLocoMotion()->m_RotateYaw = 0.0f;
		getLocoMotion()->m_PrevRotatePitch = 0;
	}

	setSitting(true);
	getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
	if(getBody()) getBody()->resetPos();

}

void ActorVillager::sitInBackround() // 设置坐下
{
	if(getLocoMotion() == NULL) return;
	getLocoMotion()->m_yOffset = 0;

	WCoord Pos = this->getPosition();
	getLocoMotion()->setPosition(Pos.x, Pos.y, Pos.z);
	getLocoMotion()->m_RotateYaw = 0.0f;
	getLocoMotion()->m_PrevRotatePitch = 0;

	setSitting(true);
	getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
	if(getBody()) getBody()->resetPos();
}



void ActorVillager::standUp() // 原地站起
{
	if(getLocoMotion() == NULL) return;
	updateBound();
	getLocoMotion()->m_yOffset = 0;

	WCoord blockpos = CoordDivBlock(getPosition());
	WCoord newpos = blockpos;
	if(m_pWorld == NULL) return;
	BlockMaterial *mtl =  g_BlockMtlMgr.getMaterial(m_pWorld->getBlockID(blockpos));
	BlockChair *  chair = dynamic_cast<BlockChair *>(mtl);
	if (chair)
	{
		BedLogicHandle::setBedOccupied(m_pWorld, blockpos, false);// 取消方块占用
		bool succeed = BedLogicHandle::getNearestEmptyChunkCoordinates(newpos, m_pWorld, blockpos, 0);
		if (!succeed)
		{
			newpos = TopCoord(blockpos);
		}
		getLocoMotion()->gotoPosition(BlockBottomCenter(newpos), getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
	}
	else
	{
		newpos = TopCoord(blockpos);
		getLocoMotion()->gotoPosition(BlockBottomCenter(newpos), getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
	}
}

int ActorVillager::sleepInBed(const WCoord &blockpos)
{
	int ret = ClientMob::sleepInBed(blockpos);
	if (ret == 0)
	{
		//播放特效和音效
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect(BODYFX_AI_SLEEP);

		}
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3402.sleep", 0.8f + GenRandomFloat()*0.4f, 0.8f + GenRandomFloat()*0.4f);
		}
		setFaceId(2);
		//针对野人睡觉被床顶上方块挤开的问题
		getLocoMotion()->setBound(20, 20);
	}
	return ret;
}


void ActorVillager::wakeUp()
{
	ClientMob::wakeUp();
	//停止特效和音效
	auto effectComponent = getEffectComponent();
	if (effectComponent)
	{
		effectComponent->stopBodyEffect(BODYFX_AI_SLEEP);
	}
	auto sound = getSoundComponent();
	if (sound)
	{
		sound->stopSoundFollowActor("ent.3402.sleep");
	}
	setFaceId(1);
	//针对野人睡觉被床顶上方块挤开的问题
	updateBound();
}

void ActorVillager::beHurtOnTrigger(float hp)
{
	auto triggerComponent = getTriggerComponent();
	if (triggerComponent)
	{
		triggerComponent->beHurtOnTrigger_Base(hp);
	}
	
	if (this->getTamedOwnerID() == getBeHurtTargetID())
	{
		return;
	}
	//给周围守卫+勇敢的野人发求救消息
	if (GetWorldManagerPtr() && m_pWorld && m_pWorld->getActorMgr())
	{
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		if (!actorMgr) return ;
		std::vector<IClientActor*> nearMobs;
		WCoord pos = getPosition();
		m_pWorld->findNearActors(nearMobs, pos, 1, OBJ_TYPE_VILLAGER);
		std::map<int, VillageInfo> out;
		GetWorldManagerPtr()->getWorldInfoManager()->getAllVillageInfo(out);
		for (size_t i = 0; i < nearMobs.size(); i++)
		{
			auto mob = nearMobs[i];
			if (!mob || getBeHurtTargetID() == mob->getObjId() || mob == this)
				continue;
			std::map<int, VillageInfo>::iterator iter = out.begin();
			for (; iter != out.end(); ++iter)
			{
				auto widSet = iter->second.bindVillagers;
				auto vObj = widSet.find((WORLD_ID)mob->getObjId());
				if (vObj != widSet.end())
				{
					AIFunctionMgr* aiModule = GET_SUB_SYSTEM(AIFunctionMgr);
					ActorVillager* otherVillager = dynamic_cast<ActorVillager*>(actorMgr->findActorByWID(mob->getObjId()));
					if (otherVillager && aiModule && (otherVillager->getProfession() == PROFESSION_MELEE_GUARD || otherVillager->getProfession() == PROFESSION_REMOTE_GUARD || aiModule->getDisposition(otherVillager) == DISPOSITION_BRAVE))
					{
						ClientMob* otherMob = dynamic_cast<ClientMob*>(actorMgr->findActorByWID(getBeHurtTargetID()));
						if (!otherMob)
						{
							continue;
						}
						long long attacker_objId = getBeHurtTargetID();
						WCoord pos = otherMob->getPosition();
						ClientActor* pShooter = otherMob->getShootingActor();
						if (pShooter)
						{
							attacker_objId = pShooter->getObjId();
							pos = pShooter->getPosition();
						}

						// 观察者事件接口 - 援助
						ObserverEvent obevent;
						obevent.SetData_EventObj(mob->getObjId());
						obevent.SetData_ToObj(attacker_objId);
						obevent.SetData_HelperObjid(getObjId());
						obevent.SetData_Position((float)pos.x, (float)pos.y, (float)pos.z);
						GetObserverEventManager().OnTriggerEvent("Actor.ReqHelp", &obevent);
					}
				}
			}
		}
	}
}

void ActorVillager::warnThief(ClientActor* thief)
{
	// 警告小偷
	WCoord pos = thief->getPosition();
	ObserverEvent obevent;
	obevent.SetData_EventObj(getObjId());
	obevent.SetData_ToObj(thief->getObjId());
	obevent.SetData_Position((float)pos.x, (float)pos.y, (float)pos.z);
	GetObserverEventManager().OnTriggerEvent("Actor.WarnThief", &obevent);
}

void ActorVillager::attackThief(ClientActor* thief)
{
	// 攻击小偷
	WCoord pos = thief->getPosition();
	ObserverEvent obevent;
	obevent.SetData_EventObj(getObjId());
	obevent.SetData_ToObj(thief->getObjId());
	obevent.SetData_Position((float)pos.x, (float)pos.y, (float)pos.z);
	GetObserverEventManager().OnTriggerEvent("Actor.AttackThief", &obevent);
}

void ActorVillager::enterWorld(World *pworld)
{
	ClientMob::enterWorld(pworld);
	if (isSleeping() && getLocoMotion())
	{
		//针对野人睡觉被床顶上方块挤开的问题
		getLocoMotion()->setBound(20,20);
	}
}

bool ActorVillager::isIceVillage() const
{
	auto id = getMonsterDef()->ID;
	return (id >= 3233) && (id <= 3239);
}

void ActorVillager::saveTickTime(unsigned int idx)
{
	m_saveTimeTick[idx] = m_pWorld->m_CurWorldTick;
}

unsigned int ActorVillager::getTickDuration(unsigned int idx) const
{
	if (0 == m_saveTimeTick[idx])
		return 20 * 60 * 10; //10分钟，第一次的cd可以直接生效
	return m_pWorld->m_CurWorldTick - m_saveTimeTick[idx];
}

void ActorVillager::setDemand(short demand)
{
	int oldDemand = m_iDemand;
	m_iDemand = demand;

	bool needChange = false;
	bool isShow = false;
	if (m_iDemand == DEMAND_HAIR_CUT || m_iDemand == DEMAND_HAIR_DYE)
	{
		needChange = true;
		isShow = false;
	}
	else if (oldDemand == DEMAND_HAIR_CUT || oldDemand == DEMAND_HAIR_DYE)
	{
		needChange = true;
		isShow = true;
	}

	if (needChange)
	{
		std::string head = getSubModeName(EQUIP_HEAD);
		if(getBody()) getBody()->showSkin(head.c_str(), isShow);
		notifyProfressCloth(head, isShow);
	}
}

bool ActorVillager::userNameBrand(int operateUin)
{
	if (getTamedOwnerID() > 0 && getTamedOwnerID() == operateUin)
	{
		long long objid = getObjId();
		MINIW::ScriptVM::game()->callFunction("MobModifyWildmanName", "d", (double)objid);
		return true;
	}
	else
	{
		return false;
	}
}

void ActorVillager::setCoolingTime(int ctype, int time)
{
	if (ctype < ARRAY_ELEMENTS(m_coolingTimes))
	{
		m_coolingTimes[ctype] = time;
	}
}

int ActorVillager::getCoolingTime(int ctype)
{
	if (ctype < ARRAY_ELEMENTS(m_coolingTimes))
	{
		return m_coolingTimes[ctype];
	}

	return 0;
}

bool ActorVillager::canDespawn()
{
	return false;
}

bool ActorVillager::IsExtremis()
{
	auto *attrib = getVillagerAttrib();
	if (attrib)
	{
		return attrib->isExtremis();
	}

	return false;
}

void ActorVillager::checkExtremisForBed(bool bExtremis)
{
	World * pworld = getWorld();
	if (pworld)
	{
		WorldBed* container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(getBedBindPos()));
		if (container && container->getBindActor() == getObjId())
		{
			if (bExtremis)
			{
				container->setBedStatus(ENUM_BED_STATUS_DIING);
			}
			else
			{
				container->setBedStatus(ENUM_BED_STATUS_NORMAL);
			}
		}

		if (bExtremis)
		{
			//更新绑定床状态
			GetWorldManagerPtr()->getWorldInfoManager()->changeVillageBedRelationship(getTamedOwnerID(), getObjId(), getBedBindPos(), ENUM_BED_STATUS_DIING);
		}
		else
		{
			//更新绑定床状态
			GetWorldManagerPtr()->getWorldInfoManager()->changeVillageBedRelationship(getTamedOwnerID(), getObjId(), getBedBindPos(), ENUM_BED_STATUS_NORMAL);
		}
	}
}

void ActorVillager::addHuggerByType(const char* str, int baseVal)
{
	int val = 0;
	auto *attrib = getVillagerAttrib();
	if (attrib)
	{
		MINIW::ScriptVM::game()->callFunction("AIFunctionDefs_GetHungryReduceByType", "s>i", str, &val);
		attrib->addHugger(val * baseVal);
	}
}


void ActorVillager::checkIsReachWakeUpTime()
{
	if (m_bCheckWakeUpTime)
	{
		auto *attrib = getVillagerAttrib();
		if (attrib && getWorld())
		{
			float wakeUpTime = attrib->getWakeupTime();
			float curTime = getWorld()->getHours();
			//到了起床时间
			if (wakeUpTime <= curTime)
			{
				//熬夜了
				ClientMob *mob = dynamic_cast<ClientMob*>(this);
				if (mob)
				{
					bool isSleeping = mob->isSleeping();
					if (!isSleeping)
					{
						attrib->setFatigue(attrib->getFatigue() + 1);
					}
				}
				//饱食度不够
				float fullUp = attrib->getFood();
				if (fullUp <= 0.01f)
				{
					//饥饿层数+1
					attrib->setHuggerStatus(attrib->getHuggerStatus() + 1);
				}
				m_bCheckWakeUpTime = false;
			}
			else
			{
				m_bCheckWakeUpTime = true;
			}
		}
	}
}

void ActorVillager::checkMoveDist()
{
	if (m_pWorld->isRemoteMode())
		return;

	m_iCheckMoveDistTick--;
	if (m_iCheckMoveDistTick <= 0)
	{
		m_iCheckMoveDistTick = 20;

		if (m_OldPos.y >= 0 && getPosition() != m_OldPos)
		{
			int distByBlock = (int)(Distance(getPosition().toVector3(), m_OldPos.toVector3()) / BLOCK_SIZE);
			if (distByBlock > 0)
			{
				ActorLocoMotion *loc = getLocoMotion();
				if (loc)
				{
					bool bInWater = ((loc->m_InWater || loc->m_InLava || loc->m_InHoney) && !getFlying());
					if (bInWater)
						addHuggerByType("swim", distByBlock);
					else
						addHuggerByType("walk", distByBlock);
				}
			}
		}

		m_OldPos = getPosition();
	}
}

void ActorVillager::statisticEvent(int eventid, int uin)
{
	if (!g_pPlayerCtrl)
		return;
	WorldDesc *worldDesc = nullptr;
	SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ClientAccountMgr_getCurWorldDesc");
	if (sandboxResult.IsExecSuccessed())
	{
		worldDesc = sandboxResult.GetData_Usertype<WorldDesc>("desc");
	}
	if (!worldDesc)
		return;

	if (!m_Def)
		return;

	char sGameLabel[32] = { 0 };
	sprintf(sGameLabel, "%d", worldDesc->gameLabel);
	char sDefId[8] = { 0 };
	sprintf(sDefId, "%d", m_Def->ID);
	char sWorldId[64] = {0};
	if(worldDesc->fromowid > 0)
		sprintf(sWorldId, "%lld", worldDesc->fromowid);
	else
		sprintf(sWorldId, "%lld", worldDesc->worldid);
	char sObjId[64] = {0};
	sprintf(sObjId, "%lld", getObjId());

	char sProfession[4] = {0};

	if (eventid != 31000)
	{
		sprintf(sProfession, "%d", getProfession());
	}
	g_pPlayerCtrl->statisticToWorld(uin, eventid, "", worldDesc->worldtype,"param_to_str", sGameLabel, sDefId, sWorldId, sObjId, sProfession);
}



int ClientSouvenir::getObjType() const
{
	return OBJ_TYPE_VILLAGESOUVENIR;
}

ActorLocoMotion* ClientSouvenir::newLocoMotion(){

	return 	CreateComponent<SouvenirMotion>("SouvenirMotion");
}

void ClientSouvenir::enterWorld(World *pworld)
{
	ClientMob::enterWorld(pworld);
	if (getLocoMotion() != NULL)
	{
		static_cast<SouvenirMotion *>(getLocoMotion())->attachPhysActor();
	}
}
/*
void ClientSouvenir::leaveWorld(bool keep_inchunk)
{
	#ifdef USE_PHYSX
	static_cast<SouvenirMotion *>(getLocoMotion())->detachPhysActor();
	#endif
	auto bindingActor = getBindingActor();
	if (bindingActor)
	{
		bindingActor->setBindChildren(getObjId(), true);
	}
	ClientActor::leaveWorld(keep_inchunk);
}*/

flatbuffers::Offset<FBSave::SectionActor> ClientSouvenir::save(SAVE_BUFFER_BUILDER &builder)
{
	auto mobdata = ClientMob::saveMob(builder);
	auto souvenir = FBSave::CreateActorSouvenir(builder, mobdata);
	
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorSouvenir, souvenir.Union());
}


bool ClientSouvenir::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorSouvenir *>(srcdata);

	if(!ClientMob::load(src->mobdata(), version))
	{
		return false;
	}
	return true;
}

bool ClientSouvenir::leftClickInteract(ClientActor*player)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		bool ret = false;
		MINIW::ScriptVM::game()->callFunction("F3203_Interact", "u[ClientMob]u[ClientPlayer]>b", this, pTempPlayer, &ret);
		setNeedClear();
	}
	return true;
}

BindActorComponent*  ClientSouvenir::getBindActorCom()
{
	if (!m_pBindActorComponent) {
		m_pBindActorComponent = CreateComponent<ClientSouvenirComponent>("ClientSouvenirComponent");
	}
	if (m_pBindActorComponent == nullptr)
	{
		return nullptr;
	}
	return m_pBindActorComponent;
}
