
#include "ClientItem.h"
#include "container_itemexpo.h"
#include "world.h"
#include "BaseItemMesh.h"
#include "BlockScene.h"
#include "special_blockid.h"
#include "DefManagerProxy.h"
#include "ActorVehicleAssemble.h"
#include "block_tickmgr.h"
#include "BlockMaterialMgr.h"
#include "BlockVisualizer.h"
#include "ClientPipelineActor.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "VehicleWorld.h"
using namespace MINIW;
using namespace Rainbow;
 
ContainerItemExpo::ContainerItemExpo() : WorldContainer(WCoord(0,0,0), 0), m_ItemMesh(NULL)
{
	m_ItemData.clear();
	checkItemTick = 0;
	m_hEffect = NULL;
	m_NeedTick = true;
}

ContainerItemExpo::ContainerItemExpo(const WCoord &blockpos) : WorldContainer(blockpos, 0), m_ItemMesh(NULL)
{
	m_ItemData.clear();
	checkItemTick = 0;
	m_hEffect = NULL;
	m_NeedTick = true;
}

void ContainerItemExpo::setItem(const BackPackGrid *itemdata)
{
	if(m_ItemMesh)
	{
		/*m_ItemMesh->DetachFromScene();
		m_ItemMesh->Release();*/
		m_ItemMesh->DestroyGameObject();
		m_ItemMesh = NULL;
	}

	if(itemdata)
	{
		int userdata = 0;
		int itemid;

		m_ItemData = *itemdata;
		m_ItemData.setNum(1);
		itemid = m_ItemData.getItemID();
		if (itemid == ITEM_COLORED_EGG || itemid == ITEM_COLORED_EGG_SMALL || itemid == ITEM_COLORED_EGGBULLET)
		{
			userdata = (itemid == ITEM_COLORED_EGG) ? 1 : 2;
		}
		//云服主机调用这里会宕机，云服没有必要执行
#ifndef IWORLD_SERVER_BUILD
		m_ItemMesh = ClientItem::createItemModel(m_ItemData.getItemID(), ITEM_MODELDISP_DROP, 1.0f, userdata, EXPO_MESH);
#endif
		countVisualizer = 0;
		//if(m_World) enterWorld(m_World); //没必要重复调用enterworld
		adjustItemMesh();
	}
	else m_ItemData.clear();

	m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
}

ContainerItemExpo::~ContainerItemExpo()
{
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh);
}

int ContainerItemExpo::getObjType() const
{
	return OBJ_TYPE_ITEMEXPO;
}

void ContainerItemExpo::resetEntityBySyncData(std::string skey)
{
	int itemid = m_ItemData.getItemID();
	auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
	if (!itemDef)
		return;

	if (itemDef->MeshType != FULLY_CUSTOM_GEN_MESH && itemDef->MeshType != IMPORT_MODEL_GEN_MESH)
		return;

	if (m_ItemMesh)
	{
		/*m_ItemMesh->DetachFromScene();
		m_ItemMesh->Release();*/
		m_ItemMesh->DestroyGameObject();
		m_ItemMesh = NULL;
	}
	m_ItemMesh = ClientItem::createItemModel(itemid, ITEM_MODELDISP_DROP, 1.0f, 2, EXPO_MESH);
	adjustItemMesh();
}

void ContainerItemExpo::adjustItemMesh()
{
	if (m_World && m_World->onClient() && m_ItemMesh)
	{
		int dir = checkItemPos();
		if (m_ItemMesh)
		{
			m_ItemMesh->SetVisibleDistance(16 * BLOCK_FSIZE);
			m_ItemMesh->UpdateTick(0);
			m_ItemMesh->AttachToScene(m_World->getScene());
		}
	}
}

void ContainerItemExpo::clear()
{
	if (!m_ItemData.isEmpty()) {
		m_ItemData.clear();
	}
}

int ContainerItemExpo::checkItemPos()
{
	int dir = -1;
	if (m_World && m_World->onClient() && m_ItemMesh)
	{
		if (m_vehicleWorld)
		{
			int itemid = m_ItemData.getItemID();
			auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
			dir = m_vehicleWorld->getBlockData(m_BlockPos) & 7;
			Rainbow::Quaternionf quatTarget = Quaternionf::zero; 
			WCoord realPos;
			Rainbow::Quaternionf realRotate;
			WCoord offsetPos = WCoord(0, 0, 0);
			auto pVehicle = static_cast<VehicleWorld*>(m_vehicleWorld)->getActorVehicleAssemble();
			int nJointType = pVehicle->getJointTypeWithPos(m_BlockPos);
			if (4 > dir)
			{
				float yaws[4] = { 90.0f, -90.0f, 0, 180.0f };
				float yaw = yaws[dir];
				float pitch = 0;
				float roll = 0;
				float forward_offset = 4.0f;
				float up_offset = -1.0f;
				if (m_ItemMesh->getModelType() == ITEM_MODEL_OMOD)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						offsetPos.y += -5;
						if (1150 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.y += -20;
							offsetPos.x -= 35;
							offsetPos.z += 40;
						}
						else if (12828 == itemid)
						{
							offsetPos.y += -20;
							m_ItemMesh->SetScale(0.3f);
						}
						else if (12253 == itemid || 12281 == itemid)
						{
							offsetPos.x -= 35;
							offsetPos.z -= 35;
						}
						else if (12283 == itemid)
						{
							offsetPos.y -= 85;
							offsetPos.x -= 45;
							offsetPos.z += 50;
						}
						else if (15506 == itemid)
						{
							offsetPos.y -= 20;
						}
						else if (12830 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.y -= 15;
						}
						else if (10500 == itemid)
						{
							offsetPos.z -= 20;
							offsetPos.y += 10;
						}
						else if (15529 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.z -= 10;
						}
						else if (12583 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.y -= 15;
						}
						else if (12822 == itemid)
						{
							m_ItemMesh->SetScale(0.2f);
							offsetPos.x -= 7;
							offsetPos.y -= 7;
							offsetPos.z -= 7;
						}
						else if (12828 == itemid)
						{
							m_ItemMesh->SetScale(0.1f);
						}
						else if (12827 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.y -= 20;
						}
						else if (15005 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.z -= 15;
							offsetPos.y += 5;
						}
						else if (15004 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.z -= 13;
						}
						else if (12002 == itemid || 12063 == itemid)
						{
							m_ItemMesh->SetScale(0.2f);
							offsetPos.z -= 5;
							offsetPos.y += 5;
						}
						else if (12001 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.y -= 5;
						}
						else if (15508 == itemid || 15519 == itemid || 15520 == itemid || 15507 == itemid)
						{
							offsetPos.y -= 15;
						}
						else if (11315 == itemid)
						{
							m_ItemMesh->SetRotation(0, 0, 90.f);
						}
						else if (11016 == itemid)
						{
							offsetPos.z -= 10;
							offsetPos.y -= 10;
							m_ItemMesh->SetScale(0.3f);
						}
					}
					else
					{
						offsetPos.y += -5;
						if (12828 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.y += -20;
							offsetPos.x += 40;
						}
						else if (12253 == itemid)
						{
							offsetPos.z -= 35;
						}
						else if (12280 == itemid)
						{
							offsetPos.x += 45;
						}
						else if (1150 == itemid)
						{
							offsetPos.y += -20;
							offsetPos.z += 35;
						}
						else if (12281 == itemid)
						{
							offsetPos.x -= 35;
							offsetPos.z -= 35;
							offsetPos.x += 30;
						}
						else if (12283 == itemid)
						{
							offsetPos.y -= 85;
							offsetPos.z += 50;
						}
						else if (15506 == itemid)
						{
							offsetPos.y -= 20;
							offsetPos.x += 45;
						}
						else if (12822 == itemid)
						{
							m_ItemMesh->SetScale(0.2f);
							offsetPos.z -= 7;
							offsetPos.y -= 7;
							offsetPos.x += 35;
						}
						else if (12827 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.x += 40;
							offsetPos.y -= 20;
						}
						else if (15529 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.x += 40;
							offsetPos.z -= 10;
						}
						else if (12583 == itemid)
						{
							offsetPos.x += 40;
							offsetPos.y -= 15;
						}
						else if (1150 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							
						}
						else if (12830 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.y -= 15;
							offsetPos.x += 40;
						}
						else if (15005 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.z -= 15;
							offsetPos.y += 5;
							offsetPos.x += 40;
						}
						else if (15004 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.z -= 13;
							offsetPos.x += 40;
						}
						else if (12002 == itemid|| 12063 == itemid)
						{
							m_ItemMesh->SetScale(0.2f);
							offsetPos.x += 40;
							offsetPos.z -= 5;
							offsetPos.y += 5;
						}
						else if (12001 == itemid)
						{
							m_ItemMesh->SetScale(0.3f);
							offsetPos.x += 40;
							offsetPos.y -= 5;
						}
						else if (15508 == itemid || 15519 == itemid || 15520 == itemid ||15507 == itemid)
						{
							offsetPos.y -= 15;
							offsetPos.x += 40;
						}
						else if (11315 == itemid)
						{
							m_ItemMesh->SetRotation(0, 0, 90);
							offsetPos.x += 40;
						}
						else if (11016 == itemid)
						{
							offsetPos.x += 40;
							offsetPos.z -= 10;
							offsetPos.y -= 10;
							m_ItemMesh->SetScale(0.3f);
						}
						else if (12293 == itemid || 12294 == itemid || 12295 == itemid || 12296 == itemid ||  12298 == itemid || 
						12587 == itemid || 12588 == itemid || 12003 == itemid || 12004 == itemid || 11035 == itemid ||
						12526 == itemid || 12053 == itemid || 12058 == itemid || 11058 == itemid || 12285 == itemid ||
						12282 == itemid || 15003 == itemid || 15008 == itemid || 15007 == itemid || 15002 == itemid ||
						11101 == itemid || 12056 == itemid || 12291 == itemid || 12240 == itemid || 11806 == itemid || 12591 == itemid ||
						817 == itemid || 11025== itemid || 11005 == itemid || 15000 ==itemid || 12289==itemid || 12008== itemid || 12051 ==itemid || 12050 == itemid ||
						11015 == itemid || 11001 ==itemid || 11002==itemid || 11003 ==itemid || 11004 ==itemid || 11011 ==itemid || 11012 ==itemid || 11013 == itemid||
						11014 ==itemid || 11021 ==itemid || 11022 ==itemid || 11023 ==itemid || 11024 ==itemid || 11031 ==itemid || 11032 ==itemid || 11033 ==itemid || 
						11034 == itemid || 12248 == itemid)
						{
							offsetPos.x += 40;
						}
					}
// 					offsetPos.z -= 20;
				}
				else if (m_ItemMesh->getModelType() == ITEM_MODEL_IMAGE)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						offsetPos.x -= 40;
						offsetPos.z -= 40;
					}
					else
					{
// 						offsetPos.x -= 40;
						offsetPos.z -= 40;
					}
				}
				else if (m_ItemMesh->getModelType() == ITEM_MODEL_BLOCK)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						offsetPos.x += 10;
					}
					else
					{
						offsetPos.x += 50;
					}
				}

				if (itemDef && itemDef->MeshType == CUSTOM_GEN_MESH)
				{
					if (itemDef->Icon == "customegg")
					{
						m_ItemMesh->SetScale(0.4f);
						offsetPos.y -= 15;
					}
					else
					{
						m_ItemMesh->SetScale(0.4f);

						forward_offset = 12.5f;
						offsetPos.y -= 8;

						float side_offset = 10.0f;
						offsetPos.x += (int)(side_offset * Rainbow::Cos(yaw + 180.0f));
						offsetPos.z += (int)(side_offset * Rainbow::Sin(yaw)			);
					}
				}
				else if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					m_ItemMesh->SetScale(0.25f);
					offsetPos.y -= 25;
				}
				else if (itemDef && itemDef->MeshType == IMPORT_MODEL_GEN_MESH)
				{
					m_ItemMesh->SetScale(0.5f);
					offsetPos.y -= 25;
				}
// 				offsetPos.x += /*WorldPos::Flt2Fix*/(forward_offset * Rainbow::Sin(-yaw));
// 				offsetPos.z += /*WorldPos::Flt2Fix*/(forward_offset * Cos(yaw + 180.0f));
				
				if (itemid == 12283) quatTarget= AngleEulerToQuaternionf(Vector3f(-90.0f, yaw + 90.0f, 90.0f));
				else if (itemid == 12253 || itemid == 12281) quatTarget= AngleEulerToQuaternionf(Vector3f(0, yaw, 0));
				else if (m_ItemMesh->getModelType() == ITEM_MODEL_IMAGE) quatTarget = AngleEulerToQuaternionf(Vector3f(0, yaw, 0));
				else if(1150 == itemid) quatTarget= AngleEulerToQuaternionf(Vector3f(0, yaw + 180, 0));
				else quatTarget= AngleEulerToQuaternionf(Vector3f(0, yaw + 90.0f, 0));

			}
			else
			{
				float zaws[3] = { 0, 180.0f, 0 };
				float zaw = zaws[dir - 4];
				auto pVehicle = static_cast<VehicleWorld*>(m_vehicleWorld)->getActorVehicleAssemble();

				float forward_offset = 4.0f;
// 				if (dir == DIR_NEG_Y)
// 				{
// 					offsetPos.y -= /*WorldPos::Flt2Fix*/(forward_offset);
// 				}
// 				else
// 				{
// 					offsetPos.y += /*WorldPos::Flt2Fix*/(forward_offset);
// 				}
				float up_offset = 0.0f;

				int itemid = m_ItemData.getItemID();
				auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
				if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					offsetPos.z -= 22;
					m_ItemMesh->SetScale(0.25);
				}

				if (m_ItemMesh->getModelType() == ITEM_MODEL_OMOD)
				{
					offsetPos.y += (int)up_offset;
					if (dir == DIR_NEG_Y)
					{
						if (1150 == itemid)
						{
							offsetPos.y -= 20;
						}
						else if (12828 == itemid)
						{
							offsetPos.y -= 50;
						}
						else if (15506 == itemid)
						{
							offsetPos.x += 10;
							offsetPos.y -= 20;
						}
						else if (12253 == itemid || 12281 == itemid)
						{
							offsetPos.z -= 10;
						}
						else if (12283 == itemid)
						{
							offsetPos.y += 10;
// 							offsetPos.x -= 45;
							offsetPos.z -= 10;
						}
						else if (12822 == itemid)
						{
							m_ItemMesh->SetScale(0.2f);
							offsetPos.y -= 10;
							offsetPos.x += 20;
							offsetPos.z -= 10;
						}
					}
					else
					{
						if (1150 == itemid)
						{
							offsetPos.y -= 20;
							offsetPos.z += 100;
						}
						else if (12828 == itemid)
						{
							offsetPos.x -= 80;
							offsetPos.y -= 30;
						}
						else if (15506 == itemid)
						{
							offsetPos.x -= 90;
							offsetPos.y -= 30;
						}
						else if (12253 == itemid)
						{
							offsetPos.z += 100;
						}
						else if (12281 == itemid)
						{
// 							offsetPos.x -= 10;
// 							offsetPos.y -= 10;
							offsetPos.z += 90;
						}
						else if (12283 == itemid)
						{
							offsetPos.y -= 90;
							offsetPos.z -= 10;
						}
						else if (12822 == itemid)
						{
							m_ItemMesh->SetScale(0.2f);
							offsetPos.x -= 100;
							offsetPos.z -= 10;
						}
					}
				}
				else if (m_ItemMesh->getModelType() == ITEM_MODEL_IMAGE)
				{
					if (nJointType == 3)
					{
						if (dir == DIR_NEG_Y)
						{
// 							offsetPos.x -= 40;
							offsetPos.z += 40;
							offsetPos.y += 50;
						}
						else
						{
// 							offsetPos.x -= 40;
							offsetPos.z += 40;
							offsetPos.y -= 50;
						}
					}
					else
					{
						if (dir == DIR_NEG_Y)
						{
							offsetPos.z -= 10;
						}
						else
						{
							offsetPos.z += 90;
						}
					}
				}
				else if (m_ItemMesh->getModelType() == ITEM_MODEL_BLOCK)
				{
					if (dir == DIR_NEG_Y)
					{
					}
					else
					{
						offsetPos.y -= 100;
					}
				}

				if (itemid == 12253 || itemid == 12281 || m_ItemMesh->getModelType() == ITEM_MODEL_IMAGE)
				{
					quatTarget= AngleEulerToQuaternionf(Vector3f(zaw + 90, 0, 0));
				}
				else if (m_ItemMesh->getModelType() == ITEM_MODEL_BLOCK || itemid == 12283)
				{
					quatTarget = AngleEulerToQuaternionf(Vector3f(zaw, 0, 0));
				}
				else if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					quatTarget = AngleEulerToQuaternionf(Vector3f(zaw, -90, -90));
				}
				else if (1150 == itemid)
				{
					quatTarget = AngleEulerToQuaternionf(Vector3f(zaw + 90, 90, 90));
				}
				else
				{
					quatTarget = AngleEulerToQuaternionf(Vector3f(zaw, 90, 90));
				}
			}
			if (pVehicle->getRealWorldPosAndRotate(dir, m_BlockPos, realPos, quatTarget, realRotate, offsetPos))
			{
				WorldPos pos = realPos.toWorldPos();
				m_ItemMesh->SetPosition(pos);
				m_ItemMesh->SetRotation(realRotate);
			}
		}
		else
		{
			dir = m_World->getBlockData(m_BlockPos) & 7;
			int Block_ID = m_World->getBlockID(m_BlockPos);
			if (dir < 4)
			{
				static float yaws[4] = { 90.0f, -90.0f, 0, 180.0f };
				float yaw = yaws[dir];
				WorldPos pos = BlockCenterCoord(m_BlockPos).toWorldPos();

				float forward_offset = 20.0f;//40.0f;

				if (Block_ID == 1184)
				{
					pos = BlockCenterCoord(TopCoord(m_BlockPos)).toWorldPos();
					int itemid = m_ItemData.getItemID();
					if (itemid == 0) { //如果itemid为0则清空数据
						if (m_ItemMesh)
						{
							//m_ItemMesh->detachFromScene();
							DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh);
							//m_ItemMesh->Release();
							//m_ItemMesh = NULL;
						}
						if (!m_ItemData.isEmpty()) {
							m_ItemData.clear();
						}
						return dir;
					}
					//auto itemDef = GetDefManagerProxy()->getItemDef(itemid);//转轴方块会刷新container,会影响到方块大小
					//if(itemDef)
					//m_ItemMesh=m_ItemMesh->setScale(itemDef->DropScale==0 ? 0.1 :itemDef->DropScale*0.5 ) //没办法正常显示大小
					//m_ItemMesh = ClientItem::createItemModel(itemid, ITEM_MODELDISP_DROP, 2.0f, 2, EXPO_MESH); //虽然能够正常显示大小，但骨头等道具会丢失贴图
					//m_ItemMesh->setScale(m_ItemMesh->getScale());
					//采用的方法是 清空mesh重新create 测试一切正常 
					if (m_ItemMesh)
					{
						//m_ItemMesh->detachFromScene();
						DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh);
						//m_ItemMesh->Release();
						//m_ItemMesh = NULL;
					}
#ifndef IWORLD_SERVER_BUILD
					m_ItemMesh = ClientItem::createItemModel(itemid, ITEM_MODELDISP_DROP, 1.0f, 0, EXPO_MESH);
					m_ItemMesh->SetScale(m_ItemMesh->GetScale() * 2.f);
					m_ItemMesh->SetPosition(pos);
#endif
					return dir;
				}
				/*
				static float side_offset = 10.0f;
				pos.x += side_offset * Cos(yaw+180.0f);
				pos.z += side_offset * Sin(yaw);
				*/

				static float up_offset = -10.0f;
				if (m_ItemMesh->getModelType() == ITEM_MODEL_OMOD) pos.y += (Rainbow::WPOS_T)up_offset;

				int itemid = m_ItemData.getItemID();
				auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
				if (itemDef && itemDef->MeshType == CUSTOM_GEN_MESH)
				{
					if (itemDef->Icon == "customegg")
					{
						m_ItemMesh->SetScale(0.4f);
						pos.y -= 150;
					}
					else
					{
						m_ItemMesh->SetScale(0.4f);

						forward_offset = 125.0f;
						pos.y -= 80;

						float side_offset = 100.0f;
						pos.x += (Rainbow::WPOS_T)(side_offset * Cos(yaw + 180.0f));
						pos.z += (Rainbow::WPOS_T)(side_offset * Sin(yaw));
					}

				}
				else if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					m_ItemMesh->SetScale(0.25);
					pos.y -= 250;
				}
				else if (itemDef && itemDef->MeshType == IMPORT_MODEL_GEN_MESH)
				{
					m_ItemMesh->SetScale(0.3f);
					pos.y -= 300;
				}
				
				pos.x += WorldPos::Flt2Fix(forward_offset * Sin(-yaw));
				pos.z += WorldPos::Flt2Fix(forward_offset * Cos(yaw + 180.0f));
				m_ItemMesh->SetPosition(pos);


				if (itemid == 12283) m_ItemMesh->SetRotation(yaw + 90.0f, -90.0f, 90.0f);
				else if (itemid == 12253) m_ItemMesh->SetRotation(yaw, 0, 0);
				else if (itemid == 12281) m_ItemMesh->SetRotation(yaw, 0, 0);
				else if (m_ItemMesh->getModelType() == ITEM_MODEL_IMAGE) m_ItemMesh->SetRotation(yaw, 0, 0);
				else m_ItemMesh->SetRotation(yaw + 90.0f, 0, 0);
			}
			else
			{
				static float zaws[3] = { 0, 180.0f, 0 };
				float zaw = zaws[dir - 4];


				WorldPos pos = BlockCenterCoord(m_BlockPos).toWorldPos();

				static float forward_offset = 20.0f; // 40.0f;
				if (dir == 4)
				{
					pos.y -= WorldPos::Flt2Fix(forward_offset);
				}
				else
				{
					pos.y += WorldPos::Flt2Fix(forward_offset);
				}
				float up_offset = 0.0f;

				int itemid = m_ItemData.getItemID();
				auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
				if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					pos.z -= 220;
					m_ItemMesh->SetScale(0.25);
				}
				else if (itemDef && itemDef->MeshType == IMPORT_MODEL_GEN_MESH)
				{
					m_ItemMesh->SetScale(0.3f);
					pos.z -= 250;
				}

				if (m_ItemMesh->getModelType() == ITEM_MODEL_OMOD) pos.y += (Rainbow::WPOS_T)up_offset;
				m_ItemMesh->SetPosition(pos);
				//星能展台修改悬浮物品使用setRotation函数
				if (itemid == 12283) m_ItemMesh->SetRotation(0, zaw, 0);
				else if (itemid == 12253) m_ItemMesh->SetRotation(0, zaw + 90, 0);
				else if (itemid == 12281) m_ItemMesh->SetRotation(0, zaw + 90, 0);
				else if (m_ItemMesh->getModelType() == ITEM_MODEL_IMAGE) m_ItemMesh->SetRotation(0, zaw + 90, 0);
				else if (m_ItemMesh->getModelType() == ITEM_MODEL_BLOCK)
				{
					m_ItemMesh->SetRotation(0, zaw, 0);
				}
				else if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
				{
					m_ItemMesh->SetRotation(-90, zaw, -90);
				}
				else if (itemDef && itemDef->MeshType == IMPORT_MODEL_GEN_MESH)
				{
					m_ItemMesh->SetRotation(-90, zaw, -90);
				}
				else
				{
					/*int x, y, z = 0;
					float x1, x2, x3, x4, x5, x6, x7, x8, x9,x10 = 0;
					MINIW::ScriptVM::game()->callFunction("getcactus_force", ">iiiffffffffff", &x, &y, &z,&x1, &x2, &x3, &x4, &x5, &x6, &x7, &x8, &x9,&x10);*/
					
					
					/*if (x != 0)
					{
						m_ItemMesh->SetRotation(x,y, z);*/
						if (GetDefManagerProxy()->isFishNeedUp(itemDef->ID))
						{
							m_ItemMesh->SetRotation(0, 0, 0);
							float scaleNum = 1.0f;
							if (itemDef->ID == ITEM_CRAB)
							{
								scaleNum = 0.5f;
							}
							else if (itemDef->ID == 13622)
							{
								scaleNum = 0.5f;
							}
							else if (itemDef->ID == ITEM_HIPPOCAMPUS)
							{
								scaleNum = 0.12f;
							}
							else if (itemDef->ID == ITEM_SMALL_HIPPOCAMPUS)
							{
								scaleNum = 0.4f;
							}
							else if (itemDef->ID == ITEM_TAME_HIPPOCAMPUS)
							{
								//scaleNum = x5;
							}
							else if (itemDef->ID == ITEM_JELLYFISH)
							{
								scaleNum = 0.2f;
							}
							else if (itemDef->ID == 13627)
							{
								scaleNum = 0.5f;
							}
							else if (itemDef->ID == 13628)
							{
								scaleNum = 0.1f;
							}
							else if (itemDef->ID == 13629)
							{
								scaleNum = 0.14f;
							}
							else if (itemDef->ID == 13630)
							{
								scaleNum = 0.15f;
							}
							m_ItemMesh->SetScale(scaleNum);
						}
						else
						{
							m_ItemMesh->SetRotation(90, zaw, 90);
						}
					//}
					
				}
			}
		}

	}

	return dir;
}

void ContainerItemExpo::enterWorld(World *pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	registerUpdateDisplay();

	adjustItemMesh();
	Block knotblock = pworld->getBlock(m_BlockPos);
	if (pworld && knotblock.getResID() == BLOCK_VISUALIZER)
	{
		if (!getItem().isEmpty())
		{
			auto eff = pworld->getEffectMgr()->playParticleEffectAsync("particles/visualizer.ent", BlockBottomCenter(m_BlockPos), 0, 0, 0, false);//展台特效)
			if (eff) {
				m_hEffect = eff->_ID;
			}
		}
	}
}

void ContainerItemExpo::leaveWorld()
{
	if(m_World->onClient() && m_ItemMesh)
	{
		m_ItemMesh->DetachFromScene();
	}
	Block knotblock = m_World->getBlock(m_BlockPos);
	if (m_World && knotblock.getResID() == BLOCK_VISUALIZER)
	{
		if (m_hEffect != 0) {
			m_World->getEffectMgr()->stopParticleEffectByID(m_hEffect);
			m_hEffect = NULL;
		}
	}
	WorldContainer::leaveWorld();
}

void ContainerItemExpo::dropItems()
{
	dropOneItem(m_ItemData);
}
void ContainerItemExpo::dropItemMesh()
{
	if (m_ItemMesh)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_ItemMesh);
		//m_ItemMesh->DetachFromScene();
		//m_ItemMesh->Release();
		//m_ItemMesh = NULL;
	}

	m_ItemData.clear();

	m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
}
flatbuffers::Offset<FBSave::ChunkContainer> ContainerItemExpo::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerItemExpo(builder, basedata, m_ItemData.save(builder));

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerItemExpo, actor.Union());
}

bool ContainerItemExpo::load(const void *srcdata)
{
	int userdata = 0;
	int itemid;
	auto src = reinterpret_cast<const FBSave::ContainerItemExpo *>(srcdata);
	loadContainerCommon(src->basedata());

	m_ItemData.load(src->item());
	itemid = m_ItemData.getItemID();
	if (itemid == ITEM_COLORED_EGG || itemid == ITEM_COLORED_EGG_SMALL || itemid == ITEM_COLORED_EGGBULLET)
	{
		userdata = (itemid == ITEM_COLORED_EGG) ? 1 : 2;
	}
#ifndef IWORLD_SERVER_BUILD
	if(!m_ItemData.isEmpty()) m_ItemMesh = ClientItem::createItemModel(itemid, ITEM_MODELDISP_DROP, 1.0f, userdata, EXPO_MESH);
#endif
	return true;
}


void ContainerItemExpo::updateTick()
{
	Block knotblock = m_World->getBlock(m_BlockPos);
	if (m_World && knotblock.getResID() == BLOCK_VISUALIZER)
	{
		checkItemTick -= 1;
		if (getItem().isEmpty() && checkItemTick<=0)
		{
			checkItemTick = GenRandomInt(0, 20);
			m_World->getEffectMgr()->stopParticleEffect("particles/visualizer.ent", BlockBottomCenter(m_BlockPos));
			m_hEffect = NULL;
			ContainerItemExpo* container = dynamic_cast<ContainerItemExpo*>(m_World->getContainerMgr()->getContainer(m_BlockPos));
			for (int i = 0; i < 6; i++) {//检测方块的6个方向是否有散落的方块 不包括自己，如果方块掉落在自己身上则不会吸进去
				WCoord pos = NeighborCoord(m_BlockPos, i);

				CollideAABB box;
				box.setPosDim(pos * BLOCK_SIZE, WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
				std::vector<IClientActor*>items;

				m_World->getActorsOfTypeInBox(items, box, OBJ_TYPE_DROPITEM);
				if (!items.empty())
				{
					ClientPipleLineActor* pipelineItem = dynamic_cast<ClientPipleLineActor*>(items[0]);
					if (pipelineItem)
					{
						return;
					}
					ClientItem* item = dynamic_cast<ClientItem*>(items[0]);//获取掉落物品信息
					assert(item);
					if (item && container->getItem().isEmpty() )
					{
						int itemid = item->getItemID();
						BackPackGrid* src = &item->m_ItemData;//物品信息保存在BackPackGrid
						if (src && src->getAttracted() == false)
						{
							src->setAttracted(true);
							setItem(src);//设置该物品信息
							item->onSubtractItem(1, 0);//将物品吸走
							m_World->setBlockData(m_BlockPos, m_World->getBlockData(m_BlockPos) | 8, 3);//吸取物品时通电
							m_World->notifyBlock(DownCoord(m_BlockPos), BLOCK_VISUALIZER, true);
							m_World->getBlockTickMgr()->scheduleBlockUpdate(m_BlockPos, BLOCK_VISUALIZER, 20);
							auto eff = m_World->getEffectMgr()->playParticleEffectAsync("particles/visualizer.ent", BlockBottomCenter(m_BlockPos), 0, 0, 0, false);//展台特效)
							if (eff) {
								m_hEffect = eff->_ID;
							}
							break;
						}
					}
				}

			}
		}
	}

	if (!getItem().isEmpty() && m_World) {
		countVisualizer = countVisualizer + 1;
		if (countVisualizer == 3000) {
			countVisualizer = 0;//减少展台刷新
			m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
		}

	}

	if (m_ItemMesh)
	{
		m_ItemMesh->updateTick();		
// 		if (m_vehicleWorld)
// 		{
// 			checkItemPos();
// 		}
	}
}

void ContainerItemExpo::updateDisplay(float dtime)
{
	if (m_ItemMesh)
	{
		if (m_vehicleWorld)
		{
			checkItemPos();
		}
		unsigned int dtick = TimeToTick(dtime);
		m_ItemMesh->UpdateTick(dtick);
	}
}

