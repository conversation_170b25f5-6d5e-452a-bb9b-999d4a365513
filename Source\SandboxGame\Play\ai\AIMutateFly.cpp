
#include "AIMutateFly.h"
#include "OgreUtils.h"
#include "world.h"
#include "special_blockid.h"
#include "ActorBody.h"
#include "ClientActorManager.h"
#include "coreMisc.h"
#include "OgreScriptLuaVM.h"
#include "navigationpath.h"
#include "ClientMob.h"
#include "FlyLocomotion.h"
//#include "ActorLocoMotion.h"
#include "LivingLocoMotion.h"
#include "ClientActorFuncWrapper.h"
#include "FindComponent.h"

AIMutateFly::AIMutateFly(ClientMob *pActor, int blockid, int prob, float speed):
m_BlockID(blockid),m_nProb(prob), m_fSpeed(speed), m_isLocomotionSave(false)
{
	m_pMobActor = pActor;
	auto functionWrapper = m_pMobActor->getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setImmuneFall(1000000);
	}
	setMutexBits(7);

	m_pLivingLocomotion = dynamic_cast<LivingLocoMotion*>(m_pMobActor->getLocoMotion());

	//�߱�ʲô�����ƶ�������Ӧ����AI�����ã�������Ϊ�˼������߼�
	m_pLivingLocomotion->setMoveAbilityFlag(MoveAbilityType::FlyLoc, true);		//���÷����ƶ�������-��AI�õ�
	m_pLivingLocomotion->setMoveAbilityFlag(MoveAbilityType::LandLoc, true);	//���������ƶ�������-��AI�õ�

	//m_pLivingLocomotion = new FlyLocomotion(pActor);
	//m_pLocomotionSave = NULL;
	/*const MonsterDef *def = m_pMobActor->m_Def;
	m_pLivingLocomotion->setBound((int)(def->Height*def->ModelScale), (int)(def->Width*def->ModelScale));
	m_pLivingLocomotion->setAttackBound(
		(int)(def->HitHeight*def->ModelScale	),
		(int)(def->HitWidth*def->ModelScale		),
		(int)(def->HitThickness*def->ModelScale	));*/
}

AIMutateFly::~AIMutateFly()
{
   /*if (m_pLocomotionSave)
   {
      ENG_DELETE(m_pLocomotionSave);
   }
   else
   {
	  ENG_DELETE(m_pLivingLocomotion);
   }*/
}

bool AIMutateFly::willRun()
{
	if (0 != GenRandomInt(m_nProb))
	{
		return false;
	}

	WCoord tmpPos;
	auto findComponent = m_pMobActor->getFindComponent();
	if (findComponent && findComponent->findFarBlock(tmpPos, m_BlockID, (float)(m_pMobActor->getViewDist() / 100)))
	{
		while(!IsAirBlockID(m_pMobActor->getWorld()->getBlockID(tmpPos)))
		{
			tmpPos.y++;
		}
		m_TargetPos = BlockCenterCoord(tmpPos);
		return true;
	}

	return false;
}

bool AIMutateFly::continueRun()
{
	if (m_pMobActor->getBeHurtTarget())
		return false;

	if(m_TryTick-- < 0)
	{
		return false;
	}

	WCoord nowpos = m_pMobActor->getLocoMotion()->getPosition();
	nowpos -= m_TargetPos;
	int len = (int)nowpos.lengthSquared();
	if(len < (30.0f * 30.0f))
	{
		return false;
	}

	return true;
}

void AIMutateFly::start()
{
	if (!m_pLivingLocomotion)
		return;

	m_TryTick = 100;
	m_isLocomotionSave = true;

	if (m_pMobActor->getNavigator())
	m_pMobActor->getNavigator()->clearPathEntity();

	m_pLivingLocomotion->setLocoMotionType(MoveAbilityType::FlyLoc);
	m_pLivingLocomotion->m_SpeedMultiple = m_fSpeed;
	m_pLivingLocomotion->m_HasTarget = true;
	m_pLivingLocomotion->m_MoveTarget = m_TargetPos + 100 * Rainbow::Vector3f::yAxis;
	m_pLivingLocomotion->setBehaviorOn(BehaviorType::Pursuit);
	m_pLivingLocomotion->m_SpeedInAir = m_pMobActor->getDef()->Speed * m_fSpeed;
	m_pLivingLocomotion->setOnGround(false);
	m_pMobActor->getBody()->setControlRotation(false);

	auto functionWrapper = m_pMobActor->getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setSimulateFly(true);
	}

	m_pMobActor->setFlagBit(ACTORFLAG_FLY, true);
}

void AIMutateFly::reset()
{
	//Probably the start function has not been executed yet ?  
	if (m_isLocomotionSave)
	{
		m_TryTick = 100;
		m_pMobActor->setFlagBit(ACTORFLAG_FLY, false);

		auto functionWrapper = m_pMobActor->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setSimulateFly(false);
		}

		m_pLivingLocomotion->setLocoMotionType(MoveAbilityType::LandLoc);
		m_pMobActor->getBody()->setControlRotation(true);
	}
}

void AIMutateFly::update()
{
	if (m_pLivingLocomotion)
		m_pLivingLocomotion->setOnGround(false);
}