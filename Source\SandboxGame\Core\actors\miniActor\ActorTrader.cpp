
#include "ActorTrader.h"
#include "container_backpack.h"
#include "DefManagerProxy.h"
#include "world.h"
#include "ClientPlayer.h"
#include "ClientActorManager.h"
#include "WorldManager.h"
#include "GameNetManager.h"
#include "ActorCSProto.h"
#include "PlayerControl.h"
//#include "GameEvent.h"
#include "proto_common.h"
#include "special_blockid.h"
#include "RuneDef.h"
#include "MobAttrib.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "SandboxCoreManagers.h"
#include "EffectComponent.h"
#include "LuaInterfaceProxy.h"
const int MAX_TRADER_ITEMS = 12;
ActorTrader::ActorTrader() : ActorContainerMob(NPCTRADE_START_INDEX), m_TourTrader(0), m_SpawnTime(0)
{
}

ActorTrader::~ActorTrader()
{
}

flatbuffers::Offset<FBSave::ActorTrader> ActorTrader::_save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveActorCommon(builder);

	flatbuffers::Offset<FBSave::ItemIndexGrid> grids[MAX_TRADER_ITEMS*2];
	int count = 0;

	for(size_t i=0; i<m_Grids.size(); i++)
	{
		BackPackGrid &payout = m_Grids[i].payout;
		BackPackGrid &obtain = m_Grids[i].obtain;
		if(payout.isEmpty() || obtain.isEmpty()) continue;

		grids[count++] = payout.saveWithIndex(builder);;
		grids[count++] = obtain.saveWithIndex(builder);
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> items = 0;
	if(count > 0) items = builder.CreateVector(grids,count);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> equips = 0;
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> bags = 0;
	float food = 0.0f;

	MobAttrib *attrib = static_cast<MobAttrib *>(getAttrib());
	if (attrib)
	{
		flatbuffers::Offset<FBSave::ItemIndexGrid> grids[MAX_EQUIP_SLOTS];
		count = 0;

		for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
		{
			BackPackGrid *itemgrid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)i);
			if (itemgrid == NULL || itemgrid->isEmpty()) continue;

			grids[count++] = itemgrid->saveWithIndex(builder);
		}
		if (count > 0) equips = builder.CreateVector(grids, count);


		flatbuffers::Offset<FBSave::ItemIndexGrid> baggrids[MAX_BAGS_GRID];
		count = 0;

		auto bagsContainer = attrib->getBags();
		if (bagsContainer)
		{
			for (int i = 0; i < MAX_BAGS_GRID; i++)
			{
				BackPackGrid *itemgrid = bagsContainer->index2Grid(i);
				if (itemgrid == NULL || itemgrid->isEmpty()) continue;

				baggrids[count++] = itemgrid->saveWithIndex(builder);
			}
		}		
		if (count > 0) bags = builder.CreateVector(baggrids, count);

		food = attrib->getFood();
	}
	

	auto npc = FBSave::CreateActorTrader(builder, basedata, m_Def->ID, m_SpawnTime, items, equips, bags, food);

	return FBSave::CreateActorTrader(builder, basedata, m_Def->ID, m_SpawnTime, items, equips, bags, food);
}

flatbuffers::Offset<FBSave::SectionActor> ActorTrader::save(SAVE_BUFFER_BUILDER& builder)
{
	auto npc = _save(builder);
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorTrader, npc.Union());
}

EXPORT_SANDBOXENGINE extern int g_BackgridCheckNumMethod;
bool ActorTrader::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorTrader *>(srcdata);
	if(!init(src->defid()))
	{
		return false;
	}

	loadActorCommon(src->basedata());

	m_SpawnTime = src->spawntime();

	auto items = src->items();
	if(items)
	{
		size_t n = items->size()/2;
		for(size_t i=0; i<n; i++)
		{
			const FBSave::ItemIndexGrid *src = items->Get(i*2+0);

			int offset = (src->index() - NPCTRADE_START_INDEX)/2;

			g_BackgridCheckNumMethod = -1; //不检查消耗
			m_Grids[offset].payout.load(src);
			g_BackgridCheckNumMethod = 0;

			auto src1 = items->Get(i * 2 + 1);
			m_Grids[offset].obtain.load(src1);
			if(src1->itemid() == ITEM_ROCKET_CRAFT && src1->durable() != 0)
				playMotion("rocket_cue", true, 0);
		}
	} 

	MobAttrib *attrib = static_cast<MobAttrib *>(getAttrib());
	if (attrib)
	{
		auto equips = src->equips();
		if (equips)
		{
			BackPackGrid itemgrid;
			for (size_t i = 0; i < equips->size(); i++)
			{
				itemgrid.load(equips->Get(i));

				attrib->equip((EQUIP_SLOT_TYPE)itemgrid.getIndex(), &itemgrid);
			}
		}

		auto bags = src->bags();
		if (bags)
		{
			BackPackGrid itemgrid;
			for (size_t i = 0; i < bags->size(); i++)
			{
				itemgrid.load(bags->Get(i));

				attrib->setBagItem(itemgrid.getIndex(), &itemgrid);
			}
		}

		attrib->setFood(src->food());
	}
	

	return true;
}

bool ActorTrader::init(int monsterid)
{
	if(!ClientMob::init(monsterid)) return false;
	if(GetWorldManagerPtr() == NULL) return false;
	if(monsterid == 3010) m_TourTrader = 1;
	else if(monsterid>=3013 && monsterid<3018) m_TourTrader = 2;
	else m_TourTrader = 0;

	m_SpawnTime = GetWorldManagerPtr()->getWorldTime();

	m_Grids.resize(MAX_TRADER_ITEMS);
	for(size_t i=0; i<m_Grids.size(); i++)
	{
		m_Grids[i].payout.setIndex(NPCTRADE_START_INDEX + i*2 + 0);
		m_Grids[i].obtain.setIndex(NPCTRADE_START_INDEX + i*2 + 1);
	}

	resetItems(true);
	return true;
}

int ActorTrader::getNumGrids()
{
	int num = 0;
	for(size_t i=0; i<m_Grids.size(); i++)
	{
		BackPackGrid &payout = m_Grids[i].payout;
		BackPackGrid &obtain = m_Grids[i].obtain;
		if(!payout.isEmpty() && !obtain.isEmpty())
			num++;
	}
	return num;
}

bool ActorTrader::HasGroup2Grids(int groupid)
{
	for(size_t i=0; i<m_GroupIDs.size(); i++)
	{
		if(m_GroupIDs[i] == groupid) return true;
	}
	return false;
}

void ActorTrader::SetOneGrid(int index, int certainid/* =0 */, int isinit/* =true */)
{
	std::vector<const NpcTradeDef *> availables;
	int totalWeight = 0;
	for(int i = 0, _num = GetDefManagerProxy()->getNpcTradeNum(); i < _num; ++i)
	{
		const NpcTradeDef *def = GetDefManagerProxy()->getNpcTradeDef(i+1);
		if (!def)
			continue;
		const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(def->ItemID);
		if (itemdef && itemdef->CondUnlcokType != 0)
		{
			if(itemdef->CondUnlcokType > 0 && GetWorldManagerPtr() && !GetWorldManagerPtr()->isUnlockItem(itemdef->CondUnlcokType)) continue;
			if (itemdef->CondUnlcokType == -1 && g_pPlayerCtrl && !g_pPlayerCtrl->isUnlockItem(itemdef->CondUnlcokType)) continue;
		}
		if(m_Def->ID == def->NpcID)
		{
			if ((certainid == 0 && !HasGroup2Grids(def->GroupID)) || (certainid > 0 && certainid == def->ItemID))
			{
				availables.push_back(def);
				totalWeight += def->Weight;
			}
		}
	}
	if(totalWeight == 0) return;

	int random = GenRandomInt(totalWeight);
	for(size_t i=0; i<availables.size(); i++)
	{
		int weight = 0;
		for(size_t j=0; j<=i; j++)
		{
			weight += availables[j]->Weight;
		}
		if(random <= weight)
		{
			int payItemId = 0;
			int payItemNum = 0;
			int obtainItemId = 0;
			int obtainItemNum = 0;

			if(availables[i]->TradeType == 0) //收购
			{
				payItemId = availables[i]->ItemID;
				payItemNum = GenRandomInt(availables[i]->NumFloor, availables[i]->NumCeil);
				obtainItemId = STAT_ITEM_ID;																
				obtainItemNum = GenRandomInt(availables[i]->PriceFloor, availables[i]->PriceCeil);
			}
			else if(availables[i]->TradeType == 1)	//出售
			{
				payItemId = STAT_ITEM_ID;
				payItemNum = GenRandomInt(availables[i]->PriceFloor, availables[i]->PriceCeil);
				obtainItemId = availables[i]->ItemID;																
				obtainItemNum = GenRandomInt(availables[i]->NumFloor, availables[i]->NumCeil);
			}
			else if(availables[i]->TradeType == 2)	//物换物
			{
				payItemId = availables[i]->PayItemID;;
				payItemNum = GenRandomInt(availables[i]->PayItemNumFloor, availables[i]->PayItemNumCeil);
				obtainItemId = availables[i]->ItemID;																
				obtainItemNum = GenRandomInt(availables[i]->NumFloor, availables[i]->NumCeil);
			}
			m_GroupIDs.push_back(availables[i]->GroupID);
			int hightNum = payItemNum/256;
			SetBackPackGrid(m_Grids[index].payout, payItemId, payItemNum%256, -1, -1, (void *)(long)(hightNum));
			SetBackPackGrid(m_Grids[index].obtain, obtainItemId, obtainItemNum, availables[i]->LockNum, -1, (void *)(long)(availables[i]->ID));
			afterChangeGrid(m_Grids[index].payout.getIndex());
			afterChangeGrid(m_Grids[index].obtain.getIndex());

			/* 现在不卖附魔道具了   直接卖已经鉴定的符文石
			if(availables[i]->EnchantFlag == 1)
			{
				int cost = enchantRandom(m_Grids[index].obtain);
				if(cost > 0)
				{
					cost += GenRandomInt(availables[i]->EnchPriceFloor, availables[i]->EnchPriceCeil)*cost/100;
					hightNum = cost/256;
					m_Grids[index].payout.addNum(int(cost%256));
					m_Grids[index].payout.userdata = (void*)(long)((int)(size_t)m_Grids[index].payout.userdata + hightNum);

				}
			}*/
			if(isRuneStoneAuthed(availables[i]->ItemID)){//已鉴定的符文石 code by:tanzhenyu

				MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_GenerateAuthedRuneStoneUserdataStr",
					MNSandbox::SandboxContext(nullptr).SetData_Number("authedRuneStoneItemID", availables[i]->ItemID));
				std::string userdatastr = "";
				if (result.IsExecSuccessed())
				{
					userdatastr = result.GetData_String();
				}
				m_Grids[index].obtain.setUserdataStr(userdatastr.c_str());
			}


			if (!isinit && availables[i]->ItemID == ITEM_ROCKET_CRAFT)
			{
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("rocket_cue");
				}
			}
			break;
		}
	}

}

void ActorTrader::resetItems(bool isInit)
{
	// 计算配置表中可用的交易项数量
	int availableCount = 0;
	for(int i = 0, _num = GetDefManagerProxy()->getNpcTradeNum(); i < _num; ++i)
	{
		const NpcTradeDef *def = GetDefManagerProxy()->getNpcTradeDef(i+1);
		if (!def) continue;
		
		// 应用相同的过滤条件
		const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(def->ItemID);
		if (itemdef && itemdef->CondUnlcokType != 0)
		{
			if(itemdef->CondUnlcokType > 0 && GetWorldManagerPtr() && !GetWorldManagerPtr()->isUnlockItem(itemdef->CondUnlcokType)) continue;
			if (itemdef->CondUnlcokType == -1 && g_pPlayerCtrl && !g_pPlayerCtrl->isUnlockItem(itemdef->CondUnlcokType)) continue;
		}
		
		if(m_Def->ID == def->NpcID)  // 匹配当前NPC
		{
			availableCount++;
		}
	}
	
	// 使用配置表中的实际数量，最少3个，最多为网格大小
	size_t num = availableCount > 3 ? availableCount : 3;
	if(!isInit && GenRandomInt(100)<10)
	{
		num++;
	}
	if(num > m_Grids.size()) num = m_Grids.size();

	for(size_t i=0; i<m_Grids.size(); i++)
	{
		SellItem &grid = m_Grids[i];

		SetBackPackGrid(grid.payout, 0, 0);
		SetBackPackGrid(grid.obtain, 0, 0);

		afterChangeGrid(grid.payout.getIndex());
		afterChangeGrid(grid.obtain.getIndex());
	}
	m_GroupIDs.clear();

	if (!isInit)
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("rocket_cue");
		}
	}
	for(size_t i=0; i<num; i++)
	{
		SetOneGrid(int(i), 0, isInit);
	}
}

int ActorTrader::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	for (size_t i = 0; i < m_Grids.size(); i++)
	{
		if (m_Grids[i].payout.isEmpty()) continue;

		storeGridData(pItemInfos->Add(), &m_Grids[i].payout);
		pAttrInfos->Add((float)(size_t)m_Grids[i].payout.userdata);

		storeGridData(pItemInfos->Add(), &m_Grids[i].obtain);
		pAttrInfos->Add((float)(size_t)m_Grids[i].obtain.userdata);
	}

	return MAX_TRADER_ITEMS * 2;
}
/* delete by:tanzhenyu
int ActorTrader::enchantRandom(BackPackGrid &tgtGrid)
{
	tgtGrid.setEnchants(0, NULL);
	int tooltype = -1;

	if(tgtGrid.def)
	{	
		DefDataTable<ToolDef> &toolTable = GetDefManagerProxy()->getToolTable();
		ToolDef *tooldef = toolTable.GetRecord(tgtGrid.def->ID);
		if(tooldef)
		{
			tooltype = tooldef->Type;
		}
	}

	if (tooltype < 0) return -1;
	GetDefManagerProxy()->setCurAccordEnchants(tooltype);

	auto itemDef = GetDefManagerProxy()->getItemDef(tgtGrid.def->ID);

	if (itemDef == nullptr) return -1;
	auto costDef = GetDefManagerProxy()->getEnchantMentDef(itemDef->StuffType);

	int total = 0;
	int probsLower[MAX_ITEM_ENCHANTS];
	int probsHigher[MAX_ITEM_ENCHANTS];
	memset(probsLower, 0, sizeof(probsLower));
	memset(probsHigher, 0, sizeof(probsHigher));
	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
	{
		probsLower[i] = total + 1;
		total += costDef->NpcAttrWeight[i];
		probsHigher[i] = total;
	}
	if(total <= 0) total = 1;

	int luckyNumber = GenRandomInt(1, total);

	int randomCnt = 0;
	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
	{
		if (luckyNumber <= probsHigher[i] && luckyNumber >= probsLower[i])
		{
			randomCnt = i + 1;
			break;
		}
	}
	if (randomCnt > MAX_ITEM_ENCHANTS) randomCnt = MAX_ITEM_ENCHANTS;
	std::vector<const EnchantDef *> availables;
	for (int i = 0, _num = GetDefManagerProxy()->getCurAccordEnchantsNum(); i < _num; ++i)
	{
		availables.push_back(GetDefManagerProxy()->getCurAccordEnchantDef(i));
	}
	// 选出randomCnt种附魔类型
	randomSelect(availables, randomCnt);

	// 赋予随机等级	
	int lprobLower[5];
	int lprobHigher[5];
	int probsCnt = 0;
	total = 0;
	memset(lprobLower, 0, sizeof(lprobLower));
	memset(lprobHigher, 0, sizeof(lprobHigher));
	for (int i = 0; i < 5; ++i)
	{
		lprobLower[i] = total + 1;
		total += costDef->NpcLevelWeight[i];
		lprobHigher[i] = total;
	}
	int cost = 0;
	for (int i = 0, _n = availables.size(); i < _n; ++i)
	{
		int randNum = GenRandomInt(1, total);
		int lvl = 0;
		for (int j = 0; j < 5; ++j)
		{
			if (randNum >= lprobLower[j] && randNum <= lprobHigher[j])
			{
				lvl = j + 1;
				break;
			}
		}
		// 超过等级上限的 干掉1级
		int eid = availables[i]->ID * 100 + lvl;
		while (eid % 100 > 0 && GetDefManagerProxy()->getEnchantDef(eid)==NULL)
			--eid;
		if (eid % 100 <= 0) continue;
		
		tgtGrid.addEnchant(eid, false);
	
		afterChangeGrid(tgtGrid.getIndex());
		lvl  = eid - availables[i]->ID * 100;
		int lvcost = enchantCost(costDef, lvl);
		if(availables.size() > 1)
			lvcost += costDef->MergeCost[lvl-1];
		cost += lvcost;	
	
	}

	return cost;
	
}

int ActorTrader::enchantCost(const EnchantMentDef *costdef, int lv)
{
	int cost = 0;
	if(lv > 1)
		cost = enchantCost(costdef, lv-1) * 2 + costdef->MergeCost[lv-1];
	else
		cost = costdef->Cost;
	return cost;
}

void ActorTrader::randomSelect(std::vector<const EnchantDef *> &enchantSet, int num)
{
	std::vector<const EnchantDef *> candiSet;
	candiSet.swap(enchantSet);

	if (candiSet.size() <= 0) return;

	int *probsLower = new int[candiSet.size()];
	int *probsHigher = new int[candiSet.size()];

	int candiCnt = 0;

	while (candiSet.size() > 0 && num > 0)
	{
		memset(probsLower, 0, sizeof(int) * candiSet.size());
		memset(probsHigher, 0, sizeof(int) * candiSet.size());
		candiCnt = 0;
		int total = 0;
		// 计算权重数组
		for (int i = 0, _n = candiSet.size(); i < _n; ++i)
		{
			probsLower[candiCnt] = total + 1;
			total += candiSet[i]->Weight;
			probsHigher[candiCnt] = total;
			candiCnt ++;
		}
		int luckyNum = m_pWorld->genRandomInt(1, total);
		int selected = 0;
		for (int i = 0; i < candiCnt; ++i)
		{
			if (luckyNum >= probsLower[i] && luckyNum <= probsHigher[i])
			{
				selected = i;
				break;
			}
		}
		enchantSet.push_back(candiSet[selected]);
		--num;

		int conflitId = candiSet[selected]->ConflictID;
		candiSet.erase(std::remove_if(candiSet.begin(), candiSet.end(), 
			[conflitId](const EnchantDef *def)->bool
		{
			return (def->ConflictID > 0 && def->ConflictID == conflitId);
		}), candiSet.end());
	}

	OGRE_DELETE_ARRAY(probsLower);
	OGRE_DELETE_ARRAY(probsHigher);
}
*/
int ActorTrader::getObjType() const
{
	return OBJ_TYPE_NPC;
}

ATTACK_TARGET_TYPE ActorTrader::getAttackTargetType()
{
	return ATTACK_TARGET_OTHERS;
}

bool ActorTrader::interact(ClientActor*player, bool onshift/* =false */, bool isMobile)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		// 检查是否已有其他玩家在交互
		if (!m_OpenUINs.empty())
		{
			// 检查是否是当前玩家自己
			bool isCurrentPlayer = false;
			for (int uin : m_OpenUINs)
			{
				if (uin == pTempPlayer->getUin())
				{
					isCurrentPlayer = true;
					break;
				}
			}
			
			// 如果不是当前玩家，说明有其他玩家在交互
			if (!isCurrentPlayer)
			{
				// 显示等待提示
				if (pTempPlayer->hasUIControl())
				{
					// 使用正确的提示方式显示等待信息
					GetLuaInterfaceProxy().showGameTips("Please wait");
				}
				return false;
			}
		}
		
		pTempPlayer->updateTaskSysProcess(TASKSYS_INTERACT_ACTOR, pTempPlayer->getCurToolID(), m_Def->ID);
		if (!m_pWorld->isRemoteMode())
		{
			faceActor(pTempPlayer, 180.0f, 180.0f);
		}
	}
	
	// 调用父类的交互逻辑（会处理openContainer和UIN管理）
	ActorContainerMob::interact(player);
	return true;
}

void ActorTrader::tick()
{
	ClientMob::tick();
	if (GetWorldManagerPtr() == NULL) return;

	int wt = GetWorldManagerPtr()->getWorldTime();
	if(m_TourTrader==1 && wt>m_SpawnTime+TOURTRADER_LIVETIME || m_TourTrader==2 && wt>m_SpawnTime+TICKS_ONEDAY)
	{
		setNeedClear();
	}
}

extern void DoCheckPackGridValid(BackPackGrid *pgrid);

BackPackGrid *ActorTrader::index2Grid(int index)
{
	assert(index >= NPCTRADE_START_INDEX);

	int offset = index - NPCTRADE_START_INDEX;
	if((offset%2) == 0) return &m_Grids[offset/2].payout;
	else
	{
		BackPackGrid *grid = &m_Grids[offset/2].obtain;
		if(grid->def) DoCheckPackGridValid(grid);
		return grid;
	}
}

bool ActorTrader::canPutItem(int index)
{
	return false;
}

void ActorTrader::onAttachUI()
{
	ActorContainerMob::onAttachUI();

	for(size_t i=0; i<m_Grids.size(); i++)
	{
		//ge GetGameEventQue().postBackpackChange(m_Grids[i].payout.getIndex());
		//ge GetGameEventQue().postBackpackChange(m_Grids[i].obtain.getIndex());
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", m_Grids[i].payout.getIndex());
		MNSandbox::SandboxContext sandboxContext1 = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", m_Grids[i].obtain.getIndex());
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext1);
		}
	}
}

