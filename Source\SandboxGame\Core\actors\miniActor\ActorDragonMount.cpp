#include "ActorDragonMount.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "EffectManager.h"
#include "GameCamera.h"
#include "MpActorManager.h"
#include "ActorAttrib.h"
#include "OgreScriptLuaVM.h"
#include "SoundComponent.h"
#include "EffectComponent.h"
//#include "DirectionByDirComponent.h"
#include "RiddenComponent.h"
#include "WorldManager.h"
#include "LivingAttrib.h"
#include "MobAttrib.h"
using namespace MINIW;

ActorDragonMount::ActorDragonMount(void)
{
	for(size_t i = 0; i < sizeof(m_fSkillCD)/sizeof(float); ++i)
	{
		m_fSkillCD[i] = 0.0f;
	}

	m_fAttackSoundTime = 0.0f;
	m_fAttackSkillCDEffectTime = 0.0f;
	m_bCanPlayEffectReadyToSpitFire = false;
	m_fDelayTime = 2.0f;
	m_bMounted = false;
}


ActorDragonMount::~ActorDragonMount(void)
{

}

void ActorDragonMount::tick()
{
	ActorHorse::tick();
	for(size_t i = 0; i < sizeof(m_fSkillCD)/sizeof(float); ++i)
	{
		m_fSkillCD[i] -= GAME_TICK_TIME;
		if (m_fSkillCD[i] <= Rainbow::EPSILON)
		{
			m_fSkillCD[i] = 0.0f;
		}
	}

	if (m_fAttackSkillCDEffectTime > Rainbow::EPSILON)
	{
		m_fAttackSkillCDEffectTime -= GAME_TICK_TIME;
		if (m_fAttackSkillCDEffectTime <= Rainbow::EPSILON)
		{
			m_fAttackSkillCDEffectTime = 0.0f;
		}
	}
}

void ActorDragonMount::update(float dtime)
{
	ActorHorse::update(dtime);
#ifndef IWORLD_SERVER_BUILD
	if (m_fAttackSoundTime > Rainbow::EPSILON)
	{
		m_fAttackSoundTime -= dtime;
		if (m_fAttackSoundTime <= Rainbow::EPSILON)
		{
			const MonsterDef* def = getDef();
			if (def)
			{
				auto sound = getSoundComponent();
				if (sound)
				{
					sound->playSound(def->AttackStopSound2.c_str(), 1, 1, 6);
				}
			}
		}
	}
#endif

	if(m_fDelayTime >= Rainbow::EPSILON)
	{
		m_fDelayTime -= dtime;
		if (m_fDelayTime < Rainbow::EPSILON)
		{
			m_bCanPlayEffectReadyToSpitFire = true;
		}
	}

	if(m_fSkillCD[0] <= Rainbow::EPSILON && m_bCanPlayEffectReadyToSpitFire)
	{
		m_bCanPlayEffectReadyToSpitFire = false;
		if(hasHorseSkill(HORSE_SKILL_YANYU_FIRE) || hasHorseSkill(HORSE_SKILL_YOUMIN_FIRE))
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("horse_3469_6");
			}
		}
	}

	if (getRiddenByActor() != NULL)
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(getRiddenByActor());
		if (player && player->hasUIControl())
			OnActorDragonMounted();
	}
}

void ActorDragonMount::checkBindPlayer(bool isinit)
{
	ActorHorse::checkBindPlayer(isinit);
	if(!m_pWorld || m_pWorld->isRemoteMode()) 
	{
		return;
	}

	if(m_BindUIN <= 0)
	{
		int mobid = m_Def->ID;
		if(mobid ==DRAGON_MOUNT_ID_LEVEL_1 || mobid == DRAGON_MOUNT_ID_LEVEL_2) //商店坐骑不可能没有bind uin
		{
			setNeedClear();
		}

		return;
	}
}

void ActorDragonMount::doSpitFireSkill()
{
	if(hasHorseSkill(HORSE_SKILL_YANYU_FIRE))
	{
		doSpitFireSkillInner(HORSE_SKILL_YANYU_FIRE);
	}
	else if(hasHorseSkill(HORSE_SKILL_YOUMIN_FIRE))
	{
		doSpitFireSkillInner(HORSE_SKILL_YOUMIN_FIRE);
	}
}

void ActorDragonMount::doSkillAttack(ClientActor *target)
{
	if(m_fSkillCD[0] <= Rainbow::EPSILON)
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("horse_3469_6");
		}
		doSpitFireSkill();
		m_bCanPlayEffectReadyToSpitFire = true;
	}
	else
	{
		if(m_fAttackSkillCDEffectTime <= Rainbow::EPSILON)
		{
			m_fAttackSkillCDEffectTime = 3.0f;
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("horse_3469_5");
			}

			if(getLocoMotion())
			{
				playAnim(getLocoMotion()->m_OnGround? SEQ_SPITFIRE_LAND_WAIT_CD : SEQ_SPITFIRE_AIR_WAIT_CD);
			}

			playAttackSound();
		}
	}
}

void ActorDragonMount::doSpitFireSkillInner(int skillID)
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
	{
		return;
	}

	std::vector<ClientActor *>actors;	
	ClientPlayer *player = dynamic_cast<ClientPlayer*>(GetWorldManagerPtr()->findActorByWID(getRiddenByActorID()));
	if (!player)
	{
		return;
	}

	float skillvals[7] ={0.0};
	getHorseSkill(skillID, skillvals);
	m_fSkillCD[0] = skillvals[0] <= Rainbow::EPSILON ? 30.0f: skillvals[0];
	CollideAABB box;
	getCollideBox(box);
	Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
	dir.y = 0.02f;
	int blockCount = skillvals[1] <= Rainbow::EPSILON ? 2 : (int)skillvals[1];
	WCoord mvec(dir * (blockCount * BLOCK_FSIZE));
	Rainbow::Vector3f colnormal;
	WCoord coordDragonHeadPos = getPosition() + WCoord(0, 200, 0); 
	box.pos = coordDragonHeadPos; //碰撞盒位置以龙头位置来进行计算，避免对站在障碍物上的生物喷火出现喷不到的情况

	float t = m_pWorld->moveBox(box, mvec, colnormal);
	actors.clear();

	int range = (int)(t*mvec.length());
	if (t < 1)  //前方有障碍物的情况下，把范围扩大半格避免贴着障碍物的生物检测不到
	{
		range += 50;
	}

	int height = skillvals[2] <= Rainbow::EPSILON ? 200: (int)(skillvals[2]*BLOCK_FSIZE);
	int angle =  skillvals[3] <= Rainbow::EPSILON ? 60 : (int)skillvals[3];

	getLocoMotion()->getFanShapedAreaFacedActors(actors, dir, range, height, angle);

	for (size_t i = 0; i < actors.size(); ++i)
	{
		ClientActor *target = actors[i];
		auto RidComp = getRiddenComponent();
		if (target == this || /*target->getObjId() == m_RidingActor*/(RidComp && (RidComp->checkRidingByActorObjId(target->getObjId()) || /*target->getObjId() == m_RiddenByActor*/RidComp->checkRiddenByActorObjId(target->getObjId())))
			|| !player->canHurtActor(target) || target->isDead())
		{
			continue;
		}

		ClientMob *mob = dynamic_cast<ClientMob*>(target);
		if(mob)
		{
			int buffLvl = skillID == HORSE_SKILL_YOUMIN_FIRE ? 5 : 4; 
			mob->getLivingAttrib()->addBuff(CATCH_FIRE_BUFF, buffLvl);
		}	
	}

	if(hasHorseSkill(HORSE_SKILL_YANYU_FIRE))
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("horse_3469_1");
		}
	}
	else if(hasHorseSkill(HORSE_SKILL_YOUMIN_FIRE))
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("horse_3469_2");
		}
	}
	
	if(getLocoMotion())
	{
		playAnim(getLocoMotion()->m_OnGround? SEQ_SPITFIRE_LAND : SEQ_SPITFIRE_AIR);
	}

	if (m_Def)
	{
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound(m_Def->AttackSound2.c_str(), 1, 1, 6);
		}
	}
	
	PB_Horse_SkillCDHC skillCDHC;
	skillCDHC.set_actorid(getObjId());
	skillCDHC.set_index(0);
	skillCDHC.set_cd(m_fSkillCD[0]);
	m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_HORSE_SKILLCD_HC, skillCDHC, this, false);
}

void ActorDragonMount::playAttackSound()
{
	m_fAttackSoundTime = 0.2f;
}

void ActorDragonMount::OnActorDragonMounted()
{
	if(m_bMounted)
	{
		return;
	}

	int mobid = m_Def ? m_Def->ID : 0;
	if(mobid == DRAGON_MOUNT_ID_LEVEL_1 || mobid == DRAGON_MOUNT_ID_LEVEL_2) 
	{
		m_bMounted = true;
		MINIW::ScriptVM::game()->callFunction("onActorDragonMounted", NULL);
	}
}

void ActorDragonMount::OnActorDragonDismounted()
{
	if(!m_bMounted)
	{
		return;
	}

	int mobid = m_Def ? m_Def->ID : 0;
	if(mobid == DRAGON_MOUNT_ID_LEVEL_1 || mobid == DRAGON_MOUNT_ID_LEVEL_2) 
	{
		m_bMounted = false;
		MINIW::ScriptVM::game()->callFunction("onActorDragonDismounted", NULL);
	}
}

void ActorDragonMount::setSkillCD(int index, float cd)
{
	if (index < 0 || index >= (sizeof(m_fSkillCD)/sizeof(float)))
	{
		return;
	}

	m_fSkillCD[index] = cd;
}

float ActorDragonMount::getSkillCD(int index)
{
	if (index < 0 || index >= (sizeof(m_fSkillCD)/sizeof(float)))
	{
		return 0.0f;
	}

	return m_fSkillCD[index];
}

bool ActorDragonMount::getHorseFlySkills(float *skillvals /*=NULL*/)
{
	bool ret = false;
	if (hasHorseSkill(HORSE_SKILL_ZHENGCHI))
	{
		ret = getHorseSkill(HORSE_SKILL_ZHENGCHI, skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_ZHANYI))
	{
		ret = getHorseSkill(HORSE_SKILL_ZHANYI, skillvals);
	}
	else
	{
		ret = ActorHorse::getHorseFlySkills(skillvals);
	}

	return ret;
}

float ActorDragonMount::getFallHurtRate()
{
	if (hasHorseSkill(HORSE_SKILL_ZHENGCHI) || hasHorseSkill(HORSE_SKILL_ZHANYI))
	{
		return 0.2f;
	}
	else
	{
		return ActorHorse::getFallHurtRate();
	}
}

void ActorDragonMount::startCharge()
{
 	ActorHorse::startCharge();
}

void ActorDragonMount::endCharge()
{
	if(getFlying())
	{
		setFlying(false);
		auto effectComponent = getEffectComponent();
		if(hasHorseSkill(HORSE_SKILL_ZHENGCHI))
		{
			if (effectComponent)
			{
				effectComponent->stopBodyEffect("horse_3469_3");
			}
		}
		else
		{
			if (effectComponent)
			{
				effectComponent->stopBodyEffect("horse_3469_4");
			}
		}
	}

	if(!isInCharge()) 
	{
		return;
	}


	if(!m_pWorld->isRemoteMode() && getLocoMotion() && getLocoMotion()->m_OnGround)
	{
		float t = getChargeProgress();
		float skillvals[7] = {0.0f};
		if (getHorseFlySkills(skillvals))
		{
			m_fEnergy = 20* skillvals[2] * t;
			setFloatingStatus(true);

			setFlagBit(ACTORFLAG_FLOATAGE, true);
			setHorseFlagBit(HORSE_FLAG::FLYING, true);//飞行	// 20220422：增加飞行状态 codeby： huangrui
			auto effectComponent = getEffectComponent();
			if(hasHorseSkill(HORSE_SKILL_ZHENGCHI))
			{
				if (effectComponent)
				{
					effectComponent->playBodyEffect("horse_3469_3");
				}
			}
			else
			{
				if (effectComponent)
				{
					effectComponent->playBodyEffect("horse_3469_4");
				}
			}

			playAnim(SEQ_JUMP);
			if(m_Def)
			{
				playLoopSound(0, m_Def->AttackSound.c_str());
			}
		}
		else
		{
			getLocoMotion()->m_Motion.y = (m_JumpHeight - 40.0f)*t + 40.0f;

			Rainbow::Vector3f fdir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
			float forwardspeed = 20.0f;

			getLocoMotion()->m_Motion.x += fdir.x*forwardspeed*t;
			getLocoMotion()->m_Motion.z += fdir.z*forwardspeed*t;
			playAnim(SEQ_JUMP);
		}
	}

	if (m_pWorld->isRemoteMode() && getLocoMotion() && getLocoMotion()->m_OnGround)
	{
		float skillvals[7] = {0.0f};
		if (getHorseFlySkills(skillvals))
		{
			float t = getChargeProgress();
			m_fEnergy = 20 * skillvals[2] * t;
			setFloatingStatus(true);
			setFlagBit(ACTORFLAG_FLOATAGE, true);
			setHorseFlagBit(HORSE_FLAG::FLYING, true);//飞行	// 20220422：增加飞行状态 codeby： huangrui
		}

	}

	resetCurCharge();
}

bool ActorDragonMount::init(int monsterid)
{
	return ActorHorse::init(monsterid);
}

flatbuffers::Offset<FBSave::SectionActor> ActorDragonMount::save(SAVE_BUFFER_BUILDER &builder)
{
	auto mobdata = ClientMob::saveMob(builder);

	flatbuffers::Offset<FBSave::ItemIndexGrid> grids[MAX_EQUIPS];
	int count = 0;

	for(size_t i=0; i<MAX_EQUIPS; ++i)
	{
		if(m_EquipGrids[i].isEmpty()) 
		{
			continue;
		}

		grids[count++] = m_EquipGrids[i].saveWithIndex(builder);
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> items = 0;
	if(count > 0) items = builder.CreateVector(grids,count);

	int skills[MAX_SKILLS];
	for(size_t i=0; i<MAX_SKILLS; ++i)
	{
		skills[i] = m_Skills[i].id | (m_Skills[i].active << 16);
	}

	flatbuffers::Offset<flatbuffers::Vector<uint64_t>> otherriddens = 0;
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->getNumRiddenPos() > 1)
	{
		uint64_t riddens[16];
		for(int i=1; i< RidComp->getNumRiddenPos(); ++i)
		{
			riddens[i-1] = m_OtherRiddens[i-1];
		}
		otherriddens = builder.CreateVector(riddens, RidComp->getNumRiddenPos()-1);
	}

	flatbuffers::Offset<flatbuffers::Vector<float>> skillcdsoffset = 0;
	std::vector<float> skillcds;
	for (size_t i = 0; i<(sizeof(m_fSkillCD)/sizeof(float)); ++i)
	{
		skillcds.push_back(m_fSkillCD[i]);
	}
	skillcdsoffset = builder.CreateVector(skillcds);
	auto dragonMount = FBSave::CreateActorDragonMount(
		builder, mobdata, getRiddenByActorID(), items, 
		builder.CreateVector(skills,MAX_SKILLS), 
		(int16_t)getAttrib()->getMaxHP(), m_LandSpeed, m_FlySpeed,
		m_SwimSpeed, m_JumpHeight, m_BindUIN, 
		m_ArmorSlotOpen?1:0, otherriddens, m_fEnergy, 
		m_bTired, 
		skillcdsoffset);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorDragonMount, dragonMount.Union());
}

bool ActorDragonMount::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorDragonMount *>(srcdata);

	if(!ClientMob::load(src->mobdata(), version))
	{
		return false;
	}

	//m_RiddenByActor = src->ridden();
	setRiddenByActorObjId(src->ridden());
	if(getRiddenByActorID() > 0)
	{
		DestroyComponent(GetComponentByName("NavigationPath"));
	}

	if(src->equips())
	{
		for(size_t i=0; i<src->equips()->size(); ++i)
		{
			const FBSave::ItemIndexGrid *itemsrc = src->equips()->Get(i);

			int offset = itemsrc->index() - HORSE_EQUIP_INDEX;
			m_EquipGrids[offset].load(itemsrc);
		}
	}
	if(src->skills())
	{
		for(size_t i=0; i<src->skills()->size(); ++i)
		{
			int s = src->skills()->Get(i);
			m_Skills[i].id = s & 0xffff;
			m_Skills[i].active = s >> 16;
		}
	}
	if (src->skillcd() && src->skillcd()->size() <= (sizeof(m_fSkillCD)/sizeof(float)))
	{
		for (size_t i = 0; i < src->skillcd()->size(); ++i)
		{
			m_fSkillCD[i] = src->skillcd()->Get(i);
		}
	}
	//if(!m_EquipGrids[0].isEmpty()) m_Body->showSkin(DEFAULT_SADDLE, true);

	if (src->maxhp() > 0)
	{
		getAttrib()->initMaxHP(src->maxhp());
	}
	if (version == 0) getAttrib()->initMaxHP(getAttrib()->getMaxHP()*5.0f);

	m_LandSpeed = src->landspeed();
	m_FlySpeed = src->flyspeed();
	m_SwimSpeed = src->swimspeed();
	m_JumpHeight = src->jumpheight();
	m_BindUIN = src->binduin();
	m_ArmorSlotOpen = src->armoropen()==1;
	auto otherriddens = src->otherriddens();
	if(otherriddens)
	{
		for(size_t i=0; i<otherriddens->size(); ++i)
		{
			m_OtherRiddens[i] = otherriddens->Get(i);
		}
	}

	if(m_FlySpeed == 0) 
	{
		m_FlySpeed = m_LandSpeed;
	}
			
	MobAttrib *attr = dynamic_cast<MobAttrib *>(getAttrib());
	if(attr)
	{
		attr->equip(EQUIP_HEAD, &m_EquipGrids[0]);
		if(m_ArmorSlotOpen) attr->equip(EQUIP_BREAST, &m_EquipGrids[1]);
	}

	m_fEnergy = src->energy();
	m_bTired = src->tired() != 0;
	if (m_bTired)
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("mob_3421_tired");
		}
	}

	m_iUseSwimSpeed = 0;
	if (hasHorseSkill(HORSE_SKILL_FLOAT)) 
	{
		m_iUseSwimSpeed += 1;
		getLocoMotion()->m_yOffset = -80;
	}

	if (hasHorseSkill(HORSE_SKILL_DIVING)) 
	{
		m_iUseSwimSpeed += 2;
		getLocoMotion()->m_yOffset = -20;
	}

	if (hasHorseSkill(HORSE_SKILL_WATER_RUSH)) 
	{
		m_iUseSwimSpeed += 4;
		getLocoMotion()->m_yOffset = -20;
		float skillvals[7];
		if (getHorseSkill(HORSE_SKILL_WATER_RUSH, skillvals)) 
		{
			m_iChargeAddSpeed = (int)skillvals[2];
			m_iChargeAddSpeed = m_iChargeAddSpeed <= 0 ? 1 : m_iChargeAddSpeed;
		}
	}

	return true;
}