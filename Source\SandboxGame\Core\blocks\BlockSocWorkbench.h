#pragma once

#include "BlockMaterial.h"

class BlockSocWorkbenchMaterial : public MultiModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSocWorkbenchMaterial)
public:
	virtual void init(int resid) override;
	//tolua_begin
	//virtual void initMultiRange(const BlockDef* def) override;
	virtual bool onMultiTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint, WorldContainer* container) override;
	virtual WorldContainer* createMultiContainer(World* pworld, const WCoord& blockpos) override;
	virtual int getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player) override;

	virtual void onMultiBlockAdded(World* pworld, const WCoord& blockpos) override;
	virtual void onMultiBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;

	virtual void update(unsigned int dtick) override;
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player) override;
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world) override;
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos) override;
	virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage) override;
	virtual int getBlockHP(World* pworld, const WCoord& blockpos) override;
	//tolua_end
}; //tolua_exports