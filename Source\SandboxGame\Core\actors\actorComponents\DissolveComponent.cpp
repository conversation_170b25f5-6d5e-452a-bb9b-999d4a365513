#include "DissolveComponent.h"
#include "ClientMob.h"
#include "ActorBody.h"
#include "OgreModel.h"
#include "world.h"
#include "SandBoxManager.h"
#include "ClientInfoProxy.h"

IMPLEMENT_COMPONENTCLASS(DissolveComponent)

using namespace Rainbow;
static const std::string NPRMatTeamplatePath = "Materials/MiniGame/MaterialTemplate/Character_NPR/Character_NPR_Tranclucent.templatemat";
static const std::string DissolveTex = "Resources/minigame/ui/mobile/dissolvetex.png";
static const int HIDE_TICK = 40;

DissolveComponent::DissolveComponent()
{
	init();
}

void DissolveComponent::init()
{
	m_dissolveIntensityRate = 0.02f;
	m_dissolveType = 0.8f;
	m_dissolveIntensity = 0.0f;
	m_curDissolveIntensityRate = 0.0f;

	m_shouldEnd = false;
	m_openDissolve = false;

	m_stayHideTick = HIDE_TICK;

	m_ownerMob = NULL;
}

DissolveComponent::~DissolveComponent()
{
	clearOriginMtl();
}

void DissolveComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	cacheOriginMtl();
	if (!GetOwner()) return ;
	m_ownerMob = dynamic_cast<ClientMob*>(GetOwner());

	BindOnTick();
}

void DissolveComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
	m_ownerMob = NULL;
}

void DissolveComponent::OnEndPlay()
{
	Super::OnEndPlay();
}

void DissolveComponent::OnTick()
{
	if (!m_openDissolve)
		return;

	if (!m_ownerMob)
		return;

	if (m_dissolveIntensity <= FLT_EPSILON)
	{
		if (m_shouldEnd)
		{
			doCloseDissolve();	
		}

		m_curDissolveIntensityRate = m_dissolveIntensityRate;
	}
	else if (m_dissolveIntensity - 1 > FLT_EPSILON)
	{
		m_stayHideTick--;
		if (m_stayHideTick <= 0)
		{
			m_stayHideTick = HIDE_TICK;
			m_curDissolveIntensityRate = -m_dissolveIntensityRate;
		}
		else
		{
			m_curDissolveIntensityRate = 0.0f;
		}
		
	}

	m_dissolveIntensity += m_curDissolveIntensityRate;

	m_ownerMob->SetModelMatFloat("_dissolve_intensity", m_dissolveIntensity);
}

void DissolveComponent::doOpenDissolve()
{
	if (!m_ownerMob)
		return;

	if (!GetClientInfoProxy()->isPureServer())
	{
		if (m_originMtl.size() <= 0)
			cacheOriginMtl();

		if (m_originMtl.size() <= 0)
			return;
	}

	m_dissolveIntensity = 0;
	m_ownerMob->SetModelMaterial(NPRMatTeamplatePath);
	m_ownerMob->SetModelTexture("_dissolve_texture", DissolveTex.c_str());
	m_ownerMob->SetModelMatFloat("_dissolve_intensity", m_dissolveIntensity);
	m_ownerMob->SetModelMatFloat("_dissolve_type", m_dissolveType);
	updateMainTexFromOriginMtl();

	m_ownerMob->SetModelMatKeyword("ENABLE_DISSOLVE", true);
	m_openDissolve = true;

	syncDissolve();
}

void DissolveComponent::doCloseDissolve()
{
	if (!m_openDissolve)
		return;

	if (!m_ownerMob)
		return;

	m_shouldEnd = false;

	m_ownerMob->SetModelMatFloat("_dissolve_intensity", 0);
	m_openDissolve = false;
	resetToOriginMtl();

	syncDissolve();
}

bool DissolveComponent::openDissolve(bool open)
{
	if (open)
	{
		m_shouldEnd = false;
		doOpenDissolve();
	}
	else
	{
		m_shouldEnd = true;
	}

	return true;
}

void DissolveComponent::setDissolveType(float type)
{
	m_dissolveType = type;
}

void DissolveComponent::setDissolveIntensityRate(float rate)
{
	m_dissolveIntensityRate = rate;
}

void DissolveComponent::cacheOriginMtl()
{
	if (!m_ownerMob)
		return;

	auto body = m_ownerMob->getBody();
	if (!(body && body->getModel()))
		return;

	clearOriginMtl();

	auto pModel = body->getModel();
	int iNum = pModel->GetModelMeshRendererNum();
	IModelMeshRenderer* pMeshRenderer = nullptr;

	for (int i = 0; i < iNum; i++)
	{
		pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
		if (!pMeshRenderer)
			continue;

		int iSubNum = pMeshRenderer->GetSubMeshNum();
		for (int j = 0; j < iSubNum; j++)
		{
			SharePtr<MaterialInstance> mtl = NativeToSharePtr<MaterialInstance>(pMeshRenderer->GetSubMeshMaterial(j));
			m_originMtl.push_back(mtl);
		}
	}
}

void DissolveComponent::clearOriginMtl()
{
	m_originMtl.clear();
}

void DissolveComponent::resetToOriginMtl()
{
	if (m_originMtl.size() <= 0)
		return;

	if (!m_ownerMob)
		return;

	auto body = m_ownerMob->getBody();
	if (!(body && body->getModel()))
		return;

	auto pModel = body->getModel();
	int iNum = pModel->GetModelMeshRendererNum();
	IModelMeshRenderer* pMeshRenderer = nullptr;

	int index = 0;
	for (int i = 0; i < iNum; i++)
	{
		pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
		if (!pMeshRenderer)
			continue;

		int iSubNum = pMeshRenderer->GetSubMeshNum();
		for (int j = 0; j < iSubNum; j++)
		{
			if (index >= m_originMtl.size())
			{
				pMeshRenderer->UpdateSubMeshMaterial(j, nullptr);
			}
			else
			{
				pMeshRenderer->UpdateSubMeshMaterial(j, (m_originMtl[index]).Get());
				index++;
			}
		}
	}
}

void DissolveComponent::updateMainTexFromOriginMtl()
{
	if (!m_ownerMob)
		return;

	auto body = m_ownerMob->getBody();
	if (!(body && body->getModel()))
		return;

	if (m_originMtl.size() <= 0)
		return;

	auto pModel = body->getModel();
	int iNum = pModel->GetModelMeshRendererNum();
	IModelMeshRenderer* pMeshRenderer = nullptr;

	int index = 0;
	for (int i = 0; i < iNum; i++)
	{
		pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
		if (!pMeshRenderer)
			continue;

		if (m_originMtl.size() <= index)
			break;

		SharePtr<Texture2D> tex = NativeToSharePtr<Texture2D>
			(static_cast<Texture2D*>(m_originMtl[index]->GetTexture(ShaderParamNames::g_DiffuseTex)));

		if (tex)
			pMeshRenderer->SetTexture(ShaderParamNames::_MainTex, tex);

		int iSubNum = pMeshRenderer->GetSubMeshNum();
		index += iSubNum;
	}
}

void DissolveComponent::syncDissolve()
{
	if (!m_ownerMob)
		return;

	World* pworld = m_ownerMob->getWorld();
	if (pworld && !pworld->isRemoteMode())
	{
		char objid_str[128];
		sprintf(objid_str, "%lld", m_ownerMob->getObjId());

		jsonxx::Object object;
		object << "objid" << objid_str;
		object << "dissolveOpen" << m_openDissolve;
		object << "dissolveRate" << m_dissolveIntensityRate;
		object << "dissolveType" << m_dissolveType;

		GetSandBoxManager().sendBroadCast("PB_ACTOR_DISSOLVE_COMPONENT_OPEN_HC", object.bin(), object.binLen());
	}
}