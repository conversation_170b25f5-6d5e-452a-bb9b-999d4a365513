#include "ClientActorSnowBall.h"
#include "ProjectileLocoMotion.h"
#include "ChunkSave_generated.h"

ClientActorSnowBall::ClientActorSnowBall()
{
}

ClientActorSnowBall::~ClientActorSnowBall()
{
}

flatbuffers::Offset<FBSave::SectionActor> ClientActorSnowBall::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);

	auto obj = FBSave::CreateActorThrowable(builder, basedata, m_ShootingActorID, m_ItemID);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorEgg, obj.Union());
}


bool ClientActorSnowBall::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorThrowable *>(srcdata);
	loadActorCommon(src->basedata());

	init(src->itemid());

	m_ShootingActorID = src->shooter();
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (loco)
	loco->syncPos = getLocoMotion()->getPosition();
	return true;
}


void ClientActorSnowBall::onImpactWithActor(ClientActor *actor, const std::string& partname)
{
	ClientActorProjectile::onImpactWithActor(actor, partname);
}
