
#include "HorseLocomotion.h"
#include "ActorHorse.h"
#include "world.h"
#include "RiddenComponent.h"
#include "ParticlesComponent.h"
#include "SoundComponent.h"
#include "ClientActorFuncWrapper.h"
#include "ActorAttrib.h"
#include "MobAttrib.h"
#include "defdata.h"
//const int MAX_HURTRESISTANT_TIME = 20;
static float glide_gravity = 0.1f;
IMPLEMENT_COMPONENTCLASS(HorseLocomotion)
float HorseLocomotion::getGravityFactor(bool up)
{
	if (m_bSpecialGravity)
	{
		if (up)
		{
			return m_fUp_gravity;
		}
		else 
		{ 
		  if (!m_OnGround)
		     return m_fDown_gravity;
		  else
			 return 1.0f;
		}
	}
	if(!up && getOwnerActor()->getFlying())
	{
		return glide_gravity;
	}
	else return 1.0f;
}

void HorseLocomotion::setSpecialGravity(float up_gravity, float down_gravity)
{
	m_bSpecialGravity = true;
	m_fUp_gravity = up_gravity;
	m_fDown_gravity = down_gravity;
}

void HorseLocomotion::getCollideBox(CollideAABB &box)
{
	ActorHorse *horse = static_cast<ActorHorse *>(getOwnerActor());
	auto RidComp = getOwnerActor()->getRiddenComponent();
	int h = RidComp && RidComp->isRidden() ? horse->getHorseDef()->RideHeight : m_BoundHeight;

	box.dim = WCoord(m_BoundSize, h, m_BoundSize);
	box.pos = getPosition() - WCoord(box.dim.x/2, m_yOffset, box.dim.z/2);
}

void HorseLocomotion::doJump()
{
	if(getOwnerActor()->getMoveMode() == ACTORMOVE_JUMP)
	{
		if(m_StartJumpTicks > 0) m_StartJumpForce += 20.0f;
		else m_StartJumpForce = 0;

		float base = 50.0f;
		if (getOwnerActor()->getAttrib()) {
			base = getOwnerActor()->getAttrib()->getSpeedAtt(Actor_Jump_Speed);
			if (base < 0.0f) { base = 50.0f; }
		}
		m_Motion.y = base + m_StartJumpForce;

		ParticlesComponent::playParticles(getOwnerActor(), "mob_3414_1");
				
		auto sound = getOwnerActor()->getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3415.jump", 0.8f + GenRandomFloat()*0.4f, 0.8f + GenRandomFloat()*0.4f);
		}
	}
	else LivingLocoMotion::doJump();
}

void HorseLocomotion::tick()
{
	if(getOwnerActor()->getMoveMode() == ACTORMOVE_JUMP)
	{
		if(m_StartJumpTicks > 0) m_StartJumpTicks--;
	}
	
	LivingLocoMotion::tick();
}

// 20210804：部分坐骑的移动逻辑不在moveEntityWithHeading里处理  codeby： keguanqiang
bool HorseLocomotion::ignoreMoveEntityWithHeading()
{
	ActorHorse *horse = static_cast<ActorHorse *>(getOwnerActor());
	if (!horse || !horse->getHorseDef())
		return false;

	auto &horseID = horse->getHorseDef()->ID;
	if (horseID == 3487 || horseID == 3489 || horseID == 4501 || horseID == 3912)
		return true;

	return false;
}

void HorseLocomotion::moveEntityWithHeading(float strafing, float forward)
{
	ActorHorse *horse = static_cast<ActorHorse *>(getOwnerActor());
	if(horse->needUpdateAI())
	{
		LivingLocoMotion::moveEntityWithHeading(strafing, forward);
		return;
	}

	auto RidComp = getOwnerActor()->getRiddenComponent();
	ClientActor *riddenactor = NULL;
	if (RidComp)
	{
		riddenactor = RidComp->getRiddenByActor();
	}
	if (!riddenactor)
	{	
		return;
	}

	LivingLocoMotion *riddenloc = dynamic_cast<LivingLocoMotion *>(riddenactor->getLocoMotion());
	if (!riddenloc)
	{
		return;
	}

	//脚本处理
	if (horse->moveLocoMotionEntityWithHeading(strafing, forward)) return;

	if (horse->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE)
	{
		//LivingLocoMotion::moveEntityWithHeading(riddenloc->m_MoveStrafing, riddenloc->m_MoveForward);
		return;
	}

	if (isFloatageing())
		return;

	if (ignoreMoveEntityWithHeading()) 	// 20210804：部分坐骑的移动逻辑不在moveEntityWithHeading里处理  codeby： keguanqiang
		return;

	//this.rotationYawHead = this.renderYawOffset = this.rotationYaw;
	strafing = riddenloc->m_MoveStrafing * 0.5f;
	forward = riddenloc->m_MoveForward;

	if(forward <= 0.0f)
	{
		forward *= 0.5f;
		//forward *= 0.25f;
		//this.field_110285_bP = 0; //
	}

	if(m_OnGround && (forward!=0 || strafing!=0) && getOwnerActor()->getMoveMode()==ACTORMOVE_JUMP)
	{
		m_isJumping = true;
	}
	else m_isJumping = false;

	int speedbase = 0;
	if (!getOwnerActor()->getFlying() && horse->getAttrib()) {
		speedbase = (int)horse->getAttrib()->getSpeedAtt(m_InWater && horse->isUseSwimSpeed() ? Actor_Swim_Speed : Actor_Walk_Speed);
		if (speedbase < 0) {
			speedbase = (m_InWater && horse->isUseSwimSpeed() ? horse->getRiddenSwimSpeed() : horse->getRiddenLandSpeed());
		}
	}
	float speed = MobAttrib::defSpeed2MoveSpeed(getOwnerActor()->getFlying() ? horse->getRiddenFlySpeed() : speedbase);

	if(!horse->isUseSwimSpeed() && (m_InWater || m_InLava || m_InHoney)) speed *= 0.5f;
	if(getOwnerActor()->getFlying())
	{
		forward = 20.0f;
	}

	if(horse->isInCharge())
	{
		speed *= 1.2f;

		float vals[7];
		if(horse->getHorseSkill(HORSE_SKILL_BURST,vals))
		{
			speed *= 1.0f + vals[0]/100.0f;
		}
	}


	if(!m_pWorld->isRemoteMode())
	{
		m_RotateYaw = m_PrevRotateYaw = riddenloc->m_RotateYaw;
		m_RotationPitch = m_PrevRotatePitch = riddenloc->m_PrevRotatePitch * 0.5f;
		if (riddenloc->m_PrevRotatePitch > 90)
		{
			m_RotationPitch = m_PrevRotatePitch = (riddenloc->m_PrevRotatePitch - 360.f) * 0.5f;
		}

		// 20210819：增加坐骑移动速度可使用buff效果的方式  codeby： keguanqiang
		auto pAttrib = dynamic_cast<LivingAttrib *>(getOwnerActor()->getAttrib());
		if (pAttrib)
		{
			float tmp = speed;
			speed += pAttrib->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_WALK_SPEED, tmp);
		}	
		//end
		auto funcWrapper = getOwnerActor()->getFuncWrapper();
		if (funcWrapper)
		{
			funcWrapper->setAIMoveSpeed(speed);
		}
		float oldfactor = m_JumpMovementFactor;
		if(getOwnerActor()->getFlying()) m_JumpMovementFactor = speed * 0.21f;
		else if(getOwnerActor()->getMoveMode()==ACTORMOVE_JUMP) m_JumpMovementFactor = speed;

		LivingLocoMotion::moveEntityWithHeading(strafing, forward);

		m_JumpMovementFactor = oldfactor;
	}
}

bool HorseLocomotion::canWalkOnLiquid(bool iswater)
{
	return static_cast<ActorHorse *>(getOwnerActor())->canWalkOnLiquid(iswater);
}

bool HorseLocomotion::checkSeatInWaterSkill(int bit)
{
	ActorHorse* pHorse = dynamic_cast<ActorHorse *>(getOwnerActor());
	if (!pHorse || !pHorse->isUseSwimSpeed()) { return false; }

	return pHorse->hasWaterSkill(bit);
}

bool HorseLocomotion::checkCanUpAndDown(float fRotationPitch)
{
	ActorHorse* pHorse = dynamic_cast<ActorHorse *>(getOwnerActor());
	if (!pHorse) { return false; }

	float skillvals[7];
	if (!pHorse->getHorseSkill(HORSE_SKILL_DIVING, skillvals) &&
		!pHorse->getHorseSkill(HORSE_SKILL_GIANTWHALE_SWIM,skillvals) &&
		!pHorse->getHorseSkill(HORSE_SKILL_SWAN_DIVING, skillvals))
	{
		return false;
	}

	return (fRotationPitch < skillvals[1] || fRotationPitch > skillvals[2]);
}

bool HorseLocomotion::isDoingRush()
{
	ActorHorse* pHorse = dynamic_cast<ActorHorse *>(getOwnerActor());
	if (!pHorse) { return false; }

	return pHorse->isDoingRush();
}

bool HorseLocomotion::isFloatageing()
{
	ActorHorse* pHorse = dynamic_cast<ActorHorse *>(getOwnerActor());
	if (pHorse)
		return pHorse->isFloatageing();

	return false;
}
void HorseLocomotion::playWalkOnLiquidEffect(bool iswater)
{
	bool isEnd = false;
	ActorHorse* pHorse = dynamic_cast<ActorHorse *>(getOwnerActor());
	if (pHorse)
		isEnd = pHorse->playWalkOnLiquidEffect(iswater);

	if (!isEnd)
		ActorLocoMotion::playWalkOnLiquidEffect(iswater);
}