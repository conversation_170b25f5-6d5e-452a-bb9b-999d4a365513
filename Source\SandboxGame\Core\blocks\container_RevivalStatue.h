
#ifndef __CONTAINER_REVIVALSTATUE_H__
#define __CONTAINER_REVIVALSTATUE_H__

#include "container_world.h"
#include <map>

class BaseEffect;

#define MAX_WORLD_EX 3

class ContainerRevivalStatue : public WorldContainer //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ContainerRevivalStatue() 
	{
		m_revivePoint.resize(0);
		
		for (int i=0; i<MAX_WORLD_EX; i++)
		{
			m_mapid[i] = -1;
		}

		for (int i = 0; i < MAX_WORLD_EX; i++)
		{
			m_isRevivalStatueActive[i] = false;
		}
		m_NeedTick = true;
	}
	ContainerRevivalStatue(const WCoord &blockpos) : WorldContainer(blockpos, 0)
	{
		m_revivePoint.resize(0);

		for (int i=0; i<MAX_WORLD_EX; i++)
		{
			m_mapid[i] = -1;
		}
		
		for (int i = 0; i < MAX_WORLD_EX; i++)
		{
			m_isRevivalStatueActive[i] = false;
		}
		m_NeedTick = true;
	}

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool load(const void *srcdata) override;
	virtual void updateTick() override;

	virtual int getObjType() const override;
	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerRevivalStatue;
	}
	virtual void enterWorld(World *pworld) override;
	virtual void leaveWorld() override;
	//tolua_end
	//��ȡ����״̬
	bool getActiveStatus();
	//���Կ������﹥��
	void tryStartMonsterSiege();
	//�����ʯ�ʵ����ƻ�
	void activeRevivalStatueBroken();
private:
	//����ʯ�ʵ���ļ���״̬
	void resetActiveStatus();
	//ʯ�ʵ����Ƿ񼤻�
	bool m_isRevivalStatueActive[MAX_WORLD_EX];
	// 0,1,2
	std::vector<WCoord>m_revivePoint;
	int m_mapid[MAX_WORLD_EX];
}; //tolua_exports

#endif