#include "container_electric_elemet.h"
#include "BlockMaterialMgr.h"
#include "special_blockid.h"
#include "world.h"
#include "IClientActor.h"
#include "ActorLocoMotion.h"
#include "ClientActorManager.h"
#include "block_tickmgr.h"
#include "BlockRayWire.h"
#include "BlockWirlessElectricUnit.h"
#include "container_electric_ray.h"

ContainerElectricElement::ContainerElectricElement() : m_nDir(0), m_nPower(0), m_OpenDir(0), m_TickDir(0), m_EmitLength(0), m_blockId(0),
													m_TickPowerCount(0), m_TickPower(0), m_TickResetPowerCount(0),m_TickWithDrawDir(0), m_oldPower(0)
{
	//setOutPutDirOpen((DirectionType)m_nDir, true);
	memset(m_Tickcount, 0, sizeof(m_Tickcount));
	memset(m_TickWithDrawCount, 0, sizeof(m_TickWithDrawCount));
	memset(m_CurLaserType, ContainerElectricLaserType_None, sizeof(m_CurLaserType));
	m_NeedTick = true;
}

ContainerElectricElement::ContainerElectricElement(const WCoord& blockpos, int baseindex) :WorldContainer(blockpos, baseindex), m_nDir(0), m_nPower(0), m_OpenDir(0), m_TickDir(0), m_EmitLength(0), m_blockId(0),
m_TickPowerCount(0), m_TickPower(0), m_TickResetPowerCount(0), m_oldPower(0), m_TickWithDrawDir(0)
{
	memset(m_Tickcount, 0, sizeof(m_Tickcount));
	memset(m_TickWithDrawCount, 0, sizeof(m_TickWithDrawCount));
	memset(m_CurLaserType, ContainerElectricLaserType_None, sizeof(m_CurLaserType));
	m_NeedTick = true;
}

ContainerElectricElement::ContainerElectricElement(const WCoord& blockpos, int dir, int power) : WorldContainer(blockpos, 0)
, m_nDir(dir), m_nPower(power), m_OpenDir(0), m_EmitLength(0), m_TickDir(0), m_blockId(0), m_TickPowerCount(0), m_TickPower(0), m_TickResetPowerCount(0), m_TickWithDrawDir(0)
{
	//setOutPutDirOpen((DirectionType)m_nDir, true);
	memset(m_Tickcount, 0, sizeof(m_Tickcount));
	memset(m_TickWithDrawCount, 0, sizeof(m_TickWithDrawCount));
	memset(m_CurLaserType, ContainerElectricLaserType_None, sizeof(m_CurLaserType));
	m_NeedTick = true;
}

int ContainerElectricElement::getObjType() const
{
	return OBJ_TYPE_BOX;
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerElectricElement::save(SAVE_BUFFER_BUILDER& builder)
{
	auto data = saveElectricContainer(builder);

	return FBSave::CreateChunkContainer(builder, getUnionType(), data.Union());
}

bool ContainerElectricElement::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerElectricBaseSave*>(srcdata);
	loadContainerCommon(src->basedata());
	m_nDir = src->dir();
	m_nPower = src->power();
	m_OpenDir = src->openDir();
	//m_Tickcount = src->tickEmit();
	m_TickDir = src->tickDir();
	m_EmitLength = src->emitLength();
	auto tickEmit = src->tickEmit();
	if (tickEmit)
	{
		//return true;
		for (size_t i = 0; i < tickEmit->size(); i++)
		{
			int ticks = tickEmit->Get(i);
			if (i < 6)
			{
				m_Tickcount[i] = ticks;
			}
		}
	}
	m_TickWithDrawDir = src->tickWithDrawDir();
	auto tickWithDraw = src->tickWithDraw();
	if (tickWithDraw)
	{
		//return true;
		for (size_t i = 0; i < tickWithDraw->size(); i++)
		{
			int ticks = tickWithDraw->Get(i);
			if (i < 6)
			{
				m_TickWithDrawCount[i] = ticks;
			}
		}
	}
	auto laserType = src->curLaserType();
	if (laserType)
	{
		//return true;
		for (size_t i = 0; i < laserType->size(); i++)
		{
			int type = laserType->Get(i);
			if (i < 6)
			{
				m_CurLaserType[i] = type;
			}
		}
	}
	m_TickPowerCount = src->tickPowerCount();
	m_TickPower = src->tickPower();
	m_TickResetPowerCount = src->tickResetPowerCount();
	return true;
}

flatbuffers::Offset<FBSave::ContainerElectricBaseSave> ContainerElectricElement::saveElectricContainer(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);
	return FBSave::CreateContainerElectricBaseSave(builder, basedata, m_nDir, m_nPower, m_OpenDir, builder.CreateVector(m_Tickcount, 6), m_TickDir, m_EmitLength, 
		builder.CreateVector(m_TickWithDrawCount, 6), m_TickWithDrawDir, builder.CreateVector(m_CurLaserType, 6), m_TickPowerCount, m_TickPower, m_TickResetPowerCount);
}

//旋转后重置数据
void ContainerElectricElement::resetDataByRotation(int dir)
{
	m_nDir = dir;
	m_OpenDir = 0;

	memset(m_Tickcount, 0, sizeof(m_Tickcount));
	memset(m_TickWithDrawCount, 0, sizeof(m_TickWithDrawCount));
	memset(m_CurLaserType, ContainerElectricLaserType_None, sizeof(m_CurLaserType));
}

int ContainerElectricElement::emitBlockLaser(int delay)
{
	int ret = 0;
	//这里是更新
	for (int i = 0; i < 6; i++)
	{
		if (getOutPutDirOpen(i))
		{
			updateBlockLaserByDir(i, delay);
		}
	}
	return ret;
}

void ContainerElectricElement::updateBlockLaser(int delay)
{
	emitBlockLaser(delay);
}

//光线获取其他垂直面的强度
static int getVerticalDirectionMaxPower(WCoord pos, int dir, World* pworld, int(&laserType)[6])
{
	int dir1 = dir;
	int dir2 = ReverseDirection(dir1);
	int maxPower = 0;
	int power = 0;
	int blockid = 0;
	WCoord curPos = pos;
	for (int i = 0; i < 6; i++)
	{
		if (i != dir1 && i != dir2)
		{
			power = 0;
			curPos = pos;
			for (int k = 0; k < WIRLESS_BLOCK_LASER_MAXL; k++)
			{
				curPos = NeighborCoord(curPos, i);
				blockid = pworld->getBlockID(curPos);
				if (IsLaserBlock(blockid))
				{
					//dirNum++;
				}
				else if (IsCanEmitBlockLaser(blockid))
				{
					BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockid);
					if (material)
					{
						power = material->outputWeakEnergy(pworld, curPos, ReverseDirection(i));
						laserType[i] = material->electricEmitBlockLaserType(pworld, curPos, ReverseDirection(i));
					}
					break;
				}
				else
				{
					break;
				}
			}
			maxPower = power > maxPower ? power : maxPower;
		}
	}
	return maxPower;
}

static bool needSetLaserBlock(int(&laserType)[6])
{
	for (int i = 0; i < 6; i++)
	{
		if (laserType[i] > 0) return true;
	}
	return false;
}

static bool isElectricInputDir(int dir, World* pword, const WCoord& blockpos, int blockid)
{
	if (!pword)
	{
		return false;
	}
	BlockWirlessElectricUnit* ele = dynamic_cast<BlockWirlessElectricUnit*>(g_BlockMtlMgr.getMaterial(blockid));
	if (!ele)
	{
		return false;
	}
	return ele->isInputDir(pword, blockpos, dir);
}

int ContainerElectricElement::emitBlockLaserByDir(int dir)
{
	if (m_World == NULL)
	{
		return 0;
	}
	if (!getOutPutDirOpen(dir))
	{
		return 0;
	}
	int defaultLaserLength = GetDefaultBlockLaserLength(m_blockId);
	int reverseLaserLength = 0;
	/*int power = getPower(dir);
	if (power <= 0)
	{
		return 0;
	}*/
	int power = getPower(dir);
	WCoord curPos = m_BlockPos;
	int i = 0;
	int blockid = 0;
	int flag = 2;
	WCoord reverseStar;
	bool findReverseDir = false;
	int reversePower = 0;
	int headNum = 0;
	int reverseDir = ReverseDirection(dir);
	int laserType[6] = { 0, 0, 0, 0, 0, 0 };
	int delay = 1;
	//int lastNum = getDirLaserLength(dir);

	//先找到反面的电源
	for (int k = 0; k < defaultLaserLength + WIRLESS_BLOCK_LASER_MAXL; k++)
	{
		curPos = NeighborCoord(curPos, dir);
		blockid = m_World->getBlockID(curPos);
		if (blockid == BLOCK_RAY_WIRE)
		{
			headNum++;
		}
		else if (IsCanEmitBlockLaser(blockid))
		{
			BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockid);
			if (material)
			{
				reversePower = material->outputWeakEnergy(m_World, curPos, (DirectionType)reverseDir);
				if (reversePower > 0)
				{
					reverseStar = curPos;
					findReverseDir = true;
				}
				reverseLaserLength = GetDefaultBlockLaserLength(blockid);
				////对面这个电器这帧要tick?
				//if (m_World->getBlockTickMgr()->isBlockTickToScheduled(curPos, blockid) && isElectricInputDir(reverseDir, m_World, curPos, blockid))
				//{
				//	int newPower = getPower();
				//	setPower(m_oldPower);
				//	material->blockTick(m_World, curPos);
				//	setPower(newPower);
				//	delay = 2;
				//}
			}
			break;
		}
		else
		{
			break;
		}
	}

	int overlap = defaultLaserLength;
	if (findReverseDir)
	{
		if (headNum <= reverseLaserLength)
		{
			overlap = 0;
		}
		else
		{
			overlap = headNum - reverseLaserLength;
		}
	}

	bool lastSet = false;
	int maxPower = 0;
	curPos = m_BlockPos;
	for (; i < defaultLaserLength; i++)
	{
		curPos = NeighborCoord(curPos, dir);
		blockid = m_World->getBlockID(curPos);
		if (!IsAirBlockID(blockid))
		{
			//根据四周算个强度,强度不一样就覆盖
			if (blockid == BLOCK_RAY_WIRE)
			{
				int data = m_World->getBlockData(curPos);
				maxPower = getVerticalDirectionMaxPower(curPos, dir, m_World, laserType);
				if (i >= overlap)
				{
					maxPower = maxPower > reversePower ? maxPower : reversePower;
				}
				maxPower = maxPower > power ? maxPower : power;
				if (maxPower != data)
				{
					lastSet = true;
					m_World->setBlockData(curPos, maxPower, 2);
				}
				else
				{
					lastSet = false;
					m_World->markBlockForUpdate(curPos);
				}
				//这里判断这个射线方块是否有电, 没电设置数据
				if (!((i >= overlap && reversePower !=0)|| power>0))
				{
					maxPower = 0;
				}
				setRayContainerRenderDir(dir, maxPower, curPos, m_World);
			}
			else
			{
				break;
			}
		}
		else
		{
			m_World->setBlockAll(curPos, BLOCK_RAY_WIRE, power, 2);
			setRayContainerRenderDir(dir, power, curPos, m_World);
			lastSet = true;
		}
	}
	//发射的比上次的少
	/*if (i < lastNum)
	{
		withDrawBlockLaserByDir(curPos, lastNum - i, dir);
	}
	else*/
	m_CurLaserType[dir] = ContainerElectricLaserType_Preview;
	if (power > 0)
	{
		m_CurLaserType[dir] = ContainerElectricLaserType_Normal;
		if (i >= 15)
		{
			curPos = NeighborCoord(curPos, dir);
		}
		blockid = m_World->getBlockID(curPos);
		//正对面情况,不要去触发了,不然会死循环
		if (IsCanEmitBlockLaser(blockid) && m_World->getIndirectPowerLevelTo(curPos, ReverseDirection(dir)) > 0)
		{
			//
		}
		else
		{
			//m_World->notifyBlockSides(curPos, BLOCK_RAY_WIRE);
			if (i == 0 && IsCanEmitBlockLaser(blockid))
			{
				if (isElectricInputDir(reverseDir, m_World, curPos, blockid))
				{
					m_World->getBlockTickMgr()->scheduleBlockUpdate(curPos, blockid, delay);
				}
			}
			else if (/*lastSet*/true)
			{
				//有改变再激发
				if (IsCanEmitBlockLaser(blockid))
				{
					if (isElectricInputDir(reverseDir, m_World, curPos, blockid))
					{
						m_World->getBlockTickMgr()->scheduleBlockUpdate(curPos, blockid, delay);
					}
				}
				else
				{
					//只激发前面的方块就可以了
					m_World->notifyBlock(curPos, BLOCK_RAY_WIRE);
					if (IsMusicBlock(blockid))
					{
						WCoord neighborPos;
						int neighborId;
						for (int k = 0; k < 6; k++)
						{
							if (k == reverseDir)
							{
								continue;
							}
							neighborPos = NeighborCoord(curPos, k);
							neighborId = m_World->getBlockID(neighborPos);
							if (IsMusicBlock(neighborId))
							{
								m_World->notifyBlock(neighborPos, BLOCK_RAY_WIRE);
							}
						}
					}
				}
			}
		}
	}
	//设置这个方向发射了多少格
	//setDirLaserLength(dir, i);
	return i;
}

void ContainerElectricElement::setOutPutDirOpen(int dir, bool open, bool needEmit)
{
	if (getOutPutDirOpen(dir) == open)
	{
		return;
	}
	DIR_BIT_RESET(m_OpenDir, dir);
	if (open)
	{
		DIR_BIT_SET(m_OpenDir, dir);
		if (needEmit)
		{
			emitBlockLaserByDir(dir);
		}
	}
	else if (needEmit)
	{
		withDrawBlockLaserByDir(dir);
	}
}

bool ContainerElectricElement::getOutPutDirOpen(int dir)
{
	return DIR_BIT_GET(m_OpenDir, dir);
}

int ContainerElectricElement::getPower(int dir)
{
	if (getOutPutDirOpen((DirectionType)dir))
	{
		return m_nPower;
	}
	return 0;
}

int ContainerElectricElement::getPower()
{
	return m_nPower;
}

void ContainerElectricElement::withDrawBlockLaser(int delay)
{
	withDrawBlockLaserByDir((DirectionType)m_nDir, delay);
}

void ContainerElectricElement::withDrawBlockLaserByDir(int dir, int delay)
{
	if (delay > 0)
	{
		m_TickWithDrawCount[dir] = delay;
		DIR_BIT_SET(m_TickWithDrawDir, dir);
	}
	else
	{
		_withDrawBlockLaserByDir(dir);
	}
}

struct MaskRayBlockEventFlag
{
	MaskRayBlockEventFlag()
	{
		BlockRayWire::ms_rayNeedNotifyBlockRemove = false;
	}
	void unLock()
	{
		BlockRayWire::ms_rayNeedNotifyBlockRemove = true;
	}	
	~MaskRayBlockEventFlag()
	{
		BlockRayWire::ms_rayNeedNotifyBlockRemove = true;
	}
};

void ContainerElectricElement::_withDrawBlockLaserByDir(int dir)
{
	if (m_World == NULL)
	{
		return;
	}
	int defaultLaserLength = GetDefaultBlockLaserLength(m_blockId);
	int reverseLaserLength = 0;
	MaskRayBlockEventFlag flag;
	WCoord curPos = m_BlockPos;
	int blockid = 0;
	WCoord reverseStar;
	bool findReverseDir = false;
	int reversePower = 0;
	int headNum = 0;
	int reverseDir = ReverseDirection(dir);
	int selfLaserType = 0;
	if (getOutPutDirOpen(dir))
	{
		selfLaserType = 1;
	}
	int reverseLaserType = 0;
	int laserType[6] = { 0, 0, 0, 0, 0, 0 };
	for (int i = 0; i < defaultLaserLength + WIRLESS_BLOCK_LASER_MAXL; i++)
	{
		curPos = NeighborCoord(curPos, dir);
		blockid = m_World->getBlockID(curPos);
		if (blockid == BLOCK_RAY_WIRE)
		{
			headNum++;
		}
		else if (IsCanEmitBlockLaser(blockid))
		{
			BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockid);
			if (material)
			{
				reversePower = material->outputWeakEnergy(m_World, curPos, ReverseDirection(dir));
				if (reversePower > 0)
				{
					reverseStar = curPos;
					findReverseDir = true;
				}
				reverseLaserType = material->electricEmitBlockLaserType(m_World, curPos, ReverseDirection(dir));
				reverseLaserLength = GetDefaultBlockLaserLength(blockid);

				//对面这个电器这帧要tick?
				/*if (m_World->getBlockTickMgr()->isBlockTickToScheduled(curPos, blockid) && isElectricInputDir(reverseDir, m_World, curPos, blockid))
				{
					int newPower = getPower();
					setPower(m_oldPower);
					material->blockTick(m_World, curPos);
					setPower(newPower);
				}*/
			}
			break;
		}
		else
		{
			break;
		}
	}
	//有个特殊情况, 如果刚好到第30个, 那么应该还要往前探一格.
	if (headNum == defaultLaserLength + WIRLESS_BLOCK_LASER_MAXL && ContainerElectricLaserType_None == reverseLaserLength)
	{
		curPos = NeighborCoord(curPos, dir);
		blockid = m_World->getBlockID(curPos);
		if (IsCanEmitBlockLaser(blockid))
		{
			BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockid);
			if (material)
			{
				reversePower = material->outputWeakEnergy(m_World, curPos, ReverseDirection(dir));
				if (reversePower > 0)
				{
					reverseStar = curPos;
					findReverseDir = true;
				}
				reverseLaserType = material->electricEmitBlockLaserType(m_World, curPos, ReverseDirection(dir));
				reverseLaserLength = GetDefaultBlockLaserLength(blockid);
			}
		}
	}
	int overlap = defaultLaserLength;
	int overlap2 = defaultLaserLength;
	if (findReverseDir)
	{
		if (headNum <= reverseLaserLength)
		{
			overlap = 0;
		}
		else
		{
			overlap = headNum - reverseLaserLength;
		}
	}
	overlap2 = headNum - reverseLaserLength;
	if (overlap2 < 0)
	{
		overlap2 = 0;
	}
	m_CurLaserType[dir] = ContainerElectricLaserType_None;
	if (selfLaserType)
	{
		m_CurLaserType[dir] = ContainerElectricLaserType_Preview;
	}
	if (true/*headNum > 0*/)
	{
		int power = 0;
		curPos = m_BlockPos;
		int maxPower = 0;
		int curReverseLaserType = reverseLaserType;
		for (int i = 0; i < headNum; i++)
		{
			if (i < overlap2)
			{
				curReverseLaserType = ContainerElectricLaserType_None;
			}
			else
			{
				curReverseLaserType = reverseLaserType;
			}
			memset(laserType, 0, sizeof(laserType));
			laserType[reverseDir] = reverseLaserType;
			laserType[dir] = selfLaserType;

			curPos = NeighborCoord(curPos, dir);
			maxPower = getVerticalDirectionMaxPower(curPos, dir, m_World, laserType);
			if (i >= overlap)
			{
				maxPower = maxPower > reversePower ? maxPower : reversePower;
			}
			if (maxPower <= 0)
			{
				if (needSetLaserBlock(laserType))
				{
					m_World->setBlockData(curPos, 0, 2);
					m_World->markBlockForUpdate(curPos);
					if (curReverseLaserType == ContainerElectricLaserType_None && selfLaserType == ContainerElectricLaserType_None)
					{
						clearContainerRenderDir(dir, curPos, true, m_World);
					}
					else if (curReverseLaserType != ContainerElectricLaserType_Normal && selfLaserType != ContainerElectricLaserType_Normal)
					{
						setRayContainerRenderDir(dir, 0, curPos, m_World);
					}
				}
				else
				{
					m_World->setBlockAll(curPos, 0, 0, 2);
				}
			}
			else
			{
				m_World->setBlockData(curPos, maxPower, 2);
				if (curReverseLaserType == ContainerElectricLaserType_None && selfLaserType == ContainerElectricLaserType_None)
				{
					clearContainerRenderDir(dir, curPos, true, m_World);
				}
				else if (curReverseLaserType != ContainerElectricLaserType_Normal && selfLaserType != ContainerElectricLaserType_Normal)
				{
					setRayContainerRenderDir(dir, 0, curPos, m_World);
				}
			}
		}
		//m_World->notifyBlockSides(curPos, BLOCK_RAY_WIRE);
		flag.unLock();
		//只激发前面的方块就可以了
		curPos = NeighborCoord(curPos, dir);
		blockid = m_World->getBlockID(curPos);
		//正对面情况,不要去触发了,不然会死循环
		if (IsCanEmitBlockLaser(blockid) && m_World->getIndirectPowerLevelTo(curPos, ReverseDirection(dir)) > 0)
		{
			//
		}
		else
		{
			//m_World->notifyBlockSides(curPos, BLOCK_RAY_WIRE);
			if (IsCanEmitBlockLaser(blockid))
			{
				if (isElectricInputDir(reverseDir, m_World, curPos, blockid))
				{
					m_World->getBlockTickMgr()->scheduleBlockUpdate(curPos, blockid, 1);
				}
			}
			else
			{
				//只激发前面的方块就可以了
				m_World->notifyBlock(curPos, BLOCK_RAY_WIRE);
				if (IsMusicBlock(blockid))
				{
					WCoord neighborPos;
					int neighborId;
					for (int k = 0; k < 6; k++)
					{
						if (k == reverseDir)
						{
							continue;
						}
						neighborPos = NeighborCoord(curPos, k);
						neighborId = m_World->getBlockID(neighborPos);
						if (IsMusicBlock(neighborId))
						{
							m_World->notifyBlock(neighborPos, BLOCK_RAY_WIRE);
						}
					}
				}
			}
		}
	}
}

void ContainerElectricElement::withDrawRangeBlockLaserByDir(const WCoord& startPos, int num, int dir, World* pworld)
{
	if (pworld == NULL)
	{
		return;
	}
	WCoord curPos = startPos;
	int blockid = 0;
	WCoord reverseStar;
	bool findReverseDir = false;
	int reversePower = 0;
	int headNum = 0;
	int maxNum = num + WIRLESS_BLOCK_LASER_MAXL;
	int reverseLaserLength = 0;
	int reverseDir = ReverseDirection(dir);
	int selfLaserType = 0;
	int reverseLaserType = 0;
	int laserType[6] = { 0, 0, 0, 0, 0, 0 };
	MaskRayBlockEventFlag flag;
	for (int i = 0; i < maxNum; i++)
	{
		curPos = NeighborCoord(curPos, dir);
		blockid = pworld->getBlockID(curPos);
		if (blockid == BLOCK_RAY_WIRE)
		{
			headNum++;
		}
		else if (IsCanEmitBlockLaser(blockid))
		{
			BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockid);
			if (material)
			{
				reversePower = material->outputWeakEnergy(pworld, curPos, ReverseDirection(dir));
				if (reversePower > 0)
				{
					reverseStar = curPos;
					findReverseDir = true;
				}
				reverseLaserType = material->electricEmitBlockLaserType(pworld, curPos, ReverseDirection(dir));
				reverseLaserLength = GetDefaultBlockLaserLength(blockid);
				////对面这个电器这帧要tick?
				//if (pworld->getBlockTickMgr()->isBlockTickToScheduled(curPos, blockid) && isElectricInputDir(reverseDir, pworld, curPos, blockid))
				//{
				//	int newPower = getPower();

				//	material->blockTick(pworld, curPos);
				//}
			}
			break;
		}
		else
		{
			break;
		}
	}
	//有个特殊情况, 如果刚好到第30个, 那么应该还要往前探一格.
	if (headNum == maxNum && ContainerElectricLaserType_None == reverseLaserLength)
	{
		curPos = NeighborCoord(curPos, dir);
		blockid = pworld->getBlockID(curPos);
		if (IsCanEmitBlockLaser(blockid))
		{
			BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockid);
			if (material)
			{
				reversePower = material->outputWeakEnergy(pworld, curPos, ReverseDirection(dir));
				if (reversePower > 0)
				{
					reverseStar = curPos;
					findReverseDir = true;
				}
				reverseLaserType = material->electricEmitBlockLaserType(pworld, curPos, ReverseDirection(dir));
				reverseLaserLength = GetDefaultBlockLaserLength(blockid);
			}
		}
	}
	if (true/*headNum > 0*/)
	{
		int overlap = num;
		if (findReverseDir)
		{
			if (headNum <= reverseLaserLength)
			{
				overlap = 0;
			}
			else
			{
				overlap = headNum - reverseLaserLength;
			}
		}
		int power = 0;
		curPos = startPos;
		int maxPower = 0;
		for (int i = 0; i < headNum; i++)
		{
			memset(laserType, 0, sizeof(laserType));
			laserType[reverseDir] = reverseLaserType;
			laserType[dir] = selfLaserType;

			curPos = NeighborCoord(curPos, dir);
			maxPower = getVerticalDirectionMaxPower(curPos, dir, pworld, laserType);
			if (i >= overlap)
			{
				maxPower = maxPower > reversePower ? maxPower : reversePower;
			}
			if (maxPower <= 0)
			{
				if (needSetLaserBlock(laserType))
				{
					pworld->setBlockData(curPos, 0, 2);
					pworld->markBlockForUpdate(curPos);
					if (reverseLaserType == ContainerElectricLaserType_None && selfLaserType == ContainerElectricLaserType_None)
					{
						clearContainerRenderDir(dir, curPos, true, pworld);
					}
					else if (reverseLaserType != ContainerElectricLaserType_Normal && selfLaserType != ContainerElectricLaserType_Normal)
					{
						setRayContainerRenderDir(dir, 0, curPos, pworld);
					}
				}
				else
				{
					pworld->setBlockAll(curPos, 0, 0, 2);
				}
			}
			else
			{
				pworld->setBlockData(curPos, maxPower, 2);
				if (reverseLaserType == ContainerElectricLaserType_None && selfLaserType == ContainerElectricLaserType_None)
				{
					clearContainerRenderDir(dir, curPos, true, pworld);
				}
				else if (reverseLaserType != ContainerElectricLaserType_Normal && selfLaserType != ContainerElectricLaserType_Normal)
				{
					setRayContainerRenderDir(dir, 0, curPos, pworld);
				}
			}
		}
		//pworld->notifyBlockSides(curPos, BLOCK_RAY_WIRE);
		flag.unLock();
		curPos = NeighborCoord(curPos, dir);
		if (IsCanEmitBlockLaser(blockid))
		{
			if (isElectricInputDir(reverseDir, pworld, curPos, blockid))
			{
				pworld->getBlockTickMgr()->scheduleBlockUpdate(curPos, blockid, 1);
			}
		}
		else
		{
			//只激发前面的方块就可以了
			pworld->notifyBlock(curPos, BLOCK_RAY_WIRE);
			if (IsMusicBlock(blockid))
			{
				WCoord neighborPos;
				int neighborId;
				for (int k = 0; k < 6; k++)
				{
					if (k == reverseDir)
					{
						continue;
					}
					neighborPos = NeighborCoord(curPos, k);
					neighborId = pworld->getBlockID(neighborPos);
					if (IsMusicBlock(neighborId))
					{
						pworld->notifyBlock(neighborPos, BLOCK_RAY_WIRE);
					}
				}
			}
		}
	}
}

void ContainerElectricElement::leaveWorld()
{
	//withDrawBlockLaser();
	//if (m_World && m_blockId == BLOCK_STAR_ENERGYLIGHT)
	//{
		//g_BlockMtlMgr.RemoveDynamicRenderData(m_World, m_BlockPos);
	//}
	WorldContainer::leaveWorld();
}

void ContainerElectricElement::setDirLaserLength(int dir, unsigned int num)
{
	if (getDirLaserLength(dir) == num)
	{
		return;
	}
	int offset = dir << 2;
	m_EmitLength &= (~(0b1111 << offset));
	if (num > 0)
	{
		m_EmitLength |= (num << offset);
	}
}

int ContainerElectricElement::getDirLaserLength(int dir)
{
	int offset = dir << 2;
	return (m_EmitLength >> offset) & 0b1111;
}

void ContainerElectricElement::updateTick()
{
	if (m_TickPowerCount > 0)
	{
		m_TickPowerCount--;
		if (m_TickPowerCount <= 0)
		{
			_setPower(m_TickPower);
			m_TickPower = 0;
		}
	}

	for (int i = 0; i < 6; i++)
	{
		if (m_Tickcount[i] > 0)
		{
			m_Tickcount[i]--;
			if (m_Tickcount[i] <= 0)
			{
				emitBlockLaserByDir(i);
				DIR_BIT_RESET(m_TickDir, i);
			}
		}
	}

	for (int i = 0; i < 6; i++)
	{
		if (m_TickWithDrawCount[i] > 0)
		{
			m_TickWithDrawCount[i]--;
			if (m_TickWithDrawCount[i] <= 0)
			{
				_withDrawBlockLaserByDir(i);
				DIR_BIT_RESET(m_TickWithDrawDir, i);
			}
		}
	}
}

void ContainerElectricElement::updateBlockLaserByDir(int dir, int delay)
{
	if (delay > 0)
	{
		m_Tickcount[dir] = delay;
		DIR_BIT_SET(m_TickDir, dir);
	}
	else
	{
		emitBlockLaserByDir(dir);
	}
}

void ContainerElectricElement::blockRemoveWithDrawBlockLaser()
{
	m_OpenDir = 0;
	withDrawBlockLaser();
}

void ContainerElectricElement::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	if (pworld)
	{
		m_blockId = pworld->getBlockID(m_BlockPos);
	}
	if (pworld && m_blockId == BLOCK_STAR_ENERGYLIGHT)
	{
		g_BlockMtlMgr.SetDynamicRendererData(pworld, m_BlockPos, m_nPower > 0);
	}
}

void ContainerElectricElement::setPower(int power, int delay)
{
	if (delay > 0)
	{
		m_TickPowerCount = delay;
		m_TickPower = power;
	}
	else
	{
		_setPower(power);
	}
}

void ContainerElectricElement::_setPower(int power)
{
	m_oldPower = m_nPower;
	m_nPower = power;
	if (m_World)
	{
		m_World->markBlockForUpdate(m_BlockPos);
	}

	if (m_blockId == BLOCK_STAR_ENERGYLIGHT && m_World) 
	{
		g_BlockMtlMgr.SetDynamicRendererData(m_World, m_BlockPos, m_nPower > 0);
	}
}

bool ContainerElectricElement::shouldWithDrawPower()
{
	if (m_nPower > 0)
	{
		//已经有要把它设置为false了的
		if (m_TickPowerCount > 0 && m_TickPower <= 0)
		{
			return false;
		}
		return true;
	}
	else
	{
		//有要设置它有电的?
		if (m_TickPowerCount > 0 && m_TickPower > 0)
		{
			//直接射出
			setPower(m_TickPower, 0);
			emitBlockLaser(0);
			return true;
		}
		return false;
	}
}

bool ContainerElectricElement::shouldEmitBlockLaser(int power, int& delay)
{
	delay = 0;
	if (power > 0)
	{
		int oldPower = getPower();
		bool needReset = false;
		for (int i = 0; i < 6; i++)
		{
			if (m_TickWithDrawCount[i] > 0)
			{
				_withDrawBlockLaserByDir(i);
				m_TickWithDrawCount[i] = 0;
				DIR_BIT_RESET(m_TickWithDrawDir, i);
				needReset = true;
				delay = getDelayRate() + ContainerElectricElementWithDrawLaserDelay;
			}
		}
		if (m_TickPower <= 0 && m_TickPowerCount > 0)
		{
			_setPower(m_TickPower);
			//emitBlockLaser(0);
			m_TickPowerCount = -1;
			needReset = true;
			delay = getDelayRate() + ContainerElectricElementWithDrawLaserDelay;
		}
		if (!needReset)
		{
			if (m_TickPower > 0 && m_TickPowerCount > 0)
			{
				if (m_TickPower != power)
				{
					return true;
				}
				return false;
			}
		}
		if (power != oldPower)
		{
			needReset = true;
		}
		return needReset;
	}
	return false;
}

void ContainerElectricElement::setRayContainerRenderDir(const int dir, const int power, const WCoord& pos, World* pworld)
{
	if (!pworld)
	{
		return;
	}
	if (pworld->getContainerMgr() == NULL)
	{
		return;
	}
	auto container = dynamic_cast<ContainerElectricRay*>(pworld->getContainerMgr()->getContainer(pos));
	if (container == NULL)
	{
		container = SANDBOX_NEW(ContainerElectricRay, pos, 0);
		pworld->getContainerMgr()->spawnContainer(container);
	}
	if (container)
	{
		container->setDirConnent(dir, power > 0);
	}
}

void ContainerElectricElement::clearContainerRenderDir(const int dir, const WCoord& pos, bool needCreate, World* pworld)
{
	if (!pworld)
	{
		return;
	}
	if (pworld->getContainerMgr() == NULL)
	{
		return;
	}
	auto container = dynamic_cast<ContainerElectricRay*>(pworld->getContainerMgr()->getContainer(pos));
	if (container == NULL)
	{
		if (!needCreate)
		{
			return;
		}
		container = SANDBOX_NEW(ContainerElectricRay, pos, 0);
		pworld->getContainerMgr()->spawnContainer(container);
	}
	if (container)
	{
		container->clearDirConect(dir);
	}
}

void ContainerElectricElement::mechaRemoveCallBack()
{
	int dir = m_OpenDir;
	m_OpenDir = 0;
	withDrawBlockLaser();
	m_OpenDir = dir;
}

/************************************************ContainerElectricSplitter begin****************************************************************/

flatbuffers::Offset<FBSave::ChunkContainer> ContainerElectricSplitter::save(SAVE_BUFFER_BUILDER& builder)
{
	auto data = saveElectricContainer(builder);
	auto actor = FBSave::CreateContainerElectricSplitterSave(builder, data);
	return FBSave::CreateChunkContainer(builder, getUnionType(), actor.Union());
}

bool ContainerElectricSplitter::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerElectricSplitterSave*>(srcdata);
	if (src)
	{
		ContainerElectricElement::load(src->basedata());
	}
	return true;
}

//int ContainerElectricSplitter::emitBlockLaser(int delay)
//{
//	int reverseDir = ReverseDirection(m_nDir);
//	int sum = 0;
//	for (int i = 0; i < 6; i++)
//	{
//		if (i != reverseDir)
//		{
//			sum += emitBlockLaserByDir((DirectionType)i);
//		}
//	}
//	
//	return sum;
//}

void ContainerElectricSplitter::withDrawBlockLaser(int delay)
{
	//输入方向就不用判断了
	int reverseDir = ReverseDirection(m_nDir);
	for (int i = 0; i < 6; i++)
	{
		if (i != reverseDir)
		{
			withDrawBlockLaserByDir((DirectionType)i, delay);
		}
	}
}

void ContainerElectricSplitter::initDir()
{
	//左右2端默认输出
	setOutPutDirOpen(RotateDir90(m_nDir), true);
	setOutPutDirOpen(RotateDirPos90(m_nDir), true);
}

/************************************************ContainerElectricDelay begin****************************************************************/
flatbuffers::Offset<FBSave::ChunkContainer> ContainerElectricDelay::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveElectricContainer(builder);
	auto actor = FBSave::CreateContainerElectricDelaySave(builder, basedata, m_Lever);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerElectricDelaySave, actor.Union());
}

bool ContainerElectricDelay::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerElectricDelaySave*>(srcdata);
	if (src)
	{
		ContainerElectricElement::load(src->basedata());
		m_Lever = src->level();
	}
	return true;
}

int ContainerElectricDelay::emitBlockLaser(int delay)
{
	updateBlockLaserByDir(m_nDir, delay);
	return 0;
}

void ContainerElectricDelay::withDrawBlockLaser(int delay)
{
	ContainerElectricElement::withDrawBlockLaser(delay);
}

void ContainerElectricDelay::_emitBlockLaser()
{
	emitBlockLaserByDir((DirectionType)m_nDir);
}

void ContainerElectricDelay::addLever()
{
	m_Lever++;
	if (m_Lever > 6)
	{
		m_Lever = 1;
	}
}

int ContainerElectricDelay::getDelayRate()
{
	return m_Lever * 0.1 * 20;
}
/************************************************ContainerElectricResister begin****************************************************************/

flatbuffers::Offset<FBSave::ChunkContainer> ContainerElectricResister::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveElectricContainer(builder);
	auto actor = FBSave::CreateContainerElectricDelaySave(builder, basedata, m_Lever);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerElectricResisterSave, actor.Union());
}

bool ContainerElectricResister::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerElectricDelaySave*>(srcdata);
	if (src)
	{
		ContainerElectricElement::load(src->basedata());
		m_Lever = src->level();
	}
	return true;
}

int ContainerElectricResister::getPower(int dir)
{
	if (getOutPutDirOpen(dir))
	{
		if (m_Lever == 0)
		{
			return m_nPower / 2;
		}
		else
		{
			int elemination = 1;
			if (m_Lever == 1)
			{
				elemination = 7;
			}
			else if (m_Lever == 2)
			{
				elemination = 4;
			}
			else if (m_Lever == 3)
			{
				//elemination = 7;
			}
			if (m_nPower > elemination)
			{
				return m_nPower - elemination;
			}
		}
	}
	return 0;
}

void ContainerElectricResister::addLever()
{
	m_Lever++;
	if (m_Lever > 3)
	{
		m_Lever = 0;
	}
	if (getPower(m_nDir) <= 0)
	{
		withDrawBlockLaser(2);
	}
	else
	{
		emitBlockLaser();
	}
}

/************************************************ContainerElectricCounter begin****************************************************************/

flatbuffers::Offset<FBSave::ChunkContainer> ContainerElectricCounter::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveElectricContainer(builder);
	auto actor = FBSave::CreateContainerElectricCounterSave(builder, basedata, m_upDir, m_leftDir, m_lastInputPort1State, m_lastInputPort2State, m_withdrawTickCount);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerElectricCounterSave, actor.Union());
}

bool ContainerElectricCounter::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerElectricCounterSave*>(srcdata);
	if (src)
	{
		ContainerElectricElement::load(src->basedata());
		m_upDir = (DirectionType)src->updir();
		m_leftDir = (DirectionType)src->leftdir();
		m_lastInputPort1State = src->ip1state();
		m_lastInputPort2State = src->ip2state();
		m_withdrawTickCount = src->tickcount();
	}
	return true;
}

void ContainerElectricCounter::enterWorld(World* pworld)
{
	ContainerElectricElement::enterWorld(pworld);

	registerUpdateTick();
}

void ContainerElectricCounter::updateTick()
{
	ContainerElectricElement::updateTick();
	ContainerUpdate();
}

DirectionType ContainerElectricCounter::calculateInputPort(DirectionType dir)
{
	if (dir == DIR_NOT_INIT)
		return (DirectionType)m_nDir;

	if (dir < 0 || dir > 3)
		dir = DIR_NEG_X;

	if (m_upDir == DIR_POS_Y && m_nDir == DIR_NOT_INIT)
	{
		//地面放置
		m_nDir = dir;
		m_leftDir = RotateDir90(m_nDir);
	}
	else if (m_upDir == DIR_NOT_INIT && m_nDir == DIR_POS_Y)
	{
		//侧面放置
		m_upDir = dir;
		switch (m_upDir)
		{
		case DIR_NEG_X:
			m_leftDir = DIR_POS_Z;
			break;
		case DIR_POS_X:
			m_leftDir = DIR_NEG_Z;
			break;
		case DIR_NEG_Z:
			m_leftDir = DIR_NEG_X;
			break;
		case DIR_POS_Z:
			m_leftDir = DIR_POS_X;
			break;
		default:
			m_leftDir = (DirectionType)m_nDir;
			break;
		}
	}
	return (DirectionType)m_nDir;
}

DirectionType ContainerElectricCounter::getInputDir1()
{
	return m_nDir != -1 ? ReverseDirection(m_nDir) : (DirectionType)0;
}

DirectionType ContainerElectricCounter::getInputDir2()
{
	return m_leftDir != DIR_NOT_INIT ? m_leftDir : (DirectionType)0;
}

void ContainerElectricCounter::powerAddOne()
{
	m_nPower = clamp<int>(++m_nPower, 0, 15);
	LOG_INFO(u8"ContainerElectricCounter::powerAddOne(): %d", m_nPower);
}

bool ContainerElectricCounter::isComplete()
{
	return m_upDir != DIR_NOT_INIT && m_leftDir != DIR_NOT_INIT;
}

void ContainerElectricCounter::ContainerUpdate()
{
	if (m_withdrawTickCount > 0)
	{
		m_withdrawTickCount--;
		if (m_withdrawTickCount <= 0)
		{
			m_nPower = 0;
			withDrawBlockLaser();
			if (m_World)
			{
				m_World->markBlockForUpdate(m_BlockPos);
				if (m_nDir != -1)
					m_World->notifyBlock(NeighborCoord(m_BlockPos, m_nDir), m_World->getBlockID(m_BlockPos));
			}
		}
	}
}