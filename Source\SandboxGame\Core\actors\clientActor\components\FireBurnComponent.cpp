#include "FireBurnComponent.h"

#include "ClientActor.h"
//#include "OgreSoundSystem.h"
#include "ActorVehicleAssemble.h"
#include "world.h"
#include "EffectManager.h"
#include "ActorLocoMotion.h"
#include "MpActorManager.h"
#include "GameNetManager.h"
#include "world_types.h"

#include "AttackedComponent.h"
#include "SoundComponent.h"
#include "LuaInterfaceProxy.h"
#include "SandboxIdDef.h"

using namespace MINIW;
IMPLEMENT_COMPONENTCLASS(FireBurnComponent)

FireBurnComponent::FireBurnComponent()
{

}
void FireBurnComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	ClientActor* actor = dynamic_cast<ClientActor*>(owner);
	if (actor) {
		actor->BindFireBurnComponent(this);
	}
	Super::OnEnterOwner(owner);
}
void FireBurnComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	ClientActor* actor = dynamic_cast<ClientActor*>(owner);
	if (actor) {
		actor->BindFireBurnComponent(nullptr);
	}
	Super::OnLeaveOwner(actor);
}

void FireBurnComponent::setFire(int buffid, int bufflv, int ticks /* = -1 */, long long fromObjid /* = 0*/)
{
	bool succ = GetOwner()->Event2().Emit<int,int,int,long long>("setFire", buffid, bufflv, ticks, fromObjid);
	if (succ)
	{
		return;
	}
	setFire_Base(buffid, bufflv, ticks, fromObjid);
	return;
}

void FireBurnComponent::setFire_Base(int buffid, int bufflv, int ticks /* = -1 */, long long fromObjid /* = 0*/)
{

}