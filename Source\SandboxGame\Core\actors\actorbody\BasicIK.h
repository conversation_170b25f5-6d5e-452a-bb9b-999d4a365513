#pragma once
#include "Animation/Skeletons/BoneNode.h"
#include "Animation/Skeletons/SkinnedSkeleton.h"

const float kSmallNumber = math::epsilon_legacy_euler();

struct BasicIKData
{
    Rainbow::BoneNode* _boneA = nullptr;
    Rainbow::BoneNode* _boneB = nullptr;
    Rainbow::BoneNode* _boneC = nullptr;
    Rainbow::GameObject* _effector = nullptr;
    Rainbow::Vector3f _poleVectorRelativeToEffector = Rainbow::Vector3f::zero;
    float _boneALength = 0.0f;
    float _boneBLength = 0.0f;
    float _weight = 1.0f;
    bool _bEnableStretch = false;
    float _stretchStartRatio = 0.0f;
    float _stretchMaxRatio = 0.0f;
    Rainbow::Vector3f _primaryAxis = Rainbow::Vector3f(0, 0, 1);
    Rainbow::Vector3f _secondaryAxis = Rainbow::Vector3f(0, 1, 0);
    float _secondaryAxisWeight = 0.0f;
};

class BasicIK
{
public:
    enum class EControlRigVectorKind
    {
        kDirection,
        kLocation
    };

    // Constructor
    BasicIK() = default;
    ~BasicIK() = default;

    // Setup methods

    void SetIDData(const BasicIKData& Data);

    // Main IK solving methods
    static void SolveTwoBoneIK(const Rainbow::Vector3f& RootPos, const Rainbow::Vector3f& JointPos, const Rainbow::Vector3f& EndPos,
        const Rainbow::Vector3f& JointTarget, const Rainbow::Vector3f& Effector, Rainbow::Vector3f& OutJointPos,
        Rainbow::Vector3f& OutEndPos, double UpperLimbLength, double LowerLimbLength,
        bool bAllowStretching, double StartStretchRatio, double MaxStretchScale);

    void SolveBasicTwoBoneIK(Rainbow::Matrix4x4f& BoneA, Rainbow::Matrix4x4f& BoneB, Rainbow::Matrix4x4f& Effector,
        const Rainbow::Vector3f& PoleVector, const Rainbow::Vector3f& PrimaryAxis,
        const Rainbow::Vector3f& SecondaryAxis, float SecondaryAxisWeight,
        float BoneALength, float BoneBLength, bool bEnableStretch,
        float StretchStartRatio, float StretchMaxRatio);

    void SampleInternal(float progressPercent);
    void SetCachedSkeletonComp(Rainbow::SkinnedSkeleton* _CachedSkeletonComp)
    {
        m_CachedSkeletonComp = _CachedSkeletonComp;
    }

    // Getters
    float GetWeight() const { return m_Weight; }
    float GetBoneALength() const { return m_BoneALength; }
    float GetBoneBLength() const { return m_BoneBLength; }
    bool IsStretchEnabled() const { return m_bEnableStretch; }
    Rainbow::Vector3f GetPoleVector() const { return m_PoleVectorRelativeToEffector; }
    EControlRigVectorKind GetPoleVectorKind() const { return m_PoleVectorKind; }

    void SetUpdate(bool bUpdate) { m_bIsUpdate = bUpdate; }
    void resetAllData()
    {
        m_BoneA = nullptr;
        m_BoneB = nullptr;
        m_BoneC = nullptr;
        m_Effector = nullptr;
        m_CachedSkeletonComp = nullptr;
    }
private:
    bool UpdateCache() { return m_bIsUpdate; }  // Placeholder implementation

    Rainbow::BoneNode* m_BoneA = nullptr;
    Rainbow::BoneNode* m_BoneB = nullptr;
    Rainbow::BoneNode* m_BoneC = nullptr;
    Rainbow::GameObject* m_Effector = nullptr;
    Rainbow::SkinnedSkeleton* m_CachedSkeletonComp = nullptr;

    float m_Weight = 1.0f;
    float m_BoneALength = 0.0f;
    float m_BoneBLength = 0.0f;
    bool m_Initialize = false;
    bool m_bEnableStretch = false;
    float m_StretchStartRatio = 0.0f;
    float m_StretchMaxRatio = 0.0f;
    float m_SecondaryAxisWeight = 0.0f;

    bool m_bIsUpdate = false;

    Rainbow::Vector3f m_PrimaryAxis = Rainbow::Vector3f::zero;
    Rainbow::Vector3f m_SecondaryAxis = Rainbow::Vector3f::zero;
    Rainbow::Vector3f m_PoleVectorRelativeToEffector = Rainbow::Vector3f::zero;
    EControlRigVectorKind m_PoleVectorKind = EControlRigVectorKind::kLocation;
};