
#include "container_alientotem.h"
#include "special_blockid.h"
#include "BlockAlienTotem.h"
#include "LuaInterfaceProxy.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "world.h"
#include "ClientActorDef.h"

flatbuffers::Offset<FBSave::ChunkContainer> WorldAlienTotemContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerAlienTotem(builder, basedata, m_OxygenTicks, m_BeaconTicks);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerAlienTotem, actor.Union());
}

bool WorldAlienTotemContainer::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerAlienTotem *>(srcdata);
	loadContainerCommon(src->basedata());

	m_OxygenTicks = src->oxygenticks();
	m_BeaconTicks = src->beaconticks();

	return true;
}

int WorldAlienTotemContainer::getObjType() const
{
	return OBJ_TYPE_EFFECT;
}

void WorldAlienTotemContainer::enterWorld(World *pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();

	if(pworld->onClient())
	{
		WCoord pos = BlockBottomCenter(m_BlockPos);
		m_SafeAreaFX = ENG_NEW(EffectParticle)(pworld, "particles/alientotem.ent", pos, 0);
		m_SafeAreaFX->setScale(16.0f);
		if(m_OxygenTicks < 0) m_SafeAreaFX->show(false);
		pworld->getEffectMgr()->addEffect(m_SafeAreaFX);

		if (pworld && pworld->getBlockID(m_BlockPos) == BLOCK_STONE_MONUMENT)
		{
			m_ActivedFX = ENG_NEW(EffectParticle)(pworld, "particles/item_1058_1.ent", pos, 0);
		}
		else
			m_ActivedFX = ENG_NEW(EffectParticle)(pworld, "particles/item_18_activated.ent", pos, 0);

		if(m_OxygenTicks < 0) m_ActivedFX->show(false);
		pworld->getEffectMgr()->addEffect(m_ActivedFX);

		m_BeaconFX = ENG_NEW(EffectParticle)(pworld, "particles/item_18_light.ent", pos, 0);
		if(m_BeaconTicks < 0) m_BeaconFX->show(false);
		pworld->getEffectMgr()->addEffect(m_BeaconFX);
	}
}

void WorldAlienTotemContainer::leaveWorld()
{
	if(m_SafeAreaFX) m_SafeAreaFX->setNeedClear();
	if(m_ActivedFX) m_ActivedFX->setNeedClear();
	if(m_BeaconFX) m_BeaconFX->setNeedClear();

	WorldContainer::leaveWorld();
}

void WorldAlienTotemContainer::startActive(int index)
{
	if(index == 0)
	{
		m_OxygenTicks = 0;
		if(m_SafeAreaFX) m_SafeAreaFX->show(true);
		if(m_ActivedFX) m_ActivedFX->show(true);
	}
	else
	{
		m_BeaconTicks = 0;
		if(m_BeaconFX) m_BeaconFX->show(true);
	}
}

void WorldAlienTotemContainer::endActive(int index)
{
	if(index == 0)
	{
		m_OxygenTicks = -1;
		if(m_SafeAreaFX) m_SafeAreaFX->show(false);
		if(m_ActivedFX) m_ActivedFX->show(false);
	}
	else
	{
		m_BeaconTicks = -1;
		if(m_BeaconFX) m_BeaconFX->show(false);
	}
}

void WorldAlienTotemContainer::updateTick()
{
	// 去除世界限制 code-by:liya 策划：黎海康 2023.2.27
	if (m_World == NULL/* || m_World->getCurMapID()<MAPID_MENGYANSTAR*/) return;
	int blockdata = m_World->getBlockData(m_BlockPos);
	if((blockdata & 4)!=0 && m_OxygenTicks<0) startActive(0);
	else if((blockdata & 4)==0 && m_OxygenTicks>=0) endActive(0);

	if((blockdata & 8)!=0 && m_BeaconTicks<0) startActive(1);
	else if((blockdata & 8)==0 && m_BeaconTicks>=0) endActive(1);

	if (m_World->getBlockID(m_BlockPos) == BLOCK_STONE_MONUMENT) return;

	if(m_OxygenTicks >= 0)
	{
		m_OxygenTicks++;
		int age = GetLuaInterfaceProxy().get_lua_const()->planet_totem_activeage*20;

		if(!m_World->isRemoteMode())
		{
			if(age > 0 && m_OxygenTicks >= age)
			{
				int blockdata = m_World->getBlockData(m_BlockPos);
				m_World->setBlockData(m_BlockPos, blockdata&~4);
				BlockAlienTotem::destroySafeArea(BLOCK_PLANTSPACE_TOTEM, m_World, m_BlockPos, GetLuaInterfaceProxy().get_lua_const()->planet_safearea_radius);
				endActive(0);
			}
			else BlockAlienTotem::stepUpdateSafeArea(m_OxygenTicks, m_World, m_BlockPos, GetLuaInterfaceProxy().get_lua_const()->planet_safearea_radius);
		}

		if(age>0 && m_OxygenTicks>0 && m_OxygenTicks>=age-30*20 && m_SafeAreaFX)
		{
			if((m_OxygenTicks%10) < 6) m_SafeAreaFX->show(true);
			else m_SafeAreaFX->show(false);
		}
	}
}
