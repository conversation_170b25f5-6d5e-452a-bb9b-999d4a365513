#include "VehicleLocomotion.h"
#include "ClientPlayer.h"
#include "BlockMaterialMgr.h"
#include "PlayerLocoMotion.h"
#include "special_blockid.h"
#include "PlayerControl.h"
#include "Mesh/LegacyOgreVertexIndexData.h"
#include "RiddenComponent.h"
#include "VehicleLocomotion.h"
#include "BindActorComponent.h"
#include "BlockScene.h"
#include "ActorCar.h"
#include "PlayerControl.h"
#include "VehicleControlInputs.h"
#include "BlockGeom.h"
using namespace MINIW;
using namespace Rainbow;
IMPLEMENT_COMPONENTCLASS(VehicleLocoMotion)
VehicleLocoMotion::VehicleLocoMotion() : m_PhysActor(NULL), m_PhysJoint(NULL), m_hasPhysActor(false)
{
	m_PosRotationIncrements = 0;
}

void VehicleLocoMotion::attachPhysActor()
{
	if (m_pWorld->isRemoteMode()) return;
	auto phyScene = this->m_pWorld->m_PhysScene;
	if (!phyScene)	return;

	ActorCar* m_pActor = static_cast<ActorCar*>(getOwnerActor()->GetActor());

	if(m_PhysActor == NULL)
	{
		auto m_Position = m_pActor->GetWorldPosition();
		auto m_Quaternion = m_pActor->GetWorldRotation();
		Rainbow::Vector3f offset(0, 0, 0);
		
		bool bIsKinematic = true;
		
		//车轮多边形+车身多边形 vecMeshes[0 1 2 3]
		std::vector<Rainbow::RAWMesh> vecMeshes(VehicleLocoMotion::MAX_WHEEL + 1);
		for (size_t i = 0; i < VehicleLocoMotion::MAX_WHEEL; i++)
		{
			auto& mesh = vecMeshes[i];
			auto obj = m_pActor->getWheelModel(i);
			if (obj) 
			{
				extractMesh(obj, mesh);
			}
		}

		//车身多边形 vecMeshes[4]
		auto& mesh = vecMeshes[VehicleLocoMotion::MAX_WHEEL];
		auto obj = m_pActor->getChassisModel();
		if (obj) 
		{
			extractMesh(obj, mesh);
		}

		Rainbow::GameObject* go = nullptr;
		//if (m_pActor && m_pActor->getTransform())
		//{
		//	go = m_pActor->getTransform()->GetGameObject();
		//}
		this->m_PhysActor = phyScene->AddRigidDynamicSimple4WVehicle(m_Position, m_Quaternion, m_fStaticFriction, m_fDynamicFriction, m_fRestitution, m_fMass, bIsKinematic, m_pActor, vecMeshes, go);
		this->m_pWorld->getScene()->AddGameObject(this->m_PhysActor->GetGameObject());
		//this->SetEnableAchored(this->m_bAchored);

		for (auto mesh : vecMeshes)
		{
			//ENG_FREE_LABEL(mesh.mVerts, kMemPhysics);
		}
	}
}
void VehicleLocoMotion::extractMesh(BlockMaterial* mtl, Rainbow::RAWMesh& mesh)
{
	BlockGeomTemplate* geom = g_BlockMtlMgr.getGeomTemplate(Rainbow::FixedString(mtl->getGeomName().c_str()));
	BlockGeomMeshInfo meshinfo;
	geom->getFaceVerts(meshinfo, 0);
	
	//设置顶点数据
	 
	mesh.mNbVerts = meshinfo.vertices.size();
	mesh.mVerts = new Vector3f[mesh.mNbVerts];
 	for (int i=0; i< mesh.mNbVerts; i++)
 	{
		mesh.mVerts[i].x = meshinfo.vertices[i].pos.x;
		mesh.mVerts[i].y = meshinfo.vertices[i].pos.y;
		mesh.mVerts[i].z = meshinfo.vertices[i].pos.z;
 	}
}



void VehicleLocoMotion::extractMesh(Rainbow::Model* obj, Rainbow::RAWMesh& mesh)
{
	auto&& scale = std::move(obj->GetTransform()->GetWorldScale());
	int vertNum = 0;
	
	auto modelData = obj->GetModelData();
	if (!modelData.IsValid()) return;
	
	const auto& meshNum = modelData->getNumMesh();
	for (size_t meshIndex = 0; meshIndex < meshNum; meshIndex++)
	{
		const auto& subMeshNum = modelData->getIthMesh(meshIndex)->GetSubMeshCount();
		for (size_t subIndex = 0; subIndex < subMeshNum; subIndex++)
		{
			const auto& subData = modelData->getSubMesh(meshIndex, subIndex);
			vertNum += subData->GetVertexData()->getNumVertex();
		}
	}

	//申请顶点空间
	mesh.mNbVerts = vertNum;
	mesh.mVerts = (Rainbow::Vector3f*)ENG_MALLOC_LABEL(mesh.mNbVerts * sizeof(Rainbow::Vector3f), kMemPhysics);
	memset(mesh.mVerts, 0, sizeof(Rainbow::Vector3f) * vertNum);
	mesh.mName = "wheelshape";
	vertNum = 0;

	mesh.mPos = obj->GetTransform()->GetLocalPosition() * scale;
	mesh.mRot = obj->GetTransform()->GetLocalRotation();
	//设置顶点数据
	for (size_t meshIndex = 0; meshIndex < meshNum; meshIndex++)
	{
		const auto& subMeshNum = modelData->getIthMesh(meshIndex)->GetSubMeshCount();
		for (size_t subIndex = 0; subIndex < subMeshNum; subIndex++)
		{
			const auto& subData = modelData->getSubMesh(meshIndex, subIndex);
			const size_t& vtNum = subData->GetVertexData()->getNumVertex();
			for (size_t vtIndex = 0; vtIndex < vtNum; vtIndex++)
			{
				mesh.mVerts[vertNum++] = (*(Rainbow::Vector3f*)subData->GetVertexData()->getPosition(vtIndex) * scale);
			}
		}
	}

}

void VehicleLocoMotion::OnCollisionEnter(const Rainbow::EventContent* collision)
{
	ClientActor* ownerActor = getOwnerActor();
	if (ownerActor)
	{
		//ActorBall* ball = static_cast<ActorBall*>(ownerActor);
		//ball->playSoundByPhysCollision();
	}
}

void VehicleLocoMotion::detachPhysActor()
{
	if (m_pWorld->isRemoteMode()) return;

	if(m_PhysActor)
	{
		auto vd4WComp = m_PhysActor->GetGameObject()->GetComponent<Rainbow::VehicleDrive4W>();
		if (vd4WComp)
		{
			Rainbow::PPtr<Rainbow::Component> vd4Component(vd4WComp);
			m_PhysActor->GetGameObject()->RemoveComponent(vd4Component);
		}

		m_pWorld->m_PhysScene->DeleteRigidActor(m_PhysActor);
		m_PhysActor = NULL;

		m_hasPhysActor = false;
	}
}

void VehicleLocoMotion::attachPhysJoint(ClientPlayer *player)
{
	if (m_pWorld->isRemoteMode()) return;

	PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *>(player->getLocoMotion());

	Rainbow::Quaternionf rot;
	//rot.setEulerAngle(0.0f, 0.0f, 0.0f);
	rot = AngleEulerToQuaternionf(Vector3f(0.0f, 0.0f, 0.0f));
	int offsetY = (loc->m_BoundHeight - m_BoundHeight) / 2 - 5;
	m_PhysJoint = m_pWorld->m_PhysScene->CreateFixJoint(
		loc->m_PhysActor, 
		Vector3f(0, 0, 0), 
		rot, 
		m_PhysActor, 
		Vector3f(0, (float)offsetY, 130), 
		Quaternionf::identity);
}

void VehicleLocoMotion::detachPhysJoint()
{
	if (m_pWorld->isRemoteMode()) return;

	if (m_PhysJoint)
	{
		m_pWorld->m_PhysScene->DeleteJoint(m_PhysJoint);
		m_PhysJoint = NULL;
	}
}

void VehicleLocoMotion::prepareTick()
{
	ActorLocoMotion::prepareTick();

	m_PrevRotateQuat = m_RotateQuat;
}

void VehicleLocoMotion::tick()
{
	ActorLocoMotion::tick();

	if (m_pWorld->isRemoteMode())
	{
		if(m_PosRotationIncrements > 0)
		{
			m_Position = m_Position + (m_ServerPos - m_Position)/m_PosRotationIncrements;
			//m_RotateQuat.slerp(m_RotateQuat, m_ServerRot, 1.0f/m_PosRotationIncrements);
			m_RotateQuat = Slerp(m_RotateQuat, m_ServerRot, 1.0f / m_PosRotationIncrements);

			Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, -m_RotateQuat.GetAxisZ());

			m_PosRotationIncrements--;
		}
		return;
	}

	if(m_PhysActor)
	{
		auto vd4WComp = m_PhysActor->GetGameObject()->GetComponent<Rainbow::VehicleDrive4W>();
		if (!vd4WComp)
		{
			return;
		}

		if (getOwnerActor())
		{
			auto OwnerRidComp = getOwnerActor()->getRiddenComponent();
			//客机开车，发送协议到主机
			if (OwnerRidComp && OwnerRidComp->getRiddenByActor())
			{
				//车是主机开的
				if (g_pPlayerCtrl && OwnerRidComp->getRiddenByActor() == g_pPlayerCtrl)
				{
 
						m_ctl.setCarKeyboardInputs(
							g_pPlayerCtrl->m_VehicleControlInputs->getAccelKeyPressed(),
							g_pPlayerCtrl->m_VehicleControlInputs->getBrakeKeyPressed(),
							g_pPlayerCtrl->m_VehicleControlInputs->getHandbrakeKeyPressed(),
							g_pPlayerCtrl->m_VehicleControlInputs->getSteerLeftKeyPressed(),
							g_pPlayerCtrl->m_VehicleControlInputs->getSteerRightKeyPressed(),
							g_pPlayerCtrl->m_VehicleControlInputs->getGearUpKeyPressed(),
							g_pPlayerCtrl->m_VehicleControlInputs->getGearDownKeyPressed());
							
						m_ctl.setCarGamepadInputs(
							g_pPlayerCtrl->m_VehicleControlInputs->getAccel(),
							g_pPlayerCtrl->m_VehicleControlInputs->getBrake(),
							g_pPlayerCtrl->m_VehicleControlInputs->getSteer(),
							g_pPlayerCtrl->m_VehicleControlInputs->getGearUp(),
							g_pPlayerCtrl->m_VehicleControlInputs->getGearDown(),
							g_pPlayerCtrl->m_VehicleControlInputs->getHandbrake());

						/*LOG_INFO("--joker accel: %d   brake: %d", g_pPlayerCtrl->m_VehicleControlInputs->getAccelKeyPressed(),
							g_pPlayerCtrl->m_VehicleControlInputs->getBrakeKeyPressed());*/

						m_ctl.update(0.1f, *vd4WComp);					 
				}
			}
		}

 		
	}
	else
	{
		m_Motion *= 0.98f;
		if(Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
		//if(Abs(m_Motion.y) < 0.5f) m_Motion.y = 0;
		if(Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;

		doMoveStep(m_Motion);
		auto bindAComponent = getOwnerActor()->getBindActorCom();

		if(bindAComponent && !bindAComponent->getTarget())
			m_Motion.y -= m_pWorld->getGravity(GRAVITY_ITEM);
	}
}

void VehicleLocoMotion::update(float dtime)
{
	ActorLocoMotion::update(dtime);

	m_UpdateRot = Slerp(m_PrevRotateQuat, m_RotateQuat, m_TickPosition.m_TickOffsetTime/GAME_TICK_TIME);
	m_UpdatePos = getFramePosition();
}

void VehicleLocoMotion::getRotation(Rainbow::Quaternionf &quat)
{
	quat = m_RotateQuat;
}

void VehicleLocoMotion::doPickThrough(ClientActor *excludesactor/* =nullptr */)
{
	if (m_pWorld->isRemoteMode()) return;

	WCoord mvec = getIntegerMotion(m_Motion);
	if (m_OnGround && mvec.y < 0)
		mvec.y = 0;
	if (mvec.length() < 100)
	{
		return;
	}

	MINIW::WorldRay ray;
	ray.m_Origin = m_Position.toWorldPos();
	ray.m_Dir = mvec.toVector3();
	ray.m_Range = ray.m_Dir.Length();
	ray.m_Dir /= ray.m_Range;

	ActorExcludes excludes;
	excludes.addActor(getOwnerActor());
	if (excludesactor)
		excludes.addActor(excludesactor);

	IntersectResult presult;
	WorldPickResult intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
	if (intertype == WorldPickResult::BLOCK) //block
	{
		int blockID = m_pWorld->getBlockID(presult.block);

		WCoord pos = CoordDivBlock(m_Position + mvec);

		if (presult.block != pos)
		{
			if (blockID == BLOCK_COLLIDER || blockID == BLOCK_MOBCOLLIDER || blockID == BLOCK_BALLCOLLIDER)
			{
				g_BlockMtlMgr.getMaterial(blockID)->DoOnActorCollidedWithBlock( getOwnerActor()->getWorld(), presult.block, getOwnerActor());
				// 观察者事件接口
				//ObserverEvent_Block obevent(presult.block.x, presult.block.y, presult.block.z, blockID, (long long)getOwnerActor()->getObjId());
				//ObserverEventManager::getSingleton().OnTriggerEvent("Block.ActorCollide", &obevent);
			}
		}
	}
	else if (intertype == WorldPickResult::ACTOR) //actor
	{
		WCoord pos = (m_Position + mvec) / BLOCK_SIZE;
		if (presult.actor->getPosition() != pos)
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(presult.actor);
			if (player)
			{
				getOwnerActor()->onCollideWithPlayer(player);
			}
			else
			{
				getOwnerActor()->collideWithActor(presult.actor->GetActor());
			}
		}
	}
}

void VehicleLocoMotion::checkPhysWorld()
{
	WCoord curpos = m_Position;
	WCoord range(SECTION_SIZE, SECTION_SIZE, SECTION_SIZE);
	WCoord minpos = CoordDivSection(curpos-range);
	WCoord maxpos = CoordDivSection(curpos+range);

	std::vector<WCoord> checkphy;
	for(int y=minpos.y; y<=maxpos.y; y++)
	{
		for(int z=minpos.z; z<=maxpos.z; z++)
		{
			for(int x=minpos.x; x<=maxpos.x; x++)
			{
				//m_pWorld->updateSectionPhysics(x, y, z);
				checkphy.push_back(WCoord(x, y, z));
			}
		}
	}

	for (int i = 0; i<(int)m_preCheckPhy.size(); i++)
	{
		 int j = 0;
		 for (; j<(int)checkphy.size(); j++)
		 {
			if (m_preCheckPhy[i] == checkphy[j])
			{
				break;
			}
		 }
		 if (j == checkphy.size())
		 {
			m_pWorld->updateLeaveSectionPhysics(m_preCheckPhy[i].x, m_preCheckPhy[i].y, m_preCheckPhy[i].z);
		 }
	}

	for (int i = 0; i<(int)checkphy.size(); i++)
	{
		 int j = 0;
		 for (; j<(int)m_preCheckPhy.size(); j++)
		 {
			if (checkphy[i] == m_preCheckPhy[j])
			{
				break;
			}
		 }
		 if (j == m_preCheckPhy.size())
		 {
			m_pWorld->updateEnterSectionPhysics(checkphy[i].x, checkphy[i].y, checkphy[i].z);
		 }
		 m_pWorld->updateSectionPhysics(checkphy[i].x, checkphy[i].y, checkphy[i].z);
	}
	m_preCheckPhy = checkphy;
}

void VehicleLocoMotion::updateBindActor()
{
	auto bindAComponent = getOwnerActor()->m_pBindActorComponent;
	if (bindAComponent)
	{
		ClientActor *binding = bindAComponent->getTarget();
		if (binding)
		{
			//if (m_pWorld->isRemoteMode()) return;
			Rainbow::Vector3f dir = Yaw2FowardDir(binding->getLocoMotion()->m_RotateYaw);

			/*Vector3f bindingPos = binding->getPosition().toVector3();
			Vector3f ballPos = getOwnerActor()->getPosition().toVector3() + getOwnerActor()->m_OffsetPos;
			Vector3f tmp = ballPos - bindingPos;
			float distance = tmp.length();

			if (distance > 200.0f)
			{
				distance = 200;
				getOwnerActor()->setBindInfo(-binding->getObjId(), WCoord(0, 0, 0));
			}
			else
			{
				Vector3f curPos = bindingPos + dir*distance;
				WCoord int_pos = WCoord(curPos);
				getOwnerActor()->m_OffsetPos = curPos - int_pos.toVector3();
				int_pos.y += (m_BoundHeight / 2);
				setPosition(int_pos.x, int_pos.y, int_pos.z);
			}*/
				
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(binding);
			if (player)
			{
				WCoord pos1 = player->getPosition();
				float distance = player->getLocoMotion()->m_BoundSize + BLOCK_SIZE*0.5f;
				WCoord pos = pos1 + WCoord(dir*(distance));
				pos.y += (m_BoundHeight / 2);
				//setPosition(pos.x, pos.y, pos.z);
				int blockid = m_pWorld->getBlockID(CoordDivBlock(pos));
				if (blockid > 0)
				{
					BlockMaterial* blockMat = m_pWorld->getBlockMaterial(CoordDivBlock(pos));
					if (blockMat && blockMat->canBlocksMovement(m_pWorld, CoordDivBlock(pos)))
					{
						pos = pos1;// +WCoord(dir*(BLOCK_FSIZE*1.0f));
						pos.y += (m_BoundHeight / 2);
					}
				}
				setPosition(pos.x, pos.y, pos.z);
			}
		}
	}
}

void VehicleLocoMotion::setPosition(int x, int y, int z)
{
	m_OldPosition = m_Position;
	m_Position.x = x;
	m_Position.y = y;
	m_Position.z = z;
	if (m_PhysActor)
		m_PhysActor->SetPos(Rainbow::Vector3f((float)x, (float)y, (float)z), m_RotateQuat);
}
