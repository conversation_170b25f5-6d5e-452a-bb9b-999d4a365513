
#ifndef __DYNAMICCONTAINER_H__
#define __DYNAMICCONTAINER_H__

#include "ChunkSave_generated.h"
#include "container_world.h"

namespace google {
    namespace protobuf {
        template <typename Element>
        class RepeatedPtrField;
    }
}
 
namespace game {
    namespace common {
        class PB_ItemData;
    }
}

// 动态容器，适合指定不同容量大小场景使用
class DynamicContainer : public WorldContainer
{
private:
	void Initialization();
public:
	DynamicContainer();
	DynamicContainer(const WCoord &blockpos, int blockid, int grid_num);
	virtual ~DynamicContainer();

	virtual int getObjType() const override
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerDynamicBox;
	}

	// 实际使用ContainerStorage进行存储，ContainerStorage本身不限制数量
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER &builder);
	flatbuffers::Offset<FBSave::ContainerDynamicBox> SaveTo(SAVE_BUFFER_BUILDER &builder);
	virtual bool load(const void *srcdata);

	virtual BackPackGrid *index2Grid(int index) override;
	virtual void afterChangeGrid(int index);
	virtual bool canPutItem(int index);
	virtual void onAttachUI();
	virtual void onDetachUI();

	virtual void dropItems();

	void SetGrid(int slotindex, const BackPackGrid &grid);

	virtual int getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos) override;

	virtual float getAttrib(int i);

	virtual int onInsertItem(const BackPackGrid &grid, int num, int params) override;
	bool canInsertItem(const BackPackGrid& grid, int param);
	virtual BackPackGrid *onExtractItem(int params) override;
	virtual int calComparatorInputOverride() override;

	void clear();
	int addItemByCount(int itemid, int num);
	void removeItemByCount(int itemid, int num);

	virtual void enterWorld(World *pworld)
	{
		WorldContainer::enterWorld(pworld);
	}

	virtual void leaveWorld() override;

	virtual int getGridNum()
	{
		return m_grid_num;
	}
	virtual bool doOpenContainer() override;
	bool actor_storage = false;
private:
	int m_blockid = 0;
	int m_grid_num = 0;
	std::vector<BackPackGrid> m_Grids;
};
#endif