#include "ClientActorNewArrow.h"
#include "ProjectileLocoMotion.h"
#include "GameMode.h"
#include "PlayerControl.h"
#include "FireBurnComponent.h"
#include "EffectComponent.h"
#include "AttackedComponent.h"
#include "ClientActorHelper.h"
#include "SandboxIdDef.h"
#include "WorldManager.h"
#include "LivingAttrib.h"
#include "MobAttrib.h"
#include "OgreEntity.h"
#include "OgreModel.h"
#include "ClientMob.h"
#include "LuaInterfaceProxy.h"

extern void ActorKnockbackByMotion(ClientActor *actor, const Rainbow::Vector3f &motion, float strength);

ClientActorNewArrow::ClientActorNewArrow()
{
	m_ItemID = ITEM_ARROW;
	m_EntityModel = NULL;
}

ClientActorNewArrow::~ClientActorNewArrow()
{

}

void ClientActorNewArrow::onImpactWithActor(ClientActor *actor, const std::string& partname)
{
	if (m_pWorld == nullptr) return ;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return ;
	ActorLiving* pLive = dynamic_cast<ActorLiving*>(actor);
	ClientActor *attacker = actorMgr->findActorByWID(m_ShootingActorID);

	//��������Ͷ���ﲻ����
	const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(actor->GetItemId());
	bool hasPhysx = false;
	if (physicsActorDef)
	{
		if (physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_NOEDIT_YESPHY || physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_YESEDIT_YESPHY)
			hasPhysx = true;
		else
			hasPhysx = false;
	}

	useItemSkill(actor);
	if (attacker != NULL)
	{
		ActorLiving* pAttackLive = dynamic_cast<ActorLiving*>(attacker);
		if ((pLive && pLive->getTeam() != 0 && pAttackLive&& pAttackLive->getTeam() != 0 && pLive->getTeam() == pAttackLive->getTeam())||hasPhysx)
		{
			return;
		}
		
	}

	if ((m_ItemID == ITEM_ARROW) && m_ShootingActorID != actor->getObjId()) {
		int bleedlv = 0;
		bool showbleed = false;
		ActorLiving* live = dynamic_cast<ActorLiving*>(actor);
		int runenum = m_Runedata.getRuneNum();
		for (int i = 0; i < runenum; i++) {
			GridRuneItemData runedata = m_Runedata.getItemByIndex(i);
			if (runedata.getRuneVal0() == 1020) {
				showbleed = true;
				bleedlv = round(runedata.getRuneVal1());
			}
		}
		if (showbleed && live->getLivingAttrib()) {
			live->getLivingAttrib()->addBuff(1020, bleedlv, -1, 0, actor->getObjId());
		}
	}
	
	//�м�����-��¼�м�����
	if (pLive != NULL && pLive->getLivingAttrib() != NULL)
	{
		pLive->getLivingAttrib()->addArrowById(m_ItemID);
	}

	//ClientActorProjectile::onImpactWithActor(actor);
	doAttackActor(actor, getLocoMotion()->m_Motion);
}

void ClientActorNewArrow::onImpactWithBlock(const WCoord *blockpos, int face)
{
	ClientActorProjectile::onImpactWithBlock(blockpos, face);
}

void ClientActorNewArrow::doAttackActor(ClientActor *target, Rainbow::Vector3f &motion)
{

	//float len = motion.length()/BLOCK_FSIZE;
	//float atkpoints = ceil(len * m_AttackPoints);
	float atkpoints = m_AttackPoints;
	float atkpointsAdd = 0.f;
	if (m_pWorld == nullptr) return;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return;
	WCoord dp = getLocoMotion()->getPosition() - m_StartPos;
	if (dp.length() >= 20 * BLOCK_FSIZE) //����һ��
	{
		//atkpoints += GenRandomInt(int(atkpoints/2)+1);
		atkpointsAdd += 1.0f;
	}

	if (isInFire())
	{
		auto FireBurnComp = target->sureFireBurnComponent();
		if (FireBurnComp)
		{
			FireBurnComp->setFire(33, getFireLv());
		}
		
	}

	ClientActor *attacker = actorMgr->findActorByWID(m_ShootingActorID);
	if (attacker == NULL) attacker = this;

	OneAttackData atkdata;
	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;
	bool isOpenNewHpdecCalculate = GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate;
	atkdata.buff_atk = m_BuffAttackAdd;
	atkdata.knockback = m_KnockbackStrength;
	atkdata.fromplayer = dynamic_cast<ClientPlayer *>(attacker);
	atkdata.bowDamageIns = m_bowDamageIns;

	if (m_BuffId != 0)
	{
		atkdata.buffId = m_BuffId / 1000;
		atkdata.buffLevel = m_BuffId % 1000;
	}

	if (GetWorldManagerPtr()->m_RuleMgr)
	{
		GameRuleMod *rulemod = GetWorldManagerPtr()->m_RuleMgr->findGameRuleMod(RULEMOD_THROWABLE_HURT);
		if (rulemod && rulemod->modvars[0] == m_ItemID)
		{
			atkpoints = rulemod->modvars[1] + m_BaseAtk;
		}
		else
		{
			atkpoints = m_AttackPoints;
			atkdata.knockback = m_KnockbackStrength;
		}
	}
	else
	{
		atkpoints = m_AttackPoints;
		atkdata.knockback = m_KnockbackStrength;
	}
	CollideAABB box;
	target->getHitCollideBox(box);


	ClientPlayer *playerattacker = dynamic_cast<ClientPlayer *>(attacker);
	//ͷ������
	if (playerattacker && target->getObjType() != OBJ_TYPE_VEHICLE)
	{
		if(getPosition().y - target->getPosition().y > 0.65f * box.dim.y)
		{
			if (isOpenNewHpdecCalculate)
			{
				atkdata.critical = true;
			}
			else
			{
				atkpoints += 0.5f * (m_AttackPoints + atkpointsAdd);
			}
			if (playerattacker->canShowShotTip())
			{
				g_pPlayerCtrl->triggerHeadshotTip();
			}
			else
			{
				auto effectComponent = playerattacker->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect(HUDFX_HEADSHOT);
				}
			}
		}
		else
		{
			if (playerattacker->canShowShotTip())
				g_pPlayerCtrl->triggerNormalshotTip();
			else
			{
				auto effectComponent = playerattacker->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect(HUDFX_NORMALSHOT);
				}
			}
		}
	}

	bool canAttack = true;
	if (playerattacker && !playerattacker->canHurtActor(target))
	{
		canAttack = false;
		atkpoints = 0.f;
	}

	// ���˺�����ϵͳ code-by:liya
	if (isOpenNewHpdecCalculate)
	{
		atkdata.atkTypeNew = m_atkType;
		memcpy(atkdata.atkPointsNew, m_AttackPointsNew, sizeof(m_AttackPointsNew));
		memcpy(atkdata.explodePoints, m_ExplodePoints, sizeof(m_ExplodePoints));
		atkdata.charge = m_strength;
		//atkdata.damping = 1.0f;
	}
	else
	{
		atkdata.atktype = ATTACK_RANGE;
		atkdata.atkpoints = atkpoints;
	}

	bool damaged = false;
	if (atkpoints != 0 || canAttack)
	{
		auto component = target->getAttackedComponent();
		if (component)
		{
			damaged = component->attackedFrom(atkdata, this);
		}
	}

	if(!m_bAttackFly)
	ActorKnockbackByMotion(target, motion, atkdata.knockback);

	applyRuneData(target, damaged);

	ClientMob *mob = dynamic_cast<ClientMob *>(attacker);
	if (mob)
	{
		//Ԫ���˺�
		for (int i = 0; i<MAX_MAGIC_ATTACK - MAX_PHYSICS_ATTACK; i++)
		{
			memset(&atkdata, 0, sizeof(atkdata));
			atkdata.damage_armor = false;
			atkdata.atktype = (ATTACK_TYPE)(MAX_PHYSICS_ATTACK + i);
			atkdata.atkpoints = mob->getMobAttrib()->getAttackPoint(atkdata.atktype, 1);
			atkdata.buff_atk = mob->getMobAttrib()->getModAttrib(MODATTR_ATTACK_PUNCH + atkdata.atktype);
			atkdata.ignore_resist = true;

			if (atkdata.atkpoints > 0)
			{
				auto component = target->getAttackedComponent();
				if (component)
				{
					component->attackedFrom(atkdata, mob);
				}
			}
		}
	}

	if (target && m_pWorld && !m_pWorld->isRemoteMode()) {
		Rainbow::Vector3f objpos = getPosition().toVector3() * 0.01f;
		ObserverEvent_ActorItemPos obevent(getObjId(), target->getObjId(), GetItemId(), objpos.x, objpos.y, objpos.z);
		if (target->IsTriggerCreature())
		{
			obevent.SetData_TargetActorID(target->getDefID());
		}
		obevent.SetData_HelperObjid(m_ShootingActorID);
		if (m_ShootingActorID > 0)
		{
			ClientMob* attacker = actorMgr->findMobByWID(m_ShootingActorID);
			if (attacker)
			{
				obevent.SetData_Actor(attacker->getDefID());
			}
		}
		GetObserverEventManager().OnTriggerEvent("Actor.Projectile.Hit", &obevent);
	}
	setNeedClear();
	//m_ArrowModel->GetMainModel()->show(false);
}

ClientActorNewArrow* ClientActorNewArrow::shootNewArrowAuto(World* pworld, const WCoord& pos, const Rainbow::Vector3f& dir, float speed, float deviation, long long shooterObjId)
{
	if (pworld == nullptr) return NULL;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return NULL;

	ClientActorNewArrow* arrow = SANDBOX_NEW(ClientActorNewArrow);
	arrow->init(12051);

	actorMgr->spawnActor(arrow, pos, 0, 0);
	arrow->m_StartPos = arrow->getPosition();

	ProjectileLocoMotion* locomove = static_cast<ProjectileLocoMotion*>(arrow->getLocoMotion());
	locomove->setThrowableHeading(dir, speed, deviation);

	arrow->playMotion(arrow->m_ProjectileDef->TailEffect.c_str(), true, 0, 2);
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
	{
		// ��ս��Զ�̡�Ԫ���˺�
		int damageType = arrow->m_ProjectileDef->DamageType;
		if (arrow->m_ProjectileDef->AttackType == 0)
		{
			if (damageType == 0 || damageType == 2 || damageType == 3)
			{
				assert(false && "Projectiledef damageType error!");
			}
			arrow->m_AttackPointsNew[damageType] = arrow->m_ProjectileDef->AttackValue;
			arrow->m_atkType = (1 << damageType);
		}
		// ������ը�˺�
		else
		{
			arrow->m_atkType = (1 << ATTACK_EXPLODE);
			if (damageType < 4)
			{
				arrow->m_ExplodePoints[0] = arrow->m_ProjectileDef->AttackValue;
			}
			else
			{
				arrow->m_ExplodePoints[damageType - 3] = arrow->m_ProjectileDef->AttackValue;
			}
		}

		arrow->m_strength = 1.0f;
	}
	else
	{
		arrow->m_AttackPoints = arrow->m_ProjectileDef->AttackValue;
	}
	if (arrow->m_ProjectileDef->TriggerCondition == 4)
	{
		arrow->m_ImpactTimeMark = 0;
	}
	arrow->setMasterObjId(shooterObjId);
	//�;ö�
	auto tooDef = GetDefManagerProxy()->getToolDef(12051);
	if (tooDef)
		arrow->m_Durable = tooDef->Duration;
	else
		arrow->m_Durable = 0;
	return arrow;
}

flatbuffers::Offset<FBSave::SectionActor> ClientActorNewArrow::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);

	WORLD_ID shooter = m_ShootingActorID;
	ProjectileLocoMotion *locmove = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	auto blockpos = WCoordToCoord3(locmove->m_BlockPos);
	auto startpos = WCoordToCoord3(m_StartPos);

	auto arrow = FBSave::CreateActorNewArrow(builder, basedata, shooter, m_ItemID, &blockpos, Block::toBlockOriginData(locmove->m_InBlockID),
		locmove->m_InBlockData, m_KnockbackStrength, m_AttackPoints, m_BuffAttackAdd, &startpos, 0,
		locmove->m_InGround, locmove->m_TicksInAir, locmove->m_TicksInGround, m_AttachedEffect, Block::toBlockDataEx(locmove->m_InBlockID));

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorNewArrow, arrow.Union());
}

bool ClientActorNewArrow::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorNewArrow *>(srcdata);
	loadActorCommon(src->basedata());

	init(ITEM_ARROW);

	m_ShootingActorID = src->shooter();


	ProjectileLocoMotion *locmove = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	locmove->m_BlockPos = WCoord(src->blockpos()->x(), src->blockpos()->y(), src->blockpos()->z());

	locmove->m_InBlockID = Block::toMakeBlockIDWithEx(src->inblock_id(), src->inblock_idex());
	locmove->m_InBlockData = src->inblock_data();
	m_KnockbackStrength = src->knockback();
	m_AttackPoints = src->atkpoint();
	m_BuffAttackAdd = src->buffatk();

	m_StartPos = Coord3ToWCoord(src->startpos());
	locmove->m_InGround = src->inground() != 0;
	locmove->m_TicksInAir = src->airticks();
	locmove->m_TicksInGround = src->groundticks();

	m_AttachedEffect = src->infire();

	if (isInFire() && m_EntityModel)
	{
		m_EntityModel->PlayMotion("1028");
	}
	
	if (!locmove->m_InGround && locmove->m_Motion.LengthSqr() > 0.1f && m_EntityModel) m_EntityModel->PlayMotion(m_ProjectileDef->TailEffect.c_str());
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (loco)
	loco->syncPos = getLocoMotion()->getPosition();
	return true;
}