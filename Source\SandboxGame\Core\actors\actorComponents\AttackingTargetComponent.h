#ifndef __ATTACKING_TARGET_COMPONENT_H__
#define __ATTACKING_TARGET_COMPONENT_H__

#include "ActorTypes.h"
#include "BaseTargetComponent.h"

class ActorLiving;
class ClientActor;
class ClientMob;
class ClientPlayer;
class AttackingTargetComponent : public BaseTargetComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(AttackingTargetComponent)

	//tolua_begin
	AttackingTargetComponent(/*ActorLiving* owner*/);
	virtual void attackTick();

	virtual	void setTarget(ClientActor *target, int targetIndex = 1) override;
	//void setAtkingTarget(ClientActor *target);
	//ActorLiving* getAtkingTarget();
	int getAtkingTimer()
	{
		return m_AttackingTimer;
	}
	void setAttackAnim(ATTACK_TYPE attacktype);
	void setAttackAnim(ATTACK_TYPE attacktype, int animTicks);

	bool isAttacking();
	virtual int getAttackAnimTicks(ATTACK_TYPE attacktype){ return 0;}
	bool needDoActualRangeAttack();//m_AttackAnimType == ATTACK_RANGE && getAttackingTargetComponent()->isAttacking()

	void doActual();
	void doAttackAnim(ATTACK_TYPE attacktype);
	//tolua_end
protected:
	

	ActorLiving* m_owner;
	//WORLD_ID     m_AttackingTarget;	
	int          m_AttackingTimer;	

	int          m_AttackAnimTicks;	
	ATTACK_TYPE  m_AttackAnimType;
}; //tolua_exports


class AttackingTargetComponentEx : public AttackingTargetComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(AttackingTargetComponentEx)

	//tolua_begin
	AttackingTargetComponentEx(/*ActorLiving* owner*/);
	virtual void attackTick() final;
	//tolua_end
	bool attackedFrom(OneAttackData& atkdata, MNSandbox::SandboxNode* inputattacker);

private:
	MNSandbox::SandboxNode* getActor();

	MNSandbox::SandboxNode* m_cachedActor = nullptr;
	uint64_t m_lastTargetId = 0;
}; //tolua_exports


class MobAttackingTargetComponent : public AttackingTargetComponentEx //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(MobAttackingTargetComponent)

	//tolua_begin
	MobAttackingTargetComponent(/*ClientMob* owner*/);
	//tolua_end
protected:
	virtual int getAttackAnimTicks(ATTACK_TYPE attacktype)override;
}; //tolua_exports

class PlayerAttackingTargetComponent : public AttackingTargetComponentEx //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(PlayerAttackingTargetComponent)
public:
	//tolua_begin
	PlayerAttackingTargetComponent(/*ClientPlayer* owner*/);
	//tolua_end
protected:
	virtual int getAttackAnimTicks(ATTACK_TYPE attacktype)override;
}; //tolua_exports

#endif