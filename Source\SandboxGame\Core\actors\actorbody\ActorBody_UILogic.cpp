#include "ActorBody.h"
#include "OgreEntity.h"
#include "BlockScene.h"
#include "ClientItem.h"
#include "SandBoxManager.h"
#include "ModelItemMesh.h"
#include "GameMode.h"
#include "OgreUtils.h"
#include "special_blockid.h"
#include "PlayerControl.h"
#include "MpActorManager.h"
#include "GameCamera.h"
#include "GlobalFunctions.h"
#include "ClientInfoProxy.h"
#include "Pkgs/PkgUtils.h"
#include "Entity/LegacySequenceMap.h"
#include "PlayerAttrib.h"
#include "ClientActorFuncWrapper.h"
#include "backpack.h"
#include "CustomModel.h"
#include "ActorLocoMotion.h"
#include "Entity/ModelRenderer.h"
#include "Entity/ModelAnimationPlayer.h"
#include "SandboxGameDef.h"
#include "PlayerLocoMotion.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

void  ActorBody::setAblePlayOtherAnim(bool type)
{
	m_ablePlayerOtherAnim = type;
}

void ActorBody::checkSpectatorBody()
{
	if (!g_pPlayerCtrl)
		return;
	if (m_OwnerPlayer == g_pPlayerCtrl
		|| (m_OwnerPlayer && (g_pPlayerCtrl->getToSpectatorUin() == m_OwnerPlayer->getUin())))
	{
		g_pPlayerCtrl->CheckSpectatorPlayerShow();
	}
}

ActorBodyUIComponent* ActorBody::GetUIComponent()
{
	if (!m_UIComponent)
	{
		m_UIComponent = SANDBOX_NEW(ActorBodyUIComponent, this);
	}

	return m_UIComponent;
}

void ActorBody::updateForOverHead(unsigned int dtick, Rainbow::Vector3f& pos)
{
	if (m_UIComponent)
	{
		m_UIComponent->updateForOverHead(dtick, pos);
	}
}

void ActorBody::setDispayName(const char* name, int teamid, int texid/* =0 */, const char* title/* = "" */)
{
	GetUIComponent()->setDispayName(name, teamid, texid, title);
}

void ActorBody::setDispayHomeBillBoard()
{
	GetUIComponent()->setDispayHomeBillBoard();
}

void ActorBody::setNeedItemIcon(Rainbow::SharePtr<Rainbow::Texture2D> huires, int tick /*= -1*/, int huiresWidth /*= 62*/, int huiresHeight /*= 62*/)
{
	GetUIComponent()->setNeedItemIcon(huires, tick , huiresWidth, huiresHeight);
}

void ActorBody::setBillBoardText(const char* text)
{
	GetUIComponent()->setBillBoardText(text);
}

void ActorBody::setBillBoardTextNpc(bool bBillBoardTextNpc)
{
	GetUIComponent()->setBillBoardTextNpc(bBillBoardTextNpc);
}

//音乐厅聊天气泡 2021-09-25 codeby: luoshuai
void ActorBody::setDispayMusicClubChatBubble()
{
	GetUIComponent()->setDispayMusicClubChatBubble();
}

//音乐厅聊天气泡 2021-09-25 codeby: luoshuai
//2021-12-20 codeby: wangyang ???????????
void ActorBody::setMusicClubChatBubbleText(const char* text, bool isShow, int bubble, float tickTime)
{
	GetUIComponent()->setMusicClubChatBubbleText(text, isShow, bubble, tickTime);
}

void ActorBody::setHPVisible(bool pVisible)
{
	GetUIComponent()->setHPVisible(pVisible);
}
bool ActorBody::getHPVisible()
{
	return GetUIComponent()->getHPVisible();
}

void ActorBody::setHPVale(int pNow, int pTotale)
{
	GetUIComponent()->setHPVale(pNow, pTotale);
}

void ActorBody::setHpExtraValue(int val)
{
	GetUIComponent()->setHpExtraValue(val);
}

void ActorBody::setHpTextDisplay(int type)
{
	GetUIComponent()->setHpTextDisplay(type);
}

void ActorBody::setHPColor(int r, int g, int b, int alpha/* =255 */)
{
	GetUIComponent()->setHPColor( r,  g,  b,  alpha/* =255 */);
}
void ActorBody::setHPTextrueName(const char* pBg, const char* pProgress)
{
	GetUIComponent()->setHPTextrueName(pBg,pProgress);
}

Rainbow::MoveByTextMgr* ActorBody::getHPChangeTextMgr()
{
	return GetUIComponent()->getHPChangeTextMgr();	
}

void ActorBody::createHPChangeText(const char* pStr, int pFontSize, const Rainbow::ColorQuad& pColor, bool isMainPlayer /*false*/)
{
	GetUIComponent()->createHPChangeText(pStr, pFontSize, pColor, isMainPlayer);
}

void ActorBody::setArmorVisible(bool pVisible)
{
	GetUIComponent()->setArmorVisible(pVisible);
}

bool ActorBody::getArmorVisible()
{
	if (m_UIComponent)
		return GetUIComponent()->getArmorVisible();

	return false;
}

void ActorBody::setArmorVale(int now, int max, int extra)
{
	GetUIComponent()->setArmorVale(now,max,extra);
}

void ActorBody::setArmorColor(int r, int g, int b, int alpha)
{
	GetUIComponent()->setArmorColor(r, g, b, alpha);	
}

void ActorBody::setArmorTextrueName(const char* pBg, const char* pProgress)
{
	GetUIComponent()->setArmorTextrueName(pBg,pProgress);
}

void ActorBody::setShowDialog(bool state)
{
	GetUIComponent()->setShowDialog(state);
}

void ActorBody::setVisibleDispayName(bool b)
{
	if (m_UIComponent)
		GetUIComponent()->setVisibleDispayName(b);
}

void ActorBody::addNameTexId(int nameTexid)
{
	GetUIComponent()->addNameTexId(nameTexid);
}

void ActorBody::setAchieveIconName(const char* texIcon, const char* texFrame)
{
	GetUIComponent()->setAchieveIconName(texIcon, texFrame);
}

void ActorBody::setAchieveVisible(bool achieveVisible)
{
	if (m_UIComponent)
		GetUIComponent()->setAchieveVisible(achieveVisible);	
}

void ActorBody::setBPTitleIconName(std::string str)
{
	GetUIComponent()->setBPTitleIconName(str);
}

void ActorBody::setBPTitleIconVisible(bool visible)
{
	GetUIComponent()->setBPTitleIconVisible(visible);	
}

void ActorBody::setVipIconName(const char* iconPath, const char* texIcon, float scale)
{
	GetUIComponent()->setVipIconName(iconPath, texIcon, scale);
}

void ActorBody::setVipIconVisible(bool vipVisible)
{
	if (m_UIComponent)
		GetUIComponent()->setVipIconVisible(vipVisible);
}

void ActorBody::setVoiceIconVisible(bool voiceVisible)
{
	if (m_UIComponent)
		GetUIComponent()->setVoiceIconVisible(voiceVisible);
}

void ActorBody::setHeadIconByPath(const char* imageResPath, const char* imageResUVName, int imageWidth /*= 0*/, int imageHeight /*= 0*/,bool isSync/*=true*/)
{
	GetUIComponent()->setHeadIconByPath(imageResPath, imageResUVName, imageWidth, imageHeight, isSync);
}

//2021-12-20 codeby: wangyang ??????
void ActorBody::setVipNameColor(int colorR, int colorG, int colorB)
{
	GetUIComponent()->setVipNameColor(colorR, colorG,colorB);
}

void ActorBody::setNameBlickStartTime(int time)
{
	GetUIComponent()->m_NameBlickStartTime = time;
}

Rainbow::NameText3D* ActorBody::getNameDispObj() 
{
	if (m_UIComponent)
		return GetUIComponent()->m_NameDispObj;

	return nullptr;
}


void ActorBody::showSaddle(int id)
{
	if (id < 0)
	{
		return;
	}

	m_ShowSaddle = id;
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model)
	{
		for (int i = 1; i <= MAX_SADDLE; i++)
		{
			model->ShowSkin(s_SaddleNames[i - 1], i == id);
		}
	}
}

void ActorBody::showNecklace(int id)
{
	m_ShowNecklace = id;
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model)
	{
		for (int i = 1; i <= MAX_NECKLACE; i++)
		{
			model->ShowSkin(s_NecklaceNames[i - 1], i == id);
		}
	}
}

void ActorBody::showRake(int id)
{
	m_ShowRake = id;
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model)
	{
		for (int i = 1; i <= MAX_RAKE; i++)
		{
			model->ShowSkin(s_RakeNames[i - 1], i == id);
		}
	}
}


void ActorBody::setIsAttachModelView(bool bAttach)
{
	m_IsAttachModelView = bAttach;

	if (!m_Entity)return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (!model) return;
	IModelAnimationPlayer* animPlayer = model->GetModelAnimationPlayer();
	if (!animPlayer)return;
	if (bAttach)
	{
		animPlayer->SetEffectDisEnable(false);
		animPlayer->SetAnimatorSetCullingMode(0);
	}
}

void ActorBody::setIsInStarStationCabin(bool bInStarStationCabin, bool isSetViewMod /*= true*/)
{
	if (isSetViewMod && m_bInStarStationCabin != bInStarStationCabin)
	{
		PlayerControl* playerControl = dynamic_cast<PlayerControl*>(m_OwnerPlayer);
		if (playerControl && g_pPlayerCtrl == playerControl && playerControl->hasUIControl())
		{

			CameraControlMode mode = CameraControlMode::CAMERA_TPS_FRONT;
			//进入传送仓强制切换成前置视角，退出时如果角色的当前视角不再是前置视角说明角色切换过了。
			if (!bInStarStationCabin)
			{
				int curmode = playerControl->getViewMode();
				if (curmode == CameraControlMode::CAMERA_TPS_FRONT)
				{
					mode = (CameraControlMode)playerControl->m_oldViewMode;
				}
				else
				{
					mode = (CameraControlMode)curmode;
				}
			}
			
			playerControl->setViewMode(mode);
		}
	}

	if (bInStarStationCabin)
	{
		setScale(0.8f);
	}
	else
	{
		setScale(1.0f);
	}

	PlayerLocoMotion* pLocomotion = dynamic_cast<PlayerLocoMotion*>(m_OwnerActor->getLocoMotion());
	if (pLocomotion)
	{
		if (m_bInStarStationCabin)
			pLocomotion->attachPhysActor();
		else
			pLocomotion->detachPhysActor();
	}

	m_bInStarStationCabin = bInStarStationCabin;
}

bool ActorBody::isInStarStationCabin()
{
	return m_bInStarStationCabin;
}


void ActorBody::setkeeplook(bool keep)
{
	m_bkeeplook = keep;
}
void ActorBody::setNeedUpdateSkin(bool bupdate)
{
	m_bupdateSkin = bupdate;
}

void ActorBody::setShowUp(bool bupdate)
{
	m_bShowUp = bupdate;
}

void ActorBody::revoverShapeHeight() {
	if (m_bNeedRecoverShapeHeight) {
		m_NameObjHeight = m_fBeforeShapeHeight;
		m_bNeedRecoverShapeHeight = false;
	}
}


//20211020 codeby:wangyu 新增播放装扮互动时显示和隐藏玩家昵称和血条
void ActorBody::setNameAndHpVisible(bool isVisble)
{
	if (m_UIComponent)
		GetUIComponent()->setNameAndHpVisible(isVisble);
}

void ActorBody::SetObjectCache(ObjectCacheBase* cache)
{
	m_ObjectCache = cache;
}

ObjectCacheBase* ActorBody::GetObjectCache()
{
	return m_ObjectCache;
}

void ActorBody::setHeadDisplayIcon(int itemid, int tick /* = -1 */)
{
#ifndef IWORLD_SERVER_BUILD
	if (itemid > 0)
	{
		int u = 0, v = 0, w = 0, h = 0, r = 255, g = 255, b = 255;
		SharePtr<Texture2D> huires = nullptr;

		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ItemIconManager_getItemIcon",
			SandboxContext(nullptr)
			.SetData_Number("toolid", itemid)
			.SetData_Number("u", u)
			.SetData_Number("v", v)
			.SetData_Number("w", w)
			.SetData_Number("h", h)
			.SetData_Number("r", r)
			.SetData_Number("g", g)
			.SetData_Number("b", b));
		if (result.IsExecSuccessed())
		{
			huires = result.GetData_UserObject<SharePtr<Texture2D>>("huires");
		/*	w = result.GetData_Number("w");
			h = result.GetData_Number("h");*/
		}
		/*setNeedItemIcon(huires, tick, w, h);*/
		setNeedItemIcon(huires, tick);
	}
	else
	{
		setNeedItemIcon(nullptr, 1);
	}
#endif
	if (m_World && !m_World->isRemoteMode())
	{
		if (itemid != m_OwnerActor->getLastHeadIconItemID() || itemid > 0)
		{
			PB_ActorHeadDisplayIconHC actorHeadDisplayIconHC;
			actorHeadDisplayIconHC.set_actorid(m_OwnerActor->getObjId());
			if (itemid > 0)
			{
				actorHeadDisplayIconHC.set_itemid(itemid);
				actorHeadDisplayIconHC.set_tick(tick);
			}
			m_World->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_HEAD_DISPLAY_ICON_HC, actorHeadDisplayIconHC, m_OwnerActor);
			m_OwnerActor->setLastHeadIconItemID(itemid);
		}
	}
}


int ActorBody::getAnimTickTime(int anim)
{
	if (m_Entity == nullptr)
	{
		return 0;
	}
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (!model) return 0;

	if(model->IsKindOf<ModelLegacy>())
	{
		Rainbow::ModelData* modelData = nullptr;
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
		if (legacymodel != nullptr && legacymodel->GetModelData())
		{
			modelData = legacymodel->GetModelData().Get();
		}

		if (modelData != nullptr)
		{
			const SharePtr<AnimationData>& animData = modelData->getAnimationSeq(seqType2ID(anim));
			if (animData)
			{
				auto seq = animData->getSequenceById(seqType2ID(anim));
				if (seq)
				{
					int num = seq->time_end - seq->time_start;
					return num / 600 * 20 + 3;
				}
			}
		}
	}

	if (model->IsKindOf<ModelNew>())
	{
		//todo...
		return 0;
	}

	return 0;
}


void ActorBody::setShapeAnimEntity(Rainbow::Entity* entity)
{
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_shapeAnimEntity);
	if (entity)
	{
		m_shapeAnimEntity = entity;
	}
}

void ActorBody::setExchangeItemIcon(SharePtr<Texture2D> huires, int num /*= -1*/, int tick /*= -1*/, int huiresWidth /*= 62*/, int huiresHeight /*= 62*/)
{
	GetUIComponent()->setExchangeItemIcon(huires,num,tick,huiresWidth,huiresHeight);
}

void ActorBody::setSaleItemIcon(SharePtr<Texture2D> huires, int num /*= -1*/, int tick /*= -1*/, int huiresWidth /*= 62*/, int huiresHeight /*= 62*/)
{
	GetUIComponent()->setSaleItemIcon(huires, num, tick, huiresWidth, huiresHeight);
}

void ActorBody::setSaleItemIconByString(const std::string& pngName, int num, int tick, int huiresWidth, int huiresHeight)
{
	GetUIComponent()->setSaleItemIconByString(pngName, num, tick, huiresWidth,huiresHeight);
}

void ActorBody::showExchangeBubble(bool show)
{
	GetUIComponent()->showExchangeBubble(show);
}


void ActorBody::setHeadExchangeDisplayIcon(int exchageItem, int saleItem, bool isActor, int exchangeNum, int saleNum, int tick)
{
	GetUIComponent()->setHeadExchangeDisplayIcon(exchageItem,  saleItem,  isActor,  exchangeNum,  saleNum,  tick);
}

void ActorBody::loadEditModelRes()
{
#ifdef BUILD_MINI_EDITOR_APP
	//if (GetFileManager().IsFileExist(m_ModelPath.c_str()))
	{
		Rainbow::Model* pModel = ActorBody::LoadModel(m_ModelPath);
		if (pModel != nullptr)
		{
			m_ModelPath = "";
			m_FaceMesh = NULL;
			if (pModel != NULL && m_Entity)
			{
				m_Entity->Load(pModel);
				m_HeadBoneID = pModel->GetBoneId("Head");
				//OGRE_RELEASE(pModel);
			}
			if (m_CurAnim[0] <= 0)
			{
				setCurAnim(0, 0);
			}
			else
			{
				int anim = m_CurAnim[0];
				setCurAnim(0, 0);
				setCurAnim(anim, 0);
			}

			if (m_Entity)
			{
				m_Entity->SetScale(Vector3f(m_RealScale, m_RealScale, m_RealScale));
			}

			clearEquipItems(pModel);
		}
	}
#endif //BUILD_MINI_EDITOR_APP
}

int  ActorBody::getInnerGraphicsOffest(int itype)
{
	return GetUIComponent()->getInnerGraphicsOffest(itype);
}
//#endif
