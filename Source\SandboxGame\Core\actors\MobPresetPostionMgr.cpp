#include "MobPresetPostionMgr.h"
#include "world.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "WorldManager.h"
#include <random>
#include <algorithm>
#include <cmath>

// 日志宏定义
#define MOBPRESETMGR_LOG(...) \
    do { \
        WarningStringMsg("[MobPresetMgr] " __VA_ARGS__); \
    } while(0)

// 泊松圆盘采样参数
const float MobPresetPostionMgr::MIN_DISTANCE_RATIO = 0.3f;  // 最小距离比例
const int MobPresetPostionMgr::MAX_ATTEMPTS = 30;            // 最大尝试次数

MobPresetPostionMgr::MobPresetPostionMgr()
    : m_debugEnabled(false)
{
}

MobPresetPostionMgr::~MobPresetPostionMgr()
{
    ClearDebugLines();
}

void MobPresetPostionMgr::Init(int minX, int maxX, int minZ, int maxZ, int pointCount)
{
    // 清理现有数据
    m_positions.clear();
    ClearDebugLines();
    
    // 生成均匀分布的点
    GenerateUniformPoints(minX, maxX, minZ, maxZ, pointCount);
}

void MobPresetPostionMgr::GenerateUniformPoints(int minX, int maxX, int minZ, int maxZ, int pointCount)
{
    if (minX >= maxX || minZ >= maxZ || pointCount <= 0)
        return;
    
    const int rangeX = maxX - minX;
    const int rangeZ = maxZ - minZ;
    const float minDistance = std::min(rangeX, rangeZ) * MIN_DISTANCE_RATIO;
    
    // 【关键修改】使用固定种子确保客户端和服务器生成相同的预设点
    // 种子基于地图范围和点数量，确保一致性
    const uint32_t fixedSeed = static_cast<uint32_t>(minX + maxX + minZ + maxZ + pointCount + 12345);
    std::mt19937 gen(fixedSeed);
    std::uniform_int_distribution<> distX(minX, maxX);
    std::uniform_int_distribution<> distZ(minZ, maxZ);
    
    MOBPRESETMGR_LOG("GenerateUniformPoints: Using fixed seed %u for consistent generation", fixedSeed);
    
    m_positions.reserve(pointCount);
    
    // 使用改进的泊松圆盘采样算法
    for (int i = 0; i < pointCount && m_positions.size() < pointCount; ++i)
    {
        bool validPoint = false;
        WCoord newPos;
        
        // 尝试生成一个有效的点
        for (int attempt = 0; attempt < MAX_ATTEMPTS; ++attempt)
        {
            newPos = WCoord(distX(gen), -1, distZ(gen));
            
            // 检查与现有点的距离
            validPoint = true;
            for (const auto& existingPos : m_positions)
            {
                if (GetDistance2D(newPos, existingPos.pos) < minDistance)
                {
                    validPoint = false;
                    break;
                }
            }
            
            if (validPoint)
                break;
        }
        
        // 如果找到有效点，添加到列表中
        if (validPoint)
        {
            m_positions.emplace_back(newPos);
        }
        else
        {
            // 如果尝试失败，降低距离要求后重试
            newPos = WCoord(distX(gen), -1, distZ(gen));
            m_positions.emplace_back(newPos);
        }
    }
    
    // 如果生成的点数不够，用简单随机填充剩余的点
    while (m_positions.size() < pointCount)
    {
        WCoord newPos(distX(gen), -1, distZ(gen));
        m_positions.emplace_back(newPos);
    }
}

std::vector<int> MobPresetPostionMgr::GetAvailablePositions() const
{
    std::vector<int> availableIndices;
    availableIndices.reserve(m_positions.size());
    
    for (size_t i = 0; i < m_positions.size(); ++i)
    {
        if (!m_positions[i].isUsed)
        {
            availableIndices.push_back(static_cast<int>(i));
        }
    }
    
    return availableIndices;
}

void MobPresetPostionMgr::SetPositionUsed(int index, bool used, int gameDay)
{
    if (index >= 0 && index < static_cast<int>(m_positions.size()))
    {
        m_positions[index].isUsed = used;
        if (used && gameDay >= 0)
        {
            m_positions[index].lastUsedDay = gameDay;
        }
    }
}

int MobPresetPostionMgr::FindPositionIndex(const WCoord& pos) const
{
    for (size_t i = 0; i < m_positions.size(); ++i)
    {
        if (m_positions[i].pos.x == pos.x && m_positions[i].pos.z == pos.z)
        {
            return static_cast<int>(i);
        }
    }
    return -1;
}

void MobPresetPostionMgr::ResetAllPositions()
{
    for (auto& pos : m_positions)
    {
        pos.isUsed = false;
        pos.lastUsedDay = -1;
    }
}

void MobPresetPostionMgr::DrawDebugLines(World* pWorld, bool enabled)
{
    MOBPRESETMGR_LOG("CLIENT: DrawDebugLines called: pWorld=%p, enabled=%d", pWorld, enabled);
    
    if (!pWorld)
    {
        MOBPRESETMGR_LOG("CLIENT: DrawDebugLines: pWorld is null");
        return;
    }
    
    // 先清理现有的调试线条
    ClearDebugLines();
    
    m_debugEnabled = enabled;
    
    if (!enabled)
    {
        MOBPRESETMGR_LOG("CLIENT: DrawDebugLines: debug disabled");
        return;
    }
    
    MOBPRESETMGR_LOG("CLIENT: Drawing debug lines for %zu preset positions (all GREEN - client doesn't track usage)", m_positions.size());
    
    // 打印所有500个预设位置坐标，供传送验证使用
    MOBPRESETMGR_LOG("=== All %zu Preset Positions ===", m_positions.size());
    for (size_t i = 0; i < m_positions.size(); ++i)
    {
        const auto& pos = m_positions[i];
        // Y坐标+1确保玩家传送到地面上方而不是地面内
        MOBPRESETMGR_LOG("[%03zu] /tp %d %d %d", i, pos.pos.x, pos.pos.y + 1, pos.pos.z);
    }
    MOBPRESETMGR_LOG("=== End of %zu Preset Positions ===", m_positions.size());
    
    // 获取世界渲染器的CurveFace
    CurveFace* curveRender = pWorld->getRender() ? pWorld->getRender()->getCurveRender() : nullptr;
    if (!curveRender)
    {
        MOBPRESETMGR_LOG("CLIENT: DrawDebugLines: curveRender is null");
        return;
    }
    
    MOBPRESETMGR_LOG("CLIENT: Creating debug lines...");
    
    // // 先创建一些靠近原点的测试线条，看看是否能显示
    // WCoord testPositions[] = {
    //     WCoord(0, 65, 0),
    //     WCoord(5, 65, 0),
    //     WCoord(10, 65, 0),
    //     WCoord(0, 65, 5),
    //     WCoord(0, 65, 10)
    // };
    
    // MOBPRESETMGR_LOG("CLIENT: Creating test lines near origin...");
    // for (int i = 0; i < 5; ++i)
    // {
    //     WCoord pos = testPositions[i];
    //     WCoord startPos(pos.x * BLOCK_SIZE, pos.y * BLOCK_SIZE, pos.z * BLOCK_SIZE);
    //     WCoord endPos(pos.x * BLOCK_SIZE, (pos.y + 15) * BLOCK_SIZE, pos.z * BLOCK_SIZE);
        
    //     SceneEffectLine* line = new SceneEffectLine(startPos, endPos, 3, CURVEFACEMTL_TEXWHITE, true, curveRender);
    //     line->SetColor(MakeBlockVector(255, 255, 0, 255)); // 黄色测试线条
    //     line->Refresh();
    //     m_debugLines.push_back(line);
        
    //     MOBPRESETMGR_LOG("CLIENT: Created test line %d: pos(%d, %d, %d) -> worldPos(%d, %d, %d)", 
    //                i, pos.x, pos.y, pos.z, startPos.x, startPos.y, startPos.z);
    // }
    
    // 为每个预设位置创建调试线条
    for (size_t i = 0; i < m_positions.size(); ++i)
    {
        const auto& pos = m_positions[i];
        
        // 创建垂直线条：从地面到较低的高度，使其更容易看到
        WCoord startPos(pos.pos.x * BLOCK_SIZE, 64 * BLOCK_SIZE, pos.pos.z * BLOCK_SIZE);
        WCoord endPos(pos.pos.x * BLOCK_SIZE, 100 * BLOCK_SIZE, pos.pos.z * BLOCK_SIZE);
        
        // 创建线条对象
        SceneEffectLine* line = new SceneEffectLine(startPos, endPos, 3, CURVEFACEMTL_TEXWHITE, true, curveRender);
        
        // 设置颜色：绿色表示可用，红色表示已使用
        if (pos.isUsed)
        {
            line->SetColor(MakeBlockVector(255, 0, 0, 255)); // 红色 - 已使用
        }
        else
        {
            line->SetColor(MakeBlockVector(0, 255, 0, 255)); // 绿色 - 可用
        }
        
        // 刷新线条
        line->Refresh();
        
        // 添加到调试线条列表
        m_debugLines.push_back(line);
        
        if (i < 5) // 只打印前5个位置的调试信息
        {
            MOBPRESETMGR_LOG("Created line %zu: pos(%d, %d, %d) -> worldPos(%d, %d, %d)", 
                   i, pos.pos.x, pos.pos.y, pos.pos.z, startPos.x, startPos.y, startPos.z);
        }
    }
    
    MOBPRESETMGR_LOG("DrawDebugLines: created %zu debug lines", m_debugLines.size());
}

void MobPresetPostionMgr::Draw(World* pWorld)
{
    if (!pWorld || !m_debugEnabled)
    {
        return;
    }
    
    // 绘制所有调试线条
    for (auto* line : m_debugLines)
    {
        if (line && line->IsActive(pWorld))
        {
            line->OnDraw(pWorld);
        }
    }
}

void MobPresetPostionMgr::ClearDebugLines()
{
    for (SceneEffectLine* line : m_debugLines) {
        if (line) {
            delete line;
        }
    }
    m_debugLines.clear();
}

// 为刷新系统实现的新方法

WCoord MobPresetPostionMgr::GetNearestAvailablePosition(const WCoord& centerPos, int maxDistance) const
{
    float minDistSq = maxDistance * maxDistance;
    int bestIndex = -1;
    
    for (int i = 0; i < m_positions.size(); i++) {
        if (!m_positions[i].isUsed) {
            float distSq = GetDistance2D(centerPos, m_positions[i].pos);
            distSq = distSq * distSq; // 平方距离
            
            if (distSq < minDistSq) {
                minDistSq = distSq;
                bestIndex = i;
            }
        }
    }
    
    if (bestIndex >= 0) {
        return m_positions[bestIndex].pos;
    }
    
    // 如果没有可用位置，返回无效坐标
    return WCoord(-1, -999, -1);
}

WCoord MobPresetPostionMgr::GetRandomAvailablePosition() const
{
    std::vector<int> availableIndices;
    
    for (int i = 0; i < m_positions.size(); i++) {
        if (!m_positions[i].isUsed) {
            availableIndices.push_back(i);
        }
    }
    
    MOBPRESETMGR_LOG("GetRandomAvailablePosition: found %zu available positions out of %zu total", 
                    availableIndices.size(), m_positions.size());
    
    if (availableIndices.empty()) {
        MOBPRESETMGR_LOG("GetRandomAvailablePosition: no available positions, returning invalid coord");
        
        // 打印前5个位置的状态来调试
        for (int i = 0; i < std::min(5, static_cast<int>(m_positions.size())); i++) {
            MOBPRESETMGR_LOG("Position %d: (%d, %d, %d) isUsed=%d", 
                            i, m_positions[i].pos.x, m_positions[i].pos.y, m_positions[i].pos.z, m_positions[i].isUsed);
        }
        
        return WCoord(-1, -999, -1); // 无效坐标，y=-999表示无效
    }
    
    // 使用固定种子确保一致性，基于当前时间和位置数确保每次调用有一定随机性
    static uint32_t callCount = 0;
    const uint32_t fixedSeed = static_cast<uint32_t>(availableIndices.size() + (++callCount) + 54321);
    std::mt19937 gen(fixedSeed);
    std::uniform_int_distribution<> dis(0, availableIndices.size() - 1);
    
    int randomIndex = availableIndices[dis(gen)];
    WCoord result = m_positions[randomIndex].pos;
    
    MOBPRESETMGR_LOG("GetRandomAvailablePosition: selected position (%d, %d, %d) at index %d", 
                    result.x, result.y, result.z, randomIndex);
    
    return result;
}

std::vector<int> MobPresetPostionMgr::GetAvailablePositionsInRange(const WCoord& centerPos, int maxDistance) const
{
    std::vector<int> result;
    float maxDistSq = maxDistance * maxDistance;
    
    for (int i = 0; i < m_positions.size(); i++) {
        if (!m_positions[i].isUsed) {
            float distSq = GetDistance2D(centerPos, m_positions[i].pos);
            distSq = distSq * distSq; // 平方距离
            
            if (distSq <= maxDistSq) {
                result.push_back(i);
            }
        }
    }
    
    return result;
}

void MobPresetPostionMgr::ResetExpiredPositions(int currentGameDay, int expireDays)
{
    for (auto& pos : m_positions) {
        if (pos.isUsed && pos.lastUsedDay >= 0) {
            if (currentGameDay - pos.lastUsedDay >= expireDays) {
                pos.isUsed = false;
                pos.lastUsedDay = -1;
                MOBPRESETMGR_LOG("Reset expired position at (%d, %d, %d), used %d days ago", 
                                pos.pos.x, pos.pos.y, pos.pos.z, currentGameDay - pos.lastUsedDay);
            }
        }
    }
}

WCoord MobPresetPostionMgr::GetPresetPointInChunk(int chunkX, int chunkZ)
{
    // 将chunk坐标转换为block坐标范围
    int blockStartX = chunkX * 16;
    int blockEndX = blockStartX + 16;
    int blockStartZ = chunkZ * 16;
    int blockEndZ = blockStartZ + 16;
    
    // 遍历所有预设位置，查找在指定chunk范围内且未被使用的点
    for (const auto& pos : m_positions) {
        // 检查该位置是否在chunk的block范围内：(blockStartX, blockEndX) 和 (blockStartZ, blockEndZ)
        // 并且该位置尚未被使用
        if (pos.pos.x >= blockStartX && pos.pos.x < blockEndX && 
            pos.pos.z >= blockStartZ && pos.pos.z < blockEndZ && 
            !pos.isUsed) {
            
            MOBPRESETMGR_LOG("Found available preset point (%d, %d, %d) in chunk (%d, %d) [blocks %d-%d, %d-%d]", 
                            pos.pos.x, pos.pos.y, pos.pos.z, chunkX, chunkZ, 
                            blockStartX, blockEndX-1, blockStartZ, blockEndZ-1);
            return pos.pos;
        }
    }
    
    // 没有找到可用的预设点，返回无效坐标
    MOBPRESETMGR_LOG("No available preset point found in chunk (%d, %d) [blocks %d-%d, %d-%d]", 
                    chunkX, chunkZ, blockStartX, blockEndX-1, blockStartZ, blockEndZ-1);
    return WCoord(-1, -999, -1);
}

float MobPresetPostionMgr::GetDistance2D(const WCoord& pos1, const WCoord& pos2) const
{
    float dx = static_cast<float>(pos1.x - pos2.x);
    float dz = static_cast<float>(pos1.z - pos2.z);
    return std::sqrt(dx * dx + dz * dz);
} 

int MobPresetPostionMgr::GetAvailablePositionCount() const
{
    int count = 0;
    for (const auto& pos : m_positions) {
        if (!pos.isUsed) {
            count++;
        }
    }
    
    if (count > 0 && count % 100 == 0) { // 每100个可用位置打印一次
        MOBPRESETMGR_LOG("GetAvailablePositionCount: %d available positions out of %zu total", 
                        count, m_positions.size());
    }
    
    return count;
}

int MobPresetPostionMgr::GetTotalPositionCount() const
{
    return static_cast<int>(m_positions.size());
} 