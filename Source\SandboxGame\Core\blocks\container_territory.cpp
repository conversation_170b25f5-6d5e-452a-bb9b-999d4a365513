#include "container_territory.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "ActorVehicleAssemble.h"
#include "ClientActorHelper.h"
#include "ChunkSave_generated.h"
#include "IPlayerControl.h"
#include "WorldRender.h"
#include "ClientInfoProxy.h"
#include "CurveFace.h"
#include "GameCamera.h"
#include "ActorVillager.h"
#include "chunk.h"
#include "PlayerControl.h"
#include "Components/BoxCollider.h"
#include "Components/MeshRenderer.h"
#include "Core/GameScene.h"
#include "BlockScene.h"
#include "Misc/GOCreation.h"
#include "world_types.h"
#include "TerritoryManager.h"
#include <container_socdoor.h>
#include <climits> // 添加对 INT_MAX 的支持
#include <algorithm> // 添加对 std::min 的支持
#include "ZmqProxy.h"
//#include "MovableObject.h"
/**********************************************************************************************
��    ����TerritoryContainer
��    �ܣ����
********************************************************************************************* */

// 在文件开头定义静态常量 - 使用完整尺寸
//BlockSize 、AABB的大小 填奇数
const Rainbow::Vector3f TerritoryContainer::DEFAULT_TERRITORY_BOX_SIZE(31, 31, 31);
// UI通知延时常量（tick）20tick = 1秒，延时0.5秒 = 10tick
const int TerritoryContainer::UI_NOTIFICATION_DELAY_TICKS = 3 * 20;

TerritoryContainer::TerritoryContainer() : WorldStorageBox()
{
	m_render = true;
	m_haveAward = false;
	m_OwnerUin = 1;
	m_showBoundingBox = true;
	m_lastErosionBlockCount = 0;
	m_uiNotificationDelayTicks = 0;
	m_maintenanceNeedsDirty = true;
	m_NeedTick = true;
}
TerritoryContainer::TerritoryContainer(const WCoord& blockpos) :WorldStorageBox(blockpos)
{
	m_render = true;
	m_haveAward = false;
	m_OwnerUin = 1;
	m_showBoundingBox = true;
	m_lastErosionBlockCount = 0;
	m_uiNotificationDelayTicks = 0;
	m_maintenanceNeedsDirty = true;
	m_NeedTick = true;
}

TerritoryContainer::~TerritoryContainer()
{
}

bool TerritoryContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerTerritory*>(srcdata);
	//loadContainerCommon(src->basedata());
	WorldStorageBox::load(src->basedata());

	m_flagPoint.resize(src->flagPoint()->size());
	for (size_t i = 0; i < m_flagPoint.size(); i++)
	{
		m_flagPoint[i] = Coord3ToWCoord(src->flagPoint()->Get(i));
	}

	m_villagersID.resize(src->villagers()->size());
	for (size_t i = 0; i < m_villagersID.size(); i++)
	{
		if (src->villagers()->Get(i) != NULL) 
		{
			m_villagersID[i] = StrToWorldId(src->villagers()->Get(i)->c_str());
		}
		
	}
	m_haveAward = src->haveAward();
	if (src->authorizedPlayers()) 
	{
		authorizedPlayers.resize(src->authorizedPlayers()->size());
		for (size_t i = 0; i < authorizedPlayers.size(); i++)
		{
			if (src->authorizedPlayers()->Get(i) != NULL)
			{
				authorizedPlayers[i] = src->authorizedPlayers()->Get(i);
			}

		}
	}
	m_render = false;
	m_showBoundingBox = true;
	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> TerritoryContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	//auto basedata = saveContainerCommon(builder);
	auto basedata = WorldStorageBox::saveContainerStorage(builder);

	std::vector<FBSave::Coord3>flagPoint;
	for (size_t i = 0; i < m_flagPoint.size(); i++)
	{
		flagPoint.push_back(WCoordToCoord3(m_flagPoint[i]));
	}
	std::vector<string> villageID;
	for (size_t i = 0; i < m_villagersID.size(); i++)
	{
		villageID.push_back(WorldIdToStr(m_villagersID[i]));
	}

	std::vector<unsigned int> authorizeds;
	for (size_t i = 0; i < authorizedPlayers.size(); i++)
	{
		authorizeds.push_back(authorizedPlayers[i]);
	}

	//flatbuffers::Offset<flatbuffers::Vector<const FBSave::Coord3 *>> flagPoint = 0,
	auto vpoint = builder.CreateVectorOfStructs(flagPoint);
	auto vvillage = builder.CreateVectorOfStrings(villageID);
	auto vauthorizeds = builder.CreateVector(authorizeds);
	auto actor = FBSave::CreateContainerTerritory(builder, basedata, vpoint, vvillage,m_haveAward, vauthorizeds);

	updateTerritory(m_World,m_uuid, m_OwnerUin, m_BlockPos, authorizedPlayers);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerTerritory, actor.Union());
}

void TerritoryContainer::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateDisplay();

	GetWorldManagerPtr()->getWorldInfoManager()->setVillagerWorship(pworld, m_OwnerUin, m_haveAward);
	UpdateEffect(pworld);
	
	// 只更新边界盒位置，使用已有尺寸的半值
	m_boundingBox.SetFromCenterAndHalfExtents(
		BlockCenterCoord(m_BlockPos).toVector3(),
		Rainbow::Vector3f(DEFAULT_TERRITORY_BOX_SIZE.x * BLOCK_SIZE * 0.5f,
						  DEFAULT_TERRITORY_BOX_SIZE.y * BLOCK_SIZE * 0.5f,
						  DEFAULT_TERRITORY_BOX_SIZE.z * BLOCK_SIZE * 0.5f)
	);

	// 修改：注册到领地管理器，传入 m_BlockPos 和 this
	TerritoryManager::GetInstance()->RegisterTerritory(m_BlockPos, this);
	TerritoryManager::GetInstance()->RegisterWithWorld(pworld);//函数内部已经处理成只调用一次
	
	// 新增：通知附近方块领地已创建
	NotifyNearbyBlocksOnCreate();
}
void TerritoryContainer::leaveWorld()
{
	// 在离开世界前通知附近方块
	NotifyNearbyBlocksOnDestroy();
	
	// 从领地管理器中注销
	TerritoryManager::GetInstance()->UnregisterTerritory(m_BlockPos);
	
	if (m_World)
	{
		if (!m_World->isRemoteMode())
		{
			//ֹͣЧ
			m_World->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_01.prefab", getEffectPos());
			m_World->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_02.prefab", getEffectPos());
		}
		else
		{
			//�ͻ���ͨͼ����Ҫ����һ��
			if (g_pPlayerCtrl && m_OwnerUin == g_pPlayerCtrl->getUin())
			{
				m_World->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_01.prefab", getEffectPos(), false);
				m_World->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_02.prefab", getEffectPos(), false);
			}
		}
	}
	
	WorldContainer::leaveWorld();
}

void TerritoryContainer::OnTriggerEnter(const Rainbow::EventContent* touch)
{
	if (m_World->isRemoteMode()) return;
	if (!touch || !(touch->userData)) return;
	auto dst = static_cast<Rainbow::Collider*>(touch->userData);
	auto dstNode = static_cast<MNSandbox::SandboxNode*>(dst->GetUserData());
	if (!dstNode)
	{
		return;
	}
	if (!dstNode->IsKindOf<ClientPlayer>()) 
		return;
	int buffId = 205;
	auto player = dstNode->ToCast<ClientPlayer>();
	
	if (!player->getLivingAttrib()->hasBuff(buffId))
		player->getLivingAttrib()->addBuff(buffId, 1);
	//auto src = this->m_pGameObject->GetComponent<Collider>();
	//MINIW::GetPhysXManagerPtr()->OnEnterTouchRecard(src, dst, Rainbow::Vector3f::zero, Rainbow::Vector3f::zero, MINIW::PhysXManager::TOUCH_TRIGGER);
}


void TerritoryContainer::OnTriggerExit(const  Rainbow::EventContent* touch)
{
	if (m_World->isRemoteMode()) return;
	if (!touch || !(touch->userData)) return;
	auto dst = static_cast<Rainbow::Collider*>(touch->userData);
	auto dstNode = static_cast<MNSandbox::SandboxNode*>(dst->GetUserData());
	if (!dstNode)
	{
		return;
	}
	if (!dstNode->IsKindOf<ClientPlayer>())
		return;
	int buffId = 205;
	auto player = dstNode->ToCast<ClientPlayer>();

	if (player->getLivingAttrib()->hasBuff(buffId))
		player->getLivingAttrib()->removeBuff(buffId, 1);
	//auto src = this->m_pGameObject->GetComponent<Collider>();
	//MINIW::GetPhysXManagerPtr()->OnExitTouchRecard(src, dst, MINIW::PhysXManager::TOUCH_TRIGGER);
}


bool TerritoryContainer::IsModfiy(int userID)
{
	// 使用新的授权系统检查用户权限
	return IsAuthorized(static_cast<unsigned int>(userID));
}

void TerritoryContainer::updateTick()
{
	//auto test = Get24HourMaintenanceInfo();
	if (m_World->isRemoteMode()) return;

	// 定时执行UI通知更新（无论数量是否变化）
	bool b = m_AttachToUI || m_OpenUINs.size() > 0;
	if (b)
	{
		// 增加延时计数器
		m_uiNotificationDelayTicks++;
		
		// 达到延时tick数就通知UI更新
		if (m_uiNotificationDelayTicks >= UI_NOTIFICATION_DELAY_TICKS)
		{
			size_t currentCount = GetManagedErosionBlockCount();
			m_lastErosionBlockCount = currentCount;
			notifyChange2Openers(-1, true);
			m_uiNotificationDelayTicks = 0; // 重置延时计数器
		}
	}
}

void TerritoryContainer::updateDisplay(float dtime)
{

	//auto physScene = m_World->m_PhysScene;
	//if (nullptr != physScene)
	//{
	//	Rainbow::RaycastHit hitInfo;
	//	//int mask = 0xFFFFFFFF;
	//	unsigned int mask = 0xffffffff;
	//	Rainbow::Vector3f dir_ = Vector3f::yAxis; //dir.GetNormalizedSafe();
	//	float dist = 1800.0f;
	//	auto pos = g_pPlayerCtrl->getPosition().toVector3()+ Vector3f(0,-400,0);
	//	//bool b = physScene->RayCast(pos, dir_, dist, hitInfo, mask, false);
	//	Rainbow::RaycastHits info =  physScene->RayCastAll(pos, dir_, dist, mask, false);
	//	
	//	for (auto& item :info)
	//	{
	//		auto clollider = item.collider;
	//		int dd = 0;
	//	}
	//	
	//}

#ifdef ENABLE_PLAYER_CMD_COMMAND
	if (ClientActor::ms_enableDebugWrapBpx)
	{// 如果启用了边界盒显示，绘制它
		if (m_showBoundingBox) {
			m_boundingBox.DrawWireframe(m_World, Rainbow::ColourValue(0.0f, 1.0f, 0.5f, 1.0f)); // 绿色
		}
	}
#endif
}

std::vector<WCoord> TerritoryContainer::GetFlagPoint()
{
	return m_flagPoint;
}

bool TerritoryContainer::HasFlagPoint(const WCoord& point)
{
	for (size_t i = 0; i < m_flagPoint.size(); i++) 
	{
		if (point == m_flagPoint[i]) 
		{
			return true;
		}
	}
	return false;
}

bool TerritoryContainer::AddFlagPoint(const std::vector<WCoord>& points)
{
	for (size_t i = 0; i < points.size(); i++)
	{
		if (!HasFlagPoint(points[i])) 
		{
			m_flagPoint.push_back(points[i]);
		}
	}
	return true;
}

bool TerritoryContainer::AddFlagPoint(const WCoord& point)
{
	if (!HasFlagPoint(point))
	{
		m_flagPoint.push_back(point);
	}
	return true;
}

bool TerritoryContainer::RemoveFlagPoint(const WCoord& point)
{
	for(size_t i = 0; i < m_flagPoint.size(); i++)
	{
		if (point == m_flagPoint[i]) 
		{
			m_flagPoint.erase(m_flagPoint.begin()+i);
			return true;
		}
	}
	return false;
}

bool TerritoryContainer::ClearFlagPoint()
{
	m_flagPoint.clear();
	return true;
}

std::vector<WORLD_ID> TerritoryContainer::GetVillagersID()
{
	std::vector<WORLD_ID> ids;
	for (size_t i = 0; i < m_villagersID.size(); i++) 
	{
		ids.push_back(m_villagersID[i]);
	}
	return ids;
}

bool TerritoryContainer::HasVillagersID(const WORLD_ID& villagerid)
{
	for (size_t i = 0; i < m_villagersID.size(); i++) 
	{
		if (villagerid == m_villagersID[i]) 
		{
			return true;
		}
	}
	return false;
}

bool TerritoryContainer::AddVillagerID(const std::vector<WORLD_ID>& villagerid)
{
	for(size_t i = 0;i < villagerid.size(); i++)
	{
		m_villagersID.push_back(villagerid[i]);
	}
	return true;
}

bool TerritoryContainer::AddVillagerID(const WORLD_ID& villagerid)
{
	m_villagersID.push_back(villagerid);
	return true;
}

bool TerritoryContainer::RemoveVillagerID(const WORLD_ID& villagerid)
{
	for (size_t i = 0; i < m_villagersID.size(); i++)
	{
		if (villagerid == m_villagersID[i])
		{
			m_villagersID.erase(m_villagersID.begin() + i);
			return true;
		}
	}
	return false;
}

bool TerritoryContainer::ClearVillagersID()
{
	m_villagersID.clear();
	return true;
}
/*
索引 MANAGED_BLOCK_COUNT_INDEX(0): 当前管理的腐蚀方块数量
索引 1, 2, 3 : 第一种材料的 itemid、所需数量、当前拥有数量
索引 4, 5, 6 : 第二种材料的 itemid、所需数量、当前拥有数量
索引 7, 8, 9 : 第三种材料的 itemid、所需数量、当前拥有数量
以此类推...（索引范围：MATERIAL_INFO_START_INDEX(1) 到 MATERIAL_INFO_END_INDEX(12)）
索引 MAINTENANCE_PERCENTAGE_INDEX(1000): 维护百分比，0~100的数值 (GetMaintenanceProportion换算后)

// 获取管理的方块数量
float blockCount = territoryContainer->getAttrib(MANAGED_BLOCK_COUNT_INDEX);

// 获取第一种材料的信息
float firstMaterialId = territoryContainer->getAttrib(MATERIAL_INFO_START_INDEX);     // itemid
float firstMaterialRequired = territoryContainer->getAttrib(MATERIAL_INFO_START_INDEX + 1);  // 所需数量
float firstMaterialAvailable = territoryContainer->getAttrib(MATERIAL_INFO_START_INDEX + 2); // 当前拥有

// 获取第二种材料的信息
float secondMaterialId = territoryContainer->getAttrib(MATERIAL_INFO_START_INDEX + 3);    // itemid
float secondMaterialRequired = territoryContainer->getAttrib(MATERIAL_INFO_START_INDEX + 4); // 所需数量
float secondMaterialAvailable = territoryContainer->getAttrib(MATERIAL_INFO_START_INDEX + 5); // 当前拥有

// 获取维护百分比 (0~100)
float maintenancePercentage = territoryContainer->getAttrib(MAINTENANCE_PERCENTAGE_INDEX);
*/
float TerritoryContainer::getAttrib(int i)
{
	if (i == MANAGED_BLOCK_COUNT_INDEX) //当前的维护的数量
		return GetManagedErosionBlockCount();
	
	if (i == MAINTENANCE_PERCENTAGE_INDEX) // 返回维护万分比，换算成0~100的百分比数值
		return static_cast<float>(GetMaintenanceProportion()) / PROPORTION_TO_PERCENTAGE;
	
	// 检查缓存是否需要更新
	if (m_maintenanceNeedsDirty)
	{
		// 更新缓存
		std::map<int, int> needs = Calculate24HourMaintenanceNeeds();
		m_cachedMaintenanceNeeds.clear();
		m_cachedMaintenanceNeeds.assign(needs.begin(), needs.end());
		m_maintenanceNeedsDirty = false;
	}
	
	// 如果缓存为空，直接返回0
	if (m_cachedMaintenanceNeeds.empty())
	{
		return 0.0f;
	}
	
	// 索引从MATERIAL_INFO_START_INDEX开始：每种材料占用PROPERTIES_PER_MATERIAL个连续索引 (itemid, required, available)
	int materialIndex = (i - MATERIAL_INFO_START_INDEX) / PROPERTIES_PER_MATERIAL;  // 材料索引
	int infoType = (i - MATERIAL_INFO_START_INDEX) % PROPERTIES_PER_MATERIAL;       // 信息类型：MATERIAL_ITEMID, MATERIAL_REQUIRED, MATERIAL_AVAILABLE
	
	// 检查材料索引是否有效
	if (materialIndex < 0 || materialIndex >= static_cast<int>(m_cachedMaintenanceNeeds.size()))
	{
		return 0.0f; // 索引超出范围
	}
	
	// 获取材料信息
	int itemid = m_cachedMaintenanceNeeds[materialIndex].first;
	int requiredCount = m_cachedMaintenanceNeeds[materialIndex].second;
	
	if (infoType == MATERIAL_ITEMID) // 返回 itemid
	{
		return static_cast<float>(itemid);
	}
	else if (infoType == MATERIAL_REQUIRED) // 返回所需数量
	{
		return static_cast<float>(requiredCount);
	}
	else if (infoType == MATERIAL_AVAILABLE) // 返回当前拥有数量
	{
		// 计算当前拥有的该材料数量
		int availableCount = 0;
		int maxgrids = getGridCount();
		
		for (int gridIndex = 0; gridIndex < maxgrids; gridIndex++)
		{
			BackPackGrid* grid = index2Grid(gridIndex + STORAGE_START_INDEX);
			if (grid && grid->getItemID() == itemid)
			{
				availableCount += grid->getNum();
			}
		}
		
		return static_cast<float>(availableCount);
	}
	
	return 0.0f; // infoType 无效
}

int TerritoryContainer::getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos)
{
	// 清空现有数据
	if (pItemInfos)
	{
		pItemInfos->Clear();
	}
	if (pAttrInfos)
	{
		pAttrInfos->Clear();
	}

	try
	{
		// 填充格子信息 - 参考 WorldStorageBox::getItemAndAttrib
		if (pItemInfos)
		{
			for (int i = 0; i < STORAGEBOX_CAPACITY * 2; i++)
			{
				BackPackGrid* tgrid = nullptr;
				if (i < STORAGEBOX_CAPACITY)
				{
					tgrid = m_ParentBox ? &m_ParentBox->m_Grids[i] : &m_Grids[i];
				}
				else
				{
					if (m_ParentBox) tgrid = &m_Grids[i - STORAGEBOX_CAPACITY];
					else if (m_AppendBox) tgrid = &m_AppendBox->m_Grids[i - STORAGEBOX_CAPACITY];
				}

				if (tgrid && !tgrid->isEmpty())
				{
					GetISandboxActorSubsystem()->StoreGridData(pItemInfos->Add(), tgrid, i + STORAGE_START_INDEX);
				}
			}
		}

		// 填充属性信息 - 使用现有的 getAttrib 函数
		if (pAttrInfos)
		{
			// 添加管理的腐蚀方块数量
			pAttrInfos->Add(getAttrib(MANAGED_BLOCK_COUNT_INDEX));
			
			// 添加维护材料信息（最多支持4种材料，每种材料3个属性）
			for (int i = MATERIAL_INFO_START_INDEX; i <= MATERIAL_INFO_END_INDEX; i++) // 材料信息索引范围
			{
				float attribValue = getAttrib(i);
				pAttrInfos->Add(attribValue);
			}
			
			// 添加维护百分比
			pAttrInfos->Add(getAttrib(MAINTENANCE_PERCENTAGE_INDEX));
		}

		// 返回格子数量 - 参考 WorldStorageBox::getItemAndAttrib
		if (m_GridCount > 0)
		{
			return m_GridCount;
		}
		else
		{
			return (m_ParentBox != nullptr || m_AppendBox != nullptr) ? STORAGEBOX_CAPACITY * 2 : STORAGEBOX_CAPACITY;
		}
	}
	catch (...)
	{
		// 异常处理
		if (pItemInfos)
		{
			pItemInfos->Clear();
		}
		if (pAttrInfos)
		{
			pAttrInfos->Clear();
		}
		return -1; // 处理失败
	}
}

void TerritoryContainer::onAttachUI()
{
	m_AttachToUI = true;
	// 重置延时计数器，等待UI完全打开
	m_uiNotificationDelayTicks = 0;
	m_lastErosionBlockCount = 0;

	//for (int i = 0; i < GRID_FULL; i++)
	//{
	//	// GameEventQue::GetInstance().postBackpackChange(FURNACE_START_INDEX + i);
	//	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
	//		SetData_Number("grid_index", FURNACE_START_INDEX + i);
	//	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
	//		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
	//	}
	//}
	// GetGameEventQue().postBackPackAttribChange();
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_ATTRIB_CHANGE", sandboxContext);
}

void TerritoryContainer::onDetachUI()
{
	m_AttachToUI = false;
	//m_lastErosionBlockCount = -1;
}

void TerritoryContainer::UpdateEffect(World* pworld)
{
	
	
	int blockdata = pworld->getBlockData(m_BlockPos);
	if ((8 & blockdata) || (4 & blockdata))
	{
		return;
	}
	int dir = (blockdata & 3);
	float yaw = 0;
	if (dir == 0)
	{
		yaw = -90;
	}
	else if (dir == 1)
	{
		yaw = 90;
	}
	else if (dir == 2)
	{
		yaw = 180;
	}
	else if (dir == 3)
	{
		yaw = 0;
	}
	int blockId = 0;

		BlockMaterial*metl = getBlockMtl();
		if (metl)
		{
			blockId = metl->getBlockResID();
		}
		

	//Ұ��ͼ�� ��Ҫ�����˿��Կ�
	if (blockId == 150001 && m_OwnerUin == 0)
	{
		int maxage = Rainbow::MAX_INT;
		bool issendclient = false;
		if (!pworld->isRemoteMode())
		{
			//������Ҫ���͵��ͻ� ���ͻ�����Ҫ���͵�����
			issendclient = true;
		}
		if (GetHaveAward())
		{
			pworld->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_01.prefab", getEffectPos(), issendclient);
			pworld->getEffectMgr()->playParticleEffectCommonAsync(2, "prefab/particles/wl_jitan_02.prefab", getEffectPos(), maxage, yaw, 0, issendclient, 50);
		}
		else
		{
			pworld->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_02.prefab", getEffectPos(), issendclient);
			pworld->getEffectMgr()->playParticleEffectCommonAsync(2, "prefab/particles/wl_jitan_01.prefab", getEffectPos(), maxage, yaw, 0, issendclient, 50);
		}
		return;
	}
	else if (GetWorldManagerPtr() && GetWorldManagerPtr()->getWorldInfoManager()->getVillagePoint(m_OwnerUin) != m_BlockPos)
	{
		return;
	}
	//��ͨͼ�� ֻ��Ҫ�Լ���
	if (g_pPlayerCtrl && m_OwnerUin == g_pPlayerCtrl->getUin())
	{
		int maxage = Rainbow::MAX_INT;
		if (GetHaveAward())
		{
			pworld->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_01.prefab", getEffectPos());
			pworld->getEffectMgr()->playParticleEffectCommonAsync(2, "prefab/particles/wl_jitan_02.prefab", getEffectPos(), maxage, yaw, 0, false, 50);
		}
		else
		{
			pworld->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_02.prefab", getEffectPos());
			pworld->getEffectMgr()->playParticleEffectCommonAsync(2, "prefab/particles/wl_jitan_01.prefab", getEffectPos(), maxage, yaw, 0, false, 50);
		}
	}
	return;
}

WCoord TerritoryContainer::getEffectPos()
{
	return BlockBottomCenter(m_BlockPos);
}

std::string TerritoryContainer::WorldIdToStr(WORLD_ID id)
{
	return std::to_string(id);
}

WORLD_ID TerritoryContainer::StrToWorldId(std::string str)
{
	return std::stoll(str);
}

std::map<int, int> TerritoryContainer::GetConsumedItemsByBlockId(int blockId) const
{
	if (blockId <= 0) {
		return std::map<int, int>(); // 返回空的映射
	}
	
	// 统一调用 DefManager 获取原始消耗材料
	std::map<int, int> originalConsumedItems = GetOriginalConsumedItems(blockId);
	
	if (originalConsumedItems.empty()) {
		return originalConsumedItems; // 没有消耗材料定义，直接返回
	}
	
	// 获取当前维护的方块数量
	size_t currentManagedBlockCount = GetManagedErosionBlockCount();
	
	// 查找对应的万分比
	int proportion = GetMaintenanceProportion();
	
	// 根据万分比调整消耗数量
	std::map<int, int> adjustedConsumedItems;
	for (const auto& item : originalConsumedItems) {
		int itemId = item.first;
		int originalCount = item.second;
		
		// 计算调整后的数量：原数量 * 万分比 / DEFAULT_PROPORTION
		int adjustedCount = (originalCount * proportion) / DEFAULT_PROPORTION;
		
		// 确保至少为1（如果原来不为0的话）
		if (originalCount > 0 && adjustedCount <= 0) {
			adjustedCount = 1;
		}
		
		adjustedConsumedItems[itemId] = adjustedCount;
	}
	
	return adjustedConsumedItems;
}

void TerritoryContainer::DrawLine(World* pworld, const WCoord& BlockPos,const WCoord& distBlockPos)
{
	Rainbow::Vector3f p1(0, 0, 0);
	Rainbow::Vector3f p2 = (BlockCenterCoord(distBlockPos) - BlockCenterCoord(BlockPos)).toVector3();
	Rainbow::Vector3f p0, p3;
	p0 = p1 * 2.0f - p2;
	Rainbow::Vector3f dir0;
	Rainbow::Vector3f camlookdir = g_pPlayerCtrl->getCamera()->getLookDir();
	p3 = p2 * 2.0f - p1;
	MINIW::CatmullRomCurve cc;
	cc.setControlPoints(p0, p1, p2, p3);
	cc.setNormals(camlookdir, camlookdir);
	pworld->getRender()->getCurveScreenRender()->addCurve(2, cc, BlockCenterCoord(BlockPos), BlockCenterCoord(distBlockPos), 6.0f, 3.0f);
}

void TerritoryContainer::SetHaveAward(bool award)
{
	m_haveAward = award;

	if (m_World == NULL) return;

	//����chunk����
	Chunk* pchunk = m_World->getChunk(m_BlockPos);
	if (pchunk)
	{
		pchunk->m_Dirty = true;
	}
}

void TerritoryContainer::ClearAllauthorizedPlayers()
{
	m_OwnerUin = 0; // 清除所有授权玩家时，重置领地所有者为0
	authorizedPlayers.clear();
	// 标记chunk为脏状态，以便保存
	if (m_World)
	{
		Chunk* pchunk = m_World->getChunk(m_BlockPos);
		if (pchunk)
		{
			pchunk->m_Dirty = true;
		}
	}
}

void TerritoryContainer::Addauthorized(unsigned int target_uin)
{
	// 检查是否已经在授权列表中
	for (size_t i = 0; i < authorizedPlayers.size(); i++)
	{
		if (authorizedPlayers[i] == target_uin)
		{
			return; // 用户已经在授权列表中，不需要重复添加
		}
	}
	
	// 添加到授权列表
	authorizedPlayers.push_back(target_uin);
	
	// 标记chunk为脏状态，以便保存
	if (m_World)
	{
		Chunk* pchunk = m_World->getChunk(m_BlockPos);
		if (pchunk)
		{
			pchunk->m_Dirty = true;
		}
	}
}

bool TerritoryContainer::IsAuthorized(unsigned int target_uin) const
{
	// 检查是否是领地拥有者
	if (m_OwnerUin == target_uin)
	{
		return true;
	}
	
	// 检查是否在授权列表中
	for (size_t i = 0; i < authorizedPlayers.size(); i++)
	{
		if (authorizedPlayers[i] == target_uin)
		{
			return true;
		}
	}
	
	return false;
}

bool TerritoryContainer::RemoveAuthorized(unsigned int target_uin)
{
	if (m_OwnerUin == target_uin)
	{
		m_OwnerUin = 0;
		return true;
	}
	// 查找并移除授权用户
	for (size_t i = 0; i < authorizedPlayers.size(); i++)
	{
		if (authorizedPlayers[i] == target_uin)
		{
			authorizedPlayers.erase(authorizedPlayers.begin() + i);
			
			// 标记chunk为脏状态，以便保存
			if (m_World)
			{
				Chunk* pchunk = m_World->getChunk(m_BlockPos);
				if (pchunk)
				{
					pchunk->m_Dirty = true;
				}
			}
			
			return true; // 成功移除
		}
	}
	
	return false; // 未找到该用户
}

std::vector<unsigned int> TerritoryContainer::GetAuthorizedPlayers() const
{
	return authorizedPlayers;
}

void TerritoryContainer::InitTrigger()
{
	if (!m_World)
	{
		return;
	}
}

// 新增领地边界盒相关方法实现
bool TerritoryContainer::IsPointInTerritoryBounds(const Rainbow::Vector3f& point) const
{
	return m_boundingBox.IsPointInside(point);
}

void TerritoryContainer::SetTerritoryBounds(const Rainbow::Vector3f& center, const Rainbow::Vector3f& halfExtents)
{
	m_boundingBox.SetFromCenterAndHalfExtents(center, halfExtents);
	
	// 更新碰撞器大小以匹配
	if (m_GameObject)
	{
		auto tf = m_GameObject->GetComponent<Rainbow::Transform>();
		tf->SetWorldPosition(center);
		tf->SetLocalScale(Rainbow::Vector3f(halfExtents.x * 2, halfExtents.y * 2, halfExtents.z * 2));
	}
}

TerritoryBoundingBox TerritoryContainer::GetTerritoryBounds() const
{
	return m_boundingBox;
}

void TerritoryContainer::ShowBoundingBox(bool show)
{
	m_showBoundingBox = show;
}

void TerritoryContainer::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (!pworld || !pworld->getContainerMgr()) return;
	auto container = pworld->getContainerMgr()->getContainer(blockpos);
	container->m_OwnerUin = player->getUin();

#ifdef BUILD_TEST
	// 仅服务端压力测试使用
	if (!pworld->isRemoteMode()) {
		if (GetClientInfoProxy()->getGameData("game_env") == 1)
		{
			// 根据用户uin预设不同的物品用于测试
			unsigned int playerUin = player->getUin();
			
			// 根据uin的最后几位数字来区分，这样可以测试不同的预设方案
			int uinMod = playerUin % 10;
			
			// 预设物品方案
			switch (uinMod)
			{
			case 0:
			case 1:
				// 方案1：基础建筑材料包
				{
					BackPackGrid* grid1 = index2Grid(STORAGE_START_INDEX + 0);
					if (grid1) grid1->setItem(1, 999); // 泥土 999个
					
					BackPackGrid* grid2 = index2Grid(STORAGE_START_INDEX + 1);
					if (grid2) grid2->setItem(4, 999); // 原木 999个
					
					BackPackGrid* grid3 = index2Grid(STORAGE_START_INDEX + 2);
					if (grid3) grid3->setItem(3, 999); // 石头 999个
				}
				break;
				
			case 2:
			case 3:
				// 方案2：高级材料包
				{
					BackPackGrid* grid1 = index2Grid(STORAGE_START_INDEX + 0);
					if (grid1) grid1->setItem(12, 500); // 铁锭 500个
					
					BackPackGrid* grid2 = index2Grid(STORAGE_START_INDEX + 1);
					if (grid2) grid2->setItem(13, 200); // 金锭 200个
					
					BackPackGrid* grid3 = index2Grid(STORAGE_START_INDEX + 2);
					if (grid3) grid3->setItem(14, 100); // 钻石 100个
				}
				break;
				
			case 4:
			case 5:
				// 方案3：工具包
				{
					BackPackGrid* grid1 = index2Grid(STORAGE_START_INDEX + 0);
					if (grid1) grid1->setItem(269, 1); // 木镐
					
					BackPackGrid* grid2 = index2Grid(STORAGE_START_INDEX + 1);
					if (grid2) grid2->setItem(256, 1); // 铁锹
					
					BackPackGrid* grid3 = index2Grid(STORAGE_START_INDEX + 2);
					if (grid3) grid3->setItem(258, 1); // 铁斧
				}
				break;
				
			case 6:
			case 7:
				// 方案4：食物包
				{
					BackPackGrid* grid1 = index2Grid(STORAGE_START_INDEX + 0);
					if (grid1) grid1->setItem(297, 64); // 面包 64个
					
					BackPackGrid* grid2 = index2Grid(STORAGE_START_INDEX + 1);
					if (grid2) grid2->setItem(364, 64); // 牛排 64个
					
					BackPackGrid* grid3 = index2Grid(STORAGE_START_INDEX + 2);
					if (grid3) grid3->setItem(322, 64); // 金苹果 64个
				}
				break;
				
			case 8:
			case 9:
				// 方案5：混合测试包
				{
					BackPackGrid* grid1 = index2Grid(STORAGE_START_INDEX + 0);
					if (grid1) grid1->setItem(1, 64); // 泥土 64个
					
					BackPackGrid* grid2 = index2Grid(STORAGE_START_INDEX + 1);
					if (grid2) grid2->setItem(12, 32); // 铁锭 32个
					
					BackPackGrid* grid3 = index2Grid(STORAGE_START_INDEX + 2);
					if (grid3) grid3->setItem(297, 16); // 面包 16个
					
					BackPackGrid* grid4 = index2Grid(STORAGE_START_INDEX + 3);
					if (grid4) grid4->setItem(269, 1); // 木镐 1个
				}
				break;
				
			default:
				// 默认：不添加任何物品
				break;
			}
		}
	}
#endif
}

bool TerritoryContainer::DeductMaterialByBlockId(int blockId)
{
	if (!m_World || blockId <= 0) {
		return false;
	}

	// 通过统一接口获取该方块的消耗材料
	std::map<int, int> consumedItems = GetConsumedItemsByBlockId(blockId);
	
	if (consumedItems.empty()) {
		return false; // 没有消耗材料定义
	}

	// 第一步：检查所有材料是否足够
	for (const auto& item : consumedItems) {
		int itemid = item.first;
		int requiredCount = item.second;
		
		if (itemid <= 0 || requiredCount <= 0) continue;
		
		// 计算当前拥有的该材料数量
		int availableCount = 0;
		int maxgrids = getGridCount();
		
		for (int i = 0; i < maxgrids; i++) {
			BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
			if (grid && grid->getItemID() == itemid) {
				availableCount += grid->getNum();
			}
		}
		
		if (availableCount < requiredCount) {
			return false; // 任何一种材料不足都返回失败
		}
	}

	// 第二步：执行扣除（此时保证能完全扣除所有材料）
	bool allSuccess = true;
	for (const auto& item : consumedItems) {
		int itemid = item.first;
		int requiredCount = item.second;
		
		if (itemid <= 0 || requiredCount <= 0) continue;
		
		// 使用安全的扣除方法
		if (!SafeRemoveItemByCount(itemid, requiredCount)) {
			allSuccess = false;
			break;
		}
	}

	return allSuccess;
}

int TerritoryContainer::GetAvailableMaterialCountByBlockId(int blockId)
{
	if (!m_World || blockId <= 0) {
		return 0;
	}

	// 通过统一接口获取该方块的消耗材料
	std::map<int, int> consumedItems = GetConsumedItemsByBlockId(blockId);
	
	if (consumedItems.empty()) {
		return 0; // 没有消耗材料定义
	}

	int minCycles = INT_MAX; // 使用最大值作为初始值
	
	// 检查每种材料可以支持多少次操作
	for (const auto& item : consumedItems) {
		int itemid = item.first;
		int requiredPerCycle = item.second;
		
		if (itemid <= 0 || requiredPerCycle <= 0) continue;
		
		// 计算当前拥有的该材料数量
		int availableCount = 0;
		int maxgrids = getGridCount();
		
		for (int i = 0; i < maxgrids; i++) {
			BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
			if (grid && grid->getItemID() == itemid) {
				availableCount += grid->getNum();
			}
		}
		
		// 计算该材料可以支持多少次操作
		int cyclesForThisMaterial = availableCount / requiredPerCycle;
		minCycles = std::min(minCycles, cyclesForThisMaterial);
	}
	
	// 如果没有找到任何有效材料，返回0
	return (minCycles == INT_MAX) ? 0 : minCycles;
}

bool TerritoryContainer::DeductMaterialBatchByBlockId(int blockId, int cycles)
{
	if (!m_World || blockId <= 0 || cycles <= 0) {
		return false;
	}

	// 通过统一接口获取该方块的消耗材料
	std::map<int, int> consumedItems = GetConsumedItemsByBlockId(blockId);
	
	if (consumedItems.empty()) {
		return false; // 没有消耗材料定义
	}

	// 第一步：检查所有材料是否足够支持指定次数的操作
	for (const auto& item : consumedItems) {
		int itemid = item.first;
		int requiredPerCycle = item.second;
		int totalRequired = requiredPerCycle * cycles;
		
		if (itemid <= 0 || requiredPerCycle <= 0) continue;
		
		// 计算当前拥有的该材料数量
		int availableCount = 0;
		int maxgrids = getGridCount();
		
		for (int i = 0; i < maxgrids; i++) {
			BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
			if (grid && grid->getItemID() == itemid) {
				availableCount += grid->getNum();
			}
		}
		
		if (availableCount < totalRequired) {
			return false; // 任何一种材料不足都返回失败
		}
	}

	// 第二步：执行批量扣除
	bool allSuccess = true;
	for (const auto& item : consumedItems) {
		int itemid = item.first;
		int requiredPerCycle = item.second;
		int totalRequired = requiredPerCycle * cycles;
		
		if (itemid <= 0 || requiredPerCycle <= 0) continue;
		
		// 使用安全的扣除方法
		if (!SafeRemoveItemByCount(itemid, totalRequired)) {
			allSuccess = false;
			break;
		}
	}

	return allSuccess;
}



// 安全的材料扣除方法
bool TerritoryContainer::SafeRemoveItemByCount(int itemid, int count)
{
	// 第一步：检查总数量是否足够
	int totalAvailable = 0;
	int maxgrids = getGridCount();
	
	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
		if (grid && grid->getItemID() == itemid)
		{
			totalAvailable += grid->getNum();
		}
	}
	
	if (totalAvailable < count)
	{
		return false; // 数量不足，不执行任何扣除
	}
	
	// 第二步：执行扣除（此时保证能完全扣除）
	int remainingToRemove = count;
	for (int i = 0; i < maxgrids && remainingToRemove > 0; i++)
	{
		BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
		if (grid && grid->getItemID() == itemid)
		{
			int removeFromThisGrid = std::min(remainingToRemove, grid->getNum());
			grid->addNum(-removeFromThisGrid);
			remainingToRemove -= removeFromThisGrid;
			
			// 通知格子变化
			afterChangeGrid(i + STORAGE_START_INDEX);
		}
	}
	
	return true; // 扣除成功
}

void TerritoryContainer::NotifyNearbyBlocksOnDestroy()
{
	if (!m_World) return;
	
	// 获取领地边界框
	TerritoryBoundingBox bounds = GetTerritoryBounds();
	Rainbow::Vector3f min = bounds.GetMin();
	Rainbow::Vector3f max = bounds.GetMax();
	
	// 转换为方块坐标并遍历
	int minX = static_cast<int>(std::floor(min.x / 100.0f));
	int minY = static_cast<int>(std::floor(min.y / 100.0f));
	int minZ = static_cast<int>(std::floor(min.z / 100.0f));
	int maxX = static_cast<int>(std::ceil(max.x / 100.0f));
	int maxY = static_cast<int>(std::ceil(max.y / 100.0f));
	int maxZ = static_cast<int>(std::ceil(max.z / 100.0f));
	
	for (int x = minX; x <= maxX; x++)
	{
		for (int y = minY; y <= maxY; y++)
		{
			for (int z = minZ; z <= maxZ; z++)
			{
				WCoord blockPos(x, y, z);
				
				// 跳过领地方块本身
				if (blockPos == m_BlockPos) continue;
				
				// 检查该位置是否真的在领地范围内
				Rainbow::Vector3f worldPos = blockPos.toVector3() * 100.0f;
				if (!bounds.IsPointInside(worldPos)) continue;
				
				// 获取该位置的容器并通知
				WorldContainer* container = m_World->getContainerMgr()->getContainer(blockPos);
				if (container)
				{
					ErosionContainer* erosionContainer = dynamic_cast<ErosionContainer*>(container);
					if (erosionContainer)
					{
						// 只有当这个领地确实是该方块当前绑定的领地时才通知销毁
						if (erosionContainer->m_TerritoryContainer == this)
						{
							erosionContainer->OnTerritoryDestroyed();
						}
					}
				}
			}
		}
	}
}

void TerritoryContainer::NotifyNearbyBlocksOnCreate()
{
	if (!m_World) return;
	
	// 获取领地边界框
	TerritoryBoundingBox bounds = GetTerritoryBounds();
	Rainbow::Vector3f min = bounds.GetMin();
	Rainbow::Vector3f max = bounds.GetMax();
	
	// 转换为方块坐标并遍历
	int minX = static_cast<int>(std::floor(min.x / 100.0f));
	int minY = static_cast<int>(std::floor(min.y / 100.0f));
	int minZ = static_cast<int>(std::floor(min.z / 100.0f));
	int maxX = static_cast<int>(std::ceil(max.x / 100.0f));
	int maxY = static_cast<int>(std::ceil(max.y / 100.0f));
	int maxZ = static_cast<int>(std::ceil(max.z / 100.0f));
	
	for (int x = minX; x <= maxX; x++)
	{
		for (int y = minY; y <= maxY; y++)
		{
			for (int z = minZ; z <= maxZ; z++)
			{
				WCoord blockPos(x, y, z);
				
				// 跳过领地方块本身
				if (blockPos == m_BlockPos) continue;
				
				// 检查该位置是否真的在领地范围内
				Rainbow::Vector3f worldPos = blockPos.toVector3() * 100.0f;
				if (!bounds.IsPointInside(worldPos)) continue;
				
				// 获取该位置的容器并通知
				WorldContainer* container = m_World->getContainerMgr()->getContainer(blockPos);
				if (container)
				{
					ErosionContainer* erosionContainer = dynamic_cast<ErosionContainer*>(container);
					if (erosionContainer)
					{
						erosionContainer->OnTerritoryCreated(this);
					}
				}
			}
		}
	}
}



// 高性能版本实现已内联到头文件中

void TerritoryContainer::AddManagedErosionBlock(ErosionContainer* erosionBlock)
{
	if (!erosionBlock) return;
	if(!erosionBlock->getIsNeedErosion()) //不需要腐蚀的
		return;
	
	// 检查是否已经在列表中
	auto it = std::find(m_managedErosionBlocks.begin(), m_managedErosionBlocks.end(), erosionBlock);
	if (it == m_managedErosionBlocks.end())
	{
		m_managedErosionBlocks.push_back(erosionBlock);
		
		// 使维护需求缓存失效
		InvalidateMaintenanceCache();
		
		// 立即通知UI更新
		if (m_World && !m_World->isRemoteMode())
		{
			m_lastErosionBlockCount = m_managedErosionBlocks.size();
			notifyChange2Openers(-1, true);
		}
	}
}

void TerritoryContainer::RemoveManagedErosionBlock(ErosionContainer* erosionBlock)
{
	if (!erosionBlock) return;
	
	// 从列表中移除
	auto it = std::find(m_managedErosionBlocks.begin(), m_managedErosionBlocks.end(), erosionBlock);
	if (it != m_managedErosionBlocks.end())
	{
		m_managedErosionBlocks.erase(it);
		
		// 使维护需求缓存失效
		InvalidateMaintenanceCache();
		
		// 立即通知UI更新
		if (m_World && !m_World->isRemoteMode())
		{
			m_lastErosionBlockCount = m_managedErosionBlocks.size();
			notifyChange2Openers(-1, true);
		}
	}
}

std::map<int, int> TerritoryContainer::Calculate24HourMaintenanceNeeds() const
{
	std::map<int, int> totalNeeds;
	
	if (!m_World) return totalNeeds;
	
	// 24小时的维护次数计算
	// 每分钟检查一次维护，24小时 = 24 * 60 = 1440分钟
	//const int HOURS_24_IN_MINUTES = 24 * 60;
	const int MAINTENANCE_CHECKS_PER_24H = 24;// HOURS_24_IN_MINUTES;
	
	// 遍历所有管理的腐蚀方块
	for (ErosionContainer* erosionBlock : m_managedErosionBlocks)
	{
		if (!erosionBlock) continue;
		
		// 通过统一接口获取该方块的消耗材料
		std::map<int, int> consumedItems = GetConsumedItemsByBlockId(erosionBlock->getBlockID());
		
		if (consumedItems.empty()) continue;
		
		// 计算24小时内该方块需要的总材料
		for (const auto& item : consumedItems) {
			int itemid = item.first;
			int countPerMaintenance = item.second;
			
			if (itemid <= 0 || countPerMaintenance <= 0) continue;
			
			// 计算24小时内该方块需要的总材料
			int totalForThisBlock = countPerMaintenance * MAINTENANCE_CHECKS_PER_24H;
			
			// 累加到总需求中
			totalNeeds[itemid] += totalForThisBlock;
		}
	}
	
	return totalNeeds;
}

bool TerritoryContainer::CanMaintain24Hours() 
{
	if (!m_World) return false;
	
	// 获取24小时维护需求
	std::map<int, int> needs = Calculate24HourMaintenanceNeeds();
	
	// 检查每种材料是否足够
	for (const auto& need : needs)
	{
		int itemid = need.first;
		int requiredCount = need.second;
		
		// 计算当前拥有的该材料数量
		int availableCount = 0;
		int maxgrids = getGridCount();
		
		for (int i = 0; i < maxgrids; i++)
		{
			BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
			if (grid && grid->getItemID() == itemid)
			{
				availableCount += grid->getNum();
			}
		}
		
		// 如果任何一种材料不足，返回false
		if (availableCount < requiredCount)
		{
			return false;
		}
	}
	
	return true;
}

std::vector<TerritoryContainer::MaintenanceInfo> TerritoryContainer::Get24HourMaintenanceInfo() 
{
	std::vector<MaintenanceInfo> infoList;
	
	if (!m_World) return infoList;
	
	// 获取24小时维护需求
	std::map<int, int> needs = Calculate24HourMaintenanceNeeds();
	
	// 为每种材料创建详细信息
	for (const auto& need : needs)
	{
		int itemid = need.first;
		int requiredCount = need.second;
		
		// 计算当前拥有的该材料数量
		int availableCount = 0;
		int maxgrids = getGridCount();
		
		for (int i = 0; i < maxgrids; i++)
		{
			BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
			if (grid && grid->getItemID() == itemid)
			{
				availableCount += grid->getNum();
			}
		}
		
		// 创建维护信息
		MaintenanceInfo info;
		info.itemid = itemid;
		info.required = requiredCount;
		info.available = availableCount;
		info.sufficient = (availableCount >= requiredCount);
		
		infoList.push_back(info);
	}
	
	return infoList;
}

int TerritoryContainer::GetMaintenanceProportion() const
{
	// 获取当前维护的方块数量
	size_t currentManagedBlockCount = GetManagedErosionBlockCount();
	
	// 默认万分比为DEFAULT_PROPORTION (100%)
	int proportion = DEFAULT_PROPORTION;
	
	// 从 BuildingMaintenanceCsv 表中查找对应的万分比
	// 通过 GetDefManagerProxy 获取所有配置记录
	auto allMaintenanceConfigs = GetDefManagerProxy()->getAllBuildingMaintenanceCsvDef();
	
	// 找到最接近且不超过当前数量的配置
	int bestMatchNum = 0;
	for (const auto* maintenanceDef : allMaintenanceConfigs) {
		if (!maintenanceDef) continue;
		
		// 找到最接近且不超过当前数量的配置
		if (maintenanceDef->num <= static_cast<int>(currentManagedBlockCount) && 
			maintenanceDef->num > bestMatchNum) {
			bestMatchNum = maintenanceDef->num;
			proportion = maintenanceDef->proportion;
		}
	}
	
	return proportion;
}

void TerritoryContainer::updateTerritory(World* pworld, const std::string& uuid, const unsigned int& OwnerUin, const WCoord& pos, std::vector<unsigned int> authorizedPlayers)
{
#ifdef IWORLD_SERVER_BUILD
	if (!pworld || !pworld->getContainerMgr() || !GetWorldManagerPtr())
	{
		return;
	}

	// 将授权用户列表转换为逗号分隔的字符串
	std::string allowedUinsStr;
	for (size_t i = 0; i < authorizedPlayers.size(); i++)
	{
		if (i > 0)
		{
			allowedUinsStr += ",";
		}
		allowedUinsStr += std::to_string(authorizedPlayers[i]);
	}

	// 获取领地边界框信息
	Rainbow::Vector3f areaMin = m_boundingBox.GetMin();
	Rainbow::Vector3f areaMax = m_boundingBox.GetMax();
	
	// 将边界框坐标转换为字符串格式
	std::string areaMinStr = std::to_string(areaMin.x) + "," + std::to_string(areaMin.y) + "," + std::to_string(areaMin.z);
	std::string areaMaxStr = std::to_string(areaMax.x) + "," + std::to_string(areaMax.y) + "," + std::to_string(areaMax.z);

	int uin = 1000;// 这是存储的数据 这里是1000
	WorldManager* worldMgr = GetWorldManagerPtr();
	long long owid = worldMgr->getWorldId();
	miniw::territory_permissions pb;
	pb.set_room_id(std::to_string(owid));
	pb.set_owner_uin(OwnerUin);
	pb.set_territory_id(uuid); // 从容器中获取territory_id
	pb.set_allowed_uins(allowedUinsStr); // 设置授权用户列表
	pb.set_position(std::to_string(pos.x) + "," + std::to_string(pos.y) + "," + std::to_string(pos.z));
	pb.set_area_min(areaMinStr); // 设置领地最小边界
	pb.set_area_max(areaMaxStr); // 设置领地最大边界
	pb.set_status("active");
	std::string reqData;
	pb.SerializeToString(&reqData);
	g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveTerritoryPermissions, reqData.c_str(), reqData.length(), owid, uin);
#endif
}

// 新增：解析MaintenanceProps字符串的辅助函数
std::map<int, int> TerritoryContainer::ParseMaintenancePropsString(const std::string& propsStr) const
{
	std::map<int, int> result;

	if (propsStr.empty()) {
		return result;
	}

	// 按逗号分割字符串
	size_t start = 0;
	size_t pos = 0;

	while ((pos = propsStr.find(',', start)) != std::string::npos) {
		std::string pair = propsStr.substr(start, pos - start);

		// 解析每一对 "itemid|count"
		size_t pipePos = pair.find('|');
		if (pipePos != std::string::npos) {
			try {
				int itemid = std::stoi(pair.substr(0, pipePos));
				int count = std::stoi(pair.substr(pipePos + 1));
				if (itemid > 0 && count > 0) {
					result[itemid] = count;
				}
			}
			catch (const std::exception&) {
				// 忽略解析错误的项
			}
		}

		start = pos + 1;
	}

	// 处理最后一对（或唯一的一对）
	if (start < propsStr.length()) {
		std::string pair = propsStr.substr(start);
		size_t pipePos = pair.find('|');
		if (pipePos != std::string::npos) {
			try {
				int itemid = std::stoi(pair.substr(0, pipePos));
				int count = std::stoi(pair.substr(pipePos + 1));
				if (itemid > 0 && count > 0) {
					result[itemid] = count;
				}
			}
			catch (const std::exception&) {
				// 忽略解析错误的项
			}
		}
	}

	return result;
}

// 新增：获取原始消耗材料的统一函数
std::map<int, int> TerritoryContainer::GetOriginalConsumedItems(int blockId) const
{
	if (blockId <= 0) {
		return std::map<int, int>(); // 返回空的映射
	}

	// 首先尝试从 DefManager 获取原始消耗材料
	std::map<int, int> originalConsumedItems = GetDefManagerProxy()->getConsumedItemsByProduceItemID(blockId);

	// 如果为空，则尝试从 ItemDef->MaintenanceProps 获取
	if (originalConsumedItems.empty()) {
		// 通过 DefManager 获取物品定义
		auto itemDef = GetDefManagerProxy()->getItemDef(blockId);
		if (itemDef && !itemDef->MaintenanceProps.empty()) {
			// 解析 MaintenanceProps 字符串
			originalConsumedItems = ParseMaintenancePropsString(itemDef->MaintenanceProps.c_str());
		}
	}

	return originalConsumedItems;
}