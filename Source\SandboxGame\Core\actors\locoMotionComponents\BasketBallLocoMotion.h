
#ifndef __BASKET_BALL_LOCOMOTION_H__
#define __BASKET_BALL_LOCOMOTION_H__

#include "ClientActor.h"
#include "OgreWorldPos.h"
//#include "OgreQuaternion.h"
#include "ActorLocoMotion.h"

namespace Rainbow
{
	class RigidDynamicActor;
}
class ClientPlayer;
class PlayerControl;
class ActorBasketBallComponent;
class BindActorComponent;

class BasketBallLocoMotion : public ActorLocoMotion //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(BasketBallLocoMotion)

	//tolua_begin
	BasketBallLocoMotion();
	//tolua_end
	virtual void prepareTick() override;
	virtual void tick() override;
	virtual void update(float dtime) override;
	virtual void updateBindActor() override;
	virtual void getRotation(Rainbow::Quaternionf &quat) override;
	virtual void doBlockCollision() override;
	virtual void doPickThrough(ClientActor *excludesactor = nullptr) override;
	virtual bool needFullRotation() override
	{
		return true;
	}
	virtual void setPosition(int x, int y, int z);
	//tolua_begin
	void attachPhysActor();
	void detachPhysActor();
	void attachPhysJoint(ClientPlayer *player);
	void detachPhysJoint();
	void checkPhysWorld();
	void updateBindActorInfo(bool bRefreshBallPos = false);
	void setUnbindPos();
	virtual void OnCollisionEnter(const Rainbow::EventContent* collision);
	Rainbow::RigidDynamicActor *m_PhysActor;
	MINIW::Joint *m_PhysJoint;

	Rainbow::WorldPos m_UpdatePos;
	Rainbow::Quaternionf m_UpdateRot;

	Rainbow::Quaternionf m_PrevRotateQuat;
	Rainbow::Quaternionf m_RotateQuat;

	Rainbow::Quaternionf m_ServerRot;
	WCoord m_ServerPos;
	int m_PosRotationIncrements;
	bool m_hasPhysActor;
	//tolua_end
}; //tolua_exports


#endif