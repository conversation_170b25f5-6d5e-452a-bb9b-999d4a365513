#include "ActorMovingLight.h"

#include "ClientActor.h"
#include "chunk.h"
#include "world.h"
#include "world_types.h"
#include "ActorLocoMotion.h"
#include "SandboxMacros.h"
#include <assert.h>
#include "Core/SandboxIdDef.h"

IMPLEMENT_COMPONENTCLASS(ActorMovingLight)

ActorMovingLight::ActorMovingLight()
	: m_Enable(false)
	, m_BlockPos(0, 0, 0)
{
	//m_ListenerLeaveWorld = SANDBOX_NEW(MNSandbox::ListenerFunction<bool>, [this](bool keepInChunk) -> void {
	//	this->OnLeaveWorld(keepInChunk);
	//});
}

ActorMovingLight::~ActorMovingLight()
{
	//SANDBOX_DELETE(m_ListenerLeaveWorld);
}

void ActorMovingLight::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void ActorMovingLight::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

void ActorMovingLight::OnTick()
{
	if (!m_Enable) return;

	if (!GetOwner()) return ;
	ClientActor* owner = GetOwner()->ToCast<ClientActor>();
	assert(owner != nullptr);

	if (owner == nullptr || owner->getWorld() == nullptr || owner->getLocoMotion() == nullptr || !owner->IsSectionDisplay())
	{
		return;
	}
	
	WCoord blockPos = CoordDivBlock(owner->getPosition());
	if (blockPos != m_BlockPos)
	{
		if (owner->IsOnViewFrustum()) 
		{
			World* world = owner->getWorld();
			world->setBlockLightEx(m_BlockPos, 0, false);
			world->setBlockLightEx(blockPos, 14, false);
			world->blockLightingChange(1, m_BlockPos);
			world->blockLightingChange(1, blockPos);
			m_BlockPos = blockPos;
		}
	}
}

void ActorMovingLight::SetEnable(bool value)
{
	m_Enable = value;
	if (!m_Enable) 
	{
		RemoveLightingPoint();
	}
}

void ActorMovingLight::OnLeaveWorld(bool keepInChunk)
{
	RemoveLightingPoint();
}

void ActorMovingLight::RemoveLightingPoint()
{
	if (!GetOwner()) return ;
	ClientActor* owner = GetOwner()->ToCast<ClientActor>();
	assert(owner != nullptr);
	if (!owner) return ;
	
	World* world = owner->getWorld();
	if (world != nullptr)
	{
		world->setBlockLightEx(m_BlockPos, 0);
	}
}

//void ActorMovingLight::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
//{
//	Super::OnEnterOwner(owner);
//	auto actor = GetOwnerActor();
//	actor->m_notifyLeaveWorld.Subscribe(*m_ListenerLeaveWorld);
//
//}
//
//void ActorMovingLight::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
//{
//	auto actor = GetOwnerActor();
//	actor->m_notifyLeaveWorld.Unsubscribe(*m_ListenerLeaveWorld);
//
//	Super::OnLeaveOwner(owner);
//}