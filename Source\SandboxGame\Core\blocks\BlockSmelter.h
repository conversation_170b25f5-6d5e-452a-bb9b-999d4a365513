
#ifndef __SMELTERBLOCKMATERIAL_H__
#define __SMELTERBLOCKMATERIAL_H__

#include "BlockMaterial.h"

class SmelterBlockMaterial : public MultiModelBlockMaterial //tolua_exports
{ //tolua_exports
	//typedef ModelBlockMaterial Super;
	DECLARE_BLOCKMATERIAL(SmelterBlockMaterial)

public:
	virtual ~SmelterBlockMaterial();
private:
	virtual void init(int resid) override;
	virtual void onPlayRandEffect(World *pworld, const WCoord &blockpos) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	//virtual bool hasContainer() override
	//{
	//	return true;
	//}
	//virtual WorldContainer *createContainer(World *pworld, const WCoord &blockpos) override;
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player);
	//virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
	//virtual bool canOutputQuantityEnergy()
	//{
	//	return true;
	//}
	virtual bool onMultiTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint, WorldContainer* container) override;
	virtual int getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player) override;
	virtual WorldContainer* createMultiContainer(World* pworld, const WCoord& blockpos) override;

	virtual int outputQuantityEnergy(World *pworld, const WCoord &blockpos, DirectionType dir);
	virtual int convertDataByRotate(int blockdata, int rotatetype) override;
	virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage) override;
	virtual int getBlockHP(World* pworld, const WCoord& blockpos) override;

private:
	RenderBlockMaterial *m_Mtls1[2];
public:
	DECLARE_BLOCKMATERIAL_INSTANCE_BEGIN(SmelterBlockMaterial)
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Dir, int)
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_CurHear, int)				// 燃料格子1当前温度
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_CurHear2, int)				// 燃料格子2当前温度
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_CurHear3, int)				// 燃料格子3当前温度
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_MaxHeat, int)				// 燃料格子1最大温度
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_MaxHeat2, int)				// 燃料格子2最大温度
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_MaxHeat3, int)				// 燃料格子3最大温度
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_MeltTicks, int)			// 燃料格子1燃烧多少时间
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_MeltTicks2, int)			// 燃料格子2燃烧多少时间
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_MeltTicks3, int)			// 燃料格子3燃烧多少时间
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_ProvideHeat, float)		// 燃料格子1提供热量（每个tick提供的热量）
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_ProvideHeat2, float)		// 燃料格子2提供热量（每个tick提供的热量）
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_ProvideHeat3, float)		// 燃料格子3提供热量（每个tick提供的热量）
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_CurMeltPro, float)			// 当前燃料格子1进度
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_CurMeltPro2, float)		// 当前燃料格子2进度
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_CurMeltPro3, float)		// 当前燃料格子3进度
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Quality, int)				// 熔炉品质，等于熔炉itemId
	DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Temperature, int)			// 温度挡位 ，1，2，3递增

		int GetCurHeat() const;
		int GetCurHeat2() const;
		int GetCurHeat3() const;

		void SetMaxHeat(const int& heat);
		int GetMaxHeat() const;
		void SetMaxHeat2(const int& heat);
		int GetMaxHeat2() const;
		void SetMaxHeat3(const int& heat);
		int GetMaxHeat3() const;

		float GetProvideHeat() const;
		float GetProvideHeat2() const;
		float GetProvideHeat3() const;

		float GetCurMeltPro() const;
		float GetCurMeltPro2() const;
		float GetCurMeltPro3() const;

		void SetQuality(const int& value);
		int GetQuality() const;
		void SetTemperature(const int& value);
		int GetTemperature() const;
	};
	virtual MNSandbox::AutoRef<BlockMaterial::BlockInstance> GetBlockInstance() override
	{
		return SmelterBlockMaterial::BlockInstance::NewInstance();
	}
}; //tolua_exports

#endif