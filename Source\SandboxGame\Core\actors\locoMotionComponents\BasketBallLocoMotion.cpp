#include "ActorBasketBall.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "PlayerStateController.h"
#include "world.h"
#include "ClientActorManager.h"
#include "LivingLocoMotion.h"
#include "BlockMaterialMgr.h"
#include "WorldManager.h"
#include "WorldRender.h"
#include "PlayerLocoMotion.h"
#include "EffectManager.h"
#include "special_blockid.h"

#include "MpActorManager.h"

#include "Entity/OgreEntity.h"
#include "Entity/OgreModel.h"
//#include "OgreGameScene.h"
#include "OgrePhysXManager.h"
//#include "OgreSceneManager.h"
#include "ObserverEventManager.h"
#include "GameCamera.h"
//#include "OgreRoot.h"
#include "ui_framemgr.h"


#include "BindActorComponent.h"
#include "BasketBallLocoMotion.h"
#include "BlockScene.h"
using namespace MINIW;
using namespace Rainbow;

extern Rainbow::UILib::FrameManager *g_pFrameMgr;

extern const float MOTION2VELOCITY;

IMPLEMENT_COMPONENTCLASS(BasketBallLocoMotion)

BasketBallLocoMotion::BasketBallLocoMotion() : m_PhysActor(NULL), m_PhysJoint(NULL), m_hasPhysActor(false)
	,m_PosRotationIncrements(0)
{
	
}

void BasketBallLocoMotion::attachPhysActor()
{
	if (m_pWorld->isRemoteMode()) return;

	if(m_PhysActor == NULL)
	{
		Rainbow::Vector3f pos = m_Position.toVector3() + Rainbow::Vector3f(0.0f, (float)m_yOffset, 0.0f);
		float mass = g_WorldMgr->m_SurviveGameConfig->basketballConfig.phys_mass*g_WorldMgr->m_SurviveGameConfig->physxconfig.mass_scale;
		m_PhysActor = m_pWorld->m_PhysScene->AddRigidDynamicActor(pos, Rainbow::Quaternionf::identity, m_BoundHeight/2.0f, g_WorldMgr->m_SurviveGameConfig->basketballConfig.phys_static_friciton, g_WorldMgr->m_SurviveGameConfig->basketballConfig.phys_dynamic_friciton, g_WorldMgr->m_SurviveGameConfig->basketballConfig.phys_restitution, mass, false, getOwnerActor());

		m_PhysActor->SetLinearDamping(g_WorldMgr->m_SurviveGameConfig->basketballConfig.phys_linear_damping);
		if(g_WorldMgr->m_SurviveGameConfig->basketballConfig.phys_Angular_damping >= 0)
			m_PhysActor->SetAngularDamping(g_WorldMgr->m_SurviveGameConfig->basketballConfig.phys_Angular_damping);

		m_PhysActor->SetLinearVelocity(m_Motion * MOTION2VELOCITY);
		if (m_pWorld->getScene())
			m_pWorld->getScene()->AddGameObject(m_PhysActor->GetGameObject());
		m_hasPhysActor = true;

		m_PhysActor->GetGameObject()->AddEvent(Evt_CollisionEnter, &BasketBallLocoMotion::OnCollisionEnter, this);
	}
}

void BasketBallLocoMotion::OnCollisionEnter(const Rainbow::EventContent* collision)
{
	ClientActor* ownerActor = getOwnerActor();
	if (ownerActor)
	{
		ActorBasketBall* ball = static_cast<ActorBasketBall*>(ownerActor);
		ball->playSoundByPhysCollision();
	}
}

void BasketBallLocoMotion::detachPhysActor()
{
	if (m_pWorld->isRemoteMode()) return;

	if(m_PhysActor)
	{
		m_pWorld->m_PhysScene->DeleteRigidActor(m_PhysActor);
		m_PhysActor = NULL;

		m_hasPhysActor = false;
	}
}

void BasketBallLocoMotion::attachPhysJoint(ClientPlayer *player)
{
	if (m_pWorld->isRemoteMode()) return;

	PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *>(player->getLocoMotion());

	Rainbow::Quaternionf rot;
	//rot.setEulerAngle(0.0f, 0.0f, 0.0f);
	rot = AngleEulerToQuaternionf(Vector3f(0.0f, 0.0f, 0.0f));
	int offsetY = (loc->m_BoundHeight - m_BoundHeight) / 2 - 5;
	m_PhysJoint = m_pWorld->m_PhysScene->CreateFixJoint(loc->m_PhysActor, Vector3f(0, 0, 0), rot, m_PhysActor, Vector3f(0, (float)offsetY, (float)130), Quaternionf::identity);
}

void BasketBallLocoMotion::detachPhysJoint()
{
	if (m_pWorld->isRemoteMode()) return;

	if (m_PhysJoint)
	{
		m_pWorld->m_PhysScene->DeleteJoint(m_PhysJoint);
		m_PhysJoint = NULL;
	}
}

void BasketBallLocoMotion::prepareTick()
{
	ActorLocoMotion::prepareTick();

	m_PrevRotateQuat = m_RotateQuat;
}

void BasketBallLocoMotion::tick()
{
	ActorLocoMotion::tick();
	ClientPlayer *player = nullptr;
	auto bindAComponent = getOwnerActor()->m_pBindActorComponent;
	if (bindAComponent)
	{
		player = dynamic_cast<ClientPlayer *>(bindAComponent->getTarget());
	}
	if (player) 
	{
		m_ServerPos = m_Position; 
	   	return;
	}

	if (m_pWorld->isRemoteMode())
	{
		if(m_PosRotationIncrements > 0)
		{
			m_Position = m_Position + (m_ServerPos - m_Position)/m_PosRotationIncrements;
			m_RotateQuat = Slerp(m_RotateQuat, m_ServerRot, 1.0f/m_PosRotationIncrements);

			Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, -m_RotateQuat.GetAxisZ());

			m_PosRotationIncrements--;
		}
		return;
	}

	if(m_PhysActor)
	{
		Rainbow::Vector3f pos;
		Rainbow::Quaternionf quat;
		m_PhysActor->GetPos(pos, quat);

		m_Motion = pos - m_Position.toVector3();
		m_Position = pos;
		m_RotateQuat = quat;
		Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, -m_RotateQuat.GetAxisZ());

		checkPhysWorld();
	}
	//else
	//{
	//	m_Motion *= 0.98f;
	//	if(Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
	//	//if(Abs(m_Motion.y) < 0.5f) m_Motion.y = 0;
	//	if(Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;

	//	doMoveStep(m_Motion);

}

void BasketBallLocoMotion::update(float dtime)
{
	ActorLocoMotion::update(dtime);
	updateBindActorInfo();

	m_UpdateRot=  Slerp(m_PrevRotateQuat, m_RotateQuat, m_TickPosition.m_TickOffsetTime/GAME_TICK_TIME);
	m_UpdatePos = getFramePosition();
}

void BasketBallLocoMotion::getRotation(Rainbow::Quaternionf &quat)
{
	quat = m_RotateQuat;
}

void BasketBallLocoMotion::doBlockCollision()
{
	if (m_pWorld == NULL) return;
	if (getOwnerActor() == NULL) return;
	if (m_pWorld->isRemoteMode()) return;
	if (getOwnerActor()->isDead()) return;

	CollideAABB box;
	getCollideBox(box);
	box.expand(-1, -1, -1);

	WCoord mingrid = CoordDivBlock(box.minPos());
	WCoord maxgrid = CoordDivBlock(box.maxPos());

	World *pworld = getOwnerActor()->getWorld();
	if (nullptr != pworld && pworld->checkChunksExist(mingrid, maxgrid))
	{
		std::vector<WCoord> wcoordv;
		std::vector<int> blockidv;
		Rainbow::HashTable<WCoord, int, WCoordHashCoder> check_woord(16);
		for (int x = mingrid.x; x <= maxgrid.x; x++)
		{
			for (int y = mingrid.y; y <= maxgrid.y; y++)
			{
				for (int z = mingrid.z; z <= maxgrid.z; z++)
				{
					int blockid = pworld->getBlockID(x, y, z);
					if (blockid > 0)
					{
						if (m_PhysActor) {
							auto pBall = dynamic_cast<ActorBasketBall*>(getOwnerActor());
							if(pBall && pBall->isHitShoot())
								m_PhysActor->SetLinearDamping(g_WorldMgr->m_SurviveGameConfig->basketballConfig.phys_linear_damping);
						}

						auto material = g_BlockMtlMgr.getMaterial(blockid);
						WCoord wcoordcollide(x, y, z);
						if (BLOCK_PHYSXCOLLIDER == blockid)
						{
							if (check_woord.find(wcoordcollide) == NULL && material
								&& material->DoOnActorCollidedWithBlock( pworld, wcoordcollide, getOwnerActor()))
							{
								std::list<WCoord> wcoordlist;
								wcoordlist.push_back(wcoordcollide);
								check_woord[wcoordcollide] = 1;
								while (wcoordlist.size())
								{
									WCoord postemp = *wcoordlist.begin();
									wcoordlist.pop_front();
									for (int i = 0; i < 6; i++)
									{
										WCoord coord = NeighborCoord(postemp, i);
										if (check_woord.find(coord) == NULL)
										{
											int NeighborBlockID = getOwnerActor()->getWorld()->getBlockID(coord);
											if (NeighborBlockID == BLOCK_PHYSXCOLLIDER)
											{
												check_woord[coord] = NeighborBlockID;
												wcoordlist.push_back(coord);
											}
										}
									}
								}
							}
						}
						else if(material)
						{
							if (blockid == BLOCK_BASKETFRAME) {
								ActorBasketBall* pBall = dynamic_cast<ActorBasketBall*>(getOwnerActor());
								if (pBall && pBall->checkShootBasketBall(wcoordcollide)) {
									material->DoOnActorCollidedWithBlock( pworld, wcoordcollide, getOwnerActor());
								}
							}
							else if (material->DoOnActorCollidedWithBlock( pworld, wcoordcollide, getOwnerActor()))
							{
								check_woord[wcoordcollide] = blockid;
							}
						}
					}
				}
			}
		}
		getOwnerActor()->clearCollideBlock();
		Rainbow::HashTable<WCoord, int, WCoordHashCoder>::Element *ele = check_woord.iterate(NULL);
		while (ele)
		{
			getOwnerActor()->setCollideBlockState(ele->key, ele->value);
			ele = check_woord.iterate(ele);
		}
	}

	if (m_OnGround && !getOwnerActor()->getFlying())
	{
		WCoord down_pos = CoordDivBlock(m_Position) + WCoord(0, -1, 0);
		BlockMaterial *mtl = m_pWorld->getBlockMaterial(down_pos);
		if (mtl)
		{
			mtl->DoOnActorWalking( m_pWorld, down_pos, getOwnerActor());
		}
	}
}

void BasketBallLocoMotion::doPickThrough(ClientActor *excludesactor/* =nullptr */)
{
	if (m_pWorld->isRemoteMode()) return;

	WCoord mvec = getIntegerMotion(m_Motion);
	if (m_OnGround && mvec.y < 0)
		mvec.y = 0;
	if (mvec.length() < 100)
	{
		return;
	}

	MINIW::WorldRay ray;
	ray.m_Origin = m_Position.toWorldPos();
	ray.m_Dir = mvec.toVector3();
	ray.m_Range = ray.m_Dir.Length();
	ray.m_Dir /= ray.m_Range;

	ActorExcludes excludes;
	excludes.addActor(getOwnerActor());
	if (excludesactor)
		excludes.addActor(excludesactor);

	IntersectResult presult;
	WorldPickResult intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
	if (intertype == WorldPickResult::BLOCK) //block
	{
		int blockID = m_pWorld->getBlockID(presult.block);

		WCoord pos = CoordDivBlock(m_Position + mvec);

		if (presult.block != pos)
		{
			if (blockID == BLOCK_COLLIDER || blockID == BLOCK_MOBCOLLIDER || blockID == BLOCK_BALLCOLLIDER)
			{
				g_BlockMtlMgr.getMaterial(blockID)->DoOnActorCollidedWithBlock( getOwnerActor()->getWorld(), presult.block, getOwnerActor());
			}
		}
	}
	else if (intertype == WorldPickResult::ACTOR) //actor
	{
		WCoord pos = (m_Position + mvec) / BLOCK_SIZE;
		if (presult.actor->getPosition() != pos)
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(presult.actor);
			if (player)
			{
				getOwnerActor()->onCollideWithPlayer(player);
			}
			else
			{
				getOwnerActor()->collideWithActor(presult.actor->GetActor());
			}
		}
	}
}

void BasketBallLocoMotion::checkPhysWorld()
{
	WCoord curpos = m_Position;
	WCoord range(SECTION_SIZE, SECTION_SIZE, SECTION_SIZE);
	WCoord minpos = CoordDivSection(curpos-range);
	WCoord maxpos = CoordDivSection(curpos+range);

	std::vector<WCoord> checkphy;
	for(int y=minpos.y; y<=maxpos.y; y++)
	{
		for(int z=minpos.z; z<=maxpos.z; z++)
		{
			for(int x=minpos.x; x<=maxpos.x; x++)
			{
				//m_pWorld->updateSectionPhysics(x, y, z);
				checkphy.push_back(WCoord(x, y, z));
			}
		}
	}

	for (int i = 0; i<(int)m_preCheckPhy.size(); i++)
	{
		 int j = 0;
		 for (; j<(int)checkphy.size(); j++)
		 {
			if (m_preCheckPhy[i] == checkphy[j])
			{
				break;
			}
		 }
		 if (j == checkphy.size())
		 {
			m_pWorld->updateLeaveSectionPhysics(m_preCheckPhy[i].x, m_preCheckPhy[i].y, m_preCheckPhy[i].z);
		 }
	}

	for (int i = 0; i<(int)checkphy.size(); i++)
	{
		 int j = 0;
		 for (; j<(int)m_preCheckPhy.size(); j++)
		 {
			if (checkphy[i] == m_preCheckPhy[j])
			{
				break;
			}
		 }
		 if (j == m_preCheckPhy.size())
		 {
			m_pWorld->updateEnterSectionPhysics(checkphy[i].x, checkphy[i].y, checkphy[i].z);
		 }
		 m_pWorld->updateSectionPhysics(checkphy[i].x, checkphy[i].y, checkphy[i].z);
	}
	m_preCheckPhy = checkphy;
}

void BasketBallLocoMotion::updateBindActor()
{
	ClientPlayer *player = nullptr;
	auto bindAComponent = getOwnerActor()->m_pBindActorComponent;
	if (bindAComponent)
	{
		player = dynamic_cast<ClientPlayer *>(bindAComponent->getTarget());
	}
	if (player) {
		if (player->getCurOperate() == PLAYEROP_BASKETBALL_DRIBBLERUN || player->getCurOperate() == PLAYEROP_BASKETBALL_DRIBBLERUN_END){return;}

		Rainbow::Vector3f dir = Yaw2FowardDir(player->getLocoMotion()->m_RotateYaw + g_WorldMgr->m_SurviveGameConfig->basketballConfig.dribbing_offset_angle);
		WCoord pos1 = player->getPosition();
		float distance = player->getLocoMotion()->m_BoundSize + BLOCK_SIZE*g_WorldMgr->m_SurviveGameConfig->basketballConfig.dribbing_offset_xz_fac;
		WCoord pos = pos1 + WCoord(dir*distance);
		pos.y += m_yOffset;
		int blockid = m_pWorld->getBlockID(CoordDivBlock(pos));
		BlockMaterial* material =  m_pWorld->getBlockMaterial(CoordDivBlock(pos));
		if (blockid > 0 && material && material->canBlocksMovement( m_pWorld, CoordDivBlock(pos)))
		{
			pos = pos1;
			pos.y += m_yOffset;
		}

		setPosition(pos.x, pos.y, pos.z);
	}
	else if (m_PhysActor) {
		auto pBall = dynamic_cast<ActorBasketBall*>(getOwnerActor());
		if (pBall)
			pBall->doPassFollow();
	}
}

void BasketBallLocoMotion::setUnbindPos()
{
	ClientPlayer *player = nullptr;
	auto bindAComponent = getOwnerActor()->m_pBindActorComponent;
	if (bindAComponent)
	{
		player = dynamic_cast<ClientPlayer *>(bindAComponent->getTarget());
	}
	if (player)
	{
		WCoord pos1 = player->getPosition();
		Rainbow::Vector3f dir1 = Yaw2FowardDir(player->getLocoMotion()->m_RotateYaw);
		float distance = player->getLocoMotion()->m_BoundSize + BLOCK_SIZE*0.3f;
		WCoord pos = pos1 + WCoord(dir1*(distance));

		pos.y += m_yOffset + m_BoundHeight;
		setPosition(pos.x, pos.y, pos.z);
		m_ServerPos = m_Position;
	}
}

void BasketBallLocoMotion::updateBindActorInfo(bool bRefreshBallPos)
{
	ClientPlayer *player = nullptr;
	if (getOwnerActor() == nullptr) return;
	auto bindAComponent = getOwnerActor()->m_pBindActorComponent;
	if (bindAComponent)
	{
		player = dynamic_cast<ClientPlayer *>(bindAComponent->getTarget());
	}
	if (player) {
		if (player->getCurOperate() != PLAYEROP_BASKETBALL_DRIBBLERUN && player->getCurOperate() != PLAYEROP_BASKETBALL_DRIBBLERUN_END && !bRefreshBallPos) { return; }

		Rainbow::Vector3f dir = Yaw2FowardDir(player->getLocoMotion()->m_RotateYaw + g_WorldMgr->m_SurviveGameConfig->basketballConfig.dribbing_offset_angle);
		WCoord pos1 = player->getPosition();
		float distance = player->getLocoMotion()->m_BoundSize + BLOCK_SIZE*g_WorldMgr->m_SurviveGameConfig->basketballConfig.dribbing_offset_xz_fac;
		WCoord pos = pos1 + WCoord(dir*(distance));
		pos.y += m_yOffset;
		int blockid = m_pWorld->getBlockID(CoordDivBlock(pos));
		BlockMaterial* material = m_pWorld->getBlockMaterial(CoordDivBlock(pos));
		if (blockid > 0 && material && material->canBlocksMovement(m_pWorld, CoordDivBlock(pos)))
		{
			pos = pos1;
			pos.y += m_yOffset;
		}
		
		if (bRefreshBallPos) {
			Rainbow::Vector3f dir1 = Yaw2FowardDir(player->getLocoMotion()->m_RotateYaw);
			distance = player->getLocoMotion()->m_BoundSize + BLOCK_SIZE*0.3f;
			pos = pos1 + WCoord(dir1*(distance));

			pos.y += m_yOffset+m_BoundHeight;
		}

		setPosition(pos.x, pos.y, pos.z);
	}
}

void BasketBallLocoMotion::setPosition(int x, int y, int z)
{
	m_OldPosition = m_Position;
	m_Position.x = x;
	m_Position.y = y;
	m_Position.z = z;
	if (m_PhysActor)
		m_PhysActor->SetPos(Rainbow::Vector3f((float)x, (float)y, (float)z), m_RotateQuat);
}
