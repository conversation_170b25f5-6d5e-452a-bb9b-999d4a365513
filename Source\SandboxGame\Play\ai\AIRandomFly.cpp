#include "AIRandomFly.h"
#include "ClientMob.h"
#include "LivingLocoMotion.h"
#include "world.h"
#include "ClientPlayer.h"
#include "ActorBody.h"
#include "SandboxIdDef.h"

AIRandomFly::AIRandomFly(ClientMob *pActor, int interval, int maxheight, int maxrange, int runprob): m_nMaxHeight(maxheight), m_nMaxRange(maxrange), m_iRunProb(runprob)
{
	setMutexBits(3);
	m_pMobActor = pActor;
	m_SwitchMinInterval = interval;
	livingLocomotion = dynamic_cast<LivingLocoMotion *>(m_pMobActor->getLocoMotion());
	if (livingLocomotion->getLocoMotionType() != MoveAbilityType::FlyLoc)
	{
		LOG_INFO("AIRandomFly can only be applied on Fly mob");
	}
	m_bInit = false;
	m_iRunTick = 0;
}

AIRandomFly::~AIRandomFly()
{

}

bool AIRandomFly::willRun()
{
	if (!livingLocomotion)
		return false;
	m_iRunTick++;
	bool willRunSucc = false;

	if(livingLocomotion && livingLocomotion->getLocoMotionType() == FlyLoc)//(m_pMobActor->m_Def->Type == MOB_FLY)
	{//code-by:hanyunqiang ���Ӽ��ݣ����Ͳ��Ƿ������͵��Ǿ���flyLocomotionͬ������ʹ�ø�ai
		if(!m_bInit && m_nMaxRange)
		{
			m_InitPos = livingLocomotion->m_Position;
			m_pMobActor->setHome(m_nMaxRange, m_InitPos.x, m_InitPos.y + 100, m_InitPos.z);
			m_bInit = true;
		}

		willRunSucc = true;
	}
	else
	{
		willRunSucc = false;
	}

	// ſ�ڵ����޷��й�
	if (m_pMobActor->getFallGround()) return false;
	
	if (m_iRunProb >= 100) return willRunSucc;

	// �����Դ���,���������ôƵ�� code-by:lizb
	if (m_iRunTick % 10 == 0)
	{
		// ��������Ǵ��ڷ���״̬ ��ȡ������ִ��
		if (m_pMobActor->getTamed())
		{
			ClientPlayer *player = m_pMobActor->getTamedOwner();
			if (player != NULL && player->isFlying())
			{
				return willRunSucc;
			}
		}

		if (GenRandomInt(1, 100) <= m_iRunProb)
		{
			return willRunSucc;
		}
	}

	return false;
}

bool AIRandomFly::continueRun()
{
	return true;
}

/*bool AIRandomFly::checkOutRange(WCoord& src, WCoord& target)
{
	WCoord pos = m_InitPos;
	WCoord pos1 = flyLocomotion->getPosition();
	double vecx = pos.x - pos1.x;
	double vecz = pos.z - pos1.z;
	if((vecx*vecx+vecz*vecz) > m_nMaxRange*m_nMaxRange)
	{
		return true;
	}
	return false;
}*/

void AIRandomFly::start()
{
	m_StartAIMark = 0;
	livingLocomotion->m_HasTarget = true;
	livingLocomotion->m_MoveTarget = m_ValidPos;
	livingLocomotion->setBehaviorOn(BehaviorType::Wander);

	// playAnim�������ͻ�ʱ����ClientActor�� code-by:lizb
	ActorBody *pActorBody = m_pMobActor->getBody();
	if (pActorBody == NULL) return;
	
	int defID = m_pMobActor->getDefID();
	if ((defID >= 3885 && defID <= 3890) || defID == 3255) // ����
	{
		if (!pActorBody->hasAnimPlaying(SEQ_WALK))
		{
			//m_pMobActor->playAnim(SEQ_WALK);
			// ʹ�������������;����Ŀͻ����Ի�ȡ����ǰ����Ķ���
			pActorBody->setCurAnim(SEQ_WALK, 0);
		}
	}
	//ѱ����ȸݺ code-by:�����
	if (defID == MONSTER_WARBLER_TAMED)
	{
		if (!pActorBody->hasAnimPlaying(SEQ_RUN))
		{
			m_pMobActor->playAnim(SEQ_RUN);
		}
	}
}

void AIRandomFly::reset()
{
	m_StartAIMark = 0;
	livingLocomotion->setBehaviorOff(BehaviorType::Wander);

	//ѱ����ȸݺ code-by:�����
	if (m_pMobActor->getDefID() == MONSTER_WARBLER_TAMED)
	{
		m_pMobActor->playAnim(SEQ_WALK);
	}
}

void AIRandomFly::update()
{
	++m_StartAIMark;
	if (GenRandomInt(0, 20) < 5)
	{
		if (m_StartAIMark > m_SwitchMinInterval)
		{
			WCoord mobPos = m_pMobActor->getPosition();
			if (m_pMobActor->isInHomeDist(mobPos.x, mobPos.y ,mobPos.z) && m_pMobActor->getLocoMotion()->findRandTargetBlock(m_ValidPos, 8, 4, NULL))
			{
				WCoord m_CheckPos = CoordDivBlock(m_ValidPos);
				int maxheight = m_nMaxHeight;
				while(maxheight--)
				{
					m_CheckPos.y--;
					if(m_pMobActor->getWorld()->getBlockID(m_CheckPos) != 0)
					{
						livingLocomotion->m_HasTarget = true;
						livingLocomotion->m_MoveTarget = m_ValidPos;
						m_StartAIMark = 0;
						break;
					}
				}
			}
			else
			{
				livingLocomotion->m_HasTarget = true;
				livingLocomotion->m_MoveTarget = m_InitPos;
				m_StartAIMark = 0;
			}
		}
	}
}
