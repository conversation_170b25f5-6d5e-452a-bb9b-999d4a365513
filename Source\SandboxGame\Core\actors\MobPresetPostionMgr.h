#pragma once

#include "world_types.h"
#include "Core/display/worlddisplay/SceneEffectLine.h"
#include <vector>
#include <unordered_map>

// 怪物预设位置信息
struct MobPresetPosition
{
    WCoord pos;         // 位置坐标
    bool isUsed;        // 是否已被使用
    int lastUsedDay;    // 最后使用的游戏天数
    
    MobPresetPosition() : pos(0, -1, 0), isUsed(false), lastUsedDay(-1) {}
    MobPresetPosition(const WCoord& position) : pos(position), isUsed(false), lastUsedDay(-1) {}
};

// 怪物预设位置管理器
class MobPresetPostionMgr
{
public:
    MobPresetPostionMgr();
    ~MobPresetPostionMgr();
    
    // 初始化预设位置点
    void Init(int minX, int maxX, int minZ, int maxZ, int pointCount = 500);
    
    // 获取所有预设位置点
    const std::vector<MobPresetPosition>& GetAllPositions() const { return m_positions; }
    
    // 获取可用的预设位置点
    std::vector<int> GetAvailablePositions() const;
    
    // 标记位置点为已使用
    void SetPositionUsed(int index, bool used, int gameDay = -1);
    
    // 根据位置坐标查找索引
    int FindPositionIndex(const WCoord& pos) const;
    
    // 重置所有位置为可用状态
    void ResetAllPositions();
    
    // 调试可视化方法
    void DrawDebugLines(World* pWorld, bool enabled = true);
    
    // 绘制调试线条（每帧调用）
    void Draw(World* pWorld);
    
    // 清理调试线条
    void ClearDebugLines();
    
    // 为刷新系统添加的新方法
    
    // 获取附近的可用刷新位置
    WCoord GetNearestAvailablePosition(const WCoord& centerPos, int maxDistance = 1000) const;
    
    // 获取随机的可用刷新位置
    WCoord GetRandomAvailablePosition() const;
    
    // 获取在指定范围内的可用位置列表
    std::vector<int> GetAvailablePositionsInRange(const WCoord& centerPos, int maxDistance) const;
    
    // 根据游戏天数重置过期的位置
    void ResetExpiredPositions(int currentGameDay, int expireDays = 1);

    // 根据chunk坐标查找预设点
    WCoord GetPresetPointInChunk(int chunkX, int chunkZ);

    // 获取可用预设点的数量
    int GetAvailablePositionCount() const;
    
    // 获取总预设点数量
    int GetTotalPositionCount() const;

private:
    // 生成随机但相对均匀分布的点
    void GenerateUniformPoints(int minX, int maxX, int minZ, int maxZ, int pointCount);
    
    // 检查两点之间的距离
    float GetDistance2D(const WCoord& pos1, const WCoord& pos2) const;
    
private:
    std::vector<MobPresetPosition> m_positions;
    std::vector<SceneEffectLine*> m_debugLines;
    bool m_debugEnabled;
    
    // 泊松圆盘采样相关参数
    static const float MIN_DISTANCE_RATIO;
    static const int MAX_ATTEMPTS;
}; 