#pragma once
#include "PlayerState.h"

class BuildBlockRepairState : public PlayerState  //tolua_exports
{ //tolua_exports
public:
    //tolua_begin
    BuildBlockRepairState(PlayerControl* host);
    virtual ~BuildBlockRepairState();
    virtual void doBeforeEntering() override;
    virtual std::string update(float dtime) override;
    virtual void doBeforeLeaving() override;
    virtual void OnTick(float elapse) final;
    //tolua_end

private:
    int m_RepairStartMark;
    int m_RepairItemDuration;
    int m_PlayRepairTicks;
    //int m_RepairMark;
    bool m_RepairComplete;
};  //tolua_exports 