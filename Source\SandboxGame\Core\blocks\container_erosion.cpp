#include "container_erosion.h"
#include "LuaInterfaceProxy.h"
#include "SandboxIdDef.h"
#include "world.h"
#include "Text3D/ProgressBar3D.h"
#include <WorldManager.h>
#include "BlockScene.h"
#include <BlockMaterialMgr.h>
#include "TerritoryManager.h"
#include "container_territory.h"
#include "Platforms/PlatformInterface.h"
static int Period = 60; //s
static int tickPeriod = 60*20;
//tickPeriod 60s 一个周期，减一次血
ErosionContainer::ErosionContainer(int baseindex):WorldContainer(baseindex),
    m_Hp(100), m_BlockID(0), tickCounter(0), m_MaxHp(100), m_HPProgressObj(NULL),
    m_MaintenanceProtectionTicks(0), m_OfflineTime(0), m_NeedComputOfflineResult(false),
    m_PendingTerritoryReevaluation(false), m_ReevaluationDelayTicks(0), is_need_erosion(true)
{
	m_NeedTick = true;
   // s_MaxErosionLevel = GetLuaInterfaceProxy().get_lua_const()->erosionMaxLevel;
}

ErosionContainer::ErosionContainer(const WCoord& blockpos,const int blockId, int baseindex)
    :WorldContainer(blockpos, baseindex), m_Hp(100), m_BlockID(blockId), tickCounter(0), m_MaxHp(100)
    , m_HPProgressObj(NULL), m_MaintenanceProtectionTicks(0), m_OfflineTime(0), m_NeedComputOfflineResult(false),
    m_PendingTerritoryReevaluation(false), m_ReevaluationDelayTicks(0), is_need_erosion(true)
{
	m_NeedTick = true;
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(m_BlockID);
	m_MaxHp = def->MaxHP;
	m_Hp = m_MaxHp;
   // s_MaxErosionLevel = GetLuaInterfaceProxy().get_lua_const()->erosionMaxLevel;
	//	//查找领地柜的位置
	//TerritoryContainer* terr = TerritoryManager::GetInstance()->GetTerritoryContainingBlock(m_BlockPos);
	//m_TerritoryContainer = terr;
	//m_TerritoryBlockPos = terr->m_BlockPos;
}

ErosionContainer::~ErosionContainer()
{
    DESTORY_GAMEOBJECT_BY_COMPOENT(m_HPProgressObj);
}

void ErosionContainer::enterWorld(World* pworld)
{
    WorldContainer::enterWorld(pworld);
	const BlockDef * def = GetDefManagerProxy()->getBlockDef(m_BlockID);
	m_MaxHp = def->MaxHP;
    bool bCreateMap = pworld->isSOCCreateMap();
    if (bCreateMap)//创造模式  在生成地图 不要腐蚀
        is_need_erosion = false; 
    registerUpdateTick();
	auto point = m_BlockPos.toVector3() * 100.0f; // BLOCK_SIZE = 100
	TerritoryContainer* terr = TerritoryManager::GetInstance()->GetTerritoryContainingBlock(point);
	m_TerritoryContainer = terr;
	if(m_TerritoryContainer)
	{
		m_TerritoryBlockPos = terr->m_BlockPos;
		// 添加到领地的管理列表
		terr->AddManagedErosionBlock(this);
	}
	else
	{
		// 领地柜不存在时的处理
		OnNoTerritoryProtection();
	}
    auto pMtl = g_BlockMtlMgr.getMaterial(m_BlockID);
    pMtl->ShowBlockCrack(pworld, m_BlockPos);
#ifndef IWORLD_SERVER_BUILD		
	//建筑的血条
	//if (m_HPProgressObj == NULL)
	//{
	//	// UNDONE 
	//	m_HPProgressObj =Rainbow::ProgressBarIn3D::Create("img_blood_strip_board_b.png", "img_blood_white.png");
	//	m_HPProgressObj->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
	//	float dist = GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.billboard_scale_dist;
	//	m_HPProgressObj->setBillboardScaleDist(dist);
	//	m_HPProgressObj->setTextBillboardScale(true, 0, dist);
	//	if (pworld)
	//	{
	//		m_HPProgressObj->AttachToScene(pworld->getScene());
	//	}
 //       m_HPProgressObj->setVale(m_Hp, m_MaxHp);
 //       m_HPProgressObj->SetPosition(m_BlockPos.toVector3()*100.0f +Vector3f(50,250,50));
	//}
#endif	
}

void ErosionContainer::leaveWorld()
{
    // 从领地管理列表中移除自己
    if (m_TerritoryContainer)
    {
        m_TerritoryContainer->RemoveManagedErosionBlock(this);
    }
    
    WorldContainer::leaveWorld();
	if (m_HPProgressObj)
	{
		m_HPProgressObj->DetachFromScene();
	}
}

void ErosionContainer::updateTick()
{
	if (!is_need_erosion)// 如果不需要腐蚀，直接返回 
        return;
    // 新增：处理离线计算
    if (m_NeedComputOfflineResult) {
        m_NeedComputOfflineResult = false;
        ComputOfflineResult();
    }

    // 新增：处理延时的领地重新评估
    if (m_PendingTerritoryReevaluation) {
        m_ReevaluationDelayTicks--;
        if (m_ReevaluationDelayTicks <= 0) {
            m_PendingTerritoryReevaluation = false;
            // 延时后重新评估领地保护
            ReevaluateTerritoryProtection();
            
            // 如果没有找到新的领地保护，应用额外的处理
            if (!m_TerritoryContainer) {
                OnNoTerritoryProtection();
            }
        }
    }

	if (m_World->isRemoteMode()) 
        return;
	
	// 增加tick计数器
	tickCounter++;
	
	// 如果在维护保护期内，减少保护期计数器但不进行腐蚀检查
	if (m_MaintenanceProtectionTicks > 0) {
		m_MaintenanceProtectionTicks--;
		tickCounter = 0; // 重置计数器，保护期内不累积
		return;
	}

	// 当达到设定的tick周期时进行维护检查和可能的腐蚀
	if (tickCounter >= EROSION_CHECK_PERIOD) {
        ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
        float erosion = 0; 
		if (pLuaConst) //pLuaConst 可能为nullptr，确保安全访问 
            erosion = pLuaConst->K_BUILD_BLOCK_EROSION;
		assert(erosion < 0.0f);
		
		bool maintenanceSuccessful = false;
		
		if (m_TerritoryContainer) {
            bool b = m_TerritoryContainer->DeductMaterialByBlockId(m_BlockID);
            if (b) {
                // 维护成功，进入保护期
                maintenanceSuccessful = true;
                m_MaintenanceProtectionTicks = PROTECTION_PERIOD_TICKS;
            }
		}
		
		// 如果维护失败，则扣除生命值
		if (!maintenanceSuccessful) {
			addHp(erosion);
            if (m_Hp <= 0) destroy();
		}
		
		tickCounter = 0; // 重置计数器
	}
}

flatbuffers::Offset<FBSave::ChunkContainer> ErosionContainer::save(SAVE_BUFFER_BUILDER& builder)
{
    //auto basedata = saveContainerCommon(builder);
    //auto actor = FBSave::CreateContainerErosion(builder, basedata, m_Hp,m_BlockID);
	auto actor = saveContainerErosion(builder);

    return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerErosion, actor.Union());
}



flatbuffers::Offset<FBSave::ContainerErosion> ErosionContainer::saveContainerErosion(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);
	// 保存时更新离线时间戳
	m_OfflineTime = MINIW::GetTimeStamp();
	// 注意：延时重新评估相关的状态不需要保存，因为它们是临时状态
	return FBSave::CreateContainerErosion(builder, basedata, m_Hp, m_BlockID, m_MaintenanceProtectionTicks, m_OfflineTime, is_need_erosion);
}

bool ErosionContainer::load(const void* srcdata)
{
    loadErosionContainer(srcdata);
 //   auto src = reinterpret_cast<const FBSave::ContainerErosion*>(srcdata);
 //   if (!src)
 //       return false;

 //   loadContainerCommon(src->basedata());
 //   m_Hp = src->hp();
	//m_BlockID = src->blockid();
	//const BlockDef * def = GetDefManagerProxy()->getBlockDef(m_BlockID);
	//m_MaxHp = def->MaxHP;
#ifndef IWORLD_SERVER_BUILD		
	if (m_HPProgressObj)
	{
		m_HPProgressObj->setVale(m_Hp, m_MaxHp);
	}
#endif

    return true;
}

bool ErosionContainer::loadErosionContainer(const void* srcdata)
{
    auto src = reinterpret_cast<const FBSave::ContainerErosion*>(srcdata);
    if (!src)
        return false;

    loadContainerCommon(src->basedata());
    m_Hp = src->hp();
    m_BlockID = src->blockid();
    const BlockDef* def = GetDefManagerProxy()->getBlockDef(m_BlockID);
    m_MaxHp = def->MaxHP;
    // 加载维护保护期数据（如果存在）
    m_MaintenanceProtectionTicks = src->maintenance_protection_ticks();
    
    // 新增：加载离线时间并设置需要计算离线结果的标志
    m_OfflineTime = src->offline_time();
    is_need_erosion = src->is_need_erosion();
    int offlineTimeDiff = MINIW::GetTimeStamp() - m_OfflineTime;
    if (offlineTimeDiff > 0) {
        m_NeedComputOfflineResult = true;
    }
    
    return true;
}

void ErosionContainer::destroy()
{
    m_World->setBlockAir(m_BlockPos);
    int blockid = m_World->getBlockID(m_BlockPos);
    BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
    if (m_World->CheckBlockSettingEnable(pmtl, ENABLE_DESTROYED) != 0)
    {
        m_World->getEffectMgr()->playBlockDestroyEffect(0, m_BlockPos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2), DIR_NEG_X, 40);
    }
}

void ErosionContainer::addHp(float hp)
{
    m_Hp += hp;
	if (m_Hp > m_MaxHp)
		m_Hp = m_MaxHp;
#ifndef IWORLD_SERVER_BUILD		
    if (m_HPProgressObj)
    {
		m_HPProgressObj->setVale(m_Hp, m_MaxHp);
    }
    m_World->addHarmedBlock(m_BlockPos);
#endif
    
    if (m_Hp > 0.0f)
    {
        int blockid = m_World->getBlockID(m_BlockPos);
        int data = m_World->getBlockData(m_BlockPos);
       // m_World->setBlockAll(m_BlockPos,blockid, data,3,true);//网络同步
        m_World->markBlockForUpdate(m_BlockPos);
    }
}

int ErosionContainer::getHp() const
{
	return std::ceil(m_Hp);
	//int hp = std::floor(m_Hp); // Changed from std::ceil to std::floor
	//hp = m_MaxHp - hp;//显示要反过来
	//return hp;
}

void ErosionContainer::OnTerritoryCreated(TerritoryContainer* newTerritory)
{
    if (!newTerritory) return;
    
    // 如果当前没有领地保护，直接绑定到新的领地
    if (!m_TerritoryContainer)
    {
        m_TerritoryContainer = newTerritory;
        m_TerritoryBlockPos = newTerritory->m_BlockPos;
        
        // 添加到新领地的管理列表
        newTerritory->AddManagedErosionBlock(this);
        
        // 获得领地保护后的处理
        tickCounter = 0;
        // 不自动给予保护期，需要通过维护材料获得
        
        return;
    }
    
    // 如果已经有领地保护，重新评估最佳领地
    ReevaluateTerritoryProtection();
}

void ErosionContainer::OnTerritoryDestroyed()
{
    // 如果被销毁的不是当前绑定的领地，不需要处理
    // 这个检查应该在调用方进行，这里假设已经确认是当前绑定的领地
    
    // 从原领地的管理列表中移除自己
    if (m_TerritoryContainer)
    {
        m_TerritoryContainer->RemoveManagedErosionBlock(this);
    }
    
    // 清除当前领地引用
    m_TerritoryContainer = nullptr;
    m_TerritoryBlockPos = WCoord(0, -128, 0);
    
    // 延时处理领地重新评估，避免时序问题
    // 当领地柜被销毁时，可能还没有从TerritoryManager中移除
    // 延时几个tick再重新评估，确保领地已经完全从系统中移除
    m_PendingTerritoryReevaluation = true;
    m_ReevaluationDelayTicks = REEVALUATION_DELAY_TICKS;
}

void ErosionContainer::OnNoTerritoryProtection()
{
    // 没有领地保护时的处理逻辑
    m_TerritoryContainer = nullptr;
    m_TerritoryBlockPos = WCoord(0, -128, 0);
    
    // 重置tick计数器，立即开始腐蚀计时
    tickCounter = 0;
    
    // 清除任何现有的维护保护期
    m_MaintenanceProtectionTicks = 0;
    
    // 可以在这里添加其他没有领地保护时的特殊处理
    // 例如：发送通知给玩家、记录日志等
}

void ErosionContainer::ReevaluateTerritoryProtection()
{
    if (!m_World) return;
    
    // 使用TerritoryManager查找最佳领地
    TerritoryContainer* bestTerritory = SelectBestTerritory();
    
    // 如果找到了更好的领地，或者当前没有领地保护
    if (bestTerritory && bestTerritory != m_TerritoryContainer)
    {
        TerritoryContainer* oldTerritory = m_TerritoryContainer;
        
        // 从原领地的管理列表中移除
        if (oldTerritory)
        {
            oldTerritory->RemoveManagedErosionBlock(this);
        }
        
        // 更新到新的领地
        m_TerritoryContainer = bestTerritory;
        m_TerritoryBlockPos = bestTerritory->m_BlockPos;
        
        // 添加到新领地的管理列表
        bestTerritory->AddManagedErosionBlock(this);
        
        // 如果是从无保护状态获得保护，给予奖励
        if (!oldTerritory)
        {
            //addHp(3.0f);
            tickCounter = 0;
        }
    }
    else if (!bestTerritory)
    {
        // 没有找到任何可用的领地保护
        OnNoTerritoryProtection();
    }
}

TerritoryContainer* ErosionContainer::SelectBestTerritory() const
{
    if (!m_World) return nullptr;
    
    // 获取当前位置的世界坐标
    Rainbow::Vector3f currentPos = m_BlockPos.toVector3() * 100.0f; // 假设BLOCK_SIZE = 100
    
    // 获取所有包含当前位置的领地
    std::vector<TerritoryContainer*> availableTerritories = 
        TerritoryManager::GetInstance()->GetTerritoriesContainingPoint(currentPos);
    
    if (availableTerritories.empty())
        return nullptr;
    
    // 如果只有一个领地，直接返回
    if (availableTerritories.size() == 1)
        return availableTerritories[0];
    
    // 多个领地时，选择最佳的
    TerritoryContainer* bestTerritory = nullptr;
    float bestScore = -1.0f;
    
    for (TerritoryContainer* territory : availableTerritories)
    {
        // 安全检查：确保领地指针有效且不是之前绑定的即将被销毁的领地
        if (!territory || !territory->m_World) {
            continue; // 跳过无效的领地
        }
        
        float score = CalculateTerritoryScore(territory);
        if (score > bestScore)
        {
            bestScore = score;
            bestTerritory = territory;
        }
    }
    
    return bestTerritory;
}

float ErosionContainer::CalculateTerritoryScore(TerritoryContainer* territory) const
{
    if (!territory) return 0.0f;
    
    float score = 0.0f;
    
    // 1. 距离因子（距离越近分数越高）
    WCoord distance = m_BlockPos - territory->m_BlockPos;
    float distanceScore = 1000.0f / (1.0f + distance.lengthSquared());
    score += distanceScore;
    
    // 2. 领地资源因子（资源越多分数越高）
    // 假设TerritoryContainer有获取资源数量的方法
    // int materialCount = territory->GetMaterialCount(11323); // 木材数量
    // float resourceScore = std::min(materialCount * 0.1f, 50.0f);
    // score += resourceScore;
    
    // 3. 领地等级因子（如果有等级系统）
    // int territoryLevel = territory->GetLevel();
    // float levelScore = territoryLevel * 10.0f;
    // score += levelScore;
    
    // 4. 所有权因子（自己的领地优先级更高）
    // if (territory->GetOwnerUin() == GetOwnerUin()) // 需要ErosionContainer也有所有者概念
    // {
    //     score += 100.0f;
    // }
    
    // 5. 领地大小因子（更大的领地可能更稳定）
    TerritoryBoundingBox bounds = territory->GetTerritoryBounds();
    Rainbow::Vector3f extent = bounds.GetHalfExtents();
    float sizeScore = (extent.x + extent.y + extent.z) * 0.01f;
    score += sizeScore;
    
    return score;
}

void ErosionContainer::ComputOfflineResult()
{
    if (m_World->isRemoteMode()) return;
    
    int offlineTimeDiff = MINIW::GetTimeStamp() - m_OfflineTime;
    if (offlineTimeDiff <= 0) return;
    
    // offlineTimeDiff 是毫秒，转换为秒
    int offlineSeconds = offlineTimeDiff / 1000;
    if (offlineSeconds <= 0) return;
    
    // 限制最大离线时间计算，避免性能问题（比如最多计算30分钟的离线时间）
    const int MAX_OFFLINE_MINUTES = 30;
    const int MAX_OFFLINE_SECONDS = MAX_OFFLINE_MINUTES * 60;
    
    if (offlineSeconds > MAX_OFFLINE_SECONDS) {
        offlineSeconds = MAX_OFFLINE_SECONDS;
    }
    
    // 计算离线期间应该执行的tick数（每秒20个tick）
    int offlineTickCount = offlineSeconds * 20;
    
    // 如果在维护保护期内，直接减少保护期时间
    if (m_MaintenanceProtectionTicks > 0) {
        m_MaintenanceProtectionTicks -= offlineTickCount;
        if (m_MaintenanceProtectionTicks < 0) {
            // 保护期结束后的剩余tick数
            int remainingTicks = -m_MaintenanceProtectionTicks;
            m_MaintenanceProtectionTicks = 0;
            
            // 计算保护期结束后的腐蚀
            ComputOfflineErosion(remainingTicks);
        }
        return;
    }
    
    // 没有保护期，直接计算腐蚀
    ComputOfflineErosion(offlineTickCount);
}

void ErosionContainer::ComputOfflineErosion(int tickCount)
{
    // 计算在离线期间会发生多少次腐蚀检查
    int erosionCycles = (tickCounter + tickCount) / EROSION_CHECK_PERIOD;
    
    if (erosionCycles <= 0) {
        // 更新tick计数器但不触发腐蚀
        tickCounter = (tickCounter + tickCount) % EROSION_CHECK_PERIOD;
        return;
    }
    
    ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
    float erosion = 0;
    if (pLuaConst) {
        erosion = pLuaConst->K_BUILD_BLOCK_EROSION;
    }
    
    // 优化：如果没有领地容器，直接计算总伤害，避免循环
    if (!m_TerritoryContainer) {
        float totalDamage = erosion * erosionCycles;
        addHp(totalDamage);
        if (m_Hp <= 0) destroy();
        tickCounter = (tickCounter + tickCount) % EROSION_CHECK_PERIOD;
        return;
    }
    
    // 有领地容器时，需要逐个检查维护材料
    // 优化：批量检查维护材料，减少函数调用次数
    int availableMaintenance = m_TerritoryContainer->GetAvailableMaterialCountByBlockId(m_BlockID);
    int successfulMaintenances = std::min(erosionCycles, availableMaintenance);
    
    if (successfulMaintenances > 0) {
        // 批量扣除维护材料
        m_TerritoryContainer->DeductMaterialBatchByBlockId(m_BlockID, successfulMaintenances);
        
        // 如果有成功的维护，最后一次维护会给予保护期
        if (successfulMaintenances == erosionCycles) {
            // 所有周期都成功维护，给予保护期
            m_MaintenanceProtectionTicks = PROTECTION_PERIOD_TICKS;
        } else {
            // 部分维护成功，计算剩余的腐蚀伤害
            int failedCycles = erosionCycles - successfulMaintenances;
            float totalDamage = erosion * failedCycles;
            addHp(totalDamage);
            if (m_Hp <= 0) destroy();
            
            // 最后一次成功维护给予保护期
            m_MaintenanceProtectionTicks = PROTECTION_PERIOD_TICKS;
        }
    } else {
        // 没有维护材料，计算总腐蚀伤害
        float totalDamage = erosion * erosionCycles;
        addHp(totalDamage);
        if (m_Hp <= 0) destroy();
    }
    
    // 更新剩余的tick计数器
    tickCounter = (tickCounter + tickCount) % EROSION_CHECK_PERIOD;
}
