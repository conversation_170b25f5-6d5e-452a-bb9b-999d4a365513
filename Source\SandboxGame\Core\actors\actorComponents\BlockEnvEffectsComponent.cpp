#include "BlockEnvEffectsComponent.h"

#include "BlockRangeCheckBase.h"
#include "BlockEnvDef.h"


IMPLEMENT_COMPONENTCLASS(BlockEnvEffectsComponent)



BlockEnvEffectsComponent::BlockEnvEffectsComponent(){


}

BlockEnvEffectsComponent::~BlockEnvEffectsComponent(){
	for(auto it = m_ranges.begin(); it != m_ranges.end(); it++){
		SANDBOX_DELETE(it->second);
	}
	m_ranges.clear();

	for(auto it = m_effects.begin(); it != m_effects.end(); it++){
		SANDBOX_DELETE(it->second);
	}	
	m_effects.clear();
}

bool BlockEnvEffectsComponent::Add(const std::string &rangeName,const std::string &effectName, const std::string &name){

	auto it = m_ranges.find(rangeName);
	if(it == m_ranges.end())
		return false;

	auto effectIt = m_effects.find(effectName);
	if(effectIt == m_effects.end())
		return false;

	//BlockEnvST st(name);
	//st.pRange  = it->second;
	//st.pEffect = effectIt->second;


	ActiveBlockEnvST activeSt;
	activeSt.blockEnvSt.Name = name;
	activeSt.blockEnvSt.pRange = it->second; 
	activeSt.blockEnvSt.pEffect = effectIt->second;
	activeSt.bActive = false;

	m_members[name] = activeSt;//.push_back(st);
	return true;
}

void BlockEnvEffectsComponent::OnTick(){
	if (!GetOwner()) return;
	ClientActor* owner = GetOwner()->ToCast<ClientActor>();
	if (!owner) return;
	if (owner->needClear())
		return;

	//m_status.clear();
	//for(unsigned int i = 0; i < m_members.size();i++){
	//	BlockEnvST &e = m_members[i].;
	//	if(e.execute(owner)){
	//		m_status.insert(e.Name);
	//	}		
	//}
	for (auto& iter : m_members)
	{
		iter.second.bActive = false;
		BlockEnvST& e = iter.second.blockEnvSt;
		if (e.execute(owner)) {
			iter.second.bActive = true;
		}
	}
}

bool BlockEnvEffectsComponent::GetStatus(const std::string& name) const{
	auto iter = m_members.find(name);
	if (iter != m_members.end())
	{
		return iter->second.bActive;
	}
	//if(iter != m_status.end()){ 
	//	return true;
	//}
	return false;
}


void BlockEnvEffectsComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	BindOnTick();
}

void BlockEnvEffectsComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::OnLeaveOwner(owner);
}

/////////////////////////////////////////////////////////////////////////