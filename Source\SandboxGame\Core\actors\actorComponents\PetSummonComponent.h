#ifndef __PET_SUMMON_COMPONENT_H__
#define __PET_SUMMON_COMPONENT_H__

#include <string>
#include "ActorComponent_Base.h"

class ClientPlayer;
struct SummonPetInfomation;

class PetSummonComponent : public ActorComponentBase
{
	struct SummonInfo {
		int monsterid;
		int petid;
		int quality;
		int stage;
		bool summoned;
		std::string serverid;
		std::string petName;
		SummonInfo(){
			summoned = true;
			monsterid = 0;
		}
		void setSummoned(){summoned = true;}
		bool isSummoned(){return summoned;}
		void newPet(int _monsterid, const std::string &_serverid, int _petid, int _stage, int _quality, const std::string &_petName)
		{
			monsterid = _monsterid;
			serverid = _serverid;
			petid = _petid;
			stage = _stage;
			quality = _quality;
			petName = _petName;
			summoned = false;
		}
	};
public:
	DECLARE_COMPONENTCLASS(PetSummonComponent)

	PetSummonComponent();
	virtual ~PetSummonComponent();

	void onLeaveWorld(bool keep_inchunk);
	virtual void summonPet(int monsterid, const std::string &serverid, int petid, int stage, int quality, const std::string &petName = "");

	const std::string& getCurSummonPetID();
	const SummonPetInfomation& getCurSummonPetInfo();
	void setCurSummonPetID(const std::string& petId);
	void setCurSummonPetInfo(int monsterid, int petid, int stage, int quality);
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);	
protected:
	ClientPlayer* m_owner;
	std::string             m_CurSummonPetID; //当前召唤的宠物ID
	SummonPetInfomation*     m_pCurSummonPetInfo;
private:
	SummonInfo				m_SummoningPetInfo;  // 待生成的宠物信息
};

class MPPetSummonComponent : public PetSummonComponent
{
public:
	DECLARE_COMPONENTCLASS(MPPetSummonComponent)

	MPPetSummonComponent();
	virtual void summonPet(int monsterid, const std::string &serverid, int petid, int stage, int quality, const std::string &petName = "") override;
};

#endif
