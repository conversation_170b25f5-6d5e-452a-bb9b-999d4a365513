#include "GunShipHelicopter.h"
#include "ClientMob.h"
#include "WorldManager.h"

#include <algorithm>
#include <random>
#include <cmath>

GunShipHelicopter::GunShipHelicopter(const Rainbow::Vector3f& spawnPosition)
    : m_position(spawnPosition)
    , m_health(0.0)
    , m_monster_id(3421)
{
    // 初始化武器系统
    
    mob_actor = nullptr;


    LOG_INFO("GunShipHelicopter::Constructor: Helicopter spawned at (%.2f, %.2f, %.2f)", 
             m_position.x, m_position.y, m_position.z);


}

GunShipHelicopter::~GunShipHelicopter() {
    if (mob_actor) {
        delete mob_actor;
    }

}


void GunShipHelicopter::Init() {

    if (g_WorldMgr && g_WorldMgr->getWorld(0)) {
        
        auto pWorld = g_WorldMgr->getWorld(0);

        ClientActorMgr *actormgr = static_cast<ClientActorMgr*>(pWorld->getActorMgr());

        if (actormgr) {
            mob_actor = actormgr->spawnMob(m_position, m_monster_id, false, false);
            if (mob_actor) {
                //mob_actor->init(m_monster_id);
                
                mob_actor->playSaySound();
            }
        }        
    }
 
}

void GunShipHelicopter::Update(float deltaTime) {
    if (IsDestroyed()) {
        return;
    }
    
    // // 更新武器冷却
    // UpdateWeaponCooldowns(deltaTime);
    
    // // 更新移动逻辑
    // UpdateMovement(deltaTime);
    
    // // 更新旋转
    // UpdateRotation(deltaTime);
    
    // // 更新音效
    // UpdateSoundEffects(deltaTime);
    
    // // 更新视觉效果
    // UpdateVisualEffects(deltaTime);
    
    // // 更新世界位置
    // UpdateWorldPosition();
}

void GunShipHelicopter::Destroy() {
    LOG_INFO("GunShipHelicopter::Destroy: Helicopter destroyed");
    
    m_health = 0.0f;
    
    // // 播放爆炸效果
    // PlayVisualEffect("helicopter_explosion", m_position);
    // PlaySoundEffect("helicopter_explosion");
    
    // // 从世界移除
    // RemoveFromWorld();
}

bool GunShipHelicopter::IsDestroyed() {
    return false;
}

Rainbow::Vector3f GunShipHelicopter::GetCurrentPosition(){
    return m_position;
}