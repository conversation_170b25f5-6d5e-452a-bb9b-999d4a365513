#include "PushSnowBallLocomotion.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "PlayerStateController.h"
#include "world.h"
#include "ClientActorManager.h"
#include "LivingLocoMotion.h"
#include "BlockMaterialMgr.h"
#include "WorldManager.h"
#include "WorldRender.h"
#include "PlayerLocoMotion.h"
#include "EffectManager.h"
#include "special_blockid.h"
#include "MpActorManager.h"
#include "Entity/OgreEntity.h"
#include "Entity/OgreModel.h"
#include "OgrePhysXManager.h"
#include "ObserverEventManager.h"
#include "BindActorComponent.h"
#include "BlockScene.h"
#include "ActorPushSnowBall.h"
#include "Physics/MessageParameters.h"
#include "LuaInterfaceProxy.h"

using namespace MINIW;
using namespace Rainbow;
IMPLEMENT_COMPONENTCLASS(PushSnowBallLocomotion)

inline bool isLiquidBlock(int blockid)
{
	/*
		1. 水：id = 3，4
		2. 岩浆：id = 5，6
		3. 蜂蜜：id = 11、12
		4. 流沙：13、14
		5. 剧毒液：17、18
	*/

	if (blockid > 2 && blockid < 7 || blockid > 10 && blockid < 15 || blockid == 17 || blockid == 18)
	{
		return true;
	}
	return false;
}

inline bool isHighTemperatureBlock(const BlockDef* def)
{
	if (def && def->TempSrc > 0)
	{
		return true;
	}
	return false;
}

inline bool isSnowBlock(int blockid)
{
	if (blockid == 115 || blockid == 3501 || blockid == 3508 || blockid == 3513 || blockid == 3520 || blockid == 3525 || blockid == 3530 || blockid == 3535 || blockid == 3540)
	{
		return true;
	}
	return false;
}

PushSnowBallLocomotion::PushSnowBallLocomotion() : m_PhysActor(NULL), m_PhysJoint(NULL), m_hasPhysActor(false), m_OldBlockPos(0, -10000, 0)
{
	m_PosRotationIncrements = 0;
	m_Speed = 0.0f;
}

void PushSnowBallLocomotion::attachPhysActor()
{
	if (m_pWorld->isRemoteMode()) return;

	if(m_PhysActor == NULL)
	{
		Rainbow::Vector3f pos = m_Position.toVector3() + Rainbow::Vector3f(0.0f, (float)0.0f, 0.0f);
		float mass = g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_mass*g_WorldMgr->m_SurviveGameConfig->physxconfig.mass_scale;
		m_PhysActor = m_pWorld->m_PhysScene->AddRigidDynamicActor(pos, Rainbow::Quaternionf::identity, m_BoundHeight/2.0f, g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_static_friciton, g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_dynamic_friciton, g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_restitution, mass, false, getOwnerActor());
		if (m_pWorld->getScene())
			m_pWorld->getScene()->AddGameObject(m_PhysActor->GetGameObject());

		m_PhysActor->SetLinearDamping(g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_linear_damping);
		if(g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_Angular_damping >= 0)
			m_PhysActor->SetAngularDamping(g_WorldMgr->m_SurviveGameConfig->ballconfig.phys_Angular_damping);

		m_PhysActor->SetLinearVelocity(m_Motion * MOTION2VELOCITY);
		m_hasPhysActor = true;
		m_PhysActor->GetGameObject()->AddEvent(Rainbow::Evt_CollisionEnter, &PushSnowBallLocomotion::OnCollisionEnter, this);
		//m_PhysActor->GetGameObject()->AddEvent(Rainbow::Evt_CollisionExit, &PushSnowBallLocomotion::OnCollisionExit, this);
	}
}


void PushSnowBallLocomotion::OnCollisionEnter(const Rainbow::EventContent* eventContent)
{
	ClientActor* ownerActor = getOwnerActor();
	if (ownerActor)
	{
		ActorPushSnowBall* ball = static_cast<ActorPushSnowBall*>(ownerActor);
		if (ball)
		{
			if (ball->isDead())
			{
				return;
			}
			ball->playSoundByPhysCollision();

			Collision* collision = reinterpret_cast<Collision*>(eventContent->userData);
			if (collision)
			{
				Rainbow::Vector3f impulse = collision->impulse;
				if (impulse.y >= 160)//冲力y掉落 = 高于三个方块掉落
				{					
					ball->snowCovered();
					ball->playDieEffect();
				}
				else if (impulse.x >= 80 || impulse.z >= 80)//冲力xy,撞墙
				{
					ball->snowCovered();
					ball->playDieEffect();
				}
			}
		}		
	}
}

//void PushSnowBallLocomotion::OnCollisionExit(const Rainbow::EventContent* eventContent)
//{
//	ClientActor* ownerActor = getOwnerActor();
//	if (ownerActor)
//	{
//		ActorPushSnowBall* ball = static_cast<ActorPushSnowBall*>(ownerActor);
//		if (ball && ball->getCreator())
//		{
//			ball->setCreator(nullptr);
//		}
//	}
//}

void PushSnowBallLocomotion::detachPhysActor()
{
	if (m_pWorld->isRemoteMode()) return;

	if(m_PhysActor)
	{
		ActorPushSnowBall* ball = static_cast<ActorPushSnowBall*>(getOwnerActor());
		if (ball)
		{
			ball->addBallSize(0);
		}		
		m_pWorld->m_PhysScene->DeleteRigidActor(m_PhysActor);
		m_PhysActor = NULL;

		m_hasPhysActor = false;
	}
}

void PushSnowBallLocomotion::attachPhysJoint(ClientPlayer *player)
{
	if (m_pWorld->isRemoteMode()) return;

	PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *>(player->getLocoMotion());

	Rainbow::Quaternionf rot;
	//rot.setEulerAngle(0.0f, 0.0f, 0.0f);
	rot = AngleEulerToQuaternionf(Vector3f(0.0f, 0.0f, 0.0f));
	int offsetY = (loc->m_BoundHeight - m_BoundHeight) / 2 - 5;
	m_PhysJoint = m_pWorld->m_PhysScene->CreateFixJoint(
		loc->m_PhysActor, 
		Vector3f(0, 0, 0), 
		rot, 
		m_PhysActor, 
		Vector3f(0, (float)offsetY, 130), 
		Quaternionf::identity);
}

void PushSnowBallLocomotion::detachPhysJoint()
{
	if (m_pWorld->isRemoteMode()) return;

	if (m_PhysJoint)
	{
		m_pWorld->m_PhysScene->DeleteJoint(m_PhysJoint);
		m_PhysJoint = NULL;
	}
}

void PushSnowBallLocomotion::prepareTick()
{
	ActorLocoMotion::prepareTick();

	m_PrevRotateQuat = m_RotateQuat;
}

void PushSnowBallLocomotion::tick()
{
	if (getOwnerActor()->isDead())
	{
		return;
	}
	ActorLocoMotion::tick();

	if (m_pWorld->isRemoteMode())
	{
		if(m_PosRotationIncrements > 0)
		{
			m_Position = m_Position + (m_ServerPos - m_Position)/m_PosRotationIncrements;
			//m_RotateQuat.slerp(m_RotateQuat, m_ServerRot, 1.0f/m_PosRotationIncrements);
			m_RotateQuat = Slerp(m_RotateQuat, m_ServerRot, 1.0f / m_PosRotationIncrements);

			Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, -m_RotateQuat.GetAxisZ());

			m_PosRotationIncrements--;
		}

	}

	if (!m_pWorld->isRemoteMode())
	{
		if (m_PhysActor)
		{
			Rainbow::Vector3f pos;
			Rainbow::Quaternionf quat;
			m_PhysActor->GetPos(pos, quat);

			m_Motion = pos - m_Position.toVector3();
			m_Position = pos;
			m_RotateQuat = quat;
			Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, -m_RotateQuat.GetAxisZ());
			checkPhysWorld();
		}
		else
		{
			m_Motion *= 0.98f;
			if (Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
			//if(Abs(m_Motion.y) < 0.5f) m_Motion.y = 0;
			if (Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;

			doMoveStep(m_Motion);
			auto bindAComponent = getOwnerActor()->getBindActorCom();

			if (bindAComponent && !bindAComponent->getTarget())
				m_Motion.y -= m_pWorld->getGravity(GRAVITY_ITEM);
		}
	}


	bool playerMove = false;	
	if (m_OldBlockPos.y == -10000)
	{
		m_OldBlockPos = m_Position / BLOCK_SIZE;
	}
	if (m_PhysActor)
	{
		updateBallsize(); //没绑定玩家
	}	
	else
	{
		auto bindAComponent = getOwnerActor()->m_pBindActorComponent;
		if (bindAComponent)
		{
			ClientActor* binding = bindAComponent->getTarget();
			if (binding)
			{
				ClientPlayer* player = dynamic_cast<ClientPlayer*>(binding);
				if (player)
				{
					playerMove = player->getLocoMotion()->m_Motion.x != 0 || player->getLocoMotion()->m_Motion.z != 0;
				}
			}
		}
	}

	if (playerMove)
	{
		updateBallsize();//绑定玩家
		ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
		if (lua_const)
		{
			if (m_BoundSize <= lua_const->pushSnowBall_Small_Size)
			{
				if (m_Speed < lua_const->pushSnowBall_Small_Speed)
				{
					if (playerMove)
					{
						m_Speed += lua_const->pushSnowBall_Small_Speed/ lua_const->pushSnowBall_Small_SpeedUp;
					}
				}
				else
				{
					m_Speed = lua_const->pushSnowBall_Small_Speed;
				}
			}
			else if (m_BoundSize <= lua_const->pushSnowBall_Middle_Size)
			{
				if (m_Speed < lua_const->pushSnowBall_Middle_Speed)
				{
					if (playerMove)
					{
						m_Speed += lua_const->pushSnowBall_Middle_Speed / lua_const->pushSnowBall_Middle_SpeedUp;
					}
				}
				else
				{
					m_Speed = lua_const->pushSnowBall_Middle_Speed;
				}
			}
			else if (m_BoundSize <= lua_const->pushSnowBall_Large_Size)
			{
				if (m_Speed < lua_const->pushSnowBall_Large_Speed)
				{
					if (playerMove)
					{
						m_Speed += lua_const->pushSnowBall_Large_Speed / lua_const->pushSnowBall_Large_SpeedUp;
					}
				}
				else
				{
					m_Speed = lua_const->pushSnowBall_Large_Speed;
				}
			}
		}

	}
}

void PushSnowBallLocomotion::update(float dtime)
{
	ActorLocoMotion::update(dtime);

	m_UpdateRot = Slerp(m_PrevRotateQuat, m_RotateQuat, m_TickPosition.m_TickOffsetTime/GAME_TICK_TIME);
	m_UpdatePos = getFramePosition();
}

void PushSnowBallLocomotion::getRotation(Rainbow::Quaternionf &quat)
{
	quat = m_RotateQuat;
}

void PushSnowBallLocomotion::doBlockCollision()
{
	if (m_pWorld == NULL) return;
	if (getOwnerActor() == NULL) return;
	if (m_pWorld->isRemoteMode()) return;
	if (getOwnerActor()->isDead()) return;

	CollideAABB box;
	getCollideBox(box);
	box.expand(-1, -1, -1);

	WCoord mingrid = CoordDivBlock(box.minPos());
	WCoord maxgrid = CoordDivBlock(box.maxPos());

	World* pworld = getOwnerActor()->getWorld();
	if (nullptr != pworld && pworld->checkChunksExist(mingrid, maxgrid))
	{
		std::vector<WCoord> wcoordv;
		std::vector<int> blockidv;
		Rainbow::HashTable<WCoord, int, WCoordHashCoder> check_woord(16);
		for (int x = mingrid.x; x <= maxgrid.x; x++)
		{
			for (int y = mingrid.y; y <= maxgrid.y; y++)
			{
				for (int z = mingrid.z; z <= maxgrid.z; z++)
				{
					int blockid = pworld->getBlockID(x, y, z);
					if (blockid > 0)
					{					
						auto material = g_BlockMtlMgr.getMaterial(blockid);
						WCoord wcoordcollide(x, y, z);								
						if (material)
						{
							const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
							if (isLiquidBlock(blockid) || isHighTemperatureBlock(def))//触碰到液体方块，高温方块
							{														
								ClientActor* ownerActor = getOwnerActor();
								if (ownerActor)
								{									
									ActorPushSnowBall* ball = static_cast<ActorPushSnowBall*>(ownerActor);
									if (ball)
									{
										ball->playDieEffect();
									}									
								}								
							}	
						}
					}
				}
			}
		}
		getOwnerActor()->clearCollideBlock();
	}
}

void PushSnowBallLocomotion::doPickThrough(ClientActor *excludesactor/* =nullptr */)
{
	if (m_pWorld->isRemoteMode()) return;

	WCoord mvec = getIntegerMotion(m_Motion);
	if (m_OnGround && mvec.y < 0)
		mvec.y = 0;
	if (mvec.length() < 100)
	{
		return;
	}

	MINIW::WorldRay ray;
	ray.m_Origin = m_Position.toWorldPos();
	ray.m_Dir = mvec.toVector3();
	ray.m_Range = ray.m_Dir.Length();
	ray.m_Dir /= ray.m_Range;

	ActorExcludes excludes;
	excludes.addActor(getOwnerActor());
	if (excludesactor)
		excludes.addActor(excludesactor);

	IntersectResult presult;
	WorldPickResult intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
	if (intertype == WorldPickResult::BLOCK) //block
	{
		int blockID = m_pWorld->getBlockID(presult.block);

		WCoord pos = CoordDivBlock(m_Position + mvec);

		if (presult.block != pos)
		{
			if (blockID == BLOCK_COLLIDER || blockID == BLOCK_MOBCOLLIDER || blockID == BLOCK_BALLCOLLIDER)
			{
				g_BlockMtlMgr.getMaterial(blockID)->DoOnActorCollidedWithBlock( getOwnerActor()->getWorld(), presult.block, getOwnerActor());
				// 观察者事件接口
				//ObserverEvent_Block obevent(presult.block.x, presult.block.y, presult.block.z, blockID, (long long)getOwnerActor()->getObjId());
				//ObserverEventManager::getSingleton().OnTriggerEvent("Block.ActorCollide", &obevent);
			}
		}
	}
	else if (intertype == WorldPickResult::ACTOR) //actor
	{
		WCoord pos = (m_Position + mvec) / BLOCK_SIZE;
		if (presult.actor->getPosition() != pos)
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(presult.actor);
			if (player)
			{
				getOwnerActor()->onCollideWithPlayer(player);
			}
			else
			{
				getOwnerActor()->collideWithActor(presult.actor->GetActor());
			}
		}
	}
}

void PushSnowBallLocomotion::checkPhysWorld()
{
	WCoord curpos = m_Position;
	WCoord range(SECTION_SIZE, SECTION_SIZE, SECTION_SIZE);
	WCoord minpos = CoordDivSection(curpos-range);
	WCoord maxpos = CoordDivSection(curpos+range);

	std::vector<WCoord> checkphy;
	for(int y=minpos.y; y<=maxpos.y; y++)
	{
		for(int z=minpos.z; z<=maxpos.z; z++)
		{
			for(int x=minpos.x; x<=maxpos.x; x++)
			{
				//m_pWorld->updateSectionPhysics(x, y, z);
				checkphy.push_back(WCoord(x, y, z));
			}
		}
	}

	for (int i = 0; i<(int)m_preCheckPhy.size(); i++)
	{
		 int j = 0;
		 for (; j<(int)checkphy.size(); j++)
		 {
			if (m_preCheckPhy[i] == checkphy[j])
			{
				break;
			}
		 }
		 if (j == checkphy.size())
		 {
			m_pWorld->updateLeaveSectionPhysics(m_preCheckPhy[i].x, m_preCheckPhy[i].y, m_preCheckPhy[i].z);
		 }
	}

	for (int i = 0; i<(int)checkphy.size(); i++)
	{
		 int j = 0;
		 for (; j<(int)m_preCheckPhy.size(); j++)
		 {
			if (checkphy[i] == m_preCheckPhy[j])
			{
				break;
			}
		 }
		 if (j == m_preCheckPhy.size())
		 {
			m_pWorld->updateEnterSectionPhysics(checkphy[i].x, checkphy[i].y, checkphy[i].z);
		 }
		 m_pWorld->updateSectionPhysics(checkphy[i].x, checkphy[i].y, checkphy[i].z);
	}
	m_preCheckPhy = checkphy;
}

void PushSnowBallLocomotion::updateBindActor()
{
	auto bindAComponent = getOwnerActor()->m_pBindActorComponent;
	if (bindAComponent)
	{
		ClientActor *binding = bindAComponent->getTarget();
		if (binding)
		{
			Rainbow::Vector3f dir = Yaw2FowardDir(binding->getLocoMotion()->m_RotateYaw);

			ClientPlayer *player = dynamic_cast<ClientPlayer *>(binding);
			if (player)
			{
				WCoord pos1 = player->getPosition();
				float distance = player->getLocoMotion()->m_BoundSize + 20 + m_BoundSize / 2;
				WCoord pos = pos1 + WCoord(dir*(distance));
				WCoord posUp = pos + WCoord(0, 100, 0);
				
				int blockid = m_pWorld->getBlockID(CoordDivBlock(pos));
				if (blockid > 0)
				{
					BlockMaterial* blockMat = m_pWorld->getBlockMaterial(CoordDivBlock(pos));
					BlockMaterial* blockMatUp = m_pWorld->getBlockMaterial(CoordDivBlock(posUp));
					if (blockMat && blockMat->canBlocksMovement(m_pWorld, CoordDivBlock(pos)))
					{
						if (blockMatUp && blockMatUp->canBlocksMovement(m_pWorld, CoordDivBlock(posUp)))
						{
							pos = pos1;
						}
						else
						{
							
							int offset = posUp.y % 100;
							pos.y = posUp.y - offset;
						}
					}
				}

				pos.y += (m_BoundHeight / 2);

				setPosition(pos.x, pos.y, pos.z);
			}
		}
	}
}

void PushSnowBallLocomotion::setPosition(int x, int y, int z)
{
	m_OldPosition = m_Position;
	m_Position.x = x;
	m_Position.y = y;
	m_Position.z = z;
	if (m_PhysActor)
		m_PhysActor->SetPos(Rainbow::Vector3f((float)x, (float)y, (float)z), m_RotateQuat);
}


void PushSnowBallLocomotion::updateBallsize()
{
	WCoord posBlock = m_Position / BLOCK_SIZE;
	int moveBlockNum = Rainbow::Abs(posBlock.x - m_OldBlockPos.x);
	moveBlockNum += Rainbow::Abs(posBlock.z - m_OldBlockPos.z);
	if (moveBlockNum > 0)
	{
		//不在上一个格子位置
		m_OldBlockPos = m_Position / BLOCK_SIZE;

		WCoord pos = CoordDivBlock(m_Position);
		WCoord blockpos1(pos.x, pos.y, pos.z);
		WCoord blockpos2(pos.x, pos.y - 1, pos.z);
		int blockid1 = m_pWorld->getBlockID(blockpos1);
		int blockid2 = m_pWorld->getBlockID(blockpos2);
		if (blockid1 > 0 || blockid2 > 0)
		{
			ActorPushSnowBall* ball = static_cast<ActorPushSnowBall*>(getOwnerActor());
			if (ball)
			{				
				if (isSnowBlock(blockid1))//积雪
				{
					//m_pWorld->destoryblock
					m_pWorld->destroyBlock(blockpos1, BLOCK_MINE_NONE);
					ball->addBallSize(1);
					return;
				}

				if (isSnowBlock(blockid2))//积雪
				{
					m_pWorld->destroyBlock(blockpos2, BLOCK_MINE_NONE);
					ball->addBallSize(1);
					return;
				}

				//const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid2);
				//if (!isLiquidBlock(blockid2) && !isHighTemperatureBlock(def))
				//{
				//	ball->addBallSize(-1);
				//}
			}
		}
	}
}