/**
* file : BlockEnvEffectsComponent
* func : 特殊功能方块对actor的作用
* by   : tanzhenyu
*/
#ifndef BLOCK_ENV_EFFECTS_H_
#define BLOCK_ENV_EFFECTS_H_

#include "ActorComponent_Base.h"
#include "SandboxMacros.h"
#include <string>

#include <map>


class BlockRangeCheckBase;
class BlockEnvEffectBase;
class Actor;
struct BlockEnvST;
struct ActiveBlockEnvST;

//bool map_comapre_ActiveBlockEnvST(const ActiveBlockEnvST& l, const ActiveBlockEnvST& r);
//{
//	return l.bActive;
//}

class BlockEnvEffectsComponent : public ActorComponentBase
{
	DECLARE_COMPONENTCLASS(BlockEnvEffectsComponent)

public:
	BlockEnvEffectsComponent();
	virtual ~BlockEnvEffectsComponent();

	bool Add(const std::string &rangeName,const std::string &effectName, const std::string &name);

	bool GetStatus(const std::string& name) const;
	void ClearStatus() {}// { m_status.clear(); };

	template<typename T>
	bool RegisterRange(const std::string &rangeName);

	bool RegisterRange(const std::string &rangeName, BlockRangeCheckBase* range){
		if(m_ranges.find(rangeName) != m_ranges.end())
			return false;
		if(!range)
			return false;
		m_ranges.insert(std::make_pair(rangeName, range));
		return true;
	}

	
	template<typename T>
	bool RegisterEffect(const std::string &effectName);

	bool RegisterEffect(const std::string &effectName, BlockEnvEffectBase* effect){
		if(m_effects.find(effectName) != m_effects.end())
			return false;
		if(!effect)
			return false;
		m_effects.insert(std::make_pair(effectName, effect));
		return true;
	}
	
protected:
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
private:
	bool map_comapre_ActiveBlockEnvST(const ActiveBlockEnvST& l, const ActiveBlockEnvST& r);
	//std::set<std::string>     m_status;
	std::map<std::string, ActiveBlockEnvST>m_members;
	//std::vector<BlockEnvST>   m_members;

	std::map<std::string, BlockRangeCheckBase*>   m_ranges;
	std::map<std::string, BlockEnvEffectBase*>    m_effects;
};

template<typename T>
bool BlockEnvEffectsComponent::RegisterRange(const std::string &rangeName) {
	auto ins = SANDBOX_NEW(T);
	if(!RegisterRange(rangeName, dynamic_cast<BlockRangeCheckBase*>(ins))){
		SANDBOX_DELETE(ins);
		return false;
	}
	return true;
}

template<typename T>
bool BlockEnvEffectsComponent::RegisterEffect(const std::string &effectName) {
	auto ins = SANDBOX_NEW(T);
	if(!RegisterEffect(effectName, dynamic_cast<BlockEnvEffectBase*>(ins))){
		SANDBOX_DELETE(ins);
		return false;
	}
	return true;
}

#endif
