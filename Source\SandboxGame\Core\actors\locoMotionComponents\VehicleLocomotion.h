#ifndef _BALL_LOCOMOTION_H_
#define _BALL_LOCOMOTION_H_

#include "ActorLocoMotion.h"
#include "Physics/Vehicles/VehiclesController.h"
#include "BlockMaterialBase.h"
namespace Rainbow
{
	class EventContent;
	class RigidDynamicActor;
	class Model;
 }
namespace MINIW
{
	class Joint;
}
class ClientActor;
class ClientPlayer;
 
class VehicleLocoMotion : public ActorLocoMotion //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(VehicleLocoMotion)
		 
	enum {
		FL_WHEEL = 0,
		FR_WHEEL = 1,
		RL_WHEEL = 2,
		RR_WHEEL = 3,
		MAX_WHEEL
	};
	//tolua_begin
	VehicleLocoMotion();
	//tolua_end
	virtual void prepareTick() override;
	virtual void tick() override;
	virtual void update(float dtime) override;
	virtual void updateBindActor() override;
	virtual void getRotation(Rainbow::Quaternionf &quat) override;
	virtual void doPickThrough(ClientActor *excludesactor = nullptr) override;
	virtual bool needFullRotation() override
	{
		return true;
	}
	virtual void setPosition(int x, int y, int z);
	//tolua_begin
	void attachPhysActor();
	void detachPhysActor();
	void attachPhysJoint(ClientPlayer *player);
	void detachPhysJoint();
	void checkPhysWorld();

	virtual void OnCollisionEnter(const Rainbow::EventContent* collision);

	
	Rainbow::RigidDynamicActor *m_PhysActor;
	MINIW::Joint *m_PhysJoint;


	Rainbow::WorldPos m_UpdatePos;
	Rainbow::Quaternionf m_UpdateRot;

	Rainbow::Quaternionf m_PrevRotateQuat;
	Rainbow::Quaternionf m_RotateQuat;

	int m_PosRotationIncrements;
	WCoord m_ServerPos;
	Rainbow::Quaternionf m_ServerRot;
	//std::vector<WCoord> m_preCheckPhy; 
	bool m_hasPhysActor;

	private:
		Rainbow::VehicleController m_ctl;
		 //重力
        float m_fGravity;
        //质量
        float m_fMass;
        //弹性
        float m_fRestitution;
        //静态摩擦力
        float m_fStaticFriction;
        //动态摩擦力
        float m_fDynamicFriction;
        //速度
        Rainbow::Vector3f m_fVelocity;
        //角速度
        Rainbow::Vector3f m_fAngleVelocity;

        //世界位置        
       //Rainbow::Vector3f m_Position;        
        //Rainbow::Quaternionf m_Quaternion;
        

		void extractMesh(Rainbow::Model* obj, Rainbow::RAWMesh& mesh);
		void extractMesh(BlockMaterial* obj, Rainbow::RAWMesh& mesh);

		 
	//tolua_end
}; //tolua_exports

#endif