--文件来源：assets/socTrade/ui_npc_trade.xml
local ui_npc_tradeAutoGen = Class("ui_npc_tradeAutoGen",ClassList["MiniUICommonNodeClass"])

local ui_npc_tradeCtrl,ui_npc_tradeModel,ui_npc_tradeView = GetInst("MiniUIManager"):GetMVC("ui_npc_tradeAutoGen")

local t_NpcInfo = {
	[3010]={descid=118, iconid=21},
	[3011]={descid=119, iconid=20},
	[3012]={descid=120, iconid=22},
	[3013]={descid=3511, iconid=23},
	[3014]={descid=3512, iconid=24},
	[3015]={descid=3582, iconid=25},
	[3016]={descid=3829, iconid=26},
	[3017]={descid=960, iconid=27},
	[3018]={descid=264, iconid=28},
	[3019]={descid=86004, iconid=28},
	[3210]={descid=86022, iconid=28},
	[3211]={descid=86023, iconid=28},
	[3222]={descid=86030, iconid=28},
	[3223]={descid=86029, iconid=28},
	[3229]={descid=86028, iconid=28},
}

--初始化
function ui_npc_tradeAutoGen:init(param)
	if self.firstInit == nil then 
		--实例化MVC
		self.ctrl = GetInst("MiniUIManager"):InstMVC("ui_npc_tradeAutoGen",{incomingParam = param,root = self.rootNode,uiType = UIType.FGUI})
		--注册UI事件
		self.ctrl:RegisterUIEvents()
		--启动
		self.ctrl:Start()
	else
		--重新赋值
		if param then 
			self.ctrl.model:SetIncomingParam(param)
		end
	end
	
	-- 初始化NPC交易数据
	self.ctrl.tradeNum = 1 -- 购买数量
	self.ctrl.npcId = OpenedContainerMob:getDef().ID
	
	-- 刷新界面
	self.ctrl:FreshAllGoods()
	
	-- 设置默认选中第一个有效商品
	if self.ctrl.goodsIndexMap and self.ctrl.goodsIndexMap[1] then
		self.ctrl.curSelect = self.ctrl.goodsIndexMap[1]
	else
		self.ctrl.curSelect = 1
	end
	
	-- 初始化货币图标（设置一次，后续不再变化）
	self.ctrl:InitCostIcon()
	
	self.ctrl:UpdateSelectedItem()
	self.ctrl:UpdateTradeInfo()
	-- 立即更新晶石数量显示
	self.ctrl:UpdateJingshiDisplay()
	-- 更新商店标题
	self.ctrl:UpdateShopTitle()
	
	-- 延迟设置默认选中状态，确保界面完全加载
	GetInst("MiniUIScheduler"):reg(function()
		self.ctrl:SetDefaultSelectedItem()
	end, 0.1, 1)
	
	self.firstInit = 0
end

--显示
function ui_npc_tradeAutoGen:onShow()
	self.ctrl:Refresh()
	-- 立即更新晶石数量显示
	self.ctrl:UpdateJingshiDisplay()
	-- 更新商店标题
	self.ctrl:UpdateShopTitle()
	--设置刷新循环 1 秒一次
	if not self.m_timer then
		self.m_timer = GetInst("MiniUIScheduler"):reg(function()
			self.ctrl:FreshAllGoods()
			self.ctrl:UpdateSelectedItem()
			self.ctrl:UpdateTradeInfo()
		end, 1.0, nil, 1.0, false)
	end
end

--隐藏
function ui_npc_tradeAutoGen:onHide()
	self:_Unreg()
	self.ctrl:Reset()
end

--移除
function ui_npc_tradeAutoGen:onRemove()
	self:_Unreg()
	self.ctrl:Remove()
	--销毁MVC实例
	GetInst("MiniUIManager"):UnInstMVC(self.ctrl)
end

function ui_npc_tradeAutoGen:_Unreg()
	if self.m_timer then
		GetInst("MiniUIScheduler"):unreg(self.m_timer)
		self.m_timer = nil
	end
end

--Ctrl:注册UI事件
function ui_npc_tradeCtrl:RegisterUIEvents()
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.ui_trade_item_list, UIEventType_ClickItem, function(obj, context)
 		if self.Ui_trade_item_listClickItem then
			self:Ui_trade_item_listClickItem(obj, context)
		end
	end)
	GetInst("MiniUIComponents"):setCallback(self.view.widgets.ui_trade_item_list, "GList.itemRenderer", function(comp, idx, obj)
 		if self.Ui_trade_item_listItemRenderer then
			self:Ui_trade_item_listItemRenderer(comp, idx, obj)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.btn_trade_dec, UIEventType_Click, function(obj, context)
 		if self.Btn_trade_decClick then
			self:Btn_trade_decClick(obj, context)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.btn_trade_inc, UIEventType_Click, function(obj, context)
 		if self.Btn_trade_incClick then
			self:Btn_trade_incClick(obj, context)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.btn_trade, UIEventType_Click, function(obj, context)
 		if self.Btn_tradeClick then
			self:Btn_tradeClick(obj, context)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.btn_trade_max, UIEventType_Click, function(obj, context)
 		if self.Btn_trade_maxClick then
			self:Btn_trade_maxClick(obj, context)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.btn_trade_quit, UIEventType_Click, function(obj, context)
 		if self.Btn_trade_quitClick then
			self:Btn_trade_quitClick(obj, context)
		end
	end)
end

-- UI事件处理函数
function ui_npc_tradeCtrl:Ui_trade_item_listClickItem(obj, context)
	local item = context:getData()
	local itemIndex = obj:getChildIndex(item)
	local listIndex = itemIndex + 1 -- 列表索引从0开始，转换为1开始
	self.curSelect = self.goodsIndexMap and self.goodsIndexMap[listIndex] or listIndex
	
	-- 切换商品时重置购买数量为1
	self.tradeNum = 1
	
	-- 移动选中图片到被点击商品项的位置
	self:MoveSelectedImageToItem(item)
	
	self:UpdateSelectedItem()
	self:UpdateTradeInfo()
end

function ui_npc_tradeCtrl:Ui_trade_item_listItemRenderer(comp, idx, obj)
	local listIndex = idx + 1 -- 列表索引从0开始，转换为1开始
	local actualIndex = self.goodsIndexMap and self.goodsIndexMap[listIndex] or listIndex
	self:FreshGoodsItem(obj, actualIndex) -- 渲染商品项
end

function ui_npc_tradeCtrl:Btn_trade_decClick(obj, context)
	if self.tradeNum > 1 then
		self.tradeNum = self.tradeNum - 1
		self:UpdateTradeInfo()
	end
end

function ui_npc_tradeCtrl:Btn_trade_incClick(obj, context)
	if ClientBackpack == nil or self.curSelect == nil then
		return
	end
	
	-- 获取背包中id==2017的数量（货币数量）
	local currencyNum = GetItemNum2Id(2017)
	local index = self.curSelect
	local payout_index = NPCTRADE_START_INDEX + 2*(index-1)
	local unitPrice = ClientBackpack:getGridNum(payout_index)
	
	if unitPrice > 0 then
		local maxNum = math.floor(currencyNum / unitPrice)
		
		-- 只有在未达到最大值时才增加
		if self.tradeNum < maxNum then
			self.tradeNum = self.tradeNum + 1
			self:UpdateTradeInfo()
		else
			ShowGameTips(GetS(10001011), 3)
		end
	end
end

function ui_npc_tradeCtrl:Btn_tradeClick(obj, context)
	-- 在执行交易前先检查是否可以交易
	if not self:CanTradeCurrentItem() then
		-- 获取具体的错误原因并显示相应提示
		local errorMsg = self:GetTradeErrorMessage()
		if errorMsg then
			ShowGameTips(errorMsg, 3)
		end
		return
	end
	
	self:NpcTradeFrame_Exchange()
end

function ui_npc_tradeCtrl:Btn_trade_maxClick(obj, context)
	if ClientBackpack == nil or self.curSelect == nil then
		return
	end
	
	-- 获取背包中id==2017的数量（货币数量）
	local currencyNum = GetItemNum2Id(2017)
	
	-- 获取当前选中商品的单价
	local index = self.curSelect
	local payout_index = NPCTRADE_START_INDEX + 2*(index-1)
	local unitPrice = ClientBackpack:getGridNum(payout_index)
	
	if unitPrice > 0 then
		-- 计算最大可购买数量
		local maxNum = math.floor(currencyNum / unitPrice)
		if maxNum > 0 then
			self.tradeNum = maxNum
			self:UpdateTradeInfo()
		else
			ShowGameTips(GetS(10001012), 3)
		end
	end
end

function ui_npc_tradeCtrl:Btn_trade_quitClick(obj, context)
	-- 关闭NPC交易界面
	GetInst("MiniUIManager"):CloseUI("ui_npc_tradeAutoGen")
end

--View:获取需要操作的节点
function ui_npc_tradeView:GetHandleNodes()
	self.widgets={}
	--list
	self.widgets.ui_trade_item_list = self.root:getChild("ui_trade_item_list")
	--Button
	self.widgets.btn_trade_dec = self.root:getChild("btn_trade_dec")
	--Button
	self.widgets.btn_trade_inc = self.root:getChild("btn_trade_inc")
	--Button
	self.widgets.btn_trade = self.root:getChild("btn_trade")
	--Button
	self.widgets.btn_trade_max = self.root:getChild("btn_trade_max")
	self.widgets.btn_trade_quit = self.root:getChild("btn_trade_quit")
	self.widgets.ui_trade_selected = self.root:getChild("ui_trade_selected")
	--UI elements for selected item display
	self.widgets.ui_trade_img = self.root:getChild("ui_trade_img")
	self.widgets.ui_trade_name = self.root:getChild("ui_trade_name")
	self.widgets.ui_trade_desc = self.root:getChild("ui_trade_desc")
	self.widgets.txt_trade_num = self.root:getChild("txt_trade_num")
	self.widgets.txt_trade_cost = self.root:getChild("txt_trade_cost")
	self.widgets.txt_jingshi_num = self.root:getChild("txt_jingshi_num") -- 晶石数量显示
	self.widgets.txt_shop_title = self.root:getChild("txt_shop_title") -- 商店标题
	self.widgets.cost_icon = self.root:getChild("n16") -- 货币图标
end

-- 刷新所有商品
function ui_npc_tradeCtrl:FreshAllGoods()
	if ClientBackpack == nil then
		return
	end
	
	-- 建立有效商品索引映射
	self.goodsIndexMap = {}
	local goodsCount = 0
	
	for i = 1, 6 do
		local obtain_index = NPCTRADE_START_INDEX + 2*i - 1
		local itemId = ClientBackpack:getGridItem(obtain_index)
		if itemId > 0 then
			goodsCount = goodsCount + 1
			self.goodsIndexMap[goodsCount] = i -- 将列表索引映射到实际商品索引
		end
	end
	
	-- 更新商品列表数量
	self.view.widgets.ui_trade_item_list:setNumItems(goodsCount)
end

-- 刷新单个商品项
function ui_npc_tradeCtrl:FreshGoodsItem(obj, index)
	if ClientBackpack == nil then
		return
	end
	
	local obtain_index = NPCTRADE_START_INDEX + 2*index - 1 -- 获得的物品索引
	local payout_index = NPCTRADE_START_INDEX + 2*(index-1) -- 付出的物品索引
	
	local tradeDef = DefMgr:getNpcTradeDef(ClientBackpack:getGridUserdata(obtain_index))
	if tradeDef == nil then 
		return 
	end

	local type = tradeDef.TradeType
	local dur = ClientBackpack:getGridDuration(obtain_index) -- 标记锁定
	
	-- 根据交易类型决定显示哪个物品的图标（关键逻辑！）
	local display_grid_index = obtain_index
	if type == 0 then
		-- 出售类型：显示付出的物品
		display_grid_index = payout_index
	end
	-- 购买/兑换类型：显示获得的物品（默认）
	
	local displayItemId = ClientBackpack:getGridItem(display_grid_index)
	if displayItemId <= 0 then
		return
	end

	-- 根据新的UI模板结构设置组件
	local tradeIcon = obj:getChild("ui_trade_icon")
	local tradePriceText = obj:getChild("txt_trade_price")
	local tradeNumText = obj:getChild("txt_trade_num")
	local selectedImage = obj:getChild("ui_trade_selected")
	
	-- 设置商品图标
	if tradeIcon then
		self:SetIconObjIcon(tradeIcon, displayItemId)
		
		-- 处理锁定状态（商品已售完时图标变灰）
		if dur == 0 then
			tradeIcon:setGrayed(true)
		else
			tradeIcon:setGrayed(false)
		end
	end
	
	-- 设置商品数量（获得的物品数量）
	if tradeNumText then
		local num = ClientBackpack:getGridNum(obtain_index)
		tradeNumText:setText("x " .. num)
	end
	
	-- 设置售价信息（购买该商品需要付出的货币数量）
	if tradePriceText then
		local payItemId = ClientBackpack:getGridItem(payout_index)
		if payItemId > 0 then
			-- 获取单价
			local price = ClientBackpack:getGridNum(payout_index)
			
			-- 格式化价格显示
			local priceText = "x " .. price
			if dur == 0 then
				-- 已售完时显示为红色
				tradePriceText:setColor({r = 254, g = 85, b = 26})
				priceText = GetS(10001022)
			else
				-- 正常显示为默认颜色
				tradePriceText:setColor({r = 153, g = 51, b = 0}) -- #993300
			end
			tradePriceText:setText(priceText)
		else
			tradePriceText:setText(GetS(10001023))
		end
	end
end

-- 移动选中图片到指定商品项的位置
function ui_npc_tradeCtrl:MoveSelectedImageToItem(item)
	local selectedImage = self.view.widgets.ui_trade_selected
	if not selectedImage or not item then
		return
	end
	
	-- 使用正确的FairyGUI方法获取商品项的真实位置
	local itemRect = GetMiniUINodeToRootRect(item)
	local itemX = 0
	local itemY = 0
	
	if itemRect then
		itemX = itemRect.x
		itemY = itemRect.y
	end
	
	-- 如果无法获取有效位置，使用备用计算方法
	if itemX == 0 and itemY == 0 then
		local itemList = self.view.widgets.ui_trade_item_list
		if not itemList then
			return
		end
		
		-- 获取商品项在列表中的索引
		local itemIndex = itemList:getChildIndex(item)
		if itemIndex >= 0 then
			-- 获取列表的绝对位置
			local listRect = GetMiniUINodeToRootRect(itemList)
			if listRect then
				-- 基于XML布局参数计算相对位置
				local lineItemCount = 3  -- 每行3个商品
				local itemWidth = 376
				local itemHeight = 263
				local colGap = 33
				local lineGap = 30
				
				local row = math.floor(itemIndex / lineItemCount)
				local col = itemIndex % lineItemCount
				
				local relativeX = col * (itemWidth + colGap)
				local relativeY = row * (itemHeight + lineGap)
				
				-- 计算绝对位置
				itemX = listRect.x + relativeX
				itemY = listRect.y + relativeY
			end
		end
	end
	
	-- transformRect 返回的已经是绝对位置，无需再加上列表位置
	local absoluteX = itemX
	local absoluteY = itemY
	
	-- 可以添加微调偏移量，让选中图片更精确地覆盖商品项
	local offsetX = -25  -- 水平偏移，根据需要调整
	local offsetY = -17 -- 垂直偏移，根据需要调整
	
	local finalX = absoluteX + offsetX
	local finalY = absoluteY + offsetY
	
	-- 设置选中图片的位置
	selectedImage:setPosition(finalX, finalY)
	selectedImage:setVisible(true)
end

-- 设置默认选中项（第一个有效商品项）
function ui_npc_tradeCtrl:SetDefaultSelectedItem()
	local itemList = self.view.widgets.ui_trade_item_list
	if not itemList then
		return
	end
	
	-- 找到第一个有效的商品项
	local firstItem = nil
	local targetListIndex = -1
	
	-- 根据goodsIndexMap找到第一个有效商品对应的列表项
	if self.goodsIndexMap and self.goodsIndexMap[1] then
		-- 第一个有效商品对应的列表索引是1（从1开始），转换为列表子项索引（从0开始）
		targetListIndex = 0
		firstItem = itemList:getChildAt(targetListIndex)
	else
		-- 如果没有goodsIndexMap，直接使用第一个商品项
		if itemList:numChildren() > 0 then
			targetListIndex = 0
			firstItem = itemList:getChildAt(targetListIndex)
		end
	end
	
	if firstItem then
		self:MoveSelectedImageToItem(firstItem)
	else
		-- 如果没有找到有效商品项，隐藏选中图片
		local selectedImage = self.view.widgets.ui_trade_selected
		if selectedImage then
			selectedImage:setVisible(false)
		end
	end
end

-- 更新选中商品的详细信息
function ui_npc_tradeCtrl:UpdateSelectedItem()
	if ClientBackpack == nil then
		return
	end
	
	local i = self.curSelect
	local obtain_index = NPCTRADE_START_INDEX + 2*i - 1
	local payout_index = NPCTRADE_START_INDEX + 2*(i-1)
	
	local tradeDef = DefMgr:getNpcTradeDef(ClientBackpack:getGridUserdata(obtain_index))
	if tradeDef == nil then
		return
	end
	
	local type = tradeDef.TradeType
	
	-- 根据交易类型决定显示哪个物品的详细信息
	local display_grid_index = obtain_index
	if type == 0 then
		-- 出售类型：显示付出的物品
		display_grid_index = payout_index
	end
	-- 购买/兑换类型：显示获得的物品（默认）
	
	local displayItemId = ClientBackpack:getGridItem(display_grid_index)
	if displayItemId > 0 then
		local def = ItemDefCsv:get(displayItemId)
		if def then
			-- 更新商品图片
			self:SetIconObjIcon(self.view.widgets.ui_trade_img, displayItemId)
			
			-- 更新商品名称
			self.view.widgets.ui_trade_name:setText(def.Name)
			
			-- 更新商品描述
			self.view.widgets.ui_trade_desc:setText(def.Desc or "")
			
			-- 更新按钮状态
			self:UpdateButtonState()
			
			-- 更新晶石数量显示
			self:UpdateJingshiDisplay()
		end
	end
end

-- 获取交易错误信息
function ui_npc_tradeCtrl:GetTradeErrorMessage()
	if ClientBackpack == nil or self.curSelect == nil then
		return GetS(10001013)
	end
	
	local index = self.curSelect
	local obtain_index = NPCTRADE_START_INDEX + 2*index - 1
	local payout_index = NPCTRADE_START_INDEX + 2*(index-1)
	
	-- 检查商品是否存在
	local obtainItemId = ClientBackpack:getGridItem(obtain_index)
	local payItemId = ClientBackpack:getGridItem(payout_index)
	if obtainItemId <= 0 or payItemId <= 0 then
		return GetS(10001014)
	end
	
	-- 检查商品是否已售完
	local dur = ClientBackpack:getGridDuration(obtain_index)
	if dur == 0 then
		return GetS(10001015)
	end
	
	-- 检查是否有足够的货币（固定使用ID=2017）
	local unitPrice = ClientBackpack:getGridNum(payout_index)
	local totalNeedNum = unitPrice * self.tradeNum
	local hasNum = GetItemNum2Id(2017)
	if hasNum < totalNeedNum then
		return GetS(10001012)
	end
	
	-- 检查创造模式下的快捷栏空间
	if not self:NpcTradeIsGodModeShortcutHasSpace() then
		if CurWorld:isCreativeMode() then
			return GetS(10001016)
		elseif CurWorld:isGameMakerMode() then
			return GetS(10001017)
		end
	end
	
	return nil -- 没有错误
end

-- 检查当前商品是否可以交易
function ui_npc_tradeCtrl:CanTradeCurrentItem()
	if ClientBackpack == nil or self.curSelect == nil then
		return false
	end
	
	local index = self.curSelect
	local obtain_index = NPCTRADE_START_INDEX + 2*index - 1
	local payout_index = NPCTRADE_START_INDEX + 2*(index-1)
	
	-- 检查商品是否存在
	local obtainItemId = ClientBackpack:getGridItem(obtain_index)
	local payItemId = ClientBackpack:getGridItem(payout_index)
	if obtainItemId <= 0 or payItemId <= 0 then
		return false
	end
	
	-- 检查商品是否已售完
	local dur = ClientBackpack:getGridDuration(obtain_index)
	if dur == 0 then
		return false
	end
	
	-- 检查是否有足够的货币（固定使用ID=2017）
	local unitPrice = ClientBackpack:getGridNum(payout_index)
	local totalNeedNum = unitPrice * self.tradeNum
	local hasNum = GetItemNum2Id(2017)
	if hasNum < totalNeedNum then
		return false
	end
	
	-- 检查创造模式下的快捷栏空间
	if not self:NpcTradeIsGodModeShortcutHasSpace() then
		if CurWorld:isCreativeMode() or CurWorld:isGameMakerMode() then
			return false
		end
	end
	
	return true
end

-- 更新按钮状态和文本
function ui_npc_tradeCtrl:UpdateButtonState()
	if ClientBackpack == nil or self.curSelect == nil then
		return
	end
	
	local index = self.curSelect
	local obtain_index = NPCTRADE_START_INDEX + 2*index - 1
	
	local tradeDef = DefMgr:getNpcTradeDef(ClientBackpack:getGridUserdata(obtain_index))
	if tradeDef == nil then 
		return 
	end
	
	local type = tradeDef.TradeType
	local canTrade = self:CanTradeCurrentItem()
	
	-- 根据交易类型和可用性更新按钮文本
	local btnText = ""
	if not canTrade then
		btnText = GetS(10001019)
	elseif type == 0 then
		btnText = GetS(538) -- 出售
	elseif type == 1 then
		btnText = GetS(3059) -- 购买
	elseif type == 2 then
		btnText = GetS(539) -- 兑换
	else
		btnText = GetS(10001020) -- 默认文本
	end
	
	-- 查找并更新按钮文本（如果按钮有子文本组件）
	local tradeBtn = self.view.widgets.btn_trade
	if tradeBtn then
		local titleChild = tradeBtn:getChild("title")
		if titleChild then
			titleChild:setText(btnText)
		end
		-- 设置按钮是否可点击和视觉状态
		tradeBtn:setTouchable(canTrade)
		tradeBtn:setGrayed(not canTrade) -- 置灰状态与可点击状态相反
		

	end
end

-- 初始化货币图标（只设置一次）
function ui_npc_tradeCtrl:InitCostIcon()
	if ClientBackpack == nil then
		return
	end
	
	-- 获取第一个有效商品的付出道具作为货币图标
	local firstValidIndex = self.goodsIndexMap and self.goodsIndexMap[1] or 1
	local payout_index = NPCTRADE_START_INDEX + 2*(firstValidIndex-1)
	local payItemId = ClientBackpack:getGridItem(payout_index)
	
end

-- 更新晶石数量显示
function ui_npc_tradeCtrl:UpdateJingshiDisplay()
	if self.view.widgets.txt_jingshi_num then
		local jingshiNum = GetItemNum2Id(2017)
		self.view.widgets.txt_jingshi_num:setText("(" .. jingshiNum .. ")")
	end
end

-- 更新商店标题
function ui_npc_tradeCtrl:UpdateShopTitle()
	if self.view.widgets.txt_shop_title and OpenedContainerMob then
		local mobDef = OpenedContainerMob:getDef()
		if mobDef and mobDef.Name and mobDef.Name ~= "" then
			self.view.widgets.txt_shop_title:setText(mobDef.Name)
		else
			self.view.widgets.txt_shop_title:setText(GetS(10001021))
		end
	end
end

-- 更新交易信息（数量和总价）
function ui_npc_tradeCtrl:UpdateTradeInfo()
	if ClientBackpack == nil then
		return
	end
	
	-- 更新购买数量显示
	self.view.widgets.txt_trade_num:setText(tostring(self.tradeNum))
	
	-- 计算总价
	local i = self.curSelect
	local payout_index = NPCTRADE_START_INDEX + 2*(i-1)
	local payItemId = ClientBackpack:getGridItem(payout_index)
	
	if payItemId > 0 then
		-- 获取单价（使用高位+低位的完整价格）
		local hightNum = ClientBackpack:getGridUserdata(payout_index) * 256
		local unitPrice = ClientBackpack:getGridNum(payout_index)
		local totalCost = unitPrice * self.tradeNum
		
		-- 更新总价显示（购买数量 × 单价）
		local costText = "x " .. totalCost
		self.view.widgets.txt_trade_cost:setText(costText)
		
		-- 根据晶石是否充足设置字体颜色
		local currentJingshi = GetItemNum2Id(2017)
		if currentJingshi >= totalCost then
			-- 晶石充足：显示白色
			self.view.widgets.txt_trade_cost:setColor({r = 255, g = 255, b = 255})
		else
			-- 晶石不足：显示红色
			self.view.widgets.txt_trade_cost:setColor({r = 255, g = 89, b = 89})
		end
	end
	
	-- 更新按钮状态（因为数量变化可能影响可用性）
	self:UpdateButtonState()
	
	-- 更新晶石数量显示
	self:UpdateJingshiDisplay()
end

-- 执行交易
function ui_npc_tradeCtrl:NpcTradeFrame_Exchange()
	if CurWorld == nil or ClientBackpack == nil or OpenedContainerMob == nil or CurMainPlayer == nil then
		return
	end
	
	local CurChooseIndex = self.curSelect
	
	-- 检查创造模式下的快捷栏空间
	if not self:NpcTradeIsGodModeShortcutHasSpace() then
		if CurWorld:isCreativeMode() then
			ShowGameTips(GetS(8045), 3)
		elseif CurWorld:isGameMakerMode() then
			ShowGameTips(GetS(6995), 3)
		end
		return
	end

	local grid_index = NPCTRADE_START_INDEX + 2*CurChooseIndex - 1
	local payItemId = ClientBackpack:getGridItem(grid_index-1)
	local obtainItemId = ClientBackpack:getGridItem(grid_index)
	
	if obtainItemId > 0 and payItemId > 0 then
		local hightNum = ClientBackpack:getGridUserdata(grid_index-1) * 256
		local unitNeedNum = ClientBackpack:getGridNum(grid_index-1) + hightNum
		local totalNeedNum = unitNeedNum * self.tradeNum
		local hasNum = GetItemNum2Id(payItemId)
		
		local tradeDef = DefMgr:getNpcTradeDef(ClientBackpack:getGridUserdata(grid_index))
		if tradeDef == nil then 
			return 
		end

		local type = tradeDef.TradeType
		local dur = ClientBackpack:getGridDuration(grid_index)
		
		if dur == 0 then -- 锁定
			if type == 0 then
				ShowGameTips(GetS(540), 3)
			else
				ShowGameTips(GetS(541), 3)
			end
		else
					if hasNum >= totalNeedNum then
			-- 执行多次交易（与老实现保持一致）
			for i = 1, self.tradeNum do
				CurMainPlayer:npcTrade(1, grid_index-1)
			end
			
			-- 显示交易成功提示（直接使用交易数量）
			local def = ItemDefCsv:get(obtainItemId)
			local text = ""
			if type == 1 then
				text = GetS(3595, def.Name, self.tradeNum)
			elseif type == 2 then
				text = GetS(3596, def.Name, self.tradeNum)
			end
			ShowGameTips(text, 1)
			
			-- 重置购买数量并刷新界面
			self.tradeNum = 1
			self:UpdateTradeInfo()
		else
				if type == 1 then -- 购买
					local lackNum = math.ceil((totalNeedNum - hasNum) / MiniCoin_Star_Ratio)
					local text = GetS(466, totalNeedNum - hasNum, lackNum)
					StoreMsgBox(5, text, GetS(469), -2, lackNum, totalNeedNum)
					GetInst('StoreMsgBoxFrame'):SetClientString(GetS(10001018))
				else
					ShowGameTips(GetS(544), 3)
				end
			end
		end
	end
end

-- 检查创造模式下快捷栏是否有空位
function ui_npc_tradeCtrl:NpcTradeIsGodModeShortcutHasSpace()
	if CurWorld == nil or ClientBackpack == nil then
		return true
	end
	
	if CurWorld and CurWorld:isGodMode() then
		if ClientBackpack then
			local emptyNum = ClientBackpack:getShorCutEmptyGridNum()
			if emptyNum > 0 then
				return true
			end
		end
		return false
	end
	
	return true
end

-- 设置图标（适配新UI结构）
function ui_npc_tradeCtrl:SetIcon(obj, id)
	local icon = obj:getChild("ui_trade_icon") or obj:getChild("icon")
	self:SetIconObjIcon(icon, id)
end

-- 设置图标对象的图标
function ui_npc_tradeCtrl:SetIconObjIcon(icon, id)
	if not icon then
		return
	end
	
	icon:setVisible(true)
	
	-- 判断是否为loader组件，使用不同的API
	local loaderIcon = tolua.cast(icon, "miniui.GLoader")
	if loaderIcon then
		-- 使用专门的loader图标设置函数
		MiniuiSetItemIcon(id, loaderIcon)
	else
		-- 普通图标组件使用texture设置
		local tex = MiniuiGetModelIconParams(id)
		if tex then
			icon:setTexture(tex)
		else
			tex = MiniuiGetItemIconTexture(id)
			if tex then
				icon:setTexture(tex)
			end
		end
	end
end

