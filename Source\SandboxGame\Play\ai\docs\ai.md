 
根据对AI目录的分析，我整理了SandboxGame中所有AI行为类的功能列表：

## SandboxGame AI行为系统完整列表

1 *   **AIArrowAttack.cpp**: 远程攻击行为，AI会使用弓箭攻击目标。
2 *   **AIAtk.cpp**: 通用的攻击行为，是许多其他具体攻击行为的基础。
3 *   **AIAtkDestroyBlockTarget.cpp**: 攻击并摧毁目标方块。
4 *   **AIAtkTiangou.cpp**: "天狗"（一种特定生物）的攻击行为。
5 *   **AIAttractBlock.cpp**: AI被特定方块吸引并向其移动。
6 *   **AIBananaFan.cpp**: 使用香蕉扇（一种武器）进行攻击。
7 *   **AIBase.cpp**: AI行为的基类，定义了所有AI行为的通用接口和基础功能。
8 *   **AIBatAttack.cpp**: 蝙蝠的攻击行为，通常是近战。
9 *   **AIBatIdle.cpp**: 蝙蝠的待机行为，在没有攻击目标时执行。
10 *   **AIBeg.cpp**: AI向玩家乞求物品。
11 *   **AIBegEx.cpp**: 扩展的乞求行为，会根据一个预定义的“喜爱食物”列表向玩家乞求。
12 *   **AIBirdFishing.cpp**: 鸟类的捕鱼行为，专门攻击飞出水面的“飞鱼”。
13 *   **AIBoom.cpp**: 自爆行为，AI追逐目标，靠近后点燃自己并爆炸。
14 *   **AIBreakDoor.cpp**: 破坏门的行为，AI会持续攻击门直到将其破坏。
15 *   **AIBumpAttack.cpp**: 冲撞攻击，AI蓄力后冲向目标，击中后会将其撞飞，如果撞到障碍物自己会眩晕。
16 *   **AICeilingAtk.cpp**: 从天花板发动的攻击，AI倒挂在天花板上，落下攻击玩家并附着在玩家身上。
17 *   **AIChangeBlock.cpp**: 改变方块的行为，AI会寻找特定方块，飞过去停留一段时间，然后将其变为另一种方块（例如蝴蝶授粉）。
18 *   **AIClean.cpp**: 清洁行为，AI会以一定概率播放一个持续特定时间的“搓手”动作。
19 *   **AIClimbTree.cpp**: 爬树行为，AI会寻找并爬上树，在树上停留一段时间后再下来。
20 *   **AIClosestDance.cpp**:    集体跳舞行为，当一定数量的同类AI聚集时，它们会移动到一起并开始跳舞。
21 *   **AICombine.cpp**: 生物组合行为，当满足特定条件时，多个同种生物会合并成一个更强大的生物。
22 *   **AICraftItem.cpp**: 制作物品行为，AI会寻找工作台并使用身上的材料制作指定的物品。
23 *   **AICreateVacantVortex.cpp**:    创造一个虚空漩涡，这是一个特殊的技能，可能会对周围环境或玩家造成影响。
24 *   **AIDigBlock.cpp**: 挖掘方块行为，AI会挖掘特定类型的方块。
25 *   **AIDissolvedByItem.cpp**: 被特定物品溶解的行为，当AI接触到某个物品时会消失。
26 *   **AIDoorInteract.cpp**: 与门交互的基础行为，主要是寻找并移动到门的位置。
27 *   **AIEarthCoreManLash.cpp**: 地心人（特定生物）的挥鞭攻击行为。
28 *   **AIEarthCoreManRain.cpp**: 地心人（特定生物）的落雨攻击行为，能召唤一阵射弹攻击。
29 *   **AIEarthCoreManSteal.cpp**: 地心人（特定生物）的偷窃行为，会偷走玩家的物品。
30 *   **AIEatBlock.cpp**: 吃方块的行为，AI会吃掉特定类型的方块。
31 *   **AIEatFeedBlock.cpp**: 吃饲料方块的行为，通常用于圈养的动物。
32 *   **AIEatFlower.cpp**: 吃花的特定行为。
33 *   **AIEatFood.cpp**: 吃地上掉落的食物道具的行为。
34 *   **AIEatGrass.cpp**: 吃草皮的行为，吃完后草地会变成泥土。
35 *   **AIEatLeaf.cpp**: 吃树叶的行为。
36 *   **AIEatThenMutate.cpp**: AI在吃掉特定食物后会变异成另一种生物。
37 *   **AIEvade.cpp**: 闪避行为，AI会尝试躲开即将到来的攻击。
38 *   **AIFearItem.cpp**: 害怕特定物品，当玩家手持该物品时，AI会逃离玩家。
39 *   **AIFearPlayer.cpp**: 害怕玩家，当玩家靠近时会逃跑。
40 *   **AIFierce.cpp**: 凶猛化行为，AI在特定条件下（如被攻击）会变得更具攻击性。
41 *   **AIFireLord.cpp**:    “炎魔”Boss的行为控制器，一个通过计时器和Lua脚本回调驱动的复杂状态机，而非标准的AI任务。
42 *   **AIFishAttack.cpp**: 鱼类的攻击行为，包含了不同鱼种的特殊逻辑，例如水母的闪电链攻击和鲨鱼的冲刺攻击（可破坏方块或撕咬玩家）。
43 *   **AIFishBeg.cpp**:   鱼类的乞求行为，会被手持特定食物的玩家吸引并跟随，但玩家的快速移动会吓跑它们。
44 *   **AIFishFly.cpp**: 飞鱼的行为，在特定条件下（如天气、被追赶）会跃出水面滑翔一段距离。
45 *   **AIFishHoveringAround.cpp**:    鲨鱼的盘旋行为，在正式攻击前，会围绕目标（玩家或船）进行盘旋。
46 *   **AIFishSwarm.cpp**:    鱼群行为，管理鱼群的集体移动，包括领袖和跟随者逻辑，以及鱼群的分裂和合并。
47 *   **AIFishSwimToSurface.cpp**:    鱼类游到水面的行为，特指水母在攻击数次后会浮上水面“休息”并恢复生命。
48 *   **AIFleeSun.cpp**: 躲避阳光的行为，在白天会寻找阴影处躲藏以避免燃烧。
49 *   **AIFlyAttack.cpp**:    通用飞行生物的攻击行为，会追逐目标并在进入范围后进行近战攻击，可附带Buff效果。
50 *   **AIFlyAttract.cpp**:    飞行生物的吸引行为，会被佩戴特定物品（如花环）的玩家或其他生物吸引并跟随。
51 *   **AIFlyBeg.cpp**: 飞行生物的乞求行为，会被手持特定食物的玩家吸引并跟随。
52 *   **AIFlyFollow.cpp**: 飞行生物的跟随行为，会跟随一个指定的目标。
53 *   **AIFlyLoveBlock.cpp**: 飞行生物喜爱特定方块的行为，会飞向并停留在该方块上。
54 *   **AIFlyPanic.cpp**: 飞行生物的恐慌行为，当受到惊吓时会快速逃离。
55 *   **AIFlyStayFlower.cpp**: 飞行生物停留在花上的行为，类似于`AIFlyLoveBlock`，但专门针对花朵。
56 *   **AIFollowDirection.cpp**: 跟随方向移动，AI会朝着一个给定的方向持续移动。
57 *   **AIFollowOwner.cpp**: 跟随主人的行为，用于已被驯服的宠物。
58 *   **AIFollowParent.cpp**: 跟随父母的行为，用于幼年生物。
59 *   **AIGetSpecialAttackattr.cpp**:    获取特殊攻击属性，这是一个辅助行为，用于在特定条件下修改AI的攻击属性。
60 *   **AIGhostBombAttack.cpp**: 幽灵的炸弹攻击，会投掷炸弹攻击目标。
61 *   **AIGhostBumpAttack.cpp**: 幽灵的冲撞攻击，与`AIBumpAttack`类似，但可能是幽灵专属的版本。
62 *   **AIGhostIceAttack.cpp**: 幽灵的冰霜攻击，会使用冰系法术攻击目标。
63 *   **AIGoCeiling.cpp**: 移动到天花板的行为，AI会寻找并移动到天花板上。
64 *   **AIHatch.cpp**: 孵化行为，用于蛋类生物，在满足条件时孵化成新的生物。
65 *   **AIHoldMonster.cpp**: 抱起其他生物的行为，AI会尝试举起附近的小型生物。
66 *   **AIHunger.cpp**: 饥饿状态处理，当AI饥饿时，会触发寻找食物的行为。
67 *   **AIHungry.cpp**: 饥饿行为的另一个实现或变种，可能与其他饥饿相关的AI（如`AIHungryAtkTarget`）协同工作。
68 *   **AIHungryAtkTarget.cpp**: 饥饿时攻击目标，当AI饥饿时，会主动攻击可作为食物来源的目标。
69 *   **AIHungryDigEgg.cpp**: 饥饿时挖蛋，AI在饥饿时会去寻找并挖出埋藏的蛋来吃。
70 *   **AIHungryEatBlock.cpp**: 饥饿时吃方块，AI在饥饿时会吃掉特定类型的方块。
71 *   **AIHungryFollowPlayer.cpp**:    饥饿时跟随玩家，AI在饥饿状态下会悄悄地跟随玩家，一段时间后会转为攻击状态。
72 *   **AIHungryStatus.cpp**: 饥饿状态的表现，当AI饥饿时，会播放特殊的嚎叫动画和音效。
73 *   **AIIceWizardFindActor.cpp**:    冰法师寻找目标的行为，会寻找特定的植物（冰晶蕨），飞过去施法使其生长。
74 *   **AIIceWizardFly.cpp**: 冰法师的飞行行为，在一个固定区域内随机飞行。
75 *   **AIIceWizardProjectileAttack.cpp**:    冰法师的远程攻击，会发射两种不同的投射物，并且在血量低时会尝试与玩家拉开距离。
76 *   **AIItemPanic.cpp**: 害怕掉落物，当附近有特定的掉落物品时，AI会恐慌并逃跑。
77 *   **AIJumpGlissadeAmphibious.cpp**: 两栖生物的跳跃和滑行，在水中会跃出水面，在冰面上会滑行。
78 *   **AIKickAway.cpp**: 踢开同类，AI会随机踢开附近的同种生物。
79 *   **AILavaCrab.cpp**:    熔岩蟹Boss的复杂行为控制器，管理其在“消失”和“出现”两种状态下的不同AI逻辑（如常规游荡、反击、搜索攻击等）。
80 *   **AILayEggInNest.cpp**: 在巢里下蛋，AI会寻找巢穴方块，走过去并在其中产下蛋。
81 *   **AILayEggs.cpp**: 下蛋行为，AI会以一定概率随机产下一个或多个预设的蛋物品。
82 *   **AILeapAtTarget.cpp**: 跳向目标的行为，当目标在一定距离范围内时，AI会直接跳向目标。
83 *   **AILeopardAtk.cpp**: 豹子的攻击行为，这是一个复杂的攻击模式，包含了常规追击、高速奔跑、跳跃攻击（向上跳和向前扑）等多种状态。
84 *   **AILieAndRest.cpp**: 躺下休息的行为，AI会随机躺下并播放睡觉动画，持续一段时间。
85 *   **AILoggerHeads.cpp**: “伐木工”生物的愤怒行为，当进入“憎恨”状态时，会追逐并攻击同类。
86 *   **AILookIdle.cpp**: 随机四处张望的待机行为。
87 *   **AILoveBlock.cpp**: 喜爱方块的行为，AI会被特定方块吸引并向其移动。
88 *   **AIMakeTrouble.cpp**: 捣乱行为，AI会寻找并破坏或与特定方块互动。
89 *   **AIMate.cpp**: 交配行为，当两个相爱的AI靠近时，它们会进行交配并生下幼崽。
90 *   **AIMilking.cpp**: 产奶行为，AI会进入一个可被玩家“挤奶”的状态。
91 *   **AIMoveTowardsRestriction.cpp**: 移动到限制区域的行为，AI会尝试移动到其活动范围的中心。
92 *   **AIMutateFly.cpp**: 飞行生物的变异行为，当满足特定条件时，AI会变异成另一种生物。
93 *   **AIMutateTarget.cpp**: 变异目标，AI会寻找并使目标生物发生变异。
94 *   **AINpcSleep.cpp**: NPC的睡觉行为，会在指定的时间和地点睡觉。
95 *   **AIOriole.cpp**: 黄鹂鸟的特殊行为，这是一个空的实现，具体逻辑可能在其他地方或未完成。
96 *   **AIPanic.cpp**: 通用的恐慌行为，当受到伤害或惊吓时，AI会快速逃跑。
97 *   **AIPanicBuff.cpp**: 带有Buff效果的恐慌行为，在逃跑的同时会给自己或周围的生物施加一个Buff。
98 *   **AIPatrolOnBlock.cpp**: 在方块上巡逻，AI会在一个预设的方块列表上进行巡逻。
99 *   **AIPetDanceToPlayer.cpp**: 宠物为玩家跳舞的行为。
100 *   **AIPetFollowOwner.cpp**: 宠物跟随主人的行为，是`AIFollowOwner`的宠物版本。
101 *   **AIPetPlayToPlayer.cpp**: 宠物与玩家玩耍的行为。
102 *   **AIPetWander.cpp**: 宠物的游荡行为，在主人附近小范围随机移动。
103 *   **AIPickupItem.cpp**: 捡起物品的行为，AI会捡起地上的特定物品。
104 *   **AIPickupItemEx.cpp**: 扩展的捡起物品行为，可能包含更复杂的逻辑，例如捡起后存储或使用。
105 *   **AIPlant.cpp**: 种植行为，AI会在合适的地面上种植特定的植物。
106 *   **AIPlayerPanic.cpp**: 因玩家而恐慌，当玩家做出特定动作（如拿出武器）时，AI会逃跑。
107 *   **AIPosWander.cpp**: 在指定位置附近游荡，AI会在一个中心点周围的固定范围内随机移动。
108 *   **AIProjectileAttack.cpp**: 通用的投射物攻击行为，AI会向目标发射投射物。
109 *   **AIRanchWander.cpp**: 在牧场范围内游荡，是`AIPosWander`的一个变种，专门用于牧场中的动物。
110 *   **AIRandomFly.cpp**: 随机飞行的行为，飞行生物会在空中随机改变方向和高度。
111 *   **AIRandomSwim.cpp**:  随机游泳行为，水生生物会在水中随机游动，并且可以根据天气（暴风雨）调整游泳的高度范围。
112 *   **AIRestrictSun.cpp**: 限制在阳光下的行为，这是一个状态切换，当白天时，会给AI添加一个“避免阳光”的标记。
113 *   **AIRideHorse.cpp**: 骑马（或其他生物）的行为，AI会寻找附近可骑乘的生物并骑上它。
114 *   **AISavageSleep.cpp**: 野人睡觉行为，在亮度低时，野人会躺下睡觉。
115 *   **AISavageStandSleep.cpp**: 野人站着睡觉的行为，在白天且亮度足够高时，野人会站着打盹。
116 *   **AISeparate.cpp**: 分裂行为，当AI死亡时，会分裂成两个新的、更小的生物。
117 *   **AISeparatePanic.cpp**: 分裂后的恐慌行为，新分裂出的生物会进入恐慌状态并四处逃窜。
118 *   **AISit.cpp**: 坐下的行为，用于已被驯服的宠物，当主人命令其坐下时执行。
119 *   **AISitBlock.cpp**: 坐在方块上的行为，AI会寻找特定的方块并坐在上面。
120 *   **AISitbyItem.cpp**: 根据主人的命令坐下，与`AISit` 类似，但会记录坐下的位置，并在主人取消命令后尝试回到原位。
121 *   **AISleep.cpp**: 通用的睡觉行为，AI在亮度低时会睡觉。
122 *   **AISpecialAct.cpp**: 特殊动作行为，AI会以一定概率播放一个预设的特殊动画。
123 *   **AIStandGuard.cpp**: 站岗行为，AI会保持在原地不动，像一个守卫。
124 *   **AIStayBlock.cpp**: 停留在方块上的行为，AI会寻找并停留在特定方块上。
125 *   **AIStoreItem.cpp**: 存储物品的行为，AI会将捡到的物品存放到附近的箱子里。
126 *   **AISwimming.cpp**: 游泳行为，这是一个状态切换，当AI在水中时，会激活游泳相关的物理特性。
127 *   **AITakeItem.cpp**: 从容器拿取物品的行为，AI会从附近的箱子中拿取指定的物品。
128 *   **AITarget.cpp**: 目标系统的基类，定义了寻找和设置攻击目标的基本逻辑。
129 *   **AITargetFollowingPlayer.cpp**: 以跟随玩家的生物为目标，AI会攻击正在跟随玩家的生物（例如宠物）。
130 *   **AITargetHurtee.cpp**: 以伤害自己的人为目标，AI会攻击最近伤害过自己的目标。
131 *   **AITargetNearest.cpp**: 以最近的生物为目标，AI会攻击范围内最近的、符合条件的生物。
132 *   **AITargetNonTamed.cpp**: 以未被驯服的生物为目标，专门攻击那些没有主人的同类。
133 *   **AITargetOwnerHurtee.cpp**: 以伤害其主人的生物为目标，用于宠物，当主人被攻击时会反击。
134 *   **AITargetOwnerHurter.cpp**: 以其主人正在攻击的生物为目标，用于宠物，会协助主人攻击。
135 *   **AITargetScream.cpp**:  尖叫并呼叫同伴，当发现目标时，AI会发出尖叫，吸引附近的同类共同攻击该目标。
136 *   **AITargetSimilar.cpp**: 以同类为目标，AI会攻击与自己种类相似的生物。
137 *   **AITargetSpecificEntity.cpp**: 以特定生物为目标，AI只会攻击预设列表中的特定种类的生物。
138 *   **AITask.cpp**: AI任务的基类，为更复杂的、基于任务的AI系统提供基础。
139 *   **AITaskToLua.cpp**: 将AI任务委托给Lua脚本执行，提供了一个将C++ AI逻辑与Lua脚本连接的桥梁。
140 *   **AITempt.cpp**: 引诱行为，AI会被手持特定食物的玩家吸引并跟随。
141 *   **AIThief.cpp**: 偷窃行为，AI会尝试靠近玩家并偷走其物品，成功后会逃跑。
142 *   **AIThrob.cpp**: “嘟嘟”鸟（特定生物）的脉动行为，会播放一个特殊的动画和声音。
143 *   **AIToppleOver.cpp**: 装死/倾倒行为，用于宠物，当主人命令时会执行。
144 *   **AITransfiguration.cpp**: 变形行为，AI在等待一段时间后会变成另一种指定的生物。
145 *   **AiVacant.cpp**:虚空之主Boss（第一阶段）的行为控制器，一个极其复杂的、基于事件和计时器的状态机，管理Boss的飞行、冲刺、召唤、传送、吸血等多种技能。
146 *   **AiVacant2.cpp**:  虚空之主Boss（第二阶段）的行为控制器，同样是一个复杂的状态机，管理Boss的捶地、扔石头、召唤其他Boss等技能。
147 *   **AIWander.cpp**: 通用的游荡行为，AI会在地面上随机走动。
148 *   **AIWanderAmphibious.cpp**: 两栖生物的游荡行为，在水中和陆地上有不同的移动逻辑。
149 *   **AIWarning.cpp**: 警戒行为，当被惊吓后，AI会进入警戒状态，四处张望索敌，如果发现敌人则会转为攻击。
150 *   **AIWatchClosest.cpp**: 注视最近玩家的行为，AI会一直朝向最近的玩家。
151 *   **AIWizardAttack.cpp**: 巫师的攻击行为，这是一个空的实现，具体逻辑可能在其他地方或未完成。
152 *   **AIWizardFly.cpp**: 巫师的飞行行为，这是一个空的实现，具体逻辑可能在其他地方或未完成。
153 *   **AIWizardProjectileAttack.cpp**: 巫师的投射物攻击行为，这是一个空的实现，具体逻辑可能在其他地方或未完成。