#ifndef __HP_PROGRESS_COMPONENT_H__
#define __HP_PROGRESS_COMPONENT_H__

//
#include "SandboxGame.h"
#include "ActorTypes.h"
//#include "world_types.h"
#include "ActorComponent_Base.h"

struct OneAttackData;
class ActorLiving;
class ClientActor;
class ClientMob;
class ClientPlayer;
class EXPORT_SANDBOXGAME HPProgressComponent;
class HPProgressComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(HPProgressComponent)
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	//tolua_begin
	HPProgressComponent();

	ATTACK_TYPE getLastAttackedType() { return m_WasAttackedType; };
	void setLastAttackedType(ATTACK_TYPE type){ m_WasAttackedType = type;};
	int getHurtType(){return m_nInjuredTypeForAITree;};
	void initHurtTypePerTick(){ m_nInjuredTypeForAITree = 0;};
	void onAttackedFrom(OneAttackData &atkdata, ClientActor *attacker);

	virtual void addHPEffect(float hp);
	void updateLastAttackTime();
	void setHPProgressDirty();

	void livingHPtick();

	virtual bool checkNeedTickFactorForHPProgress(){return false;};
	virtual void updateHPProgress(){};

	void mobHPTick();
	//tolua_end
	void setHpVisible(bool visible);
protected:
	ActorLiving* m_owner;
	ATTACK_TYPE m_WasAttackedType;
	int m_nInjuredTypeForAITree;		//给行为树判断受伤类型，0为没有受伤，1为受到场景伤害，2为受到活物伤害
	bool m_isHPProgressEnabled;
	int m_lastAttackTime;
	int m_lastAttackedTime;
	bool m_isHPProgressDirty;
	bool m_currentHurtIsCritical;
	bool m_showHP; //是否显示血条 用于行为树上使用
	int m_lastMainPlayerTeamID;	

	// 缓存配置值 避免重复获取  20210831 codeby:liusijia
	int m_playerHPSwitchFlag;
	int m_playerDamagewitch;
}; //tolua_exports

class MobHPProgressComponent : public HPProgressComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(MobHPProgressComponent)

	//tolua_begin
	MobHPProgressComponent();
	virtual bool checkNeedTickFactorForHPProgress()override;
	//tolua_end
protected:
	virtual void updateHPProgress()override;
	
	ClientMob* m_ownerMob;
	int m_lastTamedOwnerTeamId;
}; //tolua_exports

class PlayerHPProgressComponent : public HPProgressComponent //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(PlayerHPProgressComponent)
public:

	//tolua_begin
	PlayerHPProgressComponent();
	//virtual bool checkNeedTickFactorForHPProgress()override;
	//tolua_end
protected:
	virtual void updateHPProgress()override;
	ClientPlayer* m_ownerPlayer;
}; //tolua_exports


class NewHPProgressComponent : public HPProgressComponent //tolua_exports
{
private:
		int mNewValue = 0;

};
class PlayerArmorProgressComponent : public HPProgressComponent //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	PlayerArmorProgressComponent(ClientPlayer* owner);
	void addHPEffect(float hp);
	//virtual bool checkNeedTickFactorForHPProgress()override;
	//tolua_end
protected:
	virtual void updateHPProgress()override;
	ClientPlayer* m_ownerPlayer;
}; //tolua_exports
#endif