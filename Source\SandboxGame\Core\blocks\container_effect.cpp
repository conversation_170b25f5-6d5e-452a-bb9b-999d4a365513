
#include "IClientActor.h"
#include "container_effect.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "world.h"
#include "IPlayerControl.h" 
#include "GameMode.h"
#include "WorldManager.h"
#include "SandboxEventDispatcherManager.h"
#include "ClientActor.h"
#include "ClientActorHelper.h"
#include "SandboxIdDef.h"
#include "PlayerControl.h" 
using namespace MNSandbox;
static const char *s_ContainerFX[] = 
{
	"particles/portal.ent",
	"particles/item_990.ent",
	"particles/item_991.ent",
	"particles/item_992.ent",
	"particles/item_993.ent",
	"particles/item_994.ent",
	"particles/item_995.ent",
	"particles/item_996.ent",
	"particles/star.ent",
	"particles/ReadyPoint.ent",
	"particles/item_1001.ent",
	"particles/item_1002.ent",
	"particles/item_1003.ent",
	"particles/item_1003_hong.ent",
	"particles/item_1003_lan.ent",
	"particles/item_1003_lv.ent",
	"particles/item_1003_huang.ent",
	"particles/item_1003_cheng.ent",
	"particles/item_1003_zi.ent",
	"particles/item_key_dj.ent",
	"particles/item_key_djf.ent",
};

WorldEffectContainer::WorldEffectContainer()
	: m_EffectID(0), m_EffectEx(-1), m_FX(NULL), m_Offset(0, 0, 0), m_Dir(0), m_isShow(true), m_reset(false)
{
	m_NeedTick = true;
}

WorldEffectContainer::WorldEffectContainer(int id, const WCoord &blockpos, const WCoord &offset, int dir)
	: WorldContainer(blockpos, 0), m_EffectID(id), m_EffectEx(-1), m_FX(NULL), m_Offset(offset), m_Dir(dir), m_isShow(true), m_reset(false)
{
	m_NeedTick = true;
}

WorldEffectContainer::~WorldEffectContainer()
{
}

int WorldEffectContainer::getObjType() const
{
	return OBJ_TYPE_EFFECT;
}

void WorldEffectContainer::setScale(float s)
{
	if(m_FX) m_FX->setScale(s);
}

void WorldEffectContainer::show(bool b)
{
	m_isShow = b;
	if(m_FX) m_FX->show(b);
}

float WorldEffectContainer::dir2Yaw(int dir)
{
	static float yaw[4] = {90.0f, 270.0f, 0.0f, 180.0f};
	return yaw[dir];
}

void WorldEffectContainer::createFx()
{
#ifndef IWORLD_SERVER_BUILD
	if (m_FX || (m_World == NULL))
		return;
	WCoord pos = m_BlockPos*BLOCK_SIZE + m_Offset;
	if(m_EffectID==CONTAINEFX_PORTAL && m_World->getCurMapID()==1)
	{
		m_FX = ENG_NEW(EffectParticle)(m_World, "particles/portal_1.ent", pos, 0);
	}
	else if ( m_EffectID == CONTAINEFX_ACTIVEPERSONAL) // 个人复活点
	{
		int showid = m_EffectID;
		
		if (g_pPlayerCtrl)
		{
			SandboxResult result;
			SandboxContext sandboxContext;
			g_pPlayerCtrl->Event2().Emit<SandboxResult &, SandboxContext >("revive_getRevivePointEx", result, sandboxContext);
			if (result.GetData_UserObject<WCoord>("point") == m_BlockPos)
				showid = CONTAINEFX_ACTIVETEAMSPAWN;
		}
		m_FX = ENG_NEW(EffectParticle)(m_World, s_ContainerFX[showid], pos, 0);
		m_EffectEx = showid;
	}
	else if ( m_EffectID >= CONTAINEFX_TEAMSPAWN0 && m_EffectID < CONTAINEFX_TEAMSPAWN0 + MAX_GAME_TEAMS ) // 重生复活点
	{
		int showid = m_EffectID;
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getFixedPoint(m_World->getBlockID(m_BlockPos) - BLOCK_TEAMSPAWN0) == m_BlockPos)
		{
			showid = CONTAINEFX_ACTIVETEAMSPAWN;
		}
		if (showid > CONTAINEFX_ACTIVETEAMSPAWN)
		   showid = CONTAINEFX_ACTIVETEAMSPAWN;
		m_FX = ENG_NEW(EffectParticle)(m_World, s_ContainerFX[showid], pos, 0);
		m_EffectEx = showid;
	}
	else if ( m_EffectID == CONTAINEFX_ACTIVETEAMSPAWN)
	{
		int showid = m_EffectID;
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getFixedPoint(m_World->getBlockID(m_BlockPos) - BLOCK_TEAMSPAWN0) != m_BlockPos)
		{
			showid = CONTAINEFX_TEAMSPAWN0 + m_World->getBlockID(m_BlockPos) - BLOCK_TEAMSPAWN0;
		}
		if (showid > 7 || showid < 0)
			showid = 7;
		m_FX = ENG_NEW(EffectParticle)(m_World, s_ContainerFX[showid], pos, 0);
		m_EffectEx = showid;
	}
	else
	{
		m_FX = ENG_NEW(EffectParticle)(m_World, s_ContainerFX[m_EffectID], pos, 0);
	}
	m_FX->setRotation(dir2Yaw(m_Dir), 0, 0);
	if(!m_isShow) m_FX->show(false);

	m_World->getEffectMgr()->addEffect(m_FX);
#endif
}


void WorldEffectContainer::enterWorld(World *pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	if(pworld->onClient())
	{
		createFx();
	}
}

void WorldEffectContainer::leaveWorld()
{
	if(m_FX) m_FX->setNeedClear();

	WorldContainer::leaveWorld();
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldEffectContainer::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	auto offset = WCoordToCoord3(m_Offset);
	auto actor = FBSave::CreateContainerEffect(builder, basedata, m_EffectID,  m_Dir, &offset, m_isShow?1:0);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerEffect, actor.Union());
}

bool WorldEffectContainer::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerEffect *>(srcdata);
	loadContainerCommon(src->basedata());

	m_EffectID = src->effectid();
	m_Dir = src->dir();
	m_Offset = Coord3ToWCoord(src->offset());
	m_isShow = src->isshow()!=0;
	return true;
}

void WorldEffectContainer::updateTick()
{
	if (!m_World)
		return;

	if ( m_World->getBlockID(m_BlockPos) == BLOCK_PERSONALSPAWN)
	{
		if (g_pPlayerCtrl)
		{

			SandboxResult result;
			SandboxContext sandboxContext;
			g_pPlayerCtrl->Event2().Emit<SandboxResult &, SandboxContext >("revive_getRevivePointEx", result, sandboxContext);
			if (result.GetData_UserObject<WCoord>("point") == m_BlockPos)
			{
				if (m_EffectEx == CONTAINEFX_ACTIVEPERSONAL)
				{
					refresh();
					createFx();
					updateContainingBlockInfo();
				}
			}
			else
			{
				if (m_EffectEx == CONTAINEFX_ACTIVETEAMSPAWN)
				{
					refresh();
					createFx();
					updateContainingBlockInfo();
				}
			}
		}
	}
	else if ( m_World->getBlockID(m_BlockPos) >= BLOCK_TEAMSTART0 && m_World->getBlockID(m_BlockPos) < BLOCK_TEAMSTART0 + MAX_GAME_TEAMS ) { // 初始出生点
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() ) { // 玩法模式
			
			if (GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getInitialPoint(m_World->getBlockID(m_BlockPos)-BLOCK_TEAMSTART0) != m_BlockPos ) // 不是有效的初始出生点
			{
				m_World->getContainerMgr()->destroyContainer(m_BlockPos);
			}
		}
	}
	else if ( m_World->getBlockID(m_BlockPos) >= BLOCK_TEAMSPAWN0 && m_World->getBlockID(m_BlockPos) < BLOCK_TEAMSPAWN0 + MAX_GAME_TEAMS ) // 重生复活点
	{ 
	    if(GetWorldManagerPtr() == NULL) return;
		if ( GetWorldManagerPtr()->m_RuleMgr )
		{
			if ( GetWorldManagerPtr()->m_RuleMgr->getFixedPoint(m_World->getBlockID(m_BlockPos) - BLOCK_TEAMSPAWN0) == m_BlockPos ) // 激活
			{
				if (m_EffectEx != CONTAINEFX_ACTIVETEAMSPAWN)
				{
					refresh();
					createFx();
					updateContainingBlockInfo();
				}
			}
			else
			{
				if (m_EffectEx == CONTAINEFX_ACTIVETEAMSPAWN) // 未激活但系激活的特效
				{
					refresh();
					createFx();
					updateContainingBlockInfo();
				}
			}
		}
	}
	else if (m_World->getBlockID(m_BlockPos) == BLOCK_KEY_OF_BROKEN_SWORD)
	{
		if ((m_World->getBlockData(m_BlockPos) & 4) && m_EffectID == CONTAINEFX_KEYOFBROKENSWORD)
		{
			m_EffectID = CONTAINEFX_KEYOFBROKENSWORDSEAL;
			refresh();
			createFx();
			updateContainingBlockInfo();
		}
		else if ((m_World->getBlockData(m_BlockPos) & 4) == 0 && m_EffectID == CONTAINEFX_KEYOFBROKENSWORDSEAL)
		{
			m_EffectID = CONTAINEFX_KEYOFBROKENSWORD;
			refresh();
			createFx();
			updateContainingBlockInfo();
		}
	}
}

void WorldEffectContainer::refresh()
{
	if ( m_FX )
	{
		m_FX->setNeedClear(); // save in an array, EffectManger will delete it
	}
	m_FX = nullptr;
}



