﻿#include "ClientMob.h"
#include "SandboxSceneObject.h"




int ClientMob::MonsterIdGet() const
{
	return m_monsterId;
}

void ClientMob::MonsterIdSet(const int& value)
{
	setMonsterId(value);

	transformation(m_monsterId);

	OnAttributeChanged(this, &R_MonsterId);
}

const std::string& ClientMob::DisplayNameGet() const
{
	return m_displayName;
}
void ClientMob::DisplayNameSet(const std::string& value)
{
	setDisplayName(value);
	applyDisplayName();
}
//void ClientMob::LocomotionPosGet(Rainbow::Vector3f& value) const
//{
//	value = m_LocoMotion->getPosition().toVector3();
//}
//void ClientMob::LocomotionPosSet(const Rainbow::Vector3f& value)
//{
//	if (m_LocoMotion)
//	{
//		m_LocoMotion->setPosition(value.x, value.y, value.z);
//	}
//}





