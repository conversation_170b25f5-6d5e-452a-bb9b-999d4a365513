#ifndef __INTERACT_TAMED_MOB_COMPONENT_H__
#define __INTERACT_TAMED_MOB_COMPONENT_H__

#include <string>
//#include "WorldRole_generated.h"
#include "ActorComponent_Base.h"

class ClientMob;
class ClientPlayer;

class InteractTamedMobComponent : public ActorComponentBase
{
public:
	DECLARE_COMPONENTCLASS(InteractTamedMobComponent)

	InteractTamedMobComponent();

	void InteractMobPack(const std::string& name, const std::string& param, long long mobID);
	virtual bool InteractMobItem(int fromIndex, int toIndex);
	
	void moveMobItem(int gridIndex, int moveType,int toGridIndex = -1);
	void PickMobBackpack(int gridIndex, int moveType = 1, int toGridIndex=-1);
protected:
	long long m_InteractTamedMobID;
	ClientPlayer* m_owner;
};

class MPInteractTamedMobComponent : public InteractTamedMobComponent
{
	DECLARE_COMPONENTCLASS(MPInteractTamedMobComponent)
public:

	MPInteractTamedMobComponent();
	virtual bool InteractMobItem(int fromIndex, int toIndex) override;	
};

#endif