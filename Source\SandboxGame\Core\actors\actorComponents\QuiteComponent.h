#ifndef __QUITE_COMPONENT_H__
#define __QUITE_COMPONENT_H__

#include "ActorComponent_Base.h"

class ClientMob;

class QuiteComponent : public ActorComponentBase//tolua_exports
{//tolua_exports
public:
	DECLARE_COMPONENTCLASS(QuiteComponent)

	QuiteComponent();
	~QuiteComponent();

	int getInQuite()
	{
		return m_InQuite;
	}

	bool isInQuite()
	{
		return m_InQuite > 0;
	}

	void setQuiteTick(int tick) { 
		m_InQuite = tick; 
	}

	void setReadyToQuit(int num) { 
		m_iReadyToQuit = 0; 
	}

	void setRealQuit(int num) { 
		m_iRealQuitTick = 0; 
	}

	int getReadyToQuit()
	{
		return m_iReadyToQuit;
	}

	int getRealQuit()
	{
		return m_iRealQuitTick;
	}

	//void quiteTick();
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
protected:
	ClientMob* m_owner;

	int m_InQuite; //安静状态
	int m_iReadyToQuit; //准备进入Quit状态的等待tick
	int m_iRealQuitTick; //等待完成后m_InQuit的值

};//tolua_exports

#endif