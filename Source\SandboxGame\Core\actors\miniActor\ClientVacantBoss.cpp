#include "ClientVacantBoss.h"
#include "AiVacant.h"
#include "AiVacant2.h"
#include "navigationpath.h"
#include "LivingLocoMotion.h"
#include "ActorBody.h"
#include "ActorVision.h"
#include "PlayerControl.h"
#include "ChunkGen_Normal.h"
#include "BiomeRegionGenConfig.h"
#include "display/WorldRender.h"
#include "GameNetManager.h"
#include "SunHurtComponent.h"
#include "ClientActorHelper.h"
#include "RiddenComponent.h"
#include "ActionAttrStateComponent.h"
#include "Text3D/NameText3D.h"
#include "MobAttrib.h"
#include "SkyPlane.h"
#include "EffectManager.h"
#include "chunk.h"

using namespace MINIW;
using namespace Rainbow;
IMPLEMENT_SCENEOBJECTCLASS(ClientVacantBoss)
ClientVacantBoss::ClientVacantBoss() : m_AIBoss(NULL)
{
	m_SeePlayerControl = false;
	m_LastHP = 0;
	m_shieldTime = 0;

	m_TotemDeath = SANDBOX_NEW(EcosysUnitVoxelModel, "voxel/voidtotem.vox", -4);

	auto sunHurtComponent = GetComponent<SunHurtComponent>();
    if (sunHurtComponent)
    {
		sunHurtComponent->SetCanPlayEffect(false);
    }
}

ClientVacantBoss::~ClientVacantBoss()
{
	//ge GetGameEventQue().postBossState(m_Def->ID, -1);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("id", m_Def->ID).
		SetData_Number("hp", -1);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_UPDATE_BOSS_STATE", sandboxContext);
	if (m_AIBoss)
	{
		//m_AIBoss->Release();
		SANDBOX_DELETE(m_AIBoss);
	}

	SANDBOX_DELETE(m_TotemDeath);
}

bool ClientVacantBoss::init(int monsterid)
{
	const MonsterDef *def = GetDefManagerProxy()->getMonsterDef(monsterid, true);
	//Dynamic mod load. Wait to be fixed. rivershen 20170227
	if (def == NULL)
	{
		return false;
	}
	m_Def = def;
	this->setTeam(m_Def->TeamID);
	ENG_DELETE(m_Body);
	m_Body = ENG_NEW(ActorBody)( this);
	ClientMob::initMobBody(m_Body, m_Def);
	m_Body->setNeedUpdateAnim(true);
	
	newLocoMotion();
	getLocoMotion()->setBound((int)(def->Height*def->ModelScale), (int)(def->Width*def->ModelScale));
	getLocoMotion()->setAttackBound(
		(int)( def->HitHeight*def->ModelScale ), 
		(int)( def->HitWidth*def->ModelScale ), 
		(int)( def->HitThickness*def->ModelScale ));
	CreateComponent<ActorVision>("ActorVision");

	MobAttrib *attrib = NULL;
	attrib = CreateComponent<MobAttrib>("MobAttrib");
	if (!attrib) return false;
	attrib->init(def);
	attrib->setImmuneToFire(2);
	CreateComponent<NavigationPath>("NavigationPath");
	//可变部分用脚本实现
	char szScriptFun[256];
	snprintf(szScriptFun, sizeof(szScriptFun), "F%d_Init", m_Def->ID);
	MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]", this);
	
	if (m_Def->ModelType == MONSTER_CUSTOM_MODEL || m_Def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
	{
		snprintf(szScriptFun, sizeof(szScriptFun), "FCustomActor_SetAi");
	}
	else
	{
		snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SetAi", m_Def->ID);
	}

	MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientVacantBoss]", this);

	applyDisplayName();
	getBody()->setNeedUpdateSkinEffect(false);
	getBody()->setNeedUpdateRenderYawOffset(false);
	getBody()->setControlRotation(false);
	m_Mass = 1000;
	return true;
}

void ClientVacantBoss::enterWorld(World *pworld)
{
	ActorBoss::enterWorld(pworld);
	GetWorldManagerPtr()->addBossPoint(CoordDivBlock(getPosition()), m_pWorld->getCurMapID());
	if (0 == pworld->getCurMapID())
	{
		WorldRenderer* world = pworld->getRender();
		if (world)
		{
			Rainbow::SkyPlane* sky = world->getSky();
			if (sky)
				sky->setPlanet(PLANET_VOLCANO);
		}
		GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_BOSS_IS_APPEAR, SandBoxMgrTypeID::SB_BOSS_APPEAR);
	}
	if (!m_pWorld->isRemoteMode())
	{
		if (m_Def->ID == 3515)
		{
			addMissionFlags(1);
			syncToClientDialogue(85607);
		}
		else if (m_Def->ID == 3516)
		{
			addMissionFlags(2);
			syncToClientMission(2);
		}
	}
}

void ClientVacantBoss::applyDisplayName()
{
#ifndef IWORLD_SERVER_BUILD
	if (getBody() && m_Def)
	{
		std::string nameStr = m_Def->Name.c_str() ;
		bool nameDisPlay = m_Def->NameDisPlay;
		getBody()->setDispayName(nameStr.c_str(), 0);
		getBody()->getNameDispObj()->setDescExtraOneText(NULL);
		getBody()->getNameDispObj()->setDescVisible(m_Def->DescDisplay);
		getBody()->getNameDispObj()->setDescText(m_Def->Desc.c_str());
		getBody()->getNameDispObj()->setVisible(nameDisPlay);
		getBody()->getNameDispObj()->setTextColor(239, 128, 18);
		getBody()->getNameDispObj()->setZTest(true);
	}
#endif
}



//clientactor->ActorLiving->clientmob->
void ClientVacantBoss::tick()
{
	/*	ClientMob::tick();
	*/
	//-------------------------ClientActor tick begin-------------------------
	//ActorBoss::tick();
	getLocoMotion()->tick();
	getLocoMotion()->doBlockCollision();
	getLocoMotion()->doPickThrough();
	if (getAttrib()) getAttrib()->tick();
	if (getBody()) getBody()->tick();
	//-------------------------ClientActor tick end-------------------------

	mobHPTick();

	float vr = (float)m_Def->ViewDistance*BLOCK_SIZE;
#ifndef IWORLD_SERVER_BUILD
	bool see_playerctrl = getSquareDistToActor(g_pPlayerCtrl) < vr*vr;
#else
	bool see_playerctrl = false;
#endif
	if (m_SeePlayerControl && !see_playerctrl)
	{
		//ge GetGameEventQue().postBossState(m_Def->ID, -1);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("id", m_Def->ID).
			SetData_Number("hp", -1);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_UPDATE_BOSS_STATE", sandboxContext);
	}
	else if (!m_SeePlayerControl && see_playerctrl)
	{
		//ge GetGameEventQue().postBossState(m_Def->ID, -2);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("id", m_Def->ID).
			SetData_Number("hp", -2);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_UPDATE_BOSS_STATE", sandboxContext);
		m_LastHP = -1000000.0f;
	}
	m_SeePlayerControl = see_playerctrl;

	if (m_SeePlayerControl && m_LastHP != getAttrib()->getHP())
	{
		m_LastHP = getAttrib()->getHP();
		//ge GetGameEventQue().postBossState(m_Def->ID, (int)getAttrib()->getHP());
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("id", m_Def->ID).
			SetData_Number("hp", (int)getAttrib()->getHP());
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_UPDATE_BOSS_STATE", sandboxContext);
	}
	if (m_Def->ID == 3516)
	{
		m_shieldTime++;
		if (m_shieldTime >= 15 && !isDead())
		{
			m_shieldTime = 0;
			// 小护盾 大护盾
			if (getLivingAttrib()->hasBuff(VACANT_SAFE_BUFF_LITTLE) || getLivingAttrib()->hasBuff(VACANT_SAFE_BUFF_EX))
			{
				notifShieldState(1);
			}
			else
			{
				notifShieldState(0);
			}
		}
	}

	if (m_pWorld->isRemoteMode())
	{
		return;
	}



	//-------------------------ActorLiving tick begin-------------------------

	attackTick();

	//-------------------------ClientMob tick begin-------------------------
	if(m_pWorld == NULL) return;
	livingHPtick();
	
	if (m_MissionFlags == 2)
	{
		if (m_Def->ID == 3516)
		{
#ifdef IWORLD_SERVER_BUILD
			if (m_pWorld->getChunkBySCoord(CoordDivSection(getPosition().x), CoordDivSection(getPosition().z)))
			{
				startAppeal();
				addMissionFlags(4);
				syncToClientMission(3);
			}
#else
			if (m_pWorld->getChunkBySCoord(CoordDivSection(getPosition().x), CoordDivSection(getPosition().z)))
				startAppeal();

			if (m_SeePlayerControl)
			{
				addMissionFlags(4);
				syncToClientMission(3);
			}
#endif
		}
	}
}

ActorLocoMotion * ClientVacantBoss::newLocoMotion()
{
	LivingLocoMotion* loco = CreateComponent<LivingLocoMotion>("LivingLocoMotion");
	loco->initMoveAbility(MoveAbilityType::FlyLoc);
	loco->m_SpeedInAir = getAirSpeed();
	return loco;
}
extern flatbuffers::Offset<flatbuffers::Vector<const FBSave::ActorBuff*>> SaveActorBuffs(SAVE_BUFFER_BUILDER& builder, LivingAttrib* attrib);
/*
flatbuffers::Offset<flatbuffers::Vector<const FBSave::ActorBuff *>> SaveActorBuffs(SAVE_BUFFER_BUILDER &builder, LivingAttrib *attrib)
{
	std::vector<FBSave::ActorBuff>buffarray;
	if(attrib == NULL) return builder.CreateVectorOfStructs(buffarray);
	buffarray.reserve(attrib->m_Buffs.size());

	for(size_t i=0; i<attrib->m_Buffs.size(); i++)
	{
		ActorBuff &b = attrib->m_Buffs[i];
		buffarray.push_back(FBSave::ActorBuff(b.buffid, b.bufflv, b.ticks));
	}

	return builder.CreateVectorOfStructs(buffarray);
}*/

bool ClientVacantBoss::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorVacantBoss *>(srcdata);
	init(src->defid());

	loadActorCommon(src->basedata());

	getAttrib()->initHP(src->hp());
	if (version == 0) getAttrib()->initHP(getAttrib()->getHP()*5.0f);

	MobAttrib *attrib = static_cast<MobAttrib *>(getAttrib());
	if(attrib != NULL) {
		auto buffs = src->buffs();
		if (buffs)
		{
#if 0
			if (attrib->isNewStatus()) {
				for (size_t i = 0; i < buffs->size(); i++) {
					const FBSave::ActorBuff* srcbuff = buffs->Get(i);

					attrib->addBuff(srcbuff->buffid(), srcbuff->bufflv(), srcbuff->ticks());
				}
			}
			else {
				attrib->m_Buffs.resize(buffs->size());

				for (size_t i = 0; i < buffs->size(); i++)
				{
					const FBSave::ActorBuff* srcbuff = buffs->Get(i);
					ActorBuff& dest = attrib->m_Buffs[i];

					dest.buffid = srcbuff->buffid();
					dest.bufflv = srcbuff->bufflv();
					dest.ticks = srcbuff->ticks();

					dest.def = GetDefManagerProxy()->getBuffDef(dest.buffid, dest.bufflv);
				}
			}
#else
			for (size_t i = 0; i < buffs->size(); i++) {
				const FBSave::ActorBuff* srcbuff = buffs->Get(i);

				attrib->addBuff(srcbuff->buffid(), srcbuff->bufflv(), srcbuff->ticks());
			}
#endif
		}
	}

	m_MissionFlags = src->missionflags();

	const FBSave::Coord3* pos = src->spawnpos();
	if (pos)
	{
		m_SpawnPoint = Coord3ToWCoord(pos);
	}
	else
	{
		m_SpawnPoint = getPosition();
	}
	getLocoMotion()->m_Motion = Rainbow::Vector3f::zero;
	LivingLocoMotion *loco = static_cast<LivingLocoMotion *>(getLocoMotion());
	if (loco)
	{
		if (src->moveTarget())
			loco->m_MoveTarget = Coord3ToWCoord(src->moveTarget());
		loco->m_SyncPos = m_SpawnPoint;
	}
	return true;
}

flatbuffers::Offset<FBSave::SectionActor> ClientVacantBoss::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	MobAttrib *attrib = static_cast<MobAttrib *>(getAttrib());
	FBSave::Coord3 pos = WCoordToCoord3(m_SpawnPoint);
	LivingLocoMotion *loco = static_cast<LivingLocoMotion *>(getLocoMotion());
	auto moveTarget = WCoordToCoord3(loco->m_MoveTarget);
	auto buffs = SaveActorBuffs(builder, attrib);

	auto obj = FBSave::CreateActorVacantBoss(builder, basedata, m_Def->ID, getAttrib()->getHP(), m_MissionFlags, &pos, buffs, &moveTarget);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorVacantBoss, obj.Union());
}

void ClientVacantBoss::applyActorCollision(ClientActor *actor)
{
	//if (actor->m_RidingActor != getObjId() && actor->m_RiddenByActor != getObjId())
	auto actorRidComp = actor->getRiddenComponent();
	bool checkRiding = false;
	bool checkRidden = false;
	if (actorRidComp)
	{
		bool checkRiding = actorRidComp->checkRidingByActorObjId(getObjId());
		bool checkRidden = actorRidComp->checkRiddenByActorObjId(getObjId());
	}
	if (!checkRiding && !checkRidden)
	{
		WCoord dpos = actor->getLocoMotion()->getPosition() - getLocoMotion()->getPosition();
		float x = float(dpos.x) / BLOCK_FSIZE;
		float z = float(dpos.z) / BLOCK_FSIZE;
		float y = float(dpos.y) / BLOCK_FSIZE;

		float max_xz = Max(Abs(x), Abs(z));
		if (max_xz > 0)
		{
			float r = Sqrt(max_xz);
			x = x / r;
			y = y / r;
			z = z / r;
			float inv_r = 1.0f / r;

			if (inv_r > 1.0f) inv_r = 1.0f;

			x *= inv_r*5.0f*(1.0f - ACTOR_COLLIDE_DEC);
			z *= inv_r*5.0f*(1.0f - ACTOR_COLLIDE_DEC);
			y *= inv_r*5.0f*(1.0f - ACTOR_COLLIDE_DEC);
			
			//给自己反弹
			/*FlyLocomotion *loco = static_cast<FlyLocomotion *>(getLocoMotion());
			if (loco != NULL)
			{
				loco->m_ColliderMotion.x = -x;
				loco->m_ColliderMotion.y = -y;
				loco->m_ColliderMotion.z = -z;
			}*/

			if (m_Def->Mass > 0.2f * actor->getMass())
			{
				//弹开别人
				LivingLocoMotion *loco2 = dynamic_cast<LivingLocoMotion *>(actor->getLocoMotion());
				if (loco2 != NULL)
				{
					loco2->m_ColliderMotion.x = x;
					loco2->m_ColliderMotion.y = y;
					loco2->m_ColliderMotion.z = z;
				}
				else
				{
					actor->getLocoMotion()->addMotion(x, y, z);
				}
			}
		}
		else
		{
			LivingLocoMotion *loco = static_cast<LivingLocoMotion *>(getLocoMotion());
			if (loco != NULL)
			{
				loco->m_ColliderMotion.x = 0;
				loco->m_ColliderMotion.y = 0;
				loco->m_ColliderMotion.z = 0;
			}
		}
	}
}

void ClientVacantBoss::onClear()
{
	//虚空boss 二阶段 死亡放置voidtotem.vox
	if (m_Def->ID != 3516)
		return;

	if (!m_pWorld->isRemoteMode())
	{
		WCoord bossPos = CoordDivBlock(m_SpawnPoint);
		ChunkRandGen &randgen = m_pWorld->getChunk(bossPos)->m_RandGen;
		m_TotemDeath->addToWorld(m_pWorld->getWorldProxy(), randgen, bossPos);

		m_pWorld->setBlockAll(bossPos + WCoord(0, 2, 0), BLOCK_VOID_JAR, 8, 2);
	}
}

void ClientVacantBoss::onDie()
{	
	ActorBoss::onDie();
	if (m_Def->ID == 3515) return;

	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		if (OnHurtByActor() && actorMgr)
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(actorMgr->findActorByWID(getBeHurtTargetID()));
			if (player)
			{
				player->addSFActivity(SFACTIVITY_KILLMOB, m_Def->ID, 1, !player->hasUIControl());
			}
		}
		//客机视野范围里有可能不存在boss ，这里同步一次
		syncToClientBossPos(WCoord(0, 0, 0));
	}

	getLocoMotion()->onDie();
	if (getAttrib()) getAttrib()->onDie();
	setNeedClear(40);

	auto ActionAttrStateComp = getActionAttrStateComponent();
	if (!m_pWorld->isRemoteMode() && ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_DEATHDROPITEM))
	{
		int defid = m_Def->ID;
		WCoord blockpos = CoordDivBlock(m_SpawnPoint) + WCoord(0, -9, 0);
		m_pWorld->getEffectMgr()->playSound(getPosition(), "ent.3502.die", 1.0f, 1.0f, PLAYSND_SYNC | PLAYSND_LONGDIST);
	}
	getBody()->setCurAnim(SEQ_DIE, 0);
	
	if (m_Def->ID == 3516)
	{
		notifShieldState(0);
		if (!m_pWorld->isRemoteMode())
		{
			clearBossInfo(m_Def->ID);
			addMissionFlags(0);
			syncToClientDialogue(85612);
			syncToClientMission(4);
		}
		//GetGameEventQue().postMissionComplete(4);
		if(g_pPlayerCtrl && !m_llMasterObjId)
		{
			if(!m_pWorld->isRemoteMode()) //冒险成就任务
			{
				g_pPlayerCtrl->addOWScore(m_Def->KillScore);
			}
			//击败boss成就上报
			if (0 == getBeHurtTargetID() || (g_pPlayerCtrl->getUin() == getBeHurtTargetID()))
			{
				g_pPlayerCtrl->ReportKillBoss(m_pWorld, (long)getBeHurtTargetID(), m_Def->ID, m_SpawnPoint);
			}
		}
	}
	if (0 == m_pWorld->getCurMapID())
	{
		GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_BOSS_IS_APPEAR, SandBoxMgrTypeID::SB_BOSS_DISAPPEAR);
		WorldRenderer* world = m_pWorld->getRender();
		if (world)
		{
			Rainbow::SkyPlane* sky = world->getSky();
			if (sky)
				sky->setPlanet(PLANET_EARTH);
		}
	}
}

void ClientVacantBoss::moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks)
{
	LivingLocoMotion *locmove = static_cast<LivingLocoMotion *>(getLocoMotion());
	locmove->m_SyncSteps = 5;

	locmove->m_SyncPos = pos;
	locmove->m_SyncYaw = yaw;
	locmove->m_SyncPitch = pitch;
}

float ClientVacantBoss::getAirSpeed()
{
	return (float)m_Def->Speed;
}

void ClientVacantBoss::saveBossData(WorldBossData * destdata)
{
	destdata->defid = m_Def->ID;
	destdata->flags = m_MissionFlags;
	destdata->hp = getAttrib()->getHP();
	destdata->spawnpoint = m_SpawnPoint;
}

void ClientVacantBoss::loadBossData(WorldBossData * bossdata)
{
	getAttrib()->initHP(bossdata->hp);
	m_MissionFlags = bossdata->flags;
	m_SpawnPoint = bossdata->spawnpoint;
	getLocoMotion()->gotoPosition(m_SpawnPoint, GenRandomFloat()*360.0f, 0);
}

void ClientVacantBoss::startAppeal()
{
	m_AIBoss->start();
	GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_ACTOR_APPEAL, 0, getObjId()&0xffffffff, NULL, 0);
}

void ClientVacantBoss::startAppeal_(bool first)
{
	m_AIBoss->start();
	GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_ACTOR_APPEAL_, 0, getObjId()&0xffffffff, (char*)&first, sizeof(bool));
}

void ClientVacantBoss::setSpawnPoint(const WCoord &blockpos)
{
	m_SpawnPoint = BlockBottomCenter(blockpos);
	getLocoMotion()->gotoPosition(m_SpawnPoint, GenRandomFloat()*360.0f, 0);
}

void ClientVacantBoss::addAIVacantBoss(int iPriority, char* parm)
{
	if (!m_AIBoss)
	m_AIBoss = SANDBOX_NEW(AIVacant, this, parm);
}

void ClientVacantBoss::addAIVacantBoss2(int iPriority, char* parm)
{
	if (!m_AIBoss)
	m_AIBoss = SANDBOX_NEW(AIVacant2, this, parm);
}


void ClientVacantBoss::BossStageOneEnd()
{
	if (m_Def->ID == 3515)
	{
		if (m_pWorld == nullptr) return ;
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		if (!actorMgr) return ;
		getLivingAttrib()->setHP(0);
		clearBossInfo(m_Def->ID);
		addMissionFlags(0);
		syncToClientMission(2);
		syncToClientDialogue(85608);
		// 计算BOSS二阶段位置
		const auto funcCalcBoss2PartPos = [this](const WCoord& pos) -> WCoord {
			int rangeR = 32 / 2 * 16; // 直径转半径，转block coord
			int ventR = 38 * 4; // 半径转block coord
			if (BiomeRegionGenConfig::getSingletonPtr())
			{
				int step = 4;
				BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "rangeZ", rangeR); // ֱ��
				BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "ventR", ventR);
				BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "step", step);
				rangeR *= CHUNK_BLOCK_Z / 2;
				ventR *= CHUNK_BLOCK_Z / step;
			}
			WCoord realpos = pos + WCoord(0, 0, (rangeR + ventR) / 2 - 10); // 取Z方向，山脉中间。10格微调
			if (!m_pWorld->getChunk(realpos))
			{
				m_pWorld->syncLoadChunk(BlockDivSection(realpos.x), BlockDivSection(realpos.z));
			}

			// 计算高度
			int blockid = m_pWorld->getBlockID(realpos);
			if (blockid == BLOCK_UNLOAD)
			{
				assert(false && "sync load chunk failed!");
				return realpos;
			}

			if (blockid == BLOCK_AIR)
			{
				// 往下
				while(IsAirBlockID(m_pWorld->getBlockID(realpos + WCoord(0, -1, 0))) && realpos.y > 1)
				{
					realpos.y--;
				}
			}
			else
			{
				// 往上
				do
				{
					realpos.y++;
				}
				while(!IsAirBlockID(m_pWorld->getBlockID(realpos)) && realpos.y < CHUNK_BLOCK_Y);
			}
			return realpos;
		};
		WCoord boss2part = funcCalcBoss2PartPos(CoordDivBlock(this->getSpawnPoint()));

		ClientVacantBoss *actor = SANDBOX_NEW(ClientVacantBoss);
		actor->init(3516);//�ٻ����boss
		actor->setSpawnPoint(boss2part);
		actorMgr->spawnBoss(actor);
		syncToClientBossPos(CoordDivBlock(actor->getPosition())); //这里要同步
	}
}

void ClientVacantBoss::clearBossInfo(int id)
{
	if (m_pWorld == NULL)
	{
		return;
	}
	resetMissionFlags(0);
	WorldMapData* mapData = GetWorldManagerPtr()->getMapData(m_pWorld->getCurMapID());
	if (mapData)
	{
		for (int i = 0; i < mapData->bosses.size(); i++)
		{
			if (mapData->bosses[i].defid == id)
			{
				mapData->bosses[i].flags = 0;
			}
		}
	}
}

void ClientVacantBoss::notifShieldState(int state)
{
	if (m_Def->ID == 3516 && m_SeePlayerControl)
	{
		MINIW::ScriptVM::game()->callFunction("UpdateHPBuffShield", "i", state);
	}
}

void ClientVacantBoss::syncToClientMission(int id)
{
	//任务
	if (!m_pWorld->isRemoteMode())
	{
		PB_VacantBossStateHC vacantBossStateHC;
		vacantBossStateHC.set_type(VACANT_EVENT::EVE_MISSION);
		vacantBossStateHC.set_objid(getObjId());
		vacantBossStateHC.set_ival0(id);
		GetGameNetManagerPtr()->sendBroadCast(PB_VACANT_BOSS_STATE_HC, vacantBossStateHC);
	}
	//ge GetGameEventQue().postMissionComplete(id);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("id", id);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_MISSION_COMPLETE", sandboxContext);
}
//虚空boss生成后 距离玩家太远
void ClientVacantBoss::syncToClientBossPos(WCoord pos)
{
	//位置
	if (!m_pWorld->isRemoteMode())
	{
		PB_VacantBossStateHC vacantBossStateHC;
		vacantBossStateHC.set_type(VACANT_EVENT::EVE_BOSS_POS);
		vacantBossStateHC.set_objid(getObjId());
		PB_Vector3* targetPos = vacantBossStateHC.mutable_targetpos();
		targetPos->set_x(pos.x);
		targetPos->set_y(pos.y);
		targetPos->set_z(pos.z);
		GetGameNetManagerPtr()->sendBroadCast(PB_VACANT_BOSS_STATE_HC, vacantBossStateHC);
	}
	if (pos.isZero())
	{
		GetWorldManagerPtr()->removeBossPoint(m_pWorld->getCurMapID());
	}
	ChunkGenNormal* gen = dynamic_cast<ChunkGenNormal*>(m_pWorld->getChunkProvider());
	if (gen)
	{
		gen->setBossPos(pos);
	}
}
void ClientVacantBoss::syncToClientDialogue(int id)
{
	//对白
	if (!m_pWorld->isRemoteMode())
	{
		PB_VacantBossStateHC vacantBossStateHC;
		vacantBossStateHC.set_type(VACANT_EVENT::EVE_DIALOGUE);
		vacantBossStateHC.set_objid(getObjId());
		vacantBossStateHC.set_ival0(id);
		GetGameNetManagerPtr()->sendBroadCast(PB_VACANT_BOSS_STATE_HC, vacantBossStateHC);
	}
	//ge GetGameEventQue().postGameDialogue(id);
	const char* info = GetDefManagerProxy()->getStringDef(id);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_String("info", info);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_GAME_DIALOGUE", sandboxContext);
}
