#include "DrinkWaterStateAction.h"
#include "DefManagerProxy.h"
#include "PlayerAttrib.h"
#include "SoundComponent.h"
#include "WorldManager.h"
#include "SandboxIdDef.h"

DrinkWaterStateAction::DrinkWaterStateAction(ClientPlayer* pPlayer)
: ActionBase(pPlayer)
{
}

DrinkWaterStateAction::~DrinkWaterStateAction()
{

}

bool DrinkWaterStateAction::drinkWater(int status, int itemId, int drinkType)
{
#ifndef IWORLD_SERVER_BUILD
	auto soundComp = mpCtrl->getSoundComponent();
	if (soundComp)
	{
		if (status == PLAYEROP_STATUS_BEGIN)
		{
			soundComp->playSound("misc.drink", 1.0f, 1.0f);
		}
		else if (status == PLAYEROP_STATUS_END)
		{
			soundComp->playSound("misc.drink", 1.0f, 1.0f, 4);
		}
	}
#endif // IWORLD_SERVER_BUILD
	itemId = itemId == 4 ? 3 : itemId;
	const FoodDef* fooddef = GetDefManagerProxy()->getFoodDef(itemId);
	int amount = 100;
	auto pWorld = mpCtrl->getWorld();
	if (pWorld)
	{
		if (drinkType == DRINK_BLOCK_WATER && mpCtrl->m_PickResult.isIntersectLiquid)
		{
			if (status == PLAYEROP_STATUS_END)
			{
				mpCtrl->drinkWaterWithBlock(mpCtrl->m_PickResult.firstIntersectBlock, amount, drinkType, itemId);
				return true;
			}
		}
		else if (drinkType == DRINK_WATER_WITH_TOOL)
		{
			if (status == PLAYEROP_STATUS_BEGIN)
			{
				mpCtrl->setOperate(PLAYEROP_DRINKWATER, fooddef->UseTime, itemId);
				mpCtrl->useItemOnTrigger(itemId);
			}
			else if (status == PLAYEROP_STATUS_END)
			{
				if (!pWorld->isRemoteMode())
				{
					//auto waterbug = mpCtrl->getEquipGrid(EQUIP_WEAPON);
					//const FoodDef* def = GetDefManagerProxy()->getFoodDef(waterbug->getItemID());
					//if (def)
					//{
					//	amount = def->HealThirst * 10;
					//}
					//int curValue = waterbug->getWaterVolume();
					//amount = std::min(curValue, amount);
					//if (amount > 0)
					//{
					//	waterbug->addWaterVolume(-amount);
					//	mpCtrl->getPlayerAttrib()->drinkWater(amount, drinkType, itemId);
					//}
					mpCtrl->getPlayerAttrib()->onDrinkWaterBug(drinkType);
				}
			}
			return true;
		}
		else if (drinkType == DRINK_WATER_WITH_DEVICE && mpCtrl->m_PickResult.intersect_block)
		{
			if (status == PLAYEROP_STATUS_END)
			{
				mpCtrl->drinkWaterWithBlock(mpCtrl->m_PickResult.block, amount, drinkType, itemId);
			}
			return true;
		}
		else if (drinkType == TOOL_FILL_WATER && mpCtrl->m_PickResult.isIntersectLiquid)
		{
			if (status == PLAYEROP_STATUS_END)
			{
				mpCtrl->fillWaterInBug(mpCtrl->m_PickResult.firstIntersectBlock, 0, 1);
			}
			return true;
		}
	}
	return false;
} 