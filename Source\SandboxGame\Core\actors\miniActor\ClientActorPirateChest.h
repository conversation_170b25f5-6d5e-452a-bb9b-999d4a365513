#pragma once

#include "ClientActorProjectile.h"

class ClientActorPirateChest : public ClientActorProjectile //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ClientActorPirateChest();
	~ClientActorPirateChest();

	virtual void init(int itemid, ClientActor* shooter = nullptr) override;

	virtual void tick()override;

	virtual void onImpactWithActor(ClientActor* actor, const std::string& partname)override;
	virtual void onImpactWithBlock(const WCoord* blockpos, int face)override;


	virtual bool supportSaveToPB()
	{
		return false;
	}

	//tolua_end


	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata, int version) override;
protected:


protected:

}; //tolua_exports