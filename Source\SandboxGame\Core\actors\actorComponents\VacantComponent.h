﻿#ifndef __VACANT_COMPONENT_H__
#define __VACANT_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "ActorLocoMotion.h"

class ClientMob;

class VacantComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(VacantComponent)

	VacantComponent();
	~VacantComponent();

	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnTick();
	virtual void OnEndPlay();

	
	int GetChangeId(int monsterId, const std::vector<int>& vecData);
	
	void setMaxVacantEnergy(int nMaxVacantEnergy);
	//tolua_begin
	int getMaxVacantEnergy();
	void setCurVacantEnergy(int nVacantEnergy);
	int getCurVacantEnergy();
	//tolua_end
	void setChangeFrom(int from);
	int getChangeFrom();
	void toChange(); //变身
	bool isVoidNight(); //是否虚空之夜
	void init();
	//tolua_begin
	bool IsVacantType()
	{
		return m_bIsVacantType;
	}
	//tolua_end
	bool IsNormalType()
	{
		return m_bIsNormalType;
	}
	bool useItemToChange(); //使用道具变身
	//tolua_begin
	void setIsUseItemToChange(bool isUseItemChange); //记录使用道具变身标志位
	//tolua_end
	bool IsUseItemToChange();
	static int GetInteractVacantEnergy(ClientMob* m_owner, int currItemID, bool &bCanUse); //获取虚空生物添加的能量
	bool interact(ClientActor* player, int currItemID); //使用道具
	//tolua_begin
	ClientMob* createVacantVortex(long long nEnterObjid); //创建虚空漩涡
	//tolua_end
	void clearVacantVortex(); //清空虚空漩涡
	
	static bool IsVacantId(int monsterid);
	void updateLackEnergyTip(); //更新缺少能量提示

	void syncAttr(int attrtype, float val);

	virtual void CreateComponentData(jsonxx::Object& componentData) override;
	virtual void LoadComponentData(const jsonxx::Object& componentData, bool fromArchive) override;
protected:

	int m_nMaxVacantEnergy; //总虚空能量
	int m_nCurVacantEnergy; //当前虚空能量
	int m_curMonsterId;		//当前的id
	int m_nChangeId;		//变身对应id
	int m_nChangeFrom;		//从什么id变过来的 对应的m_nChangeId
	int m_nLackEnergyTipTick; //气泡提示时间
	bool m_bIsVacantType;	//是否虚空怪物
	bool m_bIsNormalType;	//是否普通怪物
	bool m_bNeedRefreshChangeHit; //是否刷新命中
	bool m_bIsNatureChangeHit;	//是否命中虚空之夜自然变身
	int m_nNatureChangeHitRate;	//虚空之夜自然变身概率
	bool m_bIsUseItemToChange; //是否使用道具变身

	long long m_pChangeMobObjid; // 变身后的生物
	
};//tolua_exports

#endif