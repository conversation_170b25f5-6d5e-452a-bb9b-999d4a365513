
#include "ClientActorThrowable.h"
#include "ActorLocoMotion.h"
#include "OgreRay.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "ClientPlayer.h"
#include "WorldManager.h"
#include "ThrowableLocoMotion.h"

using namespace Rainbow;
IMPLEMENT_COMPONENTCLASS(ThrowableLocoMotion)

ThrowableLocoMotion::ThrowableLocoMotion() 
{
	m_InGround = false;
	m_TicksInAir = 0;
	m_TicksInGround = 0;
	m_InBlockID = -1;
	m_InBlockData = -1;
	m_BlockPos = WCoord(0,0,0);
	//m_Gravity = 3.0f;
	m_Gravity = 2.0f;
}

void ThrowableLocoMotion::setThrowableHeading(const Rainbow::Vector3f &dir, float vel, float deviation)
{
	Vector3f ndir = Normalize(dir);
	ndir.x += GenGaussian()*0.0075f * deviation;
	ndir.y += GenGaussian()*0.0075f * deviation;
	ndir.z += GenGaussian()*0.0075f * deviation;

	m_Motion = ndir*vel;
	Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, m_Motion);
	m_TicksInGround = 0;
}

void ThrowableLocoMotion::tickInAir()
{
	m_TicksInAir++;

	WCoord mvec = getIntegerMotion(m_Motion);
	if(mvec.x==0 && mvec.y==0 && mvec.z==0) return;

	if(!m_pWorld->isRemoteMode())
	{
		MINIW::WorldRay ray;
		ray.m_Origin = m_Position.toWorldPos();
		ray.m_Dir = mvec.toVector3();
		ray.m_Range = ray.m_Dir.Length();
		ray.m_Dir /= ray.m_Range;

		ActorExcludes excludes;
		excludes.addActor(getOwnerActor());
		if(m_TicksInAir <= 5)
		{
			ClientActor *shooter = static_cast<ClientActorThrowable *>(getOwnerActor())->getShootingActor();
			if(shooter) excludes.addActorWithRiding(shooter);
		}
		IntersectResult inter_result;
		WorldPickResult intertype = m_pWorld->pickAll(ray, &inter_result, excludes);
		if(intertype == WorldPickResult::BLOCK) //block
		{
			m_InGround = true;
			m_BlockPos = inter_result.block;
			m_InBlockID = m_pWorld->getBlockID(m_BlockPos);
			m_InBlockData = m_pWorld->getBlockData(m_BlockPos);

			static_cast<ClientActorThrowable *>(getOwnerActor())->onImpact(NULL, &m_BlockPos);
		}
		else if(intertype == WorldPickResult::ACTOR) //actor
		{
			static_cast<ClientActorThrowable *>(getOwnerActor())->onImpact(inter_result.actor->GetActor(), NULL);
		}
		else //no intersect
		{

		}
	}

	m_Position += mvec;
	Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, m_Motion);

	float decay = 0.99f;
	if(m_InWater)
	{
		decay = 0.8f;
	}

	m_Motion *= decay;
	m_Motion.y -= m_Gravity;
}

void ThrowableLocoMotion::tickInGround()
{
	int blockid = m_pWorld->getBlockID(m_BlockPos);
	int blockdata = m_pWorld->getBlockData(m_BlockPos);

	if(m_InBlockID==blockid && m_InBlockData==blockdata)
	{
		m_TicksInGround++;
		if(m_TicksInGround >= 1200)
		{
			getOwnerActor()->setNeedClear();
		}
	}
	else
	{
		m_InGround = false;
		m_Motion.x *= GenRandomFloat()*0.2f;
		m_Motion.y *= GenRandomFloat()*0.2f;
		m_Motion.z *= GenRandomFloat()*0.2f;
		m_TicksInGround = 0;
		m_TicksInAir = 0;
	}
}

void ThrowableLocoMotion::tick()
{
	ActorLocoMotion::tick();

	//if(m_pWorld->isRemoteMode()) return;

	if(m_InGround) tickInGround();
	else tickInAir();
}

