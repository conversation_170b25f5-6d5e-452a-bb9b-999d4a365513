#include "ClientActorCoconut.h"
#include "world.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "ClientItem.h"
#include "ModelItemMesh.h"
#include "world.h"
#include "BlockScene.h"
#include "SandboxIdDef.h"
#include "ClientActorManager.h"
#include "ProjectileLocoMotion.h"
#include "BlockMaterialMgr.h"
#include "SandboxCoreSubsystem.h"
//#include "BlockDefCsv.h"
#include "SoundComponent.h"
#include "DropItemComponent.h"
#include "DefManagerProxy.h"
#include "ClientActorHelper.h"
#include "Math/Quaternionf.h"
#include "SandBoxManager.h"
#include "WorldManager.h"
#include "ActorManager.h"
using namespace MINIW;


class CoconutLocoMotion : public ProjectileLocoMotion
{
	DECLARE_COMPONENTCLASS(CoconutLocoMotion)
public:
	CoconutLocoMotion();
	virtual void tick();
};

IMPLEMENT_COMPONENTCLASS(CoconutLocoMotion)

CoconutLocoMotion::CoconutLocoMotion() : ProjectileLocoMotion()
{
	//setBound(25, 25);
	//m_yOffset = m_BoundHeight / 2;
}

void CoconutLocoMotion::tick()
{
	ClientActorCoconut* projectile = dynamic_cast<ClientActorCoconut*>(GetOwnerActor());
	if (projectile == nullptr) {
		return;
	}
	if (GetOwnerActor()->needClear()) return;
	if (m_pWorld == NULL) return;
	//����
	ActorLocoMotion::tick();
	if (m_pWorld->isRemoteMode())
	{
		m_RotateYaw = syncYaw;
		m_RotationPitch = syncPitch;

		if (m_InterplateStep > 0)
		{
			m_Position = m_Position + (syncPos - m_Position) / m_InterplateStep;
			m_InterplateStep--;
		}
		else
		{
			m_Position = syncPos;
		}
	}
	else
	{
		WCoord mvec = getIntegerMotion(m_Motion);
		if (mvec.x == 0 && mvec.y == 0 && mvec.z == 0)
		{
			m_Motion *= (1 - 0.2f);
			m_Motion.y -= m_Gravity;
			return;
		}
		MINIW::WorldRay ray;
		ActorExcludes excludes;
		IntersectResult inter_result1, inter_result2;
		IntersectResult* presult;
		int intertype = 0;
		if (projectile->m_ProjectileDef)
		{
			if (intertype == 0)
			{
				intertype = projectilePickAll(mvec, ray, &inter_result2, excludes, PICK_METHOD_SOLID);
				presult = &inter_result2;
			}
		}
		if (intertype == 1)
		{
			//����ƫ��
			Rainbow::Vector3f targetpos = ray.m_Origin.toVector3() + ray.m_Dir * presult->collide_t;
			mvec = Rainbow::Vector3f::zero;
			m_Position = WCoord(targetpos);
			WCoord blockPos = presult->block;

			auto material = g_BlockMtlMgr.getMaterial(m_pWorld->getBlockID(blockPos));
			projectile->onImpactWithBlock(&blockPos, presult->face);
			//if (material && material->EXEC_USEMODULE(OnActorCollidedWithBlock, m_pWorld, blockPos, m_OwnerActor))
			if (!GetOwner()) return ;
			ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
			if (!m_owner) return ;
			if (material && material->onActorCollidedWithBlock(m_pWorld, blockPos, GetOwnerActor()))
			{
				m_owner->clearCollideBlock();
				m_owner->setCollideBlockState(blockPos, m_InBlockID);
			}
			else
			{
				m_owner->clearCollideBlock();
			}
		}
		else if (intertype == 2)
		{
			//����ƫ��
			Rainbow::Vector3f targetpos = ray.m_Origin.toVector3() + ray.m_Dir * presult->collide_t;
			//ͬһ��team��actor������onImpactWithActor
			//const ProjectileDef* projectileDef = ProjectileDefCsv::getInstance()->get(projectile->GetItemId());

			//projectile->m_TriggerPos = inter_result.actor->getEyePosition().toVector3();
			ClientActor* owner = projectile->getShootingActor();
			if (owner)
			{
				WCoord blockPos = CoordDivBlock(presult->actor->getPosition());
				blockPos.y -= 1;
				SandBoxManager::getSingleton().DoEvent(SandBoxMgrEventID::EVENT_PROJECTILE_COLLIDE_BLOCK, 0, owner->getObjId() & 0xffffffff, (char*)&blockPos, sizeof(blockPos));
			}
			m_Position = targetpos;
			projectile->onImpactWithActor(presult->actor->GetActor(), "");
		}


		m_Motion.y -= m_pWorld->getGravity(GRAVITY_LIVING) + 3;
		setNoClip(pushOutOfBlocks(m_Position));

		WCoord prevpos = m_Position;
		doMoveStep(m_Motion);


	}
}




ClientActorCoconut::ClientActorCoconut()
{
	m_dirBlock = 0;
	//OGRE_DELETE(m_LocoMotion);
	//m_LocoMotion = new CoconutLocoMotion(this);
	CreateComponent<CoconutLocoMotion>("CoconutLocoMotion");
}
ClientActorCoconut::~ClientActorCoconut()
{

}

flatbuffers::Offset<FBSave::SectionActor> ClientActorCoconut::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveActorCommon(builder);
	ProjectileLocoMotion* loco = static_cast<ProjectileLocoMotion*>(getLocoMotion());
	auto quat = QuaternionToSave(loco->m_RotateQuat);
	auto obj = FBSave::CreateActorCoconut(builder, basedata, m_ShootingActorID, m_ItemID, &quat);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorCoconut, obj.Union());
}

bool ClientActorCoconut::load(const void* srcdata, int version)
{
	m_LiveTicks = 0;
	auto src = reinterpret_cast<const FBSave::ActorThornBall*>(srcdata);
	init(src->itemid());
	loadActorCommon(src->basedata());
	m_ShootingActorID = src->shooter();
	playMotion(m_ProjectileDef->TailEffect);
	CoconutLocoMotion* loco = dynamic_cast<CoconutLocoMotion*>(getLocoMotion());
	if (loco)
	{
		loco->syncPos = getLocoMotion()->getPosition();
		if (src->rotatequat())
			loco->m_RotateQuat = QuaternionFromSave(src->rotatequat());
		else
		{
			//loco->m_RotateQuat.setEulerAngle(loco->m_RotateYaw, loco->m_RotationPitch, 0);
			loco->m_RotateQuat = Rainbow::AngleEulerToQuaternionf(Rainbow::Vector3f(loco->m_RotationPitch, loco->m_RotateYaw, 0));
		}

		loco->m_CanTrigger = false;
		if (loco->needFullRotation())
		{
			//m_ServerYawCmp = loco->m_RotateQuat.compressToInt();
			m_ServerYawCmp = loco->m_RotateQuat.ToUInt32();
		}
		else
		{
			loco->syncYaw = loco->m_RotateYaw;
			loco->syncPitch = loco->m_RotationPitch;
		}
	}
	return true;
}

void ClientActorCoconut::init(int itemid, ClientActor* shooter)
{
	ClientActorProjectile::init(itemid, shooter);
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion*>(getLocoMotion());
	if (loco)
		loco->syncPos = getLocoMotion()->getPosition();
	loco->m_yOffset = 0;
	loco->setBound(100, 100);
	loco->setNoClip(false);
}


void ClientActorCoconut::onImpactWithActor(ClientActor* actor, const std::string& partname)
{
	doImpactActor(actor);
}

void ClientActorCoconut::onImpactWithBlock(const WCoord* blockpos, int face)
{
	if (m_ItemID == 11660)
	{
		//��ͨ
		getWorld()->setBlockAll(*blockpos + WCoord(0, 1, 0), BLCOK_COCONUT, m_dirBlock | 8);
	}
	else
	{
		//�컯
		getWorld()->setBlockAll(*blockpos + WCoord(0, 1, 0), BLCOK_COCONUT_DIF, m_dirBlock | 8);
	}
	setNeedClearEx();

	doTrigger();
}

void	ClientActorCoconut::doImpactActor(ClientActor* actor)
{
	World* pworld = getWorld();
	if (pworld == nullptr || !g_WorldMgr)
	{
		return;
	}
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
	if (player)
	{
		//�����е�����
		if (player->isSleeping() || player->isRestInBed())
		{
			player->wakeUp(false, false, true);
		}
		// ����״̬
		if (pworld->isDaytime())
		{
			doAttack(actor);
		}
		else
		{
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound("misc.sleep", 1.0f, 1.0f);
			}
			vector<IClientPlayer*> players;
			g_WorldMgr->getAllPlayers(players);
			int sleepingCount = 0;
			for (auto iter = players.begin(); iter != players.end(); iter++)
			{
				ClientPlayer* player = dynamic_cast<ClientPlayer*>(*iter);
				if (actor->getObjId() != player->getObjId())
				{
					if (player->isSleeping() || player->isRestInBed())
					{
						sleepingCount++;
					}
				}
			}
			doAttack(actor);
			if (player->isDead() || player->needClear())
			{
				return;
			}
			if (sleepingCount == players.size() - 1)
			{
				for (auto iter = players.begin(); iter != players.end(); iter++)
				{
					//����
					dynamic_cast<ClientPlayer*>((*iter))->setCoconutSkipNight(true);
				}
			}
		}
		player->setCoconutHit(true);
	}
	else
	{
		doAttack(actor);
	}
	setNeedClearEx();
	doTrigger();
}

void	ClientActorCoconut::doAttack(ClientActor* actor)
{
	if (m_ProjectileDef == nullptr)
	{
		return;
	}
	OneAttackData atkdata;
	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;
	atkdata.atktype = ATTACK_ANVIL;
	atkdata.atkpoints = 10.0f;
	atkdata.fromplayer = nullptr;
	atkdata.buffId = m_ProjectileDef->BuffId / 1000;
	atkdata.buffLevel = m_ProjectileDef->BuffLevel % 1000;
	actor->attackedFrom(atkdata, this);
	BlockDef* def = nullptr;
	if (m_ItemID == 11660)
	{
		def = GetDefManagerProxy()->getBlockDef(BLCOK_COCONUT);
	}
	else
	{
		def = GetDefManagerProxy()->getBlockDef(BLCOK_COCONUT_DIF);
	}
	if (def)
	{
		int itemid = def->HandMineDrops.item;
		//dropItem(itemid, 1);
		auto dropComponent = GetComponent<DropItemComponent>();
		if (dropComponent)
		{
			dropComponent->dropItem(itemid, 1);
		}
	}
}

void ClientActorCoconut::onCollideWithPlayer(ClientActor* actor)
{
	doImpactActor(actor);
}

ClientActorCoconut* ClientActorCoconut::shootCoconueAuto(int itemid, World* pworld, const WCoord& pos, const int& dir, float speed, float deviation)
{
	if (!pworld) return NULL;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return NULL;
	ClientActorCoconut* projectile = ENG_NEW(ClientActorCoconut)();
	projectile->init(itemid);
	projectile->m_dirBlock = dir;
	actorMgr->spawnActor(projectile, pos, 0, 0);
	projectile->m_StartPos = projectile->getPosition();

	ProjectileLocoMotion* locomove = static_cast<ProjectileLocoMotion*>(projectile->getLocoMotion());
	projectile->playMotion(projectile->m_ProjectileDef->TailEffect);
	projectile->m_AttackPoints = projectile->m_ProjectileDef->AttackValue;
	if (projectile->m_ProjectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}
	return projectile;
}