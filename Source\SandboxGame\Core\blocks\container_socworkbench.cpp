#include "container_socworkbench.h"
#include "GameNetManager.h"
#include "world.h"

ContainerSocWorkbench::ContainerSocWorkbench(int baseindex ):
	ErosionContainer(baseindex)
{

}

ContainerSocWorkbench::ContainerSocWorkbench(const WCoord& blockpos, const int blockId, int baseindex ) : 
	ErosionContainer(blockpos, blockId, baseindex)
{

}

ContainerSocWorkbench::~ContainerSocWorkbench()
{

}

void ContainerSocWorkbench::enterWorld(World* pworld)
{
	ErosionContainer::enterWorld(pworld);
#ifdef IWORLD_SERVER_BUILD
	if (!pworld) return;

	SocWorkbenchMgr* bedmgr = pworld->GetWorkbenchMgr();
	if (!bedmgr) return;

	bedmgr->AddWorkbench(m_BlockPos, m_Value);

	PB_SocWorkbenchHC hc;
	hc.set_type(1);
	PB_SocWorkbenchData* data = hc.add_socworkbenchdatas();
	data->set_x(m_BlockPos.x);
	data->set_y(m_BlockPos.y);
	data->set_z(m_BlockPos.z);
	data->set_itemid(m_Value);

	GetGameNetManagerPtr()->sendBroadCast(PB_SocWorkbench_HC, hc);
#endif
}

void ContainerSocWorkbench::leaveWorld()
{
#ifdef IWORLD_SERVER_BUILD
	SocWorkbenchMgr* bedmgr = m_World->GetWorkbenchMgr();
	if (!bedmgr) return;

	bedmgr->RemoveWorkbench(m_BlockPos);

	PB_SocWorkbenchHC hc;
	hc.set_type(2);
	PB_SocWorkbenchData* data = hc.add_socworkbenchdatas();
	data->set_x(m_BlockPos.x);
	data->set_y(m_BlockPos.y);
	data->set_z(m_BlockPos.z);
	data->set_itemid(m_Value);

	GetGameNetManagerPtr()->sendBroadCast(PB_SocWorkbench_HC, hc);
#endif

	ErosionContainer::leaveWorld();
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerSocWorkbench::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = ErosionContainer::saveContainerErosion(builder);

	auto actor = FBSave::CreateContainerSocWorkbench(builder, basedata, m_SubType, m_Value);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerSocWorkbench, actor.Union());
}

bool ContainerSocWorkbench::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerSocWorkbench*>(srcdata);
	//loadContainerCommon(src->basedata());
	ErosionContainer::load(src->basedata());

	m_SubType = src->subtype();
	m_Value = src->value();

	return true;
}
