#ifndef __ACTION_ATTR_STATE_COMPONENT_H__
#define __ACTION_ATTR_STATE_COMPONENT_H__
#include "SandboxGame.h"
#include "world_types.h"
#include "ActorComponent_Base.h"

class ClientActor;
class EXPORT_SANDBOXGAME ActionAttrStateComponent;
class ActionAttrStateComponent : public ActorComponentBase//tolua_exports
{//tolua_exports
public:
	DECLARE_COMPONENTCLASS(ActionAttrStateComponent)

	ActionAttrStateComponent();
	~ActionAttrStateComponent();
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

	//tolua_begin
	void CreateModuleEvent();

	bool checkActionAttrState(int state);

	bool checkActionAttrState_Base(int state);

	void setActionAttrState(int actionattr, bool b);

	void setActionAttrState_Base(int actionattr, bool b);

	void setAllActionAttrState(unsigned int attr);

	unsigned int getAllActionAttrState();

	void resetActionAttrState();
	//tolua_end

private:
	int GetInitValue();

protected:
	ClientActor* m_owner;

public:
	unsigned int m_ActionAttrState;//生物特殊属性的状态（例如可移动，可掉落等）

};//tolua_exports

#endif
