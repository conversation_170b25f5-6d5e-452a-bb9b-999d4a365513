#include "ClientMob.h"
#include "DefManagerProxy.h"
#include "ActorBody.h"
#include "Render/RenderCommon.h"
#include "worldMesh/MiniCraftRenderer.h"

#include "LivingLocoMotion.h"
#include "ActorVision.h"
#include "ActorAttrib.h"

#include "ClientActorManager.h"
#include "OgreScriptLuaVM.h"
//#include "OgreFileSystem.h"
#include "world.h"
#include "ClientPlayer.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "container.h"
#include "OgreUtils.h"
#include "backpack.h"
#include "ActorCSProto.h"
#include "special_blockid.h"
#include "Environment.h"
#include "ClientItem.h"
#include "ActorEarthCoreMan.h"
#include "ActorTrader.h"
#include "ActorHorse.h"
#include "ActorDragonMount.h"
#include "ActorNpc.h"
#include "ActorDesertBusinessman.h"
#include "ClientAquaticMob.h"
#include "ClientFlyMob.h"
#include "ClientActorArrow.h"
#include "ActorBall.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "MpActorManager.h"
//#include "GameMod.h"
#include "ClientPlayer.h"
#include "BlockMaterialMgr.h"
#include "OgrePrerequisites.h"
#include "ActorGhost.h"
#include "GameNetManager.h"
//#include "GameEvent.h"
//#include "stringdef.h"
#include "ActorVillager.h"
#include "BlockBed.h"
#include "MpGameSurviveCdnResMgr.h"

//#include "IClientGameManagerInterface.h"
#include "AIPickupItemEx.h"
#include "GameModeDef.h"
#include "AIFishAttack.h"
#include "AITargetHurtee.h"
#include "AISwimming.h"
#include "AIFishBeg.h"
#include "AISit.h"
#include "AIFollowOwner.h"
#include "AIWander.h"
#include "AIPetWander.h"
#include "AITargetOwnerHurtee.h"
#include "AITargetOwnerHurter.h"
#include "AITargetNonTamed.h"
#include "AIBeg.h"
#include "AIWatchClosest.h"
#include "AILookIdle.h"
#include "AIRestrictSun.h"
#include "AIFleeSun.h"
#include "AITargetNearest.h"
#include "AIBreakDoor.h"
#include "AIMoveTowardsRestriction.h"
#include "AIPanic.h"
#include "AITempt.h"
#include "AILeapAtTarget.h"
#include "AIMate.h"
#include "AIFollowParent.h"
#include "AIBoom.h"
#include "AIArrowAttack.h"
#include "AIEatGrass.h"
#include "AIRandomSwim.h"
#include "AIFearPlayer.h"
#include "AIFollowDirection.h"
#include "AITargetSpecificEntity.h"
#include "AITransfiguration.h"
#include "ProjectileFactory.h"
#include "proto_common.h"
#include "AIProjectileAttack.h"
#include "AIClosestDance.h"
#include "AISleep.h"
#include "AILoggerHeads.h"
#include "AILayEggs.h"
#include "AILayEggInNest.h"
#include "AIHatch.h"
#include "AIEatFeedBlock.h"
#include "AIToppleOver.h"
#include "AISitbyItem.h"
#include "AIMilking.h"
#include "AIEatLeaf.h"
#include "AIEatFlower.h"
#include "AIRideHorse.h"
#include "AIKickAway.h"
#include "AIMakeTrouble.h"
#include "AIGetSpecialAttackattr.h"
#include "AISitBlock.h"
#include "AILoveBlock.h"
#include "AITargetFollowingPlayer.h"
#include "AIHoldMonster.h"
#include "AICeilingAtk.h"
#include "AIGoCeiling.h"
#include "AIPetDanceToPlayer.h"
#include "AISpecialAct.h"
#include "AIHungry.h"
#include "AIRandomFly.h"
#include "AIFlyAttack.h"
#include "AIFlyBeg.h"
#include "AIFlyLoveBlock.h"
#include "AIFearItem.h"
#include "AIDissolvedByItem.h"
#include "AIAttractBlock.h"
#include "AISavageSleep.h"
#include "AIWarning.h"
#include "AIItemPanic.h"
#include "AIPlayerPanic.h"
#include "AISeparate.h"
#include "AISeparatePanic.h"
#include "AICombine.h"
#include "AIWizardFly.h"
#include "AIWizardAttack.h"
#include "AIWizardProjectileAttack.h"
#include "AIBumpAttack.h"
#include "AIDigBlock.h"
#include "AIPickupItem.h"
#include "AIStoreItem.h"
#include "AITakeItem.h"
#include "AICraftItem.h"
#include "AINpcSleep.h"
#include "AIHunger.h"
#include "AIEatFood.h"
#include "AIEatThenMutate.h"
#include "AIMutateFly.h"
#include "AIPatrolOnBlock.h"
#include "AIMutateTarget.h"
#include "AIPlant.h"
#include "AITargetScream.h"
#include "AIPanicBuff.h"
#include "AIPetPlayToPlayer.h"
#include "Text3D/Text3D.h"
#include "AIGhostBombAttack.h"
#include "AIGhostIceAttack.h"
#include "AIGhostBumpAttack.h"
#include "AIPetFollowOwner.h"
#include "PlayerControl.h"
#include "ObserverEventManager.h"
#include "BehaviorTreeManager.h"
#include "MobEventListen.h"
#include "ActorMoonMount.h"

#include "ActorDouDuMount.h"
#include "ActorPumpkinHorse.h"

#include "AIThief.h"
#include "AIBananaFan.h"
#include "AIClimbTree.h"
#include "AIEvade.h"
#include "AILeopardAtk.h"
#include "ActorSavagePriest.h"
#include "AIBegEx.h"
#include "AIRanchWander.h"
#include "AIPosWander.h"
#include "AIFlyAttract.h"
#include "AIHungryStatus.h"
#include "AIHungryAtkTarget.h"
#include "AIHungryFollowPlayer.h"
#include "AiVacant.h"
#include "AILavaCrab.h"
#include "AIEarthCoreManLash.h"
#include "AIEarthCoreManRain.h"
#include "AIEarthCoreManSteal.h"
#include "AIFlyPanic.h"
#include "AIChangeBlock.h"
#include "AIFlyStayFlower.h"
#include "AISavageStandSleep.h"
#include "AIOriole.h"
#include "AIFlyFollow.h"
#include "AIStayBlock.h"
#include "AIAtkTiangou.h"
//#include "TerrgenStatistic.h"
#include <ctime>
#include "RuneDef.h"
#include "ClientTrixenieMob.h"
#include "Minitolua.h"
#include "OgreScriptLuaVM.h"

#include "AttackingTargetComponent.h"
#include "HPProgressComponent.h"
#include "TeamComponent.h"
#include "GrowComponent.h"
#include "BreedComponent.h"
#include "QuiteComponent.h"
#include "AngryComponent.h"
#include "TameComponent.h"
#include "PlotComponent.h"
//#include "GameMod.h"

#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "Utils/thinkingdata/GameAnalytics.h"

#include "MobAttrib.h"
#include "PlayerAttrib.h"
#include "ActionAttrStateComponent.h"
#include "RiddenComponent.h"
#include "SoundComponent.h"
#include "EffectComponent.h"
#include "ParticlesComponent.h"
#include "ToAttackTargetComponent.h"
#include "CarryComponent.h"
#include "ClientActorHelper.h"
#include "DropItemComponent.h"
#include "AttackedComponent.h"
#include "FireBurnComponent.h"
#include "BTBlackboard.h"
#include "ClientFlyComponent.h"
#include "ClientAquaticComponent.h"
#include "VacantComponent.h"
#include "ScriptComponent.h"
#include "GridContainer.h"
#include "EquipGridContainer.h"
#ifdef IWORLD_SERVER_BUILD
#include "ICloudProxy.h"
#endif
//#include "TaskSubSystem.h"
#include "container_world.h"
#include "LuaInterfaceProxy.h"
#include "SpaceActorManager.h"
#define _UPDATE_BOUND_BY_SCALE_
#define SCORPION_VENOM_MIN 1018001
#define SCORPION_VENOM_MAX 1018005
#define HEGHLY_TOXIC_INDEX 1016001

using namespace MNSandbox;


ActorLocoMotion* ClientMob::newLocoMotion()
{
	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		return pClientFlyComponent->newLocoMotion();
	}

	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		return pClientAquaticComponent->newLocoMotion();
	}

	return static_cast<ActorLocoMotion*>(CreateComponent<LivingLocoMotion>("LivingLocoMotion"));
}

void ClientMob::pickItem(ClientItem* item, int puttype/* =0 */)
{
	auto ActionAttrStateComp = getActionAttrStateComponent();
	if (!(ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_PICKUP))) { return; }

	if (puttype == 0)
	{
		EQUIP_SLOT_TYPE slot = item->getItemArmorPosition();
		if (slot != MAX_EQUIP_SLOTS)
		{
			auto equipComponent = GetComponent<EquipGridContainer>();
			if (equipComponent)
			{
				equipComponent->dropOneEquipItem(slot);
				equipComponent->equip(slot, &item->m_ItemData);
			}
			else
			{
				MobAttrib* attrib = getMobAttrib();
				attrib->dropOneEquipItem(slot);
				attrib->equip(slot, &item->m_ItemData);
			}
			item->setNeedClear();
			setPersistance(true);
		}
	}
	else if (puttype == 2)
	{
		//不放装备和背包
		item->setNeedClear();
	}
	else
	{
		auto gridComponent = GetComponent<GridContainer>();
		if (gridComponent)
		{
			gridComponent->addItemByGrid(&item->m_ItemData);
			item->setNeedClear();
		}
		else
		{
			auto bags = getMobAttrib()->getBags();
			if (bags)
			{
				auto grid = item->m_ItemData;
				int num = bags->addItem_byGrid(&grid);
				item->setNeedClear();
			}
		}

	}
	int itemid = item->getDefID();
	ItemDef* itemdef = GetDefManagerProxy()->getItemDef(itemid);
	if (itemdef)
	{
		ObserverEvent_ActorItem obevent(this->getObjId(), item->getDefID(), 1, item->getObjId());
		WCoord pos = item->getPosition();
		pos = CoordDivBlock(pos);
		obevent.SetData_Position(pos.x, pos.y, pos.z);
		if (itemdef->Type == ITEM_TYPE_BLOCK)
		{
			obevent.SetData_Block(itemid);
		}
		if (IsTriggerCreature())
		{
			obevent.SetData_Actor(getDefID());
		}
		GetObserverEventManager().OnTriggerEvent("Item.Pickup", &obevent);
	}
}
ClientMob* ClientMob::CastToMob()
{
	return this;
}

bool ClientMob::SetLocoMotionType(int locotype, bool isNo)
{

	ActorLocoMotion* loco = getLocoMotion();
	if (loco)
	{
		LivingLocoMotion* livingloco = loco->ToCast<LivingLocoMotion>();
		if (livingloco)
		{
			if (!(livingloco->checkMoveAbilityFlag(static_cast<MoveAbilityType>(locotype)) && isNo))
			{
				if (isNo)
				{
					livingloco->initMoveAbility(static_cast<MoveAbilityType>(locotype));
				}
				else
				{
					livingloco->setMoveAbilityFlag(static_cast<MoveAbilityType>(locotype), isNo);
				}

			}
			if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				jsonxx::Object context;
				char objid_str[128];
				sprintf(objid_str, "%lld", getObjId());
				context << "objid" << objid_str;
				context << "locotype" << locotype;
				context << "isno" << isNo;
				SandBoxManager::getSingleton().sendBroadCast("PB_SETLOCOTYPE_HC", context.bin(), context.binLen());
			}
			return true;
		}
	}
	return false;
}

void ClientMob::onClear()
{
	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		pClientAquaticComponent->onClear();
		return;
	}

	if (getPlayClearFx() && getDef()->ID != 3416 && getDef()->ID != 3417)//熊猫不播放死亡特效
	{
		ParticlesComponent::playParticles(this, "exp.ent");
	}
}

void ClientMob::onDie()
{
	//Fire<int>(MOB_SCRIPTNOTIFY_DIE);
	char szScriptFun[256];
	snprintf(szScriptFun, sizeof(szScriptFun), "F%d_OnDie", getDef()->ID);
	MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]", this);

	// 设置标记表示这是可交互的尸体
	setFlagBit(ACTORFLAG_INTERACTIVE_CORPSE, true);
	// 设置死亡状态标记
	setFlagBit(ACTORFLAG_AI_DIE, true);
	
	// 确保生物血量为0，让isDead()方法能正确返回true
	if (getAttrib())
	{
		getAttrib()->setHP(0);
	}
	
	// 禁用死亡特效
	SetPlayClearFx(false);
	
	// 确保尸体不会被清理
	m_DieTicks = 0;
	m_NeedClearTicks = -1; // 确保ClientActor::checkNeedClear不会触发清理
	m_corpseRemainTime = getDef()->CorpseTime;
	
	// 禁用所有AI更新和行为，防止动画状态切换
	m_NeedUpdateAI = false;
	
	// 清除所有正在运行的AI任务，防止AI行为导致尸体状态切换
	if (m_AITask) 
	{
		m_AITask->clearAllRunningTasks();
		m_AITask->clearAllTasks();
	}
	
	if (m_AITaskTarget) 
	{
		m_AITaskTarget->clearAllRunningTasks();
		m_AITaskTarget->clearAllTasks();
	}
	
	if (m_isUseAILua)
	{
		GetCoreLuaDirector().CallFunctionM("AILuaManager", "clearAllRunningTasks", "i", m_AILuaKey);
	}
	
	
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		WCoord pos = CoordDivBlock(getPosition());

		std::ostringstream oss;
		oss << "[" << pos.x << "," << pos.y << "," << pos.z << "]";
		std::string location = oss.str();

		int uin = 0, toolId = 0;
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(getBeAtk());
		if (player)
		{
			uin = player->getUin();
			toolId = player->getCurToolID();
		}


		GameAnalytics::TrackEvent("mob_die", {
			{"mob_id",getDefID()},
			{"mob_name",getDef()->Name.c_str()},
			{"player_uin",uin},
			{"weapon_id", toolId},
			{"location",location}
		});

		// 观察者事件接口
		ClientActor* actor = getBeAtk();
		ObserverEvent_Actor obevent((long long)m_ObjId, actor ? actor->getObjId() : 0);
		obevent.SetData_Actor(this->getDefID());
		obevent.SetData_Position(pos.x, pos.y, pos.z);
		if (actor && actor->getShootingActor())
		{
			long long shooterObjid = actor->getShootingActor()->getObjId();
			obevent.SetData_HelperObjid(shooterObjid);
		}
		GetObserverEventManager().OnTriggerEvent("Actor.Die", &obevent);

		ObserverEvent obeventDev;
		obeventDev.SetData_EventObj(m_ObjId);
		ObserverEventManager::getSingleton().OnTriggerEvent("dev.remove", &obeventDev);
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		if (!actorMgr) return ;
		actorMgr->onMobDead(this);

		if (OnHurtByActor())
		{
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
			if (player)
			{
				//雨林埋点 玩家击杀猴子/豹子
				MNSandbox::GetGlobalEvent().Emit<int>("StatisticRainforest_onCondition_PlayerKillMonster", getDefID());

				if (m_Def)
				{
					MNSandbox::GetGlobalEvent().Emit<int, int>("StatisticTerrgen_onCondition_PlayerKillMonster", m_Def->ID, 1);
				}

				if (getDefID() == 3824)
				{
					player->setKillScorpionCount(1);
				}

				if (player->getKillScorpionCount() == 10)
				{
					WCoord center = CoordDivBlock(player->getPosition());
					int dist = GenRandomInt(-10, -7);
					if (GenRandomInt(99) > 50)
					{
						dist = GenRandomInt(7, 10);
					}
					int x = center.x + dist;
					int z = center.z + dist;
					int y = m_pWorld->getTopHeight(x, z);
					WCoord pos = WCoord(x * BLOCK_SIZE, y * BLOCK_SIZE, z * BLOCK_SIZE);

					BIOME_TYPE biomeType = m_pWorld->getBiomeType(CoordDivBlock(pos).x, CoordDivBlock(pos).z);
					if (biomeType == BIOME_DESERT && biomeType != BIOME_DESERT_OASIS)
					{
						m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/bossgiant_build.ent", pos, 30, 0, 0, true, 30);
						ClientMob* pmob = actorMgr->spawnMonster(pos.x, pos.y, pos.z, 3829);
						if (pmob)
						{
							player->setKillScorpionCount(0);

							char m_szScriptFun[256];
							snprintf(m_szScriptFun, sizeof(m_szScriptFun), "F%d_OnSpawn", 3829);
							MINIW::ScriptVM::game()->callFunction(m_szScriptFun, "u[ClientMob]", pmob);
						}
					}
				}

				player->addAchievement(3, ACHIEVEMENT_KILLMOB, getDef()->ID);
				player->addOWScore(getDef()->KillScore);
				player->addGameScoreByRule(GMRULE_SCORE_KILLMOB);
				player->updateTask(KILL_MOB, getDef()->ID, 1);
				player->defeatActorOnTrigger(m_ObjId, getDef()->ID);
				player->addWeaponSkilledPoint(WEAPON_SKILLED_TYPE_KILLED);
				player->updateTaskSysProcess(TASKSYS_KILL_MOB, getDef()->ID);
				//增加等级经验
				if (!m_llMasterObjId)
					AddLevelExp2Killer(player);

				player->addSFActivity(SFACTIVITY_KILLMOB, getDef()->ID, 1, !player->hasUIControl());

				actor->Event2().Emit<long long>("beatActorOnTrigger", m_ObjId);
			}
			else
			{
				ClientActor * actor = dynamic_cast<ClientActor*>(GetWorldManagerPtr()->findActorByWID(getBeHurtTargetID()));
				if (actor)
				{
					ClientMob* mob = NULL;
					if (actor->IsTriggerProjectile() && actor->getShootingActor()) // 投掷物类型
					{
						mob = dynamic_cast<ClientMob*>(actor->getShootingActor());
					}
					else
					{
						mob = dynamic_cast<ClientMob*>(actor);//生物类型
					}
					if (mob && mob->IsTriggerCreature())
					{
						mob->addGameScoreByRule(GMRULE_SCORE_KILLMOB);

						//打败目标
						ObserverEvent_Actor obevent(mob->getObjId(), getObjId());
						obevent.SetData_Actor(mob->getDefID());
						if (ClientActorHelper::isTriggerCreatureType(getObjType()))
						{
							obevent.SetData_TargetActorID(getDefID());
						}
						GetObserverEventManager().OnTriggerEvent("Actor.Beat", &obevent);
					}
				}
			}
#ifdef IWORLD_SERVER_BUILD
			Rainbow::GetICloudProxyPtr()->onMonsterDie(this, player ? player->getObjId() : 0);
#endif
		}
	}

	//ge GetGameEventQue().postMonsterDie(getObjId());
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("objId", getObjId()).
		SetData_Number("id", getDef()->ID);

	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_MONSTER_DIE", sandboxContext);

	getLocoMotion()->onDie();
	//getLocoMotion()->setAttackBound(0, 0, 0);	//死亡的时候清掉受击盒子
	// getAttrib()->onDie();
	//if (getBody())
	//{
	//	getBody()->mobOnDie();
	//}
	//setNeedClear(20);


	auto targetComponent = getToAttackTargetComponent();
	if (targetComponent)
	{
		targetComponent->setTarget(nullptr);
	}
	//m_BeHurtTarget = 0;
	setBeHurtTargetID(0);
	setAtkingTarget(NULL);
	setBeAtk(nullptr);

	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		pClientFlyComponent->onDie();
	}

	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		pClientAquaticComponent->onDie();
	}
}

void ClientMob::AddLevelExp2Killer(ClientPlayer* pPlayer)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode())
	{
		if (GetWorldManagerPtr()->getBaseSettingManager() && GetWorldManagerPtr()->getBaseSettingManager()->isOpenLevelModel())
		{
			if (pPlayer)
			{
				int nExp = 0;

				if (m_Def)
					nExp = m_Def->LevelExp;

				//nExp = 2;	//TODO:测试
				if (nExp > 0)
					pPlayer->getPlayerAttrib()->addLevelModelExp(nExp);
			}
		}
	}
}

void ClientMob::suddenIllnessBuffAttack() {
	if (m_pWorld == NULL) return;
	LivingAttrib* attrib = getLivingAttrib();
	auto targetComponent = getToAttackTargetComponent();
	if (attrib == NULL) return;

	int defId = getDefID();
	if (attrib->hasBuff(SUDDEN_ILLNESS_BUFF))
	{
		const std::vector<int> mobIds{ 3121,3416,3417 };//稻草人、熊猫
		int size = mobIds.size();
		for (size_t i = 0; i < size; i++)
		{
			if (defId == mobIds[i]) {
				attrib->removeBuff(SUDDEN_ILLNESS_BUFF);
				return;
			}
		}

		auto randomRun = [](ClientMob* mob) -> void {
			if (!mob) return;
			mob->m_suddenIllnessBuffTraceTick--;
			if (mob->m_suddenIllnessBuffTraceTick <= 0)
			{
				mob->m_suddenIllnessBuffTraceTick = 15;
				WCoord targetPos;
				mob->getLocoMotion()->findRandTargetBlock(targetPos, 8, 0, NULL);
				mob->getNavigator()->tryMoveTo(targetPos.x, targetPos.y, targetPos.z, 1.5);
			}
		};

		//沒有攻击，就不发起攻击
		const std::vector<int> noAttackMobIds{ 3400,3604,3605,3606,3607,3608,3609,3610,3611,3612,3613,3614,3615,3616,3617,3618,3619 };
		size = noAttackMobIds.size();
		for (size_t i = 0; i < size; i++)
		{
			if (defId == noAttackMobIds[i]) {
				randomRun(this);
				return;
			}
		}

		if (isSleeping()) {
			wakeUp();//在睡袋重新进入世界,先醒一下
		}
		if (checkIfSavageSleeping()) {//唤醒睡觉的野人
			setFlagBit(ACTORFLAG_AI_SLEEP, false);
		}

		ClientActor* target = NULL;
		if (targetComponent)
		{
			target = targetComponent->getTarget();
		}

		if (m_isSuddenIllnessFindMob == false || NULL == target || target->isDead()) {
			m_isSuddenIllnessFindMob = true;
			ClientMob* mob = selectNearMob(0, 0, 2000);
			if (mob)
			{
				if (targetComponent)
				{
					targetComponent->setTarget(mob);
				}
			}
			else
			{
				ClientPlayer* player = getActorMgr()->selectNearPlayer(getPosition(), 2000);
				if (player)
				{
					if (targetComponent)
					{
						targetComponent->setTarget(player);
					}
				}
				else
				{
					randomRun(this);
				}
			}
		}
		else {
			CollideAABB box;
			getCollideBox(box);
			double dist = m_Def->AttackDistance * BLOCK_SIZE + m_Def->Width * m_Def->ModelScale / 2 + box.dim.x / 2;
			dist = dist * dist;
			WCoord targetpos = target->getLocoMotion()->getPosition();
			if (getSquareDistToPos(targetpos.x, targetpos.y, targetpos.z) <= dist)
			{
				m_suddenIllnessBuffTraceTick--;
				if (m_suddenIllnessBuffTraceTick <= 0)
				{
					m_suddenIllnessBuffTraceTick = 15;
					attackActor(target, SEQ_ATTACK);
				}
			}
			else {
				setLookAt(target, 30.0, 30.0);
				m_suddenIllnessBuffTraceTick--;
				if (attrib->getMoveSpeed() != 0 && m_suddenIllnessBuffTraceTick <= 0)
				{
					m_suddenIllnessBuffTraceTick = 4 + GenRandomInt(0, 6);
					getNavigator()->tryMoveTo(target, 1.5);
				}
			}
		}
	}
	else {
		if (m_isSuddenIllnessFindMob)
		{
			if (targetComponent && targetComponent->getTarget())
			{
				targetComponent->setTarget(NULL);
			}
			if (m_bb) {
				BTLuaData data;
				data.SetValue_Object(0);
				m_bb->SetDataLua("bbKeyBeHurtTargetObjId", &data);
				data.Clear();
				data.SetValue_Object(0);
				m_bb->SetDataLua("enemy_id", &data);
			}
			m_isSuddenIllnessFindMob = false;
		}
	}
}

bool ClientMob::canDespawn()
{
	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		return pClientFlyComponent->canDespawn();
	}

	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		return pClientAquaticComponent->canDespawn();
	}

	if (getPersistance())
	{
		return false;
	}
#if (DEBUG_MODE || PROFILE_MODE)
    //调试代码
    if (m_Def->ID == 3095) {
        return false;
    }
#endif
	// 动物不执行远离玩家删除的判断，当玩家传送到其他星球，之前的chunk释放，动物不立即删除
	if (m_Def->Type == MOB_PASSIVE || m_Def->Type == MOB_RARE) return false;
	// 驯服的萌眼叮叮、咚咚、当当，传送不立即删除
	else if (m_Def->ID == 3124 || m_Def->ID == 3125 || m_Def->ID == 3126)
	{
		return false;
	}
	else return true;
}

void ClientMob::pickUpLoot()
{
	CollideAABB box;
	getLocoMotion()->getCollideBox(box);
	box.expand(100, 0, 100);

	std::vector<IClientActor *>actors;
	m_pWorld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_DROPITEM);

	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientItem* item = static_cast<ClientItem*>(actors[i]);
		EQUIP_SLOT_TYPE slot = item->getItemArmorPosition();
		if (slot == MAX_EQUIP_SLOTS)  continue;

		BackPackGrid* curritem = getMobAttrib()->getEquipGrid(slot);

		bool replace = true;

		if (curritem && curritem->def)
		{
			const ToolDef* tooldef1 = GetDefManagerProxy()->getToolDef(item->m_ItemData.def->ID);
			const ToolDef* tooldef2 = GetDefManagerProxy()->getToolDef(curritem->def->ID);

			//if (slot == EQUIP_WEAPON)
			{
				// 装备替换用新字段MarkScore code-by:liya
				if (tooldef1->MarkScore == tooldef2->MarkScore)
				{
					replace = item->m_ItemData.getDuration() > curritem->getDuration();
				}
				else
				{
					replace = tooldef1->MarkScore > tooldef2->MarkScore;
				}
			}
			/*else
			{
				if (tooldef1->Armors[0] + tooldef1->Armors[1] + tooldef1->Armors[2] == tooldef2->Armors[0] + tooldef2->Armors[1] + tooldef2->Armors[2])
				{
					replace = item->m_ItemData.getDuration() > curritem->getDuration();
				}
				else
				{
					replace = tooldef1->Armors[0] + tooldef1->Armors[1] + tooldef1->Armors[2] > tooldef2->Armors[0] + tooldef2->Armors[1] + tooldef2->Armors[2];
				}
			}*/
		}

		// 野人战士和野人投矛手，捡装备限定（武器除外） code-by:liya
		if ((m_Def->ID == 3101 || m_Def->ID == 3105) && slot != EQUIP_WEAPON)
		{
			if (!canEquipByPickup(item->m_ItemData.def->ID))
			{
				replace = false;
			}
		}

		// 野人投矛手不可替换武器 code-by:liya
		if (m_Def->ID == 3105 && slot == EQUIP_WEAPON)
		{
			replace = false;
		}

		if (replace)
		{
			pickItem(item);
		}
	}
}

int ClientMob::getItemSkillTargetCamp(int skillId)
{
	if (skillId > 0)
	{
		const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(skillId);
		if (skilldef)
		{
			return skilldef->TargetCamp;
		}
	}

	return 0;
}

bool ClientMob::canAttackByItemSkill(int skillId, ClientPlayer* player)
{
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(skillId);
	if (skilldef)
	{
		return ((getDef()->Type == skilldef->TargetClass || skilldef->TargetClass == -1)
			&& (skilldef->TargetID == 0 || (skilldef->TargetID && skilldef->TargetID == getDef()->ID))
			&& (skilldef->TargetCamp == 0 || (skilldef->TargetCamp == 1 && getTeam() == player->getTeam()) || (skilldef->TargetCamp == 2 && (getTeam() != player->getTeam() || getTeam() == 0))));
	}
	else
	{
		return false;
	}
}

void ClientMob::dropBagsAll()
{
	auto* mobAttrib = dynamic_cast<MobAttrib*>(getAttrib());
	if (mobAttrib)
	{
		mobAttrib->dropBagsItems();
	}
}

int ClientMob::sleepInBed(const WCoord& blockpos)
{
	if (m_pWorld == NULL)
		return 0;

	auto CarryComp = getCarryComponent();
	if (CarryComp && CarryComp->isCarried())
		return STRDEF_SLEEP_STATUS;

	if (!m_pWorld->isRemoteMode())
	{
		if (getSitting() || isSleeping() || isDead())
		{
			return STRDEF_SLEEP_STATUS;
		}

		if (!m_pWorld->hasSky())
		{
			return STRDEF_SLEEP_NOTHERE;
		}

		WorldCanvas* container = dynamic_cast<WorldCanvas*>(BedLogicHandle::getCoreContainer(m_pWorld, blockpos));
		if (container && container->getStage() == 2)  //破败的帐篷不能睡觉
		{
			return STRDEF_SLEEP_STATUS;
		}


		/*if (m_pWorld->isDaytime())
		{
			return STRDEF_SLEEP_DAYTIME;
		}*/

		WCoord curblock = CoordDivBlock(getPosition());
		if (Rainbow::Abs(curblock.x - blockpos.x) > 3 || Rainbow::Abs(curblock.y - blockpos.y) > 2 || Rainbow::Abs(curblock.z - blockpos.z) > 3)
		{
			return STRDEF_SLEEP_TOOFAR;
		}

	}

	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		mountActor_mob(NULL);
	}

	getLocoMotion()->m_yOffset = 0;

	if (m_pWorld->blockExists(blockpos))
	{
		int blockdata = m_pWorld->getBlockData(blockpos);
		int dir = blockdata & 3;
		int blockid = m_pWorld->getBlockID(blockpos);

		static int PosOffset[4][2] = { { 90,50 },{ 10,50 },{ 50,90 },{ 50,10 } };
		if (blockid == BLOCK_CANVAS)
		{
			WCoord sleepPos = BedLogicHandle::getSleepPosition(m_pWorld, blockpos);
			getLocoMotion()->setPosition(sleepPos.x, sleepPos.y, sleepPos.z);
		}
		else if (blockid == BLOCK_SHELLBED)//贝壳床
		{
			float shellbedOffXArray[4] = { 40.0f ,-40.0f, 50.0f, -50.0f };
			float shellbedOffZArray[4] = { -50.0f, 50.0f, 40.0f, -40.0f };

			WCoord sleepPos = BedLogicHandle::getSleepPosition(m_pWorld, blockpos);
			sleepPos.x += shellbedOffXArray[dir];
			sleepPos.z += shellbedOffZArray[dir];
			sleepPos.y += 100;
			getLocoMotion()->setPosition(sleepPos.x, sleepPos.y, sleepPos.z);
		}
		else
		{
			WCoord playerpos = blockpos * BLOCK_SIZE + WCoord(50, 80, 50);
			getLocoMotion()->setPosition(playerpos.x, playerpos.y, playerpos.z);
		}
		static float YawArray[4] = { 90.0f, -90.0f, 0.0f, 180.0f };
		getLocoMotion()->m_RotateYaw = YawArray[dir];
		getLocoMotion()->m_RotationPitch = -15.0f;
		LivingLocoMotion* motion = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
		if (motion != NULL)
		{
			motion->clearTarget();
		}
	}
	else
	{
		WCoord playerpos = blockpos * BLOCK_SIZE + WCoord(50, 50, 50);
		getLocoMotion()->setPosition(playerpos.x, playerpos.y, playerpos.z);
		getLocoMotion()->m_RotateYaw = 0.0f;
		getLocoMotion()->m_PrevRotatePitch = 15.0f;
	}

	int blockid = m_pWorld->getBlockID(blockpos);
	if (IsBedBlock(blockid))
	{
		BedLogicHandle::setBedOccupied(m_pWorld, blockpos, true);
	}

	setSleeping(true);
	getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);

	if (IsSleepingbagBlock(blockid) && getBody()) {
		getBody()->show(false, true);
	}

	//睡觉模型变化
	m_Body->show(!BedLogicHandle::IsSleepNeedHide(m_pWorld, blockpos), true);

	if (m_pWorld && !m_pWorld->isRemoteMode())  //睡帐篷加个消息给客机隐藏模型，以后有重构看是否可以把这个放到actorbody里去做
	{
		jsonxx::Object object;
		object << "actorId" << getObjId();
		object << "b" << !BedLogicHandle::IsSleepNeedHide(m_pWorld, blockpos);
		object << "ignorenamedispobj" << true;
		object << "ignoremotion" << true;
		int size = 0;
		unsigned char* p = NULL;
		object.saveBinary(p, size);
		GetSandBoxManager().sendToTrackingPlayers(this, (char*)("ACOTRBODY_SHOW"), p, size);
		free(p);
	}

	return 0;
}


void ClientMob::wakeUp()
{
	getLocoMotion()->m_yOffset = 0;
	WCoord blockpos = CoordDivBlock(getPosition());
	int blockid = m_pWorld->getBlockID(blockpos);
	bool isfind = false;
	if (IsBedBlock(blockid))
	{
		isfind = true;
	}
	else
	{
		blockpos = getBedBindPos();
		blockid = m_pWorld->getBlockID(blockpos);
		if (IsBedBlock(blockid))
		{
			isfind = true;
		}
	}
	if (isfind)
	{
		WCoord newpos = blockpos;
		BedLogicHandle::setBedOccupied(m_pWorld, blockpos, false);
		bool succeed = BedLogicHandle::getNearestEmptyChunkCoordinates(newpos, m_pWorld, blockpos, 0);

		if (!succeed)
		{
			newpos = TopCoord(blockpos);
		}

		getLocoMotion()->gotoPosition(BlockCenterCoord(newpos), getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
	}

	if (getBody()) {
		getBody()->show(true, true);
		if (m_pWorld && !m_pWorld->isRemoteMode())  //睡帐篷加个消息给客机隐藏模型，以后有重构看是否可以把这个放到actorbody里去做
		{
			jsonxx::Object object;
			object << "actorId" << getObjId();
			object << "b" << true;
			object << "ignorenamedispobj" << true;
			object << "ignoremotion" << true;
			int size = 0;
			unsigned char* p = NULL;
			object.saveBinary(p, size);
			GetSandBoxManager().sendToTrackingPlayers(this, (char*)("ACOTRBODY_SHOW"), p, size);
			free(p);
		}
	}
	setSleeping(false);
}

int ClientMob::onFeed(int itemid, int feedType)
{
	float healVal = 0.0f;
	if (feedType == 0) // 原喂食回血逻辑
	{
		const FoodDef* fooddef = GetDefManagerProxy()->getFoodDef(itemid);
		if (fooddef == NULL || fooddef->HealAnimal <= 0) return 0;

		healVal = (float)fooddef->HealAnimal;
	}
	else // 饲料槽饲料回血 code-by:lizb
	{
		healVal = 50.0f;
	}

	if (getAttrib()->getHP() < getAttrib()->getMaxHP())
	{
		getAttrib()->addHP(healVal);
		return 1;
	}
	return -1;
}

void ClientMob::onFeedCallback(int breedType, int feed_ret, ClientPlayer* player)
{
	bool itemused = feed_ret == 1;

	if (breedType == -1) //吃的饲料槽食物
	{
		itemused = false;
		//非加血情况 判断是否可成长
		if (feed_ret != 1)
			//m_iCurNeedFeedNum--;
			setCurNeedFeedNum(getCurNeedFeedNum() - 1);
	}

	if (breedType == BREED_ITEM_QUITE && !isInQuite() && getReadyToQuit() <= 0)
	{
		itemused = true;
		if (m_Def->ID == 3400 || m_Def->ID == 3402)
		{
			//要求等待一段时间再进入睡眠
			//m_iReadyToQuit = GenRandomInt(100, 200);
			//m_iRealQuitTick = 1000;
			setReadyToQuit(GenRandomInt(100, 200));
			setRealQuit(1000);
		}
		else
		{
			setQuiteTick(1000);
		}
	}
	else if (breedType == BREED_ITEM_LOVE || breedType == -1)
	{
		// 手持道具交互 成长繁殖
		if (breedType == BREED_ITEM_LOVE)
			//m_bEaten = true;
			setEaten(true);

		if (!isAdult()) itemused = true;

		// 成年且满足繁殖条件(不在繁殖cd且满足进食需求)
		if (isAdult() && !isInLove() && hadFeedFood())
		{
			setInLove(600);
			itemused = true;

			if (m_Def && m_Def->ID == 3407)
			{
				getMobAttrib()->addFood(m_Def->Food);
			}
		}
	}

	if (!isInLove() && !isInQuite() && breedType != -1)
	{
		if (feed_ret == 1)
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(BODYFX_TAME_FOOD);
			}
		}
		else if (feed_ret == -1)
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(BODYFX_TAME_NOFOOD);
			}
		}
	}

	if (player && itemused) player->shortcutItemUsed();
}

void ClientMob::onFeedHeldFodderCallback(int breedType, int feed_ret, ClientPlayer* player)
{
	bool itemused = feed_ret == 1;
	bool adult = isAdult();
	//手持饲料
	if (!adult)
	{
		//非加血情况 判断是否可成长
		//if (feed_ret != 1)
			//m_iCurNeedFeedNum--;
		int num = getCurNeedFeedNum();
		if (num > 0)
		{
			itemused = true;
			setCurNeedFeedNum(num - 1);
		}
		//GetWorldManagerPtr()->syncBattlePassEventToClient(NULL, "feed", m_Def->ID, "1");
	}
	if (breedType == BREED_ITEM_QUITE && !isInQuite() && getReadyToQuit() <= 0)
	{
		itemused = true;
		if (m_Def->ID == 3400 || m_Def->ID == 3402)
		{
			//要求等待一段时间再进入睡眠
			//m_iReadyToQuit = GenRandomInt(100, 200);
			//m_iRealQuitTick = 1000;
			setReadyToQuit(GenRandomInt(100, 200));
			setRealQuit(1000);
		}
		else
		{
			setQuiteTick(1000);
		}
	}
	else if (breedType == BREED_ITEM_LOVE && adult)
	{
		// 手持道具交互 成长繁殖
		//setEaten(true);

		//if (!isAdult()) itemused = true;

		// 成年且满足繁殖条件(不在繁殖cd且满足进食需求)
		if (!isInLove())
		{
			setInLove(600);
			itemused = true;

			if (m_Def && m_Def->ID == 3407)
			{
				getMobAttrib()->addFood(m_Def->Food);
			}
		}
	}

	if (!isInLove() && !isInQuite() && breedType != -1 && !itemused)
	{
		if (feed_ret == 1)
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(BODYFX_TAME_FOOD);
			}
		}
		else if (feed_ret == -1)
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(BODYFX_TAME_NOFOOD);
			}
		}
	}

	if (player && itemused) player->shortcutItemUsed();
}

bool ClientMob::interact(ClientActor* player, bool onshift/* =false */, bool isMobile)
{
	//Fire<int, AutoRef<SandboxNode>, bool>(MOB_SCRIPTNOTIFY_INTERACT, player, onshift);
	bool ret = false;
	bool retInteract = ClientActor::interact(player, onshift, isMobile);
	
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		int currItemID = pTempPlayer->getCurToolID();
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("AIFunctionMgr_ClientMob_Interact",
			SandboxContext(nullptr).
			SetData_Number("objid", getObjId()).
			SetData_Number("uin", pTempPlayer->getUin()).
			SetData_Number("toolid", currItemID)
		);

		pTempPlayer->updateTaskSysProcess(TASKSYS_INTERACT_ACTOR, currItemID, m_Def->ID);
		//篝火附近吃肉后睡觉的野人被玩家唤醒
		if (checkIfSavageSleeping() && pTempPlayer)
		{
			if (currItemID == 12599 && !needClear() && getBody() && getBody()->getNowPlaySeqID() == getBody()->seqType2ID(SEQ_MOBSLEEP)) {
				pTempPlayer->shortcutItemUsed();;
				playAnim(SEQ_SAVAGE_CRUEL);
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("mob_3200_freedom1");
				}
				auto sound = getSoundComponent();
				if (sound)
				{
					sound->playSound("misc.hit1", 1.0f, 1.0f / (GenRandomFloat() * 0.4f + 0.8f));
				}

				ParticlesComponent::playParticles(this, "ball_hit.ent");

				setNeedClear(60);
				m_iTickAnimAwake = 59;
				//按产品说得，被敲得野人都要被敲醒，但只有一个被驯服，所以其他野人要恢复到原来的状态
				//by David, 2024/5/17
				WorldBonFire* container = dynamic_cast<WorldBonFire*>(getWorld()->getContainerMgr()->getContainer(getBonFirePos()));
				if (container) {
					container->wakeMobAndEndAttract();

					ClientMob* pMob = NULL;
					const int mobIds[3] = { 3101, 3102, 3105 };
					for (unsigned int i = 0; i < sizeof(mobIds) / sizeof(int); i++) {
						std::vector<ClientActor*> mobsTemp = selectAllMobs(mobIds[i], 0, GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.awake_range * 100);
						for (unsigned int j = 0; j < mobsTemp.size(); j++) {
							pMob = dynamic_cast<ClientMob*>(mobsTemp[j]);
							if (pMob && pMob->checkIfSavageSleeping()) {
								pMob->setFlagBit(ACTORFLAG_AI_SLEEP, false);
								pMob->setFlagBit(ACTORFLAG_AI_WARNING, true);
								pMob->setPersistance(false);
								pMob->setTraceDist(1000);
								pMob->SetDanceState(0);
							}
						}
					}
				}
				return true;
			}
		}

		int breed = isBreedItem(currItemID);
		if (breed > 0)
		{
			//2023.9.20冒险优化, 可以直接手持通过草饲料喂食, 为了不与饲料槽喂食有差别,都传-1
			int feed_ret = 0;
			if ((currItemID == 11534 || currItemID == 11535))
			{
				feed_ret = onFeed(currItemID, 1);
				onFeedHeldFodderCallback(breed, feed_ret, pTempPlayer);
			}
			else
			{
				feed_ret = onFeed(currItemID);
				onFeedCallback(breed, feed_ret, pTempPlayer);
			}
			ret = true;
		}
		else
		{
			if (!m_Def->InteractionFunction.empty())
			{
				MINIW::ScriptVM::game()->callFunction(m_Def->InteractionFunction.c_str(), "u[ClientMob]u[ClientPlayer]>b", this, pTempPlayer, &ret);
			}
			else
			{
				char szScriptFun[256];
				if (getIsHomeLandPet()) //是宠物共用同一套
				{
					snprintf(szScriptFun, sizeof(szScriptFun), "F%d_Interact", 10001);
				}
				else
				{
					snprintf(szScriptFun, sizeof(szScriptFun), "F%d_Interact", getDef()->ID);
				}
				MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]u[ClientPlayer]>b", this, pTempPlayer, &ret);
				//交互完成后某些角色头顶信息可能发生改变
				checkActorDisplayName(getDef()->ID, !ret);
			}
		}

		//鞍交互
		if (currItemID == 11810)
		{
			//驯服后的焱焱蟹变成坐骑比较特殊(需换模型:3896)
			if (getDefID() == 3882)
			{
				ClientMob* newmob = ClientMob::createFromDef(3896);
				newmob->setTamedOwnerUin(getTamedOwnerID());
				newmob->getLocoMotion()->gotoPosition(getLocoMotion()->m_Position, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
				ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
				if (!actorMgr) return false;
				actorMgr->spawnActor(newmob);

				setNeedClear();

				newmob->interact(getTamedOwner());
			}
		}

		// 虚空生物使用道具
		auto pVacantComponent = getVacantComponent();
		if (!ret && pVacantComponent && pVacantComponent->interact(pTempPlayer, currItemID))
		{
			ret = true;
		}

		if (retInteract)
		{
			return true;
		}
		else
		{
			return ret;
		}
	}
	else
	{
		if (retInteract)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
}

bool ClientMob::attackActorRanged(ClientActor* target)
{
	setAtkingTarget(target);
	if (getAttackingTargetComponent())
		getAttackingTargetComponent()->setAttackAnim(ATTACK_RANGE);
	playAnim(SEQ_ATTACK);
	return true;
}

void ClientMob::doProjectileAttack(ClientActor* target, int projectileId, float power, int buffId, int count, int dropType)
{
	if (target == NULL) return;
	if (!checkIfActorCanAttack())return;

	this->setBeHurtTarget(target);
	target->setBeAtk(this);
	if (m_pWorld && !m_pWorld->isRemoteMode()) {
		ObserverEvent_Actor obevent(getObjId(), getBeHurtTargetID());
		obevent.SetData_Actor(this->getDefID());
		if (ClientActorHelper::isTriggerCreatureType(target->getObjType()))
		{
			obevent.SetData_TargetActorID(target->getDefID());
		}
		GetObserverEventManager().OnTriggerEvent("Actor.Attack", &obevent);
	}

	if (count == 1)
	{
		ProjectileFactory::throwItemByTarger(m_pWorld, this, target, power, projectileId, buffId, 0, 0, dropType);
	}
	else if (count > 1)
	{
		ProjectileFactory::throwItemByTarger(m_pWorld, this, target, power, projectileId, buffId, 0, 0, dropType);
		for (int i = 1; i < count; i++)
		{
			ProjectileFactory::throwItemByTarger(m_pWorld, this, target, power, projectileId, buffId, 50, 20, dropType);
		}
	}

	auto id = getDef()->ID;
	if (id == 3508 || id == 3509)
	{
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3508.lavaball", 1.0f, 1.0f / (GenRandomFloat() * 0.4f + 0.8f));
		}
	}
	else if (id == 3822 || id == 3823 || id == 3826 || id == 3827)
	{
		auto sound = getSoundComponent();
		if (getDef() && sound)
		{
			sound->playSound(getDef()->AttackSound2.c_str(), 1.0f, 1.0f / (GenRandomFloat() * 0.4f + 0.8f));
		}
	}
	else
	{
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound("misc.bow", 1.0f, 1.0f / (GenRandomFloat() * 0.4f + 0.8f));
		}
	}

	/*
		setAtkingTarget(target);

		m_AttackAnimType = ATTACK_RANGE;
		m_AttackAnimTicks = getAttackAnimTicks(m_AttackAnimType);

		playAnim(SEQ_ATTACK);*/



		/*
			bool setfire = getDef()->ID == 3131;
			ARROW_TYPE arrowtype = getDef()->ID == 3131 ? ARROW_NORMAL : ARROW_SPEAR;
			if (getDef()->ID == 3508 || getDef()->ID == 3509)
			{
				arrowtype = ARROW_FIREBALL;
			}

			ClientActorArrow *arrow = ClientActorArrow::shootArrow(m_pWorld, this, target, 1.1f*BLOCK_FSIZE, 14.0f -/ *m_pWorld->getDifficultySetting()* /3 * 4.0f, setfire, arrowtype);

			arrow->m_AttackPoints = getLivingAttrib()->getAttackPoint(ATTACK_RANGE);
			arrow->m_KnockbackStrength = getLivingAttrib()->getKnockback(ATTACK_RANGE, ATTACK_TARGET_ALL);*/


}
void  ClientMob::doGunAttack(ClientActor* target, int projectileId, int count, int attackType)
{
	if (target == NULL) return;
	if (!checkIfActorCanAttack())return;

	this->setBeHurtTarget(target);
	target->setBeAtk(this);
	if (m_pWorld && !m_pWorld->isRemoteMode()) {
		ObserverEvent_Actor obevent(getObjId(), getBeHurtTargetID());
		obevent.SetData_Actor(this->getDefID());
		if (ClientActorHelper::isTriggerCreatureType(target->getObjType()))
		{
			obevent.SetData_TargetActorID(target->getDefID());
		}
		GetObserverEventManager().OnTriggerEvent("Actor.Attack", &obevent);
	}
	if (count == 1)
	{
		ProjectileFactory::throwItemByGun(m_pWorld, this, target, 1, projectileId, false, false, attackType);
	}
	else if (count > 1)
	{
		//ProjectileFactory::throwItemByGun(m_pWorld, this, target, 1, projectileId, false, false, attackType);
		for (int i = 0; i < count; i++)
		{
			ProjectileFactory::throwItemByGun(m_pWorld, this, target, 1, projectileId, false, false, attackType);
		}
	}
	/*auto sound = getSoundComponent();
	if (sound)
	{
		sound->playSound("misc.bow", 1.0f, 1.0f / (GenRandomFloat() * 0.4f + 0.8f));
	}*/

}

void ClientMob::doGunAttacks(ClientActor* target, int projectileId,const std::vector<int> & attackTypes)
{
	if (target == NULL) return;
	if (!checkIfActorCanAttack())return;

	this->setBeHurtTarget(target);
	target->setBeAtk(this);
	if (m_pWorld && !m_pWorld->isRemoteMode()) {
		ObserverEvent_Actor obevent(getObjId(), getBeHurtTargetID());
		obevent.SetData_Actor(this->getDefID());
		if (ClientActorHelper::isTriggerCreatureType(target->getObjType()))
		{
			obevent.SetData_TargetActorID(target->getDefID());
		}
		GetObserverEventManager().OnTriggerEvent("Actor.Attack", &obevent);
	}
	int attacktype;
	for (int i = 0; i < attackTypes.size(); i++)
	{
		attacktype = attackTypes.at(i);
		ProjectileFactory::throwItemByGun(m_pWorld, this, target, 1, projectileId, false, false, attacktype);
	}
}


void ClientMob::doActualAttack(ClientActor* target, int targetIndex)
{
	if (target == NULL) return;

	float dist = (float)this->getSquareDistToActor(target);
	float atkDist = m_Def->AttackDistance + m_Def->Width * m_Def->ModelScale / 2;

	if (m_Def->AttackType == 0 && dist > atkDist * BLOCK_SIZE * atkDist * BLOCK_SIZE)
	{
		return;
	}

	bool ret = true;
	char szScriptFun[256];
	snprintf(szScriptFun, sizeof(szScriptFun), "F%d_AttackEntityAsMob", getDef()->ID);
	MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]u[ClientActor]>b", this, target, &ret);

	if (!ret) return;
	if (!checkIfActorCanAttack())return;
	// 扒手攻击不触发 AITargetHurtee
	if (!getFlagBit(ACTORFLAG_AI_ESCAPE))
	{
		//target->setBeHurtTarget(this);
		this->setBeHurtTarget(target);
	}
	target->setBeAtk(this);
	if (m_pWorld && !m_pWorld->isRemoteMode()) {
		ObserverEvent_Actor obevent(getObjId(), getBeHurtTargetID());
		obevent.SetData_Actor(this->getDefID());
		if (ClientActorHelper::isTriggerCreatureType(target->getObjType()))
		{
			obevent.SetData_TargetActorID(target->getDefID());
		}
		GetObserverEventManager().OnTriggerEvent("Actor.Attack", &obevent);
	}

	LivingAttrib* attrib = getLivingAttrib();

	OneAttackData atkdata;

	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;

	//Hard code for Year monster!
	if (m_Def->ID == 3505 || m_Def->ID == 3506)
	{
		atkdata.buffId = BECOME_CHIKEN_BUFF;
		atkdata.buffLevel = 1;
	}
	else if (m_Def->ID == 3120)
	{
		atkdata.buffId = BECOME_PIG_BUFF;
		atkdata.buffLevel = 1;
	}
	else if (m_Def->ID == 3824)//code-by:Logo 沙漠小蝎子几率中毒buff
	{
		setIsAttackStatus(true);//战斗状态
		atkdata.buffId = -1;
		atkdata.buffLevel = -1;
		if (GenRandomInt(100) <= 20)
		{
			if (m_addBuffCount >= 5)//转换剧毒
			{
				m_addBuffCount = 0;
				atkdata.buffId = HEGHLY_TOXIC_INDEX / 1000;
				atkdata.buffLevel = HEGHLY_TOXIC_INDEX % 1000;
			}
			else
			{
				ClientPlayer* player = dynamic_cast<ClientPlayer*>(target);
				if (player)
				{
					if (!player->getPlayerAttrib()->hasBuff(1018))
					{
						m_addBuffCount = 0;
					}
					if (!player->getPlayerAttrib()->hasBuff(1016))
					{
						int buffId = m_Def->BuffId + m_addBuffCount;
						if (buffId >= SCORPION_VENOM_MIN && buffId <= SCORPION_VENOM_MAX)
						{
							atkdata.buffId = buffId / 1000;
							atkdata.buffLevel = buffId % 1000;
							m_addBuffCount++;
						}
					}
				}

			}
		}
	}
	else if (m_Def->ID == 3829)//code-by:Logo 沙漠大蝎子几率中毒buff
	{
		setIsAttackStatus(true);//战斗状态
		atkdata.buffId = -1;
		atkdata.buffLevel = -1;
		if (GenRandomInt(100) <= 30 && !getIsBrokenTail())
		{
			atkdata.buffId = m_Def->BuffId / 1000;
			atkdata.buffLevel = m_Def->BuffId % 1000;
		}
	}
	else if (m_Def->ID != 0)
	{
		atkdata.buffId = m_Def->BuffId / 1000;
		atkdata.buffLevel = m_Def->BuffId % 1000;
	}
	else
	{
		atkdata.buffId = -1;
		atkdata.buffLevel = -1;
	}

	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
		attackProcessNew(atkdata, target);
	else
		attackProcess(atkdata, target);

	//如果是野人,增加饥饿值
	if (getObjType() == OBJ_TYPE_VILLAGER)
	{
		VillagerAttrib* attrib = dynamic_cast<VillagerAttrib*>(getAttrib());
		int val = 0;
		MINIW::ScriptVM::game()->callFunction("AIFunctionDefs_GetHungryReduceByType", "s>i", "attack", &val);
		attrib->addHugger(val);
	}
}

void ClientMob::doActualRangeAttack(ClientActor* target)
{
	if (target == NULL) return;
	if (!checkIfActorCanAttack())return;

	this->setBeHurtTarget(target);
	target->setBeAtk(target);
	if (m_pWorld && !m_pWorld->isRemoteMode()) {
		ObserverEvent_Actor obevent(getObjId(), getBeHurtTargetID());
		obevent.SetData_Actor(this->getDefID());
		if (ClientActorHelper::isTriggerCreatureType(target->getObjType()))
		{
			obevent.SetData_TargetActorID(target->getDefID());
		}
		GetObserverEventManager().OnTriggerEvent("Actor.Attack", &obevent);
	}

	bool setfire = (getDef()->ID == 3131) || m_bAiFire;
	ARROW_TYPE arrowtype = getDef()->ID == 3131 ? ARROW_NORMAL : ARROW_SPEAR;
	if (getDef()->ID == 3508 || getDef()->ID == 3509)
	{
		arrowtype = ARROW_FIREBALL;
	}

	ClientActorArrow* arrow = ClientActorArrow::shootArrow(m_pWorld, this, target, 1.1f * BLOCK_FSIZE, 14.0f -/*m_pWorld->getDifficultySetting()*/3 * 4.0f, setfire, arrowtype);

	arrow->m_AttackPoints = getLivingAttrib()->getAttackPoint(ATTACK_RANGE, 1);
	arrow->m_KnockbackStrength = getLivingAttrib()->getKnockback(ATTACK_RANGE, ATTACK_TARGET_ALL);
	if (getDef()->ID == 3508 || getDef()->ID == 3509)
	{
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3508.lavaball", 1.0f, 1.0f / (GenRandomFloat() * 0.4f + 0.8f));
		}
	}
	else
	{
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound("misc.bow", 1.0f, 1.0f / (GenRandomFloat() * 0.4f + 0.8f));
		}
	}

	//如果是野人,增加饥饿值
	if (getObjType() == OBJ_TYPE_VILLAGER)
	{
		VillagerAttrib* attrib = dynamic_cast<VillagerAttrib*>(getAttrib());
		int val = 0;
		MINIW::ScriptVM::game()->callFunction("AutoAddCurShortCutItem", "s>i", "attack", &val);
		attrib->addHugger(val);
	}
}

void ClientMob::attackProcess(OneAttackData& atkdata, ClientActor* target)
{
	bool damage_armor = true;
	LivingAttrib* attrib = getLivingAttrib();

	atkdata.atktype = (ATTACK_TYPE)m_Def->AttackType;
	ATTACK_TARGET_TYPE targettype = target->getAttackTargetType();

	atkdata.atkpoints = attrib->getAttackPoint(atkdata.atktype);
	atkdata.enchant_atk = attrib->getEnchantAttackPoint(atkdata.atktype, targettype);
	atkdata.buff_atk = attrib->getModAttrib(MODATTR_ATTACK_PUNCH + atkdata.atktype);

	atkdata.knockback = attrib->getKnockback(atkdata.atktype, targettype);
	if (m_Def->ID == 3621)
	{
		atkdata.knockback = 0;
	}
	auto component = target->getAttackedComponent();
	if (component && component->attackedFrom(atkdata, this))
	{
		damage_armor = false;
		if (atkdata.knockback > 0)
		{
			//Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
			//target->getLocoMotion()->addMotion(dir.x*knockback*0.5f, 0.1f, dir.z*knockback*0.5f);
			getLocoMotion()->m_Motion *= 0.6f;
		}

		int bufflv;
		int buffid = getLivingAttrib()->getFireAspect(bufflv);
		if (buffid > 0)
		{
			auto FireBurnComp = target->sureFireBurnComponent();
			if (FireBurnComp)
			{
				FireBurnComp->setFire(buffid, bufflv);
			}

		}
	}

	//元素伤害
	for (int i = 0; i < MAX_MAGIC_ATTACK - MAX_PHYSICS_ATTACK; i++)
	{
		//memset(&atkdata, 0, sizeof(atkdata));
		atkdata.damage_armor = damage_armor;
		atkdata.atktype = (ATTACK_TYPE)(MAX_PHYSICS_ATTACK + i);
		atkdata.atkpoints = attrib->getAttackPoint(atkdata.atktype);
		atkdata.buff_atk = attrib->getModAttrib(MODATTR_ATTACK_PUNCH + atkdata.atktype);
		atkdata.ignore_resist = true;

		if (atkdata.atkpoints > 0)
		{
			auto component = target->getAttackedComponent();
			if (component)
			{
				component->attackedFrom(atkdata, this);
			}
		}
	}
}

void ClientMob::attackProcessNew(OneAttackData& atkdata, ClientActor* target)
{
	LivingAttrib* attrib = getLivingAttrib();
	ATTACK_TARGET_TYPE targettype = target->getAttackTargetType();

	float atkPoints = 0.f;
	// 近战接口，只拿近战攻击力
	if (m_Def->AttackType == 0)
	{
		atkdata.atkTypeNew |= (1 << ATTACK_PUNCH);
		atkdata.atkPointsNew[ATTACK_PUNCH] = attrib->getAttackPoint(ATTACK_PUNCH);
	}

	for (int atkType = ATTACK_FIRE; atkType <= MAX_MAGIC_ATTACK; atkType++)
	{
		const int index = AtkType2ArmorIndex(ATTACK_TYPE(atkType));
		atkPoints = attrib->getAttackPoint((ATTACK_TYPE)atkType);
		if (atkPoints > 0)
		{
			atkdata.atkTypeNew |= (1 << index);
			atkdata.atkPointsNew[index] = atkPoints;
			// 附魔攻击力加成（单攻击类型攻击力加成以及所有攻击力加成）
			atkdata.atkPointsNew[index] += attrib->getEnchantAttackPoint((ATTACK_TYPE)atkType, targettype);
		}
	}

	atkdata.knockback = attrib->getKnockback(atkdata.atktype, targettype);
	if (m_Def->ID == 3621)
	{
		atkdata.knockback = 0;
	}
	auto component = target->getAttackedComponent();
	if (component && component->attackedFrom(atkdata, this))
	{
		if (atkdata.knockback > 0)
		{
			//Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
			//target->getLocoMotion()->addMotion(dir.x*knockback*0.5f, 0.1f, dir.z*knockback*0.5f);
			getLocoMotion()->m_Motion *= 0.6f;
		}

		int bufflv;
		int buffid = getLivingAttrib()->getFireAspect(bufflv);
		if (buffid > 0)
		{
			auto FireBurnComp = target->sureFireBurnComponent();
			if (FireBurnComp)
			{
				FireBurnComp->setFire(buffid, bufflv);
			}

		}
	}
}

void ClientMob::applyDisplayHomeBillBoard()
{
#ifndef IWORLD_SERVER_BUILD
	if (getBody() && m_Def)
	{
		//稻草人
		if (m_Def->ID == 3121)
		{
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
			{
				getBody()->setDispayHomeBillBoard();
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
					Emit("Homeland_setScarecrowObjId", SandboxContext(nullptr).SetData_Number("objid", getObjId()));
			}
		}
	}
#endif
}

bool ClientMob::attackedFrom(OneAttackData& atkdata, ClientActor* attacker)
{
	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		return pClientAquaticComponent->attackedFrom(atkdata, attacker);
	}

	return todoAttackedFrom(atkdata, attacker);
}

bool ClientMob::todoAttackedFrom(OneAttackData& atkdata, ClientActor* attacker)
{
	//Fire<int, AutoRef<SandboxNode>>(MOB_SCRIPTNOTIFY_ATTACKED, attacker);
	if (atkdata.fromplayer)
	{
		attacker = dynamic_cast<ClientActor*>(atkdata.fromplayer);
	}

	//攻击怪物
	if (attacker && (attacker->getObjType() == OBJ_TYPE_ROLE) && dynamic_cast<PlayerControl*>(attacker) && m_Def)
	{
		MNSandbox::GetGlobalEvent().Emit<int, ClientActor*, int>("StatisticTerrgen_PlayerAttackMonster", m_Def->ID, this, 1);
	}

	//篝火附近吃肉后睡觉的野人被玩家唤醒
	if (checkIfSavageSleeping() && attacker)
	{
		if (needClear()) { return false; }
		if (atkdata.atktype == ATTACK_TYPE::ATTACK_PUNCH) {
			ClientPlayer* pPlayer = dynamic_cast<ClientPlayer*>(attacker);
			if (pPlayer)
			{
				int iItemId = pPlayer->getCurToolID();
				if (iItemId == 12599 && getBody() && getBody()->getNowPlaySeqID() == getBody()->seqType2ID(SEQ_MOBSLEEP)) {
					pPlayer->shortcutItemUsed();
					playAnim(SEQ_SAVAGE_CRUEL);
					auto effectComponent = getEffectComponent();
					if (effectComponent)
					{
						effectComponent->playBodyEffect("mob_3200_freedom1");
					}
					auto sound = getSoundComponent();
					if (sound)
					{
						sound->playSound("misc.hit1", 1.0f, 1.0f / (GenRandomFloat() * 0.4f + 0.8f));
					}
					ParticlesComponent::playParticles(this, "ball_hit.ent");

					setNeedClear(60);
					m_iTickAnimAwake = 59;
					return false;
				}
			}
		}

		setFlagBit(ACTORFLAG_AI_SLEEP, false);
		setFlagBit(ACTORFLAG_AI_WARNING, true);
		SetDanceState(0);
		setTraceDist(1000);
	}


	//对稻草人做特殊处理
	if (m_Def->ID == 3121)
	{
		if (atkdata.atktype != ATTACK_FIRE)
		{
			//		atkdata.atkpoints = 0;
			atkdata.knockback = 0;
		}
	}
	//对61商人做特殊处理
	else if (m_Def->ID == 3017)
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(attacker);
		if (player && !player->getPlayerAttrib()->hasBuff(1008)) player->getPlayerAttrib()->addBuff(1008, 1);
	}
	else if (m_Def->ID == 3416)//对熊猫做特殊处理 2秒内被打两次后跑开
	{
		m_pandaBeenAttacked = m_pandaBeenAttacked + 1;
		if (m_pandaBeenAttacked > 1 && m_pandaBeenAttackedTick < 40 && m_pandaRunAwayTick == 0)
		{
			m_pandaRunAwayTick = 120;//跑开6秒后消失
		}
	}
	//对纪念品做处理,直接死亡爆物品
	/*else if(m_Def->ID == 3203)
	{
		bool ret = false;
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(attacker);
		if (player  && atkdata.atktype == ATTACK_PUNCH)
		{
			MINIW::ScriptVM::game()->callFunction("F3203_Interact", "u[ClientMob]u[ClientPlayer]>b", this, player, &ret);
			onDie();

		}

		return true;
	}*/

	if (atkdata.atktype != ATTACK_SUN)
	{
		if (isInvulnerable(attacker))
		{
			return false;
		}
		// 把闪避拆出来，单独播放闪避动作
		if (isDodge())
		{
			if (m_Def->ID == 3630 && m_pWorld->getBlockID(getLocoMotion()->getPosition() / 100) == 23)
			{
				return false;//荧光棒藻鱼在沉积岩中被攻击不播放动作
			}
			playAnim(SEQ_MOBDODGE);
			return false;
		}
	}

	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ActorHorse* horse = dynamic_cast<ActorHorse*>(RidComp->getRidingActor());
		if (horse)
		{
			bool b = horse->attackedFrom(atkdata, attacker);
			if (horse->isDead()) mountActor_mob(NULL);
			return b;
		}
	}

	if (ActorLiving::attackedFrom(atkdata, attacker))
	{
		setRangeSameTypeMobRunAway(this);
		setAISitting(false);
		setInLove(0);
		setQuiteTick(0);
		setReadyToQuit(0);
		setRealQuit(0);

		if (m_Def->ID == 3123 && attacker && attacker->getObjType() == OBJ_TYPE_ROLE) //外星人 被玩家打才会掉
		{
			static int dropitems[] = { 12572, 12573, 12574, 12565, 12569, 12570, 12571 };
			if (GenRandomInt(100) < 20)
			{
				int n = sizeof(dropitems) / sizeof(int);
				int itemid = dropitems[GenRandomInt(n)];
				auto dropComponent = GetComponent<DropItemComponent>();
				if (dropComponent)
				{
					dropComponent->dropItem(itemid, 1);
				}
			}
		}

		if (!isDead() && m_Def->ID == 3829 && attacker)//code-by:Logo 沙漠的大蝎子未死掉落物
		{
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(attacker);
			int toolId = 0;
			if (player)
			{
				toolId = player->getCurToolID();
			}
			if (toolId == 12005 || toolId == 12003)//断尾
			{
				if (GenRandomInt(100) > 45 && GenRandomInt(100) < 59 && !getIsHurtDropItem())
				{
					playAnim(SEQ_SCORPION_TAIL);
					setIsBrokenTail(true);
					setIsHurtDropItem(true);
					auto dropComponent = GetComponent<DropItemComponent>();
					if (dropComponent)
					{
						dropComponent->dropItem(11633, 1);
					}

					if (getBody())
					{
						getBody()->showNecklace(0);
					}
					if (m_pWorld && !m_pWorld->isRemoteMode())
					{
						jsonxx::Object object;
						object << "objid" << this->getObjId();
						object << "NecklaceID" << 0;
						GetSandBoxManager().sendToTrackingPlayers(this, "MOB_SCORPION_NECKLACE_CHANGE", object.bin(), object.binLen());
					}
				}
			}
			else if (toolId == 12001 || toolId == 12010 || toolId == 11230)//破甲
			{
				if (GenRandomInt(100) > 45 && GenRandomInt(100) < 54 && !getIsHurtDropItem())
				{
					playAnim(SEQ_SCORPION_NAIL);
					setIsHurtDropItem(true);
					auto dropComponent = GetComponent<DropItemComponent>();
					if (dropComponent)
					{
						dropComponent->dropItem(11632, 1);
					}
					if (getBody())
					{
						char texname[256];
						sprintf(texname, "%s/male1.png", "entity/100076");
						getBody()->changeBodyTex(texname, "rtexbody");
					}
				}
			}
		}

		if (dynamic_cast<ClientPlayer*>(attacker))
		{
			std::vector<WORLD_ID> allVillagers = GetWorldManagerPtr()->getWorldInfoManager()->getAllVillagers(attacker->getObjId());
			static bool b = true;
			for (size_t i = 0; i < allVillagers.size(); i++)
			{
				auto actor = attacker->getActorMgr()->findActorByWID(allVillagers[i]);
				if (!actor)
					continue;
				actor->Event2().Emit<ClientActor*, ClientActor*>("bePlayerReqHelpAttackMobOnTrigger", this, attacker);
			}
		}

		// 受击停止韧性恢复
		m_noAttackedTicks = 0;
		m_recoverToughnessTicks = -1;

		return true;
	}
	return false;
}

ATTACK_TARGET_TYPE ClientMob::getAttackTargetType()
{
	return (ATTACK_TARGET_TYPE)m_Def->Nature;
}

bool ClientMob::canSpawnHere(World* pworld, const WCoord& pos)
{
	WCoord blockpos = CoordDivBlock(pos);

	if (m_Def->Type == MOB_PASSIVE)
	{
		if (pworld->getBlockID(DownCoord(blockpos)) != BLOCK_GRASS) return false;
		if (pworld->getFullBlockLightValue(blockpos) < 9) return false;

		if (getBlockPathWeight(pworld, blockpos) < 0) return false;
	}
	else if (m_Def->Type == MOB_HOSTILE)
	{
		//if(pworld->getDifficultySetting() == 0) return false;
		//if(pworld->getBlockSunIllum(blockpos.x, blockpos.y, blockpos.z) > GenRandomInt(32)) return false;

		int lt;
		if (pworld->isThundering(blockpos))
		{
			int tmp = pworld->m_Environ->getSunLightSubtract();
			pworld->m_Environ->setSunLightSubtract(10);
			lt = pworld->getBlockLightValue(blockpos);
			pworld->m_Environ->setSunLightSubtract(tmp);
		}
		else lt = pworld->getBlockLightValue(blockpos);

		if (lt > GenRandomInt(8)) return false;
		if (getBlockPathWeight(pworld, blockpos) < 0) return false;
	}

	CollideAABB box;
	box.dim = WCoord(getLocoMotion()->m_BoundSize, getLocoMotion()->m_BoundHeight, getLocoMotion()->m_BoundSize);
	box.pos = pos - WCoord(box.dim.x / 2, getLocoMotion()->m_yOffset, box.dim.z / 2);
	if (m_Def->Type == MOB_WATER)
	{
		if (pworld->isAnyLiquid(box.minPos(), box.maxPos()))
		{
			return true;
		}
	}
	else
	{
		if (pworld->isAnyLiquid(box.minPos(), box.maxPos()))
		{
			return false;
		}
	}

	return pworld->checkNoActorCollision(box, NULL);
}

bool ClientMob::IsPotionApplicable(int buffid)
{
	bool ret = true;

	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		char szScriptFun[256];
		snprintf(szScriptFun, sizeof(szScriptFun), "F%d_IsPotionApplicable", getDef()->ID);

		MINIW::ScriptVM::game()->callFunction(szScriptFun, "i>b", buffid, &ret);
	}

	return ret;
}

void ClientMob::onCollideWithPlayer(ClientActor* player)
{
	ClientActor::onCollideWithPlayer(player);
	if (m_AITask && player && player->IsKindOf<ClientPlayer>())
	{
		m_AITask->OnCollideWithPlayer(player->ToCast<ClientPlayer>());
	}
}

bool ClientMob::canNavigation()
{
	auto* loco = getLocoMotion();
	if (loco && loco->getJumping())
		return false;

	return true;
}

bool ClientMob::isDead()
{
	if (getAttrib())
		return getAttrib()->isDead();

	return false;
}

void ClientMob::setBodyCull()
{
	if (!m_bSectionIsDisplay)
	{
		ClientActor::setBodyCull();
		float distance = SetMobVisibleDistance();
		if (getBody() && getBody()->getEntity())
		{
			getBody()->getEntity()->SetVisibleDistance(distance, Rainbow::CullPolicy::kCullPolicyDistance);
		}
	}
	else
	{
		ClientActor::setBodyCull();

		/*float distance = SetMobVisibleDistance();
		if (getBody() && getBody()->getEntity())
		{
			getBody()->getEntity()->SetVisibleDistance(distance, Rainbow::CullPolicy::kCullPolicyDistance);
		}*/
		if (getEntity())
			getEntity()->SetVisibleDistance(1.0f, Rainbow::CullPolicy::kCullPolicyNone);
	}

}
float ClientMob::SetMobVisibleDistance()
{
	int viewRange = Rainbow::GetMiniCraftRenderer().GetClientSetting().m_ViewSectionRange;
	int range = viewRange * SECTION_BLOCK_DIM;
	return range * BLOCK_SIZE / 2;
}
void ClientMob::setHome(int iHomeDist, int x, int y, int z)
{
	m_HomeDist = iHomeDist;
	getLocoMotion()->setHomePosition(WCoord(x, y, z));
}

bool ClientMob::isInHomeDist(int x, int y, int z)
{
	if (m_HomeDist == -1)  return true;
	WCoord pos(x, y, z);
	WCoord vec = getLocoMotion()->getHomePosition() - pos;

	if (vec.length() > m_HomeDist) return false;
	else return true;
}

bool ClientMob::followOwnerAttack(ClientActor* pTarget, ClientActor* pOwner)
{
	if (NULL == pTarget || NULL == pOwner)  return false;

	ClientMob* mob = dynamic_cast<ClientMob*>(pTarget);
	if (mob)
	{
		int monid = mob->getDef()->ID;
		if (monid == 3109)
		{
			return false;
		}

		if (mob->getTamed() && mob->getTamedOwner() == pOwner)
		{
			return false;
		}
	}

	return true;
}

bool ClientMob::tryMoveToXYZ(int x, int y, int z, float speed)
{
	return getNavigator()->tryMoveTo(x, y, z, speed);
}

bool ClientMob::mountActor_mob(ClientActor* actor)
{
	bool syncpos = false;
	ClientActor* another = NULL;
	ClientActor* ridingactor = NULL;

	auto RidComp = sureRiddenComponent();
	if (RidComp)
	{
		if (RidComp && RidComp->isRiding() && actor == NULL)
		{
			ridingactor = RidComp->getRidingActor();
			if (ridingactor)
			{
				auto RidingComp = ridingactor->getRiddenComponent();
				if (!m_pWorld->isRemoteMode())
				{
					if (!ridingactor->isDead() && !ridingactor->needClear() && RidingComp && RidingComp->getNumRiddenPos() > 1 && RidingComp->getRiddenByActor(0) == this)
					{
						another = RidingComp->getRiddenByActor(1);
					}
					calUnmountPos(ridingactor);
				}
				if (RidingComp)
				{
					RidingComp->clearRiddenActor(this);
				}
			}

			setRidingActor(NULL);
		}
		else
		{
			if (!RidComp->mountActor_Base(actor))
			{
				return false;
			}
		}
	}

	if (!m_pWorld->isRemoteMode())
	{
		PB_ActorMountActorHC actorMountActorHC;
		actorMountActorHC.set_actoridowner(getObjId());
		actorMountActorHC.set_actorid(actor == NULL ? 0 : actor->getObjId());

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_MOUNTACTOR_HC, actorMountActorHC, this, true);
	}

	return true;
}
bool ClientMob::IsTickForever()
{
	if (m_SpaceActorHandle)
	{
		return m_SpaceActorHandle->IsVaild();
	}
	return false;
}
bool ClientMob::SetTickForever(bool forever)
{

	// 其实这里不会在云服上跑。。
#ifndef IWORLD_SERVER_BUILD
	if(!getWorld()|| getWorld()->isRemoteMode())
		return false;

	if (m_SpaceActorHandle)
	{
		if (!forever)
		{
			m_SpaceActorHandle->SetIsVaild(forever);
			m_SpaceActorHandle = nullptr;
			return true;
		}
	}
	else
	{
		if (forever && getActorMgr() && getActorMgr()->GetSpaceManager())
		{
			m_SpaceActorHandle = getActorMgr()->GetSpaceManager()->SetForeverTick(this);
		}
		return true;
	}
#endif
	return false;
}

void ClientMob::calUnmountPos(ClientActor* actor)
{
	CollideAABB box;
	getCollideBox(box);

	WCoord actorpos = actor->getPosition();
	box = box.getOffsetBox(actorpos - getPosition());

	WCoord retpos = actorpos + WCoord(0, actor->getLocoMotion()->m_BoundHeight + 1, 0);

	for (int dx = -150; dx < 200; dx += 100)
	{
		for (int dz = -150; dz < 200; dz += 100)
		{
			if (dx == 0 && dz == 0) continue;

			CollideAABB tmpbox = box.getOffsetBox(dx, BLOCK_SIZE, dz);
			if (m_pWorld->checkNoGroundCollision(tmpbox))
			{
				WCoord pos = actorpos + WCoord(dx, 0, dz);
				WCoord blockpos = CoordDivBlock(pos);

				if (m_pWorld->doesBlockHaveSolidTopSurface(blockpos))
				{
					getLocoMotion()->gotoPosition(pos + WCoord(0, BLOCK_SIZE, 0));
					return;
				}

				WCoord downpos = DownCoord(blockpos);
				if (m_pWorld->doesBlockHaveSolidTopSurface(downpos) || IsWaterBlockID(m_pWorld->getBlockID(downpos)))
				{
					retpos = pos + WCoord(0, BLOCK_SIZE, 0);
				}
			}
		}
	}

	getLocoMotion()->gotoPosition(retpos);
}

bool ClientMob::interactBlock(const WCoord& blockpos)
{
	if (getWorld()->isRemoteMode())
	{
		return true;
	}
	getLivingAttrib()->removeBuff(INVULNERABLE_BUFF);

	int blockid = m_pWorld->getBlockID(blockpos);
	//notifyInteractBlock2Tracking(blockpos, targetface);
	playAnim(SEQ_ATTACK);

	BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
	DirectionType targetface = DIR_NEG_X;
	const Rainbow::Vector3f colpoint;
	if (pmtl && pmtl->onTrigger(m_pWorld, blockpos, targetface, NULL, Rainbow::Vector3f(0, 0, 0)))
	{
		// 观察者事件接口
		ObserverEvent_ActorBlock obevent((long long)getObjId(), pmtl->getBlockResID(), blockpos.x, blockpos.y, blockpos.z);
		GetObserverEventManager().OnTriggerEvent("Block.Trigger", &obevent);

		playAnim(SEQ_ATTACK);
		return true;
	}
	return true;
}

void ClientMob::destroyBlock(const WCoord& pos, bool destroy_effect, int type, bool dropItem/* = true*/)
{
	if (getWorld()->isRemoteMode())
	{
		return;
	}

	int blockid = m_pWorld->getBlockID(pos);
	if (!GetDefManagerProxy()->checkItemCrc(blockid)) return;
	int blockdata = m_pWorld->getBlockData(pos);

	BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	if (destroy_effect)
	{
		m_pWorld->getEffectMgr()->playBlockDestroyEffect(0, pos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2), DIR_NEG_X, 40);
	}

	bool needdropitem = true;
	m_pWorld->mobDestroyBlock(pos, type, getObjId(), dropItem);

	if (destroy_effect)
	{
		WCoord centerpos = BlockCenterCoord(pos);
		if (!def->DigSound.empty()) m_pWorld->getEffectMgr()->playSound(centerpos, def->DigSound.c_str(), GSOUND_DESTROY);
	}
}

PackContainer* ClientMob::getBags()
{
	auto gridComponent = GetComponent<GridContainer>();
	if (gridComponent)
	{
		return nullptr;
	}
	else
	{
		MobAttrib* attrib = static_cast<MobAttrib*>(getAttrib());
		return attrib->getBags();
	}

}

int ClientMob::addBagsItem(int itemid, int num)
{

	auto gridComponent = GetComponent<GridContainer>();
	if (gridComponent)
	{
		return gridComponent->addItemByCount(itemid, num);
	}
	else
	{
		MobAttrib* attrib = static_cast<MobAttrib*>(getAttrib());
		if (attrib->getBags())
		{
			return attrib->getBags()->addItemByCount(itemid, num);
		}
	}


	return 0;
}

int ClientMob::getCurToolID()
{
	return getEquipItem(EQUIP_WEAPON);
}

void ClientMob::addCurToolDuration(int num)
{
	auto living = dynamic_cast<ActorLiving*>(this);
	auto livingAttr = living != nullptr ? living->getLivingAttrib() : nullptr;
	if (!livingAttr)
		return;

	BackPackGrid* pgrid = livingAttr->getEquipGrid(EQUIP_WEAPON);
	if (pgrid->def)
	{
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(pgrid->def->ID);
		if (tooldef && tooldef->Duration > 0)
		{
			getMobAttrib()->onCurToolUsed(num);
		}
	}
}

int ClientMob::removeItemInNormalPack(int itemid, int num)
{

	auto gridComponent = GetComponent<GridContainer>();
	if (gridComponent)
	{
		int itemCount = gridComponent->getItemCount(itemid);
		if (itemCount <= 0)
			return 0;

		if (itemCount < num)
			num = itemCount;
		gridComponent->removeItemByCount(itemid, num);
		return num;
	}
	else
	{
		auto bags = getMobAttrib()->getBags();
		if (bags)
		{
			int itemCount = bags->getItemCount(itemid);
			if (itemCount <= 0)
				return 0;

			if (itemCount < num)
				num = itemCount;
			bags->removeItemByCount(itemid, num);

			return num;
		}
	}
	return 0;
}

void ClientMob::shortcutItemUsed(bool ignoreDurable /* = false */)
{
	getMobAttrib()->onCurToolUsed();
}

bool ClientMob::placeBlock(int blockid, int x, int y, int z, int face, float facept_x, float facept_y, float facept_z)
{
	World* pworld = getWorld();
	WCoord placepos(x, y, z);

	bool canplace = false;
	if (!pworld->canPlaceActorOnSide(blockid, placepos, false, face, NULL))
	{
		if (pworld->canPlaceActorOnSide(blockid, placepos, false, face, this) && getLocoMotion()->m_OnGround)
		{
			CollideAABB box, box2;
			getCollideBox(box);
			box = box.getOffsetBox(0, BLOCK_SIZE, 0);
			box2.setPosDim(placepos * BLOCK_SIZE, WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));

			if (!box.intersect(box2) && pworld->checkNoCollisionBoundBox(box, this))
			{
				LivingLocoMotion* locomotion = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
				if (locomotion != NULL) {
					locomotion->doJump();
					setMotionChange(getLocoMotion()->m_Motion);
					canplace = true;
				}
			}
		}
	}
	else canplace = true;

	if (canplace)
	{
		BlockMaterial* newmtl = g_BlockMtlMgr.getMaterial(blockid);
		int blockdata = -1;
		if (newmtl != NULL) {
			blockdata = newmtl->getPlaceBlockData(pworld, placepos, (DirectionType)face, facept_x, facept_y, facept_z, 0);
		}
		if (blockdata < 0) return false;
		/*if (TaskSubSystem::GetTaskSubSystem())
		{
			TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_BREED_MOB, getPosition(), getDef()->ID);
		}*/
		WCoord pos = getPosition();
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("type", TASKSYS_BREED_MOB).
				SetData_Userdata("WCoord", "trackPos", &pos).
				SetData_Number("target1", getDef()->ID).
				SetData_Number("target2", 0).
				SetData_Number("goalnum", 1);
			MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
		}
		pworld->setBlockAll(placepos, blockid, blockdata, 3);
		getMobAttrib()->onCurToolUsed();
		//被放下的功能先注释
		/*if (pworld->getBlockID(placepos) == blockid)
		{
			newmtl->onBlockPlacedBy(pworld, placepos, this);
		}*/

		const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
		//由Block def中的高度，设置方块的data，第三个bit代表 block的上面
		for (int i = 1; i < def->Height; i++)
		{
			pworld->setBlockAll(placepos + WCoord(0, i, 0), blockid, blockdata | 8, 3);
		}

		playBlockPlaceSound(blockid, placepos.x, placepos.y, placepos.z);
		return true;
	}

	return false;
}

void GridDataToJson(BackPackGrid* pGrid, jsonxx::Object& jsonObj)
{
	if (pGrid == NULL) return;

	jsonObj << "Item_ID" << pGrid->getItemID();
	jsonObj << "Item_Num" << pGrid->getNum();
	jsonObj << "Item_Index" << pGrid->getIndex();
	jsonObj << "Item_Durable" << pGrid->getDuration();

	if (pGrid->getNumEnchant() > 0) //附魔数据
	{
		jsonxx::Array enchant;
		int numEnch = pGrid->getNumEnchant();

		for (int chIdx = 0; chIdx < numEnch; chIdx++)
		{
			enchant << pGrid->getIthEnchant(chIdx);
		}
		jsonObj << "Item_Enchant" << enchant;
	}
}

//code-by:lizb 生物转成生物蛋后需存储数据
void ClientMob::dropEgg(int itemid, int num)
{
	if (itemid == 0 || getLocoMotion() == NULL || m_pWorld == NULL)
	{
		return;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return ;
	WCoord pos = getLocoMotion()->getPosition();
	pos.y += getLocoMotion()->m_BoundHeight / 2;

	int range = BLOCK_SIZE * 3 / 2;
	pos.x += GenRandomInt(range) - GenRandomInt(range);
	pos.z += GenRandomInt(range) - GenRandomInt(range);


	BackPackGrid grid;
	SetBackPackGrid(grid, itemid, num);

	jsonxx::Object jsonObj;
	//特殊类型的生物需要保存信息
	if (getTamedOwnerID() > 0)
	{
		do
		{
			// 1.归属的主人信息
			jsonObj << "TamedOwner" << getTamedOwnerID();

			// 2.基本信息: 名称、血量、饥饿度
			jsonObj << "Base_Name" << m_displayName;
			MobAttrib* pAttrib = this->getMobAttrib();
			if (pAttrib == NULL) break;
			jsonObj << "Base_HP" << pAttrib->getHP();
			jsonObj << "Base_Food" << pAttrib->getFood();

			// 3.装备信息
			jsonxx::Array mobEquips;
			for (int index = 0; index < MAX_EQUIP_SLOTS; index++)
			{
				BackPackGrid* pGrid = pAttrib->getEquipGrid((EQUIP_SLOT_TYPE)index);
				if (pGrid->isEmpty()) continue;

				jsonxx::Object equipItem;
				equipItem << "Slot_Type" << index;
				GridDataToJson(pGrid, equipItem);

				mobEquips << equipItem;
			}
			jsonObj << "Pack_Equips" << mobEquips;

			// 4.背包信息
			PackContainer* backpack = pAttrib->getBags();
			if (backpack != NULL)
			{
				jsonxx::Array mobbpack;
				for (int i = 0; i < (int)backpack->m_Grids.size(); i++)
				{
					BackPackGrid& grid = backpack->m_Grids[i];
					if (grid.isEmpty()) continue;

					jsonxx::Object packItem;
					GridDataToJson(&grid, packItem);

					mobbpack << packItem;
				}
				jsonObj << "Pack_Begs" << mobbpack;
				//都要变成蛋了, 实际生物就把背包清空
				backpack->clear();
			}
			// 5.村民的表情、发型、职业
			ActorVillager* pVillager = dynamic_cast<ActorVillager*>(this);
			if (pVillager != NULL)
			{
				jsonObj << "Subs_FaceId" << pVillager->getFaceId();
				jsonObj << "Subs_HairId" << pVillager->getHairId();
				jsonObj << "Subs_HairColor" << pVillager->getHairColor();
				jsonObj << "Subs_Profession" << (int)pVillager->getProfession();
			}

			// 6.坐骑的MaxHP、Speed、Jump
			ActorHorse* pHorse = dynamic_cast<ActorHorse*>(this);
			if (pHorse != NULL)
			{
				jsonObj << "Subs_H_MaxHP" << pAttrib->getMaxHP();
				jsonObj << "Subs_H_Jump" << pHorse->getMaxJumpHeight();
				jsonObj << "Subs_H_Speed" << pHorse->getRiddenLandSpeed();
			}
		} while (false);
	}
	grid.userdata_str = jsonObj.json();

	actorMgr->spawnItem(pos, grid, num);
}

void ClientMob::applyActorCollision(ClientActor* actor)
{
	//Fire<int, AutoRef<SandboxNode>>(MOB_SCRIPTNOTIFY_COLLIDE, actor);
	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		pClientFlyComponent->applyActorCollision(actor);
		return;
	}

	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		pClientAquaticComponent->applyActorCollision(actor);
		return;
	}

	ClientActor::applyActorCollision(actor);
}