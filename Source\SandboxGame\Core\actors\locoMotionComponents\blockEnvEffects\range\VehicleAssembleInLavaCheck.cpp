#include "VehicleAssembleInLavaCheck.h"

#include "ActorVehicleAssemble.h"
#include "SandboxIdDef.h"//BLOCK_STILL_LAVA

using namespace MNSandbox;


VehicleAssembleInLavaCheck::VehicleAssembleInLavaCheck(int min_xz_limit, int min_y_limit)
:BoundCoordCheck(min_xz_limit,min_y_limit,2,BLOCK_STILL_LAVA, BLOCK_FLOW_LAVA)
{

}

bool VehicleAssembleInLavaCheck::isInRange(ClientActor* pActor, BlockEnvEffectBase* pEffect)
{
	if (!pActor)
	{
		return false;
	}
	bool ret = BoundCoordCheck::isInRange(pActor, pEffect);
	ClientActor* owner = pActor;// context.GetData_Usertype<ClientActor>("owner");
	if(owner){
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(owner);
		if (vehicle)
			ret = vehicle->inInLava();
	}
	return ret;
}