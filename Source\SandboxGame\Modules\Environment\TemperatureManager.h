﻿#ifndef __TEMPERATURE_MANAGER_H__
#define __TEMPERATURE_MANAGER_H__
/*******************
*	温度管理器
*	author:: zhangyusong
*******************/

#include "OgreWCoord.h"
#include "OgreBlock.h"
#include "SandboxGame.h"
#include "TemperatureManagerInterface.h"
class World;
class Section;
class ClientMob;

class EXPORT_SANDBOXGAME TemperatureManager : public TemperatureManagerInterface
{

public:

	// 移动温度源
	struct MobileSourceTemperatureData
	{
		float _val;
		int _tick;
		long long _objid;
		MobileSourceTemperatureData() : _val(0.f), _tick(-1), _objid(0) {}
		MobileSourceTemperatureData(float val, int tick, long long objid) : _val(val), _tick(tick), _objid(objid) {}
	};

public:
	TemperatureManager();
	virtual ~TemperatureManager();
	// TemperatureManagerInterface
	virtual void OnTick() override;

	// 获取环境温度 = 地形 + 高度 + 天气
	float GetEnviromentTemperature(World* world, const WCoord& blockpos);

	// 获取地形温度
	float GetBiomeTemperature(World* world, const WCoord& blockpos);

	// 获取高度温度
	float GetHeightTemperature(World* world, const WCoord& blockpos);

	// 获取天气温度
	float GetWeatherTemperature(World* world, const WCoord& blockpos);

	// 获取位置温度 = 环境温度 + 温度源
	float GetPositionTemperature(World* world, const WCoord& blockpos);

	// 获取方块温度源
	float GetSourceTemperature(World* world, const WCoord& blockpos);

	// 获取移动温源温度
	float GetMoblieSourceTemperature(World* world, const WCoord& blockpos);

	// 获取温度在什么区间
	int GetTemperatureLevel(float temp);
	// TemperatureManagerInterface
	// 获取方块温度和区间
	virtual void GetBlockTemperatureAndLevel(World* world, const WCoord& blockpos, float& temp, int& level) override;

	// 获取温度区间和在区间最大最小值
	void GetTemperatureLevelAndVal(float temp, int& level, float& minVal, float& maxVal);

	//
	// 获取温度系统活跃状态
	virtual bool GetTemperatureActive() override { return m_Active; }

	// 添加区域温度源(该range范围内固定变化 val 温度值)
	int AddAreaTemperatureSource(World* world, const WCoord& blockpos, float val, int tick = -1);

	// 移除区域温度源
	bool RemoveAreaTemperatureSource(int index);

	// 温度是否在适宜区间
	bool IsTemperatureSuitable(float temp);

	// 设置温度系统活跃与否
	void SetTemperatureActive(bool val) { m_Active = val; }
	
	// 清理位置温度缓存
	void ClearPosTemperatureCache(World* world, const WCoord& blockpos, int range);

	// 设置温度源(特殊用法，一般添加使用 AddAreaSourceTemperature 接口）
	void SetAreaTemperatureSource(int index, Section* psection);

	// 添加移动温度源（跟随对象移动）
	int AddMobileTemperatureSource(World* world, long long objid, float val, int tick = -1);

	// 移除移动温度源
	bool RemoveMobileTemperatureSource(int index);
private:

	void PlayerTick();
	void MobTick();
	void SourceTick();

	// section温度源
	void GetSectionSourceTemperature(Section* psection, const WCoord& blockpos, float& heat, float& ice, float& maxHeat, float& maxIce, int range = 0);

	// section 里的区域温度源
	void GetSectionAreaSourceTemperature(Section* psection, const WCoord& blockpos, float& heat, float& ice, float& maxHeat, float& maxIce, int range = 0);

	// 热源计算
	void CalHeatSource(World* world, const WCoord& blockpos, const WCoord& sourcepos, float val, float& heat, float& maxHeat);

	// 冷源计算
	void CalIceSource(World* world, const WCoord& blockpos, const WCoord& sourcepos, float val, float& ice, float& maxIce);

	// 移动温度源
	void GetMoblieSourceTemperature(World* world, const WCoord& blockpos, float& heat, float& ice, float& maxHeat, float& maxIce);

public:
	bool m_ShowData = false;	// GM展示数据

private:
	bool m_Active = true;					// 功能是否开启
	unsigned int m_Tick = 0;

	int m_ConstMilaTempAtten = 0;
	int m_ConstMilaTempAdd = 0;
	int m_ConstMengyanTempAtten = 0;
	int m_ConstMengyanTempAdd = 0;
	int m_ConstPingtanTempAtten = 0;
	int m_ConstPingtanTempAdd = 0;
	float m_ConstPlantTempChangeVal = 1.0f;
	float m_ConstTempBurn = 0.f;
	float m_ConstTempTopHeat = 0.f;
	float m_ConstTempHeat = 0.f;
	float m_ConstTempIce = 0.f;
	float m_ConstTempTopIce = 0.f;
	float m_ConstTempFreeze = 0.f;
	float m_ConstWeatherRain = 0.f;
	float m_ConstWeatherSnow = 0.f;
	float m_ConstWeatherTempest = 0.f;
	float m_ConstWeatherTempestUp = 0.f;
	float m_ConstWeatherBlizzard = 0.f;
	float m_ConstWeatherBlizzardUp = 0.f;
	float m_ConstWeatherThunder = 0.f;

	std::vector<long long> m_MobList;
	float m_MobListSizeRate = 0.f;
	std::vector<long long> m_PlayerList;
	float m_PlayerListSizeRate = 0.f;

	int m_AreaSourceInfoMapIdx = 0;
	std::unordered_map<char, std::unordered_map<int, WCoord>> m_AreaSourceInfoMap;
	int m_MobileSourceInfoMapIdx = 0;
	std::unordered_map<char, std::unordered_map<int, MobileSourceTemperatureData>> m_MobileSourceInfoMap;				// 不会存档
};




#include "PluginManager.h"
#include "Plugin.h"
#include "IPluginBase.h"
#include "EventHandleEx.h"
#include "event/SandboxCallback.h"

class TemperatureNetSys : public IPluginBase, public IEventExcuteEx
{
public:
	TemperatureNetSys(PluginManager* p);
	virtual ~TemperatureNetSys();

	virtual bool Awake();
	virtual bool Init();
	virtual bool Execute(float dtime);
	virtual bool Shut();
	bool CreateModuleEvent();
	void OnExecute(const char* eventname, void* context, int nLen, unsigned long long userdata);
	void OnExecute(unsigned short wEventID, unsigned char bSrcType, unsigned long dwSrcID, char* pszContext, int nLen) {}

private:
	std::map<std::string, MNSandbox::Callback>  m_eventCallbacks;
};

#endif