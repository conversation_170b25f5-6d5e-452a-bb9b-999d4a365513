#include "SummonMonsterSiegeMgr.h"
#include "SandboxCoreDriver.h"
#include "OgreScriptLuaVM.h"
#include "SkyPlaneDefine.h"
#include "EffectParticle.h"
#include "PlayerControl.h"
#include "EffectManager.h"
#include "ClientPlayer.h"
#include "SandboxIdDef.h"
#include "ActorAttrib.h"
#include "OgreEntity.h"
#include "ClientMob.h"
#include "ClientActor.h"
#include "ActorBody.h"
#include "world.h"
#include "Serialize/TransferUtilities.h"
#include "File/FileManager.h"
#include "WorldManager.h"

using namespace Rainbow;
using namespace MNSandbox;
using namespace MINIW;
using namespace std;

const char* SUMMON_STATE_SAVE_PATH = "data/w%lld/monsterSiege.json";

const int SummonMonsterSiegeMgr::ELITE_FANTOM_ID1 = 3917;
const int SummonMonsterSiegeMgr::ELITE_FANTOM_ID2 = 3918;
const int SummonMonsterSiegeMgr::ELITE_FANTOM_ID3 = 3919;
const int SummonMonsterSiegeMgr::SUMMON_MONSTER_TICK_GAP = 5;
//const int SummonMonsterSiegeMgr::SUMMON_MONSTER_TEAM_TICK_GAP = 1500;
const int SummonMonsterSiegeMgr::SUMMON_FANTOM_MIN_DISTANCE = 38;
const int SummonMonsterSiegeMgr::SUMMON_FANTOM_MAX_DISTANCE = 64;
const int SummonMonsterSiegeMgr::SUMMON_NOAMAL_MONSTER_DISTANCE = 16;
const float SummonMonsterSiegeMgr::RETAIN_FANTOM_HP_PERCENT = 0.34F;
const int SummonMonsterSiegeMgr::SUMMON_MONSTER_TEAM_COUNT = 4;
const int SummonMonsterSiegeMgr::FANTOM_SUMMON_ANIM = 100989;
const int SummonMonsterSiegeMgr::FANTOM_COME_ON_STAGE_ANIM = 100990;

SummonMonsterSiegeMgr::SummonMonsterSiegeMgr(World *curWorld) : 
	m_SummonState(SummonState::Summon_Wait), m_TickCount(0),
	m_SummonTeamCount(0), m_EquipBoarmanCount(0), m_TeamTick(0),
	m_DifficultyLevel(SiegeDifficultyLevel::DifficultyLevel_UnDefine)
{
	m_CurWorld = curWorld;
	m_PlayerUinArr.clear();
	m_SiegeDifficultInfo.clear();
	m_WaitSummonTeams.clear();
	m_FantomsInfos.clear();

	LoadSiegeDifficultInfo();
}

SummonMonsterSiegeMgr::~SummonMonsterSiegeMgr()
{
	this->SaveCurSummonData();

	m_CurWorld = NULL;
	m_SiegeDifficultInfo.clear();
}

void SummonMonsterSiegeMgr::RevivalStatueBroken()
{
	//�߻�˵ʯ��������֮������ٻ������ı䵱ǰ�߼�
	return;

	if (m_FantomsInfos.size() == 0)
		return;

	if (!m_CurWorld)
		return;

	ActorManager* pActorManager = dynamic_cast<ActorManager*>(m_CurWorld->getActorMgr());
	if (!pActorManager)
		return;

	for (auto iter = m_FantomsInfos.begin(); iter != m_FantomsInfos.end(); iter++)
	{
		ClientActor* pClientActor = FindSummonFantom(iter->first);
		if (!pClientActor)
			continue;
		pActorManager->despawnActor(pClientActor);
	}
	StopSummonMonster();
}

bool SummonMonsterSiegeMgr::LoadPreSummonData()
{
	char path[256];
	snprintf(path, sizeof(path) - 1, SUMMON_STATE_SAVE_PATH, m_CurWorld->getOWID());

	core::string pathFull;
	GetFileManager().ToWritePathFull(path, pathFull);
	if (!GetFileManager().IsFileExistWritePath(pathFull.c_str()))
		return false;
	
	AutoRefPtr<DataStream> databuf = GetFileManager().OpenFileWritePath(path);
	if (!databuf) return false;

	std::string readBuf = std::string((char*)databuf->GetMemoryImage(), databuf->Size());
	jsonxx::Object nodesObj;
	if (!nodesObj.parse(readBuf))
		return false;
	
	int curDay = g_WorldMgr->getWorldTimeDay();
	if (!nodesObj.has<jsonxx::Number>("day"))
	{
		GetFileManager().DeleteWritePathFileOrDir(pathFull.c_str());
		return false;
	}
	
	int preDay = nodesObj.get<jsonxx::Number>("day");
	//������������Բ���
	if (preDay != curDay)
	{
		GetFileManager().DeleteWritePathFileOrDir(pathFull.c_str());
		return false;
	}

	if (nodesObj.has<jsonxx::Number>("tickcount"))
	{
		m_TickCount = nodesObj.get<jsonxx::Number>("tickcount");
	}
	if (nodesObj.has<jsonxx::Number>("equipboardmancount"))
	{
		m_EquipBoarmanCount = nodesObj.get<jsonxx::Number>("equipboardmancount");
	}
	if (nodesObj.has<jsonxx::Number>("state"))
	{
		m_SummonState = (SummonState)nodesObj.get<jsonxx::Number>("state");
	}
	if (nodesObj.has<jsonxx::Number>("teamcount"))
	{
		m_SummonTeamCount = nodesObj.get<jsonxx::Number>("teamcount");
	}
	if (nodesObj.has<jsonxx::Number>("fantomcount"))
	{
		m_SummonFantomCount = nodesObj.get<jsonxx::Number>("fantomcount");
	}
	if (nodesObj.has<jsonxx::Number>("level"))
	{
		m_DifficultyLevel = (SiegeDifficultyLevel)nodesObj.get<jsonxx::Number>("level");
	}
	if (nodesObj.has<jsonxx::Array>("playeruin"))
	{
		m_PlayerUinArr.clear();
		jsonxx::Array playerUins = nodesObj.get<jsonxx::Array>("playeruin");
		for (int index = 0; index < playerUins.size(); ++index)
		{
			jsonxx::Object playerObj = playerUins.get<jsonxx::Object>(index);
			if (playerObj.has<jsonxx::Number>("objid"))
			{
				m_PlayerUinArr.push_back(playerObj.get<jsonxx::Number>("objid"));
			}
		}
	}
	if (nodesObj.has<jsonxx::Array>("fantominfo"))
	{
		m_FantomsInfos.clear();
		jsonxx::Array fantoms = nodesObj.get<jsonxx::Array>("fantominfo");
		for (int index = 0; index < fantoms.size(); ++index)
		{
			jsonxx::Object fantomObj = fantoms.get<jsonxx::Object>(index);
			if (fantomObj.has<jsonxx::Number>("objid") && fantomObj.has<jsonxx::Boolean>("isappear") && fantomObj.has<jsonxx::Number>("skinid"))
			{
				long long fantomId = fantomObj.get<jsonxx::Number>("objid");
				int skinId = fantomObj.get<jsonxx::Number>("skinid");
				int animId = fantomObj.get<jsonxx::Number>("animid");
				int playCount = fantomObj.get<jsonxx::Number>("playcount");
				FantomInfo fantomInfo(skinId, animId, playCount);
				m_FantomsInfos.insert(make_pair(fantomId, fantomInfo));
			}
		}
	}
	if (nodesObj.has<jsonxx::Array>("waitsummoninfo"))
	{
		m_WaitSummonTeams.clear();
		jsonxx::Array waitSummonInfo = nodesObj.get<jsonxx::Array>("waitsummoninfo");
		for (int index = 0; index < waitSummonInfo.size(); ++index)
		{
			jsonxx::Object monsterTeam = waitSummonInfo.get<jsonxx::Object>(index);
			if (monsterTeam.has<jsonxx::Number>("playeruin") && monsterTeam.has<jsonxx::Number>("fantomid") &&
				monsterTeam.has<jsonxx::Array>("monsterids") && monsterTeam.has<jsonxx::Number>("fantomobjid"))
			{
				WaitSummonTeam waitSummonTeam;
				waitSummonTeam.playerUin = monsterTeam.get<jsonxx::Number>("playeruin");
				waitSummonTeam.fantomObjID = monsterTeam.get<jsonxx::Number>("fantomobjid");
				waitSummonTeam.fantomID = monsterTeam.get<jsonxx::Number>("fantomid");
				if (monsterTeam.has<jsonxx::Number>("tick"))
				{
					waitSummonTeam.tick = monsterTeam.get<jsonxx::Number>("tick");
				}
				jsonxx::Array monsterIds = monsterTeam.get<jsonxx::Array>("monsterids");
				for (int idIndex = 0; idIndex < monsterIds.size(); ++idIndex)
				{
					jsonxx::Object monsterObj = monsterIds.get<jsonxx::Object>(idIndex);
					if (monsterObj.has<jsonxx::Number>("monsterid"))
					{
						int monsterId = monsterObj.get<jsonxx::Number>("monsterid");
						waitSummonTeam.waitSummonMonster.push(monsterId);
					}
				}
				m_WaitSummonTeams.push_back(waitSummonTeam);
			}
		}
	}
	for (auto iter = m_FantomsInfos.begin(); iter != m_FantomsInfos.end(); iter++)
	{
		ClientActor* pClientActor = FindSummonFantom(iter->first);
		if (!pClientActor)
			continue;
		ShowFantomSkinById(iter->second.curSkinId, pClientActor);
		ClientMob* pClientMob = dynamic_cast<ClientMob*>(pClientActor);
		if (!pClientMob)
			continue;
		pClientMob->setDieInDay(true, 100107);
	}
	return true;
}

void SummonMonsterSiegeMgr::SaveCurSummonData()
{
	if (m_SummonState == SummonState::Summon_Wait)
		return;

	int curDay = g_WorldMgr->getWorldTimeDay();

	char path[256];
	snprintf(path, sizeof(path) - 1, SUMMON_STATE_SAVE_PATH, m_CurWorld->getOWID());
	core::string pathFull;
	GetFileManager().ToWritePathFull(path, pathFull);
	if (GetFileManager().IsFileExistWritePath(pathFull.c_str()))
	{
		GetFileManager().DeleteWritePathFileOrDir(pathFull.c_str());
	}

	jsonxx::Object textContent;

	textContent << "day" << curDay;
	textContent << "state" << (int)m_SummonState;
	if (m_SummonState != SummonState::Summon_Finish)
	{
		textContent << "tickcount" << m_TickCount;
		textContent << "equipboardmancount" << m_EquipBoarmanCount;
		textContent << "teamcount" << m_SummonTeamCount;
		textContent << "fantomcount" << m_SummonFantomCount;
		textContent << "level" << (int)m_DifficultyLevel;

		jsonxx::Array playerUins;
		for (auto iter = m_PlayerUinArr.begin(); iter != m_PlayerUinArr.end(); iter++)
		{
			jsonxx::Object playerInfo;
			playerInfo << "objid" << *iter;
			playerUins << playerInfo;
		}
		textContent << "playeruin" << playerUins;

		jsonxx::Array fantomids;
		for (auto iter = m_FantomsInfos.begin(); iter != m_FantomsInfos.end(); iter++)
		{
			jsonxx::Object fantomInfo;
			fantomInfo << "objid" << iter->first;
			fantomInfo << "skinid" << iter->second.curSkinId;
			fantomInfo << "animid" << iter->second.waitPlayAnimId;
			fantomInfo << "playcount" << iter->second.waitPlayAnimCount;
			fantomids << fantomInfo;
		}
		textContent << "fantominfo" << fantomids;

		jsonxx::Array waitSummonInfo;
		for (auto iter = m_WaitSummonTeams.begin(); iter != m_WaitSummonTeams.end(); iter++)
		{
			jsonxx::Object teamInfo;
			teamInfo << "playeruin" << iter->playerUin;
			teamInfo << "fantomobjid" << iter->fantomObjID;
			teamInfo << "fantomid" << iter->fantomID;
			teamInfo << "tick" << iter->tick;
			jsonxx::Array monsterids;
			size_t monsterSize = iter->waitSummonMonster.size();
			for (int index = 0; index < monsterSize; index++)
			{
				int monsterId = iter->waitSummonMonster.front();
				jsonxx::Object monsterobj;
				monsterobj << "monsterid" << monsterId;
				monsterids << monsterobj;
				iter->waitSummonMonster.push(monsterId);
				iter->waitSummonMonster.pop();
			}
			teamInfo << "monsterids" << monsterids;
			waitSummonInfo << teamInfo;
		}
		textContent << "waitsummoninfo" << waitSummonInfo;
	}

	GetFileManager().SaveToWritePath(path, textContent.json().c_str(), strlen(textContent.json().c_str()));
}

void SummonMonsterSiegeMgr::ResetSummonMonsterSiege()
{
	m_SummonState = SummonState::Summon_Wait;
}

void SummonMonsterSiegeMgr::LoadSiegeDifficultInfo()
{
	bool loadRet = false;
	MINIW::ScriptVM::game()->callFunction("GetSiegeDifficultInfo", "u[SummonMonsterSiegeMgr]>b", this, &loadRet);
	if (!loadRet)
	{
		m_SiegeDifficultInfo.clear();;
	}
}

void SummonMonsterSiegeMgr::CreateSiegeDifficultInfo(int amount)
{
	for (int i = 0; i < amount; i++)
	{
		SiegeDifficultInfo monsterDifficultInfo;
		m_SiegeDifficultInfo.push_back(monsterDifficultInfo);
	}
}

SiegeDifficultInfo* SummonMonsterSiegeMgr::GetSiegeDifficultInfo(int index)
{
	if (m_SiegeDifficultInfo.size() == 0 || index >= m_SiegeDifficultInfo.size())
		return NULL;
	return &m_SiegeDifficultInfo[index];
}

SiegeDifficultInfo* SummonMonsterSiegeMgr::GetSiegeDifficultInfo()
{
	if (m_SiegeDifficultInfo.size() == 0 || m_DifficultyLevel == SiegeDifficultyLevel::DifficultyLevel_UnDefine)
		return NULL;

	for (SiegeDifficultInfo& difficultInfo : m_SiegeDifficultInfo)
	{
		if (difficultInfo.difficultLevel == m_DifficultyLevel)
		{
			return &difficultInfo;
		}
	}
	return NULL;
}

void SummonMonsterSiegeMgr::OnUpdate(float dtime)
{

}

void SummonMonsterSiegeMgr::OnTick()
{
	if (m_CurWorld == NULL || g_pPlayerCtrl == NULL)
		return;

	if (m_SummonState == SummonState::Summon_Wait || m_SummonState == SummonState::Summon_Finish)
		return;

	PlaySummonAnim();

	if (m_SummonState == SummonState::Summon_Fantom)
	{
		SummonFantom();
	}
	else if (m_SummonState == SummonState::Summon_MonsterTeam)
	{
		SummonMonsterTeam();
	}
	else if (m_SummonState == SummonState::Summon_Monster)
	{
		SummonMonster();
	}

	++m_TickCount;
}

void SummonMonsterSiegeMgr::StopSummonAnim()
{
	if (m_FantomsInfos.size() == 0)
		return;
	//ֹͣ�����ٻ�����
	for (auto iter = m_FantomsInfos.begin(); iter != m_FantomsInfos.end(); iter++)
	{
		ClientActor* pClientActor = FindSummonFantom(iter->first);
		if (!pClientActor)
			continue;
		ActorBody* pAcotrBody = pClientActor->getBody();
		if (!pAcotrBody)
			continue;
		if (!pClientActor->IsLoadModelFinished())
			continue;
		if (!pAcotrBody->hasAnimSeq(FANTOM_SUMMON_ANIM))
			continue;
		pAcotrBody->stopAnimBySeqId(FANTOM_SUMMON_ANIM);
	}
	m_FantomsInfos.clear();
}

void SummonMonsterSiegeMgr::PlaySummonAnim()
{
	if (m_FantomsInfos.size() == 0)
		return;

	if (!m_CurWorld)
		return;
	bool isDay = m_CurWorld->isDaytime();
	//�����˾Ͳ�����
	if (isDay)
		return;

	for (auto iter = m_FantomsInfos.begin(); iter != m_FantomsInfos.end(); iter++)
	{
		ClientActor* pClientActor = FindSummonFantom(iter->first);
		if (!pClientActor)
			continue;
		ActorBody* pAcotrBody = pClientActor->getBody();
		if (!pAcotrBody)
			continue;
		if (!pClientActor->IsLoadModelFinished())
			continue;
		if (!pAcotrBody->hasAnimSeq(iter->second.waitPlayAnimId))
			continue;
		if (pAcotrBody->getNowPlaySeqID() != iter->second.waitPlayAnimId && iter->second.waitPlayAnimCount > 0)
		{
			pAcotrBody->playAnimBySeqId(iter->second.waitPlayAnimId, Rainbow::AnimPlayMode::ANIM_MODE_ONCE_STOP);
		}
		bool isPlayEnd = pAcotrBody->hasAnimPlayEnd(iter->second.waitPlayAnimId);
		if (isPlayEnd && iter->second.waitPlayAnimCount > 0)
		{
			iter->second.waitPlayAnimCount -= 1;
		}
	}
	
	//m_DelayPlayAnimFantoms.erase(iter);
}

bool SummonMonsterSiegeMgr::StartSummonMonster(SiegeDifficultyLevel level, vector<int> playerUins)
{
	if (level == SiegeDifficultyLevel::DifficultyLevel_UnDefine || playerUins.size() == 0)
		return false;

	bool isContinueSummon = LoadPreSummonData();

	if (!isContinueSummon)
	{
		m_DifficultyLevel = level;
		m_PlayerUinArr = playerUins;
		m_TickCount = 0;
		m_SummonFantomCount = 0;
		m_SummonTeamCount = 0;
		m_EquipBoarmanCount = 0;
		m_FantomsInfos.clear();
		m_SummonState = SummonState::Summon_Fantom;
		m_WaitSummonTeams.clear();
		for(int index = 0; index < SUMMON_MONSTER_TEAM_COUNT; ++index)
		{
			for (int playerIndex = 0; playerIndex < playerUins.size(); ++playerIndex)
			{
				WaitSummonTeam waitSummonTeam;
				waitSummonTeam.playerUin = playerUins[playerIndex];
				m_WaitSummonTeams.push_back(waitSummonTeam);
			}
		}
	}

	MINIW::ScriptVM::game()->callFunction("UpdateSummonMonsterState", "b", true);

	return true;
}

void SummonMonsterSiegeMgr::StopSummonMonster()
{
	m_DifficultyLevel = DifficultyLevel_UnDefine;
	m_PlayerUinArr.clear();
	m_TickCount = 0;
	m_SummonFantomCount = 0;
	m_SummonTeamCount = 0;
	m_EquipBoarmanCount = 0;
	m_WaitSummonTeams.clear();
	StopSummonAnim();
	m_FantomsInfos.clear();

	m_SummonState = SummonState::Summon_Finish;

	SaveCurSummonData();

	MINIW::ScriptVM::game()->callFunction("UpdateSummonMonsterState", "b", false);
}

void SummonMonsterSiegeMgr::SummonFantom()
{
	if (m_TickCount % SUMMON_MONSTER_TICK_GAP != 0)
		return;

	if (m_CurWorld == NULL)
		return;
	ActorManager* actorManager = dynamic_cast<ActorManager*>(m_CurWorld->getActorMgr());
	if (actorManager == NULL)
		return;

	//���ʹͽ��������ֱ��ת����һ��״̬
	if (m_SummonFantomCount >= m_PlayerUinArr.size())
	{
		m_SummonState = SummonState::Summon_MonsterTeam;
		return;
	}

	int playerUin = m_PlayerUinArr[m_SummonFantomCount];
	IClientPlayer* clientPlayer = actorManager->iFindPlayerByUin(playerUin);
	if (clientPlayer == NULL)
	{
		DeleteWaitSummonTeamByUin(playerUin);
		return;
	}

	WCoord basePos = clientPlayer->iGetPosition();
	WCoord underfootPos = DownCoord(CoordDivBlock(basePos));
	//˵���ڿ���
	if (m_CurWorld->getBlockID(underfootPos) == BLOCK_AIR)
	{
		IntersectResult result;
		result.intersect_actor = false;
		Vector3f rayDir(0, -1, 0);
		WorldRay worldray;
		worldray.m_Origin = basePos.toWorldPos();
		worldray.m_Range = 64 * BLOCK_FSIZE;
		worldray.m_Dir = Rainbow::Normalize(rayDir);
		bool pickRet = m_CurWorld->pickGround(worldray, &result);
		//˵�����¶������
		if (!pickRet)
		{
			DeleteWaitSummonTeamByUin(playerUin);
			return;
		}
		basePos = result.block * BLOCK_SIZE;
	}
	WCoord summonPoint(0, 0, 0);
	bool findPointRet = FindFitableSummonPoint(basePos, &summonPoint, true);
	//��Ҹ����Ҳ�����ȥʯ�ʵ��񸽽�ȥ��
	if (!findPointRet)
		findPointRet = FindFitableSummonPoint(m_RevivalStatuePos, &summonPoint, true);

	//�Ҳ������ʵĵ�ֱ��ʹ��ʯ�ʵ����λ��
	if (!findPointRet)
		summonPoint = m_RevivalStatuePos;

	vector<VoidMonsterTeamInfo*> voidMonsterTeamInfo;
	for (int index = 0; index < SUMMON_MONSTER_TEAM_COUNT; ++index)
	{
		VoidMonsterTeamInfo* pMonsterTeamInfo = GetTeamInfoByRand();
		//��ȡ������Ϣ
		if (pMonsterTeamInfo == NULL)
			continue;
		voidMonsterTeamInfo.push_back(pMonsterTeamInfo);
	}

	if (voidMonsterTeamInfo.size() != SUMMON_MONSTER_TEAM_COUNT)
		return;

	ClientMob* pMobFantom = actorManager->spawnMonster(summonPoint.x, summonPoint.y, summonPoint.z, voidMonsterTeamInfo[0]->fantomId);
	if (pMobFantom == NULL)
		return;

	pMobFantom->setDieInDay(true, 100107);
	ActorBody* pActorBody = pMobFantom->getBody();
	if (pActorBody == NULL)
		return;

	for (int index = 0; index < SUMMON_MONSTER_TEAM_COUNT; ++index)
	{
		int waitSummonTeamIndex = m_SummonFantomCount + index * m_PlayerUinArr.size();
		WaitSummonTeam& summonTeam = m_WaitSummonTeams[waitSummonTeamIndex];
		for (VoidMonsterInfo monsterInfo : voidMonsterTeamInfo[index]->monsterInfos)
		{
			for (int index = 0; index < monsterInfo.monsterCount; ++index)
			{
				summonTeam.waitSummonMonster.push(monsterInfo.monsterID);
			}
		}
		summonTeam.fantomObjID = pMobFantom->getObjId();
		summonTeam.fantomID = voidMonsterTeamInfo[index]->fantomId;
		summonTeam.tick = voidMonsterTeamInfo[index]->tick;
	}

	auto animIter = m_FantomsInfos.find(pMobFantom->getObjId());
	if (animIter == m_FantomsInfos.cend())
	{
		FantomInfo fantomInfo(ELITE_FANTOM_ID1, FANTOM_COME_ON_STAGE_ANIM, 1);
		m_FantomsInfos.insert(make_pair(pMobFantom->getObjId(), fantomInfo));
	}

	++m_SummonFantomCount;
	//���������ϵ�һ����
	if (m_WaitSummonTeams.size() > 0)
	{
		m_TeamTick = m_WaitSummonTeams[0].tick;
	}
	//��Ӣ���ʹͽ������
	if (m_SummonFantomCount == m_PlayerUinArr.size())
	{
		m_SummonState = SummonState::Summon_MonsterTeam;
		//�׶�ֱ�Ӹ�10��tick��ʼ�ٻ�,��Ϊ���ʹͽ�ļ�����Ҫʱ��
		m_TickCount = m_TeamTick - 10;
	}

	SaveCurSummonData();
}

void SummonMonsterSiegeMgr::SummonMonsterTeam()
{
	if (m_SummonState != SummonState::Summon_MonsterTeam)
		return;

	if (m_CurWorld == NULL)
		return;

	if (m_WaitSummonTeams.size() == 0)
	{
		m_SummonState = SummonState::Summon_Monster;
		return;
	}

	if (m_TickCount == 0 || m_TeamTick == 0 || m_TickCount % m_TeamTick != 0)
		return;

	ClientActor* pLiveMobFantom = NULL;
	int teamCount = m_WaitSummonTeams.size();
	for (int index = 0; index < teamCount; ++index)
	{
		vector<WaitSummonTeam>::iterator needSummonTeam = m_WaitSummonTeams.begin();
		pLiveMobFantom = FindSummonFantom(needSummonTeam->fantomObjID);
		if (pLiveMobFantom == NULL)
		{
			//��Ӣ���ʹͽ������ֱ�Ӵ��б���ɾ��
			m_WaitSummonTeams.erase(needSummonTeam);
			++m_SummonTeamCount;
			continue;
		}
		break;
	}
	//˵����Ӣ���ʹͽ��������
	if (pLiveMobFantom == NULL)
	{
		m_SummonState = SummonState::Summon_Monster;
		return;
	}

	if (m_WaitSummonTeams.cbegin() != m_WaitSummonTeams.cend())
	{
		int fantomSkinId = m_WaitSummonTeams.begin()->fantomID;
		auto curFantom = m_FantomsInfos.find(pLiveMobFantom->getObjId());
		if (curFantom != m_FantomsInfos.end())
		{
			curFantom->second.curSkinId = fantomSkinId;
			curFantom->second.waitPlayAnimId = FANTOM_SUMMON_ANIM;
			curFantom->second.waitPlayAnimCount = 3;
		}
		ShowFantomSkinById(fantomSkinId, pLiveMobFantom);
	}

	m_SummonState = SummonState::Summon_Monster;
	m_TickCount = 0;
	++m_SummonTeamCount;

	SaveCurSummonData();
}

void SummonMonsterSiegeMgr::SummonMonster()
{
	if (m_SummonState != SummonState::Summon_Monster)
		return;

	//�ȴ��ٻ��Ķ���Ϊ����ֱ��ֹͣ
	if (m_WaitSummonTeams.size() == 0)
	{
		StopSummonMonster();
		return;
	}

	vector<WaitSummonTeam>::iterator needSummonTeam = m_WaitSummonTeams.begin();
	if (needSummonTeam->waitSummonMonster.size() == 0)
	{
		//û�й�����ٻ���ֱ��ɾ��
		m_WaitSummonTeams.erase(needSummonTeam);
		return;
	}

	if (m_TickCount == 0 || m_TickCount % SUMMON_MONSTER_TICK_GAP != 0)
		return;

	if (m_CurWorld == NULL)
		return;
	ActorManager* actorManager = dynamic_cast<ActorManager*>(m_CurWorld->getActorMgr());
	if (actorManager == NULL)
		return;

	ClientActor* pFantom = FindSummonFantom(needSummonTeam->fantomObjID);
	//��Ӣ���ʹͽ���������뿪����������
	if (pFantom == NULL)
	{
		//û�й�����ٻ���ֱ��ɾ��
		m_WaitSummonTeams.erase(needSummonTeam);
		return;
	}

	const WCoord& fantomPos = pFantom->getPosition();
	WCoord summonPoint(0, 0, 0);
	bool findPointRet = FindFitableSummonPoint(fantomPos, &summonPoint, false);
	//�����λ��ʧ��ֱ��ʹ�þ�Ӣ���ʹͽ��λ��
	if (!findPointRet)
	{
		summonPoint = fantomPos;
	}

	const WCoord& playerPos = g_pPlayerCtrl->getPosition();
	float yaw = 0.0F, pitch = 0.0F;
	Vector3f dir = playerPos.toVector3() - summonPoint.toVector3();
	Direction2PitchYaw(&yaw, &pitch, dir);
	EffectParticle* pEffect = m_CurWorld->getEffectMgr()->playParticleEffectAsync("prefab/particles/zhaohuan.prefab", WCoord(summonPoint.x, summonPoint.y + 10, summonPoint.z), 40, yaw, 0, true, 64);
	//pEffect->setScale(10);

	ClientMob* pMonster = actorManager->spawnMonster(summonPoint.x, summonPoint.y, summonPoint.z, needSummonTeam->waitSummonMonster.front());
	//��ֻ�����ٻ�ʧ��ֱ�Ӷ���
	if (pMonster == NULL)
	{
		needSummonTeam->waitSummonMonster.pop();
		return;
	}

	pMonster->setDieInDay(true);
	EquipForBoarman(pMonster);
	needSummonTeam->waitSummonMonster.pop();

	//����û���ٻ���ȴ���һ֡�����ٻ�
	if (needSummonTeam->waitSummonMonster.size() != 0)
		return;

	//��ǰ����Ĺ����ٻ����ˣ�ֱ�Ӳ���������¼
	m_WaitSummonTeams.erase(needSummonTeam);

	//��һ������Ѫ�������Ѫ
	if (m_SummonTeamCount > m_PlayerUinArr.size() && pFantom)
	{
		ActorAttrib* pAttr = pFantom->getAttrib();
		if (pAttr)
		{
			float fantomCurHP = pAttr->getHP() - pAttr->getMaxHP() * RETAIN_FANTOM_HP_PERCENT;
			pAttr->setHP(fantomCurHP);
		}
	}

	//�����ٻ��꣬����û���ٻ���
	if (m_WaitSummonTeams.size() != 0)
	{
		m_SummonState = SummonState::Summon_MonsterTeam;
		m_TickCount = 0;
		m_TeamTick = m_WaitSummonTeams[0].tick;
		SaveCurSummonData();
	}
	else
	{
		StopSummonMonster();
	}
}

void SummonMonsterSiegeMgr::EquipForBoarman(ClientMob* pClientMob)
{
	if (pClientMob == NULL)
		return;
	if (pClientMob->getDefID() != 3101)
		return;

	ActorBody* pActorBody = pClientMob->getBody();
	if (pActorBody == NULL)
		return;
	Rainbow::Entity* pEntity = pActorBody->getBodyEntity();
	if (pEntity == NULL)
		return;
	Model* pModel = pEntity->GetMainModel();
	if (pModel == NULL)
		return;

	vector<int> equipList = { 11002 , 11012 };

	//��һ��Ұ��װ����ͷ���ڶ���Ұ��װ������
	if (m_EquipBoarmanCount == 0)
	{
		pActorBody->setEquipItem(EQUIP_SLOT_TYPE::EQUIP_WEAPON, equipList[0]);
	}
	else if(m_EquipBoarmanCount == 1)
	{
		pActorBody->setEquipItem(EQUIP_SLOT_TYPE::EQUIP_WEAPON, equipList[1]);
	}
	else
	{
		//���װ��һ��
		srand((unsigned)time(nullptr));
		int itemIndex = rand() % equipList.size();
		if (itemIndex < equipList.size())
		{
			pActorBody->setEquipItem(EQUIP_SLOT_TYPE::EQUIP_WEAPON, equipList[itemIndex]);
		}
	}

	++m_EquipBoarmanCount;
}

int SummonMonsterSiegeMgr::GetDistanceByRand(bool isFantom, unsigned int salt)
{
	//�����������5��tick����Ҫʹ�ú��뼶
	time_t timeMs = time(nullptr) * (m_TickCount + 1000 + salt);
	srand((unsigned)timeMs);
	if (isFantom)
	{
		int deltaDistance = SummonMonsterSiegeMgr::SUMMON_FANTOM_MAX_DISTANCE - SummonMonsterSiegeMgr::SUMMON_FANTOM_MIN_DISTANCE;
		int doubleDeltaDis = deltaDistance * 2;
		//����0~doubleDeltaDis��ֵ
		int randomDistance = rand() % doubleDeltaDis;
		randomDistance = randomDistance - deltaDistance;
		return randomDistance;
	}
	else
	{
		int doubleDeltaDis = 2 * SummonMonsterSiegeMgr::SUMMON_NOAMAL_MONSTER_DISTANCE;
		int randomDistance = rand() % doubleDeltaDis;
		randomDistance = randomDistance - SummonMonsterSiegeMgr::SUMMON_NOAMAL_MONSTER_DISTANCE;
		LOG_INFO("SummonMonsterSiegeMgr::GetDistanceByRand: randomDistance %d", randomDistance);
		return randomDistance;
	}
}

VoidMonsterTeamInfo* SummonMonsterSiegeMgr::GetTeamInfoByRand()
{
	if (m_SiegeDifficultInfo.size() == 0)
		return NULL;

	SiegeDifficultInfo* curDifficultInfo = GetSiegeDifficultInfo();

	if (curDifficultInfo == NULL)
		return NULL;

	if (curDifficultInfo->monsterTeamInfos.size() == 0)
		return NULL;

	int randomRange = 0;
	for (VoidMonsterTeamInfo teamInfo : curDifficultInfo->monsterTeamInfos)
	{
		randomRange = randomRange + teamInfo.proportion;
	}

	srand((unsigned)time(nullptr));
	int randomNum = rand() % randomRange + 1;

	int teamSize = curDifficultInfo->monsterTeamInfos.size();
	for (int index = 0; index < teamSize; ++index)
	{
		VoidMonsterTeamInfo& teamInfo = curDifficultInfo->monsterTeamInfos[index];
		if (randomNum <= teamInfo.proportion)
		{
			return &teamInfo;
		}
		randomNum = randomNum - teamInfo.proportion;
	}
	
	return &(curDifficultInfo->monsterTeamInfos[0]);
}

bool SummonMonsterSiegeMgr::FindFitableSummonPoint(const WCoord& basePoint, WCoord *summonPoint, bool isFantom)
{
	for (size_t index = 0; index < 20; ++index)
	{
		int deltaX = GetDistanceByRand(isFantom, index);
		int deltaZ = GetDistanceByRand(isFantom, index);

		int trySummonX = basePoint.x + deltaX * BLOCK_SIZE;
		int trySummonZ = basePoint.z + deltaZ * BLOCK_SIZE;

		WCoord curWorldPos(trySummonX, basePoint.y, trySummonZ);
		WCoord curBlockPos = CoordDivBlock(curWorldPos);
		//��������2������λ��
		int blockYUpLimit = (curBlockPos.y + 5) > 255 ? 255 : curBlockPos.y + 5;
		int blockYDownLimit = (curBlockPos.y - 5) < 0 ? 0 : curBlockPos.y - 5;
		LOG_INFO("SummonMonsterSiegeMgr::FindFitableSummonPoint blockYUpLimit = %d,blockYDownLimit = %d", blockYUpLimit, blockYDownLimit);
		for (int blockYPos = blockYDownLimit; blockYPos <= blockYUpLimit; ++blockYPos)
		{
			WCoord readyBlockPos(curBlockPos.x, blockYPos, curBlockPos.z);
			int blockID = m_CurWorld->getBlockID(readyBlockPos);
			WCoord downBlockPos = DownCoord(readyBlockPos);
			WCoord topBlockPos = TopCoord(readyBlockPos);
			int downBlockID = m_CurWorld->getBlockID(downBlockPos);
			int topBlockID = m_CurWorld->getBlockID(topBlockPos);
			//��ǰλ�ú�ͷ���ǿ������鲢���·����ǿ�������ŷ��ر�λ��
			if (blockID == BLOCK_AIR && topBlockID == BLOCK_AIR && downBlockID != BLOCK_AIR)
			{
				summonPoint->x = readyBlockPos.x * BLOCK_SIZE;
				summonPoint->y = readyBlockPos.y * BLOCK_SIZE;
				summonPoint->z = readyBlockPos.z * BLOCK_SIZE;
				return true;
			}
		}
	}

	return false;
}

void SummonMonsterSiegeMgr::DeleteWaitSummonTeamByUin(int playerUin)
{
	vector<WaitSummonTeam> waitSummonTeams;
	for (WaitSummonTeam summonTeam : m_WaitSummonTeams)
	{
		if (summonTeam.playerUin != playerUin)
		{
			waitSummonTeams.push_back(summonTeam);
		}
	}
	m_WaitSummonTeams = waitSummonTeams;

	vector<int> playerUinArr;
	for (int uin : m_PlayerUinArr)
	{
		if (playerUin == uin)
			continue;
		playerUinArr.push_back(uin);
	}
	m_PlayerUinArr = playerUinArr;
}

ClientActor* SummonMonsterSiegeMgr::FindSummonFantom(long long objId)
{
	if (m_CurWorld == NULL)
		return NULL;
	ActorManager* actorManager = dynamic_cast<ActorManager*>(m_CurWorld->getActorMgr());
	if (actorManager == NULL)
		return NULL;

	auto findActorCB = [objId](ClientActor* pActor) -> bool
	{
		if (pActor->getObjId() == objId)
			return true;
		return false;
	};

	return actorManager->FindActor(findActorCB);
}

void SummonMonsterSiegeMgr::ShowFantomSkinById(int fantomID, ClientActor* pActor)
{
	if (!pActor)
		return;
	ActorBody* pActorBody = pActor->getBody();
	if (!pActorBody)
		return;
	pActorBody->showAllSkins(false);
	if(fantomID == ELITE_FANTOM_ID1)
		pActorBody->showSkin("body1", true);
	else if (fantomID == ELITE_FANTOM_ID2)
		pActorBody->showSkin("body2", true);
	else if (fantomID == ELITE_FANTOM_ID3)
		pActorBody->showSkin("body3", true);
}