
#ifndef __CLIENTACTORARROW_H__
#define __CLIENTACTORARROW_H__

#include "ClientActor.h"
//tolua_begin
enum ARROW_TYPE
{
	ARROW_NORMAL = 0,
	ARROW_PICKABLE,
	ARROW_SPEAR,
	ARROW_FIREBALL,
};
//tolua_end

class ClientActorArrow : public ClientActor //tolua_exports
{ //tolua_exports
	DECLARE_SCENEOBJECTCLASS(ClientActorArrow)
public:
	//tolua_begin
	ClientActorArrow();

	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld(bool keep_inchunk) override;

	static ClientActorArrow *shootArrow(World *pworld, ClientActor *shootactor, float strength, ARROW_TYPE arrowtype, bool setfire);
	static ClientActorArrow *shootArrow(World *pworld, ClientActor *shootactor, ClientActor *target, float speed, float deviation, bool setfire, ARROW_TYPE arrowtype);
	static ClientActorArrow *shootArrowAuto(World *pworld, const WCoord &pos, const Rainbow::Vector3f &dir, float speed, float deviation, long long shooterObjId);

	virtual void update(float dtime);
	virtual int getObjType()const override
	{
		return OBJ_TYPE_ARROW;
	}
	virtual void onCollideWithPlayer(ClientActor *player) override;
	virtual bool needWalkEffects()
	{
		return false;
	}
	//virtual void onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum);

	void init(ARROW_TYPE arrowtype);
	Rainbow::Entity *getArrowModel()
	{
		return m_ArrowModel;
	}

	void setShootingActor(ClientActor *actor);
	virtual ClientActor *getShootingActor();
	bool doAttackActor(ClientActor *target, Rainbow::Vector3f &motion);

	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool load(const void *srcdata, int version) override;
	virtual bool attackedFrom(OneAttackData &atkdata, ClientActor *attacker);

	bool NeedTickForever() override {
		return true;
	}

	bool isInFire()
	{
		return (m_AttachedEffect & 1) != 0;
	}
	bool hasExplodeEffect()
	{
		return (m_AttachedEffect & 2) != 0;
	}

	float m_KnockbackStrength;
	float m_AttackPoints;
	float m_BuffAttackAdd;
	ARROW_TYPE m_ArrowType;
	WCoord m_StartPos;
	//tolua_end
protected:
	virtual ~ClientActorArrow();

	Rainbow::Entity *m_ArrowModel;
	WORLD_ID m_ShootingID;
	//bool m_InFire;
	unsigned char m_AttachedEffect; //bit0: fire,   bit1: explode
}; //tolua_exports

#endif