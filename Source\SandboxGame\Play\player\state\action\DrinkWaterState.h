#pragma once
#include "PlayerState.h"
#include "DrinkWaterStateAction.h"

class DrinkWaterState :public PlayerState, public DrinkWaterStateAction  //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	DrinkWaterState(PlayerControl* host);
	virtual ~DrinkWaterState();
	virtual void doBeforeEntering() override;
	virtual std::string update(float dtime) override;
	virtual void doBeforeLeaving() override;
	virtual void OnTick(float elapse) final;
	//tolua_end

private:
	int m_DrinkStartMark;
	int m_DrinkItemDuration;
	int m_PlayDrinkTicks;

	int m_DrinkMark;
	bool m_HasFinished;
}; //tolua_exports 