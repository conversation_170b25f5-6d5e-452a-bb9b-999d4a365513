#include "FoodValue.h"
#include "ClientMob.h"
#include "ClientActor.h"
#include "DieComponent.h"

IMPLEMENT_REFCLASS(FoodValue)
FoodValue::FoodValue()
{

}

FoodValue::~FoodValue()
{
}


void FoodValue::SetBasicMaxFood(float fValue)
{
	m_fBasicMaxFood = fValue;
}

float FoodValue::GetBasicMaxFood() const
{
	return m_fBasicMaxFood;
}

void FoodValue::SetMaxFood(float fValue)
{
	m_fMaxFood = fValue;
}

float FoodValue::GetMaxFood() const
{
	return m_fMaxFood;
}

void FoodValue::SetOverflowFood(float fValue)
{
	m_fOverflowFood = fValue;
}


float FoodValue::GetOverflowFood() const
{
	return m_fOverflowFood;
}

void FoodValue::IncreaseFood(float fValue)
{
	IncreaseValueEvent(fValue);
}

void FoodValue::DecreaseFood(float fValue)
{
	DecreaseValueEvent(fValue);
}

void FoodValue::SetBasicOverflowFood(float fValue)
{
	m_fBasicOverflowFood = fValue;
}

float FoodValue::GetBasicOverflowFood() const
{
	return m_fBasicOverflowFood;
}

void FoodValue::OnValueChangeBefore()
{

}

void FoodValue::OnValueChangeAfter()
{
	if (m_fValue <= 0 && mpActor != nullptr)
	{
		auto DieComp = mpActor->SureDieComponent();
		if (DieComp)
		{
			DieComp->onDie();
		}
	}
} 