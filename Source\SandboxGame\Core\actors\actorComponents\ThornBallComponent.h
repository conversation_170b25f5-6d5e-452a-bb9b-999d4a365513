#ifndef __THORN_BALL_COMPONENT_H__
#define __THORN_BALL_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "SandboxGame.h"
#include "proto_common.pb.h"
struct ThornBallData
{
	ThornBallData()
	{
		pos = Rainbow::Vector3f(0, 0, 0);
		anchorId = 0;
	}
	int anchorId;
	Rainbow::Vector3f pos;
};
class EXPORT_SANDBOXGAME ThornBallComponent;
class ThornBallComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
	//typedef ActorComponentBase Super;
public:
	DECLARE_COMPONENTCLASS(ThornBallComponent)
	//tolua_begin
	ThornBallComponent();
	virtual ~ThornBallComponent();
	void onTick();
	//tolua_end
	void CreateEvent2();
	void DestroyEvent2();
public:
	void	setThornAnchorId(int anchorId, Rainbow::Vector3f pos); //绑定位置
	void	removeThornBallModel(int num);
	void	clearThornAnchorId();
	int		getThornAnchorNum();
	ThornBallData	getThornAnchorAt(int index);
	void	createThornBall();//  创建模型
	int		getThronBallNum();//能挂多少个
	void	dropThornBall(int num);
	void	doThornBallTick();
	void	showThornBall(bool is);
	void	reboundsAttackedUp(float atkpoints);
	void	reboundsAttackedRound(float atkpoints, int dir,bool impactInjured = false);
	void	attackedWounded(float atkpoints, bool impactInjured = false);
	int		checkCrashDir();
private:
	std::vector<ThornBallData>	m_thornData;

	MNSandbox::AutoRef<MNSandbox::Listener<google::protobuf::RepeatedPtrField< ::game::common::PB_SawtoothInfo >*>> m_listenerThornBall1;
}; //tolua_exports

#endif