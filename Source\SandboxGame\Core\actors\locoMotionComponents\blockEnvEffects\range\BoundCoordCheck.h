#ifndef BOUND_COORD_CHECK_H_
#define BOUND_COORD_CHECK_H_

#include "BlockRangeCheckBase.h"
#include <vector>

class BoundCoordCheck : public BlockRangeCheckBase
{
public:
	BoundCoordCheck(int min_xz_limit, int min_y_limit, int blockCnt, ...);

	virtual bool isInRange(ClientActor* pActor, BlockEnvEffectBase* pEffect) override;
private:
	std::vector<int> m_blockids;
	const int   m_minXZLimit;
	const int   m_minYLimit;
};


#endif