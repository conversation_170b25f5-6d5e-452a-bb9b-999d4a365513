﻿#ifndef __CLIENTFLY_COMPONENT_H__
#define __CLIENTFLY_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "ActorLocoMotion.h"
#include "proto_common.pb.h"
#include "proto_hc.pb.h"
#include "flatbuffers/flatbuffers.h"
#include "ChunkSave_generated.h"

namespace FBSave
{
	struct ActorFlyMob;
	struct SectionActor;
}

class ClientFlyComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(ClientFlyComponent)

	ClientFlyComponent();
	~ClientFlyComponent();

	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void tick();
	virtual bool init();
	float getAirSpeed();
	virtual ActorLocoMotion* newLocoMotion();
	bool needSaveInChunk();
	virtual flatbuffers::Offset<FBSave::SectionActor> save(flatbuffers::FlatBufferBuilder& builder, flatbuffers::Offset<FBSave::ActorMob>& mobdata);
	virtual flatbuffers::Offset<FBSave::ActorFlyMob> saveMob(flatbuffers::FlatBufferBuilder& builder, flatbuffers::Offset<FBSave::ActorMob>& mobData);
	virtual void applyActorCollision(ClientActor* actor);
	virtual bool load(const void* srcdata, int version);
	bool loadFromClientMobFB(const void* srcdata, int version);
	virtual int saveToPB(game::hc::PB_GeneralEnterAOIHC* pb);
	virtual int LoadFromPB(const game::hc::PB_GeneralEnterAOIHC& pb);
	virtual void moveToPosition(const WCoord& pos, float yaw, float pitch, int interpol_ticks);
	virtual bool canDespawn();
	virtual void onDie();
	//tolua_begin
	void setHpProgressBarOffsetY(int offsetY);//916?°?? 2021/08/18 codeby:wudeshen
	int getHpProgressOffsetY();
	//tolua_end
protected:
	//ClientMob* m_owner;
	int m_HpProgressOffsetY; //916?°?? 2021/08/18 codeby:wudeshen

};//tolua_exports

#endif