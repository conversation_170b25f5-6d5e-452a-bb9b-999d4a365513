#include "ClientActorFuncWrapper.h"
#include "ClientActorDef.h"
#include "LuaInterfaceProxy.h"
#include "ClientActor.h"
using namespace MINIW;
IMPLEMENT_COMPONENTCLASS(ClientActorFuncWrapper)

ClientActorFuncWrapper::ClientActorFuncWrapper()
{
	m_nAiCanMoveTerrain = 3;
	m_JetpackFlying = false;
	m_AvoidSun = false;
	m_AvoidCloud = true;
	m_AvoidWater = true;
	m_CanSwimming = false;
	m_CanFly = false;
	m_CanPassClosedWoodenDoors = false;
	m_KeepDigging = false;
	m_KeepEatting = false;
	m_bMustSyncPos = false;
	m_nImmuneFall = 0;
	m_bCanmove = true;
	m_JetpackFlying = false;
	m_BodyTypeBeforeMutate = 0;
	m_FallDistance = 0;
	m_BtreeCtrlCount = 0;
	m_bSimulateFly = false;
	m_facade = "";
	m_LandMovementFactor = 200;
	m_openDoor = false;
	m_flyHeight = 5.0;
}
ClientActorFuncWrapper::~ClientActorFuncWrapper()
{

}

void ClientActorFuncWrapper::setJetpackFlying(bool b)
{
	m_JetpackFlying = b;
}
bool ClientActorFuncWrapper::getJetpackFlying()
{
	return m_JetpackFlying;
}
void ClientActorFuncWrapper::setLandMove(bool enable)
{
	if (enable)
	{
		m_nAiCanMoveTerrain |= LandTerrain;
	}
	else
	{
		m_nAiCanMoveTerrain &= ~LandTerrain;
	}
}

bool ClientActorFuncWrapper::canLandMove()
{
	return m_nAiCanMoveTerrain & LandTerrain;
}

void ClientActorFuncWrapper::setWaterMove(bool enable)
{
	if (enable)
	{
		m_nAiCanMoveTerrain |= WaterTerrain;
	}
	else
	{
		m_nAiCanMoveTerrain &= ~WaterTerrain;
	}
}

bool ClientActorFuncWrapper::canWaterMove()
{
	return m_nAiCanMoveTerrain & WaterTerrain;
}

void ClientActorFuncWrapper::setLavaMove(bool enable)
{
	if (enable)
	{
		m_nAiCanMoveTerrain |= LavaTerrain;
	}
	else
	{
		m_nAiCanMoveTerrain &= ~LavaTerrain;
	}
}

bool ClientActorFuncWrapper::canLavaMove()
{
	return m_nAiCanMoveTerrain & LavaTerrain;
}

void ClientActorFuncWrapper::setBodyTypeBeforeMutate(int n)
{
	m_BodyTypeBeforeMutate = n;
}
int ClientActorFuncWrapper::getBodyTypeBeforeMutate()
{
	return m_BodyTypeBeforeMutate;
}

void ClientActorFuncWrapper::setKeepDigging(int n)
{
	m_KeepDigging = n;
}
int ClientActorFuncWrapper::getKeepDigging()
{
	return m_KeepDigging;
}

void ClientActorFuncWrapper::setKeepEatting(int n)
{
	m_KeepEatting = n;
}
int ClientActorFuncWrapper::getKeepEatting()
{
	return m_KeepEatting;
}

void ClientActorFuncWrapper::setFallDistance(float d)
{
	m_FallDistance = d;
}
float ClientActorFuncWrapper::getFallDistance()
{
	return m_FallDistance;
}

void ClientActorFuncWrapper::setMinicodeRole(bool b)
{
	m_isMinicodeRole = b;
}
bool ClientActorFuncWrapper::isMinicodeRole()
{
	return m_isMinicodeRole;
}

void ClientActorFuncWrapper::addFallDistance(float d)
{
	m_FallDistance += d;
}
void ClientActorFuncWrapper::setBtreeControl(bool enable)
{
	if (enable) {
		m_BtreeCtrlCount++;
	}
	else {
		m_BtreeCtrlCount--;
		if (m_BtreeCtrlCount < 0) m_BtreeCtrlCount = 0;
	}
}
bool ClientActorFuncWrapper::getBtreeControl()
{
	return m_BtreeCtrlCount > 0;
}

const char *  ClientActorFuncWrapper::getActorFacade()
{
	return m_facade.c_str();
}

void ClientActorFuncWrapper::setActorFacade(std::string facde)
{
	m_facade = facde;
}
void ClientActorFuncWrapper::setAIMoveSpeed(float speed)
{
	m_LandMovementFactor = speed;
}

float ClientActorFuncWrapper::getAIMoveSpeed()
{
	if (!GetOwner()) return m_LandMovementFactor;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return m_LandMovementFactor;
	if (m_owner->getSneaking())
		return m_LandMovementFactor * GetLuaInterfaceProxy().get_lua_const()->sneaking_yidong_beilv;
	else
		return m_LandMovementFactor;
}

void ClientActorFuncWrapper::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	ClientActor *pOwnerActor = static_cast<ClientActor*>(owner);
	if (pOwnerActor)
	{
		pOwnerActor->BindFuncWrapper(this);
	}
	Super::OnEnterOwner(owner);
}

void ClientActorFuncWrapper::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	ClientActor *pOwnerActor = static_cast<ClientActor*>(owner);
	if (pOwnerActor)
	{
		pOwnerActor->BindFuncWrapper(NULL);
	}
	Super::OnLeaveOwner(owner);
}