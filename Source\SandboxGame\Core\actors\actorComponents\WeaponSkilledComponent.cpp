#include "WeaponSkilledComponent.h"
#include "ClientPlayer.h"
#include "ClientInfoProxy.h"

void WeaponSkilledComponent::addSkilledPoint(ClientPlayer* player, int pointType, int weaponType)
{
	int ownerUin = player->getUin();
	if (ownerUin != GetClientInfoProxy()->getUin()) return;
	
	MNSandbox::GetGlobalEvent().Emit<int, int>("ClientAccountMgr_addSkilledPoint", pointType, weaponType);
	/*
	if (m_ownerPlayer->getUin() != GetClientAccountMgr().getUin()) return;
	AccountData* pAccountData = GetClientAccountMgr().getAccountData();
	if (pAccountData == nullptr) return;
	
	int skilledPoint = 0;
	ConstAtLua* pConstAtLua = GetLuaInterfaceProxy().get_lua_const();
	switch (pointType)
	{
	case WEAPON_SKILLED_TYPE_KILLED:
		skilledPoint = pConstAtLua->weapon_skillpoint_killed;
		break;
	case WEAPON_SKILLED_TYPE_CDSKILL:
		skilledPoint = pConstAtLua->weapon_skillpoint_cdskill;
		break;
	case WEAPON_SKILLED_TYPE_CUTTREE:
		skilledPoint = pConstAtLua->weapon_skillpoint_cuttree;
		break;
	case WEAPON_SKILLED_TYPE_DIGBLOCK:
		skilledPoint = pConstAtLua->weapon_skillpoint_digblock;
		break;
	case WEAPON_SKILLED_TYPE_PLANTLNAD:
		skilledPoint = pConstAtLua->weapon_skillpoint_plantland;
		break;
	default: break;
	}
	if (skilledPoint <= 0) return;
	
	pAccountData->addSkilledPoint(weaponType, skilledPoint);
	*/
}