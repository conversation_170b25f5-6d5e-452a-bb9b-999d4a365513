#include "ActionAttrStateComponent.h"

#include "ClientActor.h"
#include "ClientPlayer.h"

#include "WorldManager.h"
#include "GameModeDef.h"
#include "ActorLocoMotion.h"
#include <assert.h>
#include "ClientActorManager.h"
#include "ClientPlayer.h"

IMPLEMENT_COMPONENTCLASS(ActionAttrStateComponent)


ActionAttrStateComponent::ActionAttrStateComponent()
{
	m_ActionAttrState = GetInitValue();
}


ActionAttrStateComponent::~ActionAttrStateComponent()
{

}

void ActionAttrStateComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	ClientActor* pOwnerActor = dynamic_cast<ClientActor*>(owner);
	if (pOwnerActor)
	{
		pOwnerActor->BindActionAttrStateComponent(this);
	}
}

void ActionAttrStateComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	ClientActor* pOwnerActor = dynamic_cast<ClientActor*>(owner);
	if (pOwnerActor)
	{
		pOwnerActor->BindActionAttrStateComponent(nullptr);
	}
}

void ActionAttrStateComponent::CreateModuleEvent()
{

}

bool ActionAttrStateComponent::checkActionAttrState(int state)
{
	bool ret = false;
	/*bool succ = GetOwnerActor()->Event2().Emit<bool&,int>("checkActionAttrState", ret,state);
	if (succ)
	{
		return ret;
	}*/
	if (GetOwnerPlayer())
	{
		ret = GetOwnerPlayer()->checkActionAttrState(state);
		return ret;
	}
	return checkActionAttrState_Base(state);
}

bool ActionAttrStateComponent::checkActionAttrState_Base(int state)
{
	unsigned tmp = m_ActionAttrState;
	if (g_WorldMgr == NULL) return false;
	if (!g_WorldMgr->isGameMakerRunMode())
	{
		tmp = GetInitValue();
	}
	if (g_WorldMgr->canOpenAttrPropSet())
	{
		tmp = m_ActionAttrState;
	}
	return (tmp & state) > 0;
}


void ActionAttrStateComponent::setActionAttrState(int actionattr, bool b)
{
	//bool succ = GetOwnerActor()->Event2().Emit<int,int>("setActionAttrState", actionattr,b);
	auto m_owner = dynamic_cast<ClientPlayer*>(GetOwner());
	if (m_owner)
	{
		m_owner->setActionAttrState(actionattr, b);
		return;
	}

	setActionAttrState_Base(actionattr, b);
}

void ActionAttrStateComponent::setActionAttrState_Base(int actionattr, bool b)
{
	if (!g_WorldMgr)
		return;

	if (!g_WorldMgr->m_RuleMgr)
	{
		if (!g_WorldMgr->canOpenAttrPropSet())
		{
			return;
		}
	}

	if (b)
		m_ActionAttrState |= actionattr;
	else
		m_ActionAttrState &= ~actionattr;
}

void ActionAttrStateComponent::setAllActionAttrState(unsigned int attr)
{
	m_ActionAttrState = attr;
}

unsigned int ActionAttrStateComponent::getAllActionAttrState()
{
	return m_ActionAttrState;
}

void ActionAttrStateComponent::resetActionAttrState()
{
	m_ActionAttrState = GetInitValue();
}

int ActionAttrStateComponent::GetInitValue()
{
	return ENABLE_INITVALUE;
}