#include "container_tombstone.h"
#include "world.h"
#include "BlockScene.h"
#include "ClientActor.h"
#include "Text3D/Text3D.h"
#include "BlockTombStone.h"
#include "DefManagerProxy.h"
#include "chunk.h"
#include "ClientActorManager.h"
#include "ClientPlayer.h"
#include "ActorVehicleAssemble.h"
#include "backpack.h"
#include "OgreStringUtil.h"
#include "ActorCSProto.h"
#include "OgreUtils.h"
#include "ClientInfoProxy.h"
#include "VehicleWorld.h"
using namespace MNSandbox;
using namespace Rainbow;

TombStoneContainer::TombStoneContainer(): WorldContainer(TOMBSTONE_STRAT_INDEX),
                                          m_TextObj(NULL), m_TextColor(255, 255, 255), m_strText(""),
                                          m_strContent(""), m_isFirstSpawn(false),
                                          m_canEdit(false), m_profession(0), m_lootItemId(0)
{
	for (int i = 0; i < TOMB_STONE_NUM; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
	m_NeedTick = true;
}

TombStoneContainer::TombStoneContainer(const WCoord& blockpos): WorldContainer(blockpos, TOMBSTONE_STRAT_INDEX),
                                                                m_TextObj(NULL), m_TextColor(255, 255, 255),
                                                                m_strText(""),
                                                                m_strContent(""), m_isFirstSpawn(false),
                                                                m_canEdit(false), m_profession(0), m_lootItemId(0)
{
	for (int i = 0; i < TOMB_STONE_NUM; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
	m_NeedTick = true;
}

TombStoneContainer::~TombStoneContainer()
{
	if (m_TextObj) 
	{
		Rainbow::GameObject::Destroy(m_TextObj->GetGameObject());
	}
}

int TombStoneContainer::getObjType() const
{
	return OBJ_TYPE_SIGNS;
}

flatbuffers::Offset<FBSave::ChunkContainer> TombStoneContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);
	auto str = builder.CreateString(m_strText);
	auto strContent = builder.CreateString(m_strContent);
	int8_t isFirstSpawn = m_isFirstSpawn ? 1 : 0;
	
	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
	flatbuffers::Offset<FBSave::ItemGrid> items[NUMGRIDS];
	unsigned char indices[NUMGRIDS];
	int count = 0;

	for (int i = 0; i < NUMGRIDS; i++)
	{
		if (!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			indices[count] = (unsigned char)i;
			count++;
		}
	}

	auto actor = FBSave::CreateContainerTombStone(builder, basedata, str, strContent, isFirstSpawn,builder.CreateVector(items, count), builder.CreateVector(indices, count), m_profession);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerTombStone, actor.Union());
}

bool TombStoneContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerTombStone*>(srcdata);
	if (!src)
	{
		return false;
	}
	loadContainerCommon(src->basedata());
	m_isFirstSpawn = src->isFirstSpawn() != 0;
	m_strContent = src->content()->c_str();
	m_profession = src->profession();
	init();
	if (m_TextObj)
	{
		std::string OriginalJson = "";
		if (src->text() != NULL) OriginalJson = src->text()->c_str(); 
		int lang = GetClientInfoProxy()->getArchiveLang();		//当前选择的语言
		std::string showText = GetClientInfoProxy()->parseTextFromLanguageJson(OriginalJson, lang);
		GetDefManagerProxy()->filterStringDirect((char*)showText.c_str());
		//m_TextObj->setText(showText.c_str());
	}
	if (src->text() != NULL) 
	{
		m_strText = src->text()->c_str();
	}
	auto items = src->items();
	auto indices = src->indices();
	for (size_t i = 0; i < items->size(); i++)
	{
		int index = indices->Get(i);
		assert(index >= 0 && index < sizeof(m_Grids) / sizeof(m_Grids[0]));

		m_Grids[index].load(items->Get(i));
	}
	return true;
}

void TombStoneContainer::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	//registerUpdateTick();
	registerUpdateDisplay();

	if (m_World && m_World->onClient() && m_TextObj)
	{
		applyBlockDir();
		float dist = 16 * BLOCK_FSIZE;
		m_TextObj->SetVisibleDistance(dist);
		m_TextObj->AttachToScene(m_World->getScene());
	}
}

void TombStoneContainer::leaveWorld()
{
	if (m_World->onClient() && m_TextObj)
	{
		m_TextObj->DetachFromScene();
	}
	WorldContainer::leaveWorld();
}

void TombStoneContainer::updateTick()
{
	//if (m_World && m_World->onClient() && m_TextObj)
	//{
	//	int lt = 0;
	//	if (m_vehicleWorld)
	//	{
	//		WCoord pos = m_BlockPos * BLOCK_SIZE;
	//		auto pVehicle = m_vehicleWorld->getActorVehicleAssemble();
	//		if (pVehicle)
	//		{
	//			pos = pVehicle->convertWcoord(m_BlockPos);
	//			lt = m_World->getBlockLightValue(CoordDivBlock(pos));
	//		}
	//	}
	//	else
	//	{
	//		lt = m_World->getBlockLightValue(m_BlockPos);
	//	}
	//	//float c = 1.0f * lt / 15;
	//	//m_TextObj->setTextColor(c * m_TextColor.r, c * m_TextColor.g, c * m_TextColor.b);
	//}
}

void TombStoneContainer::onAttachUI()
{

}

void TombStoneContainer::onDetachUI()
{

}

void TombStoneContainer::updateDisplay(float dtime)
{
	if (m_vehicleWorld)
	{
		applyBlockDir();
	}
}

void TombStoneContainer::dropItems()
{
	for (int i = 0; i < TOMB_STONE_NUM; i++)
	{
		dropOneItem(m_Grids[i]);
	}
}

BackPackGrid* TombStoneContainer::index2Grid(int index)
{
	assert(index >= TOMBSTONE_STRAT_INDEX);
	index -= TOMBSTONE_STRAT_INDEX;
	if (index >= getGridCount()) return NULL;
	if (index < TOMB_STONE_NUM) return &m_Grids[index];
	return NULL;
}

int TombStoneContainer::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (int i = 0; i < TOMB_STONE_NUM; i++)
		{
			if (m_Grids[i].isEmpty()) continue;

			storeGridData(pItemInfos->Add(), &m_Grids[i]);
		}
	}
	if (pAttrInfos)
	{
		pAttrInfos->Add(getAttrib(0)); //方块id
		if (m_canEdit)
			pAttrInfos->Add(1.0f);
		else
			pAttrInfos->Add(0.0f);
	}
	return 3;
}

int TombStoneContainer::getGridCount()
{
	return TOMB_STONE_NUM;
}

void TombStoneContainer::init()
{
#ifndef IWORLD_SERVER_BUILD
	m_TextObj = Rainbow::Text3D::Create(64, 56, 56, 40, 40, false, false);
	if (m_TextObj)
	{
		m_TextObj->setRenderType(Rainbow::CANNOT_IN_SNAPSHOT);
	}
#endif
}

void TombStoneContainer::applyBlockDir()
{
	if (m_World && m_World->onClient() && m_TextObj)
	{
		Rainbow::WorldPos pos;
		Rainbow::Quaternionf rot;
		BlockTombTone::computeTextXform(pos, rot, m_World, static_cast<VehicleWorld*>(m_vehicleWorld), m_BlockPos);
		//m_TextObj->setRotation(rot);
		//m_TextObj->setPosition(pos);
		//m_TextObj->update(0);
	}
}

void TombStoneContainer::setFontColor(int r, int g, int b)
{
	m_TextColor.set(r, g, b, 255);
}

void TombStoneContainer::canEdit(IClientPlayer* player)
{
	m_canEdit = false;
	if (!player)
		return;
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	int iItemId = playerTmp->getCurToolID();
	if (iItemId == 11011 || iItemId == 11012 || iItemId == 11013 ||
		iItemId == 11014 || iItemId == 11015 || iItemId == 11016)
	{
		m_canEdit = true;
	}
}

int TombStoneContainer::getLootResultInfoSize()
{
	return m_lootItemResultInfos.size();
}

std::string TombStoneContainer::getLootResultInfoByType(int type)
{
	if (m_lootItemResultInfos.size() != (MAX_NUM-1) || type >= (int)m_lootItemResultInfos.size())
	{
		LOG_INFO("======loot result data occurs error!!!======");
		return "";
	}
	return m_lootItemResultInfos[type];
}

void TombStoneContainer::setTombStoneTitle(std::string title)
{
	if (m_strText != title)
	{
		m_strText = title;
	}
}

void TombStoneContainer::setTombStoneContent(std::string content)
{
	if (m_strContent != content)
	{
		m_strContent = content;
	}
}

bool TombStoneContainer::canPlaceMode(IClientPlayer* player)
{
	if (!player)
		return false;
	int iItemId = player->getCurToolID();
	if (iItemId == 12595 || iItemId == 12596 || iItemId == 12597 || iItemId == 12624)
	{
		if (getMobGiftNum() == 0)
		{
			return true;
		}
	}
	return false;
}

void TombStoneContainer::getMobLootItem(IClientPlayer* player,std::string& out)
{
	if (!player)return;
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	int index = playerTmp->getCurShortcut() + playerTmp->getBackPack()->getShortcutStartIndex();
	BackPackGrid* src = playerTmp->getBackPack()->index2Grid(index);
	if (!src)
	{
		return;
	}
	std::string itemUserData = src->getUserdataStr();
	out = itemUserData;
	splitItemUserData(itemUserData);
}

int TombStoneContainer::getMobGiftNum()
{
	int num = 0;
	for (int i = 0; i < TOMB_STONE_NUM; i++)
	{
		if (m_Grids[i].def)
			num++;
	}

	return num;
}

int TombStoneContainer::addItemByCount(int itemid, int num,std::string userStr)
{
	//return InsertItemToEmptyGrids(this, 0, &m_Grids[0], getGridCount(), itemid, 1, -1, 0, NULL, NULL, userStr.c_str());;
	GridCopyData data(itemid, 1, -1, -1, 0, NULL, 0, NULL, NULL, userStr.c_str(), NULL);
	return InsertItemToEmptyGrids_byGridCopyData(this, 0, &m_Grids[0], getGridCount(), data);
}

void TombStoneContainer::removeItemByCount(int itemid, int num)
{
	num = 1;
	int maxgrids = getGridNum();
	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = index2Grid(i + m_BaseIndex);
		if (grid && grid->getItemID() == itemid)
		{
			if (num >= grid->getNum())
			{
				num -= grid->getNum();
				grid->addNum(-grid->getNum());
				grid->clear();
				afterChangeGrid(grid->getIndex());
			}
			else
			{
				grid->addNum(-num);
				afterChangeGrid(grid->getIndex());
				return;
			}
		}
	}
}

int TombStoneContainer::getGridItemId()
{
	if (m_Grids[0].def)
		return m_Grids[0].def->ID;
	return 0;
}

void TombStoneContainer::setGridItemId(int itemId)
{
	m_Grids[0].setItem(itemId, 1);
	afterChangeGrid(m_BaseIndex);
}

void TombStoneContainer::splitItemUserData(std::string& userData)
{
	if (userData.empty())
		return;
	m_lootItemResultInfos.clear();
	Rainbow::StringUtil::split(m_lootItemResultInfos,userData,"|");
}

const char* TombStoneContainer::getText() const
{
	return m_strText.c_str();
}

std::string TombStoneContainer::getAllText()
{
	jsonxx::Object textObj;
	textObj << "title" << m_strText;
	textObj << "content" << m_strContent;
	std::string textJson = textObj.json();
	return textJson;
}

void TombStoneContainer::setText(const char* text)
{
	if (!m_World) return;
	if (m_TextObj)
	{
		int lang = GetClientInfoProxy()->getArchiveLang();
		std::string showText = GetClientInfoProxy()->parseTextFromLanguageJson(text, lang);
		GetDefManagerProxy()->filterStringDirect((char*)showText.c_str());
		m_TextObj->setText(showText.c_str());

		//保存chunk数据
		Chunk* pchunk = m_World->getChunk(m_BlockPos);
		if (pchunk)
		{
			pchunk->m_Dirty = true;
		}
	}
	m_strText = text;
	m_isFirstSpawn = true;
	m_World->markBlockForUpdate(m_BlockPos, m_BlockPos, true);
}
