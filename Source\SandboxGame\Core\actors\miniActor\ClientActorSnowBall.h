#pragma once
#pragma once

#include "ClientActorProjectile.h"

namespace FBSave
{
	struct SectionActor;
}

class ClientActorSnowBall :public ClientActorProjectile //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ClientActorSnowBall();
	void onImpactWithActor(ClientActor *actor, const std::string& partname) override;
	flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	bool load(const void *srcdata, int version) override;
	virtual bool supportSaveToPB()
	{
		return false;
	}
	//tolua_end
protected:
	~ClientActorSnowBall();
}; //tolua_exports

