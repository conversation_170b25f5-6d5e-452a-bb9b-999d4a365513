#include "ActorMechaUnit.h"
#include "SectionMesh.h"
#include "section.h"
#include "BlockMaterialMgr.h"
#include "BlockMechaBlock.h"
#include "world.h"
#include "container_mecha.h"
#include "special_blockid.h"
#include "Collision.h"
#include "container_world.h"		   
#include "EffectManager.h"
#include "OgreWCoord.h"
#include "ClientActor.h"
#include "MechaLocoMotion.h"
#include "ActorLocoMotion.h"
#include "MpActorManager.h"
extern bool g_EnableReLighting;
using namespace MINIW;
using namespace Rainbow;
const int MAX_MECHAUNIT_BLOCKS = 256; //一个mecha unit最多拥有这么多方块

enum
{
	MECHA_MOTION_BLOCKED = 0,
	MECHA_MOTION_CONTINUE,
	MECHA_MOTION_POWERED,
	MECHA_MOTION_NOPOWER
};


IMPLEMENT_COMPONENTCLASS(MechaLocoMotion)
MechaLocoMotion::MechaLocoMotion() : m_MoveType(), m_MaxMoveLen(0),
m_Cur<PERSON>oveLen(0),
m_LastMoveLen(0),
m_Speed(0),
m_BeBlocked(false),
m_RunTime(-1), m_RotSpeed(0), m_bEnd(false)
{
}

void MechaLocoMotion::tick()
{
	if(m_MoveType == MECHA_MOVE_ROTATE) tickRotate();
	else tickSlide();
}

void MechaLocoMotion::setEnd(bool end)
{
	m_bEnd = end;
}

void MechaLocoMotion::getCollideBox(CollideAABB &box)
{
	box.setPoints(m_MinPos+m_Position, m_MaxPos+m_Position);
}

void MechaLocoMotion::startSlideToBlock(MECHA_MOVE_T movetype, const WCoord &fromblock, const WCoord &targetblock, const WCoord &originblock, const WCoord &minblock, const WCoord &maxblock, int speed, int curmovelen, bool beblocked)
{
	m_MoveType = movetype;
	assert(fromblock==originblock || targetblock==originblock);
	if(fromblock == originblock)
	{
		m_FromPos = fromblock*BLOCK_SIZE;
		m_ToPos = m_FromPos + (m_FromPos - targetblock*BLOCK_SIZE);
	}
	else //在移动过程中反向后的运动状态
	{
		m_FromPos = targetblock*BLOCK_SIZE;
		m_ToPos = m_FromPos + (m_FromPos - fromblock*BLOCK_SIZE);
		Swap(m_FromPos, m_ToPos);
	}

	m_MinPos = (minblock - originblock) * BLOCK_SIZE;
	m_MaxPos = (WCoord(maxblock.x+1,maxblock.y+1,maxblock.z+1) - originblock) * BLOCK_SIZE;

	WCoord d = m_ToPos - m_FromPos;
	m_MaxMoveLen = Max(Abs(d.x), Max(Abs(d.y),Abs(d.z)));
	m_CurMoveLen = curmovelen;
	m_LastMoveLen = curmovelen;
	m_Speed = speed;
	m_BeBlocked = beblocked;

	WCoord pos = d*m_CurMoveLen/m_MaxMoveLen + m_FromPos;
	gotoPosition(pos, 0, 0);
}

void MechaLocoMotion::resetNewTarget(const WCoord &dp)
{
	m_FromPos = m_ToPos;
	m_ToPos = m_ToPos + dp*BLOCK_SIZE;

	WCoord d = m_ToPos - m_FromPos;
	m_MaxMoveLen = Max(Abs(d.x), Max(Abs(d.y),Abs(d.z)));
	m_CurMoveLen = 0;
	m_LastMoveLen = 0;

	m_Position.x = m_CurMoveLen*d.x/m_MaxMoveLen + m_FromPos.x;
	m_Position.y = m_CurMoveLen*d.y/m_MaxMoveLen + m_FromPos.y;
	m_Position.z = m_CurMoveLen*d.z/m_MaxMoveLen + m_FromPos.z;
}

void MechaLocoMotion::reverseMoveDir()
{
	Swap(m_FromPos, m_ToPos);
	m_CurMoveLen = m_MaxMoveLen - m_CurMoveLen;
	m_LastMoveLen = m_CurMoveLen;
}

bool MechaLocoMotion::setRotateDir(bool ispositive)
{
	if(ispositive && m_RotSpeed<0 || !ispositive && m_RotSpeed>0)
	{
		m_RotSpeed = -m_RotSpeed;
		return true;
	}
	return false;
}

bool MechaLocoMotion::getRoteteDir()
{
	return m_RotSpeed > 0;
}

inline int CalMaxRadius(int x1, int y1, int x2, int y2)
{
	int v = Rainbow::Max(x1*x1, x2*x2) + Rainbow::Max(y1*y1, y2*y2);
	return Rainbow::RoundToCeil(Rainbow::Sqrt(v));
}
void MechaLocoMotion::startRotate(MECHA_MOVE_T movetype, const WCoord &originblock, const WCoord &driverblock, const WCoord &minblock, const WCoord &maxblock, float rotspeed, float curangle, bool beblocked)
{
	m_MoveType = movetype;
	m_RotSpeed = rotspeed;
	m_BeBlocked = beblocked;

	WCoord pos = originblock * BLOCK_SIZE + WCoord(BLOCK_SIZE/2, BLOCK_SIZE/2, BLOCK_SIZE/2);
	gotoPosition(pos, curangle, 0);

	WCoord minb = minblock - originblock;
	WCoord maxb = maxblock - originblock;
	WCoord axis = originblock - driverblock;

	if(axis.x != 0)
	{
		int r = CalMaxRadius(minb.y, minb.z, maxb.y, maxb.z);
		minb.y = minb.z = -r;
		maxb.y = maxb.z = r;
	}
	else if(axis.y != 0)
	{
		int r = CalMaxRadius(minb.x, minb.z, maxb.x, maxb.z);
		minb.x = minb.z = -r;
		maxb.x = maxb.z = r;
	}
	else
	{
		int r = CalMaxRadius(minb.x, minb.y, maxb.x, maxb.y);
		minb.x = minb.y = -r;
		maxb.x = maxb.y = r;
	}

	m_MinPos = minb*BLOCK_SIZE - WCoord(BLOCK_SIZE/2, BLOCK_SIZE/2, BLOCK_SIZE/2);
	m_MaxPos = maxb*BLOCK_SIZE + WCoord(BLOCK_SIZE/2, BLOCK_SIZE/2, BLOCK_SIZE/2);
}

/*
inline int DotProduct(const WCoord &d1, const WCoord &d2)
{
	return d1.x*d2.x + d1.y*d2.y + d1.z*d2.z;
}
float PointSegmentDistSqr(const WCoord &pt, const WCoord &sa, const WCoord &sb)
{
	WCoord d = pt - sa;
	WCoord ab = sa - sb;
	if(DotProduct(d, ab) >= 0) return d.lengthSquared();

	d = pt - sb;
	int tmpdot = DotProduct(d, ab);
	if(tmpdot <= 0) return d.lengthSquared();

	return d.lengthSquared() - tmpdot*tmpdot/ab.lengthSquared();
}*/

void MechaLocoMotion::tickRotate()
{
	ActorMechaUnit *unit = static_cast<ActorMechaUnit *>(getOwnerActor());

	if(!m_pWorld->isRemoteMode() || !m_BeBlocked)
	{
		m_RotateYaw += m_RotSpeed;
		if(m_RotateYaw>=360.0f && m_PrevRotateYaw>=360.0f)
		{
			m_RotateYaw -= 360.0f;
			m_PrevRotateYaw -= 360.0f;
		}
		else if (m_RotateYaw<=-360.0f && m_PrevRotateYaw<=-360.0f)
		{
			m_RotateYaw += 360.0f;
			m_PrevRotateYaw += 360.0f;
		}
	}

	if(!m_pWorld->isRemoteMode())
	{
		if(unit->checkRotateCollision(m_RotateYaw))
		{
			m_RotateYaw = m_PrevRotateYaw;
			unit->onCollideStop();
			if(!m_BeBlocked)
			{
				m_BeBlocked = true;
				sendMechaMotionToClient(MECHA_MOTION_BLOCKED);
			}
			// 已经停止，切回未供能状态
			if (m_bEnd)
				unit->onArriveTarget();
			WCoord unitDriver = unit->getDriverBlock();
			if (m_pWorld->getBlockID(unitDriver) != unit->getDriverBlockId())
			{
				unit->onArriveTarget();
				return;
			}
			return;
		}

		if(m_BeBlocked)
		{
			m_BeBlocked = false;
			sendMechaMotionToClient(MECHA_MOTION_CONTINUE);
		}

		if (unit->getAngle() > 0 || m_bEnd)
		{
			int angle = unit->getAngle()%360;
			int anglereal = (int)m_RotateYaw%360;
			if (angle == 0)
			{
				if (anglereal>320)
					anglereal -= 360;
				else if( anglereal < -320)
					anglereal += 360;
			}
			if (angle >= anglereal && angle <= (anglereal + m_RotSpeed))
			{
				m_RotateYaw = (float)angle;
				unit->onArriveTarget();
			}
			else if( -angle <= anglereal && -angle >= (anglereal + m_RotSpeed))
			{
				m_RotateYaw = (float)-angle;
				unit->onArriveTarget();
			}
		}										 
	}
}

int MechaLocoMotion::getCurAngle()
{
	int anglereal = (int)m_RotateYaw % 360;
	if (anglereal < 0)
		anglereal = 360 + anglereal;
	else if (anglereal >= 360)
		anglereal -= 360;

	return anglereal;
}

void MechaLocoMotion::setMotionFromServer(int motiontype, float motionparam)
{
	m_BeBlocked = motiontype==MECHA_MOTION_BLOCKED;

	if(m_MoveType == MECHA_MOVE_SLIDE_INC || m_MoveType == MECHA_MOVE_SLIDE_DEC) m_CurMoveLen = int(motionparam);
	else if(m_MoveType == MECHA_MOVE_ROTATE) m_RotateYaw = motionparam;
}


void MechaLocoMotion::sendMechaMotionToClient(int motiontype)
{
	PB_MechaMotionHC mechaMotionHC;
	mechaMotionHC.set_objid(getOwnerActor()->getObjId());
	mechaMotionHC.set_motiontype(motiontype);

	if (m_MoveType == MECHA_MOVE_SLIDE_INC || m_MoveType == MECHA_MOVE_SLIDE_DEC) mechaMotionHC.set_motionparam((float)m_CurMoveLen);
	else if (m_MoveType == MECHA_MOVE_ROTATE) mechaMotionHC.set_motionparam(m_RotateYaw);
	else return;

	m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_MECHA_MOTION_HC, mechaMotionHC, getOwnerActor());
}

/*
WCoord MechaLocoMotion::updatePushedObjects(const CollideAABB &actorbox, const CollideAABB &mechabox)
{
	WCoord motion(0, 0, 0);

	if(actorbox.minY() < mechabox.minY())
	{
		if(actorbox.maxY() > mechabox.minY()) motion.y = mechabox.minY() - actorbox.maxY();
	}
	else if(actorbox.minY() > mechabox.minY())
	{
		if(actorbox.minY() < mechabox.maxY()) motion.y = mechabox.maxY() - actorbox.minY();
	}

	if(actorbox.minX() < mechabox.minX())
	{
		if(actorbox.maxX() > mechabox.minX()) motion.x = mechabox.minX() - actorbox.maxX();
	}
	else if(actorbox.minX() > mechabox.minX())
	{
		if(actorbox.minX() < mechabox.maxX()) motion.x = mechabox.maxX() - actorbox.minX();
	}

	if(actorbox.minZ() < mechabox.minZ())
	{
		if(actorbox.maxZ() > mechabox.minZ()) motion.z = mechabox.minZ() - actorbox.maxZ();
	}
	else if(actorbox.minZ() > mechabox.minZ())
	{
		if(actorbox.minZ() < mechabox.maxZ()) motion.z = mechabox.maxZ() - actorbox.minZ();
	}

	int ax = Abs(motion.x);
	int ay = Abs(motion.y);
	int az = Abs(motion.z);
	int minmotion = Min(ax, Min(ay, az));
	if(minmotion == ay)
	{
		//if(motion.y != 0) motion.y = 10*motion.y/Abs(motion.y);
		motion.x = motion.z = 0;
	}
	else if(minmotion == ax)
	{
		//if(motion.x != 0) motion.x = 10*motion.x/Abs(motion.x);
		motion.y = motion.z = 0;
	}
	else
	{
		//if(motion.z != 0) motion.z = 10*motion.z/Abs(motion.z);
		motion.x = motion.y = 0;
	}

	return motion;
}*/

void MechaLocoMotion::tickSlide()
{
	ActorMechaUnit *unit = static_cast<ActorMechaUnit *>(getOwnerActor());
	m_LastMoveLen = m_CurMoveLen;
	if(unit->needClear()) return;

	if(!m_pWorld->isRemoteMode() || !m_BeBlocked)
	{
		m_CurMoveLen += m_Speed;
		if(m_CurMoveLen > m_MaxMoveLen) m_CurMoveLen = m_MaxMoveLen;
	}

	WCoord d = m_ToPos - m_FromPos;
	WCoord curpos;
	curpos.x = m_CurMoveLen*d.x/m_MaxMoveLen + m_FromPos.x;
	curpos.y = m_CurMoveLen*d.y/m_MaxMoveLen + m_FromPos.y;
	curpos.z = m_CurMoveLen*d.z/m_MaxMoveLen + m_FromPos.z;

	if(!m_pWorld->isRemoteMode())
	{
		bool iscollide = false;
		WCoord blocksize(BLOCK_SIZE-1, BLOCK_SIZE-1, BLOCK_SIZE-1);
		if(CoordDivBlock(curpos) != CoordDivBlock(m_Position) || CoordDivBlock(curpos+blocksize) != CoordDivBlock(m_Position+blocksize)) //穿越方块边界
		{
			if(d.x != 0)
			{
				int x = unit->checkBlockCollision(m_Position, curpos, 0, iscollide);
				m_CurMoveLen = (x-m_FromPos.x)*m_MaxMoveLen / d.x;
			}
			else if(d.y != 0)
			{
				int y = unit->checkBlockCollision(m_Position, curpos, 1, iscollide);
				m_CurMoveLen = (y-m_FromPos.y)*m_MaxMoveLen / d.y;
			}
			else if(d.z != 0)
			{
				int z = unit->checkBlockCollision(m_Position, curpos, 2, iscollide);
				m_CurMoveLen = (z-m_FromPos.z)*m_MaxMoveLen / d.z;
			}

			m_Position.x = m_CurMoveLen*d.x/m_MaxMoveLen + m_FromPos.x;
			m_Position.y = m_CurMoveLen*d.y/m_MaxMoveLen + m_FromPos.y;
			m_Position.z = m_CurMoveLen*d.z/m_MaxMoveLen + m_FromPos.z;
		}
		else m_Position = curpos;

		if(iscollide && !m_BeBlocked)
		{
			m_BeBlocked = true;
			sendMechaMotionToClient(MECHA_MOTION_BLOCKED);
		}
		else if(!iscollide && m_BeBlocked)
		{
			m_BeBlocked = false;
			sendMechaMotionToClient(MECHA_MOTION_CONTINUE);
		}

		if(m_CurMoveLen == m_MaxMoveLen)
		{
			//if(!unit->continueMove()) 
			unit->onArriveTarget();
		}
	}
	else m_Position = curpos;

	float dlen = float(m_CurMoveLen - m_LastMoveLen);
	m_Motion.x = dlen*d.x/m_MaxMoveLen;
	m_Motion.y = dlen*d.y/m_MaxMoveLen;
	m_Motion.z = dlen*d.z/m_MaxMoveLen;

	const float MIN_MOTIONY = 5.0f;
	if(m_Motion.y>=0 && m_Motion.y<MIN_MOTIONY)
	{
		m_Motion.y = MIN_MOTIONY;
	}
}
