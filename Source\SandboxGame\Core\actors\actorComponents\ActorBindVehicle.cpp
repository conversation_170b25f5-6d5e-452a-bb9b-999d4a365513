#include "ActorBindVehicle.h"
#include "ClientActor.h"
#include "ActorVehicleAssemble.h"
#include "ActorLocoMotion.h"
#include "world.h"
#include "GameNetManager.h"
#include "ClientActorManager.h"
#include "OgreBlock.h"
#include <assert.h>
#include "SandboxIdDef.h"
IMPLEMENT_COMPONENTCLASS(ActorBindVehicle)

ActorBindVehicle::ActorBindVehicle():m_VehicleID(0),m_VehicleAttachPos(0, 0, 0)
{

}

bool ActorBindVehicle::getInVehiclePos(Rainbow::Vector3f &pos)
{
	if (!GetOwner()) return false;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return false;
	World* pWorld = m_owner->getWorld();
	if(!pWorld) return false;

	ActorVehicleAssemble* vehicle = getBindVehicle();
	if (vehicle)
	{
		Block block = vehicle->getBlock(m_VehicleAttachPos);
		if (block.isEmpty() == false && block.getResID() == BLOCK_CLAW)
		{
			if (m_owner->getObjType() >= OBJ_TYPE_FURNACE)
			{
				WCoord center = m_VehicleAttachPos*BLOCK_SIZE + WCoord(0, BLOCK_SIZE/2, 0);
				pos = vehicle->convertWcoord(Rainbow::Vector3f((float)center.x, (float)center.y, (float)center.z), true).toVector3();
			}
			else
			{
				pos = vehicle->convertWcoord(m_VehicleAttachPos).toVector3();
			}
			Rainbow::Vector3f dir; 
			int groupId = 0;
			dir = vehicle->convertRealDir(m_VehicleAttachPos, block.getData()&7, groupId);
			CollideAABB box;
			m_owner->getCollideBox(box);
			dir *= (float)box.dim.x;
			pos = pos + dir; 
			return true;
		}
	}
	return false;
}

bool ActorBindVehicle::getInVehicleQuat(Rainbow::Quaternionf &quat)
{
	return false;
}

void ActorBindVehicle::updatePosByVehicle()
{
	//刷新玩家的位置
	if (m_VehicleID)
	{
		if (!GetOwner()) return ;
		ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
		if (!m_owner) return ;
		Rainbow::Vector3f pos;
		if (getInVehiclePos(pos) && pos.y > -10000000 )
			m_owner->getLocoMotion()->setPosition((int)pos.x, (int)pos.y, (int)pos.z);
	}	
}

void ActorBindVehicle::sendBroadCastBindMsg(long long targetObjId, long long vehicleObjId, const WCoord& pos)
{
	PB_VehicleBindActorHC vehicleAssembleBindHC;
	vehicleAssembleBindHC.set_vehicleobjid(vehicleObjId);
	vehicleAssembleBindHC.set_bindobjid(targetObjId);

	PB_Vector3* blockpos = vehicleAssembleBindHC.mutable_blockpos();
	blockpos->set_x(pos.x);
	blockpos->set_y(pos.y);
	blockpos->set_z(pos.z);
	GetGameNetManagerPtr()->sendBroadCast(PB_VEHICLE_BIND_ACTOR_HC, vehicleAssembleBindHC);
}

void ActorBindVehicle::Bind(long long vehicleObjId, const WCoord &attachpos, bool sendMsg)
{
	if (!GetOwner()) return ;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return ;
	m_VehicleID = vehicleObjId;	
	m_VehicleAttachPos = attachpos;
	if(sendMsg){
		sendBroadCastBindMsg(m_owner->getObjId(), m_VehicleID, m_VehicleAttachPos);
	}	
}
void ActorBindVehicle::UnBind(bool sendMsg)
{
	if (!GetOwner()) return ;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return ;
	m_VehicleID = 0;
	m_VehicleAttachPos = WCoord(0,0,0);
	if(sendMsg){
		sendBroadCastBindMsg(m_owner->getObjId(), m_VehicleID, m_VehicleAttachPos);
	}	
}


Block ActorBindVehicle::getVehicleAttachPosBlock()
{

	ActorVehicleAssemble *vehicle = getBindVehicle();
	if (vehicle)
	{
		return vehicle->getBlock(m_VehicleAttachPos);
	}

	return Block();
}

void ActorBindVehicle::setBindRot(bool &setbindrot, Rainbow::Vector3f &pos, Rainbow::Quaternionf &quat)
{
	if (m_VehicleID)
	{
		ActorVehicleAssemble* vehicle = getBindVehicle();
		if (vehicle)
		{
			getInVehiclePos(pos);
			setbindrot = getInVehicleQuat(quat);
		}
	}	
}

ActorVehicleAssemble* ActorBindVehicle::getBindVehicle()
{
	if (m_VehicleID)
	{
		if (!GetOwner()) return NULL;
		ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
		if (!m_owner) return NULL;
		World* pWorld = m_owner->getWorld();	
		ClientActor *actor = pWorld->getActorMgr()->ToCastMgr()->findActorByWID(m_VehicleID);
		if (actor && actor->getObjType() == OBJ_TYPE_VEHICLE){
			ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble *>(actor);
			return vehicle;
		}	
	}
	return NULL;
}

IMPLEMENT_COMPONENTCLASS(PlayerBindVehicle)

PlayerBindVehicle::PlayerBindVehicle():ActorBindVehicle()
{

}


bool PlayerBindVehicle::getInVehiclePos(Rainbow::Vector3f &pos)
{
	ActorVehicleAssemble *vehicle = getBindVehicle();
	if (vehicle)
	{
		Block block = vehicle->getBlock(m_VehicleAttachPos);
		if (!GetOwner()) return NULL;
		ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
		if (!m_owner) return NULL;
		if (block.isEmpty() == false && (m_owner->isSleeping() || m_owner->isRestInBed()))
		{
			WCoord playerpos = m_VehicleAttachPos * BLOCK_SIZE + WCoord(0, 50, 0);
			WCoord pos_;
			pos_ = vehicle->convertWcoord(Rainbow::Vector3f((float)playerpos.x, (float)playerpos.y, (float)playerpos.z), true);
			pos = pos_.toVector3();
			return true;
		}
	}
	return false;
}

bool PlayerBindVehicle::getInVehicleQuat(Rainbow::Quaternionf &quat)
{
	ActorVehicleAssemble *vehicle = getBindVehicle();
	if (vehicle)
	{
		Block block = vehicle->getBlock(m_VehicleAttachPos);
		if (!GetOwner()) return NULL;
		ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
		if (!m_owner) return NULL;
		if (block.isEmpty() == false && (m_owner->isSleeping() || m_owner->isRestInBed()))
		{
			int blockdata = block.getData();
			int dir = blockdata & 3;
			float dirs[4] = { 90.0f, -90.0f, 0.0f, 180.0f };
			quat=Rainbow::AxisAngleToQuaternionf(Rainbow::Vector3f(0.0f, 1.0f, 0.0f), Rainbow::Deg2Rad(dirs[dir]));
			// 新引擎的四元数乘法需要交换顺序
			quat = vehicle->convertRot(m_VehicleAttachPos) * quat;
			return true;
		}
	}
	return false;
}

