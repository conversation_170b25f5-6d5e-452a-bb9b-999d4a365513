#include "container_pipeline.h"
#include "ClientActorManager.h"
#include "ClientPipelineActor.h"
#include "BlockMaterialMgr.h"
#include "container_detectionpipe.h"
#include "special_blockid.h"
#include "world.h"
#include "ActorManager.h"
#include "chunk.h"

bool ContainerPipeline::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerPiplineSave*>(srcdata);
	if (src == NULL)
	{
		return false;
	}
	ContainerElectricElement::load(src->basedata());
	m_connectDir = src->connectDir();
	m_Dir = src->dir();
	m_transferDir = src->transferDir();
	m_curTranDir = src->endDir();
	m_tricks = src->tick();
	m_transferObjId = src->pipleActor();
	m_itemId = src->itemid();
	return true;
}

flatbuffers::Offset<FBSave::ContainerPiplineSave> ContainerPipeline::savePipeLine(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveElectricContainer(builder);
	return FBSave::CreateContainerPiplineSave(builder, basedata, m_connectDir, m_transferDir, m_tricks, m_Dir, m_curTranDir, m_transferObjId, m_itemId);
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerPipeline::save(SAVE_BUFFER_BUILDER& builder)
{
	auto actor = savePipeLine(builder);
	return FBSave::CreateChunkContainer(builder, getUnionType(), actor.Union());
}

bool ContainerPipeline::canBeginTransfer()
{
	if (m_transferObjId > 0)
	{
		return false;
	}
	return true;
}

void ContainerPipeline::beginTransfer(long long objid)
{

	//assert(endDir >= 0 && endDir < 6);
	//m_centerPostion = BlockCenterCoord(m_BlockPos);
	//m_startPostion = m_centerPostion + g_DirectionCoord[m_Dir] * -BLOCK_SIZE / 2;
	//m_endPostion = m_endPostion + g_DirectionCoord[endDir] * BLOCK_SIZE / 2;
	if (m_World == NULL)
	{
		assert(0);
		return;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_World->getActorMgr());
	if (!actorMgr) return;
	ClientPipleLineActor* obj = dynamic_cast<ClientPipleLineActor*>(actorMgr->findActorByWID(objid));
	if (obj == NULL)
	{
		return;
	}
	m_itemId = obj->getItemID();
	m_curTranDir = -1;
	checkDirConnect();
	checkTransferDir();
	m_tricks = 0;
	m_transferObjId = objid; 
	//保存chunk数据
	Chunk* pchunk = m_World->getChunk(m_BlockPos);
	if (pchunk)
	{
		pchunk->m_Dirty = true;
	}
	m_World->markBlockForUpdate(m_BlockPos);
}

bool ContainerPipeline::updateItemTransferMotion(WCoord& postion, IClientActor* actor)
{
	if (m_World == NULL)
	{
		return false;
	}
	if (m_tricks < 0)
	{
		return false;
	}
	if (actor == NULL)
	{
		return false;
	}
	if (m_transferObjId != actor->getObjId())
	{
		return false;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_World->getActorMgr());
	if (!actorMgr) return false;
	assert(m_Dir < 6);
	assert(m_curTranDir < 6);
	int reverseDir = ReverseDirection(m_Dir);
	WCoord centerPostion = BlockCenterCoord(m_BlockPos);
	WCoord startPostion = centerPostion + g_DirectionCoord[reverseDir] * -(BLOCK_SIZE - 1) / 2;
	WCoord endPostion;
	if (m_curTranDir >= 0)
	{
		endPostion = centerPostion + g_DirectionCoord[m_curTranDir] * (BLOCK_SIZE - 1) / 2;
	}
	else
	{
		endPostion = centerPostion;
	}
	if (m_tricks < 4)
	{
		postion = startPostion + (g_DirectionCoord[reverseDir] * (BLOCK_SIZE / 2) * (m_tricks + 1)  / 4);
		m_tricks++;
	}
	else
	{
		if (m_curTranDir >= 0)
		{
			postion = centerPostion + (g_DirectionCoord[m_curTranDir] * (BLOCK_SIZE / 2) * (m_tricks - 3) / 4) - g_DirectionCoord[m_curTranDir];
			m_tricks++;
		}
		else
		{
			checkDirConnect();
			checkTransferDir();
			postion = centerPostion;
		}

		if (m_curTranDir >= 0)
		{
			WCoord nextBlockPos = NeighborCoord(m_BlockPos, m_curTranDir);
			int blockId = m_World->getBlockID(nextBlockPos);
			if (IsPipellineTransferBlock(blockId))
			{
				ContainerDetectionPipeline* container = dynamic_cast<ContainerDetectionPipeline*>(m_World->getContainerMgr()->getContainer(nextBlockPos));
				if (container)
				{
					IClientActor* actor = actorMgr->findActorByWID(m_transferObjId);
					int transportItemid = actor->getDefID();
					if (!container->m_Grids[0].isEmpty())
					{
						int checkItemid = container->m_Grids[0].getItemID();
						if (checkItemid != transportItemid)
						{
							postion = centerPostion;
							m_curTranDir = -1;
							m_tricks = 4;
						}
					}
				}
			}
		}
	}
	std::function<void()> BackCenter = [this, &postion, &centerPostion]() {
		postion = centerPostion;
		m_curTranDir = -1;
		m_tricks = 4;
	};
	//如果tick > 8并且下面有管道连接
	if (m_tricks >= 8 && m_curTranDir >= 0)
	{
		WCoord nextBlockPos = NeighborCoord(m_BlockPos, m_curTranDir);
		int blockId = m_World->getBlockID(nextBlockPos);
		if (IsPipellineTransferBlock(blockId))
		{
			//让它到下一个方块
			postion += g_DirectionCoord[m_curTranDir] * 2;
			BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockId);
			bool succee = false;
			if (material)
			{
				succee = material->beginTransfer(m_World, nextBlockPos, m_transferObjId, m_curTranDir);
			}
			if (!succee)
			{
				BackCenter();
			}
			else
			{
				endTransfer();
			}
		}
		else
		{
			BackCenter();
		}
	}
	else if (m_tricks >= 8)
	{
		BackCenter();
	}
	return true;
}

void ContainerPipeline::endTransfer()
{
	m_tricks = -1;
	m_curTranDir = -1;
	m_transferObjId = -1;
	m_itemId = -1;
	//int reverseDir = ReverseDirection(m_Dir);
	//保存chunk数据
	Chunk* pchunk = m_World->getChunk(m_BlockPos);
	if (pchunk)
	{
		pchunk->m_Dirty = true;
	}
	m_World->markBlockForUpdate(m_BlockPos);
}

void ContainerPipeline::enterWorld(World* pworld)
{
	ContainerElectricElement::enterWorld(pworld);

	registerUpdateTick();
}

void ContainerPipeline::updateTick()
{
	ContainerElectricElement::updateTick();
	if (m_checkCount >= m_checkStep)
	{
		if (m_transferObjId > 0)
		{
			ClientActor* actor = static_cast<ActorManager*>(m_World->getActorMgr())->findActorByWID(m_transferObjId);
			if (!actor)
			{
				endTransfer();
			}
		}
		m_checkCount = 0;
	}
	m_checkCount++;
}
	

void ContainerPipeline::initTransferDir()
{
	bool init = true;
	for (int i = 0; i < 6; i++)
	{
		if (i != m_Dir)
		{
			if (isDirConnect(i) && !isDirHasTransfered(i))
			{
				init = false;
				break;
			}
		}
	}
	if (init)
	{
		m_transferDir = 0;
	}
}

bool ContainerPipeline::isDirConnect(int dir)
{
	return DIR_BIT_GET(m_connectDir, dir);
}

void ContainerPipeline::setDirConnect(int dir, bool connected)
{
	DIR_BIT_RESET(m_connectDir, dir);
	if (connected)
	{
		DIR_BIT_SET(m_connectDir, dir);
	}
}

void ContainerPipeline::setDirHasTansfered(int dir, bool transfered)
{
	DIR_BIT_RESET(m_transferDir, dir);
	if (transfered)
	{
		DIR_BIT_SET(m_transferDir, dir);
	}
}

bool ContainerPipeline::isDirHasTransfered(int dir)
{
	return DIR_BIT_GET(m_transferDir, dir);
}

bool ContainerPipeline::isShouldTransfered(int dir)
{
	return isDirConnect(dir) && !isDirHasTransfered(dir);
}


int ContainerPipeline::onInsertItem(const BackPackGrid& grid, int num, int params)
{
	int ret = 0;
	if (m_transferObjId == -1)
	{
		if (m_itemId > 0)
		{
			assert(0);
		}
		{
			BackPackGrid cgrid;
			InsertItemIntoArray(this, &cgrid, 1, grid, 1);
			ClientPipleLineActor* actor = SANDBOX_NEW(ClientPipleLineActor, cgrid);
			ActorLocoMotion* locmove = actor->getLocoMotion();

			WCoord centerPostion = BlockCenterCoord(m_BlockPos);
			int reverseDir = ReverseDirection(m_Dir);
			WCoord startPostion = centerPostion + g_DirectionCoord[reverseDir] * -(BLOCK_SIZE - 1) / 2;
			locmove->gotoPosition(startPostion, 0, 0);

			m_World->getActorMgr()->spawnActor(actor);
			beginTransfer(actor->getObjId());
			ret = 1;
		}		
	}

	return ret;
}

void ContainerPipeline::checkDirConnect()
{
	if (m_World == NULL)
	{
		return;
	}
	WCoord curPos = m_BlockPos;
	int blockid = 0;
	//int reverseDir = ReverseDirection(m_Dir);
	BackPackGrid grid;
	if (m_itemId < 0)
	{
		grid.setItem(0, 1);
	}
	else
	{
		grid.setItem(m_itemId, 1);
	}
	for (int i = 0; i < 6; i++)
	{
		if (m_Dir != i)
		{
			curPos = NeighborCoord(m_BlockPos, i);
			blockid = m_World->getBlockID(curPos);
			if (IsPipellineTransferBlock(blockid))
			{
				BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockid);
				if (material && material->canConnectToDir(m_World, curPos, i, grid))
				{
					setDirConnect(i, true);
				}
				else
				{
					setDirConnect(i, false);
				}
			}
			else
			{
				setDirConnect(i, false);
			}
		}
	}
}

bool ContainerPipeline::isTransfering()
{
	return m_tricks >= 0 && m_transferObjId > 0;
}

void ContainerPipeline::checkTransferDir()
{
	assert(m_curTranDir < 0 || m_curTranDir >= 6);
	initTransferDir();
	if (m_curTranDir < 0 || m_curTranDir >= 6)
	{
		//这次要传送到哪个方向
		int dir[5] = { 0 };
		int reverseDir = ReverseDirection(m_Dir);
		//放置方向第一
		dir[0] = reverseDir;
		if (reverseDir == DIR_NEG_Z || reverseDir == DIR_POS_Z)
		{
			dir[1] = RotateDir90HeadingAngle(reverseDir);
			dir[2] = RotateDir90PitchAngle(reverseDir);
			dir[3] = RotateReverseDir90HeadingAngle(reverseDir);
			dir[4] = RotateReverseDir90PitchAngle(reverseDir);
		}
		else if (reverseDir == DIR_NEG_X || reverseDir == DIR_POS_X)
		{
			dir[1] = RotateDir90HeadingAngle(reverseDir);
			dir[2] = RotateReverseDir90RawAngle(reverseDir);
			dir[3] = RotateReverseDir90HeadingAngle(reverseDir);
			dir[4] = RotateDir90RawAngle(reverseDir);
		}
		else if (reverseDir == DIR_NEG_Y || reverseDir == DIR_POS_Y)
		{
			dir[1] = RotateReverseDir90RawAngle(reverseDir);
			dir[2] = RotateReverseDir90PitchAngle(reverseDir);
			dir[3] = RotateDir90RawAngle(reverseDir);
			dir[4] = RotateDir90PitchAngle(reverseDir);
		}
		int endDir = -1;
		for (int i = 0; i < 5; i++)
		{
			if (isShouldTransfered(dir[i]))
			{
				endDir = dir[i];
				break;
			}
		}
		if (endDir >= 0 && endDir < 6)
		{
			setDirHasTansfered(endDir, true);
		}
		m_curTranDir = endDir;
	}
}

void ContainerPipeline::checkCurTransferDir()
{
	if (m_curTranDir >= 0 && m_curTranDir < 6)
	{
		if (!isDirConnect(m_curTranDir))
		{
			m_curTranDir = -1;
		}
	}
}

bool ContainerPipeline::outputItems()
{
	int dir = ReverseDirection(m_Dir);
	WCoord pos = NeighborCoord(m_BlockPos, dir);
	int blockid = m_World->getBlockID(pos);	

	WorldContainer* container = m_World->getContainerMgr()->getContainer(pos);
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_World->getActorMgr());
	if (!actorMgr) return false;
	if (container)
	{		
		ClientItem* item = dynamic_cast<ClientItem*>(actorMgr->findActorByWID(m_transferObjId));
		if (item)
		{
			int n = container->onInsertItem(item->m_ItemData, item->m_ItemData.getNum(), ReverseDirection(dir));
			if (n > 0)
			{					
				item->m_ItemData.clear();
				item->setNeedClear();
				return true;
			}
		}
	}
	return false;
}

void ContainerPipeline::clear()
{
	endTransfer();
}