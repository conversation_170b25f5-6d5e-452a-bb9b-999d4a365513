#ifndef __CLIENTACTOR_COCONUT_H__
#define __CLIENTACTOR_COCONUT_H__
#include <vector>
#include "ClientActorProjectile.h"
//椰子
class ClientActorCoconut : public ClientActorProjectile { //tolua_exports
public:
	static ClientActorCoconut* shootCoconueAuto(int itemid, World* pworld, const WCoord& pos, const int& dir, float speed, float deviation);
	//tolua_begin
	ClientActorCoconut();
	virtual ~ClientActorCoconut();
	virtual void init(int itemid, ClientActor* shooter = nullptr);

	virtual void onImpactWithActor(ClientActor* actor, const std::string& partname);
	virtual void onImpactWithBlock(const WCoord* blockpos, int face);
	virtual void onCollideWithPlayer(ClientActor* actor);
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata, int version) override;
	virtual int getObjType() const override
	{
		return OBJ_TYPE_COCONUT_PRO;
	}
	//tolua_end
private:
	void	doAttack(ClientActor* actor);
	void	doImpactActor(ClientActor* actor);
	int		m_dirBlock;
}; //tolua_exports

#endif //__CLIENTACTOR_COCONUT_H__

