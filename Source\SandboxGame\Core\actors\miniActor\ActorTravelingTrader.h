﻿#ifndef __ACTOR_TRAVELING_TRADER_H__
#define __ACTOR_TRAVELING_TRADER_H__

#include "ClientMob.h"
#include "SandboxGame.h"

class TravelingTraderInfo;

class EXPORT_SANDBOXGAME ActorTravelingTrader;
class ActorTravelingTrader : public ClientMob //tolua_exports
{//tolua_exports
	DECLARE_SCENEOBJECTCLASS(ActorTravelingTrader)
public:
	//tolua_begin
	ActorTravelingTrader();
	virtual ~ActorTravelingTrader();
	virtual int getObjType() const override;
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata, int version) override;
	virtual bool needSaveInChunk() override { return false; }
	virtual bool supportSaveToPB() override { return false; }
	virtual void tick() override;
	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld(bool keep_inchunk) override;
	virtual bool attackedFrom(OneAttackData& atkdata, ClientActor* attacker) override;
	virtual void onDie() override;
	//坐 相关
	void sitInChair(const WCoord blockpos);
	void sitInBackround();
	void standUp();
	//床 相关
	virtual WCoord getBedBindPos() override;
	virtual void setBedBindPos(WCoord pos) override;
	bool IsBindedBed(int blockPosX, int blockPosY, int blockPosZ);

	void ResetTimePoint(); //刷新游商的上次记录的时间点
	void ResetHousingLv(); //重置居住等级为0
	bool IsInHomeReal(); //是否绑定了床位，且在家中
	unsigned char GetHungerState(); //获取饥饿状态
	void SetHungerState(unsigned char state, bool isDo = false); //设置饥饿状态
	int GetHoursFromeLastDo(); //上次切换时，持续的时间(h)
	bool GiveFood(ClientPlayer* player); //给食物
	int GetCurBiomeId(int& posX, int& posY, int& posZ); //获取当前的生态ID
	void SetModelStyle(int id); //设置风格
	WCoord GetHomePos(); //获取绑定的家(复活点)的位置
	WCoord GetLastInHomePos(); //获取上次在家时的位置
	void EnableSyncPosition();
	//tolua_end

private:
	TravelingTraderInfo& GetInfo(bool& ret);
	int FindBlockNumberByType(int posx, int posy, int posz, int range, int editType);

	bool m_WaitModelLoadingForSetModelStyle{ false };
	int m_styleId_fromLoad{ -1 };
	int m_styleId{ -1 };

	bool m_sleeping_fromLoad{ false };
	bool m_bodyShow_fromLoad{ false };

public:
	static ActorTravelingTrader* Get(World* pworld);
	static ActorTravelingTrader* current_ActorTravelingTrader;
};//tolua_exports
#endif