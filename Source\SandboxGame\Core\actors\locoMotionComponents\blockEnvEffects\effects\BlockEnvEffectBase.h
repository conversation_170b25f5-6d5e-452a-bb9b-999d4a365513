
#ifndef BLOCK_ENV_EFFECT_BASE_H_
#define BLOCK_ENV_EFFECT_BASE_H_

#include "SandboxMacros.h"
//#include "SandboxContext.h"
//#include "ClientActor.h"

class ClientActor;
class BlockEnvEffectBase
{
public:
	virtual ~BlockEnvEffectBase() {}

	virtual void executeEffect(ClientActor* pActor) = 0;//const 
	void SetMoveBlockNum(int num) { m_moveBlockNum = num; }
	int GetMoveBlockNum() { return m_moveBlockNum; }
	void SetStatus(bool status) { m_status = status; }
	bool GetStatus() { return m_status; }
	void SetFlowmotion(const Rainbow::Vector3f& v) { m_flowmotion = v; }
	Rainbow::Vector3f GetFlowmotion() { return m_flowmotion; }
private:
	int m_moveBlockNum = 0;
	bool m_status = false;
	Rainbow::Vector3f m_flowmotion;

};


#endif