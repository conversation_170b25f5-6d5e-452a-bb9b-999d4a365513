#include "PhysicsLocoMotionComponent.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "BlockGeom.h"
#include "special_blockid.h"
#include "DefManagerProxy.h"
#include "WorldManager.h"
#include "OgrePhysXManager.h"

#include "BlockScene.h"
#include "BaseClass/EventDispatcher.h"

using namespace MINIW;
using namespace Rainbow;
IMPLEMENT_COMPONENTCLASS(PhysicsLocoMotionComponent)

PhysicsLocoMotionComponent::PhysicsLocoMotionComponent() :  m_PhysJoint(nullptr)
{
}

void PhysicsLocoMotionComponent::attachPhysActor(int id)
{
	//if (m_pWorld->isRemoteMode()) return;
	if(g_WorldMgr == NULL || g_WorldMgr->m_SurviveGameConfig  == NULL || m_pWorld == NULL || m_pWorld->m_PhysScene == NULL)
	{
		return;
	}

	if(m_PhysActor == NULL)
	{
		Rainbow::Vector3f pos = m_Position.toVector3();
		float mass = g_WorldMgr->m_SurviveGameConfig->vehicleconfig.mass_component*g_WorldMgr->m_SurviveGameConfig->vehicleconfig.mass_scale;
		const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(id);
		if (physicsActorDef && physicsActorDef->Mass > 0)
			mass = physicsActorDef->Mass * g_WorldMgr->m_SurviveGameConfig->vehicleconfig.mass_scale;
		m_PhysActor = m_pWorld->m_PhysScene->AddRigidDynamicActor(pos, Rainbow::Quaternionf::identity, Rainbow::Vector3f(50, 50, 50), 0.8f, 0.8f, 0.3f, mass, false, NULL, NULL, true);
	}
}

void PhysicsLocoMotionComponent::detachPhysActor()
{
	//if (m_pWorld->isRemoteMode()) return;
	if(m_PhysActor)
	{
		m_pWorld->m_PhysScene->DeleteRigidActor(m_PhysActor);
		m_PhysActor = NULL;
	}
}

void PhysicsLocoMotionComponent::attachPhysJoint(Rainbow::RigidDynamicActor *actor)
{
	//if (m_pWorld->isRemoteMode()) return;
	//PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *>(player->getLocoMotion());
	Rainbow::Quaternionf rot;
	//rot.setEulerAngle(0.0f, 0.0f, 0.0f);
	rot = AngleEulerToQuaternionf(Vector3f(0.0f, 0.0f, 0.0f));
	//int offsetY = (loc->m_BoundHeight - m_BoundHeight) / 2 - 5;
	//m_PhysJoint = m_pWorld->m_PhysScene->CreateFixJoint(loc->m_PhysActor, Vector3f(0, 0, 0), rot, m_PhysActor, Vector3f(0, offsetY, 130), Quaternionf::identity);
	//m_PhysJoint = m_pWorld->m_PhysScene->CreateD6Joint(actor, Vector3f(120, -50, 120), rot, static_cast<RigidDynamicActor *>(m_PhysActor), Vector3f(0, 0, 0), Quaternionf::identity);
}

void PhysicsLocoMotionComponent::detachPhysJoint()
{
	//if (m_pWorld->isRemoteMode()) return;
	if (m_PhysJoint)
	{
		m_pWorld->m_PhysScene->DeleteJoint(m_PhysJoint);
		m_PhysJoint = NULL;
	}
}
