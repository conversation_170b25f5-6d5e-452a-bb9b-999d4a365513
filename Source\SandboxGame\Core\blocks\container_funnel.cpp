
#include "IClientActor.h"
#include "container_funnel.h"
//#include "GameEvent.h"
#include "world.h"
#include "BlockFunnel.h"
#include "ClientItem.h"
#include "DefManagerProxy.h"


#include "VehicleWorld.h"
#include "ActorCSProto.h"
#include "ActorVehicleAssemble.h"

#define MAX_FUNNEL_GRIDS  5

EXPORT_SANDBOXENGINE extern int CheckInsertItemIntoArray(BaseContainer* container, BackPackGrid* gridarray, int arraylen, const BackPackGrid& grid);

WorldFunnelContainer::WorldFunnelContainer() : WorldContainer(WCoord(0,0,0), FUNNEL_START_INDEX), m_TransferTicks(-1), m_UpsideDown(false)
{
	m_Grids.resize(MAX_FUNNEL_GRIDS);
	checkItemTick = 0;
	for(size_t i=0; i<m_Grids.size(); i++) m_Grids[i].reset(m_BaseIndex+i);
	m_NeedTick = true;
}

WorldFunnelContainer::WorldFunnelContainer(const WCoord &blockpos, bool upsidedown) : WorldContainer(blockpos, FUNNEL_START_INDEX), m_TransferTicks(-1), m_UpsideDown(upsidedown)
{
	m_Grids.resize(MAX_FUNNEL_GRIDS);
	checkItemTick = 0;
	for(size_t i=0; i<m_Grids.size(); i++) m_Grids[i].reset(m_BaseIndex+i);
	m_NeedTick = true;
}

WorldFunnelContainer::~WorldFunnelContainer()
{
}

int WorldFunnelContainer::getObjType() const
{
	return OBJ_TYPE_FUNNEL;
}

void WorldFunnelContainer::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);

	registerUpdateTick();
}

void WorldFunnelContainer::updateTick()
{
	if(m_World->isRemoteMode()) return;

	m_TransferTicks--;
	if(m_TransferTicks > 0) return;
	m_TransferTicks = 0;
	m_BlockPosEx = m_BlockPos;
	if (m_vehicleWorld)
	{
		auto pAssemble = static_cast<VehicleWorld*>(m_vehicleWorld)->getActorVehicleAssemble();
		m_BlockPosEx = pAssemble->convertWcoord(m_BlockPos);

		m_BlockPosEx.x = m_BlockPosEx.x >= 0 ? (int)ceil((float)m_BlockPosEx.x / 100.f) : (int)floor((float)m_BlockPosEx.x / 100.f);
		m_BlockPosEx.y = m_BlockPosEx.y >= 0 ? (int)ceil((float)m_BlockPosEx.y / 100.f) : (int)floor((float)m_BlockPosEx.y / 100.f);
		m_BlockPosEx.z = m_BlockPosEx.z >= 0 ? (int)ceil((float)m_BlockPosEx.z / 100.f) : (int)floor((float)m_BlockPosEx.z / 100.f);
		if (BlockFunnel::isPowered(m_vehicleWorld->getBlockData(m_BlockPos))) return;
	}
	else
	{
		if (BlockFunnel::isPowered(m_World->getBlockData(m_BlockPos))) return;
	}

	bool r1 = outputItems();
	bool r2 = inputItems();
	if(r1 || r2)
	{
		m_TransferTicks = 8;
	}
}

bool WorldFunnelContainer::outputItems()
{
	int dir = m_World->getBlockData(m_BlockPos) & 7;
	if (m_vehicleWorld)
	{
		dir = m_vehicleWorld->getBlockData(m_BlockPos) & 7;
	}
	if(m_UpsideDown && dir==DIR_NEG_Y) dir = ReverseDirection(dir);

	WCoord pos = NeighborCoord(m_BlockPos, dir);
	WorldContainer *container = m_World->getContainerMgr()->getContainer(pos);
	if (m_vehicleWorld)
	{
		container = m_vehicleWorld->getContainerMgr()->getContainer(pos);
	}
	if(container)
	{
		for(size_t i=0; i<m_Grids.size(); i++)
		{
			if(!m_Grids[i].isEmpty())
			{
				int n = container->onInsertItem(m_Grids[i], 1, ReverseDirection(dir));
				if(n < 0) continue;
				if(n == 0) break;

				onSubtractItem(&m_Grids[i], 1);
				return true;
			}
		}
	}

	/*
	CollideAABB box;
	box.setPoints(pos*BLOCK_SIZE, WCoord(pos.x+1, pos.y+1, pos.z+1)*BLOCK_SIZE);
	std::vector<IClientActor *>actors;
	m_World->getActorsInBox(actors, box);

	for(size_t j=0; j<actors.size(); j++)
	{
		IClientActor *actor = actors[j];
		
	}*/
	return false;
}

bool WorldFunnelContainer::inputItems()
{
	int dir = m_UpsideDown ? DIR_NEG_Y : DIR_POS_Y;
	WCoord wpos = NeighborCoord(m_BlockPosEx, dir);
	WCoord pos = NeighborCoord(m_BlockPos, dir);
	if(m_World == NULL) return false;

	if (m_World->getContainerMgr() == NULL && (!m_vehicleWorld || m_vehicleWorld->getContainerMgr() == NULL)) return false;

	WorldContainer *container = m_World->getContainerMgr()->getContainer(pos);
	if (m_vehicleWorld)
	{
		container = m_vehicleWorld->getContainerMgr()->getContainer(pos);
	}
	if(container)
	{
		BackPackGrid *grid = container->onExtractItem(ReverseDirection(dir));
		if(grid && onInsertItem(*grid, 1, dir)>0)
		{
			container->onSubtractItem(grid, 1);
			return true;
		}
	}
	else
	{
		checkItemTick -= 1;
		if (checkItemTick > 0)
		{
			return false;
		}
		checkItemTick = GenRandomInt(0, 20);
		CollideAABB box;
		box.setPosDim(wpos*BLOCK_SIZE, WCoord(BLOCK_SIZE/2,BLOCK_SIZE/2,BLOCK_SIZE/2));
		std::vector<IClientActor *>items;
		m_World->getActorsOfTypeInBox(items, box, OBJ_TYPE_DROPITEM);
		if(!items.empty())
		{
			ClientItem *item = dynamic_cast<ClientItem *>(items[0]);
			assert(item);
			BackPackGrid* src = &item->m_ItemData;//物品信息保存在BackPackGrid
			if (src && src->getAttracted() == false)
			{
				src->setAttracted(true);
				int n = onInsertItem(item->m_ItemData, item->m_ItemData.getNum(), dir);
				if (n > 0)
				{
					item->onSubtractItem(n, 0);
					return true;
				}
			}
		}
	}
	return false;
}

void WorldFunnelContainer::dropItems()
{
	for(size_t i=0; i<m_Grids.size(); i++)
	{
		dropOneItem(m_Grids[i]);
	}
}

int WorldFunnelContainer::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (size_t i = 0; i < m_Grids.size(); i++)
		{
			if (m_Grids[i].isEmpty()) continue;

			storeGridData(pItemInfos->Add(), &m_Grids[i]);
		}
	}
	return m_Grids.size();
}

int WorldFunnelContainer::onInsertItem(const BackPackGrid &grid, int num, int params)
{
	return InsertItemIntoArray(this, &m_Grids[0], int(m_Grids.size()), grid, num);
}

bool WorldFunnelContainer::canInsertItem(const BackPackGrid& grid, int param)
{
	return CheckInsertItemIntoArray(this, &m_Grids[0], int(m_Grids.size()), grid) <= 0;
}

BackPackGrid *WorldFunnelContainer::onExtractItem(int params)
{
	for(size_t i=0; i<m_Grids.size(); i++)
	{
		if(!m_Grids[i].isEmpty()) return &m_Grids[i];
	}
	return NULL;
}

int WorldFunnelContainer::calComparatorInputOverride()
{
	return CalculateItemsComparatorInput(&m_Grids[0], int(m_Grids.size()));
}

bool WorldFunnelContainer::checkPutItem(int itemid, int num)
{
	const ItemDef *def = GetDefManagerProxy()->getItemDef(itemid);
	if (!def) return false;

	int putnum = 0;

	for (int i = 0; i < MAX_FUNNEL_GRIDS; i++)
	{
		BackPackGrid &grid = m_Grids[i];
		if (grid.isEmpty())
		{
			putnum += def->StackMax;
			if (putnum >= num) return true;
		}
		if (grid.getItemID() == itemid)
		{
			putnum += (def->StackMax - grid.getNum());
			if (putnum >= num) return true;
		}
	}
	return false;
}

BackPackGrid *WorldFunnelContainer::getGridByItemID(int itemid)
{
	for (int i = 0; i < MAX_FUNNEL_GRIDS; i++)
	{
		BackPackGrid &grid = m_Grids[i];
		if (grid.getItemID() == itemid)
		{
			return &grid;
		}
	}

	return NULL;
}

bool WorldFunnelContainer::checkEmptyGrid(int resid)
{
	for(int i=0; i<MAX_FUNNEL_GRIDS; i++)
	{
		BackPackGrid &grid = m_Grids[i];
		if(grid.isEmpty())
		{
			return true;
		}
		if(grid.getItemID() == resid)
		{
			return true;
		}
	}
	return false;
}
int WorldFunnelContainer::addItem_byGridCopyData(const GridCopyData& grid)
{
	return InsertItemIntoArray_byGridCopyData(this, &m_Grids[0], MAX_FUNNEL_GRIDS,  grid);
}

//int WorldFunnelContainer::addItem(int resid, int num, int durable /* = -1 */, int enchantnum/* =0 */, const int enchants[]/* =0 */, void *userdata, const char *userdata_str)
//{
//	return InsertItemIntoArray(this, &m_Grids[0], MAX_FUNNEL_GRIDS, resid, num, durable, enchantnum, enchants, userdata, userdata_str);
//}

BackPackGrid *WorldFunnelContainer::index2Grid(int index)
{
	assert(index>=m_BaseIndex && size_t(index)<m_BaseIndex+m_Grids.size());

	return &m_Grids[index-m_BaseIndex];
}

void WorldFunnelContainer::onAttachUI()
{
	m_AttachToUI = true;

	for(size_t i=0; i<m_Grids.size(); i++)
	{
		//ge GetGameEventQue().postBackpackChange(FUNNEL_START_INDEX+i);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", FUNNEL_START_INDEX + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}
}

void WorldFunnelContainer::onDetachUI()
{
	m_AttachToUI = false;
}

bool WorldFunnelContainer::canPutItem(int index)
{
	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldFunnelContainer::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	flatbuffers::Offset<FBSave::ItemGrid> items[32];
	unsigned char indices[32];
	int count = 0;

	for(size_t i=0; i<m_Grids.size(); i++)
	{
		if(!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			indices[count] = (unsigned char)i;
			count++;
		}
	}

	auto actor = FBSave::CreateContainerFunnel(builder, basedata, builder.CreateVector(items, count), builder.CreateVector(indices,count), m_Grids.size(), m_TransferTicks, m_UpsideDown?1:0);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerFunnel, actor.Union());
}

bool WorldFunnelContainer::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerFunnel *>(srcdata);
	loadContainerCommon(src->basedata());

	auto items = src->items();
	auto indices = src->indices();

	for(size_t i=0; i<items->size(); i++)
	{
		int index = indices->Get(i);
		assert(index>=0 && size_t(index)<m_Grids.size());

		m_Grids[index].load(items->Get(i));
	}

	m_TransferTicks = src->transferticks();
	m_UpsideDown = src->upsidedown()>0;
	return true;
}

void WorldFunnelContainer::clear()
{
	int maxgrids = getGridNum();
	for (int i = 0; i < maxgrids; i++){
		BackPackGrid& grid = m_Grids[i];
		if (!grid.isEmpty()) {
			grid.clear();
			afterChangeGrid(grid.getIndex());
		}
	}
}

int WorldFunnelContainer::addItemByCount(int itemid, int num)
{
	GridCopyData copydata;
	copydata.resid = itemid;
	copydata.num = num;
	return addItem_byGridCopyData(copydata);
}

void WorldFunnelContainer::removeItemByCount(int itemid, int num)
{
	int maxgrids = getGridNum();
	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid *grid = index2Grid(i + m_BaseIndex);
		if (grid && grid->getItemID() == itemid)
		{
			if (num >= grid->getNum())
			{
				num -= grid->getNum();
				grid->addNum(-grid->getNum());
				grid->clear();
				afterChangeGrid(grid->getIndex());
			}
			else
			{
				grid->addNum(-num);
				afterChangeGrid(grid->getIndex());
				return;
			}
		}
	}
}
