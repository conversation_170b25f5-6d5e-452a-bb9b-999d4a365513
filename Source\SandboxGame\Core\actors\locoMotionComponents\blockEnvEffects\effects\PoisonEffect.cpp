#include "PoisonEffect.h"

#include "ClientActor.h"
#include "ActorTypes.h"//ATTACK_FIRE
#include "ConstAtLua.h"
#include "ActorAttrib.h"
#include "AttackedComponent.h"
#include "LuaInterfaceProxy.h"
using namespace MNSandbox;


void PoisonEffect::executeEffect(ClientActor* pActor)
{
	if (!pActor)
	{
		assert(pActor != nullptr);
		return;
	}


	ActorAttrib *attrib = pActor->getAttrib();
	if (attrib && !attrib->hasImmuneType(attrib->getImmuneTypeByAttackType(ATTACK_POISON)))
	{
		auto component = pActor->getAttackedComponent();
		{
			component->attackedFromType_Base(ATTACK_POISON, 1 * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv);
		}
	}
}
