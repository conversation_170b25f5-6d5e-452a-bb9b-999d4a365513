#pragma once

#include "ClientActorProjectile.h"

class ClientActorNewArrow :public ClientActorProjectile //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ClientActorNewArrow();
	void onImpactWithActor(ClientActor *actor, const std::string& partname) override;
	void onImpactWithBlock(const WCoord *blockpos, int face) override;
	void doAttackActor(ClientActor *target, Rainbow::Vector3f &motion);
	static ClientActorNewArrow* shootNewArrowAuto(World* pworld, const WCoord& pos, const Rainbow::Vector3f& dir, float speed, float deviation, long long shooterObjId);

	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool load(const void *srcdata, int version) override;
	virtual bool supportSaveToPB()
	{
		return false;
	}


	virtual int getObjType()const override
	{
		return OBJ_TYPE_ARROW;
	}
	//tolua_end
protected:
	~ClientActorNewArrow();
}; //tolua_exports

