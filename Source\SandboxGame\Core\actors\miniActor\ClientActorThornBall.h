#ifndef __CLIENTACTORTHORN_BALL_H__
#define __CLIENTACTORTHORN_BALL_H__

#include "ClientActorProjectile.h"
#include "GameScene/MovableObject.h"
namespace game {
    namespace hc {
        class PB_GeneralEnterAOIHC;
    }
}

//刺球
class ClientActorThornBall : public ClientActorProjectile { //tolua_exports
public:
	static void createThornEntity(ClientActor* actor);
	static void createThornEntity2(ClientActor* actor, int num);//清理所有的一次生成
	static void createSawtooth(World* pworld, const WCoord& blockpos, ClientActor* actor);
	//tolua_begin
	ClientActorThornBall();
	virtual ~ClientActorThornBall();
	virtual void init(int itemid, ClientActor* shooter = nullptr);
	virtual void onImpactWithActor(ClientActor* actor, const std::string& partname);
	virtual void onImpactWithBlock(const WCoord* blockpos, int face);
	virtual void onCollideWithPlayer(ClientActor* player);
	virtual int saveToPB(game::hc::PB_GeneralEnterAOIHC* pb);
	virtual int LoadFromPB(const game::hc::PB_GeneralEnterAOIHC& pb);

	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata, int version) override;
	virtual int getObjType() const override
	{
		return OBJ_TYPE_THORNBALL;
	}
	virtual void setNeedClearEx(int delay_ticks = 0);
	//tolua_end
	int		getBallLostCount();
	bool	isMaxActorThornBall();
	void	doImpactActor();
	void	setIsDrop(bool is);
	bool 	isDrop();
	bool	isNeedTailEffect();
	void	setNeedTailEffect(bool is);
private:
	Rainbow::MovableObject* m_ThornBallModel;
	ClientActor* m_ImpactActor;
	int			m_MaxActorCount;
	bool		m_IsInMaxState;
	bool		m_IsDrop;
	bool		m_IsTailEffect;
}; //tolua_exports

#endif //__CLIENTACTORTHORN_BALL_H__

