#include "AIFlyAttract.h"
#include "ClientMob.h"
#include "LivingLocoMotion.h"
#include "ClientPlayer.h"
#include "ActorVision.h"
#include "ToAttackTargetComponent.h"
#include "ActorBody.h"
#include "SandboxIdDef.h"
#include "LivingAttrib.h"
#include "ActorFollow.h"

using namespace MNSandbox;
AIFlyAttract::AIFlyAttract(ClientMob *pActor, int iDist, int iFollowDist, int iTimeOut, int followCnt, int height)
{
	setMutexBits(1);
	m_pMobActor = pActor;
	m_FindDist = (float)iDist;
	m_FollowDist = iFollowDist;
	m_timeOut = iTimeOut;
	m_pLivingLocomotion = dynamic_cast<LivingLocoMotion *>(m_pMobActor->getLocoMotion());
	assert(m_pLivingLocomotion != NULL);
	m_TargetID = -1;
	m_StopDist = 40000;
	m_HasArrive = false;
	//916ð�� 2021/08/18 codeby:wudeshen
	if (followCnt > 3 || followCnt < 0)
		followCnt = 3;
	m_FollowCnt = followCnt;
	m_TickCnt = 0;
	m_height = height;
}

AIFlyAttract::~AIFlyAttract()
{

}

bool AIFlyAttract::willRun()
{
	if (!m_pLivingLocomotion)
	{
		return false;
	}
	//�����������
	ActorLiving *pTarget = dynamic_cast<ActorLiving *>(m_pMobActor->getActorMgr()->findActorByWID(m_TargetID));
	if (NULL == pTarget)
	{
		pTarget = dynamic_cast<ActorLiving *>(m_pMobActor->getActorMgr()->selectNearPlayer(m_pMobActor->getLocoMotion()->getPosition(), (int)m_FindDist));
		
		if (m_pMobActor->getMonsterDef()->ID == LanternRiddleBirdId) //916ð�� 2021/08/18 codeby:wudeshen
		{
			if (pTarget && !m_pMobActor->getVision()->canSee(pTarget))
			{
				pTarget = NULL;
			}
		}
		else
		{
			int mobids[] = { 3101,3200,3201,3202 };
			for (int i = 0; i < 4; i++)
			{
				//ActorFollow::isEquipGarland()
				if (NULL == pTarget || !(pTarget->getLivingAttrib() && pTarget->getLivingAttrib()->getEquipItemWithType(EQUIP_HEAD) == ITEM_GARLAND))  //!pTarget->Event().Emit("isEquipGarland", NS_SANDBOX::SandboxContext(nullptr)).IsExecSuccessed())
				{
					pTarget = dynamic_cast<ActorLiving *>(m_pMobActor->selectNearMob(mobids[i], 0, (int)m_FindDist));
				}
			}
		}

	}
	
	if (NULL == pTarget || pTarget->isDead() || pTarget->isInWater())
	{
		return false;
	}
	return canAttractMe(pTarget);
}

void AIFlyAttract::start()
{
	m_pLivingLocomotion->m_HasTarget = true;
	ActorLiving *pTarget = dynamic_cast<ActorLiving *>(m_pMobActor->getActorMgr()->findActorByWID(m_TargetID));
	if (pTarget)
	{
		m_pLivingLocomotion->m_MoveTarget = pTarget->getPosition() + 300 * Rainbow::Vector3f::yAxis;
		m_pLivingLocomotion->setBehaviorOn(BehaviorType::Pursuit);
	}

	ActorBody *pActorBody = m_pMobActor->getBody();
	if (pActorBody == NULL) return;

	// ����
	int actorId = m_pMobActor->getDefID();
	if ((actorId >= 3885 && actorId <= 3890) || actorId == 3255)
	{
		if (!pActorBody->hasAnimPlaying(SEQ_WALK))
		{
			//m_pMobActor->playAnim(SEQ_WALK);
			pActorBody->setCurAnim(SEQ_WALK, 0);
		}
	}
	m_TickCnt = 0;


	if (m_pMobActor->getMonsterDef()->ID == 3897 && pTarget)
	{
		WCoord diff = m_pMobActor->getPosition() - pTarget->getPosition();
		diff.y = 0;
		Rainbow::Vector3f dir(diff.x, 0, diff.z);
		dir  = MINIW::Normalize(dir);
		m_pLivingLocomotion->m_MoveTarget = pTarget->getPosition() + m_FollowDist * dir + Rainbow::Vector3f(0.0f, m_height, 0.0f);
	}
}

bool AIFlyAttract::continueRun()
{
	//�����������
	ClientActor *target = m_pMobActor->getBeHurtTarget();
	if (NULL != target && !target->isDead())
	{
		if (m_pMobActor->getVision()->canSee(target))
			return false;
	}

	ActorLiving *pTarget = dynamic_cast<ActorLiving *>(m_pMobActor->getActorMgr()->findActorByWID(m_TargetID));
	if (pTarget == NULL)
		return false;

	WORLD_ID objId = m_pMobActor->getObjId();
	auto comp = pTarget->getActorFollow();
	if (comp && comp->checkContinueFollow(objId))
	{
		return false;
	}

	WCoord vec = pTarget->getLocoMotion()->getPosition() - m_pMobActor->getLocoMotion()->getPosition();
	if (vec.lengthSquared() > 1.2f * m_FindDist * m_FindDist)
	{
		return false;
	}

	if (m_pMobActor->getMonsterDef()->ID == LanternRiddleBirdId) //916ð�� 2021/08/18 codeby:wudeshen
	{
		if (pTarget && !m_pMobActor->getVision()->canSee(pTarget))
		{
			return false;
		}
	}

	return true;
}

void AIFlyAttract::reset()
{
	m_HasArrive = false;
	m_pLivingLocomotion->setBehaviorOff(BehaviorType::Pursuit);
}

void AIFlyAttract::update()
{
	if (m_pMobActor->getMonsterDef()->ID != LanternRiddleBirdId) //916ð�� 2021/08/18 codeby:wudeshen
	{
		if (!m_pLivingLocomotion->isBehaviorOn(BehaviorType::Pursuit))
		{
			m_pLivingLocomotion->setBehaviorOn(BehaviorType::Pursuit);
		}

		ActorLiving *pTarget = dynamic_cast<ActorLiving *>(m_pMobActor->getActorMgr()->findActorByWID(m_TargetID));
		ClientActor *target = nullptr;
		auto targetComponent = m_pMobActor->getToAttackTargetComponent();
		if (targetComponent)
		{
			target = targetComponent->getTarget();
		}
		if (!target && pTarget && pTarget->getBeHurtTarget() && baseIsSuitableTarget(pTarget->getBeHurtTarget(), m_pMobActor))
		{
			ClientActor *tmpTarget = pTarget->getBeHurtTarget();
			//���˸��������
			if (tmpTarget && tmpTarget->getObjId() != pTarget->getObjId())
			{
				targetComponent->setTarget(pTarget->getBeHurtTarget());
			}
		}
		//û�й�����?
		else if (pTarget)
		{
			float diff = (pTarget->getPosition() - m_pMobActor->getPosition()).length();
			if (diff > 400 && m_TickCnt % 60 == 0)
			{
				m_pLivingLocomotion->m_MoveTarget = pTarget->getPosition() + 300 * Rainbow::Vector3f::yAxis;
			}
			else
			{
				if (!m_pMobActor->getBody()->hasAnimPlaying(SEQ_HOVER) &&
					!m_pMobActor->getBody()->hasAnimPlaying(SEQ_HAPPLYFLY) &&
					!m_pMobActor->getBody()->hasAnimPlaying(SEQ_UPSETFLY))
					m_pMobActor->playAnim(SEQ_HOVER);
			}
			m_TickCnt++;
			m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);
		}
	}
	else //916ð�� 2021/08/18 codeby:wudeshen
	{
		ActorLiving *pTarget = dynamic_cast<ActorLiving *>(m_pMobActor->getActorMgr()->findActorByWID(m_TargetID));
		if (pTarget)
		{
			float diff = (pTarget->getPosition() - m_pMobActor->getPosition()).length();
			if (diff > m_FindDist && m_TickCnt % 60 == 0)
			{
				WCoord diff =  m_pMobActor->getPosition() - pTarget->getPosition();
				diff.y = 0;
				Rainbow::Vector3f dir(diff.x, 0, diff.z);
				dir  = MINIW::Normalize(dir);
				m_pLivingLocomotion->m_MoveTarget = pTarget->getPosition() + m_FollowDist * dir + Rainbow::Vector3f(0.0f, m_height, 0.0f);
			}
			else
			{
				if(!m_pMobActor->getBody()->hasAnimPlaying(SEQ_HOVER) &&
					!m_pMobActor->getBody()->hasAnimPlaying(SEQ_HAPPLYFLY) &&
					!m_pMobActor->getBody()->hasAnimPlaying(SEQ_UPSETFLY))
					m_pMobActor->playAnim(SEQ_HOVER);
			}
			m_TickCnt++;
		}
		m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);
	}
}

bool AIFlyAttract::canAttractMe(ActorLiving *living)
{
	if (NULL != living)
	{
		WORLD_ID objId = m_pMobActor->getObjId();
		auto comp = living->getActorFollow();
		if (comp)
		{
			bool ret = comp->checkCanAddFollow(objId, m_FollowDist, m_timeOut, m_TargetID, m_pMobActor->getMonsterDef()->ID, m_FollowCnt);
			if (ret)
			{
				m_TargetID = living->getObjId();
				return true;
			}
			else
			{
				m_TargetID = -1;
				return false;
			}
		}
	}
	return false;
}