#include "container_importmodel.h"

#include "ImportCustomModelMgr.h"
#include "BlockMaterialMgr.h"
#include "BlockScene.h"
#include "WorldManager.h"
#include "DefManagerProxy.h"
#include "MpGameSurviveCdnResMgr.h"
#include "File/FileManager.h"
#include "FullyCustomModelMgr.h"
//#include "GameEvent.h"
#include "ClientActorDef.h"

#include "Entity/OgreEntity.h"
using namespace MINIW;
using namespace Rainbow; 
ContainerImportModel::ContainerImportModel() : WorldContainer(), m_pEntity(NULL), m_ImportModelKey(""),m_iDir(0), m_bNeedDel(false)
{
	m_curanimid = -1;
	m_curanimplaymodel = -1;
	m_NeedTick = true;
}

ContainerImportModel::ContainerImportModel(const WCoord &blockpos, int dir) : WorldContainer(blockpos, 0), m_pEntity(NULL), m_iDir(dir), m_ImportModelKey(""), m_bNeedDel(false)
{
	m_curanimid = -1;
	m_curanimplaymodel = -1;
	m_NeedTick = true;
}

ContainerImportModel::~ContainerImportModel()
{
	if (m_pEntity)
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_pEntity);
}

int ContainerImportModel::getObjType() const
{
	return OBJ_TYPE_IMPORTMODEL;
}


flatbuffers::Offset<FBSave::ChunkContainer> ContainerImportModel::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerImportModel(builder, basedata, builder.CreateString(m_ImportModelKey), m_iDir);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerImportModel, actor.Union());
}

EXPORT_SANDBOXENGINE extern int g_BackgridCheckNumMethod;
bool ContainerImportModel::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerImportModel *>(srcdata);
	loadContainerCommon(src->basedata());
	m_ImportModelKey = src->modelkey()->c_str();
	m_iDir = src->direction();

	return true;
}

bool ContainerImportModel::canAddToChunk(Chunk *pchunk)
{
	if (!pchunk)
		return false;

	return true;
}

void ContainerImportModel::enterWorld(World *pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	registerUpdateDisplay();

	auto blockdef = GetDefManagerProxy()->getBlockDef(pworld->getBlockID(m_BlockPos));
	bool isDefTypeOk = (blockdef->Type == "importmodel" || blockdef->Type == "modbase");
	if (!blockdef || !isDefTypeOk)
	{
		m_bNeedDel = true;
		return;
	}

	if (!m_pEntity)
		updateEntity();
}

void ContainerImportModel::leaveWorld()
{
	WorldContainer::leaveWorld();
	if (m_pEntity)
	{
		//m_pEntity->DetachFromScene();
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_pEntity);
	}

}

void ContainerImportModel::updateTick()
{
	if (m_bNeedDel && m_World)
	{
		m_World->getContainerMgr()->destroyContainer(m_BlockPos);
		return;
	}
}

void ContainerImportModel::updateDisplay(float dtime)
{
	if (m_pEntity)
	{
		unsigned int dtick = TimeToTick(dtime);
		m_pEntity->UpdateTick(dtick);
	}
}

int ContainerImportModel::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	return 0;
}

void ContainerImportModel::onAttachUI()
{
	m_AttachToUI = true;

	//ge GetGameEventQue().postBackPackAttribChange();
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_ATTRIB_CHANGE", sandboxContext);
}

void ContainerImportModel::onDetachUI()
{
	m_AttachToUI = false;
}

void ContainerImportModel::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);
}

bool ContainerImportModel::canPutItem(int index)
{
	return true;
}


void ContainerImportModel::resetEntityBySyncData(std::string skey)
{
	if (!MpGameSurviveCdnResMgr::GetInstancePtr())
		return;

	if (!MpGameSurviveCdnResMgr::GetInstancePtr()->hasRes(2, m_ImportModelKey))
		return;

	updateEntity();
}

void ContainerImportModel::updateEntity()
{
#ifndef IWORLD_SERVER_BUILD	
	if (m_pEntity)
	{
		//m_pEntity->DetachFromScene();
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_pEntity);
	}

	if (!FullyCustomModelMgr::GetInstancePtr())
		return;

	if (m_ImportModelKey.empty())
		return;

	Rainbow::Model *pModel = ImportCustomModelMgr::GetInstancePtr()->getImportModel(m_ImportModelKey);
	if (!pModel)
		return;


	m_pEntity = Entity::Create();
	m_pEntity->Load(pModel);

	

	//int hightOffset = m_bFCMBlock ? 0 : BLOCK_SIZE / 2;
	WCoord pos = WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2) + m_BlockPos*BLOCK_SIZE;
	m_pEntity->SetPosition(pos.toWorldPos());


	if (m_pEntity->GetMainModel())
	{
		Vector4f lightparam(1, 1, 0, 0);
		if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, m_BlockPos);
		m_pEntity->SetInstanceData(lightparam);
	}


	if (m_iDir == DIR_NEG_X)
		m_pEntity->SetRotation(90, 0, 0);
	else if (m_iDir == DIR_POS_X)
		m_pEntity->SetRotation(270, 0, 0);
	else if (m_iDir == DIR_NEG_Z)
		m_pEntity->SetRotation(0, 0, 0);
	else if (m_iDir == DIR_POS_Z)
		m_pEntity->SetRotation(180, 0, 0);

	if (m_curanimid > 0 && m_curanimplaymodel > 0)
	{
		m_pEntity->PlayAnim(m_curanimid, m_curanimplaymodel);
		m_curanimid = -1;
		m_curanimplaymodel = -1;
	}
	else
	{
		m_pEntity->PlayAnim(0);
	}

	if (m_World)
		m_pEntity->AttachToScene(m_World->getScene());
#endif
}

void ContainerImportModel::convertRotateByBluePrint(int rotatetype)
{

	if (rotatetype == ROTATE_90)
	{
		if (m_iDir == DIR_NEG_X)
			m_iDir = DIR_NEG_Z;
		else if (m_iDir == DIR_POS_X)
			m_iDir = DIR_POS_Z;
		else if (m_iDir == DIR_NEG_Z)
			m_iDir = DIR_POS_X;
		else if (m_iDir == DIR_POS_Z)
			m_iDir = DIR_NEG_X;
	}
	else if (rotatetype == ROTATE_180)
	{
		if (m_iDir == DIR_NEG_X)
			m_iDir = DIR_POS_X;
		else if (m_iDir == DIR_POS_X)
			m_iDir = DIR_NEG_X;
		else if (m_iDir == DIR_NEG_Z)
			m_iDir = DIR_POS_Z;
		else if (m_iDir == DIR_POS_Z)
			m_iDir = DIR_NEG_Z;
	}
	else if (rotatetype == ROTATE_270)
	{
		if (m_iDir == DIR_NEG_X)
			m_iDir = DIR_POS_Z;
		else if (m_iDir == DIR_POS_X)
			m_iDir = DIR_NEG_Z;
		else if (m_iDir == DIR_NEG_Z)
			m_iDir = DIR_NEG_X;
		else if (m_iDir == DIR_POS_Z)
			m_iDir = DIR_POS_X;
	}
	else if (rotatetype == MIRROR_0)
	{
		if (m_iDir == DIR_NEG_X)
			m_iDir = DIR_POS_X;
		else if (m_iDir == DIR_POS_X)
			m_iDir = DIR_NEG_X;
		else if (m_iDir == DIR_NEG_Z)
			m_iDir = DIR_NEG_Z;
		else if (m_iDir == DIR_POS_Z)
			m_iDir = DIR_POS_Z;
	}
	else if (rotatetype == MIRROR_180)
	{
		if (m_iDir == DIR_NEG_X)
			m_iDir = DIR_NEG_X;
		else if (m_iDir == DIR_POS_X)
			m_iDir = DIR_POS_X;
		else if (m_iDir == DIR_NEG_Z)
			m_iDir = DIR_POS_Z;
		else if (m_iDir == DIR_POS_Z)
			m_iDir = DIR_NEG_Z;
	}
	else if (rotatetype == MIRROR_90)
	{
		if (m_iDir == DIR_NEG_X)
			m_iDir = DIR_POS_Z;
		else if (m_iDir == DIR_POS_X)
			m_iDir = DIR_NEG_Z;
		else if (m_iDir == DIR_NEG_Z)
			m_iDir = DIR_POS_X;
		else if (m_iDir == DIR_POS_Z)
			m_iDir = DIR_NEG_X;
	}
	else if (rotatetype == MIRROR_270)
	{
		if (m_iDir == DIR_NEG_X)
			m_iDir = DIR_NEG_Z;
		else if (m_iDir == DIR_POS_X)
			m_iDir = DIR_POS_Z;
		else if (m_iDir == DIR_NEG_Z)
			m_iDir = DIR_NEG_X;
		else if (m_iDir == DIR_POS_Z)
			m_iDir = DIR_POS_X;
	}

	afterChangeGrid(-1);
}

void ContainerImportModel::changeModelDir(int dir)
{
	m_iDir = dir;
	if (!m_pEntity)
		return;


	if (m_iDir == DIR_NEG_X)
		m_pEntity->SetRotation(90, 0, 0);
	else if (m_iDir == DIR_POS_X)
		m_pEntity->SetRotation(270, 0, 0);
	else if (m_iDir == DIR_NEG_Z)
		m_pEntity->SetRotation(0, 0, 0);
	else if (m_iDir == DIR_POS_Z)
		m_pEntity->SetRotation(180, 0, 0);
}

void ContainerImportModel::PlayAnim(int animid, int playmode)
{
	if (!m_pEntity) 
	{
		if (playmode > 0 && (playmode == ANIM_MODE_LOOP || playmode == ANIM_MODE_ONCE_STOP))
		{
			m_curanimid = animid;  //触发器设置方块动画  暂定只有循环播放和播放停止到最后一帧才缓存
			m_curanimplaymodel = playmode;
		}
		return;
	}

	m_pEntity->PlayAnim(animid, playmode);
}

void ContainerImportModel::SetSubMeshInfo(const BlockSubMeshInfo& info)
{
	m_SubMeshInfo = info;
}

BlockSubMeshInfo ContainerImportModel::GetSubMeshInfo()
{
	return m_SubMeshInfo;
}
