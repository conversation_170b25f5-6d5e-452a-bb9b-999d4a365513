#ifndef __RIDDEN_COMPONENT_H__
#define __RIDDEN_COMPONENT_H__

#include "Math/Vector3f.h"
#include "world_types.h"
#include "ActorComponent_Base.h"
#include "SandboxGame.h"
#include "world_ActorExcludes.h"

class ClientActor;
class IPlayerControl;
class ClientPlayer;
class EXPORT_SANDBOXGAME RiddenComponent;
class RiddenComponent : public ActorComponentBase//tolua_exports
{//tolua_exports
public:
	DECLARE_COMPONENTCLASS(RiddenComponent)
	/* 进入owner */
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	/* 离开owner */
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	RiddenComponent();
	~RiddenComponent();

	void CreateEvent2();
	void DestroyEvent2();
	//virtual void OnTick();

	void initData();

	void CreateModuleEvent();

	//tolua_begin
	void setRidingActorObjId(WORLD_ID objId)
	{
		m_RidingActor = objId;
	}

	long long getRidingActorObjId()
	{
		return m_RidingActor;
	}


	bool isRiding()
	{
		return m_RidingActor != 0;
	}
	bool isRidden()
	{
		return m_RiddenByActor != 0;
	}

	void setNumRiddenPos(int num = 1);

	bool checkRiddenByActorObjId(WORLD_ID objId, bool otherRiddens = false);
	bool checkRidingByActorObjId(WORLD_ID objId);
	ClientActor* getRidingActor();

	void initOtherRiddens(int size = 1);



	// virtual
	void setRidingActor(ClientActor* actor);
	int findRiddenIndex(ClientActor *ridden);
	void setRiddenByActor(ClientActor *ridden, int i = 0);
	void setRiddenByActorObjId(WORLD_ID objId, int i = 0);
	long long getRiddenByActorID(int i = 0);
	ClientActor * getRiddenByActor(int i = 0);
	int getNumRiddenPos();
	bool canClearRidden(ClientActor *ridden, bool isforce = false);
	void clearRiddenActor(ClientActor *ridden);
	void clearRiddenActor();
	int findEmptyRiddenIndex(int index = 0);
	bool getRiddenBindRot(ClientActor* ridden, Rainbow::Quaternionf& rot);
	bool getRiddenChangeFPSView();
	void resetRiddenBindPos();
	Rainbow::Vector3f getRiddenBindPos(ClientActor * ridden);
	bool canBeRided(ClientPlayer *player);
	virtual WCoord getRiderPosition(ClientActor *ridden);

	//tolua_end

	int getNumRiddenPos_Base();
	void resetRiddenBindPos_Base();
	void setRidingActor_Base(ClientActor* actor);
	Rainbow::Vector3f getRiddenBindPos_Base(ClientActor *ridden);
	int findEmptyRiddenIndex_Base(int index = 0);
	WORLD_ID getRiddenByActorID_Base(int i = 0);
	void setRiddenByActorObjId_Base(WORLD_ID objId, int i = 0);
	void setRiddenByActor_Base(ClientActor *ridden, int i = 0);
	ClientActor * getRiddenByActor_Base(int i = 0);
	int findRiddenIndex_Base(ClientActor *ridden);
	bool getRiddenChangeFPSView_Base();
	bool canClearRidden_Base(ClientActor *ridden, bool isforce = false);
	bool getRiddenBindRot_Base(ClientActor* ridden, Rainbow::Quaternionf& rot);
	bool canBeRided_Base(ClientPlayer *player);
	WCoord getRiderPosition_Base(ClientActor *ridden);


	bool mountActor_Base(ClientActor *actor, bool isforce = false, int seatIndex = -1);
	bool mountActor_Base(long long objid, int posindex);

	//tolua_begin
	bool isVehicleController();	//坐在驾驶座或者坐在可控制部件的乘客座上
	bool isVehicleDriver();		//坐在驾驶座上

	//virtual
	bool mountActor(ClientActor *actor, bool isforce = false, int seatIndex = -1);
	bool mountActor(long long objid, int posindex);
	//tolua_end

	bool isVehiclePassenger();
	void setRiddenControl(bool bctrl);
	bool getRiddenControl();
public:
	WORLD_ID m_RidingActor;		//作为骑乘者时，坐骑/载具 的objid
	WORLD_ID m_RiddenByActor;	//作为坐骑/载具时，主要骑乘者的objid


	std::vector<WORLD_ID> m_OtherRiddens;  //额外的骑乘者

	int m_NumRidePos; //坐骑/载具 位置绑点的数量,有几个则代表该坐骑/载具 可以承载几人

	bool m_canControl;// 坐骑被骑乘时是否可移动操作

	MNSandbox::AutoRef<MNSandbox::Listener<Rainbow::Quaternionf&, Rainbow::Quaternionf, IPlayerControl*>> m_listenerRidden1;
	MNSandbox::AutoRef<MNSandbox::Listener<bool&, Rainbow::Quaternionf, IPlayerControl*>> m_listenerRidden2;
	MNSandbox::AutoRef<MNSandbox::Listener<Rainbow::Vector3f, int, float, float>> m_listenerRidden3;
	MNSandbox::AutoRef<MNSandbox::Listener<bool&, CollideAABB&>> m_listenerRidden4;
	MNSandbox::AutoRef<MNSandbox::Listener<ActorExcludes&>> m_listenerRidden5;
	MNSandbox::AutoRef<MNSandbox::Listener<bool&>> m_listenerRidden6;
	MNSandbox::AutoRef<MNSandbox::Listener<IClientActor*, IClientActor*>> m_listenerRidden7;
	MNSandbox::AutoRef<MNSandbox::Listener<ActorExcludes*, IClientActor*>> m_listenerRidden8;
	MNSandbox::AutoRef<MNSandbox::Listener<bool&>> m_listenerRidden9;
};//tolua_exports

#endif