#ifndef __CHANGE_COLOR_COMPONENT_H__
#define __CHANGE_COLOR_COMPONENT_H__

//#include "world_types.h"
//#include <vector>
//#include "WorldRole_generated.h"
#include "ActorComponent_Base.h"

class WCoord;
class ClientPlayer;

namespace game {
	namespace hc {
		class PB_PlayerBodyColorHC;
	}
};

class ChangeColorComponent : public ActorComponentBase
{
public:
	DECLARE_COMPONENTCLASS(ChangeColorComponent)

	ChangeColorComponent();

	//void onTick();
	virtual void OnTick() override;
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
	void onHandlePlayerBodyColor2Client(const game::hc::PB_PlayerBodyColorHC &playerBodyColorHC);

protected:	
	void setDestSkinColor(unsigned int color);//handlePlayerBodyColor2Client
	void setCurSkinColor(unsigned int color);
	void setColorIncrements(float increments);

	void checkChangeColor(WCoord &blockpos);
	void setColor(unsigned int color);	
	void gradualChangeColor();	

	ClientPlayer* m_owner;

	unsigned int m_DestSkinColor;//目标皮肤颜色
	unsigned int m_CurSkinColor;//当前皮肤颜色
	float  m_ColorIncrements;//颜色变化增量单位
	int m_CheckSkinColorTicks;//计时 定时触发功能 30tick 触发一次  变色龙皮肤变色功能
};


#endif