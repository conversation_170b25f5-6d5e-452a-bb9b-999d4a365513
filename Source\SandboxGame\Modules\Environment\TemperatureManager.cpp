﻿#include "TemperatureManager.h"
#include "world.h"
#include "LuaInterfaceProxy.h"
#include "PlayerControl.h"
#include "ClientPlayer.h"
#include "PlayerAttrib.h"
#include "ClientMob.h"
#include "WeatherManager.h"
#include "ClientActorHelper.h"
#include "WorldManager.h"
#include "SandBoxManager.h"
#include "MobAttrib.h"
#include "section.h"
#include "chunk.h"
#include "BlockMaterialBase.h"
#include "ActorManagerInterface.h"

using namespace MINIW;
using namespace MNSandbox;

static int BIOME_TEMPERATURE_HEIGHT[] =
{
	64,
	-1,
	32
};

TemperatureManager::TemperatureManager()
{
	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
	if (lua_const)
	{
		m_ConstMilaTempAtten = lua_const->m_ConstMilaTempAtten;
		m_ConstMilaTempAdd = lua_const->m_ConstMilaTempAdd;
		m_ConstMengyanTempAtten = lua_const->m_ConstMengyanTempAtten;
		m_ConstMengyanTempAdd = lua_const->m_ConstMengyanTempAdd;
		m_ConstPingtanTempAtten = lua_const->m_ConstPingtanTempAtten;
		m_ConstPingtanTempAdd = lua_const->m_ConstPingtanTempAdd;
		m_ConstTempBurn = lua_const->m_ConstTempBurn;
		m_ConstTempTopHeat = lua_const->m_ConstTempTopHeat;
		m_ConstTempHeat = lua_const->m_ConstTempHeat;
		m_ConstTempIce = lua_const->m_ConstTempIce;
		m_ConstTempTopIce = lua_const->m_ConstTempTopIce;
		m_ConstTempFreeze = lua_const->m_ConstTempFreeze;
		m_ConstWeatherRain = lua_const->m_ConstWeatherRain;
		m_ConstWeatherSnow = lua_const->m_ConstWeatherSnow;
		m_ConstWeatherTempest = lua_const->m_ConstWeatherTempest;
		m_ConstWeatherTempestUp = lua_const->m_ConstWeatherTempestUp;
		m_ConstWeatherBlizzard = lua_const->m_ConstWeatherBlizzard;
		m_ConstWeatherBlizzardUp = lua_const->m_ConstWeatherBlizzardUp;
		m_ConstWeatherThunder = lua_const->m_ConstWeatherThunder;
		m_ConstPlantTempChangeVal = lua_const->m_ConstPlantTempChangeVal;
	}

	m_MobList.clear();
	m_PlayerList.clear();
	m_AreaSourceInfoMap.clear();
	m_MobileSourceInfoMap.clear();
}

TemperatureManager::~TemperatureManager()
{
	m_MobList.clear();
	m_PlayerList.clear();
	m_AreaSourceInfoMap.clear();
	m_MobileSourceInfoMap.clear();
}

void TemperatureManager::OnTick()
{
	if (g_WorldMgr && (g_WorldMgr->isGameMakerRunMode() || g_WorldMgr->isGameMakerMode()) && g_WorldMgr->m_RuleMgr)
	{
		float val = 0;
		g_WorldMgr->getGameRuleValue(GMRULE_TEMPERATURE, val);
		if ((int)val == 1) m_Active = true;
		else m_Active = false;
	}
	if (MNSandbox::Config::GetSingleton().IsSandboxMode())
	{
		return;
	}


	if (!m_Active) return;
	if (g_WorldMgr == NULL) return;
	if (g_WorldMgr->isRemote() && m_Tick < 60) return;
	SourceTick();
	PlayerTick();
	MobTick();

	m_Tick++;
}

void TemperatureManager::PlayerTick()
{
	int interval = 20;			// 分帧20次计算一个玩家数组
	int rem = m_Tick % interval;
	if (rem == 0)
	{
		m_PlayerList.clear();
		g_WorldMgr->getAllPlayersObjid(m_PlayerList);
		m_PlayerListSizeRate = m_PlayerList.size() / (float)interval;
	}
	int s = m_PlayerListSizeRate * rem;
	int e = m_PlayerListSizeRate * (rem + 1);
	if (e > m_PlayerList.size()) e = m_PlayerList.size();
	bool isRemote = g_WorldMgr->isRemote();
	for (int i = s; i < e; i++)
	{
		long long objid = m_PlayerList[i];
		IClientActor* actor = g_WorldMgr->findActorByWID(objid);
		if (actor == NULL) continue;
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
		if (player == NULL) continue;
		float val = GetPositionTemperature(player->getWorld(), CoordDivBlock(player->getPosition()));
		float posTemp = player->getLivingAttrib()->getPosTemperature();
		if (!isRemote)
		{
			if (val != posTemp)
			{
				jsonxx::Object context;
				context << "posTemp" << val;
				SandBoxManager::getSingleton().sendToClient(player->getUin(), "PB_TEMPERATURE_PLAYER_POSTEMP_HC", context.bin(), context.binLen());
			}
		}
		player->getPlayerAttrib()->setPosTemperature(val);
	}
}

void TemperatureManager::MobTick()
{
	if (!g_pPlayerCtrl || !g_pPlayerCtrl->getWorld()) return;
	World* world = g_pPlayerCtrl->getWorld();
	int interval = 40;
	int rem = m_Tick % interval;
	if (rem == 0)
	{
		m_MobList.clear();
		int size = m_PlayerList.size();
		for (int i = 0; i < size; i++)
		{
			std::vector<long long> list;
			(static_cast<ClientActorMgr*>(world->getActorMgr()))->selectNearAllMobs(list, g_pPlayerCtrl->getPosition(), 3 * SECTION_BLOCK_DIM * BLOCK_SIZE);
			m_MobList.insert(m_MobList.end(), list.begin(), list.end());
		}

		m_MobListSizeRate = m_MobList.size() / (float)interval;
	}

	int s = ceil(m_MobListSizeRate * rem);
	int e = ceil(m_MobListSizeRate * (rem + 1));
	if (e > m_MobList.size()) e = m_MobList.size();
	bool isRemote = world->isRemoteMode();
	int mapid = world->getCurMapID();
	for (int i = s; i < e; i++)
	{
		long long objid = m_MobList[i];
		IClientActor* actor = world->getActorMgr()->iFindActorByWID(objid);
		if (actor == NULL) continue;
		ClientMob* mob = dynamic_cast<ClientMob*>(actor);
		if (mob == NULL) continue;
		float val = GetPositionTemperature(world, CoordDivBlock(mob->getPosition()));
		float posTemp = mob->getLivingAttrib()->getPosTemperature();
		if (isRemote)
		{
			if (val != posTemp)
			{
				jsonxx::Object context;
				context << "mapid" << mapid;
				context << "objid" << mob->getObjId();
				context << "posTemp" << posTemp;
				SandBoxManager::getSingleton().sendToHost("PB_TEMPERATURE_MOB_POSTEMP_HC", context.bin(), context.binLen());
			}
		}
		mob->getMobAttrib()->setPosTemperature(val);
	}
}

void TemperatureManager::SourceTick()
{
	/*for (auto iter = m_AreaSourceInfoMap.begin(); iter != m_AreaSourceInfoMap.end(); iter++)
	{
		std::unordered_map<int, AreaSourceInfo>& map = iter->second;
		int mapid = iter->first;
		for (auto it = map.begin(); it != map.end();)
		{
			AreaSourceInfo& info = it->second;

			if (info._pData)
			{
				if (info._pData->_tick == 0)
				{
					World* world = GetWorldManagerPtr()->getWorld(mapid);
					if (world)
					{
						Section* psection = world->getSection(info._blockpos);
						if (psection)
						{
							auto areaMap = psection->GetWritableSharedSectionData().getAreaTemperatureSourceMap();
							if (areaMap->find(it->first) != areaMap->end())
							{
								areaMap->erase(it->first);
							}
						}
						it = map.erase(it);
					}
				}
				else
				{
					it++;
					info._pData->_tick--;
				}
			}
			else
			{
				it = map.erase(it);
			}
		}
	}*/

	for (auto iter = m_MobileSourceInfoMap.begin(); iter != m_MobileSourceInfoMap.end(); iter++)
	{
		auto& map = iter->second;
		for (auto it = map.begin(); it != map.end();)
		{
			auto& data = it->second;
			if (data._tick == 0)
			{
				it = map.erase(it);
			}
			else
			{
				data._tick--;
				it++;
			}
		}
	}
}
float TemperatureManager::GetEnviromentTemperature(World* world, const WCoord& blockpos)
{
	if (!world || !m_Active) return 0.f;
	float val = GetBiomeTemperature(world, blockpos) + GetHeightTemperature(world, blockpos) + GetWeatherTemperature(world, blockpos);
	return clamp(val, TEMPERATURE_MIN_ICE, TEMPERATURE_MAX_HEAT);
}

float TemperatureManager::GetBiomeTemperature(World* world, const WCoord& blockpos)
{
	if (!world || !m_Active) return 0.f;
	auto biomeDef = world->getBiome(blockpos.x, blockpos.z);
	short Temperature[] = { 0,0 };
	if (biomeDef) std::copy(std::begin(biomeDef->Temperature), std::end(biomeDef->Temperature), std::begin(Temperature));
	float hour = world->getHours();
	if (hour >= 8.0f && hour < 20.0f) return Temperature[0];
	else return Temperature[1];
}

float TemperatureManager::GetHeightTemperature(World* world, const WCoord& blockpos)
{
	if (!world || !m_Active) return  0.f;

	// 每升高一格，温度下降0.01
	int heightDelta = blockpos.y - WATER_LEVEL;
	return clamp(heightDelta * -0.05, -10.0, +10.0);

	// int mapid = world->getCurMapID();
	// int height = BIOME_TEMPERATURE_HEIGHT[mapid];
	// float heightTemp = 0;
	// float val = height - blockpos.y;
	// if (mapid == MAPID_GROUND)
	// {
	// 	int biomeType = world->getBiomeType(blockpos.x, blockpos.z);
	// 	int terraintype = world->getTerrainType();
	// 	if (terraintype == TERRAIN_FLAT || terraintype == TERRAIN_HARDSAND_FLAT || terraintype == TERRAIN_WATER_FLAT || terraintype == TERRAIN_EMPTY_FLAT)
	// 	{
	// 		val = 7 - blockpos.y;
	// 		if (val > 0) heightTemp = (int)(val / m_ConstPingtanTempAdd) * m_ConstPlantTempChangeVal;
	// 		if (val < 0)  heightTemp = (int)(val / m_ConstPingtanTempAtten) * m_ConstPlantTempChangeVal;
	// 	}
	// 	else if (biomeType != BIOME_AIR_PLAINS && biomeType != BIOME_AIR_PLAINS_H && biomeType != BIOME_AIRISLAND)
	// 	{
	// 		if (val > 0) heightTemp = (int)(val / m_ConstMilaTempAdd) * m_ConstPlantTempChangeVal;
	// 		if (val < 0)  heightTemp = (int)(val / m_ConstMilaTempAtten) * m_ConstPlantTempChangeVal;
	// 	}
	// }
	// else if (mapid == MAPID_MENGYANSTAR)
	// {
	// 	if (val > 0) heightTemp = (int)(val / m_ConstMengyanTempAdd) * m_ConstPlantTempChangeVal;
	// 	if (val < 0)  heightTemp = (int)(val / m_ConstMengyanTempAtten) * m_ConstPlantTempChangeVal;
	// }
	// return clamp(heightTemp, TEMPERATURE_MIN_ICE, TEMPERATURE_MAX_HEAT);
}

float TemperatureManager::GetWeatherTemperature(World* world, const WCoord& blockpos)
{
	if (!world || !world->getWeatherMgr() || !m_Active) return 0.0f;
	int weather = world->getWeatherMgr()->getWeather(blockpos);
	if (weather == GROUP_RAIN_WEATHER) return m_ConstWeatherRain;
	else if (weather == GROUP_SNOW_WEATHER) return m_ConstWeatherSnow;
	else if (weather == GROUP_TEMPEST_WEATHER)
	{
		if (world->getWeatherMgr()->getTempestStage(blockpos) == PART_WEATHER_STAGE_STRENGTH) return m_ConstWeatherTempestUp;
		else return m_ConstWeatherTempest;
	}
	else if (weather == GROUP_BLIZZARD_WEATHER)
	{
		if (world->getWeatherMgr()->getWeatherStage(weather, blockpos) == PART_WEATHER_STAGE_STRENGTH) return m_ConstWeatherBlizzardUp;
		else return m_ConstWeatherBlizzard;
	}
	else if (weather == GROUP_THUNDER_WEATHER)
		return m_ConstWeatherThunder;
	return 0.0f;
}

float TemperatureManager::GetPositionTemperature(World* world, const WCoord& blockpos)
{
	if (!world || !m_Active) return 0.f;
	float eTemp = GetEnviromentTemperature(world, blockpos);
	float sTemp = GetSourceTemperature(world, blockpos);
	float mTemp = GetMoblieSourceTemperature(world, blockpos);
	float sumTemp = eTemp + sTemp + mTemp;
	if (sumTemp > eTemp && sumTemp > sTemp && sumTemp > mTemp)
	{
		return max(eTemp, max(sTemp, mTemp));
	}
	else if (sumTemp < eTemp && sumTemp < sTemp && sumTemp < mTemp)
	{
		return min(eTemp, min(sTemp, mTemp));
	}
	return sumTemp;
}

float TemperatureManager::GetSourceTemperature(World* world, const WCoord& blockpos)
{
	if (!world || !m_Active) return 0.f;
	if (blockpos.y < 0 || blockpos.y - CHUNK_BLOCK_Y >= SECTION_BLOCK_DIM) return 0.f;
	float heat = 0.f, maxHeat = 0.f, ice = 0.f, maxIce = 0.f;

	WCoord _pos = blockpos;
	if (blockpos.y - CHUNK_BLOCK_Y > 0) _pos.y -= SECTION_BLOCK_DIM;
	// 查找坐标点附近chunk得所有温度源
	Section* psection = world->getSection(_pos);
	if (psection)
	{
		std::unordered_map<short, char>* pMap = psection->getPosTemperatureMap();
		if (!pMap->empty())
		{
			Chunk* pchunk = world->getChunk(blockpos.x, blockpos.z);
			WCoord pos = blockpos - pchunk->m_Origin;
			int idx = xyz2Index(pos.x, blockpos.y, pos.z);
			if (pMap->find(idx) != pMap->end())
			{
				return pMap->find(idx)->second;
			}
		}

		WCoord offpos = blockpos - psection->m_Origin;
		for (int dz = -1; dz <= 1; ++dz)
		{
			int z = ((SECTION_BLOCK_DIM + dz * SECTION_BLOCK_DIM) / 2 - offpos.z) * dz;
			for (int dy = -1; dy <= 1; ++dy)
			{
				int y = ((SECTION_BLOCK_DIM + dy * SECTION_BLOCK_DIM) / 2 - offpos.y) * dy;
				for (int dx = -1; dx <= 1; ++dx)
				{
					Section* neighbourSect = psection->getNeighbourSection(dx, dy, dz);
					if (neighbourSect)
					{
						int x = ((SECTION_BLOCK_DIM + dx * SECTION_BLOCK_DIM) / 2 - offpos.x) * dx;
						int maxLen = max(x, max(y, z));
						GetSectionSourceTemperature(neighbourSect, blockpos, heat, ice, maxHeat, maxIce, maxLen);
						GetSectionAreaSourceTemperature(neighbourSect, blockpos, heat, ice, maxHeat, maxIce, maxLen);
					}
				}
			}
		}
	}

	float sumTemp = heat + ice;
	if (sumTemp > 0 && sumTemp > maxHeat) sumTemp = maxHeat;
	if (sumTemp < 0 && sumTemp < maxIce) sumTemp = maxIce;
	sumTemp = clamp(sumTemp, TEMPERATURE_MIN_ICE, TEMPERATURE_MAX_HEAT);
	if (psection && blockpos.y <= CHUNK_BLOCK_Y)
	{
		std::unordered_map<short, char>* pMap = psection->getPosTemperatureMap();
		Chunk* pchunk = world->getChunk(blockpos.x, blockpos.z);
		WCoord pos = blockpos - pchunk->m_Origin;
		int idx = xyz2Index(pos.x, blockpos.y, pos.z);
		if (pMap->find(idx) == pMap->end())
		{
			pMap->insert(make_pair(idx, (int)sumTemp));
		}
	}
	return sumTemp;
}

void TemperatureManager::GetSectionSourceTemperature(Section* psection, const WCoord& blockpos, float& heat, float& ice, float& maxHeat, float& maxIce, int range)
{
	const std::unordered_map<char, std::unordered_map<unsigned short, char>>* umap = psection->getValTemperatureMap();
	if (umap->empty()) return;
	WCoord pos;
	for (int i = range; i <= (int)TEMPERATURE_MAX_HEAT; i++)
	{
		if (umap->find(i) != umap->end())
		{
			auto map = &umap->find(i)->second;
			for (auto iter = map->begin(); iter != map->end(); ++iter)
			{
				int _x, _y, _z;
				index2XYZ(_x, _y, _z, iter->first);
				pos.x = _x + psection->m_Origin.x;
				pos.y = _y /*+ psection->m_Origin.y*/;
				pos.z = _z + psection->m_Origin.z;
				CalHeatSource(psection->getWorld(), blockpos, pos, i, heat, maxHeat);
			}
		}
	}
	for (int i = TEMPERATURE_MIN_ICE; i <= -range; i++)
	{
		if (umap->find(i) != umap->end())
		{
			auto map = &umap->find(i)->second;
			for (auto iter = map->begin(); iter != map->end(); ++iter)
			{
				int _x, _y, _z;
				index2XYZ(_x, _y, _z, iter->first);
				pos.x = _x + psection->m_Origin.x;
				pos.y = _y /*+ psection->m_Origin.y*/;
				pos.z = _z + psection->m_Origin.z;
				CalIceSource(psection->getWorld(), blockpos, pos, i, ice, maxIce);
			}
		}
	}
}

void TemperatureManager::GetSectionAreaSourceTemperature(Section* psection, const WCoord& blockpos, float& heat, float& ice, float& maxHeat, float& maxIce, int range)
{
	std::unordered_map<int, AreaTemperatureSourceData>* map = psection->GetWritableSharedSectionData().getAreaTemperatureSourceMap();
	if (map->empty()) return;
	WCoord pos;
	for (auto iter = map->begin(); iter != map->end(); iter++)
	{
		AreaTemperatureSourceData& data = iter->second;
		if (data._val >= range)
		{
			int _x, _y, _z;
			index2XYZ(_x, _y, _z, data._pos);
			pos.x = _x + psection->m_Origin.x;
			pos.y = _y /*+ psection->m_Origin.y*/;
			pos.z = _z + psection->m_Origin.z;
			CalHeatSource(psection->getWorld(), blockpos, pos, data._val, heat, maxHeat);
		}
		else if (data._val <= -range)
		{
			int _x, _y, _z;
			index2XYZ(_x, _y, _z, data._pos);
			pos.x = _x + psection->m_Origin.x;
			pos.y = _y/* + psection->m_Origin.y*/;
			pos.z = _z + psection->m_Origin.z;
			CalIceSource(psection->getWorld(), blockpos, pos, data._val, ice, maxIce);
		}
	}
}

void TemperatureManager::CalHeatSource(World* world, const WCoord& blockpos, const WCoord& sourcepos, float val, float& heat, float& maxHeat)
{
	if (!world) return;
	WCoord pos = blockpos - sourcepos;
	Rainbow::Vector3f vec = pos.toVector3();
	int lenX = abs(pos.x);
	int lenZ = abs(pos.z);
	int lenY = abs(pos.y);
	int len = max(lenX, max(lenZ, lenY));
	float temp = val;
	if (len != 0)
	{
		if (temp > (float)len)
		{
			vec *= (1.0f / (float)len);
			WCoord oldpos = sourcepos;
			for (int i = 1; i <= len; i++)
			{
				WCoord bpos = sourcepos + WCoord(vec.x * i, vec.y * i, vec.z * i);
				if (bpos == oldpos) continue;
				oldpos = bpos;
				int atten = BlockMaterial::getTemperatureOpacity(world->getBlockID(bpos));
				temp -= atten;
				if (temp <= 0)
				{
					temp = 0;
					break;
				}
				if (bpos == blockpos)
					break;
			}
		}
		else
			temp = 0;
	}

	heat += temp;
	maxHeat = maxHeat < temp ? temp : maxHeat;
}

void TemperatureManager::CalIceSource(World* world, const WCoord& blockpos, const WCoord& sourcepos, float val, float& ice, float& maxIce)
{
	if (!world) return;
	WCoord pos = blockpos - sourcepos;
	Rainbow::Vector3f vec = pos.toVector3();
	int lenX = abs(pos.x);
	int lenZ = abs(pos.z);
	int lenY = abs(pos.y);
	int len = max(lenX, max(lenZ, lenY));
	float temp = val;
	if (len != 0)
	{
		if (temp < (float)-len)
		{
			vec *= (1.0f / (float)len);
			WCoord oldpos = sourcepos;
			for (int i = 1; i <= len; i++)
			{
				WCoord bpos = sourcepos + WCoord(vec.x * i, vec.y * i, vec.z * i);
				if (bpos == oldpos) continue;
				oldpos = bpos;
				int atten = BlockMaterial::getTemperatureOpacity(world->getBlockID(bpos));
				temp += atten;
				if (temp >= 0)
				{
					temp = 0;
					break;
				}
				if (bpos == blockpos)
					break;
			}
		}
		else
			temp = 0;
	}


	ice += temp;
	maxIce = maxIce > temp ? temp : maxIce;
}

int TemperatureManager::AddAreaTemperatureSource(World* world, const WCoord& blockpos, float val, int tick /* = -1 */)
{
	if (!world || !m_Active) return 0;
	int mapid = world->getCurMapID();
	m_AreaSourceInfoMapIdx++;
	int index = m_AreaSourceInfoMapIdx + (mapid << 30);
	Section* psection = world->getSection(blockpos);
	if (psection)
	{
		auto map = psection->GetWritableSharedSectionData().getAreaTemperatureSourceMap();
		if (map->size() > 100) return 0;
		if (map->find(index) == map->end())
		{
			WCoord spos = blockpos - psection->m_Origin;
			int pos = xyz2Index(spos.x, blockpos.y, spos.z);
			map->insert(make_pair(index, AreaTemperatureSourceData(val, tick, pos, index)));

			if (m_AreaSourceInfoMap.find(mapid) != m_AreaSourceInfoMap.end())
			{
				m_AreaSourceInfoMap[mapid][index] = blockpos;
			}
			else
			{
				std::unordered_map<int, WCoord> infoMap;
				infoMap[index] = blockpos;
				m_AreaSourceInfoMap[mapid] = infoMap;
			}
		}
		else
		{
			// index 重复
			assert(false);
			return 0;
		}
	}
	return index;
}

bool TemperatureManager::RemoveAreaTemperatureSource(int index)
{
	if (index <= 0)
	{
		assert(false);
		return false;
	}
	int mapid = index >> 30;
	if (m_AreaSourceInfoMap.find(mapid) != m_AreaSourceInfoMap.end())
	{
		if (m_AreaSourceInfoMap[mapid].find(index) != m_AreaSourceInfoMap[mapid].end())
		{
			WCoord blockpos = m_AreaSourceInfoMap[mapid][index];

			World* world = GetWorldManagerPtr()->getWorld(mapid);
			if (world)
			{
				Section* psection = world->getSection(blockpos);
				if (psection)
				{
					auto map = psection->GetWritableSharedSectionData().getAreaTemperatureSourceMap();
					if (map->find(index) != map->end())
					{
						map->erase(index);
					}
					else
					{
						assert(false);
					}
				}
			}
			else
			{
				// 世界不在的删除有问题，不应该操作还没生成世界的温度源
				assert(false);
			}

			m_AreaSourceInfoMap[mapid].erase(index);
			return true;
		}
	}

	return false;
}

float TemperatureManager::GetMoblieSourceTemperature(World* world, const WCoord& blockpos)
{
	if (!world || !m_Active) return 0;
	float heat = 0, maxHeat = 0, ice = 0, maxIce = 0;
	GetMoblieSourceTemperature(world, blockpos, heat, ice, maxHeat, maxIce);
	float sumTemp = heat + ice;
	if (sumTemp > 0 && sumTemp > maxHeat) sumTemp = maxHeat;
	if (sumTemp < 0 && sumTemp < maxIce) sumTemp = maxIce;
	return clamp(sumTemp, TEMPERATURE_MIN_ICE, TEMPERATURE_MAX_HEAT);
}

int TemperatureManager::GetTemperatureLevel(float temp)
{
	if (temp > TEMPERATURE_MAX_HEAT || temp < TEMPERATURE_MIN_ICE) return TEMPERATURE_LEVEL_NONE;
	if (temp > m_ConstTempBurn) return TEMPERATURE_LEVEL_BURN;
	else if (temp > m_ConstTempTopHeat) return TEMPERATURE_LEVEL_TOPHEAT;
	else if (temp > m_ConstTempHeat) return TEMPERATURE_LEVEL_HEAT;
	else if (temp >= m_ConstTempIce) return TEMPERATURE_LEVEL_SUITABLE;
	else if (temp >= m_ConstTempTopIce) return TEMPERATURE_LEVEL_ICE;
	else if (temp >= m_ConstTempFreeze) return TEMPERATURE_LEVEL_TOPICE;
	else return TEMPERATURE_LEVEL_FREEZE;
}

bool TemperatureManager::IsTemperatureSuitable(float temp)
{
	return GetTemperatureLevel(temp) == TEMPERATURE_LEVEL_SUITABLE;
}

void TemperatureManager::GetBlockTemperatureAndLevel(World* world, const WCoord& blockpos, float& temp, int& level)
{
	if (!world || !m_Active)
	{
		temp = 0.f;
		level = TEMPERATURE_LEVEL_NONE;
		return;
	}
	temp = GetPositionTemperature(world, blockpos);
	level = GetTemperatureLevel(temp);
}

void TemperatureManager::GetTemperatureLevelAndVal(float temp, int& level, float& minVal, float& maxVal)
{
	if (temp > TEMPERATURE_MAX_HEAT || temp < TEMPERATURE_MIN_ICE)
	{ 
		level = TEMPERATURE_LEVEL_NONE;
		minVal = 0;
		maxVal = 0;
	}
	else if (temp > m_ConstTempBurn)
	{
		level = TEMPERATURE_LEVEL_BURN;
		minVal = m_ConstTempBurn;
		maxVal = TEMPERATURE_MAX_HEAT;
	}
	else if (temp > m_ConstTempTopHeat)
	{
		level = TEMPERATURE_LEVEL_TOPHEAT;
		minVal = m_ConstTempTopHeat;
		maxVal = m_ConstTempBurn;
	}
	else if (temp > m_ConstTempHeat)
	{
		level = TEMPERATURE_LEVEL_HEAT;
		minVal = m_ConstTempHeat;
		maxVal = m_ConstTempTopHeat;
	}
	else if (temp >= m_ConstTempIce)
	{ 
		level = TEMPERATURE_LEVEL_SUITABLE;
		minVal = m_ConstTempIce;
		maxVal = m_ConstTempHeat;
	}
	else if (temp >= m_ConstTempTopIce)
	{
		level = TEMPERATURE_LEVEL_ICE;
		minVal = m_ConstTempTopIce;
		maxVal = m_ConstTempIce;
	}
	else if (temp >= m_ConstTempFreeze)
	{ 
		level = TEMPERATURE_LEVEL_TOPICE;
		minVal = m_ConstTempFreeze;
		maxVal = m_ConstTempTopIce;
	}
	else
	{ 
		level = TEMPERATURE_LEVEL_FREEZE;
		minVal = TEMPERATURE_MIN_ICE;
		maxVal = m_ConstTempFreeze;
	}
}

void TemperatureManager::ClearPosTemperatureCache(World* world, const WCoord& blockpos, int range)
{
	if (!world || !m_Active) return;
	if (blockpos.y < 0 || blockpos.y - CHUNK_BLOCK_Y >= SECTION_BLOCK_DIM) return;

	WCoord pos = blockpos;
	if (blockpos.y - CHUNK_BLOCK_Y > 0) pos.y -= SECTION_BLOCK_DIM;
	Section* psection = world->getSection(pos);
	if (psection)
	{
		WCoord offpos = blockpos - psection->m_Origin;
		for (int dz = -1; dz <= 1; ++dz)
		{
			int z = ((SECTION_BLOCK_DIM + dz * SECTION_BLOCK_DIM) / 2 - offpos.z) * dz;
			for (int dy = -1; dy <= 1; ++dy)
			{
				int y = ((SECTION_BLOCK_DIM + dy * SECTION_BLOCK_DIM) / 2 - offpos.y) * dy;
				for (int dx = -1; dx <= 1; ++dx)
				{
					Section* neighbourSect = psection->getNeighbourSection(dx, dy, dz);
					if (neighbourSect)
					{
						int x = ((SECTION_BLOCK_DIM + dx * SECTION_BLOCK_DIM) / 2 - offpos.x) * dx;
						int maxLen = max(x, max(y, z));
						if (range >= maxLen)
							neighbourSect->getPosTemperatureMap()->clear();
					}
				}
			}
		}
	}
}

void TemperatureManager::SetAreaTemperatureSource(int index, Section* psection)
{
	int mapid = index >> 30;
	if (mapid < MAPID_GROUND || mapid > MAPID_MENGYANSTAR) return;
	if (!psection) return;
	auto map = psection->GetWritableSharedSectionData().getAreaTemperatureSourceMap();
	if (map->find(index) == map->end()) return;
	auto& data = map->find(index)->second;
	int x, y, z;
	index2XYZ(x, y, z, data._pos);

	if (m_AreaSourceInfoMap.find(mapid) != m_AreaSourceInfoMap.end())
	{
		m_AreaSourceInfoMap[mapid][index] = WCoord(x + psection->m_Origin.x, y, z + psection->m_Origin.z);
	}
	else
	{
		std::unordered_map<int, WCoord> infoMap;
		infoMap[index] = WCoord(x + psection->m_Origin.x, y, z + psection->m_Origin.z);
		m_AreaSourceInfoMap[mapid] = infoMap;
	}
	if (index > m_AreaSourceInfoMapIdx) 
		m_AreaSourceInfoMapIdx = index;
}

int TemperatureManager::AddMobileTemperatureSource(World* world, long long objid, float val, int tick /* = -1 */)
{
	if (!world || !m_Active) return 0;
	if (m_MobileSourceInfoMapIdx > 1000) return 0;// 考虑性能，暂时不给创建超过1000个
	int mapid = world->getCurMapID();
	m_MobileSourceInfoMapIdx++;
	int index = m_MobileSourceInfoMapIdx + (mapid << 30);

	if (m_MobileSourceInfoMap.find(mapid) != m_MobileSourceInfoMap.end())
	{
		m_MobileSourceInfoMap[mapid][index] = MobileSourceTemperatureData(val, tick, objid);
	}
	else
	{
		std::unordered_map<int, MobileSourceTemperatureData> map;
		map[index] = MobileSourceTemperatureData(val, tick, objid);
		m_MobileSourceInfoMap[mapid] = map;
	}
	return index;
}

bool TemperatureManager::RemoveMobileTemperatureSource(int index)
{
	if (index <= 0)
	{
		assert(false);
		return false;
	}
	int mapid = index >> 30;
	if (m_MobileSourceInfoMap.find(mapid) != m_MobileSourceInfoMap.end())
	{
		if (m_MobileSourceInfoMap[mapid].find(index) != m_MobileSourceInfoMap[mapid].end())
		{
			m_MobileSourceInfoMap[mapid].erase(index);
		}
		else
		{
			// 时间到了被清理了
		}
		return true;
	}
	return false;
}

void TemperatureManager::GetMoblieSourceTemperature(World* world, const WCoord& blockpos, float& heat, float& ice, float& maxHeat, float& maxIce)
{
	if (!world || !m_Active || m_MobileSourceInfoMap.empty()) return;
	int mapid = world->getCurMapID();
	if (m_MobileSourceInfoMap.find(mapid) != m_MobileSourceInfoMap.end())
	{
		auto& map = m_MobileSourceInfoMap[mapid];
		for (auto iter = map.begin(); iter != map.end(); ++iter)
		{
			auto& data = iter->second;
			IClientActor* actor = world->getActorMgr()->iFindActorByWID(data._objid);
			if (actor)
			{
				float temp = data._val;
				WCoord pos = CoordDivBlock(actor->getPosition());
				if (temp > 0) CalHeatSource(world, blockpos, pos, temp, heat, maxHeat);
				else CalIceSource(world, blockpos, pos, temp, ice, maxIce);
			}
		}
	}
}


TemperatureNetSys::TemperatureNetSys(PluginManager* p)
{
}

TemperatureNetSys::~TemperatureNetSys()
{
}

bool TemperatureNetSys::Awake()
{
	return true;
}

bool TemperatureNetSys::Init()
{
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_TEMPERATURE_PLAYER_POSTEMP_HC");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_TEMPERATURE_MOB_POSTEMP_HC");
	//SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_TEMPERATURE_AREA_ADD_HC");
	//SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_TEMPERATURE_AREA_REMOVE_HC");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_TEMPERATURE_HC");
	return true;
}

bool TemperatureNetSys::Execute(float dtime)
{
	return true;
}

bool TemperatureNetSys::Shut()
{
	SandBoxManager::getSingleton().unSubscibeEx((IEventExcuteEx*)this, "PB_TEMPERATURE_PLAYER_POSTEMP_HC");
	SandBoxManager::getSingleton().unSubscibeEx((IEventExcuteEx*)this, "PB_TEMPERATURE_MOB_POSTEMP_HC");
	//SandBoxManager::getSingleton().unSubscibeEx((IEventExcuteEx*)this, "PB_TEMPERATURE_AREA_ADD_HC");
	//SandBoxManager::getSingleton().unSubscibeEx((IEventExcuteEx*)this, "PB_TEMPERATURE_AREA_REMOVE_HC");
	SandBoxManager::getSingleton().unSubscibeEx((IEventExcuteEx*)this, "PB_TEMPERATURE_HC");

	for (auto& kv : m_eventCallbacks) {
		SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe(kv.first, kv.second);
	}
	return true;
}

bool TemperatureNetSys::CreateModuleEvent()
{
	return true;
}

void TemperatureNetSys::OnExecute(const char* eventname, void* context, int nLen, unsigned long long userdata)
{
	if (!GetWorldManagerPtr()) return;
	jsonxx::Object* obj = (jsonxx::Object*)context;
	if (0 == strcmp(eventname, "PB_TEMPERATURE_PLAYER_POSTEMP_HC"))
	{
		if (g_pPlayerCtrl && g_pPlayerCtrl->getPlayerAttrib())
		{
			float val = (float)obj->get<jsonxx::Number>("posTemp");
			g_pPlayerCtrl->getPlayerAttrib()->setPosTemperature(val);
		}
	}
	else if (0 == strcmp(eventname, "PB_TEMPERATURE_MOB_POSTEMP_HC"))
	{
		World* world = GetWorldManagerPtr()->getWorld((int)obj->get<jsonxx::Number>("mapid"));
		if (world)
		{
			IClientActor* actor = world->getActorMgr()->iFindActorByWID((int)obj->get<jsonxx::Number>("objid"));
			ClientMob* mob = dynamic_cast<ClientMob*>(actor);
			if (mob && mob->getLivingAttrib()) mob->getLivingAttrib()->setPosTemperature((float)obj->get<jsonxx::Number>("posTemp"));
		}
	}
	else if (0 == strcmp(eventname, "PB_TEMPERATURE_HC"))
	{
		if (g_pPlayerCtrl && g_pPlayerCtrl->getPlayerAttrib())
		{
			float val = (float)obj->get<jsonxx::Number>("finalPosTemp");
			g_pPlayerCtrl->getPlayerAttrib()->setFinalPosTemperature(val);
		}
	}
	//else if (0 == strcmp(eventname, "PB_TEMPERATURE_AREA_ADD_HC"))
	//{
	//	int mapid = (int)obj->get<jsonxx::Number>("mapid");
	//	World* world = GetWorldManagerPtr()->getWorld(mapid);
	//	if (world)
	//	{
	//		long long objid = (long long)obj->get<jsonxx::Number>("objid");
	//		float val = (float)obj->get<jsonxx::Number>("val");
	//		int index = (int)obj->get<jsonxx::Number>("index");
	//		Rainbow::Vector3f pos = Vec3ToVector3Json(obj->get<jsonxx::Array>("pos"));
	//		GetWorldManagerPtr()->getTemperatureMgr()->SetAreaSourceTemperature(index, pos, val, -1, objid);
	//	}
	//}
	//else if (0 == strcmp(eventname, "PB_TEMPERATURE_AREA_REMOVE_HC"))
	//{
	//	int index = (int)obj->get<jsonxx::Number>("index");
	//	GetWorldManagerPtr()->getTemperatureMgr()->RemoveAreaTemperatureSource(index);
	//}
}