
#include "ProjectileLocoMotion.h"
#include "ClientActorProjectile.h"
#include "OgreUtils.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "OgreUtils.h"
#include "ClientActorManager.h"
#include "ClientActor.h"
#include "ActorLocoMotion.h"
#include "DefManagerProxy.h"
#include "EffectManager.h"
#include "special_blockid.h"
#include "ClientActorLiving.h"
#include "BlockCottonrug.h"
#include "ProjectileFactory.h"
#include "ObserverEventManager.h"
#include "ClientPlayer.h"
#include "OgrePhysXManager.h"
#include "SandBoxManager.h"
#include "WorldManager.h"
#include "ActorDriftBottle.h"

#include "RiddenComponent.h"
#include "foundation/PxTransform.h"
#include "ClientActorThornBall.h"
#include "BlockCanvas.h"
#include "BaseClass/EventDispatcher.h"
#include "LuaInterfaceProxy.h"

using namespace MINIW;
using namespace Rainbow;
IMPLEMENT_COMPONENTCLASS(ProjectileLocoMotion)

ProjectileLocoMotion::ProjectileLocoMotion() :  syncYaw(0),syncPitch(0)
{
	m_InGround = false;
	m_TicksInAir = 0;
	m_TicksInGround = 0;
	m_InBlockID = -1;
	m_InBlockData = -1;
	m_BlockPos = WCoord(0, 0, 0);
	m_ShakeTick = 0;
	m_InterplateStep = 0;
	m_BlockFace = 0;
	m_Gravity = 4.0f;
	m_SpeedDecay = 0.4f;
	m_TriggerCondition = TRIGGER_ON_COLLISION;
	m_CanTrigger = true;
	m_BoomerangTime = 0.0f;
	m_BoomerangTime2 = 0.0f;
	m_BoomerangMotion = Rainbow::Vector3f(0, 0, 0);
	m_BoomerangCrossMotion = Rainbow::Vector3f(0, 0, 0);
	m_BoomerangPos = WCoord(0, 0, 0);
}

void ProjectileLocoMotion::setThrowableHeading(const Rainbow::Vector3f &dir, float vel, float deviation)
{
	Vector3f ndir = dir.GetNormalizedSafe();
	ndir.x += GenGaussian()*0.0075f * deviation;
	ndir.y += GenGaussian()*0.0075f * deviation;
	ndir.z += GenGaussian()*0.0075f * deviation;

	m_Motion = ndir*vel;
	Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, m_Motion);
	m_TicksInGround = 0;

	if (g_WorldMgr->m_SurviveGameConfig->physxconfig.enable)
	{
		if (this->m_PhysActor)
		{
			//this->m_PhysActor->SetAngularVelocity(Rainbow::Vector3f(g_WorldMgr->m_SurviveGameConfig->ballconfig.kick_ball_angularX_v, g_WorldMgr->m_SurviveGameConfig->ballconfig.kick_ball_angularY_v, g_WorldMgr->m_SurviveGameConfig->ballconfig.kick_ball_angularZ_v));
			//this->m_PhysActor->SetLinearVelocity(ndir * MOTION2VELOCITY);
			//Rainbow::Quaternionf quat;
			//quat.setEulerAngle(m_RotateYaw,m_RotationPitch,0);
			//
			//Rainbow::Vector3f pos = m_Position.toVector3() + Rainbow::Vector3f(0.0f, m_yOffset, 0.0f);
			//this->m_PhysActor->SetPos(pos, quat);
		
			//this->m_PhysActor->SetAngularVelocity(Rainbow::Vector3f(0, 200, 0));
			this->m_PhysActor->SetLinearVelocity(m_Motion * MOTION2VELOCITY);
		}
	}
}

void ProjectileLocoMotion::tickInAir()
{
	m_TicksInAir++;
	float decay = m_SpeedDecay;
	if (m_InWater && getOwnerActor()->GetItemId() != ITEM_SPOUT)
	{
		decay = 0.2f;
	}

	WCoord mvec = getIntegerMotion(m_Motion);
	if (mvec.x == 0 && mvec.y == 0 && mvec.z == 0)
	{
		m_Motion *= (1- decay);
		m_Motion.y -= m_Gravity;
		if (isBoomerangItem(getOwnerActor()->GetItemId()))
		{
			m_Motion.y -= 2;
		}
		ClientActorProjectile* projectile = static_cast<ClientActorProjectile*>(getOwnerActor());
		if (projectile)
		{
			projectile->onMotionStop();
		}
		return;
	}

	ClientActorProjectile* projectile = static_cast<ClientActorProjectile *>(getOwnerActor());
	if (!projectile)
	{
		return;
	}

	MINIW::WorldRay ray;
	ActorExcludes excludes;
	IntersectResult inter_result1, inter_result2;
	IntersectResult* presult;

	
	if( ProjectileFactory::getTypeFromItemId(projectile->GetItemId()) == PROJECTILE_HOOK || 
		ProjectileFactory::getTypeFromItemId(projectile->GetItemId()) == PROJECTILE_ATTRACT || 
		isBoomerangItem(projectile->GetItemId()) ||
		projectile->GetItemId() == ITEM_AIR_BALL)
	{
		decay = 0.0f;
	}
	int intertype = 0;
	const ProjectileDef* projectileDef = GetDefManagerProxy()->getProjectileDef(projectile->GetItemId());

	//沙球击中水
	if (projectileDef->ID == 15073)
	{
		WCoord pos = CoordDivBlock(m_Position);
		int blockid = m_pWorld->getBlockID(pos);
		if (BlockMaterialMgr::isWater(blockid))//(blockid == BLOCK_FLOW_WATER || blockid == BLOCK_STILL_WATER)
		{
			m_pWorld->setBlockAll(pos, BLOCK_SOLIDSAND, 0);
			projectile->setNeedClear();
		}
	}

//炮弹击中水爆炸
	if (projectileDef->ID == 15076 || projectileDef->ID == 11424)
	{
		WCoord pos = CoordDivBlock(m_Position);
		int blockid = m_pWorld->getBlockID(pos);
		if (BlockMaterialMgr::isWater(blockid))//(blockid == BLOCK_FLOW_WATER || blockid == BLOCK_STILL_WATER)
		{
			projectile->playEffect();
			projectile->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/sea_40_baozha.ent", m_Position, 100, GenRandomFloat() * 360.0f, 0, false, 50);
			projectile->setNeedClear();
			return;
		}
	}

	if (projectileDef->ID == BLOCK_FAR_DRIFTBOTTLE || projectileDef->ID == BLOCK_DRIFTBOTTLE)
	{
		WCoord pos = CoordDivBlock(m_Position);
		int blockid = m_pWorld->getBlockID(pos);
		if (BlockMaterialMgr::isWater(blockid))//(blockid == BLOCK_STILL_WATER || blockid == BLOCK_FLOW_WATER)
		{
			int biomeid = m_pWorld->getBiomeId(pos.x, pos.z);
			int mobid = projectile->GetItemId() == BLOCK_FAR_DRIFTBOTTLE ? 3231 : 3230;
			ClientMob* mob = projectile->getActorMgr()->spawnMob((pos + WCoord((float)0.5, 1, 0.5)) * BLOCK_SIZE, mobid, false, false);
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(projectile->getShootingActor());
			ActorDriftBottle* bottleMob = dynamic_cast<ActorDriftBottle*>(mob);
			if (bottleMob)
			{
				bottleMob->setUserData(projectile->m_UserDataStr);
			}
			if (biomeid == BIOME_DEEPOCEAN || biomeid == BIOME_OCEAN || biomeid == BIOME_RIVER || biomeid == BIOME_BEACH)
			{
				if (player)
				{
					MINIW::ScriptVM::game()->callFunction("DriftBottleUpdateLettersRemote", "is", player->getUin(), projectile->m_UserDataStr.c_str());
					if (mob != NULL)
					{
						mob->setDieTick(20);
					}
				}
			}
			else
			{
				//ClientItem* item = projectile->getActorMgr()->spawnItem((pos + WCoord(0.5, 1, 0.5)) * BLOCK_SIZE, BLOCK_DRIFTBOTTLE, 1);
				//item->m_ItemData.userdata_str = projectile->m_UserDataStr;
				//item->setEffect("111");
				if (player)
				{
					MINIW::ScriptVM::game()->callFunction("DriftBottleUseReport", "isi", player->getUin(), projectile->m_UserDataStr.c_str(), 3);
				}
			}
			projectile->setNeedClear();
			return;
		}
		else
		{
			//
		}
	}

	if (isBoomerangItem(projectile->GetItemId()))//回旋镖处理
	{
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(projectile->getShootingActor());
		if (m_InWater && projectile->m_BoomerangState <= 1)
		{
			projectile->m_BoomerangState = 4;//在水中
			projectile->stopEntityModelAnim();//停止旋转
			projectile->playEntityModelAnim(100100);
		}

		if (!m_InWater && projectile->m_BoomerangState == 0 && (m_Motion.x != 0 || m_Motion.z != 0))
		{
			projectile->m_BoomerangState = 1;
			m_BoomerangMotion = m_Motion;
			m_BoomerangCrossMotion = CrossProduct(Rainbow::Vector3f(0, 1.0f, 0), m_Motion);//垂直前进方向的量

			m_BoomerangTime = 64.0f;//最大时长
			float speed = m_Motion.Length();
			float mult = 1.0f; //时间倍数最大为1；
			float r = Sqrt(m_BoomerangCrossMotion.x * m_BoomerangCrossMotion.x + m_BoomerangCrossMotion.z * m_BoomerangCrossMotion.z);
			m_BoomerangCrossMotion *= speed / r * 0.4f; //修正左右方向的值大小，倍率为前进方向的0.4倍

			m_Motion += m_BoomerangCrossMotion;

			if (projectileDef)
			{
				mult = speed / projectileDef->InitSpeed;
			}

			m_BoomerangTime2 = ceil(m_BoomerangTime * mult / 2.0f);//时长的一半
			m_BoomerangTime = 2.0f * m_BoomerangTime2;//确定为time2得两倍
			m_BoomerangMotion *= 2.0f / m_BoomerangTime;
			m_BoomerangCrossMotion *= 4.0f / m_BoomerangTime;

			if (player)
				m_BoomerangPos = player->getPosition();
			else
				m_BoomerangPos = WCoord(0, 0, 0);
		}

		if (projectile->m_BoomerangState == 3)//未接住
		{
			m_Motion -= m_BoomerangMotion;
			m_Motion += m_BoomerangCrossMotion;
			m_Motion.y -= 2;
		}
		else if (projectile->m_BoomerangState == 2)//飞回阶段
		{
			if (m_BoomerangTime <= 0)
				projectile->m_BoomerangState = 3;
			m_BoomerangTime -= 1.0f;
			m_Motion -= m_BoomerangMotion;
			m_Motion += m_BoomerangCrossMotion;
			if (player)
			{
				//每当人物移动，同时回旋镖也移动相等单位；
				WCoord pos = player->getPosition() - m_BoomerangPos;
				m_Position += pos;
				m_BoomerangPos = player->getPosition();
			}
		}
		else if (projectile->m_BoomerangState == 1)
		{
			m_BoomerangTime -= 1.0f;
			m_Motion -= m_BoomerangMotion;
			m_Motion -= m_BoomerangCrossMotion;
			if (m_BoomerangTime <= m_BoomerangTime2)
			{
				projectile->m_BoomerangState = 2;
				if (player)
				{
					//飞回开始，人物移动过得距离均分给回旋镖motion。
					WCoord pos = player->getPosition() - m_BoomerangPos;
					Vector3f tickMotion = pos.toVector3() / m_BoomerangTime2;
					m_Motion += tickMotion;
					m_BoomerangPos = player->getPosition();
				}
			}
			if (m_BoomerangTime == m_BoomerangTime2 * 2 - 4.0f) //慢4个tick播放才能同步到
				projectile->playEntityModelAnim(100105, 0);//播放旋转动作
		}
		else if (projectile->m_BoomerangState == 4)
		{
			decay = 0.2f;
		}

		if (projectile->m_BoomerangState != 4)
		{
			mvec = getIntegerMotion(m_Motion);
		}
	}

	if (projectile->GetItemId() == ITEM_HARPOON || projectile->GetItemId() == ITEM_OCEANARROW) {
		decay = 0.0f;
	}
	// 羊毛地毯特殊处理：和彩蛋、彩蛋枪的子弹碰撞
	if (projectileDef && IsColorableItem(projectileDef->ID))
	{
		if (projectilePickAll(mvec, ray, &inter_result1, excludes, PICK_METHOD_CLICK) == 1
			&& dynamic_cast<CottonrugMaterial*>(g_BlockMtlMgr.getMaterial(m_pWorld->getBlockID(inter_result1.block))))
		{
			intertype = 1;
			presult = &inter_result1;
		}
	}

	if (intertype == 0 && getOwnerActor()->getObjType() != OBJ_TYPE_BLOCK_LASER)
	{
		intertype = projectilePickAll(mvec, ray, &inter_result2, excludes, PICK_METHOD_SOLID);
		presult = &inter_result2;
	}

	if (intertype == 1 && isBoomerangItem(projectile->GetItemId()) && projectile->m_BoomerangState == 2)
	{
		//回旋镖飞回过程无视任何方块碰撞
		intertype = 0;
	}
	
	if(intertype == 1) //block
	{
		
		m_BlockPos = presult->block;
		m_BlockFace = presult->face;
		m_InBlockID = m_pWorld->getBlockID(m_BlockPos);
		bool canPenetrate = projectile->checkCanPenetrate(m_InBlockID); //是否能击碎/穿透
		m_InGround = !canPenetrate;
		
		ClientActor * owner = projectile->getShootingActor();
		if (owner)
		{
			GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_PROJECTILE_COLLIDE_BLOCK, 0, owner->getObjId()&0xffffffff, (char*)&m_BlockPos, sizeof(m_BlockPos));
		}
		//impact with not loadded block
		if (m_InBlockID == 4095)
		{
			getOwnerActor()->setNeedClear();
		}
		m_InBlockData = m_pWorld->getBlockData(m_BlockPos);

		//Harcode for arrow shake
		if (projectile->GetItemId() == ITEM_ARROW)
			m_ShakeTick = 7;

		if (isBoomerangItem(projectile->GetItemId()))
		{
			projectile->m_BoomerangState = 0;
			projectile->stopEntityModelAnim();//停止旋转
			projectile->playEntityModelAnim(100100);
		}

		//计算偏移
		Vector3f targetpos = ray.m_Origin.toVector3() + ray.m_Dir * presult->collide_t;
		mvec = Vector3f::zero;
		m_Position = WCoord(targetpos);

		//为了防止能量矛穿刺2个方块以上，强制插在第一个方块上（特殊处理）
		if (projectile->GetItemId() == ITEM_ENERGYSPEAR)
		{
			m_Position.y = m_BlockPos.y * BLOCK_SIZE + 90;
		}

		projectile->onImpactWithBlock(&m_BlockPos, presult->face);

		auto material = g_BlockMtlMgr.getMaterial(m_InBlockID);
		if (material && material->DoOnActorCollidedWithBlock( m_pWorld, m_BlockPos, getOwnerActor()))
		{
			getOwnerActor()->clearCollideBlock();
			getOwnerActor()->setCollideBlockState(m_BlockPos, m_InBlockID);
		}
		else
		{
			getOwnerActor()->clearCollideBlock();
		}
		//如果是帐篷,则给睡在里面的玩家一次伤害
		if (projectile->m_AttackPoints > 0)
		{
			if (m_InBlockID == BLOCK_CANVAS)
			{
				BlockCanvas* blockmtl = dynamic_cast<BlockCanvas*>(g_BlockMtlMgr.getMaterial(BLOCK_CANVAS));
				if (blockmtl && blockmtl->IsBedOccupied(m_pWorld, m_BlockPos, m_InBlockData))
				{
					WCoord occupidPos = CoordDivBlock(blockmtl->getSleepPosition(m_pWorld, m_BlockPos));
					if (!m_pWorld->isRemoteMode())
					{
						ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
						if (!actorMgr) return ;
						auto players = actorMgr->getAllPlayer();
						ClientPlayer* target = NULL;

						for (auto player : players)
						{
							if (player && !player->isDead() && player->isRestInBed() && player->getWorld() && player->getCurMapID() == m_pWorld->getCurMapID())
							{
								WCoord playerPosition = CoordDivBlock(player->getPosition());
								if (playerPosition == occupidPos)
								{
									target = player;
									break;
								}
							}
						}
						if (target)
						{
							OneAttackData atkdata;
							//memset(&atkdata, 0, sizeof(atkdata));
							atkdata.damage_armor = true;
							// 新伤害计算系统 code-by:liya
							if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
							{
								atkdata.atkTypeNew = projectile->m_atkType;
								memcpy(atkdata.atkPointsNew, projectile->m_AttackPointsNew, sizeof(projectile->m_AttackPointsNew));
								memcpy(atkdata.explodePoints, projectile->m_ExplodePoints, sizeof(projectile->m_ExplodePoints));
								atkdata.charge = projectile->m_strength;
								//atkdata.damping = 1.0f;
							}
							else
							{
								atkdata.atktype = ATTACK_RANGE;
								atkdata.atkpoints = projectile->m_AttackPoints;
							}
							atkdata.fromplayer = NULL;
							target->attackedFrom(atkdata, NULL);
						}
					}
				}
			}
		}
		// 观察者事件接口
		//ObserverEvent_Block obevent(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z, m_InBlockID, (long long)getOwnerActor()->getObjId());
		//ObserverEventManager::getSingleton().OnTriggerEvent("Block.ActorCollide", &obevent);
	}
	else if(intertype == 2) //actor
	{
		//计算偏移
		Vector3f targetpos = ray.m_Origin.toVector3() + ray.m_Dir * presult->collide_t;
		//同一个team的actor不触发onImpactWithActor
		//const ProjectileDef* projectileDef = GetDefManagerProxy()->getProjectileDef(projectile->GetItemId());		
		//projectile->m_TriggerPos = inter_result.actor->getEyePosition().toVector3();
		ClientActor * owner = projectile->getShootingActor();
		if (owner)
		{
			WCoord blockPos = CoordDivBlock(presult->actor->getPosition());
			blockPos.y -= 1;
			GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_PROJECTILE_COLLIDE_BLOCK, 0, owner->getObjId()&0xffffffff, (char*)&blockPos, sizeof(blockPos));
		}
		
		if (isBoomerangItem(projectile->GetItemId()) && presult->actor)
		{
			//回旋镖飞出去过程对飞行路线上接触到的所有生物造成伤害
			if (projectile->getBeShootedActor() && projectile->m_HasImpackActor == true && projectile->getBeShootedActor()->getObjId() != presult->actor->getObjId() && projectile->m_BoomerangState >= 1)
				projectile->m_HasImpackActor = false;
			//不对自己骑着得坐骑造成伤害
			if (owner)
			{
				auto RidComp = owner->getRiddenComponent();
				if (RidComp && RidComp->isRiding() && RidComp->getRidingActorObjId() == presult->actor->getObjId())
					projectile->m_HasImpackActor = true;
			}
		}

		// 穿刺技能
		//if (projectile->passActor() && projectile->m_HasImpackActor)
		//{
		//	projectile->m_HasImpackActor = false;
		//}
		
		if (!projectile->m_HasImpackActor || projectile->canPassActor())
		{
			if (presult->actor->getObjId() == getOwnerActor()->getMasterObjId())
			{
				intertype = 0;
			}
			else
			{
				m_Position = targetpos;
				projectile->onImpactWithActor(presult->actor->GetActor(), presult->actorpart);

				if (projectile->GetItemId() != ITEM_BLOCK_LASER)
				{
					if (isBoomerangItem(projectile->GetItemId()))
					{
						//不变
						m_Motion *= 1;
					}
					else if (projectile->GetItemId() == ITEM_THORNBALL)
					{
						auto thorn = dynamic_cast<ClientActorThornBall*>(getOwnerActor());
						if (thorn)
						{
							//直接落地
							thorn->setIsDrop(true);
						}
						return;
					}
					else
					{
						if (!projectile->canPassActor())
						{
							m_Motion.x = 0;
							m_Motion.y = 0;
							m_Motion.z = 0;
						}
					}
				}

				ClientPlayer *player = dynamic_cast<ClientPlayer *>(projectile->getShootingActor());
				if (player)
					player->attackHitOnTrigger(presult->actor->getObjId(), presult->actor->getDefID());

			}
		}
		if (!projectile->canPassActor() && !isBoomerangItem(projectile->GetItemId()))
		{
			mvec = Vector3f::zero;
			if (getOwnerActor()->getObjType() != OBJ_TYPE_HOOK)
			{
				m_Position = WCoord(targetpos);
			}
		}
	}

	if (intertype == 2 && (1 == projectile->m_ProjectileDef->TriggerCondition || 3 == projectile->m_ProjectileDef->TriggerCondition) && 0 == projectile->m_ProjectileDef->Pickable)
	{
 		if (getOwnerActor()->getObjType() != OBJ_TYPE_HOOK)
		{
			ItemDef * item =  GetDefManagerProxy()->getItemDef(projectile->m_ProjectileDef->ID);
			if (item && item->gamemod)
			{
				if (projectile->m_ProjectileDef->TriggerDelay <= 0.001f) //插件投掷物设置延迟释放
				{
					projectile->setNeedClearEx();
				}
			}
			else
			{
				projectile->setNeedClearEx();
			}

		}
	}
	if (g_WorldMgr && g_WorldMgr->m_SurviveGameConfig && g_WorldMgr->m_SurviveGameConfig->physxconfig.enable == false || m_hasPhysActor == false)
	{
		m_Position += mvec;
	}

	//空气球离开了水就直接爆炸
	if (intertype == 0 && projectileDef->ID == ITEM_AIR_BALL)
	{
		WCoord pos = CoordDivBlock(m_Position);
		int blockid = m_pWorld->getBlockID(pos);

		if (blockid == BLOCK_AIR)
		{
			projectile->onImpactWithBlock(&pos, 0);
		}
	}
	Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, m_Motion);

	m_Motion *= (1 - decay);
	m_Motion.y -= m_Gravity;

	if (isBoomerangItem(projectile->GetItemId()))
	{
		if (projectile->m_BoomerangState == 0 || projectile->m_BoomerangState == 4)
			m_Motion.y -= 2;
	}

	m_CanTrigger = true;
}

void ProjectileLocoMotion::tickInGround()
{
	int blockid = m_pWorld->getBlockID(m_BlockPos);
	int blockdata = m_pWorld->getBlockData(m_BlockPos);

	if(m_InBlockID==blockid && m_InBlockData==blockdata)
	{
		m_TicksInGround++;
		if(m_TicksInGround >= 1200 && getOwnerActor()->IsAutoClear())
		{
			getOwnerActor()->setNeedClear();
		}
	}
	else
	{
		m_Motion.x = 0;
		m_Motion.y = 0;
		m_Motion.z = 0;
		m_InGround = false;
		m_Motion.x *= GenRandomFloat()*0.2f;
		m_Motion.y *= GenRandomFloat()*0.2f;
		m_Motion.z *= GenRandomFloat()*0.2f;
		m_TicksInGround = 0;
		m_TicksInAir = 0;
	}
}

void ProjectileLocoMotion::tick()
{
	//LOG_Vector3i(m_Motion);
	if (getOwnerActor()->getObjType() != OBJ_TYPE_BLOCK_LASER && getOwnerActor()->getObjType() != OBJ_TYPE_LASER)
	{
		ActorLocoMotion::tick();
	}
	else
	{
		if(getOwnerActor()->needClear()) return;
		if(!m_pWorld->isRemoteMode() && m_Position.y < -64*BLOCK_SIZE && !getOwnerActor()->isDead())
		{
			getOwnerActor()->kill();
		}
	}
	if (m_pWorld->isRemoteMode())
	{
		if (m_hasPhysActor)
		{
			if(m_PosRotationIncrements > 0)
			{
				m_Position = m_Position + (m_ServerPos - m_Position)/m_PosRotationIncrements;
				m_RotateQuat = Slerp(m_RotateQuat, m_ServerRot, 1.0f/m_PosRotationIncrements);

				//Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, -m_RotateQuat.GetAxisZ());
				if (m_PhysActor)
				{
					m_PhysActor->SetPos(m_Position.toVector3(), m_ServerRot);
				}
				m_PosRotationIncrements--;
			}
			else if (m_InterplateStep > 0)
			{
				m_Position = m_Position + (syncPos - m_Position) / m_InterplateStep;
				if (m_PhysActor)
				{
					m_PhysActor->SetPos(m_Position.toVector3(), m_ServerRot);
				}
				m_InterplateStep--;
			}
			checkPhysWorld();
			return;
		}
		else
		{
			//m_Position = syncPos;
			m_RotateYaw = syncYaw;
			m_RotationPitch = syncPitch;

			if (m_InterplateStep > 0)
			{
				m_Position = m_Position + (syncPos - m_Position) / m_InterplateStep;
				//m_RotateYaw = m_RotateYaw + WrapAngleTo180(syncYaw - m_RotateYaw) / m_InterplateStep;
				//m_RotationPitch = m_RotationPitch + (syncPitch - m_RotationPitch) / m_InterplateStep;
				//LOG_INFO("interplateStep %d", m_InterplateStep);
				m_InterplateStep--;
			}
			else
			{
				m_Position = syncPos;

				/*m_Position = m_Position + getIntegerMotion(m_Motion);

				if (m_OnGround) m_Motion *= 0.5f;
				Vector3 s_MotionDecay(0.9f, 0.98f, 0.9f);
				m_Motion *= s_MotionDecay;*/
			}
		}
	}
	else
	{
		if (g_WorldMgr && g_WorldMgr->m_SurviveGameConfig && g_WorldMgr->m_SurviveGameConfig->physxconfig.enable)
		{
			if(m_PhysActor)
			{
				Rainbow::Vector3f pos;
				Rainbow::Quaternionf quat;
				/*m_PhysActor->GetPos(pos, quat);*/
				const RigidBaseActor* physActor = m_PhysActor;
				physActor->GetTransform()->GetWorldPositionAndRotation(pos, quat);
				/*PxTransform trans;
				const PxRigidDynamic* physActor = static_cast<PxRigidDynamic *>(m_PhysActor->GetPxRigidActor());
				trans = physActor->getGlobalPose();
				pos = Vector3f(trans.p.x, trans.p.y, trans.p.z);
				quat = Quaternionf(trans.q.x, trans.q.y, trans.q.z, trans.q.w);*/
				//m_Motion = pos - m_Position.toVector3();
				//改为用线速度设置Motion，通过前后位置差设置的方法会导致一些情况下碰撞检测出现问题 by:Jeff
				m_Motion = m_PhysActor->GetLinearVelocity() / MOTION2VELOCITY;
				m_Position = pos;
				//m_Position = pos - quat*Rainbow::Vector3f(0, m_BoundBox.dim.y/2, 0);
				m_RotateQuat = quat;

				checkPhysWorld();
			}
		}

		if (m_ShakeTick > 0) --m_ShakeTick;
		if (m_InGround) tickInGround();
		else tickInAir();
	}
}

void ProjectileLocoMotion::setPosition(int x, int y, int z)
{
	m_OldPosition = m_Position;
	m_Position.x = x;
	m_Position.y = y;
	m_Position.z = z;
	if (m_PhysActor)
		m_PhysActor->SetPos(Rainbow::Vector3f((float)x, (float)y, (float)z), m_RotateQuat);
}
int ProjectileLocoMotion::projectilePickAll(WCoord mvec, MINIW::WorldRay& input_ray, IntersectResult* result, ActorExcludes& excludes, int pickmethod)
{
	ClientActorProjectile* projectile = static_cast<ClientActorProjectile*>(getOwnerActor());
	if (!projectile)
	{
		return -1;
	}
	//后面应该加入team的判断
	int excludeTeam = 0;
	ActorLiving* living = dynamic_cast<ActorLiving*>(projectile->getShootingActor());
	if (living && living->getDefID() >= 3510 && living->getDefID() <= 3514)
	{
		excludeTeam = living->getTeam();
	}
	//不要射到自己
	excludes.addActor(getOwnerActor());
	if (m_TicksInAir <= 5)
	{
		ClientActor* owner = projectile->getShootingActor();

		if (owner) excludes.addActorWithRiding(owner);
	}
	input_ray.m_Origin = m_Position.toWorldPos();
	input_ray.m_Dir = mvec.toVector3();
	input_ray.m_Range = input_ray.m_Dir.Length();
	input_ray.m_Dir /= input_ray.m_Range;

	std::function<bool(const WCoord&)> filter = [this](const WCoord& v) -> bool {
		int blockId = m_pWorld->getBlockID(v);
		const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockId);
		if (def and def->Type == "schoolfence" || def->Type == "centerdoor")  //这种类型的方块，投射物可以穿透过去
			return true;

		return false;
	};

	int intertype = (int)m_pWorld->pickAll(input_ray, result, excludes, (PICK_METHOD)pickmethod, excludeTeam, filter, false);
	return intertype;
}

void ProjectileLocoMotion::OnCollisionEnter(const Rainbow::EventContent* collision)
{
	if (getOwnerActor())
	{
		ClientActorProjectile* projectile = static_cast<ClientActorProjectile*>(getOwnerActor());
		projectile->playSoundByPhysCollision();
	}
}

