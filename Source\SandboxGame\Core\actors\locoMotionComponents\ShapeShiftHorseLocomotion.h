#ifndef __SHAPE_SHIFT_HORSE_LOCOMOTION_H__
#define __SHAPE_SHIFT_HORSE_LOCOMOTION_H__
#include "HorseLocomotion.h"

class ShapeShiftHorseLocomotion : public HorseLocomotion
{
public:
	DECLARE_COMPONENTCLASS(ShapeShiftHorseLocomotion)

	ShapeShiftHorseLocomotion() : m_ShapeShiftYaw(0), m_ShapeShiftPitch(0)
	{
		m_PosRotationIncrements = 0;
		speedMultiplier = 20.0f;

		m_MaxSpeed = 0;
		m_fSpeedUpTime = 0;
		m_fSpeedUpVal = 0;
		m_bWaitStopEffect = false;
		m_bRabbitRun = false;
	}

	virtual ~ShapeShiftHorseLocomotion()
	{

	}
	virtual void tick();

	bool m_bRabbitRun;
	int m_PosRotationIncrements;
	WCoord m_ShapeShiftHorsePos;
	float m_ShapeShiftYaw;
	float m_ShapeShiftPitch;
	float speedMultiplier;
	float m_MaxSpeed;
	float m_fSpeedUpTime;
	float m_fSpeedUpVal;
	bool m_bWaitStopEffect;
};


#endif