#include "BoundCoordCheck.h"

#include <cstdarg>
#include <algorithm>

#include "world.h"
#include "ClientActor.h"
#include "ActorLocoMotion.h"
#include "SandboxContext.h"

using namespace MNSandbox;


BoundCoordCheck::BoundCoordCheck(int min_xz_limit, int min_y_limit, int blockCnt, ...)
:m_minXZLimit(min_xz_limit),m_minYLimit(min_y_limit)
{
	m_blockids.resize(blockCnt);

    va_list params;
    va_start(params, blockCnt);
    for(int i = 0; i < blockCnt; i++){
    	m_blockids[i] = va_arg(params, int);
    }
    va_end(params);
}

bool BoundCoordCheck::isInRange(ClientActor* pActor, BlockEnvEffectBase* pEffect)
{
	if (!pActor)
	{
		return false;
	}
	ClientActor* owner = pActor; //context.GetData_Usertype<ClientActor>("owner");

	World* pWorld     = owner->getWorld();
	assert(pWorld != nullptr);
	ActorLocoMotion* locomotion = owner->getLocoMotion();
	assert(locomotion != nullptr);

	int boundWidth  = locomotion->m_BoundSize;
	int boundHeight = locomotion->m_BoundHeight;

	CollideAABB box;
	locomotion->getCollideBox(box);
	int dxz = std::min(boundWidth / 2, m_minXZLimit);
	int dy  = std::min(boundHeight / 2, m_minYLimit);
	box.expand(-dxz, -dy, -dxz);

	int resid2 = m_blockids.size() > 1 ? m_blockids[1] : -1;
	return pWorld->hasBlocksInCoordRange(box.minPos(), box.maxPos(), m_blockids[0], resid2);

}