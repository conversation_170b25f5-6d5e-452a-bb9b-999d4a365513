﻿#include "ActorBodyUIComponent.h"
#include "Mesh/LegacySkinMeshRenderer.h"
#include "BlockScene.h"
#include "ClientItem.h"
#include "SandBoxManager.h"
#include "Text3D/UIIn3DSceneMerger.h"
#include "Text3D/ProgressBar3D.h"
#include "Text3D/MoveByText.h"
#include "Text3D/NameText3D.h"
#include "Text3D/Voice3D.h"
#include "ModelItemMesh.h"
#include "GameMode.h"
#include "OgreUtils.h"
#include "special_blockid.h"
#include "PlayerControl.h"
#include "MpActorManager.h"
#include "GameCamera.h"
#include "CameraManager.h"
#include "CustomMotionMgr.h"
#include "ImportCustomModelMgr.h"
#include "GlobalFunctions.h"
#include "ClientInfoProxy.h"
#include "Pkgs/PkgUtils.h"
#include "Entity/LegacySequenceMap.h"
#include "PlayerAttrib.h"
#include "Text3D/MusicClubChatBubble3D.h"
#include "Texture/LegacyTextureUtils.h"
#include "CarryComponent.h"
#include "RiddenComponent.h"
#include "ClientActorFuncWrapper.h"
#include "ParticlesComponent.h"
#include "EffectComponent.h"
#include "ActorSandworm.h"
#include "Text3D/ImageBoard3D.h"
#include "backpack.h"
#include "CustomModel.h"
#include "ActorLocoMotion.h"
#include "Entity/ModelRenderer.h"
#include "Entity/ModelAnimationPlayer.h"
#include "Core/nodes/chatBubble/SandboxChatBubbleManager.h"
#include "SoundComponent.h"
#include "SandboxGameDef.h"
#if HUD_TEST
#include "HUDUI/Render/HUDLevitationFontRender.h"
#include "HUDUI/Render/HUDTitleRender.h"
#endif
#include "ActorHorse.h"
#include "ClientFlyMob.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

ActorBodyUIComponent::ActorBodyUIComponent(ActorBody* actorBody) : 
	m_ActorBody(actorBody),
	m_NameDispObj(NULL),
	m_HPProgressObj(NULL),
	m_HPChangeEffMgr(NULL),
	m_HPVisible(false),
	m_BillBoardText(NULL),
	m_BillBoardBkg(NULL),
	m_AchieveBoard(NULL),
	m_AchieveIcon(NULL),
	m_TitleIcon(NULL),
	m_Dialog(NULL),
	m_NeedItemMerger(NULL),
	m_NeedItemBoard(NULL),
	m_NeedItemIcon(NULL),
	m_NeedItemIconPtr(nullptr),
	m_bInviteActStart(false), //20211020 codeby：wangyu 新增互动动作开始标记
	m_VipIcon(NULL),
	m_VoiceObj(nullptr),
	m_MusicClubChatBubbleObj(NULL),
	m_ArmorProgressObj(NULL),
	m_ArmorVisible(false),
	m_ImageBoard(NULL)
{
	m_NameDispObj = NULL;
	m_HPProgressObj = NULL;
	m_HPChangeEffMgr = NULL;
	m_HPVisible = false;
	m_NameBlickStartTime = 0;
	//2021-12-20 codeby: wangyang vip变量初始化
	m_isVip = false;
}

ActorBodyUIComponent::~ActorBodyUIComponent()
{
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_NameDispObj);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_HPProgressObj);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_MusicClubChatBubbleObj);
	ENG_DELETE(m_HPChangeEffMgr);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_BillBoardText);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_BillBoardBkg);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_AchieveBoard);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_AchieveIcon);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_TitleIcon);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_Dialog);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_NeedItemBoard);
	m_NeedItemMerger = NULL;
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_NeedItemIconPtr);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_VipIcon);
	if (m_VoiceObj) {
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_VoiceObj);
	}
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_ArmorProgressObj);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_ImageBoard);
}


static unsigned char s_TeamNameColor[7][3] =
{
	{147,142,146}, {237,73,22}, {22,149,238}, {26,238,22}, {237,223,22}, {237,144,22}, {194,22,237}
};

unsigned int GetTeamNameColor(int teamid)
{
	return (int(s_TeamNameColor[teamid][0]) << 16) | (int(s_TeamNameColor[teamid][1]) << 8) | (int(s_TeamNameColor[teamid][2]) << 0);
}

void ActorBodyUIComponent::setDispayName(const char* name, int teamid, int texid/* =0 */, const char* title/* = "" */)
{
#ifndef IWORLD_SERVER_BUILD	
	if (name)
	{
		// 初始化昵称
		if (m_NameDispObj == NULL)
		{
			if (texid == 1 || texid == 2)
			{
				m_NameDispObj = NameText3D::Create(28, 160, 28, 80.0f, 14, true, false, "ui/mobile/texture0/ingame.png", texid);
			}
			else if (m_ActorBody->isPlayer())
			{
				m_NameDispObj = NameText3D::Create(16, 320, 20, 160.0f, 10.0f, true, false, "ui/mobile/texture0/ingame.png", texid);
			}
			else
			{
				m_NameDispObj = NameText3D::Create(16, 1400, 150, 700.0f, 75.0f, true, false, "ui/mobile/texture0/ingame.png", texid);
			}
			if (m_ActorBody->m_World && m_NameDispObj)
			{
				m_NameDispObj->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
				m_NameDispObj->SetVisibleDistance(700);
				m_NameDispObj->AttachToScene(m_ActorBody->m_World->getScene());
				ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_ActorBody->m_OwnerActor);
				if (!player)
				{
					m_NameDispObj->setVisible(false);
				}
			}
		}
		 
		InitNeedItemBoard(texid);
		InitNeedItemIconPtr(texid);

		if (teamid == JUDGE_TEAM_ID)
			teamid = 0;

		m_TexId = texid;
		m_TeamId = teamid;

		if (teamid > 0)
		{
			if (m_NameDispObj) m_NameDispObj->setZTest(true);
			if (m_TitleIcon) m_TitleIcon->setZTest(true);
		}

		m_NameDispObj->setZTest(true);

		if (m_NameDispObj)
		{
			m_NameDispObj->setText(name);
			if (m_ActorBody->m_OwnerActor && m_ActorBody->m_OwnerActor->getObjType() != OBJ_TYPE_VILLAGER)  //野人不修改昵称颜色 
			{
				if (teamid >= 0 && teamid < MAX_GAME_TEAMS) // 裁判也不修改颜色
				{
					if (texid == 1 || texid == 2)
					{
						m_NameDispObj->setTextColor(246, 222, 153);
					}
					else
					{
						if (teamid > 0 || m_isVip == false)//2021-12-20 codeby: wangyang ?????????????????????????????
						{
							m_NameDispObj->setTextColor(s_TeamNameColor[teamid][0], s_TeamNameColor[teamid][1], s_TeamNameColor[teamid][2]);
						}
					}
				}
			}
			m_NameDispObj->setNameTexId(texid);
		}

		// 初始化悦享赛事勋章
		if (!m_TitleIcon && title != "")
		{
			this->setBPTitleIconName(title);
		}
	}
	else
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_NameDispObj);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_AchieveIcon);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_TitleIcon)
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_AchieveBoard);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_Dialog);
		//DESTORY_GAMEOBJECT_BY_COMPOENT(m_NeedItemIcon);
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_NeedItemBoard);
		m_NeedItemMerger = NULL;
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_NeedItemIconPtr);
		m_NeedItemIconPtr = nullptr;
	}
#endif		
}

void ActorBodyUIComponent::onEnterWorld(World* pworld)
{
	if (m_NameDispObj)
	{
		if (!m_NameDispObj->IsInScene())
		{
			m_NameDispObj->AttachToScene(pworld->getScene());
		}
	}

	if (m_HPProgressObj)
	{
		if (!m_HPProgressObj->IsInScene())
		{
			m_HPProgressObj->AttachToScene(pworld->getScene());
		}
	}

	if (m_ArmorProgressObj)
	{
		if (!m_ArmorProgressObj->IsInScene())
		{
			m_ArmorProgressObj->AttachToScene(pworld->getScene());
		}
	}

	if (m_ImageBoard)
	{
		if (!m_ImageBoard->IsInScene())
		{
			m_ImageBoard->AttachToScene(pworld->getScene());
		}
	}

	if (m_HPChangeEffMgr)
	{
		m_HPChangeEffMgr->setGameScene(pworld->getScene());
	}

	if (m_NeedItemBoard)
	{
		m_NeedItemBoard->AttachToScene(pworld->getScene());
	}

	if (m_AchieveIcon)
	{
		m_AchieveIcon->AttachToScene(pworld->getScene());
	}
	if (m_TitleIcon)
	{
		m_TitleIcon->AttachToScene(pworld->getScene());
	}
	if (m_AchieveBoard)
	{
		m_AchieveBoard->AttachToScene(pworld->getScene());
	}

	if (m_Dialog)
	{
		m_Dialog->AttachToScene(pworld->getScene());
	}

	if (m_VipIcon)
	{
		m_VipIcon->AttachToScene(pworld->getScene());
	}
}

void ActorBodyUIComponent::onLeaveWorld()
{
	if (m_HPProgressObj)
	{
		m_HPProgressObj->DetachFromScene();
	}

	if (m_NameDispObj)
	{
		m_NameDispObj->DetachFromScene();
	}

	if (m_HPChangeEffMgr)
	{
		m_HPChangeEffMgr->setGameScene(NULL);
	}
	if (m_MusicClubChatBubbleObj)
	{
		m_MusicClubChatBubbleObj->DetachFromScene();
	}
	if (m_BillBoardText)
	{
		m_BillBoardText->DetachFromScene();
	}
	if (m_BillBoardBkg)
	{
		m_BillBoardBkg->DetachFromScene();
	}

	if (m_NeedItemBoard)
	{
		m_NeedItemBoard->DetachFromScene();
	}

	if (m_NeedItemIconPtr)
	{
		m_NeedItemIconPtr->DetachFromScene();
	}

	if (m_AchieveIcon)
	{
		m_AchieveIcon->DetachFromScene();
	}
	if (m_TitleIcon)
	{
		m_TitleIcon->DetachFromScene();
	}
	if (m_AchieveBoard)
	{
		m_AchieveBoard->DetachFromScene();
	}

	if (m_Dialog)
	{
		m_Dialog->DetachFromScene();
	}
	if (m_VoiceObj) {
		m_VoiceObj->DetachFromScene();
	}

	if (m_VipIcon)
	{
		m_VipIcon->DetachFromScene();
	}
}

void ActorBodyUIComponent::InitVipIcon(int texid)
{
	if (!m_VipIcon)
	{
		if (texid == 1 || texid == 2)
		{
			m_VipIcon = Image3D::Create(160, 28, 80.0f, 14, true, false);
		}
		else if (m_ActorBody->isPlayer())
		{
			m_VipIcon = Image3D::Create(320, 20, 160.0f, 10.0f, true, false);
		}
		else
		{
			m_VipIcon = Image3D::Create(1400, 150, 700.0f, 75.0f, true, false);
		}

		if (m_VipIcon)
		{
			m_VipIcon->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
			m_VipIcon->SetVisibleDistance(16 * BLOCK_FSIZE);
			m_VipIcon->SetScale(0.15f);
			m_VipIcon->setVisible(false);
		}

		if (m_TeamId > 0)
		{
			if (m_VipIcon) m_VipIcon->setZTest(true);
		}
	}
}

void ActorBodyUIComponent::InitNeedItemIconPtr(int texid)
{
	if (!m_NeedItemIconPtr)
	{
		if (texid == 1 || texid == 2)
		{
			m_NeedItemIconPtr = Image3D::Create(160, 28, 80.0f, 14, true, true);
		}
		else if (m_ActorBody->isPlayer())
		{
			m_NeedItemIconPtr = Image3D::Create(320, 20, 160.0f, 10.0f, true, true);
		}
		else
		{
			m_NeedItemIconPtr = Image3D::Create(1400, 150, 700.0f, 75.0f, true, true);
		}
		if (m_NeedItemIconPtr)
		{
			m_NeedItemIconPtr->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
			m_NeedItemIconPtr->setOffset(680, 25);
			m_NeedItemIconPtr->setDrawSize(31, 31);
			m_NeedItemIconPtr->setVisible(false);
			m_NeedItemIconPtr->SetDepthBias(-1);
			m_NeedItemMerger->AddSubObject(m_NeedItemIconPtr);
		}
	}
}

void ActorBodyUIComponent::InitNeedItemBoard(int texid)
{
	// 初始化显示需求物品图标
	if (!m_NeedItemBoard)
	{
		if (texid == 1 || texid == 2)
		{
			m_NeedItemBoard = Image3D::Create(160, 28, 80.0f, 14, true, true);
		}
		else if (m_ActorBody->isPlayer())
		{
			m_NeedItemBoard = Image3D::Create(320, 20, 160.0f, 10.0f, true, true);
		}
		else
		{
			m_NeedItemBoard = Image3D::Create(1400, 150, 700.0f, 75.0f, true, true);
		}
		if (m_NeedItemBoard)
		{
			m_NeedItemBoard->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
			m_NeedItemBoard->setImageRes("ui/mobile/texture0/ingame", "need");
			m_NeedItemBoard->setOffset(660, 10);
			m_NeedItemBoard->setDrawSize(64, 64);
			m_NeedItemBoard->setVisible(false);

			m_NeedItemMerger = m_NeedItemBoard->GetGameObject()->CreateComponent<UIIn3DSceneMerger>();
			m_NeedItemMerger->AddSubObject(m_NeedItemBoard);
		}

	}
}

void ActorBodyUIComponent::InitDialog(int texid)
{
	// 初始化对话框
	if (!m_Dialog)
	{
		if (texid == 1 || texid == 2)
		{
			m_Dialog = Image3D::Create(160, 28, 80.0f, 14, true, true);
		}
		else if (m_ActorBody->isPlayer())
		{
			m_Dialog = Image3D::Create(320, 20, 160.0f, 10.0f, true, true);
		}
		else
		{
			m_Dialog = Image3D::Create(1400, 150, 700.0f, 75.0f, true, true);
		}

		if (m_Dialog)
		{
			m_Dialog->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
			m_Dialog->setImageRes("ui/mobile/texture0/ingame", "xsyd_icon_duihua");
			m_Dialog->setOffset(185, -30);
			m_Dialog->setDrawSize(31, 29);
			m_Dialog->setVisible(false);
		}
	}
}

void ActorBodyUIComponent::InitAchieveBoard(int texid)
{
	if (!m_AchieveBoard)
	{
		if (texid == 1 || texid == 2)
		{
			m_AchieveBoard = Image3D::Create(160, 28, 80.0f, 14, true, false);
		}
		else if (m_ActorBody->isPlayer())
		{
			m_AchieveBoard = Image3D::Create(320, 20, 160.0f, 10.0f, true, false);
		}
		else
		{
			m_AchieveBoard = Image3D::Create(1400, 150, 700.0f, 75.0f, true, false);
		}

		if (m_AchieveBoard)
		{
			m_AchieveBoard->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
			m_AchieveBoard->SetVisibleDistance(16 * BLOCK_FSIZE);
			m_AchieveBoard->setScale(0.15f);
			m_AchieveBoard->setVisible(false);

			if (m_ActorBody->m_World)
			{
				m_AchieveBoard->AttachToScene(m_ActorBody->m_World->getScene());
			}
		}
		if (m_ActorBody->m_World)
		{
			m_AchieveBoard->AttachToScene(m_ActorBody->m_World->getScene());
		}

		if (m_TeamId > 0)
		{
			if (m_AchieveBoard) m_AchieveBoard->setZTest(true);
		}
	}
}

void ActorBodyUIComponent::InitAchieveIcon(int texid)
{
	// 初始化勋章
	if (!m_AchieveIcon)
	{
		// if (texid == 1 || texid == 2)
		// {
		// 	m_AchieveIcon = Image3D::Create(160, 28, 80.0f, 14, true, false);
		// }
		// else if (m_ActorBody->isPlayer())
		// {
		// 	m_AchieveIcon = Image3D::Create(320, 20, 160.0f, 10.0f, true, false);
		// }
		// else
		// {
		// 	m_AchieveIcon = Image3D::Create(1400, 150, 700.0f, 75.0f, true, false);
		// }

		// if (m_AchieveIcon)
		// {
		// 	m_AchieveIcon->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
		// 	m_AchieveIcon->SetVisibleDistance(16 * BLOCK_FSIZE);
		// 	m_AchieveIcon->setScale(0.15f);
		// 	m_AchieveIcon->setVisible(false);

		// 	if (m_ActorBody->m_World)
		// 	{
		// 		m_AchieveIcon->AttachToScene(m_ActorBody->m_World->getScene());
		// 	}
		// }
		// if (m_ActorBody->m_World)
		// {
		// 	m_AchieveIcon->AttachToScene(m_ActorBody->m_World->getScene());
		// }

		// if (m_TeamId > 0)
		// {
		// 	if (m_AchieveIcon) m_AchieveIcon->setZTest(true);
		// }
	}
}

void ActorBodyUIComponent::setDispayHomeBillBoard()
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_BillBoardBkg == NULL)
	{
		m_BillBoardBkg = Image3D::Create(1400, 150, 700.0f, 75.0f, true, false);
		if (m_BillBoardBkg)
		{
			m_BillBoardBkg->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
			m_BillBoardBkg->setVisible(true);
			m_BillBoardBkg->setImageRes("ui/mobile/texture0/homechest", "img_board_bubble");
			m_BillBoardBkg->setOffset(720, -30);
			/*float boardW = 0, boardH = 0;
			m_BillBoardBkg->getRealSize(boardW, boardH);
			m_BillBoardBkg->setDrawSize(boardW/2, boardH/2);*/
			m_BillBoardBkg->setScale(0.5f);
			m_BillBoardBkg->SetVisibleDistance(16 * BLOCK_FSIZE);

			if (g_pPlayerCtrl)
			{
				World* pworld = g_pPlayerCtrl->getWorld();
				if (pworld)
					m_BillBoardBkg->AttachToScene(pworld->getScene());
			}
		}
	}
	if (m_BillBoardText == NULL)
	{
		m_BillBoardText = Text3D::Create(12, 1400, 150, 700.0f, 75.0f, true, false);
		if (m_BillBoardText)
		{
			m_BillBoardText->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
			m_BillBoardText->setText("");
			m_BillBoardText->setTextColor(61, 69, 70);
			m_BillBoardText->setVisible(true);
			m_BillBoardText->SetVisibleDistance(16 * BLOCK_FSIZE);
			m_BillBoardText->setAutoWrap(true);

			if (g_pPlayerCtrl)
			{
				World* pworld = g_pPlayerCtrl->getWorld();
				if (pworld)
					m_BillBoardText->AttachToScene(pworld->getScene());
			}
		}
		UIIn3DSceneMerger* merge = m_BillBoardText->GetGameObject()->CreateComponent<UIIn3DSceneMerger>();
		if (m_BillBoardBkg)
		{
			merge->AddSubObject(m_BillBoardBkg);
		}
		merge->AddSubObject(m_BillBoardText);
		merge->SetVisibleDistance(16 * BLOCK_FSIZE);
	}
#endif		
}

void ActorBodyUIComponent::setNeedItemIcon(Rainbow::SharePtr<Rainbow::Texture2D> huires, int tick /*= -1*/, int huiresWidth /*= 62*/, int huiresHeight /*= 62*/)
{
	if (m_NeedItemIconPtr && m_NeedItemBoard)
	{
		if (huires)
		{
			m_NeedItemBoard->setVisible(true);
			m_NeedItemIconPtr->setVisible(true);
			m_NeedItemIconPtr->setImageRes(huires, huiresWidth, huiresHeight);
			m_NeedItemIconPtr->setShowTick(tick);
			m_NeedItemBoard->setShowTick(tick);
		}
		else
		{
			m_NeedItemBoard->setVisible(false);
			m_NeedItemIconPtr->setVisible(false);
		}
	}
}

void ActorBodyUIComponent::setBillBoardText(const char* text)
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_BillBoardBkg)
	{
		m_BillBoardBkg->setVisible((strcmp(text, "") == 0) ? false : true);
	}

	if (m_BillBoardText)
	{
		/*m_BillBoardText->setBillBoardVisible((strcmp(text, "") == 0) ? false : true);
		m_BillBoardText->setBillBoardtexTex(text);*/
		m_BillBoardText->setVisible((strcmp(text, "") == 0) ? false : true);

		/*if (text == NULL)
			m_billBoardText = "";
		else
			m_billBoardText = text;*/
		m_BillBoardText->setBillboardScale(true, 200.0f, Rainbow::MAX_FLOAT);
		m_BillBoardText->setText(text);
	}
#endif
}

void ActorBodyUIComponent::setBillBoardTextNpc(bool bBillBoardTextNpc)
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_BillBoardBkg)
	{
		m_BillBoardBkg->setOffsetY(bBillBoardTextNpc ? 55 : 10);
	}
	if (m_BillBoardText && GetWorldManagerPtr())
	{
		float fNpcOffsetY = bBillBoardTextNpc ? 45 : 0;
		int nLineCount = 0;
		int language = GetClientInfoProxy()->getGameData("lang");
		//语言： 1--英语  2--繁体
		int charactorNumber = language == 1 ? 23 : 27;
		int nMyHomeLineCount = gFunc_IsMyHomeGardenWorldType(GetWorldManagerPtr()->getFromWorldID(), GetClientInfoProxy()->getUin()) ? 3 : 1;
		//此处会有偏移过多的bug，主要是行数计算，为了更准确一些，添加了进行"\n"换行的计算
		//如果其他语言还有问题，那就得在charactorNumber上进行更精准的区分
		std::string billboardText = m_BillBoardText->getText().c_str();
		size_t pos = billboardText.find("\\n");
		size_t last_pos = 0;
		while (pos != std::string::npos)
		{
			int textLen = billboardText.substr(last_pos, pos - last_pos + 2).length();
			nLineCount += bBillBoardTextNpc ? ((textLen % charactorNumber) == 0 ? textLen / charactorNumber : textLen / charactorNumber + 1) : nMyHomeLineCount;
			//LOG_INFO("AAAAA setBillBoardTextNpc11 text:%s, textLen:%d, nLineCount:%d", billboardText.substr(last_pos, pos - last_pos).c_str(), textLen, nLineCount);
			last_pos = pos + 2;
			pos = billboardText.find("\\n", last_pos);
		}
		if (last_pos < billboardText.size())
		{
			int textLen = billboardText.substr(last_pos, pos - last_pos).length();
			nLineCount += bBillBoardTextNpc ? ((textLen % charactorNumber) == 0 ? textLen / charactorNumber : textLen / charactorNumber + 1) : nMyHomeLineCount;
			//LOG_INFO("AAAAA setBillBoardTextNpc11 text:%s, textLen:%d, nLineCount:%d", billboardText.substr(last_pos, pos - last_pos).c_str(), textLen, nLineCount);
		}

		/*int textLen = m_BillBoardText->getText().length();
		int nMyHomeLineCount = gFunc_IsMyHomeGardenWorldType(GetWorldManagerPtr()->getFromWorldID(), GetClientInfo().getUin()) ? 3 : 1;
		int nLineCount = bBillBoardTextNpc ? ((textLen % 27) == 0 ? textLen / 27 : textLen / 27 + 1) : nMyHomeLineCount;*/
		float fOffsetY = (4 - nLineCount) * 7;
		//泰语等多语言计算可能不对，这里临时调整下 code_by:huangfubin 2022.1.4
		if (fOffsetY < 0)
		{
			fOffsetY = 0;
		}

		const float left = 740;
		const float top = 30 + fOffsetY + fNpcOffsetY;
		const float width = 120;
		const float height = 50;
		m_BillBoardText->setRect(left, top, left + width, top + height);
	}
#endif
}

//音乐厅聊天气泡 2021-09-25 codeby: luoshuai
void ActorBodyUIComponent::setDispayMusicClubChatBubble()
{
	if (SandboxChatBubbleManager::GetInstancePtr() && SandboxChatBubbleManager::GetInstancePtr()->getActorShowCustomChatBubble())
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_ActorBody->m_OwnerActor);
		int uin = player ? player->getUin() : 0;
		SandboxChatBubbleManager::GetInstancePtr()->setDispayActorChatBubble(uin);
		return;
	}

	if (m_MusicClubChatBubbleObj == NULL)
	{
		//if (m_AttachScene)
		//{
		m_MusicClubChatBubbleObj = Rainbow::MusicClubChatBubble3D::Create(24, 1400, 150, 700.0f, 75.0f, true, false);
		if (m_MusicClubChatBubbleObj)
		{
			m_MusicClubChatBubbleObj->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
			m_MusicClubChatBubbleObj->setTextColor(61, 69, 70);
			m_MusicClubChatBubbleObj->setMusicClubChatBubbleVisible(true);
			m_MusicClubChatBubbleObj->SetVisibleDistance(16 * BLOCK_FSIZE);
			//
			if (m_ActorBody->m_World)
			{
				BlockScene* scene = m_ActorBody->m_World->getScene();
				m_MusicClubChatBubbleObj->AttachToScene(scene);
			}

		}
		//}	
	}
}
void ActorBodyUIComponent::initDisplayVoiceIcon()
{
	if (!m_VoiceObj)
	{
		m_VoiceObj = Rainbow::Voice3D::Create(18, 18, 18, 18, true, false);
		if (m_VoiceObj)
		{
			m_VoiceObj->SetVisibleDistance(16 * BLOCK_FSIZE);
			//
			if (m_ActorBody->m_World)
			{
				BlockScene* scene = m_ActorBody->m_World->getScene();
				m_VoiceObj->AttachToScene(scene);
			}
		}
		else
		{
			m_VoiceObj->setVoiceIconVisible(false);
		}
	}
}

//音乐厅聊天气泡 2021-09-25 codeby: luoshuai
//2021-12-20 codeby: wangyang ???????????
void ActorBodyUIComponent::setMusicClubChatBubbleText(const char* text, bool isShow, int bubble, float tickTime)
{
	if (SandboxChatBubbleManager::GetInstancePtr() && SandboxChatBubbleManager::GetInstancePtr()->getActorShowCustomChatBubble())
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_ActorBody->m_OwnerActor);
		int uin = player ? player->getUin() : 0;
		SandboxChatBubbleManager::GetInstancePtr()->setActorChatBubbleText(uin, text, isShow, bubble, tickTime);
		return;
	}


	if (m_MusicClubChatBubbleObj)
	{
		//2021-12-20 codeby: wangyang ???????????
		m_MusicClubChatBubbleObj->setBgPicInfoByID(bubble);
		m_MusicClubChatBubbleObj->setMusicClubChatBubbleVisible(isShow);
		m_MusicClubChatBubbleObj->setMusicClubChatBubbleText(text, tickTime);
	}
}
void ActorBodyUIComponent::checkAndNewHPOBJ()
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_HPProgressObj == NULL)
	{
		// UNDONE 
		m_HPProgressObj = ProgressBarIn3D::Create("img_blood_strip_board_b.png", "img_blood_white.png");
		m_HPProgressObj->GetGameObject()->SetLayer(kLayerIndexCustom_HUD);
		float dist = GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.billboard_scale_dist;
		m_HPProgressObj->setBillboardScaleDist(dist);
		m_HPProgressObj->setTextBillboardScale(true, 0, dist);
		if (m_ActorBody->m_World)
		{
			m_HPProgressObj->AttachToScene(m_ActorBody->m_World->getScene());
		}
	}
#endif		
}
void ActorBodyUIComponent::setHPVisible(bool pVisible)
{
#ifndef IWORLD_SERVER_BUILD		
	checkAndNewHPOBJ();
	//血条不显示
	m_HPVisible = false;
#endif		
}
bool ActorBodyUIComponent::getHPVisible()
{
#ifndef IWORLD_SERVER_BUILD		
	return m_HPVisible;
#else
	return false;
#endif		
}
void ActorBodyUIComponent::setHPVale(int pNow, int pTotale)
{
#ifndef IWORLD_SERVER_BUILD		
	checkAndNewHPOBJ();
	if (pNow < 0) pNow = 0;
	if (m_HPProgressObj)
		m_HPProgressObj->setVale(pNow, pTotale);
#endif		
}

void ActorBodyUIComponent::setHpExtraValue(int val)
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_HPProgressObj != NULL)
	{
		m_HPProgressObj->setExtraValue(val);
	}
#endif		
}

void ActorBodyUIComponent::setHpTextDisplay(int type)
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_HPProgressObj != NULL)
	{
		m_HPProgressObj->setTextDisplayStyle((ProgressBarIn3D::PROGRESSBAR3D_TEXTDISPLY)type);
	}
#endif	
}

void ActorBodyUIComponent::setHPColor(int r, int g, int b, int alpha/* =255 */)
{
#ifndef IWORLD_SERVER_BUILD		
	checkAndNewHPOBJ();
	if (m_HPProgressObj)
		m_HPProgressObj->setColor(r, g, b, alpha);
#endif		
}
void ActorBodyUIComponent::setHPTextrueName(const char* pBg, const char* pProgress)
{
#ifndef IWORLD_SERVER_BUILD			
	checkAndNewHPOBJ();
	if (m_HPProgressObj)
		m_HPProgressObj->setTextrueName(pBg, pProgress);
#endif			
}

Rainbow::MoveByTextMgr* ActorBodyUIComponent::getHPChangeTextMgr()
{
#ifndef IWORLD_SERVER_BUILD	
	if (m_HPChangeEffMgr == NULL)
	{
		m_HPChangeEffMgr = ENG_NEW(MoveByTextMgr)(GetWorldManagerPtr()->m_SurviveGameConfig->hpchangeeffectcfg.per_actor_font_limit);
		if (m_ActorBody->m_World)
		{
			m_HPChangeEffMgr->setGameScene(m_ActorBody->m_World->getScene());
		}
	}
	return m_HPChangeEffMgr;
#else	
	return NULL;
#endif				
}

void ActorBodyUIComponent::createHPChangeText(const char* pStr, int pFontSize, const Rainbow::ColorQuad& pColor, bool isMainPlayer /*false*/)
{
#ifndef IWORLD_SERVER_BUILD		
	Vector3f pos(0, MAX_FLOAT, 0);
	ClientActor* riding = NULL;
	ClientActor* carried = NULL;
	if (m_ActorBody->m_OwnerActor && !m_ActorBody->m_IsAttachModelView)
	{
		auto RidComp = m_ActorBody->m_OwnerActor->getRiddenComponent();
		auto CarryComp = m_ActorBody->m_OwnerActor->getCarryComponent();
		if (RidComp && RidComp->isRiding() && (riding = RidComp->getRidingActor()) != NULL)
		{
			auto ridingComp = riding->getRiddenComponent();
			if (ridingComp)
				pos = ridingComp->getRiddenBindPos(m_ActorBody->m_OwnerActor);
		}
		else if (CarryComp && CarryComp->isCarried() && (carried = CarryComp->getCarriedActor()) != NULL)
		{
			auto CarriedComp = carried->getCarryComponent();
			if (CarriedComp)
				pos = CarriedComp->getCarryingBindPos();
		}
		else
		{
			pos = m_ActorBody->m_OwnerActor->getLocoMotion()->getFramePosition().toVector3();
			pos.y -= m_ActorBody->m_OwnerActor->getLocoMotion()->m_yOffset;
		}
	}
	if (pos.y != MAX_FLOAT && CameraManager::GetInstancePtr())
	{
		float h = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundHeight;
		float w = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundSize;
		HPChangeEffectConfig& effectCfg = GetWorldManagerPtr()->m_SurviveGameConfig->hpchangeeffectcfg;
		float _yRate = effectCfg.offset_y_rate;
		if (g_pPlayerCtrl && g_pPlayerCtrl->getViewMode() == CAMERA_FPS)
		{
			_yRate = effectCfg.first_view_offset_y_rate;
		}
		else if (g_pPlayerCtrl && g_pPlayerCtrl->getViewMode() == CAMERA_TPS_OVERLOOK)
		{
			_yRate = effectCfg.over_view_offset_y_rate;
		}
		//GetClientInfoProxy()->getAccount().c_str();
		Vector3f cameraPos = CameraManager::GetInstance().getCameraPos().toVector3();
		Vector3f subResult = (pos + Vector3f(0.0f, h * _yRate, 0)) - cameraPos;
		Vector3f textPos = cameraPos + (subResult * (1 - (((w + effectCfg.offset_eye) / 2) / subResult.Length())));
		Rainbow::MoveByTextMgr* mgr = getHPChangeTextMgr();
		float rangeAngle = isMainPlayer ? effectCfg.main_angle : effectCfg.mob_angle;
		float yDirIndex = 1;
		if (rangeAngle < 0)
		{
			yDirIndex = -1;
			rangeAngle = rangeAngle * -1;
		}
		float distance = effectCfg.move_distance;
		Vector3f vect = Vector3f(0, 0, 0);
		int rangeAngleTemp = int(rangeAngle);
		rangeAngleTemp = rangeAngleTemp == 0 ? 1 : rangeAngleTemp;  // 防止取模运算时被除数为0
		float angle = rand() % rangeAngleTemp;
		angle += 90 - (rangeAngle / 2);
		vect.x = distance * cos(angle * 3.1415 / 180);
		vect.y = distance * sin(angle * 3.1415 / 180) * yDirIndex;
		mgr->createMoveByText(pStr, pFontSize, ColorRGBA32(pColor.r, pColor.g, pColor.b, pColor.a), WorldPos::fromVector3(textPos), vect, effectCfg.alhpa_end, effectCfg.time, effectCfg.ZTest);
	}
#endif		
}

void ActorBodyUIComponent::checkAndNewArmorOBJ()
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_ArmorProgressObj == NULL)
	{
		m_ArmorProgressObj = Rainbow::ProgressBarIn3D::Create("", "");
		m_ArmorProgressObj->setTextureName2("", "img_shield_red.png", "ui/mobile/texture0/ingame2.xml", "ui/mobile/texture0/ingame2.png");
	}
#endif		
}

void ActorBodyUIComponent::setArmorVisible(bool pVisible)
{
#ifndef IWORLD_SERVER_BUILD		
	checkAndNewArmorOBJ();
	m_ArmorVisible = pVisible;
#endif		
}

bool ActorBodyUIComponent::getArmorVisible()
{
#ifndef IWORLD_SERVER_BUILD		
	return m_ArmorVisible;
#else
	return false;
#endif		
}

void ActorBodyUIComponent::setArmorVale(int now, int max, int extra)
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_ArmorProgressObj)
	{
		m_ArmorProgressObj->setVale(now, max);
		m_ArmorProgressObj->setExtraValue(extra);
	}
#endif		
}

void ActorBodyUIComponent::setArmorColor(int r, int g, int b, int alpha)
{
#ifndef IWORLD_SERVER_BUILD			
	checkAndNewArmorOBJ();
	m_ArmorProgressObj->setColor(r, g, b, alpha);
#endif		
}

void ActorBodyUIComponent::setArmorTextrueName(const char* pBg, const char* pProgress)
{
#ifndef IWORLD_SERVER_BUILD		
	checkAndNewArmorOBJ();
	m_ArmorProgressObj->setTextureName2(pBg, pProgress, "ui/mobile/texture0/ingame2.xml", "ui/mobile/texture0/ingame2.png");
#endif		
}

void ActorBodyUIComponent::setShowDialog(bool state)
{
#ifndef IWORLD_SERVER_BUILD		

	if (state)
	{
		InitDialog(m_TexId);
	}

	if (m_Dialog)
	{
		m_Dialog->setVisible(state);
	}
#endif		
}

void ActorBodyUIComponent::setVisibleDispayName(bool b)
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_NameDispObj != NULL)
		m_NameDispObj->setVisible(b);
	setVipIconVisible(b); // add by wangyang 20220303 vipicon???????????????
	//setAchieveVisible(b);
	//setBPTitleIconVisible(b);
	//setHPVisible(b);
	//setVoiceIconVisible(b);
#endif		
}

void ActorBodyUIComponent::addNameTexId(int nameTexid)
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_NameDispObj != NULL)
		m_NameDispObj->addNameTexId(nameTexid);
#endif
}

void ActorBodyUIComponent::setAchieveIconName(const char* texIcon, const char* texFrame)
{
#ifndef IWORLD_SERVER_BUILD		

	InitAchieveIcon(m_TexId);
	InitAchieveBoard(m_TexId);

	if (m_AchieveIcon && m_AchieveBoard && m_NameDispObj)
	{
		m_AchieveIcon->setImageRes("ui/mobile/texture2/achievement", texIcon);
		m_AchieveBoard->setImageRes("ui/mobile/texture2/achievement", texFrame);

		float textW = 0, textH = 0, boardW = 0, boardH = 0, iconW = 0, iconH = 0;
		m_NameDispObj->getTextSize(textW, textH);
		float iconStartX = 135 - textW * 0.5f;

		m_AchieveBoard->getRealSize(boardW, boardH);
		m_AchieveIcon->getRealSize(iconW, iconH);

		m_AchieveIcon->setOffsetX(iconStartX);
		m_AchieveBoard->setOffset(iconStartX - (boardW - iconW) / 2, -(boardH - iconH) / 2);
	}
#endif		
}

void ActorBodyUIComponent::setAchieveVisible(bool achieveVisible)
{
#ifndef IWORLD_SERVER_BUILD		
	// if (m_AchieveIcon)
	// {
	// 	m_AchieveIcon->setVisible(achieveVisible);
	// }

	// if (m_AchieveBoard)
	// {
	// 	m_AchieveBoard->setVisible(achieveVisible);
	// }
#endif		
}

void ActorBodyUIComponent::setBPTitleIconName(std::string str)
{
#ifndef IWORLD_SERVER_BUILD		
	int str_cur_len = 6;
	float offX = 500;
	float offY = 180;

	std::vector<std::string> strVec;
	if (str != "")
	{
		Rainbow::StringUtil::split(strVec, str, "|");
		if (strVec.size() == str_cur_len)
		{
			offX = strVec[1].empty() ? offX : std::stof(strVec[1]);
			offY = strVec[2].empty() ? offY : std::stof(strVec[2]);
		}
	}

	m_TitleIcon = Image3D::Create(offX, offY, offX, offY, true, false);
	if (m_TitleIcon)
	{
		m_TitleIcon->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
		m_TitleIcon->SetVisibleDistance(16 * BLOCK_FSIZE);
		m_TitleIcon->setScale(0.4f);
		m_TitleIcon->setVisible(false);
		if (m_ActorBody->m_World)
		{
			m_TitleIcon->AttachToScene(m_ActorBody->m_World->getScene());
		}
	}

	if (str == "" || strVec.size() != str_cur_len)
	{
		return;
	}

	std::string titleIcon = strVec[0];
	if (m_TitleIcon && m_NameDispObj)
	{
		if (titleIcon != "")
		{
			m_TitleIcon->setVisible(true);
		}
		else
		{
			m_TitleIcon->setVisible(false);
		}

		m_TitleIcon->setImageRes("", titleIcon.c_str(), std::stoi(strVec[1]), std::stoi(strVec[2]));

		float textW = 0, textH = 0, boardW = 0, boardH = 0, iconW = 0, iconH = 0;
		float titleW = 0, tilteH = 0;

		m_NameDispObj->getTextSize(textW, textH);
		m_TitleIcon->getRealSize(titleW, tilteH);
		float iconStartX = 135 - titleW;
		float iconStartY = (textH - tilteH) / 2 + 2;
		m_TitleIcon->setOffset(std::stof(strVec[3]), -3 + std::stof(strVec[4]));
		m_TitleIcon->setScale(std::stof(strVec[5]));
	}
#endif		
}

void ActorBodyUIComponent::setBPTitleIconVisible(bool visible)
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_TitleIcon)
	{
		m_TitleIcon->setVisible(visible);
	}
#endif		
}

void ActorBodyUIComponent::setVipIconName(const char* iconPath, const char* texIcon, float scale)
{
	InitVipIcon(m_TexId);
	InitAchieveBoard(m_TexId);

	if (m_VipIcon && m_NameDispObj && m_AchieveBoard)
	{
		m_VipIcon->SetScale(scale);
		m_VipIcon->setImageRes(iconPath, texIcon);

		float textW = 0, textH = 0, boardW = 0, boardH = 0, iconW = 0, iconH = 0;

		m_AchieveBoard->getRealSize(boardW, boardH);
		m_NameDispObj->getTextSize(textW, textH);
		m_VipIcon->getRealSize(iconW, iconH);
		float iconStartX = 160 - textW * 0.5f - boardW - iconW;
		float iconStartY = (textH - iconH) / 2 + 2;

		m_VipIcon->setOffset(iconStartX, iconStartY);
		if (m_ActorBody->m_World)
		{
			m_VipIcon->AttachToScene(m_ActorBody->m_World->getScene());
		}
	}
}

void ActorBodyUIComponent::setVipIconVisible(bool vipVisible)
{
	if (m_VipIcon)
	{
		m_VipIcon->setVisible(vipVisible);
	}
}

void ActorBodyUIComponent::setVoiceIconVisible(bool voiceVisible)
{
	if (voiceVisible)
	{
		initDisplayVoiceIcon();
	}

	if (m_VoiceObj)
	{
		m_VoiceObj->setVoiceIconVisible(voiceVisible);
	}
}

void ActorBodyUIComponent::setHeadIconByPath(const char* imageResPath, const char* imageResUVName, int imageWidth /*= 0*/, int imageHeight /*= 0*/, bool isSync/*=true*/)
{
#ifndef IWORLD_SERVER_BUILD
	if (m_NeedItemIconPtr && m_NeedItemBoard)
	{
		if (imageWidth != 0 && imageHeight != 0)
		{
			m_NeedItemBoard->setVisible(true);
			m_NeedItemIconPtr->setVisible(true);
			m_NeedItemIconPtr->setImageRes(imageResPath, imageResUVName, imageWidth, imageHeight);
		}
		else
		{
			m_NeedItemBoard->setVisible(false);
			m_NeedItemIconPtr->setVisible(false);
		}
	}
#endif
	if (m_ActorBody->m_World && !m_ActorBody->m_World->isRemoteMode() && m_ActorBody->m_OwnerActor && isSync)
	{
		jsonxx::Object object;
		object << "actorId" << m_ActorBody->m_OwnerActor->getObjId();
		object << "imageResPath" << imageResPath;
		object << "imageResUVName" << imageResUVName;
		object << "imageWidth" << imageWidth;
		object << "imageHeight" << imageHeight;
		int size = 0;
		unsigned char* p = NULL;
		object.saveBinary(p, size);
		GetSandBoxManager().sendToTrackingPlayers(m_ActorBody->m_OwnerActor, (char*)("SHOW_HEAD_ICON_BY_PATH"), p, size);
		free(p);
	}
}

//2021-12-20 codeby: wangyang ??????
void ActorBodyUIComponent::setVipNameColor(int colorR, int colorG, int colorB)
{
	m_isVip = true;
	if (m_NameDispObj)
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_ActorBody->m_OwnerActor);
		int teamId = player == NULL ? -1 : player->getTeam();
		if (teamId <= 0)
		{
			// ??ж???????????????
			m_NameDispObj->setTextColor(colorR, colorG, colorB);
		}
	}
}


//20211020 codeby:wangyu 新增播放装扮互动时显示和隐藏玩家昵称和血条
void ActorBodyUIComponent::setNameAndHpVisible(bool isVisble)
{
	//装扮互动未开始则返回
	if (!m_NameDispObj || !m_HPProgressObj || !m_bInviteActStart)
	{
		return;
	}
	//播放时隐藏
	m_NameDispObj->setVisible(!isVisble);
	m_HPProgressObj->setVisible(!isVisble);
	setVipIconVisible(!isVisble); // add by wangyang 20220303 vipicon???????????????
	setAchieveVisible(!isVisble);
	setBPTitleIconVisible(!isVisble);
	setHPVisible(!isVisble);
	setVoiceIconVisible(!isVisble);
	//播放结束时，等待下次开始
	if (!isVisble)
	{
		m_bInviteActStart = isVisble;
	}
}

void ActorBodyUIComponent::setHeadDisplayIcon(int itemid, int tick /* = -1 */)
{
#ifndef IWORLD_SERVER_BUILD
	if (itemid > 0)
	{
		int u = 0, v = 0, w = 0, h = 0, r = 255, g = 255, b = 255;
		SharePtr<Texture2D> huires = nullptr;

		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ItemIconManager_getItemIcon",
			SandboxContext(nullptr)
			.SetData_Number("toolid", itemid)
			.SetData_Number("u", u)
			.SetData_Number("v", v)
			.SetData_Number("w", w)
			.SetData_Number("h", h)
			.SetData_Number("r", r)
			.SetData_Number("g", g)
			.SetData_Number("b", b));
		if (result.IsExecSuccessed())
		{
			huires = result.GetData_UserObject<SharePtr<Texture2D>>("huires");
			/*	w = result.GetData_Number("w");
				h = result.GetData_Number("h");*/
		}
		/*setNeedItemIcon(huires, tick, w, h);*/
		setNeedItemIcon(huires, tick);
	}
	else
	{
		setNeedItemIcon(nullptr, 1);
	}
#endif
	if (m_ActorBody->m_World && !m_ActorBody->m_World->isRemoteMode())
	{
		if (itemid != m_ActorBody->m_OwnerActor->getLastHeadIconItemID() || itemid > 0)
		{
			PB_ActorHeadDisplayIconHC actorHeadDisplayIconHC;
			actorHeadDisplayIconHC.set_actorid(m_ActorBody->m_OwnerActor->getObjId());
			if (itemid > 0)
			{
				actorHeadDisplayIconHC.set_itemid(itemid);
				actorHeadDisplayIconHC.set_tick(tick);
			}
			m_ActorBody->m_World->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_HEAD_DISPLAY_ICON_HC, actorHeadDisplayIconHC, m_ActorBody->m_OwnerActor);
			m_ActorBody->m_OwnerActor->setLastHeadIconItemID(itemid);
		}
	}
}


void ActorBodyUIComponent::initExchangeItemIcon()
{
	if (!m_ImageBoard)
	{
		m_ImageBoard = ImageBoard3D::Create(250, 100, 250, 100);
		m_ImageBoard->setBkgTexture("particles/duihua.png", 128, 128, 80, -30, 150, 100);
		m_ImageBoard->setFontSize(13);
		m_ImageBoard->setFontColor(61, 69, 70);
		m_ImageBoard->setFontOffset(28, 20);
		m_ImageBoard->SetVisibleDistance(16 * BLOCK_FSIZE);
		if (!m_ImageBoard->IsInScene() && m_ActorBody->m_World)
		{
			m_ImageBoard->AttachToScene(m_ActorBody->m_World->getScene());
		}
	}
}


void ActorBodyUIComponent::setExchangeItemIcon(SharePtr<Texture2D> huires, int num /*= -1*/, int tick /*= -1*/, int huiresWidth /*= 62*/, int huiresHeight /*= 62*/)
{
	if (m_ImageBoard)
	{
		if (huires)
		{
			m_ImageBoard->setLeftTexture(huires, huiresWidth, huiresHeight, 110, -5, 32, 32);
			if (num != 1)
			{
				char ch[10] = { 0 };
				sprintf(ch, "%d", num);
				m_ImageBoard->setLeftFontStr(ch);
			}
			else
			{
				m_ImageBoard->setLeftFontStr("");
			}
		}
	}
}

void ActorBodyUIComponent::setSaleItemIcon(SharePtr<Texture2D> huires, int num /*= -1*/, int tick /*= -1*/, int huiresWidth /*= 62*/, int huiresHeight /*= 62*/)
{
	if (m_ImageBoard)
	{
		if (huires)
		{
			m_ImageBoard->setRightTexture(huires, huiresWidth, huiresHeight, 160, -5, 32, 32);
			if (num != 1)
			{
				char ch[10] = { 0 };
				sprintf(ch, "%d", num);
				m_ImageBoard->setRightFontStr(ch);
			}
			else
			{
				m_ImageBoard->setRightFontStr("");
			}
		}
	}
}

void ActorBodyUIComponent::setSaleItemIconByString(const std::string& pngName, int num, int tick, int huiresWidth, int huiresHeight)
{
	if (m_ImageBoard)
	{
		if (!pngName.empty())
		{
			m_ImageBoard->setRightTexture(pngName, huiresWidth, huiresHeight, 160, -5, 32, 32);
			if (num != 1)
			{
				char ch[10] = { 0 };
				sprintf(ch, "%d", num);
				m_ImageBoard->setRightFontStr(ch);
			}
			else
			{
				m_ImageBoard->setRightFontStr("");
			}
		}
	}
}

void ActorBodyUIComponent::showExchangeBubble(bool show)
{
	if (m_ImageBoard)
	{
		m_ImageBoard->setVisible(show);
	}
}


void ActorBodyUIComponent::setHeadExchangeDisplayIcon(int exchageItem, int saleItem, bool isActor, int exchangeNum, int saleNum, int tick)
{
	//if (m_Body)
	{
#ifndef IWORLD_SERVER_BUILD
		if (exchageItem > 0 && saleItem > 0)
		{
			initExchangeItemIcon();
			int u = 0, v = 0, w = 0, h = 0, r = 255, g = 255, b = 255;
			SharePtr<Texture2D> huires = nullptr;

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ItemIconManager_getItemIcon",
				SandboxContext(nullptr)
				.SetData_Number("toolid", exchageItem)
				.SetData_Number("u", u)
				.SetData_Number("v", v)
				.SetData_Number("w", w)
				.SetData_Number("h", h)
				.SetData_Number("r", r)
				.SetData_Number("g", g)
				.SetData_Number("b", b));
			if (result.IsExecSuccessed())
			{
				huires = result.GetData_UserObject<SharePtr<Texture2D>>("huires");
				setExchangeItemIcon(huires, exchangeNum, tick, 64, 64);
			}

			//m_Body->getNameDispObj()->setIconHuires(huires, tick);

			if (isActor)
			{
				char iconName[50] = { 0 };
				sprintf(iconName, "ui/roleicons/%d.png", saleItem);
				setSaleItemIconByString(string(iconName), saleNum, tick, 128, 128);
			}
			else
			{
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ItemIconManager_getItemIcon",
					SandboxContext(nullptr)
					.SetData_Number("toolid", saleItem)
					.SetData_Number("u", u)
					.SetData_Number("v", v)
					.SetData_Number("w", w)
					.SetData_Number("h", h)
					.SetData_Number("r", r)
					.SetData_Number("g", g)
					.SetData_Number("b", b));
				if (result.IsExecSuccessed())
				{
					huires = result.GetData_UserObject<SharePtr<Texture2D>>("huires");
					setSaleItemIcon(huires, saleNum, tick, 64, 64);
				}
			}
			showExchangeBubble(true);
		}
		else
		{
			//m_Body->getNameDispObj()->setIconHuires(0, 1);
			setExchangeItemIcon(nullptr, 1);
			setSaleItemIcon(nullptr, 1);
			showExchangeBubble(false);
		}
#endif
		if (m_ActorBody->m_World && !m_ActorBody->m_World->isRemoteMode() && m_ActorBody->m_OwnerActor)
		{
			/*PB_ActorHeadDisplayExchangeIconHC actorHeadDisplayIconHC;
			actorHeadDisplayIconHC.set_actorid(m_OwnerActor->getObjId());
			actorHeadDisplayIconHC.set_exchangeitemid(exchageItem);
			actorHeadDisplayIconHC.set_exchangeitemnum(exchangeNum);
			actorHeadDisplayIconHC.set_saleitemid();
			actorHeadDisplayIconHC.set_saleitemnum(saleNum);
			actorHeadDisplayIconHC.set_tick(tick);
			actorHeadDisplayIconHC.set_issalemonster(isActor);*/


			//m_World->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_HEAD_DISPLAY_EXCHANGE_ICON_HC, actorHeadDisplayIconHC, m_OwnerActor);
			jsonxx::Object object;
			object << "actorId" << m_ActorBody->m_OwnerActor->getObjId();
			object << "exchangeItem" << exchageItem;
			object << "exchangeNum" << exchangeNum;
			object << "saleItem" << saleItem;
			object << "saleNum" << saleNum;
			object << "tick" << tick;
			object << "isActor" << isActor;
			int size = 0;
			unsigned char* p = NULL;
			object.saveBinary(p, size);
			GetSandBoxManagerPtr()->sendToTrackingPlayers(m_ActorBody->m_OwnerActor, (char*)("SHOW_EXCHANGE_ITEM_ICON"), p, size);
			free(p);
		}
	}
}

void ActorBodyUIComponent::updatemForChatBubble3D(float dtime, unsigned int dtick, Rainbow::Vector3f& pos)
{
	if (SandboxChatBubbleManager::GetInstancePtr() && SandboxChatBubbleManager::GetInstancePtr()->getActorShowCustomChatBubble())
	{
		if (pos.y != MAX_FLOAT && m_ActorBody->m_OwnerActor)
		{
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_ActorBody->m_OwnerActor);
			int uin = player ? player->getUin() : 0;

			float h = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundHeight;
			Rainbow::Vector3f newPosition = pos + Rainbow::Vector3f(0.0f, h + 70, 0.0f);
			SandboxChatBubbleManager::GetInstancePtr()->updateActorChatBubblePosition(uin, newPosition);
		}
		return;
	}

	if (m_MusicClubChatBubbleObj)
	{
		if (m_MusicClubChatBubbleObj->getMusicClubChatBubbleVisible())
		{
			//m_HadShowTime += deltime;
			//if (m_HadShowTime > m_MaxShowTime)
			//{
			//	m_HadShowTime = 0.0f;
			//	m_BubbleVisible = false;
			//}

			//RenderableObject::update(deltime);

			//this->setBubbleText(m_ContentText.c_str());

			//if (m_OwnerActor == NULL) return;

			//Rainbow::Vector3f pos(0, MAX_FLOAT, 0);
			//ClientActor* riding = NULL;
			//ClientActor* carried = NULL;

			//if (m_OwnerActor->isRiding() && (riding = m_OwnerActor->getRidingActor()) != NULL)
			//{
			//	pos = riding->getRiddenBindPos(m_OwnerActor);
			//}
			//else if (m_OwnerActor->isCarried() && (carried = m_OwnerActor->getCarriedActor()) != NULL)
			//{
			//	pos = carried->getCarryingBindPos();
			//}
			//else
			//{
			//	pos = m_OwnerActor->getLocoMotion()->getFramePosition().toVector3();
			//	pos.y -= m_OwnerActor->getLocoMotion()->m_yOffset;
			//}

			float h = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundHeight;
			if (pos.y != MAX_FLOAT)
			{
				m_MusicClubChatBubbleObj->SetPosition(WorldPos::fromVector3(pos + Rainbow::Vector3f(0.0f, h + 70, 0.0f)));
			}
		}
		else
		{
			m_MusicClubChatBubbleObj->setMusicClubChatBubbleVisible(false);
		}
	}
}

int  ActorBodyUIComponent::getInnerGraphicsOffest(int itype)
{
	int offset = 0;
#ifndef IWORLD_SERVER_BUILD
	if (m_ActorBody->m_OwnerActor)
	{
		int hpHeight = 0;
		if (m_HPProgressObj)
		{
			hpHeight = m_HPProgressObj->getHeight() + GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.interval_to_name;
			if (m_ArmorProgressObj && m_ArmorProgressObj->isVisible())
			{
				hpHeight += m_ArmorProgressObj->getHeight() + g_WorldMgr->m_SurviveGameConfig->armorprogresscfg.interval_to_name;
			}
		}

		offset = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundHeight;

		if (itype == 3) //头顶特效
		{
			if (m_ActorBody->m_HeadEffectObjHeight == 0)
			{
				return  0;
			}
			return offset + m_ActorBody->m_HeadEffectObjHeight;
		}
		if (itype == 4) // 血条高度
		{
			if (m_HPProgressObj && m_HPProgressObj->isVisible())
			{
				return offset + hpHeight;
			}
			return 0;
		}
		offset = offset + (m_ActorBody->m_OwnerPlayer ? m_ActorBody->m_NameObjHeight : GetWorldManagerPtr()->m_SurviveGameConfig->npcconfig.namedisplay_offset_y);
		offset += m_ActorBody->m_HeadEffectObjHeight;
		if (m_HPProgressObj && m_HPProgressObj->isVisible())
		{
			offset += hpHeight;
		}
		if (itype == 1) // 昵称
		{
			return offset += 21;
		}
		if (itype == 2) //称号
		{
			if (m_TitleIcon && m_TitleIcon->isVisible())
			{
				float w = 0.f, h = 0.f;
				m_TitleIcon->getRealSize(w, h);
				if (w > 1.0f && h > 1.0f)
				{
					float sx = 0.f, sy = 0.f;
					m_TitleIcon->getImageScale(sx, sy);
					h = h / sy + 5;
					offset += h;
					return offset;
				}
			}
		}
	}
#endif
	return 0;
}

void ActorBodyUIComponent::updateForHp(float dtime, unsigned int dtick, Rainbow::Vector3f& pos)
{
	OPTICK_EVENT();
	if (m_HPProgressObj)
	{
#if HUD_TEST
		totalFontTime += dtime;
		if (totalFontTime > 1.5f)
		{
			totalFontTime = 0;
			//测试飘字
			std::srand(std::time(0));
			int hurtVal = std::rand() % 10001;
			int font = (std::rand() % 18);
			float h = (float)m_OwnerActor->getLocoMotion()->m_BoundHeight;
			float w = (float)m_OwnerActor->getLocoMotion()->m_BoundSize;
			if (m_OwnerPlayer)
				h += m_NameObjHeight;
			else
				h += GetWorldManagerPtr()->m_SurviveGameConfig->npcconfig.namedisplay_offset_y;
			auto mob = dynamic_cast<ClientFlyMob*>(m_OwnerActor);
			float offsetY = h + (mob ? mob->getHpProgressOffsetY() : 0);
			HUDLevitationFontRenderer::GetInstance()->AddHudLevitationFont(
				m_Entity->GetTransform(), offsetY, (HUDLevitationFontType)font, IntToString(hurtVal));
			//测试title
			if (m_titileId < 0)
			{
				m_titileId = HUDTitleRender::GetInstance()->RegisterTitle(m_Entity->GetTransform(), offsetY, false);
				HUDTitleInfo* title = HUDTitleRender::GetInstance()->GetTitle(m_titileId);
				int           bloodType = std::rand() % 4;
				if (bloodType > 0)
					bloodType -= 1;
				//血条
				title->BeginTitle();
				title->PushBlood(HUDBloodType(bloodType), 1);
				title->EndTitle();

				//昵称
				title->BeginTitle();
				title->PushTitle("纯爱战士·马小刀", HUDTilteType::PlayerName, 0);
				title->EndTitle();
				//帮会名字
				title->BeginTitle();
				title->PushTitle("迷你世界战神", HUDTilteType::PlayerCorp, 0);
				title->EndTitle();
				//称号
				title->BeginTitle();
				title->PushTitle("天下无双", HUDTilteType::PlayerDesignation, 0);
				title->EndTitle();

				//  // movieClip
				title->BeginTitle();
				title->PushEffectSprite(HUDTilteType::HeadIcon, HUDEffectType::Effect_MovieClip, 0);
				title->EndTitle();
				//IMG
				title->BeginTitle();
				title->PushEffectSprite(HUDTilteType::PKFlag, HUDEffectType::Effect_IMG, 0);
				title->EndTitle();
				//spine
				title->BeginTitle();
				title->PushEffectSprite(HUDTilteType::PKFlag, HUDEffectType::Effect_Spine, 1);
				title->EndTitle();

				title->SetBloodMinMaxVal(0, 100000);
				title->SetBloodVal(100000);
				//           HUDTitleRender::GetInstance()->ShowChatBubble(m_Entity->GetTransform(), "今天天气很好!心情也不错~ balabalbala!~",offsetY,100,0);

			}
			else
			{
				HUDTitleInfo* title = HUDTitleRender::GetInstance()->GetTitle(m_titileId);
				float         curVal = title->m_bloodBar->m_BaseObject->getValue();

				int randNumber = std::rand() % 11;
				if (curVal < 0)
				{
					curVal = title->m_bloodBar->m_BaseObject->getMax();

				}
				title->SetBloodVal(curVal - randNumber * 800);
			}
		}
#endif
		if (pos.y != MAX_FLOAT)
		{
			bool bvisible = m_HPVisible;
			if (m_ActorBody->m_OwnerPlayer && m_ActorBody->m_OwnerPlayer->getTeam() == JUDGE_TEAM_ID)
			{
				bvisible = false;
			}
			if (bvisible)
			{
				float h = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundHeight;
				float w = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundSize;
				if (m_ActorBody->m_OwnerPlayer)
					h += m_ActorBody->m_NameObjHeight;
				else
					h += GetWorldManagerPtr()->m_SurviveGameConfig->npcconfig.namedisplay_offset_y;
				h += m_ActorBody->m_HeadEffectObjHeight;
				int nHpProgressOffsetY = 0;
				auto pClientFlyComponent = m_ActorBody->m_OwnerActor->getClientFlyComponent();
				if (pClientFlyComponent)
				{

					nHpProgressOffsetY = pClientFlyComponent->getHpProgressOffsetY();
				}
				else
				{
					auto mob = dynamic_cast<ClientFlyMob*>(m_ActorBody->m_OwnerActor);
					if (mob)
					{
						nHpProgressOffsetY = mob->getHpProgressOffsetY();
					}
					else if (m_ActorBody->m_OwnerActor->isPlayer())
					{
						nHpProgressOffsetY = 10;
					}
				}

				m_HPProgressObj->SetPosition(WorldPos::fromVector3(pos + Vector3f(0.0f, h + nHpProgressOffsetY, 0.0f)));   //916e?? 2021/08/18 codeby:wudeshen

				{
					float w1 = w;
					w1 = w1 > GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.max_width ? GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.max_width : w1;
					w1 = w1 < GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.min_width ? GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.min_width : w1;

					if (m_HPProcessWidth != w1)
					{
						m_HPProgressObj->setScaleByWidth(w1);
						m_HPProcessWidth = w1;
					}
				}

				m_HPProgressObj->setMaxHight(GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.max_hight);
				if (g_pPlayerCtrl)
				{
					ActorLiving* living = dynamic_cast<ActorLiving*>(m_ActorBody->m_OwnerActor);
					if (living)
					{
						WCoord dist = m_ActorBody->m_OwnerActor->getPosition() - g_pPlayerCtrl->getPosition();
						if (m_HPVisible)
						{
							m_HPProgressObj->setVisible(dist.length() <= GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.progress_display_dist);
							m_HPProgressObj->setTextVisible(dist.length() <= (GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.text_display_dist));
						}
					}
				}
			}
			else
			{
				m_HPProgressObj->setVisible(false);
			}
		}
		m_HPProgressObj->update(dtick);

		bool showHp = true;
		if (m_ActorBody->m_OwnerPlayer)
			showHp = m_ActorBody->m_World ? (m_ActorBody->m_World->IsShowHpBar() || m_ActorBody->m_World->IsShowName()) : true;
		else
			showHp = m_ActorBody->m_World ? m_ActorBody->m_World->IsShowHpBar() : true;


		ClientActor* riding = NULL;
		ClientActor* carried = NULL;
		if (m_ActorBody->m_OwnerActor && !m_ActorBody->m_IsAttachModelView)
		{
			auto RidComp = m_ActorBody->m_OwnerActor->getRiddenComponent();
			if (RidComp && RidComp->isRiding() && (riding = RidComp->getRidingActor()) != NULL)
			{
				ActorHorse* ride = dynamic_cast<ActorHorse*>(riding);
				if (ride && ride->getHorseFlagBit(HORSE_FLAG::INVISIBLE) && !m_ActorBody->m_bIsShow)
				{
					showHp = false;
				}
			}
		}
		m_HPProgressObj->setShieldByUI(!showHp);
	}

#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
	if (m_ActorBody->m_OwnerActor && !m_ActorBody->m_IsAttachModelView)
#else
	if (m_ActorBody->m_OwnerActor && !m_ActorBody->m_UIModelView)
#endif
	{
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_updategraphicsvec)
		{
			GetWorldManagerPtr()->m_updategraphicsvec(dtime, m_ActorBody->m_OwnerActor, pos);
		}
	}


	if (m_HPChangeEffMgr)
	{
		if (g_pPlayerCtrl && m_HPChangeEffMgr->m_MoveByTextQue && m_HPChangeEffMgr->getVisible())
		{
			for (int i = 0; i < m_HPChangeEffMgr->m_MoveByTextQue->size(); i++)
			{
				WCoord dist = m_HPChangeEffMgr->m_MoveByTextQue->at(i)->GetPosition() - g_pPlayerCtrl->getPosition();
				m_HPChangeEffMgr->m_MoveByTextQue->at(i)->setVisible(dist.length() <= GetWorldManagerPtr()->m_SurviveGameConfig->hpchangeeffectcfg.display_dist);
			}
		}
		m_HPChangeEffMgr->update(dtime);
	}

	//2021-10-20 codeby:wangyu 播放互动动作隐藏血条和昵称
	bool isVisible = m_ActorBody->isPlayingSkinAct();
	setNameAndHpVisible(isVisible);
	updateForArmor(dtime, dtick, pos);
}

void ActorBodyUIComponent::updateForArmor(float dtime, unsigned int dtick, Rainbow::Vector3f& pos)
{
	if (m_ArmorProgressObj)
	{
		if (pos.y != MAX_FLOAT)
		{
			if (m_ArmorVisible && m_HPVisible)
			{
				float h = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundHeight;
				float w = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundSize;
				if (m_ActorBody->m_OwnerPlayer)
					h += m_ActorBody->m_NameObjHeight;
				else
					h += g_WorldMgr->m_SurviveGameConfig->npcconfig.namedisplay_offset_y;
				if (m_HPProgressObj && m_HPVisible)
				{
					h += m_HPProgressObj->getHeight() + g_WorldMgr->m_SurviveGameConfig->armorprogresscfg.interval_to_name;
				}
				h += m_ActorBody->m_HeadEffectObjHeight;
				//	m_ArmorProgressObj->SetPosition(WorldPos::fromVector3(pos + Rainbow::Vector3f(0.0f, h + m_OwnerActor->m_HpProgressOffsetY, 0.0f)));
				float w1 = w;
				//护甲的,暂时和血条一样长
				w1 = w1 > g_WorldMgr->m_SurviveGameConfig->armorprogresscfg.max_width ? g_WorldMgr->m_SurviveGameConfig->armorprogresscfg.max_width : w1;
				w1 = w1 < g_WorldMgr->m_SurviveGameConfig->armorprogresscfg.min_width ? g_WorldMgr->m_SurviveGameConfig->armorprogresscfg.min_width : w1;
				m_ArmorProgressObj->setMaxHight(g_WorldMgr->m_SurviveGameConfig->armorprogresscfg.max_hight);
				m_ArmorProgressObj->setScaleByWidth(w1);
				if (g_pPlayerCtrl)
				{
					ActorLiving* living = dynamic_cast<ActorLiving*>(m_ActorBody->m_OwnerActor);
					if (living)
					{
						WCoord dist = m_ActorBody->m_OwnerActor->getPosition() - g_pPlayerCtrl->getPosition();
						if (m_ArmorVisible)
						{
							m_ArmorProgressObj->setVisible(true);
							m_ArmorProgressObj->setTextVisible(false);
						}
					}
				}
			}
			else
			{
				m_ArmorProgressObj->setVisible(false);
			}
		}
		m_ArmorProgressObj->update(dtick);
	}
}

void ActorBodyUIComponent::updateForOverHead(unsigned int dtick, Rainbow::Vector3f& pos)
{
	OPTICK_EVENT();
	// 根据相对于相机的距离决定是否开启分帧策略
	m_RelativePosFromCamera = pos - CameraManager::GetInstance().getCameraPos().toVector3();

	m_unOverHeadTick += dtick;
	// 针对相机20M以上的对象开启分帧策略
	if (m_RelativePosFromCamera.LengthSqr() > 1500 * 1500)
	{
		//名字牌更新限频率，最多0.06s更新一次约等于分两帧
		// 位置变化不超过6cm的时候也不更新
		if (m_unOverHeadTick < 60 || (m_vLastHeadPos - pos).LengthSqr() < 36.f)
		{
			return;
		}
	}

	m_vLastHeadPos = pos;
	dtick = m_unOverHeadTick;
	m_unOverHeadTick = 0;

	bool showName = m_ActorBody->m_World ? m_ActorBody->m_World->IsShowName() : true;

	if (m_BillBoardText && m_BillBoardBkg && m_BillBoardText->isVisible() && m_BillBoardBkg->isVisible())
	{
		float h = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundHeight;
		if (pos.y != MAX_FLOAT)
		{
			m_BillBoardText->SetPosition(pos + Vector3f(0.0f, h + 30, 0.0f));
			m_BillBoardBkg->SetPosition(pos + Vector3f(0.0f, h + 30, 0.0f));
		}

		m_BillBoardBkg->update(dtick);
		m_BillBoardText->update(dtick);
	}

	if (m_NameDispObj)
	{
		if (pos.y != MAX_FLOAT)
		{
			int hpHeight = 0;
			if (m_HPProgressObj && m_HPVisible)
			{
				hpHeight = m_HPProgressObj->getHeight() + GetWorldManagerPtr()->m_SurviveGameConfig->hpprogresscfg.interval_to_name;
				//护甲显示，血条一定也显示
				if (m_ArmorProgressObj && m_ArmorVisible)
				{
					hpHeight += m_ArmorProgressObj->getHeight() + g_WorldMgr->m_SurviveGameConfig->armorprogresscfg.interval_to_name;
				}
			}
			float h = (float)m_ActorBody->m_OwnerActor->getLocoMotion()->m_BoundHeight;
			if (m_ActorBody->m_OwnerPlayer)
				h += m_ActorBody->m_NameObjHeight;
			else
				h += GetWorldManagerPtr()->m_SurviveGameConfig->npcconfig.namedisplay_offset_y;
			h += m_ActorBody->m_HeadEffectObjHeight;
			h += hpHeight;

			Vector3f namePos = (pos + Vector3f(0.0f, h, 0.0f));
			if (m_NameDispObj->isVisible() && !m_NameDispObj->isShieldByUI())
				m_NameDispObj->SetPosition(namePos);

			if (m_VoiceObj && m_VoiceObj->getVoiceIconVisible())
			{
				float width = 20, height = 20;
				if (m_NameDispObj)
				{
					m_NameDispObj->getTextSize(width, height);
				}
				m_VoiceObj->SetPosition(namePos);
				m_VoiceObj->SetOffset(width * 0.5f, 2.8f);
			}

			//m_AchieveBoard->setVisible(false);
			if (m_AchieveBoard && m_AchieveBoard->isVisible() && !m_AchieveBoard->isShieldByUI())
			{
				m_AchieveBoard->setScale(0.15f);
				m_AchieveBoard->SetPosition(pos + Vector3f(0.1f, h, 0.0f));
			}

			//m_AchieveIcon->setVisible(false);
			// if (m_AchieveIcon && m_AchieveIcon->isVisible() && !m_AchieveIcon->isShieldByUI())
			// {
			// 	m_AchieveIcon->setScale(0.15f);
			// 	m_AchieveIcon->SetPosition(pos + Vector3f(0.1f, h, 0.0f));
			// }

			if (m_TitleIcon && m_TitleIcon->isVisible() && !m_TitleIcon->isShieldByUI())
				m_TitleIcon->SetPosition(namePos);

			if (m_Dialog && m_Dialog->isVisible() && !m_Dialog->isShieldByUI())
				m_Dialog->SetPosition(namePos);

			if (m_NeedItemBoard && m_NeedItemBoard->isVisible() && !m_NeedItemBoard->isShieldByUI())
				m_NeedItemBoard->SetPosition(namePos);

			if (m_NeedItemIconPtr && m_NeedItemIconPtr->isVisible() && !m_NeedItemIconPtr->isShieldByUI())
				m_NeedItemIconPtr->SetPosition(namePos);

			if (m_VipIcon && m_VipIcon->isVisible() && !m_VipIcon->isShieldByUI())
				m_VipIcon->SetPosition(namePos);

			if (g_pPlayerCtrl)
			{
				ClientMob* mob = dynamic_cast<ClientMob*>(m_ActorBody->m_OwnerActor);
				if (mob)
				{
					WCoord dist = m_ActorBody->m_OwnerActor->getPosition() - g_pPlayerCtrl->getPosition();
					if (mob->m_Def->NameDisPlay)
					{
						bool isVisible = dist.length() <= GetWorldManagerPtr()->m_SurviveGameConfig->npcconfig.namedisplay_dist;
						if (m_NameDispObj) m_NameDispObj->setVisible(isVisible);
						if (m_AchieveBoard) m_AchieveBoard->setVisible(isVisible);
						//if (m_AchieveIcon) m_AchieveIcon->setVisible(isVisible);
						if (m_VipIcon) m_VipIcon->setVisible(isVisible);
						if (m_TitleIcon) m_TitleIcon->setVisible(isVisible);
					}
					if (mob->m_Def->DescDisplay)
						if (m_NameDispObj) m_NameDispObj->setDescVisible(dist.length() <= GetWorldManagerPtr()->m_SurviveGameConfig->npcconfig.namedisplay_dist);
				}

			}
		}

		if (m_NameDispObj && m_NameDispObj->isVisible() && !m_NameDispObj->isShieldByUI())
			m_NameDispObj->update(dtick);

		if (m_AchieveBoard && m_AchieveBoard->isVisible() && !m_AchieveBoard->isShieldByUI())
			m_AchieveBoard->update(dtick);

		// if (m_AchieveIcon && m_AchieveIcon->isVisible() && !m_AchieveIcon->isShieldByUI())
		// 	m_AchieveIcon->update(dtick);

		if (m_VipIcon && m_VipIcon->isVisible() && !m_VipIcon->isShieldByUI())
			m_VipIcon->update(dtick);

		if (m_Dialog && m_Dialog->isVisible() && !m_Dialog->isShieldByUI())
			m_Dialog->update(dtick);

		if (m_TitleIcon && m_TitleIcon->isVisible() && !m_TitleIcon->isShieldByUI())
			m_TitleIcon->update(dtick);

		if (m_NeedItemBoard && m_NeedItemBoard->isVisible() && !m_NeedItemBoard->isShieldByUI())
			m_NeedItemBoard->update(dtick);

		if (m_NeedItemIconPtr && m_NeedItemIconPtr->isVisible() && !m_NeedItemIconPtr->isShieldByUI())
			m_NeedItemIconPtr->update(dtick);

		if (m_VoiceObj && m_VoiceObj->IsShow())
		{
			m_VoiceObj->update(TickToTime(dtick));
		}

		if (m_NameBlickStartTime != 0)
		{
			//头顶ICON闪烁
			int curTime = GetWorldManagerPtr()->getWorldTime();

			int itemId = 0;
			MINIW::ScriptVM::game()->callFunction("F3021_GetCurNeedItemIdWithOutRandom", ">i", &itemId);
			int u = 0, v = 0, w = 0, h = 0, r = 255, g = 255, b = 255;
			SharePtr<Texture2D> tex = nullptr;

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ItemIconManager_getItemIcon",
				SandboxContext(nullptr)
				.SetData_Number("toolid", itemId)
				.SetData_Number("u", u)
				.SetData_Number("v", v)
				.SetData_Number("w", w)
				.SetData_Number("h", h)
				.SetData_Number("r", r)
				.SetData_Number("g", g)
				.SetData_Number("b", b));
			if (result.IsExecSuccessed())
			{
				tex = result.GetData_UserObject<SharePtr<Texture2D>>("huires");
				u = result.GetData_Number("u");
				v = result.GetData_Number("v");
				w = result.GetData_Number("w");
				h = result.GetData_Number("h");
				r = result.GetData_Number("r");
				g = result.GetData_Number("g");
				b = result.GetData_Number("b");
			}
			if (tex != 0)
			{
				InitNeedItemBoard(m_TexId);
				InitNeedItemIconPtr(m_TexId);

				if (curTime - m_NameBlickStartTime >= 0 && curTime - m_NameBlickStartTime < 5)
				{
					//m_NameDispObj->setIconHuires(huires);
					m_NeedItemIconPtr->setImageRes(tex, w, h);
					m_NeedItemIconPtr->setVisible(true);
					m_NeedItemBoard->setVisible(true);
				}
				else if (curTime - m_NameBlickStartTime >= 5 && curTime - m_NameBlickStartTime < 10)
				{
					//m_NameDispObj->setIconHuires(NULL);
					if (m_NeedItemIconPtr) m_NeedItemIconPtr->setVisible(false);
					if (m_NeedItemBoard) m_NeedItemBoard->setVisible(false);
				}
				else if (curTime - m_NameBlickStartTime >= 10 && curTime - m_NameBlickStartTime < 15)
				{
					//m_NameDispObj->setIconHuires(huires);
					m_NeedItemIconPtr->setImageRes(tex, w, h);
					m_NeedItemIconPtr->setVisible(true);
					m_NeedItemBoard->setVisible(true);
				}
				else if (curTime - m_NameBlickStartTime >= 15 && curTime - m_NameBlickStartTime < 20)
				{
					//m_NameDispObj->setIconHuires(NULL);
					m_NeedItemIconPtr->setVisible(false);
					m_NeedItemBoard->setVisible(false);
				}
				else
				{
					//m_NameDispObj->setIconHuires(huires);
					m_NeedItemIconPtr->setImageRes(tex, w, h);
					m_NeedItemIconPtr->setVisible(true);
					m_NeedItemBoard->setVisible(true);
					m_NameBlickStartTime = 0;
				}
			}
		}

		ClientPlayer* player = m_ActorBody->m_OwnerPlayer;
		if (player && g_pPlayerCtrl)
		{
			static Rainbow::NoFreeFixedString hideOwnerName("hideOwnerName");
			static Rainbow::NoFreeFixedString hideOtherName("hideOtherName");
			bool needHideOwnerName = GetIWorldConfigProxy()->getGameData(hideOwnerName);
			bool needHideOtherName = GetIWorldConfigProxy()->getGameData(hideOtherName);
			if ((g_pPlayerCtrl == player && needHideOwnerName) || (g_pPlayerCtrl != player && needHideOtherName))
			{
				showName = false;
				m_HPVisible = false;
			}
			if (m_NameDispObj && m_NameDispObj->isVisible() != showName) m_NameDispObj->setVisible(showName);
		}

		if (m_NameDispObj) m_NameDispObj->setShieldByUI(!showName);
		if (m_AchieveBoard) m_AchieveBoard->setShieldByUI(!showName);
		// if (m_AchieveIcon) m_AchieveIcon->setShieldByUI(!showName);
		if (m_VipIcon) m_VipIcon->setShieldByUI(!showName);
		//m_Dialog->setVisible(showName&& m_Dialog->isVisible());
		if (m_TitleIcon) m_TitleIcon->setShieldByUI(!showName);
		//m_NeedItemBoard->setVisible(showName&& m_NeedItemBoard->isVisible());
		//m_NeedItemIconPtr->setVisible(showName&& m_NeedItemIconPtr->isVisible());
	}
	if (m_ImageBoard && m_ImageBoard->visible())
	{
		Vector3f showPos = (pos + Vector3f(0.f, 250, 0.0f));
		m_ImageBoard->SetPosition(showPos);
		//m_ImageBoard->update(dtick);
		//m_ImageBoard->setVisible(showName&& m_ImageBoard->visible());
	}
}