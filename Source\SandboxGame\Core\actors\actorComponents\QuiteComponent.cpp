#include "QuiteComponent.h"

#include "ClientMob.h"
IMPLEMENT_COMPONENTCLASS(QuiteComponent)

QuiteComponent::QuiteComponent()
{
	m_InQuite = 0;
	m_iReadyToQuit = 0;
	m_iRealQuitTick = 0;
}

QuiteComponent::~QuiteComponent()
{

}

void QuiteComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwn<PERSON>(owner);
	BindOnTick();
}

void QuiteComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	Super::<PERSON><PERSON><PERSON><PERSON>Own<PERSON>(owner);
}

void QuiteComponent::OnTick()
{
	if (m_iReadyToQuit > 0)
	{
		m_iReadyToQuit--;
		if (m_iReadyToQuit == 0)
		{
			setQuiteTick(m_iRealQuitTick);
		}
	}

	if (m_InQuite > 0)
	{
		m_InQuite--;
	}
}
