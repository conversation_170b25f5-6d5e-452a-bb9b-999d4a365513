#ifndef __DROP_ITEM_COMPONENT_H__
#define __DROP_ITEM_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "container.h"
#include "SandboxGame.h"
class ClientActor;
class EXPORT_SANDBOXGAME DropItemComponent;

class DropItemComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(DropItemComponent)
	DropItemComponent();
	~DropItemComponent();
	//tolua_begin
	int getDropItemNum_Base();
	void dropItem(int itemid, int num);
	void dropSpecialLetterItem(int itemid, int num, std::string letterStr);
	void dropItem(BackPackGrid *pgrid);
	void dropItemToward(BackPackGrid *pgrid, ClientActor* pActor);

	
	//virtual
	int getDropItemNum(); 
	//tolua_end

	void GenDropPos(const WCoord& startPt, int count);
protected:

	struct DropPos
	{
		WCoord pos;
		bool used = false;
	};

	bool m_PosInited = false;
	std::vector<DropPos> m_DropPosList;

}; //tolua_exports
#endif