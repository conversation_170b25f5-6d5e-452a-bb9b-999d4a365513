﻿#pragma once

class ClientActor;
class ClientMob;
#include <limits>
#include "AttributeValue.h"
class EXPORT_SANDBOXGAME ThirstValue;

class ThirstValue : public AttributeValue //tolua_export
{//tolua_export
	DECLARE_REFCLASS(ThirstValue)
public:
	ThirstValue();
	virtual ~ThirstValue();

	//设置口渴
	void SetThirstValue(float fValue);
	float GetThirstValue() const;

	//设置基础最大口渴
	void SetBasicMaxThirst(float fValue);
	float GetBasicMaxThirst() const;

	//设置最大口渴
	void SetMaxThirst(float fValue);
	float GetMaxThirst() const;

	//设置基础溢出值
	void SetBasicOverflowThirst(float fValue);
	float GetBasicOverflowThirst()const;

	//设置溢出值
	void SetOverflowThirst(float fValue);
	float GetOverflowThirst() const;

	//增加口渴
	void IncreaseThirst(float fValue);

	//减少口渴
	void DecreaseThirst(float fValue);

private:
	//tolua_begin
	//数值变化前
	virtual void OnValueChangeBefore();
	//数值变化后
	virtual void OnValueChangeAfter();

	//tolua_end

private:
	float m_fBasicMaxThirst = 0;
	float m_fMaxThirst = 0;
	float m_fBasicOverflowThirst = 0;
	float m_fOverflowThirst = 0;
	
};
