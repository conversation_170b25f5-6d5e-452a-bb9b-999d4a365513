#ifndef __ACTOR_SNOWHARE_H__
#define __ACTOR_SNOWHARE_H__

#include "ClientActorLiving.h"
#include "ChunkViewer.h"
#include "ClientMob.h"
#include "ClientTrixenieMob.h"
#include "LivingLocoMotion.h"

class ActorSnowHare : public ClientMob //tolua_exports
{ //tolua_exports

public:
	//tolua_begin
	ActorSnowHare();
	virtual ~ActorSnowHare();
	virtual bool init(int monsterid);
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder)override;
	virtual bool load(const void* srcdata, int version)override;

	int GetFurColor() { return m_FurColor; }
	void ChangeFurColor(int value);
	int GetFurColorByParent(int fatherColor, int motherColor);
	void SetFurColorByParent(int fatherColor,int motherColor);

	bool IsThickHasHare(int x, int y, int z);
	bool HideInThick(int x,int y,int z);
	//tolua_end

	virtual void enterWorld(World* pworld) override;
	virtual ActorBody* newActorBody() override;
	virtual int saveToPB(PB_GeneralEnterAOIHC* pb) override;
	virtual int LoadFromPB(const PB_GeneralEnterAOIHC& pb) override;
	virtual void playStepSound() override;

	ClientMob* getNearbyMixMate(int mixDefId = 0);
	int GetParentId();
	int GetColorIdByFurColor(int furColor);

private:
	bool isVacantSnowHare();
	
protected:
	int m_FurColor;
	bool m_IsColorSet;
private:
	typedef ClientMob Super;

}; //tolua_exports
#endif

